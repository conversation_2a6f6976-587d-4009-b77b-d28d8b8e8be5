#!/bin/bash

set -ex

LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B)

cd /tmp

rm -rf devapis || true

git clone https://code.byted.org/devinfra/devapis

cd devapis

apt update
apt install -y rsync parallel

rsync -a --delete /home/<USER>/idls/byted --include="*.proto" .

find byted -name "*.proto" | parallel sed -i "'s#code.byted.org/devinfra/hagrid/pbgen/#code.byted.org/devinfra/pbgen/#g'"
find byted -name "*.proto" | parallel sed -i "'s&(validate.required)&(vendors.validate.required)&g'"
find byted -name "*.proto" | parallel sed -i "'s&(validate.rules)&(vendors.validate.rules)&g'"
find byted -name "*.proto" | parallel sed -i "'s&vendors/validate/validate.proto&validate/validate.proto&g'"
find byted -name "*.proto" | parallel sed -i "'s&option go_package = \"code.byted.org/devinfra/hagrid/idls/byted&option go_package = \"byted&g'"
find byted -name "*.proto" | parallel sed -i "'s&option go_package = \"code.byted.org/devinfra/pbgen/byted&option go_package = \"byted&g'"
find byted -name "*.proto" | parallel sed -i "'s&import \"idls/byted&import \"byted&g'"
find byted -name "*.proto" | parallel sed -i "'s&import \"validate/&import \"vendors/validate/&g'"

export GIT_COMMITTER_NAME="${CI_ACTOR}"
export GIT_COMMITTER_EMAIL="${CI_ACTOR}@bytedance.com"
export GIT_AUTHOR_NAME="CI bot"
export GIT_AUTHOR_EMAIL="<EMAIL>"

if [[ -z "$(git status --porcelain)" ]]; then
    echo "Workspace is clean"
    exit 0
fi


git add byted
git commit -m "$LAST_COMMIT_MESSAGE"

if [[ "$CI_HEAD_BRANCH" != "main" ]]; then
    git push -f origin HEAD:$CI_HEAD_BRANCH
else
    git push origin HEAD:$CI_HEAD_BRANCH
fi
