package diff_package

import (
	"container/list"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"time"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	diff "github.com/volcengine/volc-sdk-golang/service/mars"

	"code.byted.org/clientQA/artifact-manager/container"
	"code.byted.org/clientQA/artifact-manager/domain"
	"code.byted.org/clientQA/artifact-manager/framework/thirdparty/tos"
	"code.byted.org/clientQA/artifact-manager/framework/util"
	EpArtifactManager "code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager"
	"code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/artifact"
	"code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/releaserule"
	"code.byted.org/clientQA/artifact-manager/service"
)

const distributed = 1

func getDiffInstance() *diff.Diff {
	instance := diff.DefaultInstance
	instance.Client.SetAccessKey(testAk)
	instance.Client.SetSecretKey(testSk)
	return instance
}

// 新增差分service
func GenDiffService(serviceName string, comment string, appId uint, hostAbi string) error {
	request := &diff.CreateServiceReq{
		Name:    serviceName,
		Comment: comment,
	}

	instance := getDiffInstance()
	if instance == nil {
		return errors.New("[diff_package] instance is nil")
	}

	response, status, err := instance.CreateService(request, getDiffAppId(appId, hostAbi))
	if status != http.StatusOK {
		return util.NewError(err, "[diff_package] GenDiffService err in CreateService")
	}

	if response == nil || response.Data == nil {
		return util.NewError(err, "[diff_package] GenDiffService err, response is nil, response:%v", response)
	}
	logs.Info("[diff_package] GenDiffService create service success, serviceId:%v", response.Data.ServiceId)
	return nil
}

// 创建差分生成任务，同时把新的包上传至差分服务
func CreateDiffPackageTask(art domain.Artifact) (int64, error) {
	if art.ArtifactType != artifact.ArtifactType_APK.String() || art.CloudAppId != 1128 ||
		(art.ReleaseEnv != "Official" && art.ReleaseEnv != "Gray" &&
			art.ReleaseEnv != "OFFICIAL" && art.ReleaseEnv != "GRAY") {
		logs.Info("artifactId:%v don't need diff, pkg_type:%v, app_id:%v, type:%v", art.Id, art.PmPkgType, art.CloudAppId, art.ArtifactType)
		return 0, nil
	}

	taskId, err := CreateDiffRequest(art, nil)
	if err != nil {
		return 0, err
	}
	logs.Info("[diff_package] InsertDiffTask success, taskId:%v", taskId)
	return taskId, nil
}

// 发送生成差分请求
func CreateDiffRequest(art domain.Artifact, oldIds []uint64) (int64, error) {
	abi, err := getDiffPackageAbi(art.Tags, art.Id)
	if err != nil {
		logs.Error("[diff_package] CheckDiffTaskStatus err in getDiffPackageAbi, artId:%v", art.Id)
		abi = "64"
	}

	outerLink := art.OuterLink
	if outerLink == "" {
		outerLink, err = getOuterURL(int64(art.CloudAppId), art.DownloadURL)
		if err != nil {
			logs.Error("[diff_package] CreateDiffRequest getOuterURL err:%v", err.Error())
		}
	}

	// 由于现在差分服务不支持同时存在多个相同版本，为了能将包成功上传，用artifact的id来表示差分服务的版本。
	extraInfo, err := json.Marshal(map[string]interface{}{
		"id":           art.Id,
		"version":      art.UpdateVersion,
		"md5":          art.Md5,
		"cloud_app_id": art.CloudAppId,
		"url":          outerLink,
	})
	if err != nil {
		logs.Error("[diff_package] CreateDiffRequest json marshal err, artifact:%v", util.ToJsonSimple(art))
	}

	oldArtIds := oldIds
	if oldIds == nil || len(oldIds) == 0 {
		oldArtIds, err = getOldVersionArtifacts(art)
		if err != nil {
			return 0, util.NewError(err, "[diff_package] CreateDiffRequest err in getOldVersionArtifacts, artifactId:%v", art.Id)
		}
	}
	request := &diff.GenByVersionReq{
		Options: map[string]interface{}{
			"oldClearSignature": true,
		},
		ServiceId:    getDiffServiceId(art.CloudAppId, abi),
		Alg:          "wp", // 固定先用wp算法，即HDiffPatch
		NewUrl:       outerLink,
		NewVersion:   uint64(art.Id),
		NewExtraInfo: string(extraInfo),
		OldVersions:  oldArtIds,
	}

	instance := getDiffInstance()
	if instance == nil {
		return 0, errors.New("[diff_package] instance is nil")
	}

	response, status, err := instance.GenByVersion(request, getDiffAppId(art.CloudAppId, abi))
	logs.Info("[diff_package] GenByVersion artifactId:%v, req:%v, res:%v, status:%v, err:%v", art.Id, util.ToJsonSimple(request), util.ToJsonSimple(response), status, util.ToJsonSimple(err))
	if status != http.StatusOK || response.Data == nil {
		logs.Error("[diff_package] CreateDiffRequest err in GenByVersion:%v", util.ToJsonSimple(response))
		return 0, util.NewError(err, "[diff_package] CreateDiffRequest err in GenByVersion")
	}

	taskId := response.Data.TaskId
	cache := container.GetContainer().DiffCache
	cache.InsertDiffTask(taskId)
	return taskId, nil
}

// 消费本地taskId缓存列表，遍历查看是否有已完成的任务，有的话更新库并把task从列表中去除
func UpdateDiffPackagesByCache() error {
	cache := container.GetContainer().DiffCache
	cache.PrintDiffTaskList()
	diffTaskList := cache.GetDiffTaskList()

	lock := cache.GetUpdateLock()
	updatedList := make([]*list.Element, 0)
	lock.Lock()
	for e := diffTaskList.Front(); e != nil; e = e.Next() {
		taskId, ok := e.Value.(int64)
		if !ok {
			continue
		}
		updated, err := CheckDiffTaskStatus(taskId)
		if err != nil {
			logs.Error("[diff_package] UpdateDiffPackages err in CheckDiffTaskStatus, "+
				"taskId:%v, err:%v", taskId, err.Error())
			continue
		}
		if updated {
			updatedList = append(updatedList, e)
		}
	}
	lock.Unlock()

	for _, task := range updatedList {
		taskId := cache.PopDiffTask(task)
		logs.Info("[diff_package] UpdateDiffPackages updated taskId:%v", taskId)
	}
	return nil
}

// 查询差分任务是否结束。结束了的话，把结果更新到数据库中
func CheckDiffTaskStatus(taskId int64) (bool, error) {
	instance := getDiffInstance()
	if instance == nil {
		return false, errors.New("[diff_package] instance is nil")
	}

	request := &diff.CheckResponseReq{
		TaskId: taskId,
	}

	response, result, status, err := instance.CheckResponse(request)
	logs.Info("[diff_package] CheckDiffTaskStatus CheckResponse taskId:%v response:%v, res%v, status:%v, err:%v",
		taskId, util.ToJsonSimple(response), util.ToJsonSimple(result), status, util.ToJsonSimple(err))
	if status != http.StatusOK || response == nil || response.Data == nil || response.Data.Api != 0 || err != nil {
		return false, nil
	}

	res, ok := result.(*diff.GenResult)
	if !ok || res == nil {
		return false, nil
	}
	// 表示这次任务没做差分
	if res.Data == nil || len(*res.Data) == 0 {
		return false, nil
	}

	for _, pkg := range *res.Data {
		if pkg.Code != 0 && pkg.Code != 1010 {
			logs.Info("[diff_package] pkg.Code != 0, taskId:%v, code:%v", taskId, pkg.Code)
			continue
		}
		logs.Info("[diff_package] Create diff package start, taskId:%v", taskId)
		diffPkg, err := getDiffPackage(pkg, int(taskId))
		if err != nil {
			logs.Error("[diff_package] CheckDiffTaskStatus err in getDiffPackage, taskId:%v, err:%v", taskId, err.Error())
			continue
		}

		if diffPkg == nil {
			continue
		}

		diffRepo := container.GetContainer().Mysql.DiffPackageRepo
		diffId, err := diffRepo.Create(diffPkg)
		if err != nil {
			logs.Error("[diff_package] CheckDiffTaskStatus err in diffRepo Create, taskId:%v, diffPkg:%v, err:%v",
				taskId, util.ToJsonSimple(diffPkg), err.Error())
			continue
		}
		logs.Info("[diff_package] Create diff package success, id:%v", diffId)
	}
	return true, nil
}

// 获取用于数据库更新的diffPackageModel
func getDiffPackage(pkg diff.RespPatch, taskId int) (*domain.DiffPackage, error) {
	taskIdStr := strconv.Itoa(taskId)
	diffPkg := &domain.DiffPackage{
		TaskId:    taskIdStr,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	oldExtraInfo, newExtraInfo := &DiffExtraInfo{}, &DiffExtraInfo{}
	err := json.Unmarshal([]byte(pkg.OldExtraInfo), oldExtraInfo)
	if err != nil {
		return nil, util.NewError(err, "[diff_package] getDiffPackage err in unmarshal oldExtraInfo:%v, taskId:%v",
			pkg.OldExtraInfo, taskId)
	}
	diffPkg.OldPackageId = oldExtraInfo.Id
	diffPkg.CloudAppId = oldExtraInfo.CloudAppId
	diffPkg.OldPackageMD5 = oldExtraInfo.MD5
	diffPkg.OldPackageUrl = oldExtraInfo.Url
	diffPkg.OldPackageVersion = oldExtraInfo.Version
	diffPkg.PatchMD5 = pkg.Md5
	diffPkg.Ratio = pkg.Ratio

	err = json.Unmarshal([]byte(pkg.NewExtraInfo), newExtraInfo)
	if err != nil {
		logs.Info("[diff_package] err in unmarshal, err:%v, info:%v", util.ToJsonSimple(err), pkg.NewExtraInfo)
		return nil, util.NewError(err, "[diff_package] getDiffPackage err in unmarshal newExtraInfo:%v, taskId:%v",
			pkg.NewExtraInfo, taskId)
	}

	// 只对低版本做差分。等于没关系，方便QA测试
	if newExtraInfo.Version < oldExtraInfo.Version {
		logs.Info("[diff_package] getDiffPackage newExtraInfo.Version <= oldExtraInfo.Version, oldExtra:%v, newExtra:%v",
			pkg.OldExtraInfo, pkg.NewExtraInfo)
		return nil, nil
	}

	if isDiffPackageExist(oldExtraInfo.Id, newExtraInfo.Id, taskIdStr) {
		logs.Info("[diff_package] isDiffPackageExist, oldId:%v, newId:%v, taskId:%v", oldExtraInfo.Id, newExtraInfo.Id, taskIdStr)
		return nil, nil
	}

	diffPkg.NewPackageId = newExtraInfo.Id
	diffPkg.NewPackageMD5 = newExtraInfo.MD5
	diffPkg.NewPackageUrl = newExtraInfo.Url
	diffPkg.NewPackageVersion = newExtraInfo.Version
	diffPkg.PatchSdkVersion = pkg.MinSdkVersion
	diffPkg.OldMD5RemovingChannel = pkg.OldInfo
	diffPkg.PatchAlg = pkg.Alg
	diffPkg.OnlineState = 1

	patchUrl, err := getOuterURL(int64(oldExtraInfo.CloudAppId), pkg.Url)
	if err != nil {
		return nil, util.NewError(err, "[diff_package] getDiffPackage err in getPatchURL, taskId:%v", taskId)
	}
	diffPkg.PatchUrl = patchUrl
	return diffPkg, nil
}

func isDiffPackageExist(oldPackageId int, newPackageId int, taskId string) bool {
	filter := domain.DiffFilter{
		OldPackageId: oldPackageId,
		NewPackageId: newPackageId,
		TaskId:       taskId,
	}

	diffRepo := container.GetContainer().Mysql.DiffPackageRepo
	diffPkgs, err := diffRepo.Filter(filter)
	if err != nil {
		logs.Error("[diff_package] isDiffPackageExist err in diffRepo Filter, taskId:%v, filter:%v, err:%v",
			taskId, util.ToJsonSimple(filter), err.Error())
		return false
	}
	return diffPkgs != nil && len(diffPkgs) > 0
}

// 获取火山引擎下面差分的产品id
func getDiffAppId(appId uint, hostAbi string) int64 {
	m, _ := DiffVolcengineAppId[appId]
	if env.IsBoe() {
		m, _ = TestDiffVolcengineAppId[appId]
	}
	aid, _ := m[hostAbi]
	return aid
}

// 获取差分service的ID
func getDiffServiceId(appId uint, hostAbi string) int {
	m, _ := DiffServiceID[appId]
	if env.IsBoe() {
		m, _ = TestDiffServiceID[appId]
	}
	serviceId, _ := m[hostAbi]
	return serviceId
}

// 获取外链
func getOuterURL(cloudAppId int64, originalUrl string) (string, error) {
	domainClient := container.GetContainer().CdnClient
	downloader := util.NewHttpDownloader()
	outerLink, err := tos.GetPkgOuterLink(context.Background(), downloader, originalUrl, cloudAppId, domainClient, tos.DOMAIN_CN)
	if err != nil {
		return "", util.NewError(err, "[diff_package] getOuterURL err, url:%v, appId:%v", originalUrl, cloudAppId)
	}
	return outerLink, nil
}

// 获取宿主包的abi
func getDiffPackageAbi(tag string, artifactId uint) (string, error) {
	m := make(map[string]interface{})
	err := json.Unmarshal([]byte(tag), &m)
	if err != nil {
		logs.Error("[diff_package] getDiffPackageAbi unmarshal artifact tag err:%v, "+
			"tag:%v, artifactId:%v", err.Error(), tag, artifactId)
		return "", err
	}

	abiInterface, ok := m[artifact.Tags_abi_filter.String()]
	if !ok {
		logs.Error("[diff_package] getDiffPackageAbi artifact don't have abi tag, "+
			"tag:%v, artifactId:%v", tag, artifactId)
		return "", errors.New("artifact don't have abi tag")
	}

	abi, ok := abiInterface.(string)
	if !ok || (ok && abi != "32" && abi != "64") {
		logs.Error("[diff_package] getDiffPackageAbi artifact abi not match, "+
			"tag:%v, artifactId:%v", tag, artifactId)
		return "", errors.New("artifact don't have abi tag")
	}
	return abi, nil
}

// 获取低版本包。优先找正式包，然后灰度包，最后找独立灰度技术包。
func getOldVersionArtifacts(newArt domain.Artifact) ([]uint64, error) {
	updateVersionInt, err := strconv.Atoi(newArt.UpdateVersion)
	if err != nil {
		return nil, util.NewError(err, "[diff_package] getOldVersionArtifacts artifact updateVersion:%v, artifactId:%v",
			newArt.UpdateVersion, newArt.Id)
	}
	c := container.GetContainer()
	filter := domain.ArtifactFilter{
		CloudAppId: newArt.CloudAppId,
		ReleaseEnv: []string{
			"Official",
			"OFFICIAL",
		},
		ArtifactTypes:       []string{artifact.ArtifactType_APK.String()},
		UpdaterVersionRange: []uint{1, uint(updateVersionInt)},
		ProviderList:        []string{"RELEASE_WORKFLOW", "DEVOPS"},
		DistributeStatuses:  []int64{distributed},
	}
	artifacts, err := c.Mysql.ArtifactRepo.Filter(filter, 0, 12)
	if err != nil {
		return nil, util.NewError(err, "[diff_package] getOldVersionArtifacts artifact filter official artifactId:%v", newArt.Id)
	}

	artifactIds := make([]uint64, 0)
	for _, oldArt := range artifacts {
		artifactIds = append(artifactIds, uint64(oldArt.Id))
	}
	logs.Info("[getOldVersionArtifacts] new pkg:%v, old pkgs:%v", newArt.Id, util.ToJsonSimple(artifactIds))
	return artifactIds, nil
}

func CronUpdateDiffPackageByCache() {
	logs.Info("CronUpdateDiffPackages start")
	if err := UpdateDiffPackagesByCache(); err != nil {
		logs.Info("UpdateDiffPackagesByCache err:%v", err.Error())
	}
}

// 定时接口，对于没有更新差分成功的artifact去做刷新
func UpdateDiffPackage(appid int) {
	artifacts, err := GetNewVersionArtifacts(appid)
	if err != nil {
		return
	}

	for _, artifact := range artifacts {
		filter := domain.DiffFilter{
			NewPackageId: int(artifact.Id),
		}
		diffRepo := container.GetContainer().Mysql.DiffPackageRepo
		diffPkgs, err := diffRepo.Filter(filter)
		if err != nil {
			logs.Error("[diff_package] UpdateDiffPackages err in diffRepo Filter, newPackageId:%v, filter:%v, err:%v",
				artifact.Id, util.ToJsonSimple(filter), err.Error())
			continue
		}
		// 说明这个包已经生成差分了
		if diffPkgs != nil && len(diffPkgs) > 0 {
			continue
		}

		taskId, err := CreateDiffPackageTask(*artifact)
		if err != nil || taskId == 0 {
			continue
		}
		logs.Info("[diff_package] UpdateDiffPackages CreateDiffPackageTask success, taskID:%v, artifactId:%v, artifactVersion:%v",
			taskId, artifact.Id, artifact.UpdateVersion)
	}
}

// 获取创建时间距离现在小于1天，且过去2个小时仍没有更新差分成功的包，用于做差分高版本包
func GetNewVersionArtifacts(appid int) ([]*domain.Artifact, error) {
	c := container.GetContainer()
	filter := domain.ArtifactFilter{
		CloudAppId:    uint(appid),
		ReleaseEnv:    []string{"Official", "Gray", "RDGRAY", "OFFICIAL", "GRAY"},
		ArtifactTypes: []string{artifact.ArtifactType_APK.String()},
		CreatedAt:     []time.Time{time.Now().AddDate(0, 0, -1), time.Now().Add(time.Hour * 2)},
	}

	artifacts, err := c.Mysql.ArtifactRepo.Filter(filter, 0, 30)
	if err != nil {
		logs.Error("[diff_package] GetNewVersionArtifacts artifact filter err:%v, filter:%v", err.Error(), util.ToJsonSimple(filter))
		return nil, err
	}
	return artifacts, nil
}

// 刷新接口，提供两个url，刷新两个包之间的差分包
func CreateCertainDiffPackage(newURL string, oldURL string, appId int) int64 {

	newMD5, err := util.CalcMd5(newURL)
	if err != nil {
		logs.Error("[diff_package] CreateCertainDiffPackage calculate oldMD5 err:%v, newURL:%v", err.Error(), newURL)
		return 0
	}

	oldMD5 := ""
	if oldURL != "" {
		oldMD5, err = util.CalcMd5(oldURL)
		if err != nil {
			logs.Error("[diff_package] CreateCertainDiffPackage calculate oldMD5 err:%v, oldURL:%v", err.Error(), oldURL)
			return 0
		}
	}

	c := container.GetContainer()
	filter := domain.ArtifactFilter{
		Md5:        []string{oldMD5},
		CloudAppId: uint(appId),
	}

	oldArtifact, err := c.Mysql.ArtifactRepo.Filter(filter, 0, 2)
	if err != nil {
		logs.Error("[diff_package] CreateCertainDiffPackage artifact filter err:%v, filter:%v", err.Error(), util.ToJsonSimple(filter))
		return 0
	}

	filter.Md5 = []string{newMD5}
	newArtifact, err := c.Mysql.ArtifactRepo.Filter(filter, 0, 2)
	if err != nil {
		logs.Error("[diff_package] CreateCertainDiffPackage artifact filter err:%v, filter:%v", err.Error(), util.ToJsonSimple(filter))
		return 0
	}

	if len(newArtifact) == 0 {
		logs.Error("[diff_package] CreateCertainDiffPackage err, oldMD5:%v, len(oldArtifacts):%v, newMD5:%v, len(newArtifacts):%v",
			oldMD5, len(oldArtifact), newMD5, len(newArtifact))
		return 0
	}

	oldId := uint64(1)
	if len(oldArtifact) > 0 {
		oldId = uint64(oldArtifact[0].Id)
	}

	taskId, err := CreateDiffRequest(*newArtifact[0], []uint64{oldId})
	if err != nil || taskId == 0 {
		logs.Error("[diff_package] CreateCertainDiffPackage CreateDiffRequest err:%v", err.Error())
		return 0
	}
	logs.Info("[diff_package] CreateCertainDiffPackage InsertDiffTask success, taskId:%v", taskId)
	return taskId
}

func CronUpdateDiffPackage(ctx context.Context, req releaserule.CronUpdateDiffPackageReq) (*releaserule.CronUpdateDiffPackageResp, *service.ServiceError) {
	UpdateDiffPackage(int(req.AppID))
	return &releaserule.CronUpdateDiffPackageResp{}, nil
}

func UpdateDiffPackageManually(ctx context.Context, req releaserule.UpdateDiffPackageManuallyReq) (*releaserule.UpdateDiffPackageManuallyResp, *service.ServiceError) {
	taskId := CreateCertainDiffPackage(req.NewURL_, req.OldURL, int(req.AppID))
	return &releaserule.UpdateDiffPackageManuallyResp{
		TaskID: taskId,
	}, nil
}

func UpdateDiffTaskStatus(ctx context.Context, req releaserule.UpdateDiffTaskStatusReq) (*releaserule.UpdateDiffTaskStatusResp, *service.ServiceError) {
	updated, err := CheckDiffTaskStatus(req.TaskID)
	if err != nil {
		logs.Error("[diff_package] UpdateDiffTaskStatus err in CheckDiffTaskStatus, "+
			"taskId:%v, err:%v", req.TaskID, err.Error())
		return &releaserule.UpdateDiffTaskStatusResp{
			Updated: updated,
			Message: err.Error(),
		}, nil
	}
	logs.Info("[diff_package] UpdateDiffTaskStatus updated:%v taskId:%v", updated, req.TaskID)
	return &releaserule.UpdateDiffTaskStatusResp{
		Updated: updated,
		Message: "success",
	}, nil
}

func RefreshDiffPkg(ctx context.Context, req releaserule.RefreshDiffPkgReq) (*EpArtifactManager.DefaultResp, *service.ServiceError) {

	c := container.GetContainer()
	for _, artId := range req.GetArtifactIds() {
		art, err := c.Mysql.ArtifactRepo.Find(uint(artId))
		if err != nil {
			logs.CtxWarn(ctx, "[RefreshDiffPkg] ArtifactRepo Find(%d) failed, err = %v", artId, err)
			continue
		}
		taskId, err := CreateDiffPackageTask(*art)
		if err != nil || taskId == 0 {
			logs.CtxWarn(ctx, "[RefreshDiffPkg] CreateDiffPackageTask(%d) failed, err = %v", artId, err)
			continue
		}
		logs.CtxInfo(ctx, "[RefreshDiffPkg] UpdateDiffPackages CreateDiffPackageTask success, taskID:%v, artifactId:%v, artifactVersion:%v",
			taskId, art.Id, art.UpdateVersion)
	}
	return EpArtifactManager.NewDefaultResp(), nil
}
