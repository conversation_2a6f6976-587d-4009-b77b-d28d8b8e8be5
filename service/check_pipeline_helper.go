package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"code.byted.org/bits/common_lib/utils"
	"code.byted.org/clientQA/artifact-manager/container"
	"code.byted.org/clientQA/artifact-manager/domain"
	"code.byted.org/clientQA/artifact-manager/framework/thirdparty/bytest"
	"code.byted.org/clientQA/artifact-manager/framework/util"
	"code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/checkpipeline"
	"code.byted.org/clientQA/rls_util/errorutil"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"github.com/spf13/cast"
)

const (
	// workflow_id 参数
	WorkflowID = "workflow_id"

	// 稳定性测试的 Service_ID
	StabilityTestServiceID   = 1
	StabilityTestServiceName = "稳定性测试"

	// 默认填充的稳定性测试对应的 ByTest 任务 check_workflow_tasks ID
	StabilityTestWorkflowTaskIosId     = 34
	StabilityTestWorkflowTaskAndroidId = 33

	StabilityTestWorkflowTaskIosIdBoe     = 24
	StabilityTestWorkflowTaskAndroidIdBoe = 25

	// 默认填充的稳定性测试对应的 ByTest 任务 check_tools_config ID
	StabilityTestCheckToolsConfigIosId            = 62
	StabilityTestCheckToolsConfigAndroidId        = 61
	StabilityTestCheckToolsConfigIosOverseaId     = 66
	StabilityTestCheckToolsConfigAndroidOverseaId = 67

	StabilityTestCheckToolsConfigIosIdBoe            = 78
	StabilityTestCheckToolsConfigAndroidIdBoe        = 77
	StabilityTestCheckToolsConfigIosOverseaIdBoe     = 79
	StabilityTestCheckToolsConfigAndroidOverseaIdBoe = 80

	// 默认填充的稳定性测试对应的 ByTest 任务 check_default_tools_config ID
	StabilityTestCheckToolsDefaultConfigIosId            = 28
	StabilityTestCheckToolsDefaultConfigAndroidId        = 27
	StabilityTestCheckToolsDefaultConfigIosOverseaId     = 29
	StabilityTestCheckToolsDefaultConfigAndroidOverseaId = 30

	StabilityTestCheckToolsDefaultConfigIosIdBoe            = 23
	StabilityTestCheckToolsDefaultConfigAndroidIdBoe        = 22
	StabilityTestCheckToolsDefaultConfigIosOverseaIdBoe     = 24
	StabilityTestCheckToolsDefaultConfigAndroidOverseaIdBoe = 25
)

type FillDefaultBytestServiceAdapter struct {
	isCustomized            bool
	minLevel                int
	appID                   int64
	artifactID              int64
	region                  string
	userEmail               string
	toolsName               []string
	platform                checkpipeline.Platform
	toolsConfigList         []domain.ToolsConfig
	tasksJobByToolsConfigID map[uint]*domain.TasksJobs
	toolsStage              map[uint]domain.WorkflowTasks
	defaultToolsMap         map[uint]domain.DefaultToolsConfig
}

// FillDefaultBytestService handle filling the default Bytest CI tasks if none is given
func FillDefaultBytestService(ctx context.Context, adapter FillDefaultBytestServiceAdapter) (FillDefaultBytestServiceAdapter, error) {

	c := container.GetContainer()

	logs.CtxInfo(ctx, "[FillDefaultBytestService] Start check fill default bytest service. adapter:%+v", adapter)

	creator := adapter.userEmail
	if strings.Contains(adapter.userEmail, "@") {
		creator = strings.Split(adapter.userEmail, "@")[0]
	}

	// 0. 检查是否需要填充默认 bytest 任务，不需要直接返回
	needToFillDefaultByTest, newMinLevel := IsNeedToFillDefaultByTest(ctx, adapter)
	if !needToFillDefaultByTest {
		adapter.minLevel = newMinLevel
		return adapter, nil
	}

	// 1. 抓取默认稳定性测试模版 ID
	stabilityTestTemplate, err := bytest.GetBytestStabilityTestDefaultTemplateID(adapter.appID, adapter.platform, creator, adapter.region)
	if err != nil {
		return adapter, errorutil.NewErr(err, "[FillDefaultBytestService] failed to get template id. adapter:%+v", utils.ToJson(adapter))
	}

	// 2. 拿到对应的 ToolsConfig, DefaultToolsConfig 数据
	toolsConfigWhereFilter := map[string]interface{}{ID: getStabilityTaskConfigID(adapter.platform, adapter.region)}
	defaultToolsConfigWhereFilter := map[string]interface{}{ID: getStabilityTaskDefaultConfigID(adapter.platform, adapter.region)}
	newToolsConfigList, err := c.Mysql.ToolsConfigRepo.FindAllByFilter(toolsConfigWhereFilter, nil)
	if err != nil || len(newToolsConfigList) != 1 {
		return adapter, errorutil.NewErr(err, "[FillDefaultBytestService] failed to get tools config. adapter:%+v, toolsConfigWhereFilter:%+v", utils.ToJson(adapter), utils.ToJson(toolsConfigWhereFilter))
	}
	newDefaultToolsConfigList, err := c.Mysql.DefaultToolsConfigRepo.FindAllByFilter(defaultToolsConfigWhereFilter, nil)
	if err != nil || len(newDefaultToolsConfigList) != 1 {
		return adapter, errorutil.NewErr(err, "[FillDefaultBytestService] failed to get fail to get default tools config. adapter:%+v, defaultToolsConfigWhereFilter:%+v", utils.ToJson(adapter), utils.ToJson(defaultToolsConfigWhereFilter))
	}
	newToolsConfig := newToolsConfigList[0]
	newDefaultToolsConfig := newDefaultToolsConfigList[0]

	// 3. 把从接口中拿到的 template 信息填充到 newToolsConfig / toolsMap
	newToolsConfig.ToolsNameCN = stabilityTestTemplate.Name
	newParam, err := getNewParam(stabilityTestTemplate)
	if err != nil {
		return adapter, errorutil.NewErr(err, "[FillDefaultBytestService] failed to marshal newParam. adapter:%+v, newToolsConfig:%+v, newDefaultToolsConfig:%+v, stabilityTestTemplate:%+v", utils.ToJson(adapter), utils.ToJson(newToolsConfig), utils.ToJson(newDefaultToolsConfig), utils.ToJson(stabilityTestTemplate))
	}
	newToolsConfig.Params = newParam
	newDisplayInfo, err := getNewDisplayInfo(stabilityTestTemplate.Description)
	if err != nil {
		return adapter, errorutil.NewErr(err, "[FillDefaultBytestService] failed to get newDisplayInfo. adapter:%+v, newToolsConfig:%+v, newDefaultToolsConfig:%+v, stabilityTestTemplate:%+v", utils.ToJson(adapter), utils.ToJson(newToolsConfig), utils.ToJson(newDefaultToolsConfig), utils.ToJson(stabilityTestTemplate))
	}
	newToolsConfig.DisplayInfo = newDisplayInfo
	workflowTaskData, err := GetDefaultStabilityBytestWorkflowTask(adapter.platform)
	if err != nil {
		return adapter, errorutil.NewErr(err, "[FillDefaultBytestService] failed to get workflow task data. adapter:%+v, newToolsConfig:%+v, newDefaultToolsConfig:%+v, stabilityTestTemplate:%+v", utils.ToJson(adapter), utils.ToJson(newToolsConfig), utils.ToJson(newDefaultToolsConfig), utils.ToJson(stabilityTestTemplate))
	}

	// 4. 更新入参的字段
	adapter.toolsConfigList = append(adapter.toolsConfigList, newToolsConfig)
	adapter.defaultToolsMap[newDefaultToolsConfig.ID] = newDefaultToolsConfig
	adapter.toolsStage[newToolsConfig.ID] = workflowTaskData[0]
	adapter.minLevel = util.Min(newMinLevel, workflowTaskData[0].StageLevel)
	adapter.toolsName = append(adapter.toolsName, newDefaultToolsConfig.ToolsNameCN)

	logs.CtxInfo(ctx, "[FillDefaultBytestService] fillDefaultBytest success. adapter:%+v", adapter)

	return adapter, nil
}

// IsNeedToFillDefaultByTest check whether we need to fill the default Bytest given the current toolsConfig
func IsNeedToFillDefaultByTest(ctx context.Context, adapter FillDefaultBytestServiceAdapter) (bool, int) {

	newMinLevel := 999
	needToFillDefaultBytest := true

	// 非 iOS / Android 不需要添加 ByTest
	if adapter.platform != checkpipeline.Platform_PlatformiOS &&
		adapter.platform != checkpipeline.Platform_PlatformAndroid {
		return false, adapter.minLevel
	}

	templateIDtoServiceIDMap, err := GetTemplateIDtoServiceIDMap(ctx, adapter.appID, adapter.platform)
	if err != nil {
		logs.CtxError(ctx, "[IsNeedToFillDefaultByTest] error occurred while retrieving inverted map. err:%s, appId:%v, platform:%v", err.Error(), adapter.appID, adapter.platform)
	}

	for index := 0; index < len(adapter.toolsConfigList); index++ {
		toolsConfig := adapter.toolsConfigList[index]
		defaultToolsConfig := adapter.defaultToolsMap[toolsConfig.DefaultToolsID]

		// 0. 拿Job URL 过滤掉不会被执行的 bytest 任务
		urls := util.UnmarshalParam(defaultToolsConfig.Urls)
		if _, exists := urls[JOB_URL]; !exists {
			// 如果 URls 字段中没有 job_url，跳过
			logs.CtxWarn(ctx, "[IsNeedToFillDefaultByTest] job url doesn't exist. defaultToolsConfig:%+v", utils.ToJson(defaultToolsConfig))
			continue
		}
		jobURL, ok := urls[JOB_URL].(string)
		if !ok {
			// 如果 URls 字段中 job_url 不是 string，跳过
			logs.CtxWarn(ctx, "[IsNeedToFillDefaultByTest] job url is invalid. defaultToolsConfig:%+v", utils.ToJson(defaultToolsConfig))
			continue
		}
		if adapter.isCustomized &&
			isBytestAutoTestURL(jobURL) &&
			adapter.tasksJobByToolsConfigID[toolsConfig.ID].BuildConfigID == 0 {
			// 如果用户走自定义配置的 bytest 任务，跳过默认的 bytest 任务
			logs.CtxInfo(ctx, "[IsNeedToFillDefaultByTest] skip default bytest. adapter:%+v", utils.ToJson(adapter))
			continue
		}

		// 1. 任务走到这里说明是预期要被执行的回归测试任务, 更新下 MinLevel 的值
		currTaskStageLevel := adapter.toolsStage[toolsConfig.ID].StageLevel
		if currTaskStageLevel < newMinLevel {
			newMinLevel = currTaskStageLevel
		}

		// 2. 拿到 bytest 任务的 template_id
		var templateID int64
		toolsConfigParam := util.UnmarshalParam(toolsConfig.Params)
		if _, exists := toolsConfigParam[TEMPLATE_ID]; !exists {
			logs.CtxWarn(ctx, "[IsNeedToFillDefaultByTest] don't have template ID key. defaultToolsConfig:%+v", utils.ToJson(defaultToolsConfig))
			continue
		}
		templateID, err = strconv.ParseInt(toolsConfigParam[TEMPLATE_ID].(string), 10, 64)
		if err != nil {
			logs.CtxError(ctx, "[IsNeedToFillDefaultByTest] template ID cannot be parsed. adapter:%+v, err:%s", adapter, err.Error())
			continue
		}

		// 3. 看这个 template_id 对应的 service_id 是不是 "稳定性测试" 的 service_id，如果是，那么我们就不需要填充默认测试任务
		if templateIDtoServiceIDMap[templateID] == StabilityTestServiceID {
			logs.CtxInfo(ctx, "[IsNeedToFillDefaultByTest] no need to fill bytest for current check pipeline. adapter:%+v", adapter)
			needToFillDefaultBytest = false
		}
	}

	logs.CtxInfo(ctx, "[IsNeedToFillDefaultByTest] success. needToFillDefaultBytest:%v, newMinLevel:%v, adapter:%+v", needToFillDefaultBytest, newMinLevel, adapter)

	return needToFillDefaultBytest, newMinLevel

}

// FillWorkflowIDtoToolsRunHistory 用于填充 workflow_id 字段到 send_param 中，目前只针对"发布版本覆盖安装&Top厂商兼容性"任务
func FillWorkflowIDtoToolsRunHistory(ctx context.Context, runHistoryIDs []uint) (err error) {
	defer func() {
		if err != nil {
			err = errorutil.NewErr(err, "[FillWorkflowIDtoToolsRunHistory] failed! runHistoryIDs:%v", runHistoryIDs)
		}
	}()

	toolsRunHistoryRepo := container.GetContainer().Mysql.ToolsRunHistoryRepo

	for _, runHistoryID := range runHistoryIDs {
		toolsRunHistoryDO, findErr := toolsRunHistoryRepo.Find(runHistoryID)
		if findErr != nil {
			return errorutil.NewErr(findErr, "[FillWorkflowIDtoToolsRunHistory] failed to get Run history. runHistoryID:%v", runHistoryID)
		}
		logs.CtxInfo(ctx, "[FillWorkflowIDtoToolsRunHistory] toolsRunHistoryDO:%+v", utils.ToJson(toolsRunHistoryDO))

		// unmarshal 已有的 send parameter
		var sendParameters map[string]interface{}
		updateMapper := make(map[string]interface{})
		sendParametersByteArray := []byte(toolsRunHistoryDO.SendParam)
		unmarshalErr := json.Unmarshal(sendParametersByteArray, &sendParameters)
		if unmarshalErr != nil {
			return errorutil.NewErr(unmarshalErr, "[FillWorkflowIDtoToolsRunHistory] failed to unmarshal send param. sendParam:%s", toolsRunHistoryDO.SendParam)
		}

		// 将当前的 sendParameter中加上 workflow_id 字段，并 marshal
		sendParameters[WorkflowID] = runHistoryID
		sendParametersRaw, marshalErr := json.Marshal(sendParameters)
		if marshalErr != nil {
			return errorutil.NewErr(marshalErr, "[FillWorkflowIDtoToolsRunHistory] failed to marshal updated send param. send param:%+v", utils.ToJson(sendParameters))
		}

		// 将 marshal 好的 send_param 字段写入更新的 mapper，并调用 GORM 的 update
		updateMapper[SEND_PARAM] = string(sendParametersRaw)
		updateErr := toolsRunHistoryRepo.Update(runHistoryID, updateMapper)
		if updateErr != nil {
			return errorutil.NewErr(updateErr, "[FillWorkflowIDtoToolsRunHistory] failed to update. ID:%v, updateMapper:%+v", runHistoryID, utils.ToJson(updateMapper))
		}
		logs.CtxInfo(ctx, "[FillWorkflowIDtoToolsRunHistory] success! runHistoryID:%v, toolsRunHistoryDO:%+v, updateMapper:%+v", runHistoryID, utils.ToJson(toolsRunHistoryDO), utils.ToJson(updateMapper))
	}

	return nil
}

// GetTemplateIDtoServiceIDMap Get the map stores the inverted index of the template ID to its belonging Service ID
func GetTemplateIDtoServiceIDMap(ctx context.Context, appID int64, platform checkpipeline.Platform) (result map[int64]int64, err error) {
	result = make(map[int64]int64)
	serviceList, err := bytest.GetBytestServiceAndTemplate(ctx, appID, platform, "")
	if err != nil {
		return result, errorutil.NewErr(err, "[GetTemplateIDtoServiceNameMap] get template service data failed. appID:%v, platform: %v", appID, platform)
	}
	for _, serviceData := range serviceList {
		for _, template := range serviceData.Templates {
			result[template.ID] = int64(template.ServiceID)
		}
	}
	return result, nil
}

func GetDefaultStabilityBytestWorkflowTask(platform checkpipeline.Platform) ([]domain.WorkflowTasks, error) {
	c := container.GetContainer()
	IDs := getStabilityWorkflowTaskIDs()
	inFilter := map[string]interface{}{
		ID:       IDs,
		PLATFORM: platform,
	}
	workflowTasks, err := c.Mysql.WorkflowTasksRepo.FindAllByFilter(nil, inFilter)
	if err != nil {
		return nil, errorutil.NewErr(err, "[GetDefaultStabilityBytestWorkflowTask] get workflow task failed. inFilter:%+v", utils.ToJson(inFilter))
	}
	return workflowTasks, nil
}

// IsDefaultStabilityTask whether the current task is added stability bytest
func IsDefaultStabilityTask(toolsConfigID uint) bool {
	if env.IsBoe() {
		toolsConfigIDList := []uint{StabilityTestCheckToolsConfigAndroidIdBoe, StabilityTestCheckToolsConfigIosIdBoe,
			StabilityTestCheckToolsConfigAndroidOverseaIdBoe, StabilityTestCheckToolsConfigIosOverseaIdBoe}
		return util.ArrayUintContains(toolsConfigIDList, toolsConfigID)
	} else {
		toolsConfigIDList := []uint{StabilityTestCheckToolsConfigAndroidId, StabilityTestCheckToolsConfigIosId,
			StabilityTestCheckToolsConfigAndroidOverseaId, StabilityTestCheckToolsConfigIosOverseaId}
		return util.ArrayUintContains(toolsConfigIDList, toolsConfigID)
	}
}

func isBytestAutoTestURL(url string) bool {
	bytestAutoTestURL := util.Config.Bytest.URL + "auto_test/inner/"
	return strings.Contains(url, bytestAutoTestURL)
}

func getStabilityWorkflowTaskIDs() []int64 {
	if env.IsBoe() {
		return []int64{StabilityTestWorkflowTaskAndroidIdBoe, StabilityTestWorkflowTaskIosIdBoe}
	} else {
		return []int64{StabilityTestWorkflowTaskAndroidId, StabilityTestWorkflowTaskIosId}
	}
}

func getStabilityTaskConfigID(platform checkpipeline.Platform, region string) int64 {
	if env.IsBoe() {
		switch {
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.DomesticApp:
			return StabilityTestCheckToolsConfigAndroidIdBoe
		case platform == checkpipeline.Platform_PlatformiOS && region == util.DomesticApp:
			return StabilityTestCheckToolsConfigIosIdBoe
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.OverseaApp:
			return StabilityTestCheckToolsConfigAndroidOverseaIdBoe
		case platform == checkpipeline.Platform_PlatformiOS && region == util.OverseaApp:
			return StabilityTestCheckToolsConfigIosOverseaIdBoe
		}
	} else {
		switch {
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.DomesticApp:
			return StabilityTestCheckToolsConfigAndroidId
		case platform == checkpipeline.Platform_PlatformiOS && region == util.DomesticApp:
			return StabilityTestCheckToolsConfigIosId
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.OverseaApp:
			return StabilityTestCheckToolsConfigAndroidOverseaId
		case platform == checkpipeline.Platform_PlatformiOS && region == util.OverseaApp:
			return StabilityTestCheckToolsConfigIosOverseaId
		}
	}
	// 没有命中任何一个条件，返回不存在 ID
	return -1
}

func getStabilityTaskDefaultConfigID(platform checkpipeline.Platform, region string) int64 {
	if env.IsBoe() {
		switch {
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.DomesticApp:
			return StabilityTestCheckToolsDefaultConfigAndroidIdBoe
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.OverseaApp:
			return StabilityTestCheckToolsDefaultConfigAndroidOverseaIdBoe
		case platform == checkpipeline.Platform_PlatformiOS && region == util.DomesticApp:
			return StabilityTestCheckToolsDefaultConfigIosIdBoe
		case platform == checkpipeline.Platform_PlatformiOS && region == util.OverseaApp:
			return StabilityTestCheckToolsDefaultConfigIosOverseaIdBoe
		}
	} else {
		switch {
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.DomesticApp:
			return StabilityTestCheckToolsDefaultConfigAndroidId
		case platform == checkpipeline.Platform_PlatformAndroid && region == util.OverseaApp:
			return StabilityTestCheckToolsDefaultConfigAndroidOverseaId
		case platform == checkpipeline.Platform_PlatformiOS && region == util.DomesticApp:
			return StabilityTestCheckToolsDefaultConfigIosId
		case platform == checkpipeline.Platform_PlatformiOS && region == util.OverseaApp:
			return StabilityTestCheckToolsDefaultConfigIosOverseaId
		}
	}
	// 没有命中任何一个条件，返回不存在 ID
	return -1
}

func getNewParam(templateData bytest.Template) (string, error) {

	newParamMap := map[string]interface{}{
		"template_id": cast.ToString(templateData.ID),
	}

	newParam, err := json.Marshal(newParamMap)
	if err != nil {
		return "", fmt.Errorf("[getNewParam] failed to marshal newParam. templateData:%+v", utils.ToJson(templateData))
	}

	return string(newParam), nil

}

func getNewDisplayInfo(description string) (string, error) {
	displayInfo := map[string]string{
		BYTEST_SERVICE_ID:           cast.ToString(StabilityTestServiceID),
		BYTEST_SERVICE_NAME:         StabilityTestServiceName,
		BYTEST_TEMPLATE_DESCRIPTION: description,
	}
	displayInfoStr, err := json.Marshal(displayInfo)
	if err != nil {
		return "", fmt.Errorf("[getNewDisplayInfo] failed to marshal displayInfo. displayInfo: %+v", utils.ToJson(displayInfo))
	}

	return string(displayInfoStr), err
}

func ParseJobUrl(jobUrl string, totalParam map[string]interface{}) error {
	parsedUrl, err := url.Parse(jobUrl)
	if err != nil {
		return errorutil.NewErr(err, "[ParseJobUrl] parse url failed. jobUrl:%s", jobUrl)
	}
	queriesKeyValuePairs := parsedUrl.Query()

	for queryKey, queryValue := range queriesKeyValuePairs {
		if _, keyExist := totalParam[queryKey]; keyExist {
			// 如果 totalParam 已经有这个 query key 了，就不复写了！！
			continue
		}
		// queryValue 目前假设是一个，如果有多个改这边逻辑
		totalParam[queryKey] = queryValue[0]
	}

	return nil
}
