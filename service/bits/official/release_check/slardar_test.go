package release_check

import (
	"code.byted.org/clientQA/artifact-manager/framework/util"
	BytedanceBitsIntegration "code.byted.org/clientQA/artifact-manager/kitex_gen/bytedance/bits/integration"
	EpArtifactManagerBitsgray "code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/bitsgray"
	"code.byted.org/clientQA/artifact-manager/rpc"
	"context"
	"testing"
)

func TestGetSlardarData(t *testing.T) {
	resp, err := GetSlardarData(context.Background(), &EpArtifactManagerBitsgray.GetSlardarDataReq{
		IntegrationID: 73,
		AppID:         1302,
	})
	if err != nil {
		t.Fatalf("###########X###########, %+v \n", err)
	} else {
		t.Logf("###########X###########, %+v \n", util.ToJsonSimple(resp))
	}
}

func TestGetAllGrayReleaseInIntegration(t *testing.T){
	resp, err := getAllGrayReleaseInIntegration(81, 13, "ANDROID")
	if err != nil {
		t.Fatalf("###########X###########, %+v \n", err)
	} else {
		t.Logf("###########X###########, %+v \n", util.ToJsonSimple(resp))
	}
}

func TestGrayList(t *testing.T) {
	resp, err := rpc.Integration.GrayList(context.Background(), &BytedanceBitsIntegration.GrayListRequest{
		IntegrationID: 338,
		PageSize:      PageSizeBigEnough,
		PageNum:       1,
	})
	if err != nil {
		t.Fatalf("###########X###########, %+v \n", err)
	} else {
		t.Logf("###########X###########, %+v \n", util.ToJsonSimple(resp))
	}
}

func TestGetArtifactIDsFromGray(t *testing.T) {
	resp, err := getArtifactIDsFromGray(84)
	if err != nil {
		t.Fatalf("###########X###########, %+v \n", err)
	} else {
		t.Logf("###########X###########, %+v \n", util.ToJsonSimple(resp))
	}
}