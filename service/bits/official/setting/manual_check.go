package setting

import (
	EpArtifactManagerBitsgray "code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/bitsgray"
	"code.byted.org/clientQA/artifact-manager/service/bits/setting"
	"code.byted.org/clientQA/rls_util/errorutil"
	"context"
	"encoding/json"
)

type ManualSetting struct {
	V1s     []*EpArtifactManagerBitsgray.ManualV1 `json:"v1"`
	V1MaxId int64                                 `json:"v1_max_id"`
}

func ListOfficialManualCheck(ctx context.Context, req *EpArtifactManagerBitsgray.ListOfficialManualCheckReq) (
	*EpArtifactManagerBitsgray.ListOfficialManualCheckResp, error) {
	manualSetting, err := getManualSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[ListOfficialManualCheck] err get manual setting")
	} else if manualSetting == nil {
		return &EpArtifactManagerBitsgray.ListOfficialManualCheckResp{}, nil
	} else {
		return &EpArtifactManagerBitsgray.ListOfficialManualCheckResp{ManualV1: manualSetting.V1s}, nil
	}
}

func AddOfficialManualCheck(ctx context.Context, req *EpArtifactManagerBitsgray.AddOfficialManualCheckReq) (
	*EpArtifactManagerBitsgray.AddOfficialManualCheckResp, error) {
	manualSetting, err := getManualSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[AddOfficialManualCheck] err get manual setting")
	}

	var v1InitialID int64 = 1
	if manualSetting == nil {
		newManualSetting := ManualSetting{
			V1s: []*EpArtifactManagerBitsgray.ManualV1{
				req.ManualV1,
			},
			V1MaxId: v1InitialID,
		}
		newManualSetting.V1s[0].ID = v1InitialID
		err := addManualSetting(req.AppID, newManualSetting)
		if err != nil {
			return nil, errorutil.NewErr(err, "[AddOfficialManualCheck] err add manual setting")
		}
	} else {
		currentMaxID := manualSetting.V1MaxId
		req.ManualV1.ID = currentMaxID + 1
		manualSetting.V1s = append(manualSetting.V1s, req.ManualV1)
		manualSetting.V1MaxId = currentMaxID + 1
		err = updateManualSetting(req.AppID, *manualSetting)
		if err != nil {
			return nil, errorutil.NewErr(err, "[AddOfficialManualCheck] update manual setting")
		}
	}

	return nil, nil
}

func UpdateOfficialManualCheck(ctx context.Context, req *EpArtifactManagerBitsgray.UpdateOfficialManualCheckReq) (
	*EpArtifactManagerBitsgray.UpdateOfficialManualCheckResp, error) {
	manualSetting, err := getManualSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[UpdateOfficialManualCheck] err get manual setting")
	}

	if manualSetting == nil {
		return nil, errorutil.NewErr(err, "[UpdateOfficialManualCheck] manual setting not found")
	} else {
		for i, SettingItem := range manualSetting.V1s {
			if SettingItem.ID == req.ManualV1.ID {
				manualSetting.V1s[i] = req.ManualV1
			}
		}
		err = updateManualSetting(req.AppID, *manualSetting)
		if err != nil {
			return nil, errorutil.NewErr(err, "[UpdateOfficialManualCheck] update manual setting")
		}
	}

	return nil, nil
}

func DeleteOfficialManualCheck(ctx context.Context, req *EpArtifactManagerBitsgray.DeleteOfficialManualCheckReq) (
	*EpArtifactManagerBitsgray.DeleteOfficialManualCheckResp, error) {
	manualSetting, err := getManualSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[DeleteOfficialManualCheck] err get manual setting")
	}
	var newV1Settings []*EpArtifactManagerBitsgray.ManualV1
	for _, SettingItem := range manualSetting.V1s {
		if SettingItem.ID != req.V1Id {
			newV1Settings = append(newV1Settings, SettingItem)
		}
	}
	err = updateManualSetting(req.AppID, ManualSetting{
		V1s: newV1Settings,
		V1MaxId: manualSetting.V1MaxId,
	})
	if err != nil {
		return nil, errorutil.NewErr(err, "[DeleteOfficialManualCheck] update manual setting")
	}
	return nil, nil
}

func getManualSettingType() string {
	return "setting.official.manual_check"
}

func getManualSetting(appID int64) (*ManualSetting, error) {
	settingDomain, err := setting.GetSetting(appID, getManualSettingType())
	if err == setting.ErrRecordNotFound {
		return nil, nil
	} else if err != nil {
		return nil, errorutil.NewErr(err, "[getManualSetting] record not found")
	}

	manualSetting := ManualSetting{}
	err = json.Unmarshal([]byte(settingDomain.Settings), &manualSetting)
	if err != nil {
		return nil, errorutil.NewErr(err, "[getManualSetting] err unmarshal, setting: %v", manualSetting)
	}

	return &manualSetting, nil
}

func updateManualSetting(appID int64, manualSetting ManualSetting) error {
	settingBytes, err := json.Marshal(manualSetting)
	if err != nil {
		return errorutil.NewErr(err, "[updateManualSetting] err marshal manual setting, %v, %+v",
			appID, manualSetting)
	}

	err = setting.UpdateSetting(appID, getManualSettingType(), string(settingBytes))
	if err != nil {
		return errorutil.NewErr(err, "[updateManualSetting] err update setting, %v, %v", appID, manualSetting)
	}

	return nil
}

func addManualSetting(appID int64, manualSetting ManualSetting) error {
	settingBytes, err := json.Marshal(manualSetting)
	if err != nil {
		return errorutil.NewErr(err, "[addManualSetting] err marshal manual setting, %v, %+v",
			appID, manualSetting)
	}

	err = setting.AddSetting(appID, getManualSettingType(), string(settingBytes))
	if err != nil {
		return errorutil.NewErr(err, "[addManualSetting] err update setting, %v, %v", appID, manualSetting)
	}

	return nil
}
