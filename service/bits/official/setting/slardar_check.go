package setting

import (
	"context"
	"encoding/json"

	EpArtifactManagerBitsgray "code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/bitsgray"
	"code.byted.org/clientQA/artifact-manager/service/bits/setting"
	"code.byted.org/clientQA/rls_util/errorutil"
)

type SlardarSetting struct {
	V1s []*EpArtifactManagerBitsgray.V1 `json:"v1"`
}

func ListOfficialSettingSlardar(ctx context.Context, req *EpArtifactManagerBitsgray.ListOfficialSettingSlardarReq) (
	*EpArtifactManagerBitsgray.ListOfficialSettingSlardarResp, error) {
	slardarSetting, err := getSlardarSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[ListOfficialSettingSlardar] err get fusing setting")
	} else if slardarSetting == nil {
		return &EpArtifactManagerBitsgray.ListOfficialSettingSlardarResp{}, nil
	} else {
		return &EpArtifactManagerBitsgray.ListOfficialSettingSlardarResp{V1: slardarSetting.V1s}, nil
	}
}

func AddOfficialSettingSlardar(ctx context.Context, req *EpArtifactManagerBitsgray.AddOfficialSettingSlardarReq) (
	*EpArtifactManagerBitsgray.AddOfficialSettingSlardarResp, error) {
	slardarSetting, err := getSlardarSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[AddOfficialSettingSlardar] err get fusing setting")
	}

	if slardarSetting == nil {
		var newSlardarSetting SlardarSetting
		v1Setting := req.V1
		v1Setting.ID = 1
		newSlardarSetting.V1s = append(newSlardarSetting.V1s, v1Setting)
		err := addSlardarSetting(req.AppID, newSlardarSetting)
		if err != nil {
			return nil, errorutil.NewErr(err, "[AddOfficialSettingSlardar] err add slardar setting")
		}
	} else {
		currentMaxID := getSlardarSettingMaxID(*slardarSetting)
		req.V1.ID = currentMaxID + 1
		slardarSetting.V1s = append(slardarSetting.V1s, req.V1)
		err = updateSlardarSetting(req.AppID, *slardarSetting)
		if err != nil {
			return nil, errorutil.NewErr(err, "[AddOfficialSettingSlardar] update slardar setting")
		}
	}

	return nil, nil
}

func UpdateOfficialSettingSlardar(ctx context.Context, req *EpArtifactManagerBitsgray.UpdateOfficialSettingSlardarReq) (
	*EpArtifactManagerBitsgray.UpdateOfficialSettingSlardarResp, error) {
	slardarSetting, err := getSlardarSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[UpdateOfficialSettingSlardar] err get slardar setting")
	}

	if slardarSetting == nil {
		return nil, errorutil.NewErr(err, "[UpdateOfficialSettingSlardar] slardar setting not found")
	} else {
		for i, slardarSettingItem := range slardarSetting.V1s {
			if slardarSettingItem.ID == req.V1.ID {
				slardarSetting.V1s[i] = req.V1
			}
		}
		err = updateSlardarSetting(req.AppID, *slardarSetting)
		if err != nil {
			return nil, errorutil.NewErr(err, "[UpdateOfficialSettingSlardar] update slardar setting")
		}
	}

	return nil, nil
}

func DeleteOfficialSettingSlardar(ctx context.Context, req *EpArtifactManagerBitsgray.DeleteOfficialSettingSlardarReq) (
	*EpArtifactManagerBitsgray.DeleteOfficialSettingSlardarResp, error) {
	slardarSetting, err := getSlardarSetting(req.AppID)
	if err != nil {
		return nil, errorutil.NewErr(err, "[UpdateOfficialSettingSlardar] err get slardar setting")
	}
	var newV1Settings []*EpArtifactManagerBitsgray.V1
	for _, slardarSettingItem := range slardarSetting.V1s {
		if slardarSettingItem.ID != req.V1Id {
			newV1Settings = append(newV1Settings, slardarSettingItem)
		}
	}
	err = updateSlardarSetting(req.AppID, SlardarSetting{
		V1s: newV1Settings,
	})
	if err != nil {
		return nil, errorutil.NewErr(err, "[UpdateOfficialSettingSlardar] update slardar setting")
	}
	return nil, nil
}

func getSlardarSettingType() string {
	return "setting.official.slardar_check"
}

func getSlardarSetting(appID int64) (*SlardarSetting, error) {
	settingDomain, err := setting.GetSetting(appID, getSlardarSettingType())
	if err == setting.ErrRecordNotFound {
		return nil, nil
	} else if err != nil {
		return nil, errorutil.NewErr(err, "[ListOfficialSettingSlardar] record not found")
	}

	slardarSetting := SlardarSetting{}
	err = json.Unmarshal([]byte(settingDomain.Settings), &slardarSetting)
	if err != nil {
		return nil, errorutil.NewErr(err, "[ListOfficialSettingSlardar] err unmarshal, setting: %v", slardarSetting)
	}

	return &slardarSetting, nil
}

func getSlardarSettingMaxID(slardarSetting SlardarSetting) int64 {
	var currentMaxID int64 = -1
	for _, settingItem := range slardarSetting.V1s {
		if settingItem.ID > currentMaxID {
			currentMaxID = settingItem.ID
		}
	}

	return currentMaxID
}

func updateSlardarSetting(appID int64, slardarSetting SlardarSetting) error {
	settingBytes, err := json.Marshal(slardarSetting)
	if err != nil {
		return errorutil.NewErr(err, "[updateSlardarSetting] err marshal slardar setting, %v, %+v",
			appID, slardarSetting)
	}

	err = setting.UpdateSetting(appID, getSlardarSettingType(), string(settingBytes))
	if err != nil {
		return errorutil.NewErr(err, "[updateSlardarSetting] err update setting, %v, %v", appID, slardarSetting)
	}

	return nil
}

func addSlardarSetting(appID int64, slardarSetting SlardarSetting) error {
	settingBytes, err := json.Marshal(slardarSetting)
	if err != nil {
		return errorutil.NewErr(err, "[addSlardarSetting] err marshal slardar setting, %v, %+v",
			appID, slardarSetting)
	}

	err = setting.AddSetting(appID, getSlardarSettingType(), string(settingBytes))
	if err != nil {
		return errorutil.NewErr(err, "[addSlardarSetting] err update setting, %v, %v", appID, slardarSetting)
	}

	return nil
}
