package check_list

import (
	"code.byted.org/clientQA/artifact-manager/container"
	"code.byted.org/clientQA/artifact-manager/framework/util"
	EpArtifactManagerBitsgray "code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/bitsgray"
	"code.byted.org/clientQA/artifact-manager/rpc"
	"context"
	"fmt"
	"os"
	"testing"
)

func TestMain(m *testing.M) {
	container.InitFramework()
	rpc.InitRPC()
	os.Exit(m.Run())
}

func TestGetCheckListSetting(t *testing.T) {
	resp, err := GetCheckListSetting(context.Background(), &EpArtifactManagerBitsgray.GetCheckListSettingReq{
		AppID: 2,
		Type:  "gray",
	})
	if err != nil {
		fmt.Printf("###########X###########, %+v \n", err)
	} else {
		fmt.Printf("###########X###########, %+v \n", util.ToJsonSimple(resp))
	}
}

func TestUpdateCheckListSetting(t *testing.T) {
	resp, err := UpdateCheckListSetting(context.Background(), &EpArtifactManagerBitsgray.UpdateCheckListSettingReq{
		AppID: 2,
		Type:  "lr",
		Settings: &EpArtifactManagerBitsgray.Settings{
			ReviewWhenEnterNextStage: false,
			AllowNextStage:           false,
			ReviewConfig: []*EpArtifactManagerBitsgray.ReviewConfig{
				{
					RequireAll: false,
					Reviewers:  []string{"people1", "people3"},
				},
			},
		},
	})
	if err != nil {
		fmt.Printf("###########X###########, %+v \n", err)
	} else {
		fmt.Printf("###########X###########, %+v \n", util.ToJsonSimple(resp))
	}
}
