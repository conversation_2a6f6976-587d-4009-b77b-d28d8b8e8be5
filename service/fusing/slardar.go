package fusing

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"code.byted.org/bits/common_lib/utils"
	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
	errors2 "code.byted.org/gopkg/pkg/errors"
	"github.com/spf13/cast"

	"code.byted.org/clientQA/artifact-manager/adaptor"
	"code.byted.org/clientQA/artifact-manager/container"
	"code.byted.org/clientQA/artifact-manager/dal/mysql/plugin_patch/plugin_config_rule"
	MysqlSlardar "code.byted.org/clientQA/artifact-manager/dal/mysql/slardar"
	"code.byted.org/clientQA/artifact-manager/domain"
	"code.byted.org/clientQA/artifact-manager/framework/thirdparty/skynet"
	"code.byted.org/clientQA/artifact-manager/framework/util"
	BitsIntegration "code.byted.org/clientQA/artifact-manager/kitex_gen/bytedance/bits/integration"
	"code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/releaserule"
	EpArtifactManagerSlardar "code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/slardar"
	"code.byted.org/clientQA/artifact-manager/rpc"
	"code.byted.org/clientQA/artifact-manager/service"
	"code.byted.org/clientQA/artifact-manager/service/release"
	"code.byted.org/clientQA/artifact-manager/service/tool"
)

func ManualFuseRelease(ctx context.Context, req *EpArtifactManagerSlardar.ManualFuseReleaseReq) (*EpArtifactManagerSlardar.ManualFuseReleaseResp, *service.ServiceError) {
	m := container.GetContainer().Mysql

	bitsReleaseSlardar, err := m.SlardarRepo.Read.GetBitsReleaseSlardar(ctx, req.BitsReleaseSlardarID)
	if err != nil {
		logs.CtxError(ctx, "GetBitsReleaseSlardar error. err: %v", err)
		return &EpArtifactManagerSlardar.ManualFuseReleaseResp{}, service.NewServerError(err)
	}

	go func() {
		confirmReq := &BitsIntegration.FuseLarkConfirmNotifyRequest{
			IntegrationID:  cast.ToInt64(bitsReleaseSlardar.IntegrationId),
			BitsReleaseURL: util.SafeString(req.BitsReleaseURL),
			FuseName:       req.FuseName,
			FuseType:       cast.ToInt64(bitsReleaseSlardar.FuseType),
			AlarmTime:      util.SafeInt64(req.AlarmTime),
			Operator:       req.Operator,
			IsSuc:          true,
		}

		if err := FuseRelease(ctx, bitsReleaseSlardar.ReleaseType, bitsReleaseSlardar.ReleaseId); err != nil {
			logs.CtxError(ctx, "FuseRelease error. err: %v", err)

			// 发送熔断失败消息
			confirmReq.IsSuc = false
			if _, err := rpc.Integration.FuseLarkConfirmNotify(ctx, confirmReq); err != nil {
				logs.CtxError(ctx, "FuseLarkConfirmNotify error. err: %v", err)
			}
			return
		}

		if err := insertFuseRecord(ctx, bitsReleaseSlardar, bitsReleaseSlardar.SlardarName, req.FuseReason, req.Operator); err != nil {
			logs.CtxError(ctx, "insertFuseRecord error. err: %v", err)
		}

		// 发送熔断成功消息
		if _, err := rpc.Integration.FuseLarkConfirmNotify(ctx, confirmReq); err != nil {
			logs.CtxError(ctx, "FuseLarkConfirmNotify error. err: %v", err)
		}
	}()

	return &EpArtifactManagerSlardar.ManualFuseReleaseResp{}, nil
}

func ListReleaseSlardar(ctx context.Context, req *EpArtifactManagerSlardar.ListReleaseSlardarReq) (*EpArtifactManagerSlardar.ListReleaseSlardarResp, *service.ServiceError) {
	m := container.GetContainer().Mysql

	bitsReleaseSlardarList, err := m.SlardarRepo.Read.GetBitsReleaseSlardarByRelease(ctx, req.ReleaseType, req.ReleaseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &EpArtifactManagerSlardar.ListReleaseSlardarResp{}, nil
		}
		logs.CtxError(ctx, "GetBitsReleaseSlardarByRelease error. err: %v", err)
		return &EpArtifactManagerSlardar.ListReleaseSlardarResp{}, service.NewServerError(err)
	}

	releaseSlardarDTOList := make([]*EpArtifactManagerSlardar.ReleaseSlardarDTO, 0)
	for _, bitsReleaseSlardar := range bitsReleaseSlardarList {
		releaseSlardarDTOList = append(releaseSlardarDTOList, adaptor.AdaptToReleaseSlardarDTO(bitsReleaseSlardar))
	}

	return &EpArtifactManagerSlardar.ListReleaseSlardarResp{
		SlardarList: releaseSlardarDTOList,
		BaseResp:    nil,
	}, nil
}

func ListSlardarTemplate(ctx context.Context, req *EpArtifactManagerSlardar.ListSlardarTemplateReq) (*EpArtifactManagerSlardar.ListSlardarTemplateResp, *service.ServiceError) {
	slardarTemplateDTOList := make([]*EpArtifactManagerSlardar.SlardarTemplateDTO, 0)
	m := container.GetContainer().Mysql

	templates, err := m.BitsFuseTemplateRepo.ListFuseTemplate(ctx, req.BitsAppID)
	if err != nil {
		logs.CtxError(ctx, "[ListSlardarTemplate] ListFuseTemplate error: %+v", err)
		return &EpArtifactManagerSlardar.ListSlardarTemplateResp{}, service.NewServerError(err)
	}
	for i := range templates {
		slardarTemplateDTOList = append(slardarTemplateDTOList, adaptor.AdaptFuseTemplateToSlardarTemplateDTO(templates[i]))
	}

	templateUrl := tool.GetSlardarTemplateUrl(ctx, req.BitsAppID)
	bitsSlardarTemplateList, err := m.SlardarRepo.Read.ListTemplateByVersionPhase(ctx, req.BitsAppID, req.VersionPhase)
	if err != nil && err != gorm.ErrRecordNotFound {
		logs.CtxError(ctx, "ListBitsSlardarTemplate error. bitsAppId: %d, err: %v", req.BitsAppID, err)
		return &EpArtifactManagerSlardar.ListSlardarTemplateResp{}, service.NewServerError(err)
	}

	for _, bitsSlardarTemplate := range bitsSlardarTemplateList {
		slardarTemplateDTOList = append(slardarTemplateDTOList, adaptor.AdaptToSlardarTemplateDTO(bitsSlardarTemplate))
	}

	return &EpArtifactManagerSlardar.ListSlardarTemplateResp{
		SlardarTemplateURL:  templateUrl,
		SlardarTemplateList: slardarTemplateDTOList,
		BaseResp:            nil,
	}, nil
}

func SearchReleaseFuse(ctx context.Context, req *EpArtifactManagerSlardar.SearchReleaseFuseReq) (*EpArtifactManagerSlardar.SearchReleaseFuseResp, *service.ServiceError) {
	m := container.GetContainer().Mysql

	templates, err := getTemplateList(ctx, req.BitsAppID, req.VersionPhase)
	if err != nil && err != gorm.ErrRecordNotFound {
		logs.CtxError(ctx, "getTemplateList error. req: %v, err: %v", req, err)
		if err == gorm.ErrRecordNotFound {
			return &EpArtifactManagerSlardar.SearchReleaseFuseResp{
				TotalCount: 0,
				FuseList:   make([]*EpArtifactManagerSlardar.ReleaseFuseDTO, 0),
			}, nil
		}
		return &EpArtifactManagerSlardar.SearchReleaseFuseResp{}, service.NewServerError(err)
	}

	var templateIds []int64
	for _, template := range templates {
		templateIds = append(templateIds, int64(template.SlardarTemplateId))
	}
	fuzzySearch := fmt.Sprint("%", req.FuzzySearch, "%")
	source := []int{1}
	templateIds = append(templateIds, 1)
	if req.IsSetSource() {
		if req.GetSource() == EpArtifactManagerSlardar.FuseSource_Slardar {
			source = []int{}
		} else if req.GetSource() == EpArtifactManagerSlardar.FuseSource_Skynet {
			source = []int{1}
			templateIds = []int64{}
		}
	}
	total, err := m.SlardarRepo.Read.FuzzyCountBitsFuseRecord(ctx, req.BitsAppID, templateIds, fuzzySearch, fuzzySearch, source)
	if err != nil {
		logs.CtxError(ctx, "FuzzyCountBitsFuseRecord error. err: %v", err)
		return &EpArtifactManagerSlardar.SearchReleaseFuseResp{}, service.NewServerError(err)
	}

	bitsFuseRecordList, err := m.CustomRepo.SearchFuseRecords(ctx, req.BitsAppID, templateIds, fuzzySearch, fuzzySearch,
		source, req.Field, req.Direcation, (req.PageNo-1)*req.PageSize, req.PageSize)
	if err != nil {
		logs.CtxError(ctx, "FuzzySearchBitsFuseRecord error. err: %v", err)
		if err == gorm.ErrRecordNotFound {
			return &EpArtifactManagerSlardar.SearchReleaseFuseResp{
				TotalCount: total,
				FuseList:   make([]*EpArtifactManagerSlardar.ReleaseFuseDTO, 0),
			}, nil
		}
		return &EpArtifactManagerSlardar.SearchReleaseFuseResp{}, service.NewServerError(err)
	}

	releaseFuseDTOList := make([]*EpArtifactManagerSlardar.ReleaseFuseDTO, 0)
	for _, bitsFuseRecord := range bitsFuseRecordList {
		releaseFuseDTOList = append(releaseFuseDTOList, adaptor.AdaptToReleaseFuseDTO(bitsFuseRecord))
	}

	return &EpArtifactManagerSlardar.SearchReleaseFuseResp{
		TotalCount: total,
		FuseList:   releaseFuseDTOList,
		BaseResp:   nil,
	}, nil
}

func SlardarCallback(ctx context.Context, req *EpArtifactManagerSlardar.SlardarCallbackReq) (*EpArtifactManagerSlardar.SlardarCallbackResp, *service.ServiceError) {
	m := container.GetContainer().Mysql
	var bitsReleaseSlardar *MysqlSlardar.BitsReleaseSlardar
	slardarId, slardarRegion, fuseReason := tool.ParseFromSlardarCallback(req.SlardarCallback)
	bitsReleaseSlardarList, err := m.SlardarRepo.Read.ListBitsReleaseSlardarBySlardarId(ctx, slardarId)
	if err != nil {
		logs.CtxError(ctx, "ListBitsReleaseSlardarBySlardarId error. slardarId: %d, err: %v", slardarId, err)
		return &EpArtifactManagerSlardar.SlardarCallbackResp{}, service.NewServerError(err)
	}
	bitsReleaseSlardar, err = getTargetReleaseSlardar(ctx, slardarRegion, bitsReleaseSlardarList)
	if err != nil {
		logs.CtxError(ctx, "have no mapping slardar. slardarId: %d, err: %v", slardarId, err)
		return &EpArtifactManagerSlardar.SlardarCallbackResp{}, service.NewServerError(err)
	}

	if bitsReleaseSlardar.FuseType == int8(EpArtifactManagerSlardar.FuseType_AUTO) {
		if err := slardarCallbackAuto(ctx, bitsReleaseSlardar, fuseReason); err != nil {
			logs.CtxError(ctx, "slardarCallbackAuto error. err: %v", err)
			return &EpArtifactManagerSlardar.SlardarCallbackResp{}, service.NewServerError(err)
		}
	} else {
		if err := slardarCallbackManual(ctx, bitsReleaseSlardar, fuseReason); err != nil {
			logs.CtxError(ctx, "slardarCallbackManual error. err: %v", err)
			return &EpArtifactManagerSlardar.SlardarCallbackResp{}, service.NewServerError(err)
		}
	}

	return &EpArtifactManagerSlardar.SlardarCallbackResp{}, nil
}

func SwitchSlardarTemplateFuseType(ctx context.Context, req *EpArtifactManagerSlardar.SwitchSlardarTemplateFuseTypeReq) (*EpArtifactManagerSlardar.SwitchSlardarTemplateFuseTypeResp, *service.ServiceError) {
	m := container.GetContainer().Mysql

	if req.GetFuseSource() == EpArtifactManagerSlardar.FuseSource_Slardar {
		if _, err := m.SlardarRepo.Write.SwitchBitsSlardarTemplateFuseType(ctx, int8(req.FuseType), req.BitsSlardarTemplateID); err != nil {
			logs.CtxError(ctx, "UpdateBitsSlardarTemplate error. err: %v", err)
			return &EpArtifactManagerSlardar.SwitchSlardarTemplateFuseTypeResp{}, service.NewServerError(err)
		}
		return &EpArtifactManagerSlardar.SwitchSlardarTemplateFuseTypeResp{}, nil
	}

	if err := m.BitsFuseTemplateRepo.UpdateFuseType(ctx, req.BitsSlardarTemplateID, int64(req.FuseType)); err != nil {
		logs.CtxError(ctx, "[SwitchSlardarTemplateFuseType] switch fuse template types error: %+v", err)
		return &EpArtifactManagerSlardar.SwitchSlardarTemplateFuseTypeResp{}, service.NewServerError(err)
	}

	return &EpArtifactManagerSlardar.SwitchSlardarTemplateFuseTypeResp{}, nil
}

func syncSkyNetTemplate(ctx context.Context, req *EpArtifactManagerSlardar.SyncSlardarTemplateReq) error {
	templates, err := container.GetContainer().Mysql.BitsFuseTemplateRepo.ListFuseTemplateBySource(ctx, req.BitsAppID, int64(EpArtifactManagerSlardar.FuseSource_Skynet))
	if err != nil {
		return errors2.Wrapf(err, "[syncSkyNetTemplate] ListFuseTemplateBySource")
	}
	resp, err := skynet.GetAlertTemplate(ctx, req.CloudAppID)
	if err != nil {
		return errors2.WithMessagef(err, "[syncSkyNetTemplate] GetAlertTemplate error")
	}
	var (
		skynetTemplateIdMap = map[int]*domain.BitsFuseTemplate{}
		onlineTemplates     = resp.Data.Data
		c                   = container.GetContainer()
		deletedIDs          = []int64{}
	)
	for i := range templates {
		if _, ok := skynetTemplateIdMap[templates[i].TemplateId]; ok {
			deletedIDs = append(deletedIDs, templates[i].Id)
			continue
		}
		skynetTemplateIdMap[templates[i].TemplateId] = templates[i]
	}
	for _, t := range onlineTemplates {
		tmp, ok := skynetTemplateIdMap[t.Id]
		if !ok {
			err := c.Mysql.BitsFuseTemplateRepo.Create(ctx, &domain.BitsFuseTemplate{
				FuseType:       0,
				BitsAppId:      int(req.BitsAppID),
				CloudAppId:     int(req.CloudAppID),
				TemplateId:     t.Id,
				Platform:       req.Platform.String(),
				TemplateName:   t.ModuleName,
				CrashTypeLabel: "User feedback",
				Creator:        utils.ParseBitsAPIBffUserEmail(ctx),
				Source:         int(EpArtifactManagerSlardar.FuseSource_Skynet),
			})
			if err != nil {
				return errors2.Wrapf(err, "[syncSkyNetTemplate] create skynet fuse template error")
			}
			continue
		}
		if t.ModuleName != tmp.TemplateName {
			tmp.TemplateName = t.ModuleName
			err := c.Mysql.BitsFuseTemplateRepo.Updates(ctx, tmp)
			if err != nil {
				return errors2.Wrapf(err, "[syncSkyNetTemplate] update skynet template error")
			}
		}
		delete(skynetTemplateIdMap, t.Id)
	}
	for _, v := range skynetTemplateIdMap {
		deletedIDs = append(deletedIDs, v.Id)
	}
	if len(deletedIDs) > 0 {
		err := c.Mysql.BitsFuseTemplateRepo.BatchDelete(ctx, deletedIDs)
		if err != nil {
			return errors2.Wrapf(err, "[syncSkyNetTemplate] BatchDelete error")
		}
	}
	return nil
}

func SyncSlardarTemplate(ctx context.Context, req *EpArtifactManagerSlardar.SyncSlardarTemplateReq) (*EpArtifactManagerSlardar.SyncSlardarTemplateResp, *service.ServiceError) {

	err := syncSkyNetTemplate(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[SyncSlardarTemplate] syncSkyNetTemplate error: %+v", err)
		return &EpArtifactManagerSlardar.SyncSlardarTemplateResp{}, service.NewServerError(err)
	}
	bitsSlardarTemplateList, err := getTemplateList(ctx, req.BitsAppID, req.VersionPhase)
	if err != nil && err != gorm.ErrRecordNotFound {
		logs.CtxError(ctx, "getTemplateList error. bitsAppId: %d, verisonPhase: %s, err: %v", req.BitsAppID, req.VersionPhase, err)
		return &EpArtifactManagerSlardar.SyncSlardarTemplateResp{}, service.NewServerError(err)
	}
	slardarTemplateIdMap := make(map[int]*MysqlSlardar.BitsSlardarTemplate)
	for _, bitsSlardarTemplate := range bitsSlardarTemplateList {
		slardarTemplateIdMap[bitsSlardarTemplate.SlardarTemplateId] = bitsSlardarTemplate
	}

	slardarTemplateList, err := tool.ListSlardarTemplate(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "ListSlardarTemplate error. err: %v", err)
		return &EpArtifactManagerSlardar.SyncSlardarTemplateResp{}, service.NewServerError(err)
	}

	if err := container.GetContainer().ExecuteTransaction(func(c *container.Container) error {

		// slardar平台若有新增模板则插入
		templateIdSetFromSlardar := set.NewIntSet()
		insertList := make([]*MysqlSlardar.BitsSlardarTemplate, 0)
		for _, slardarTemplate := range slardarTemplateList {
			templateIdSetFromSlardar.Add(cast.ToInt(slardarTemplate.TemplateId))
			bitsSlardarTemplate, ok := slardarTemplateIdMap[cast.ToInt(slardarTemplate.TemplateId)]
			if ok {
				if slardarTemplate.TemplateName != bitsSlardarTemplate.SlardarTemplateName ||
					slardarTemplate.VersionPhase != bitsSlardarTemplate.VersionPhase ||
					slardarTemplate.CrashTypeLabel != bitsSlardarTemplate.CrashTypeLabel ||
					int(slardarTemplate.AutoDeleteInternal) != bitsSlardarTemplate.AutoDeleteInterval {

					if _, err := c.Mysql.SlardarRepo.Write.UpdateBitsSlardarTemplateBySlardarTemplate(ctx,
						slardarTemplate.TemplateName, slardarTemplate.VersionPhase, slardarTemplate.CrashTypeLabel,
						int(slardarTemplate.AutoDeleteInternal), bitsSlardarTemplate.Id); err != nil {
						logs.CtxError(ctx, "UpdateBitsSlardarTemplateBySlardarTemplate error. bitsSlardarTemplate.Id=%v, err: %v", bitsSlardarTemplate.Id, err)
						return err
					}
				}
			} else {
				insertList = append(insertList, &MysqlSlardar.BitsSlardarTemplate{
					FuseType:            int8(EpArtifactManagerSlardar.FuseType_AUTO),
					BitsAppId:           int(req.BitsAppID),
					CloudAppId:          int(req.CloudAppID),
					SlardarTemplateId:   cast.ToInt(slardarTemplate.TemplateId),
					CreatedAt:           time.Now(),
					UpdatedAt:           time.Now(),
					Platform:            req.Platform.String(),
					SlardarRegion:       tool.GetSlardarRegion(req.Region),
					SlardarTemplateName: slardarTemplate.TemplateName,
					CrashTypeLabel:      slardarTemplate.CrashTypeLabel,
					Creator:             utils.ParseBitsAPIBffUserEmail(ctx),
					AutoDeleteInterval:  int(slardarTemplate.AutoDeleteInternal),
					VersionPhase:        slardarTemplate.VersionPhase,
				})
			}
		}
		if len(insertList) != 0 {
			if _, err := c.Mysql.SlardarRepo.Write.BatchInsertBitsSlardarTemplate(ctx, insertList); err != nil {
				logs.CtxError(ctx, "BatchBitsSlardarTemplate error. err: %v", err)
				return err
			}
		}

		// 若是已有模版不存在于slardar平台则删除
		deleteIdList := make([]int, 0)
		for _, bitsSlardarTemplate := range bitsSlardarTemplateList {
			if !templateIdSetFromSlardar.Contains(bitsSlardarTemplate.SlardarTemplateId) {
				deleteIdList = append(deleteIdList, bitsSlardarTemplate.Id)
			}
		}
		if len(deleteIdList) != 0 {
			if _, err := c.Mysql.SlardarRepo.Write.DelBitsSlardarTemplate(ctx, deleteIdList); err != nil {
				logs.CtxError(ctx, "DelBitsSlardarTemplate error. err: %v", err)
				return err
			}
		}

		return nil
	}); err != nil {
		logs.CtxError(ctx, "SyncSlardarTemplate error. err: %v", err)
		return &EpArtifactManagerSlardar.SyncSlardarTemplateResp{}, service.NewServerError(err)
	}

	return &EpArtifactManagerSlardar.SyncSlardarTemplateResp{}, nil
}

func DeleteSlardarRule(ctx context.Context, req *EpArtifactManagerSlardar.DeleteSlardarRuleReq) (*EpArtifactManagerSlardar.DeleteSlardarRuleResp, *service.ServiceError) {
	m := container.GetContainer().Mysql

	if err := tool.DeleteSlardarRule(ctx, req); err != nil {
		logs.CtxError(ctx, "tool.DeleteSlardarRule error. err: %v", err)
		return &EpArtifactManagerSlardar.DeleteSlardarRuleResp{}, service.NewServerError(err)
	}

	releaseSlardarList, err := m.SlardarRepo.Read.GetBitsReleaseSlardarByIntegrationId(ctx, req.IntegrationID)
	if err != nil {
		logs.CtxError(ctx, "SlardarRepo.Read.GetBitsReleaseSlardarBySlardarId error. IntegrationID: %d, err: %v", req.IntegrationID, err)
		if err == gorm.ErrRecordNotFound {
			return &EpArtifactManagerSlardar.DeleteSlardarRuleResp{}, nil
		}
		return &EpArtifactManagerSlardar.DeleteSlardarRuleResp{}, service.NewServerError(err)
	}

	for _, releaseSlardar := range releaseSlardarList {
		if releaseSlardar.IsDelete == 1 {
			continue
		}

		releaseSlardar.IsDelete = 1
		if _, err := m.SlardarRepo.Write.UpdateBitsReleaseSlardar(ctx, releaseSlardar); err != nil {
			logs.CtxError(ctx, "SlardarRepo.Write.UpdateBitsReleaseSlardar. releaseSlardar: %v, err: %v", releaseSlardar, err)
			continue
		}
	}

	return &EpArtifactManagerSlardar.DeleteSlardarRuleResp{}, nil
}

func FuseRelease(ctx context.Context, releaseType int, releaseId int) error {
	if releaseType == int(EpArtifactManagerSlardar.ReleaseType_Normal) ||
		releaseType == int(EpArtifactManagerSlardar.ReleaseType_GooglePlay) {
		if _, err := release.SuspendReleaseRule(ctx, &releaserule.SuspendReleaseRuleReq{
			ReleaseRuleID: int64(releaseId),
		}); err != nil {
			logs.CtxError(ctx, "SuspendReleaseRule error. releaseId: %d, err: %v", releaseId, err)
			return err
		}
	}

	if releaseType == int(EpArtifactManagerSlardar.ReleaseType_MuteGrey) {
		err := suspendMuteGreyRule(ctx, releaseId)
		if err != nil {
			logs.CtxError(ctx, "suspendMuteGreyRule error. releaseId: %d, err: %v", releaseId, err)
			return fmt.Errorf("suspendMuteGreyRule error. releaseId: %d, err: %v", releaseId, err)
		}

	}

	return nil
}

func suspendMuteGreyRule(ctx context.Context, atomExecutionId int) error {
	// 下线插件规则
	atomExecution, err := container.GetContainer().Mysql.AtomExecutionRepo.FindByID(int64(atomExecutionId))
	if err != nil {
		logs.CtxError(ctx, "FindByID error. releaseId: %d, err: %v", atomExecutionId, err)
		return fmt.Errorf("FindByID error. releaseId: %d, err: %v", atomExecutionId, err)
	}
	pluginConfigRule := struct {
		PluginConfigRuleID int64 `json:"plugin_config_rule_id"`
	}{}
	err = json.Unmarshal([]byte(atomExecution.Output), &pluginConfigRule)
	if err != nil {
		logs.CtxError(ctx, "Unmarshal error. releaseId: %d, err: %v", atomExecutionId, err)
		return fmt.Errorf("Unmarshal error. releaseId: %d, err: %v", atomExecutionId, err)
	}
	if pluginConfigRule.PluginConfigRuleID == 0 {
		logs.CtxError(ctx, "pluginConfigRule.PluginConfigRuleID is 0. releaseId: %d, err: %v", atomExecutionId, err)
		return fmt.Errorf("pluginConfigRule.PluginConfigRuleID is 0. releaseId: %d, err: %v", atomExecutionId, err)
	}

	logs.CtxInfo(ctx, "[suspendMuteGreyRule]plugin rule id: %v", pluginConfigRule.PluginConfigRuleID)
	ruleInfo, err := container.GetContainer().PluginMysql.PluginConfigRule.R.GetPluginConfigRule(ctx, pluginConfigRule.PluginConfigRuleID)
	if err != nil {
		logs.CtxError(ctx, "[suspendMuteGreyRule]can not get rule info, rule id: %v, err: %v", pluginConfigRule.PluginConfigRuleID, err)
		return fmt.Errorf("[suspendMuteGreyRule]can not get rule info, rule id: %v, err: %v", pluginConfigRule.PluginConfigRuleID, err)
	}

	// 头条需求: 静默灰度熔断后插件规则关闭，不下线
	newStatus := int(plugin_config_rule.PluginConfigRuleStatus_OFFLINE)
	toutiaoAppIDs := []int{6589, 315828, 13, 35}
	for _, toutiaoAppID := range toutiaoAppIDs {
		if toutiaoAppID == ruleInfo.AppId {
			newStatus = int(plugin_config_rule.PluginConfigRuleStatus_CLOSED)
			break
		}
	}
	err = container.GetContainer().PluginMysql.PluginConfigRule.W.UpdatePluginConfigRule(ctx, &plugin_config_rule.PluginConfigRule{
		Id:         int(pluginConfigRule.PluginConfigRuleID),
		Status:     newStatus,
		ModifyTime: time.Now(),
	})
	if err != nil {
		logs.CtxError(ctx, "UpdatePluginConfigRule error. releaseId: %d, err: %v", atomExecutionId, err)
		return fmt.Errorf("UpdatePluginConfigRule error. releaseId: %d, err: %v", atomExecutionId, err)
	}

	return nil
}

func getTemplateList(ctx context.Context, bitsAppId int64, versionPhase string) ([]*MysqlSlardar.BitsSlardarTemplate, error) {
	m := container.GetContainer().Mysql

	if len(versionPhase) != 0 {
		return m.SlardarRepo.Read.ListTemplateByVersionPhase(ctx, bitsAppId, versionPhase)
	}
	return m.SlardarRepo.Read.ListBitsSlardarTemplate(ctx, bitsAppId)
}

func slardarCallbackAuto(ctx context.Context, bitsReleaseSlardar *MysqlSlardar.BitsReleaseSlardar, fuseReason string) error {
	m := container.GetContainer().Mysql
	if err := FuseRelease(ctx, bitsReleaseSlardar.ReleaseType, bitsReleaseSlardar.ReleaseId); err != nil {
		logs.CtxError(ctx, "FuseRelease error. releaseId: %d, err: %v", bitsReleaseSlardar.ReleaseId, err)
		if bitsReleaseSlardar.ReleaseType == int(EpArtifactManagerSlardar.ReleaseType_Normal) ||
			bitsReleaseSlardar.ReleaseType == int(EpArtifactManagerSlardar.ReleaseType_GooglePlay) {
			releaseUrl := utils.GetBitsHostWithScheme()
			releaseRule, err := m.ReleaseRuleRepo.Find(uint(bitsReleaseSlardar.ReleaseId))
			if err != nil {
				logs.CtxError(ctx, "ReleaseRuleRepo.Find error. releaseId: %d, err: %v", bitsReleaseSlardar.ReleaseId, err)
			} else {
				releaseUrl = tool.GetReleaseUrl(bitsReleaseSlardar.ReleaseType, releaseRule)
			}

			// 若是发布单仍然为运行中说明熔断失败，发送熔断失败消息
			if releaseRule.Status == releaserule.ReleaseRuleStatus_EFFECTED.String() {
				if _, err := rpc.Integration.FuseLarkConfirmNotify(ctx, &BitsIntegration.FuseLarkConfirmNotifyRequest{
					IntegrationID:  cast.ToInt64(bitsReleaseSlardar.IntegrationId),
					BitsReleaseURL: releaseUrl,
					FuseName:       bitsReleaseSlardar.SlardarName,
					FuseType:       cast.ToInt64(bitsReleaseSlardar.FuseType),
					AlarmTime:      time.Now().Unix(),
					Operator:       "slardar自动熔断",
					IsSuc:          false,
				}); err != nil {
					logs.CtxError(ctx, "FuseLarkConfirmNotify error. err: %v", err)
				}
			}
		}
		return err
	}

	go func() {
		if err := insertFuseRecord(ctx, bitsReleaseSlardar, bitsReleaseSlardar.SlardarName, fuseReason, ""); err != nil {
			logs.CtxError(ctx, "insertFuseRecord error. err: %v", err)
		}
	}()

	if err := tool.SendFuseLark(ctx, bitsReleaseSlardar, fuseReason); err != nil {
		logs.CtxError(ctx, "SendFuseLark error. err: %v", err)
		return err
	}

	return nil
}

func slardarCallbackManual(ctx context.Context, bitsReleaseSlardar *MysqlSlardar.BitsReleaseSlardar, fuseReason string) error {
	m := container.GetContainer().Mysql
	if bitsReleaseSlardar.ReleaseType == int(EpArtifactManagerSlardar.ReleaseType_Normal) ||
		bitsReleaseSlardar.ReleaseType == int(EpArtifactManagerSlardar.ReleaseType_GooglePlay) {
		releaseRule, err := m.ReleaseRuleRepo.Find(uint(bitsReleaseSlardar.ReleaseId))
		if err != nil {
			logs.CtxError(ctx, "ReleaseRuleRepo.Find error. releaseId: %d, err: %v", bitsReleaseSlardar.ReleaseId, err)
			return err
		}

		if releaseRule.Status != releaserule.ReleaseRuleStatus_EFFECTED.String() {
			logs.CtxError(ctx, "release status is not effected. no need to send fuse lark. releaseId: %d, releaseStatus: %s", bitsReleaseSlardar.ReleaseId, releaseRule.Status)
			return errors.New("no need to send manual fuse lark")
		}
	}

	if err := tool.SendFuseLark(ctx, bitsReleaseSlardar, fuseReason); err != nil {
		logs.CtxError(ctx, "SendFuseLark error. err: %v", err)
		return err
	}

	return nil
}

func insertFuseRecord(ctx context.Context, bitsReleaseSlardar *MysqlSlardar.BitsReleaseSlardar, fuseName, fuseReason, operator string) error {
	c := container.GetContainer()
	integration, err := c.BitsMysql.BitsRepo.Read.GetIntegration(ctx, int64(bitsReleaseSlardar.IntegrationId))
	if err != nil {
		logs.CtxError(ctx, "BitsRepo.Read.GetIntegration error. integrationId: %d, err: %v", bitsReleaseSlardar.IntegrationId, err)
		return err
	}

	appInfo, err := c.BitsMysql.BitsRepo.Read.GetAppInfo(ctx, integration.UniqueId)
	if err != nil {
		logs.CtxError(ctx, "BitsRepo.Read.GetAppInfo error. bitsAppId: %d, err: %v", integration.UniqueId, err)
		return err
	}

	artifact, err := getArtifact(bitsReleaseSlardar)
	if err != nil {
		logs.CtxError(ctx, "getArtifact error. slardarTemplateId: %d, err: %v", bitsReleaseSlardar.SlardarTemplateId, err)
		return err
	}

	fuseRecord := &MysqlSlardar.BitsFuseRecord{
		FuseType:              bitsReleaseSlardar.FuseType,
		BitsAppId:             int(appInfo.Id),
		CloudAppId:            int(appInfo.AppCloudId),
		BitsReleaseSlardarId:  bitsReleaseSlardar.Id,
		BitsSlardarTemplateId: bitsReleaseSlardar.SlardarTemplateId,
		ReleaseId:             bitsReleaseSlardar.ReleaseId,
		CreatedAt:             time.Now(),
		Platform:              strings.ToUpper(appInfo.TechnologyStack),
		UpdateVersion:         artifact.UpdateVersion,
		VersionName:           artifact.VersionName,
		FuseName:              fuseName,
		FuseReason:            fuseReason,
		Creator:               operator,
	}
	if _, err := c.Mysql.SlardarRepo.Write.InsertBitsFuseRecord(ctx, fuseRecord); err != nil {
		logs.CtxError(ctx, "InsertBitsFuseRecord error. fuseRecord: %+v, err: %v", fuseRecord, err)
		return err
	}

	return nil
}

func getArtifact(bitsReleaseSlardar *MysqlSlardar.BitsReleaseSlardar) (*domain.Artifact, error) {
	m := container.GetContainer().Mysql

	if bitsReleaseSlardar.ReleaseType == int(EpArtifactManagerSlardar.ReleaseType_GooglePlay) ||
		bitsReleaseSlardar.ReleaseType == int(EpArtifactManagerSlardar.ReleaseType_Normal) {
		releaseRule, err := m.ReleaseRuleRepo.Find(uint(bitsReleaseSlardar.ReleaseId))
		if err != nil {
			logs.Error("ReleaseRuleRepo.Find error. releaseId: %d, err: %v", bitsReleaseSlardar.ReleaseId, err)
			return &domain.Artifact{}, err
		}

		return m.ArtifactRepo.Find(releaseRule.ArtifactId)
	}

	return &domain.Artifact{}, errors.New("unsupport ")
}

func getSlardarRegion(bitsAppRegion string) string {
	if bitsAppRegion == strings.ToLower(EpArtifactManagerSlardar.Region_CN.String()) {
		return tool.GetSlardarRegion(int64(EpArtifactManagerSlardar.Region_CN))
	}

	return tool.GetSlardarRegion(int64(EpArtifactManagerSlardar.Region_MALIVA))
}

/**
** 避免国内国外slardar id重复
 */
func getTargetReleaseSlardar(ctx context.Context, slardarRegion string, bitsReleaseSlardarList []*MysqlSlardar.BitsReleaseSlardar) (*MysqlSlardar.BitsReleaseSlardar, error) {
	m := container.GetContainer().BitsMysql

	for _, releaseSlardar := range bitsReleaseSlardarList {
		integration, err := m.BitsRepo.Read.GetIntegration(ctx, int64(releaseSlardar.IntegrationId))
		if err != nil {
			logs.CtxError(ctx, "BitsRepo.Read.GetIntegration error. integrationId: %d, err: %v", releaseSlardar.IntegrationId, err)
			return &MysqlSlardar.BitsReleaseSlardar{}, err
		}

		appInfo, err := m.BitsRepo.Read.GetAppInfo(ctx, integration.UniqueId)
		if err != nil {
			logs.CtxError(ctx, "BitsRepo.Read.GetAppInfo error. bitsAppId: %d, err: %v", integration.UniqueId, err)
			return &MysqlSlardar.BitsReleaseSlardar{}, err
		}

		if getSlardarRegion(appInfo.Region) == slardarRegion {
			return releaseSlardar, nil
		}
	}
	return &MysqlSlardar.BitsReleaseSlardar{}, errors.New("have no target release slardar")
}

func AddFuseRule(ctx context.Context, releaseRuleId int64) error {
	c := container.GetContainer()
	releaseRule, err := c.Mysql.ReleaseRuleRepo.Find(uint(releaseRuleId))
	if err != nil {
		return err
	}
	if releaseRule.BitsAppId == 0 {
		return errors.New("bitsAppId == 0")
	}
	bitsSlardarTemplateList, err := c.Mysql.SlardarRepo.Read.ListBitsSlardarTemplate(ctx, int64(releaseRule.BitsAppId))
	if err != nil {
		logs.CtxError(ctx, "ListBitsSlardarTemplate error. bitsAppId: %d, err: %v", releaseRule.BitsAppId, err)
		if err == gorm.ErrRecordNotFound {
			return nil
		}
		return err
	}

	slardarTemplateDTOList := make([]*EpArtifactManagerSlardar.SlardarTemplateDTO, 0)
	for _, bitsSlardarTemplate := range bitsSlardarTemplateList {
		slardarTemplateDTOList = append(slardarTemplateDTOList, adaptor.AdaptToSlardarTemplateDTO(bitsSlardarTemplate))
	}
	if releaseRule.Id > 0 {
		artifact, err := c.Mysql.ArtifactRepo.Find(releaseRule.ArtifactId)
		if err != nil {
			return err
		}
		exists, err := tool.ExistsSlardarRule(ctx, c, releaseRuleId, EpArtifactManagerSlardar.ReleaseType_Normal)
		if err != nil {
			return err
		}
		if len(releaseRule.ReleaseRate) == 0 && releaseRule.ReleaseEnv != "OFFICIAL" && !exists && len(bitsSlardarTemplateList) > 0 {
			var slardarCfgList []*releaserule.SlardarCfg
			for _, template := range slardarTemplateDTOList {
				slardarCfgList = append(slardarCfgList, &releaserule.SlardarCfg{
					RuleID:   template.SlardarTemplateID,
					RuleName: template.SlardarTemplateName,
				})
			}
			logs.Infof("slardarCfgList %+v", util.ToJsonSimple(slardarCfgList))
			err = tool.CreateSlardarRule(ctx, c, tool.CreateSlardarRuleReq{
				SlardarCfgList: slardarCfgList,
				Artifact:       artifact,
				BitsAppId:      int64(releaseRule.BitsAppId),
				Region:         int64(EpArtifactManagerSlardar.Region_CN),
				IntegrationId:  int64(releaseRule.IntegrationId),
				ReleaseType:    EpArtifactManagerSlardar.ReleaseType_Normal,
				ReleaseRuleId:  int64(releaseRule.Id),
				VersionPhase:   util.SlardarVersionPhaseGray,
				UserEmail:      releaseRule.Creator,
			})
			if err != nil {
				logs.CtxError(ctx, "error %+v", err)
				return err
			}
			logs.Infof("slardarCfgList %+v successfully", util.ToJsonSimple(slardarCfgList))
			return nil
		} else {
			logs.CtxInfo(ctx, "rule(%d) do not need create fuse rule", releaseRuleId)
			return nil
		}
	}
	return nil
}

func AutoAddFuseRule(ctx context.Context, req *EpArtifactManagerSlardar.AutoAddFuseRuleReq) (r *EpArtifactManagerSlardar.AutoAddFuseRuleResp, err error) {
	for _, ruleId := range req.RelaseRuleIds {
		err := AddFuseRule(ctx, ruleId)
		if err != nil {
			return EpArtifactManagerSlardar.NewAutoAddFuseRuleResp(), err
		}
	}
	return EpArtifactManagerSlardar.NewAutoAddFuseRuleResp(), nil
}
