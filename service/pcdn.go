package service

import (
	"bytes"
	"code.byted.org/bits/release_hotfix/utils"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	volc "code.byted.org/volcengine/volc-signer-golang"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

type PcdnEnvConf struct {
	Service string `json:"service"`
	Ak      string `json:"ak"`
	Sk      string `json:"sk"`
}
type PcdnConf struct {
	PpeConf         PcdnEnvConf     `json:"ppe_conf"`
	OnlineConf      PcdnEnvConf     `json:"online_conf"`
	AppMapToService map[int64]int64 `json:"app_map_to_service"`
	WarmUpConf      WarmUpConf      `json:"warm_up_conf"`
}

func (p PcdnConf) getEnvConf() PcdnEnvConf {
	if env.IsPPE() {
		return p.PpeConf
	} else if env.IsProduct() {
		return p.OnlineConf
	} else {
		return PcdnEnvConf{}
	}
}

type PcdnFileItem struct {
	FileID      string `json:"FileID"`
	DownloadUrl string `json:"DownloadUrl,omitempty"`
	Qps         int    `json:"Qps,omitempty"`
	Action      string `json:"Action"`
}

type AddDownloadTaskReq struct {
	FileList []*PcdnFileItem `json:"FileList"`
}

type WarmUpConf struct {
	Qps    int                `json:"qps"`
	Plugin map[int64][]string `json:"plugin"`
}

func PcdnAddDownloadTask(ctx context.Context, taskReq *AddDownloadTaskReq, pcdnConf PcdnConf, cloudAppId int64) error {
	envConf := pcdnConf.getEnvConf()
	serviceId := pcdnConf.AppMapToService[cloudAppId]

	bodyBytes, err := json.Marshal(taskReq)
	if err != nil {
		return err
	}

	req, err := http.NewRequest(http.MethodPost,
		fmt.Sprintf("https://open.volcengineapi.com/?Action=AddDownloadTask&Version=2019-01-01&ServiceID=%d&X-Account-Id=111", serviceId),
		bytes.NewBuffer(bodyBytes))
	if err != nil {
		logs.CtxError(ctx, "[PcdnAddDownloadTask] NewRequest(serviceId = %d, bodyBytes = %s) failed, err = %v", serviceId, string(bodyBytes), err)
		return err
	}
	req = volc.Sign(req, volc.Credentials{
		AccessKeyID:     envConf.Ak,
		SecretAccessKey: envConf.Sk,
		Region:          "cn-north-1",
		Service:         envConf.Service,
	})

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logs.CtxError(ctx, "[PcdnAddDownloadTask] do request failed, err = %v, req = %s", err, utils.ToJsonSimple(req))
		return err
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)
	logs.CtxInfo(ctx, "[PcdnAddDownloadTask] resp.Code = %s, resp.body = %s, envConf = %s",
		resp.Status, string(body), utils.ToJsonSimple(envConf))

	return nil
}
