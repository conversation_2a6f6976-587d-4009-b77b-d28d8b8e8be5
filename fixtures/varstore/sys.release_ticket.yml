name:
  cn: 发布单变量组
  en: Variables of Release ticket
description:
  cn: 发布单变量组
  en: Variables of Release ticket
namespace: 1
provider: 8
keyprefix: "sys.release_ticket."
createdby: yuanzhendong

vardefinitions:
  - name: title
    kind: 3
    shortdesc:
      cn: 发布单名称
      en: Name of a release ticket
    longdesc:
      cn: 发布单名称，赋值时机：发布单创建/编辑
      en: Name of a release ticket

  - name: test_approver
    kind: 4
    shortdesc:
      cn: 发布单质量负责人
      en: QA approvers of a release ticket
    longdesc:
      cn: 发布单质量负责人，赋值时机：发布单创建/编辑
      en: QA approvers of a release ticket
    needloadwhenrender: true

  - name: test_approver_str
    kind: 3
    shortdesc:
      cn: 发布单质量负责人
      en: QA approvers of a release ticket
    longdesc:
      cn: 发布单质量负责人，赋值时机：发布单创建/编辑
      en: QA approvers of a release ticket

  - name: release_approver
    kind: 4
    shortdesc:
      cn: 发布负责人
      en: Release approvers of a release ticket
    longdesc:
      cn: 发布负责人，赋值时机：发布单创建/编辑
      en: Release approvers of a release ticket
    needloadwhenrender: true

  - name: developer
    kind: 4
    shortdesc:
      cn: 与发布单关联的开发者
      en: Developers that linked with a release ticket
    longdesc:
      cn: 与发布单关联的开发者，赋值时机：开发任务关联/取消关联
      en: Developers that linked with a release ticket
    needloadwhenrender: true

  - name: members
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 聚合了发布单上的“发布负责人”、“质量负责人”以及所部署项目的“项目负责人”
      en: The aggregation of "Release Approvers", "QA Approvers" of a release ticket, and "Project Owners" of the project to be deployed
    longdesc:
      cn: 聚合了发布单上的“发布负责人”、“质量负责人”以及所部署项目的“项目负责人”
      en: The aggregation of "Release Approvers", "QA Approvers" of a release ticket, and "Project Owners" of the project to be deployed

  - name: control_plane
    kind: 4
    shortdesc:
      cn: 发布控制面
      en: Release control planes (regions)
    longdesc:
      cn: 发布控制面，赋值时机：发布单创建/编辑
      en: Release control planes (regions)

  - name: url
    kind: 3
    shortdesc:
      cn: 发布单链接
      en: URL of a release ticket
    longdesc:
      cn: 发布单链接
      en: URL of a release ticket

  - name: creation_date
    kind: 3
    shortdesc:
      cn: 发布单的创建日期，如2024-01-01
      en: The date when a release ticket is created
    longdesc:
      cn: 发布单的创建日期，如2024-01-01
      en: The date when a release ticket is created, for example, 2024-01-01.

  - name: scheduled_freeze_date
    kind: 3
    shortdesc:
      cn: 发布单创建时填写的计划锁定的日期
      en: The scheduled freeze date that is entered in a new release ticket
    longdesc:
      cn: 发布单创建时填写的计划锁定的日期，例如：2023-02-23
      en: The scheduled freeze date that is entered in a new release ticket, for example, 2023-02-23.

  - name: scheduled_release_date
    kind: 3
    shortdesc:
      cn: 发布单创建时填写的预计发布时间
      en: The last release date of a release ticket
    longdesc:
      cn: 发布单创建时填写的预计发布时间，例如：2023-02-23
      en: The last release date of a release ticket, for example, 2023-02-23.

  - name: feature_ids
    kind: 4
    shortdesc:
      cn: Meego ID（包含需求和缺陷）
      en: IDs of Meego(Includes requirements and issues)
    longdesc:
      cn: Meego ID（包含需求和缺陷）
      en: IDs of Meego(Includes requirements and issues)

  - name: feature_urls
    kind: 4
    shortdesc:
      cn: Meego URL（包含需求和缺陷）
      en: URLs of Meego(Includes requirements and issues)
    longdesc:
      cn: Meego URL（包含需求和缺陷）
      en: URLs of Meego(Includes requirements and issues)
    needloadwhenrender: true

  - name: main_repo_feature_branch
    kind: 3
    shortdesc:
      cn: 上线模版引用为源分支
      en: origin branch of Online Deployment Execution
    longdesc:
      cn: 上线模版引用为源分支
      en: origin branch of Online Deployment Execution

  - name: main_repo_deploy_branch
    kind: 3
    shortdesc:
      cn: 发布单的发布分支
      en: release branch of the release ticket
    longdesc:
      cn: 发布单的发布分支
      en: release branch of the release ticket

  - name: main_repo_integration_branch
    kind: 3
    shortdesc:
      cn: 发布单的集成分支
      en: integration branch of the release ticket
    longdesc:
      cn: 发布单的集成分支
      en: integration branch of the release ticket

  - name: main_repo_archive_branch
    kind: 3
    shortdesc:
      cn: 发布单的归档分支
      en: archive branch of the release ticket
    longdesc:
      cn: 发布单的归档分支
      en: archive branch of the release ticket

  - name: boe_env_name
    kind: 3
    shortdesc:
      cn: BOE CN 功能环境名称
      en: BOE CN feature environment name
    longdesc:
      cn: BOE CN 功能环境名称
      en: BOE CN feature environment name

  - name: boe_cn_env_name
    kind: 3
    shortdesc:
      cn: BOE CN 功能环境名称
      en: BOE CN feature environment name
    longdesc:
      cn: BOE CN 功能环境名称，值同 boe_env_name
      en: BOE CN feature environment name, its value is equal to boe_env_name

  - name: boe_i18n_env_name
    kind: 3
    shortdesc:
      cn: BOE I18N 功能环境名称
      en: BOE I18N feature environment name
    longdesc:
      cn: BOE I18N 功能环境名称，值同 boe_env_name
      en: BOE I18N feature environment name, its value is equal to boe_env_name

  - name: boe_i18n_sg_env_name
    kind: 3
    shortdesc:
      cn: BOE I18N-SG 功能环境名称
      en: BOE I18N-SG feature environment name
    longdesc:
      cn: BOE I18N-SG 功能环境名称
      en: BOE I18N-SG feature environment name

  - name: boe_i18n_va_env_name
    kind: 3
    shortdesc:
      cn: BOE I18N-VA 功能环境名称
      en: BOE I18N-VA feature environment name
    longdesc:
      cn: BOE I18N-VA 功能环境名称
      en: BOE I18N-VA feature environment name

  - name: boe_us_ttp_env_name
    kind: 3
    shortdesc:
      cn: BOE US-TTP 功能环境名称
      en: BOE US-TTP feature environment name
    longdesc:
      cn: BOE US-TTP 功能环境名称
      en: BOE US-TTP feature environment name

  - name: ppe_env_name
    kind: 3
    shortdesc:
      cn: PPE CN 环境标签
      en: PPE CN env name
    longdesc:
      cn: PPE CN 环境标签
      en: PPE CN env name

  - name: ppe_cn_env_name
    kind: 3
    shortdesc:
      cn: PPE CN 功能环境名称
      en: PPE CN feature environment name
    longdesc:
      cn: PPE CN 功能环境名称
      en: PPE CN feature environment name

  - name: ppe_i18n_env_name
    kind: 3
    shortdesc:
      cn: PPE I18N 功能环境名称
      en: PPE I18N feature environment name
    longdesc:
      cn: PPE I18N 功能环境名称
      en: PPE I18N feature environment name

  - name: ppe_us_ttp_name
    kind: 3
    shortdesc:
      cn: PPE US-TTP 功能环境名称
      en: PPE US-TTP feature environment name
    longdesc:
      cn: PPE US-TTP 功能环境名称
      en: PPE US-TTP feature environment name

  - name: ppe_eu_ttp_name
    kind: 3
    shortdesc:
      cn: PPE EU-TTP 功能环境名称
      en: PPE EU-TTP feature environment name
    longdesc:
      cn: PPE EU-TTP 功能环境名称
      en: PPE EU-TTP feature environment name

  - name: ppe_cn_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线上 CN 的 PPE 泳道下的 TCE 集群配置
      en: Online CN's PPE environment cluster configurations
    longdesc:
      cn: 线上 CN 的 PPE 泳道下的 TCE 集群配置
      en: Online CN's PPE environment cluster configurations

  - name: ppe_i18n_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线上 I18N 的 PPE 泳道下的 TCE 集群配置
      en: Online I18N's PPE environment cluster configurations
    longdesc:
      cn: 线上 I18N 的 PPE 泳道下的 TCE 集群配置
      en: Online I18N's PPE environment cluster configurations

  - name: ppe_usttp_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线上 US-TTP 的 PPE 泳道下的 TCE 集群配置
      en: Online US-TTP's PPE environment cluster configurations
    longdesc:
      cn: 线上 US-TTP 的 PPE 泳道下的 TCE 集群配置
      en: Online US-TTP's PPE environment cluster configurations

  - name: ppe_euttp_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线上 EU-TTP 的 PPE 泳道下的 TCE 集群配置
      en: Online EU-TTP's PPE environment cluster configurations
    longdesc:
      cn: 线上 EU-TTP 的 PPE 泳道下的 TCE 集群配置
      en: Online EU-TTP's PPE environment cluster configurations

  - name: boe_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线下 CN&I18N 的泳道下的 TCE 集群配置
      en: BOE CN&I18N's environment cluster configurations
    longdesc:
      cn: 线下 CN&I18N 的泳道下的 TCE 集群配置
      en: BOE CN&I18N's environment cluster configurations

  - name: prod_sg_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线下 I18N-SG 的泳道下的 TCE 集群配置
      en: BOE I18N-SG's environment cluster configurations
    longdesc:
      cn: 线下 I18N-SG 的泳道下的 TCE 集群配置
      en: BOE I18N-SG's environment cluster configurations

  - name: prod_va_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线下 I18N-VA 的泳道下的 TCE 集群配置
      en: BOE I18N-VA's environment cluster configurations
    longdesc:
      cn: 线下 I18N-VA 的泳道下的 TCE 集群配置
      en: BOE I18N-VA's environment cluster configurations

  - name: prod_ttp_env_cluster_configs
    kind: 4
    needloadwhenrender: true
    shortdesc:
      cn: 线下 US-TTP 的泳道下的 TCE 集群配置
      en: BOE US-TTP's environment cluster configurations
    longdesc:
      cn: 线下 US-TTP 的泳道下的 TCE 集群配置
      en: BOE US-TTP's environment cluster configurations

  - name: boe_cluster_names
    kind: 3
    shortdesc:
      cn: boe 集群名称列表
      en: boe cluster name list
    longdesc:
      cn: boe 集群名称列表
      en: boe cluster name list

  - name: ppe_cluster_names
    kind: 3
    shortdesc:
      cn: ppe 集群名称列表
      en: ppe cluster name list
    longdesc:
      cn: ppe 集群名称列表
      en: ppe cluster name list

  - name: boe_clusters
    kind: 4
    shortdesc:
      cn: boe feature 部署的集群配置
      en: cluster settings of boe feature service
    longdesc:
      cn: boe feature 部署的集群配置
      en: cluster settings of boe feature service
    needloadwhenrender: true

  - name: boe_prod_clusters
    kind: 4
    shortdesc:
      cn: boe 基准服务部署的集群配置
      en: cluster settings of boe prod service
    longdesc:
      cn: boe 基准服务部署的集群配置
      en: cluster settings of boe prod service
    needloadwhenrender: true

  - name: ppe_clusters
    kind: 4
    shortdesc:
      cn: ppe 部署的集群配置
      en: cluster settings of ppe feature service
    longdesc:
      cn: ppe 部署的集群配置
      en: cluster settings of ppe feature service
    needloadwhenrender: true

  - name: i18n_ppe_clusters
    kind: 4
    shortdesc:
      cn: i18n ppe 部署的集群配置
      en: cluster settings of i18n-ppe feature service
    longdesc:
      cn: i18n ppe 部署的集群配置
      en: cluster settings of i18n-ppe feature service
    needloadwhenrender: true

  - name: upgrade_cluster_set
    kind: 5
    shortdesc:
      cn: 线上服务部署的集群配置，基于当前控制面
      en: cluster settings of online service, based on current control plane
    longdesc:
      cn: 线上服务部署的集群配置，基于当前控制面
      en: cluster settings of online service, based on current control plane
    needloadwhenrender: true

  - name: i18n_upgrade_cluster_set
    kind: 5
    shortdesc:
      cn: i18n线上服务部署的集群配置
      en: cluster settings of i18n-online service
    longdesc:
      cn: i18n线上服务部署的集群配置
      en: cluster settings of i18n-online service
    needloadwhenrender: true

  - name: cn_upgrade_cluster_set
    kind: 5
    shortdesc:
      cn: cn线上服务部署的集群配置
      en: cluster settings of cn-online service
    longdesc:
      cn: cn线上服务部署的集群配置
      en: cluster settings of cn-online service
    needloadwhenrender: true

  - name: rollout_strategy
    kind: 3
    shortdesc:
      cn: 集群升级模式
      en: upgrade mode
    longdesc:
      cn: 集群升级模式
      en: upgrade mode

  - name: cn_rollout_strategy
    kind: 3
    shortdesc:
      cn: cn 集群升级模式
      en: upgrade mode
    longdesc:
      cn: cn 集群升级模式
      en: upgrade mode

  - name: i18n_rollout_strategy
    kind: 3
    shortdesc:
      cn: i18n 集群升级模式
      en: upgrade mode
    longdesc:
      cn: i18n 集群升级模式
      en: upgrade mode

  - name: surge_config
    kind: 5
    shortdesc:
      cn: 滚动升级配置
      en: rolling settings
    longdesc:
      cn: 滚动升级配置
      en: rolling settings
    needloadwhenrender: true

  - name: cn_surge_config
    kind: 5
    shortdesc:
      cn: cn 滚动升级配置
      en: rolling settings
    longdesc:
      cn: cn 滚动升级配置
      en: rolling settings
    needloadwhenrender: true

  - name: i18n_surge_config
    kind: 5
    shortdesc:
      cn: i18n 滚动升级配置
      en: rolling settings
    longdesc:
      cn: i18n 滚动升级配置
      en: rolling settings
    needloadwhenrender: true

  - name: git_tag_type
    kind: 3
    shortdesc:
      cn: git tag 类型
      en: git tag type
    longdesc:
      cn: git tag 类型
      en: git tag type

  - name: scm_version_tag
    kind: 3
    shortdesc:
      cn: scm tag 类型
      en: scm tag type
    longdesc:
      cn: scm tag 类型
      en: scm tag type

  - name: skip_ttp
    kind: 1
    shortdesc:
      cn: 是否跳过部署ttp
      en: whether to skip ttp deploy
    longdesc:
      cn: 是否跳过部署ttp
      en: whether to skip ttp deploy

  - name: psm
    kind: 3
    shortdesc:
      cn: 项目 psm
      en: PSM of Project
    longdesc:
      cn: 项目 psm
      en: PSM of Project

  - name: main_scm_repo_id
    kind: 2
    shortdesc:
      cn: SCM 主仓库 ID
      en: ID of Main SCM Repo
    longdesc:
      cn: SCM 主仓库 ID
      en: ID of Main SCM Repo

  - name: main_git_repo_id
    kind: 2
    shortdesc:
      cn: Git 主仓库 ID
      en: ID of Main Git Repo
    longdesc:
      cn: Git 主仓库 ID
      en: ID of Main Git Repo

  - name: main_git_repo_name
    kind: 3
    shortdesc:
      cn: Git 主仓库名称
      en: Name of Main Git Repo
    longdesc:
      cn: Git 主仓库名称
      en: Name of Main Git Repo

  - name: main_scm_version
    kind: 3
    shortdesc:
      cn: Scm 主仓库版本
      en: Version of Main SCM Repo
    longdesc:
      cn: Scm 主仓库版本
      en: Version of Main SCM Repo

  - name: dependent_scm_list
    kind: 4
    shortdesc:
      cn: 依赖仓库列表
      en: List of Dependent SCM repo
    longdesc:
      cn: 依赖仓库列表
      en: List of Dependent SCM repo
    needloadwhenrender: true

  - name: env_repo_infos
    kind: 4
    shortdesc:
      cn: ENV 依赖仓库列表
      en: List of ENV Dependent SCM repo
    longdesc:
      cn: ENV 依赖仓库列表
      en: List of ENV Dependent SCM repo
    needloadwhenrender: true

  - name: boe_cluster_ids
    kind: 4
    shortdesc:
      cn: boe feature 部署的集群 ID 列表
      en: cluster IDs of boe feature service
    longdesc:
      cn: boe feature 部署的集群 ID 列表
      en: cluster IDs of boe feature service
    needloadwhenrender: true

  - name: boe_prod_cluster_ids
    kind: 4
    shortdesc:
      cn: boe 基准部署的集群 ID 列表
      en: cluster IDs of boe prod service
    longdesc:
      cn: boe 基准部署的集群 ID 列表
      en: cluster IDs of boe prod service
    needloadwhenrender: true

  - name: ppe_cluster_ids
    kind: 4
    shortdesc:
      cn: cn ppe 部署的集群 ID 列表
      en: cluster IDs of cn ppe service
    longdesc:
      cn: cn ppe 部署的集群 ID 列表
      en: cluster IDs of cn ppe service
    needloadwhenrender: true

  - name: i18n_ppe_cluster_ids
    kind: 4
    shortdesc:
      cn: i18n ppe 部署的集群 ID 列表
      en: cluster IDs of i18n ppe service
    longdesc:
      cn: i18n ppe 部署的集群 ID 列表
      en: cluster IDs of i18n ppe service
    needloadwhenrender: true

  - name: work_item_list
    kind: 4
    shortdesc:
      cn: 工作项列表
      en: List of WorkItems
    longdesc:
      cn: 工作项列表
      en: List of WorkItems
    needloadwhenrender: true

  - name: requirements
    kind: 4
    shortdesc:
      cn: 需求项列表
      en: List of Requirements
    longdesc:
      cn: 需求项列表
      en: List of Requirements
    needloadwhenrender: true

  - name: requirement_url
    kind: 3
    shortdesc:
      cn: 需求链接
      en: URL of Requirement
    longdesc:
      cn: 需求链接
      en: URL of Requirement

  - name: issues
    kind: 4
    shortdesc:
      cn: 缺陷项列表
      en: List of Issues
    longdesc:
      cn: 缺陷项列表
      en: List of Issues
    needloadwhenrender: true

  - name: description
    kind: 3
    shortdesc:
      cn: 发布单描述
      en: Description of Release Ticket
    longdesc:
      cn: 发布单描述
      en: Description of Release Ticket

  - name: release_ticket_id
    kind: 3
    shortdesc:
      cn: 发布单 ID（仅在发布单阶段中注入）
      en: ID of Release Ticket (only inject on stages of Release Ticket)
    longdesc:
      cn: 发布单 ID（仅在发布单阶段中注入）
      en: ID of Release Ticket (only inject on stages of Release Ticket)

  - name: release_ticket_name
    kind: 3
    shortdesc:
      cn: 发布单名称（仅在发布单阶段中注入）
      en: Name of Release Ticket (only inject on stages of Release Ticket)
    longdesc:
      cn: 发布单名称（仅在发布单阶段中注入）
      en: Name of Release Ticket (only inject on stages of Release Ticket)

  - name: qa_role
    kind: 4
    shortdesc:
      cn: 质量负责人
      en: QA approvers
    longdesc:
      cn: 质量负责人
      en: QA approvers
    needloadwhenrender: true

  - name: forward_tce_deployment_id
    kind: 3
    shortdesc:
      cn: 正向 TCE 工单
      en: tce deployment id
    longdesc:
      cn: 正向 TCE 工单
      en: tce deployment id

  - name: web_custom
    kind: 5
    shortdesc:
      cn: web 非发布阶段配置
      en: web custom stage config
    longdesc:
      cn: web 非发布阶段配置
      en: web custom stage config
    needloadwhenrender: true

  - name: web_deploy
    kind: 5
    shortdesc:
      cn: web 发布阶段配置
      en: web deploy stage config
    longdesc:
      cn: web 发布阶段配置
      en: web deploy stage config
    needloadwhenrender: true

  - name: web_rollback_ids
    kind: 3
    shortdesc:
      cn: web 回滚工单
      en: web rollback ids
    longdesc:
      cn: web 回滚工单
      en: web rollback ids

  - name: web_rollback_isttp
    kind: 1
    shortdesc:
      cn: 是否 ttp web 回滚工单
      en: is ttp web rollback
    longdesc:
      cn: 是否 ttp web 回滚工单
      en: is ttp web rollback

  - name: rollback_trigger
    kind: 3
    shortdesc:
      cn: 回滚触发人
      en: rollback trigger
    longdesc:
      cn: 回滚触发人
      en: rollback trigger

  - name: ttp_execution_id
    kind: 3
    shortdesc:
      cn: ttp execution id
      en: ttp execution id
    longdesc:
      cn: ttp execution id
      en: ttp execution id

  - name: ttp_execution_url
    kind: 3
    shortdesc:
      cn: ttp execution url
      en: ttp execution url
    longdesc:
      cn: ttp execution url
      en: ttp execution url

  - name: ttp_rollback_projects_info
    kind: 3
    shortdesc:
      cn: ttp rollback projects info
      en: ttp rollback projects info
    longdesc:
      cn: ttp rollback projects info
      en: ttp rollback projects info

  - name: ies_geckolist
    kind: 4
    shortdesc:
      cn: 跨端项目部署信息(IES)
      en: Hybrid project deployment information (IES)
    longdesc:
      cn: 跨端项目部署信息(IES)
      en: Hybrid project deployment information (IES)
    needloadwhenrender: true

  - name: ies_batch_gecko_map
    kind: 5
    shortdesc:
      cn: 跨端项目批量发布部署信息(IES)
      en: Hybrid project batch deployment information (IES)
    longdesc:
      cn: 跨端项目批量发布部署信息(IES)
      en: Hybrid project batch deployment information (IES)
    needloadwhenrender: true

  - name: tt_geckolist
    kind: 4
    shortdesc:
      cn: 跨端项目部署信息(TT)
      en: Hybrid project deployment information (TT)
    longdesc:
      cn: 跨端项目部署信息(TT)
      en: Hybrid project deployment information (TT)
    needloadwhenrender: true

  - name: planed_release_date
    kind: 3
    shortdesc:
      cn: 发布单设置的计划开始发布日期，如2024-01-01
      en: Planed start release date, for example, 2024-01-01.
    longdesc:
      cn: 发布单设置的计划开始发布日期，如2024-01-01
      en: Planed start release date, for example, 2024-01-01.

  - name: planed_start_integration_date
    kind: 3
    shortdesc:
      cn: 发布单设置的计划开始集成日期，如2024-01-01
      en: Planed start integration date, for example, 2024-01-01.
    longdesc:
      cn: 发布单设置的计划开始集成日期，如2024-01-01
      en: Planed start integration date, for example, 2024-01-01.

  - name: planed_finish_integration_date
    kind: 3
    shortdesc:
      cn: 发布单设置的计划结束集成日期，如2024-01-01
      en: Planed finish integration date, for example, 2024-01-01.
    longdesc:
      cn: 发布单设置的计划结束集成日期，如2024-01-01
      en: Planed finish integration date, for example, 2024-01-01.

  - name: planed_create_date
    kind: 3
    shortdesc:
      cn: 创建发布单时填写的计划创建时间 (已废弃)
      en: Planed create date (deprecated)
    longdesc:
      cn: 创建发布单时填写的计划创建时间 (已废弃)
      en: Planed create date (deprecated)

  - name: creation_time
    kind: 3
    shortdesc:
      cn: 发布单的创建时间，如2024-01-01 00:00
      en: The time when a release ticket is created, for example, 2024-01-01 00:00.
    longdesc:
      cn: 发布单的创建时间，如2024-01-01 00:00
      en: The time when a release ticket is created, for example, 2024-01-01 00:00.

  - name: planed_start_integration_time
    kind: 3
    shortdesc:
      cn: 发布单设置的计划开始集成时间，如2024-01-01 00:00
      en: Planed finish integration time, for example, 2024-01-01 00:00.
    longdesc:
      cn: 发布单设置的计划开始集成时间，如2024-01-01 00:00
      en: Planed finish integration time, for example, 2024-01-01 00:00.

  - name: planed_finish_integration_time
    kind: 3
    shortdesc:
      cn: 发布单设置的计划结束集成时间，如2024-01-01 00:00
      en: Planed finish integration time, for example, 2024-01-01 00:00.
    longdesc:
      cn: 发布单设置的计划结束集成时间，如2024-01-01 00:00
      en: Planed finish integration time, for example, 2024-01-01 00:00.

  - name: planed_release_time
    kind: 3
    shortdesc:
      cn: 发布单设置的计划开始发布时间，如2024-01-01 00:00
      en: Planed start release time, for example, 2024-01-01 00:00.
    longdesc:
      cn: 发布单设置的计划开始发布时间，如2024-01-01 00:00
      en: Planed start release time, for example, 2024-01-01 00:00.

  - name: forward_build_id
    kind: 2
    shortdesc:
      cn: 正向流水线构建 ID
      en: forward build id of pipeline
    longdesc:
      cn: 正向流水线构建 ID
      en: forward build id of pipeline

  - name: forward_job
    kind: 4
    shortdesc:
      cn: 跨端回滚所需正向 Job 信息
      en: hybrid rollback required forward job information
    longdesc:
      cn: 跨端回滚所需正向 Job 信息
      en: hybrid rollback required forward job information
    needloadwhenrender: true

  - name: forward_job_id
    kind: 3
    shortdesc:
      cn: 回滚所需的正向 job_id
      en: rollback required forward job id
    longdesc:
      cn: 回滚所需的正向 job_id
      en: rollback required forward job id

  - name: project_owners
    kind: 4
    shortdesc:
      cn: 项目负责人
      en: Owners of project
    longdesc:
      cn: 项目负责人
      en: Owners of project
    needloadwhenrender: true

  - name: feature_ids_str
    kind: 3
    shortdesc:
      cn: 发布单中关联的 Meego id（包含需求和缺陷，以逗号分隔）
      en: The meego id associated with the release ticket (Includes requirements and issues, split by comma)
    longdesc:
      cn: 发布单中关联的 Meego id（包含需求和缺陷，以逗号分隔）
      en: The meego id associated with the release ticket (Includes requirements and issues, split by comma)

  - name: requirement_ids_str
    kind: 3
    shortdesc:
      cn: 发布单中关联的 Meego 需求 id（以逗号分隔）
      en: The meego requirement id associated with the release ticket (split by comma)
    longdesc:
      cn: 发布单中关联的 Meego 需求 id（以逗号分隔）
      en: The meego requirement id associated with the release ticket (split by comma)

  - name: issue_ids_str
    kind: 3
    shortdesc:
      cn: 发布单中关联的 Meego 缺陷 id（以逗号分隔）
      en: The meego issue id associated with the release ticket (split by comma)
    longdesc:
      cn: 发布单中关联的 Meego 缺陷 id（以逗号分隔）
      en: The meego issue id associated with the release ticket (split by comma)

  - name: faas_deploy_config
    kind: 5
    shortdesc:
      cn: faas 发布阶段配置
      en: faas deploy stage config
    longdesc:
      cn: faas 发布阶段配置
      en: faas deploy stage config
    needloadwhenrender: true

  - name: faas_env_config
    kind: 5
    shortdesc:
      cn: faas 发布多环境配置
      en: faas env deploy config
    longdesc:
      cn: faas 发布多环境配置
      en: faas env deploy config
    needloadwhenrender: true

  - name: forward_faas_deployment_id
    kind: 3
    shortdesc:
      cn: 正向 FaaS 工单
      en: FaaS deployment id
    longdesc:
      cn: 正向 FaaS 工单
      en: FaaS deployment id

  - name: cronjob_deploy_config
    kind: 5
    shortdesc:
      cn: cronjob 发布阶段配置
      en: cronjob deploy stage config
    longdesc:
      cn: cronjob 发布阶段配置
      en: cronjob deploy stage config
    needloadwhenrender: true

  - name: pipeline_control_plane
    kind: 3
    shortdesc:
      cn: 流水线运行时控制面
      en: Pipeline Runtime Control Plane
    longdesc:
      cn: 流水线运行时，具体所处控制面的信息，例如：CN、I18N、EU-TTP、US-TTP
      en: Information of the specific Control Plane where the pipeline resides during runtime，for example CN、I18N、EU-TTP、US-TTP

  - name: main_scm_repo_name
    kind: 3
    shortdesc:
      cn: SCM 主仓库名称
      en: Name of Main SCM Repo
    longdesc:
      cn: SCM 主仓库名称
      en: Name of Main SCM Repo

  - name: lark_group_ids_str
    kind: 3
    shortdesc:
      cn: 发布单绑定的飞书群
      en: Lark Group Ids registered in release ticket
    longdesc:
      cn: 发布单绑定的飞书群，多个以 , 分割组成的字符串
      en: Lark Group Id registered in release ticket，value type is string (splitted by ,)

  - name: is_single_dc
    kind: 3
    shortdesc:
      cn: 单机房部署
      en: Single Dc Deployment
    longdesc:
      cn: 单机房部署
      en: Single Dc Deployment

  - name: single_dc
    kind: 3
    shortdesc:
      cn: 单机房部署的机房信息
      en: DC Information of Single Dc Deployment
    longdesc:
      cn: 单机房部署的机房信息
      en: DC Information of Single Dc Deployment
  - name: stage_id
    kind: 2
    shortdesc:
      cn: 已废弃
      en: deprecated
    longdesc:
      cn: 已废弃
      en: deprecated
  - name: node_id
    kind: 2
    shortdesc:
      cn: 发布单阶段id
      en: Stage Id Of ReleaseTicket
    longdesc:
      cn: 发布单阶段id
      en: Stage Id Of ReleaseTicket

  - name: project_metadata_custom
    kind: 5
    shortdesc:
      cn: 自定义项目的自定义元数据字段
      en: Custom metadata fields for custom project
    longdesc:
      cn: 使用“应用组建-一键创建”创建自定义项目时添加的自定义参数
      en: Custom parameters added when creating a custom project using "Application Component - One-click Creation"

  - name: tcc_config
    kind: 5
    shortdesc:
      cn: TCC 原子发布需要变量
      en: Vars for tcc atoms
    longdesc:
      cn: TCC 原子发布需要变量
      en: Vars for tcc atoms

  - name: scm_configs_be
    kind: 3
    shortdesc:
      cn: SCM 信息
      en: SCM information
    longdesc:
      cn: SCM 信息，包含主仓和依赖仓信息
      en: SCM information，including main repository and dependent repository information

  - name: rollback_reason
    kind: 3
    shortdesc:
      cn: 回滚原因
      en: rollback reason
    longdesc:
      cn: 触发回滚时选择的回滚原因
      en: Rollback reason selected when the rollback is triggered

  - name: rollback_comment
    kind: 3
    shortdesc:
      cn: 回滚备注
      en: rollback comment
    longdesc:
      cn: 触发回滚时手动填写的备注
      en: comment Manually entered when the rollback is triggered

  - name: rollback_config
    kind: 5
    shortdesc:
      cn: 回滚配置
      en: rollback config
    longdesc:
      cn: 包括 :是否开启回滚配置修改，回滚粒度，滚动间隔时间，回滚更新方式：（先杀后起/先启后杀）
      en: 'Include: rollback granularity, rolling interval, rolling back update mode: (start first/delete first)'

  - name: web_forward_deployment_ids
    kind: 4
    shortdesc:
      cn: web正向工单
      en: web forward deployment ids
    longdesc:
      cn: 需要回滚的web正向工单
      en: web forward deployment ids
    needloadwhenrender: true
  - name: psm_list
    kind: 4
    shortdesc:
      cn: PSM列表
      en: psm list
    longdesc:
      cn: 发布单本次发布的PSM列表
      en: PSM list of the current release ticket

  - name: process_type
    kind: 3
    shortdesc:
      cn: 研发流程设置类型，含火车、需求、紧急上线等
      en: R&D process setting types, including train, feature, hotfix, etc.
    longdesc:
      cn: 研发流程设置类型，含火车、需求、紧急上线等
      en: R&D process setting types, including train, feature, hotfix, etc.

  - name: enable_hot_deploy
    kind: 3
    shortdesc:
      cn: 是否开启热部署
      en: is enable hot deploy.
    longdesc:
      cn: 是否开启热部署
      en: is enable hot deploy.

  - name: boe_env_hot_deployment
    kind: 3
    shortdesc:
      cn: 线下 CN&I18N 是否开启热部署
      en: whether hot deployment is enabled in cn&i18n boe.
    longdesc:
      cn: 线下 CN&I18N 是否开启热部署
      en: whether hot deployment is enabled in cn&i18n boe.

  - name: boe_i18n_sg_env_hot_deployment
    kind: 3
    shortdesc:
      cn: 线下 I18N-SG 是否开启热部署
      en: whether hot deployment is enabled in i18n-sg boe.
    longdesc:
      cn: 线下 I18N-SG 是否开启热部署
      en: whether hot deployment is enabled in i18n-sg boe.

  - name: boe_i18n_va_env_hot_deployment
    kind: 3
    shortdesc:
      cn: 线下I 18N-VA 是否开启热部署
      en: whether hot deployment is enabled in i18n-va boe.
    longdesc:
      cn: 线下I 18N-VA 是否开启热部署
      en: whether hot deployment is enabled in i18n-va boe.

  - name: ppe_cn_env_hot_deployment
    kind: 3
    shortdesc:
      cn: 线上 CN 是否开启热部署
      en: whether hot deployment is enabled in cn online.
    longdesc:
      cn: 线上 CN 是否开启热部署
      en: whether hot deployment is enabled in cn online.

  - name: ppe_i18n_env_hot_deployment
    kind: 3
    shortdesc:
      cn: 线上 I18N 是否开启热部署
      en: whether hot deployment is enabled in i18n online.
    longdesc:
      cn: 线上 I18N 是否开启热部署
      en: whether hot deployment is enabled in i18n online.

  - name: boe_base_region
    kind: 3
    shortdesc:
      cn: BOE 基准部署区域
      en: boe base region
    longdesc:
      cn: BOE 基准部署区域
      en: boe base region

  - name: integration_branch_list
    kind: 4
    shortdesc:
      cn: 发布单主流水线上的集成分支列表
      en: Integration branch list on the main release pipeline.
    longdesc:
      cn: 发布单主流水线上的集成分支列表
      en: Integration branch list on the main release pipeline.

  - name: release_branch_list
    kind: 4
    shortdesc:
      cn: 发布单主流水线上的发布分支列表
      en: Release branch list on the main release pipeline.
    longdesc:
      cn: 发布单主流水线上的发布分支列表
      en: Release branch list on the main release pipeline.

  - name: announcement
    kind: 3
    shortdesc:
      cn: 公告
      en: announcement
    longdesc:
      cn: 发布单公告
      en: release_ticket announcement
  - name: ppe_cn_env_enable_debug
    kind: 3
    shortdesc:
      cn: 线上 CN 是否开启PPE Debug
      en: whether ppe debug is enabled in cn online.
    longdesc:
      cn: 线上 CN 是否开启PPE Debug
      en: whether ppe debug is enabled in cn online.
  - name: ppe_i18n_env_enable_debug
    kind: 3
    shortdesc:
      cn: 线上 I18N 是否开启PPE Debug
      en: whether ppe debug is enabled in i18n online.
    longdesc:
      cn: 线上 I18N 是否开启PPE Debug
      en: whether ppe debug is enabled in i18n online.

  - name: detail_url
    kind: 3
    shortdesc:
      cn: 研发流程流水线 URL，可以定位到发布单详情中具体流水线位置
      en: The URL of R&D pipeline can pinpoint the specific pipeline location within the release ticket details
    longdesc:
      cn: 研发流程流水线 URL，可以定位到开发任务详情中具体流水线位置
      en: The URL of R&D pipeline can pinpoint the specific pipeline location within the release ticket details

  - name: project_deploy_type_custom
    kind: 3
    shortdesc:
      cn: 自定义项目部署方式
      en: deploy type of custom project
    longdesc:
      cn: 自定义项目部署方式
      en: deploy type of custom project

  - name: project_custom_sidecar_name
    kind: 3
    shortdesc:
      cn: Mesh-sidecar自定义项目sidecar_name
      en: Mesh-sidecar sidecar_name
    longdesc:
      cn: Mesh-sidecar自定义项目sidecar_name
      en: Mesh-sidecar sidecar_name

  - name: subpath
    kind: 3
    shortdesc:
      cn: 项目路径
      en: project subpath
    longdesc:
      cn: 项目路径
      en: project subpath