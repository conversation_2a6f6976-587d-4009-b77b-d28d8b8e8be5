// Code generated by Kitex v1.0.6. DO NOT EDIT.

package reviewservice

var IDLs = map[string]string{
	"../../base.thrift": `namespace py base
namespace go base
namespace rs base
namespace java com.bytedance.thrift.base

struct TrafficEnv {
    1: bool Open = false,
    2: string Env = "",
}

struct Base {
    1: string LogID = "",
    2: string Caller = "",
    3: string Addr = "",
    4: string Client = "",
    5: optional TrafficEnv TrafficEnv,
    6: optional map<string, string> Extra,
}

struct BaseResp {
    1: string StatusMessage = "",
    2: i32 StatusCode = 0,
    3: optional map<string, string> Extra,
}`,

	"appstore_review.thrift": `include "../../base.thrift"

namespace go ep.artifact.manager.appstorereview
namespace py ep.artifact.manager.appstorereview

// Task Queue Type
const string TaskTypeGPUpload = "GPUpload"

const string TaskTypeReviewGPPrefix          = "ReviewGP"                // 前缀，通过这个区分发布/预审的任务
const string TaskTypeReviewGPUploadPkg       = "ReviewGPUploadPkg"       // 上传包
const string TaskTypeReviewGPUploadMetadata  = "ReviewGPUploadMetadata"  // 上传元数据
const string TaskTypeReviewGPSubmitForReview = "ReviewGPSubmitForReview" // 提审

const string KeyPatternGPGetReviewStatusLock    = "gp:api:get_review_status"
const string KeyUltramanGPVersionListLock       = "gp:api:version_list_lock:%v" //bundle_id
const string KeyPatternGPReviewStatus           = "gp:api:review_status:%v"  // bundle_id
const string KeyUltramanGPVersionList           = "gp:api:version_list:%v"   // bundle_id
const string KeyUltramanGPInstallUserNum        = "gp:api:install_user_num:%v" // bundle_id
const string KeyUltramanGPInstallUserNumLock    = "gp:api:install_user_num_lock:%v" // bundle_id
const string KeyUltramanGPActiveDeviceNum       = "gp:api:active_device_num:%v" //bundle_id
const string KeyUltramanGPActiveDeviceNumLock   = "gp:api:active_device_num_lock:%v"//bundle_id
const string KeyUltramanGPSupportDeviceChanges       = "gp:api:support_device_change:%v:%v:%v" //bundle_id:version:version_code
const string KeyUltramanGPSupportDeviceChangesLock   = "gp:api:support_device_change_lock:%v:%v:%v"//bundle_id:version:version_code

enum AppEnv {
	REVIEW
	MARS
}

struct ReviewFrontContext {
	1: optional UserInfo user_info
	2: optional AppInfo app_info
}

enum AppStorePhaseReleaseStatus {
	INACTIVE = 0
	ACTIVE = 1
	PAUSED = 2
	COMPLETE = 3
}

enum StatusType {
    INIT = 0,                                       //预审单刚创建
    CHECKING = 1,                                   //预审单上传完物料和包,进入确认项检查
    CHECKREFUSED = 2,                               //预审单确认项检查被拒绝
    REVIEWING = 3,                                  //预审单物料和包确认项检查完,进入预审
    REVIEWREFUSED = 4,                              //预审单预审被拒绝
    COMMITTING = 5,                                 //预审单预审通过待提审
    COMMITREFUSED = 6,                              //预审单提审被拒绝
    APPSTOREREVIEWING = 7,                          //预审单提审通过提交AppStore审核
    APPSTOREREFUSED = 8,                            //AppStore拒审了
    PUBLISHING = 9,                                 //AppStore通过了，待销售(readyForSale)
    PREPARECOMMITING = 10,                          //提审通过，准备提交AppStore
    APPSTOREREVIEWWAITING = 11,                     //正在等待审核
    APPSTOREBINARYINVALID = 12,                     //二进制文件无效
    APPSTOREREJECTED = 13,                          //被拒绝（新）
    APPSTOREREJECTEDBYDEVELOPER = 14,               //被开发者拒绝
    APPSTOREOFFSHELF = 15,                          //被下架
    APPSTOREPENDINGRELEASE = 16,                    //AppStore审核通过了，等待开发者发布
    ACCEPTED = 17,                                  //已接单
    APPEALING = 18,                                 //已申诉
    APPEALINGFAIL = 19                              //申诉失败,选择关闭单子
    RESTART = 20                                    //被驳回重新开始
    INITREFUSED = 21                                //初始状态被驳回,适用于物料和包一个init
    RESTARTREFUSED = 22                             //重新开始后被关闭,适用同上
    ACCEPTEDREFUSED = 23                            //接单后关闭单子
    SLOVE = 24                                      //已解决
    APPSTORECOMMITFAIL = 25                         //AppStore送审失败
    APPSTOREOFFSHELFBYDEV = 26                      //被开发者下架
    WAITING_GP_REVIEWING = 27                       //等待gp审核
    GP_REVIEWING = 28                               //gp审核中
    WAITING_GP_RELEASE = 29                         //等待gp放量
    GP_RELEASE = 30                                 //gp已放量
    GP_REJECT = 31                                  //被gp拒绝
    GP_COMMIT_FAIL = 32                             //GP送审失败
    WAITING_GP_COMMIT = 33                          //等待gp后台送审
    APPSTORE_METADATA_REJECTED =34                  //苹果后台因为元数据拒绝
    PREPARE_FOR_SUBMISSION = 35                     //苹果后台等待提交状态,不对应预审单状态,供轮询使用
    APPSTORE_REJECT_TERMINAL = 36                   //被苹果拒审后终止
    GP_REVIEW_DONE = 37                             //GP平台审核结束
    RELEASE_TERMINATED = 38                         //GP发布单被关闭
}

enum CheckItemStatus {
    UNCONFIRMED = 0,                                //初始状态
    REFUSED = 1,                                    //确认项不通过
    PASS = 2,                                       //确认项通过
    AUTO_PASS = 3,                                  //确认项自动通过
}

//这个东西还是得要
enum Role {
    MATERIAL = 1,                                   //物料负责人
    CHECK = 2,                                      //检查负责人
    PRECHECK = 3,                                   //预审负责人
    COMMIT = 4,                                     //提审负责人
    FOCUS = 5,                                      //关注人
    ACCEPTED = 6,                                   //拒审接单人
    MANAGER = 7                                     //应用负责人
}

enum Tag {
    UNDEFINED = 0        // 缺省值
    REJECTED = 1         // 该版本被拒审过
    AUTO_COMMIT = 2      // 自动提审
    SELF_REVIEW = 3      // 自助预审
    SETTINGS = 4         // 存在settings配置
    TCC = 5              // 存在TCC配置
    STARLING_WHATS_NEW = 6   // whats_new字段存在starling配置
    STARLING_DESCRIPTION = 7 // description字段存在starling配置
    APPEALING = 8            // 预审单申诉中
    PROCESSING = 9           // 预审单拒审处理中
    GP_REVIEW_RISK = 10      // GP预审存在风险
    GP_REVIEW_NON_RISK = 11      // GP预审通过
    GP_REVIEW_UNDONE = 13    // 未预审
    GP_RELEASE = 14          // GP商店状态,当前默认已发布
    PREVIOUS_VERSION_REJECTED = 15 // 上一个版本发生过拒审
}

enum TagType {
    UNDEFINED = 0 // 缺省值
    ReviewTag = 1 // 预审tag
}

enum MaterialStatus {
    UNDONE                          = 0      //未同步
    UPLOADING                       = 1      //同步中
    FAIL                            = 2      //同步失败
    SYNCHRONIZED                    = 3      //已同步
    CREATING_EDIABLE_VERSION        = 4      //后台创建可编辑版本中
    CREATING_EDIABLE_VERSION_FAIL   = 5      //后台创建可编辑版本失败
    CREATING_FOLDER                 = 6      //后台创建文件夹中
    CREATING_FOLDER_FAIL            = 7      //后台创建文件夹失败
    DOWNLOADING_METADATA            = 8      //后台下载文字物料信息中
    DOWNLOADING_METADATA_FAIL       = 9      //后台下载文字物料信息失败
    DOWNLOADING_ATTACHED_FILE       = 10     //后台下载附件中
    DOWNLOADING_ATTACHED_FILE_FAIL  = 11     //后台下载文字物料信息失败
    DOWNLOADING_PICANDVIDEO         = 12     //后台下载图片和视频信息中
    DOWNLOADING_PICANDVIDEO_FAIL    = 13     //后台下载图片和视频信息失败
    COMPRESSING_FILE                = 14     //后台压缩文件中
    COMPRESSING_FILE_FAIL           = 15     //后台压缩文件失败
    STATUS_CHECKED                  = 16     //后台状态检查中
    REJECTED                        = 17     //物料被驳回
    CHECKING                        = 18     //物料确认项检查中
    REVIEWING                       = 19     //物料预审
    COMMITTING                      = 20     //物料提审
    COMMITTINGPASS                  = 21     //物料提审通过
    CHECKINGREFUSED                 = 22     //物料确认项检查被拒绝
    REVIEWINGREFUSED                = 23     //物料预审被拒绝
    COMMITTINGREFUSED               = 24     //物料提审被拒绝
    IAP_UPLOADING_SUCCESS           = 25     //iap同步成功
    IAP_UPLOADING_FAIL              = 26     //iap同步失败
    IAP_UPLOADING                   = 27     //物料同步成功
    SPECIALREJECTED                 = 28     //物料特殊流程被驳回,提交后直达提审
    CREATING_LANG                   = 29     //增加本地化语言中
    CREATED_LANG_FAIL               = 30     //增加本地化语言失败
    DELETEING_LANG                  = 31     //删除本地化语言中
    DELETED_LANG_FAIL               = 32     //删除本地化语言失败
}

enum ReviewResult {
    UNREVIEWED          = 0,     //初始状态
    PASS                = 1,     //全部通过
    REFUSED             = 2,     //整个单子拒绝
    PACKAGEPASS         = 3,     //包通过
    MATERIALPASS        = 4,     //物料通过
    PACKAGEREJECTED     = 5,     //包驳回
    MATERIALREJECTED    = 6,     //物料驳回
    BOTHREJECTED        = 7,     //一起驳回,仅限预审和提审调用
    APPEAL              = 8,     //拒审用,申诉
    MATERIALREFUSED     = 9,     //物料拒审
    PACKAGEREFUSED      = 10,    //包拒审
    COMMITFAIL          = 11,    //提审失败
    MATERIALWITHDREW    = 12,    //物料撤审
    PACKAGEWITHDREW     = 13,    //包撤审
    BOTHWITHDREW        = 14,    //物料和包撤审
    EMERG_CREATED       = 15,    //紧急流程创建
    EMERG_CHANGED       = 16,    //rocket创建的预审单切换至紧急流程
    PACKAGEREJECTEDWITHSPECIALFLOW = 17, //包驳回并转为特殊流程（提交完直接提审）
    MATERIALREJECTEDWITHSPECIALFLOW = 18, //物料驳回并转为特殊流程（提交完直接提审）
    BOTHREJECTEDWITHSPECIALFLOW = 19 //全驳回并转为特殊流程（提交完直接提审）
}

enum ResignJobState {
    FAILED      = 1   //重签名失败
    SUCCEEDED   = 2   //重签名成功
    TIMEDOUT    = 3   //重签名timeout
    NOTSTARTED  = 4   //重签名未开始
    RUNNING     = 5   //重签名正在进行中
}

enum ResignJobStatus {
	RUNNING   = 0   //重签名正在进行中
	SUCCEEDED = 1   //重签名成功
	FAILED    = 2   //重签名失败
	TIMEDOUT  = 3   //重签名timeout
	NOTSTARTED = 4  //重签名未开始
}

enum ITCPkgUploadStatus {
    UNDONE              = 0     //包未上传
	RUNNING             = 1     //包同步到苹果后台中
	SUCCEEDED           = 2     //包同步到苹果后台成功
	FAILED              = 3     //包同步到苹果后台失败
	TIMEDOUT            = 4     //包同步到苹果后台超时
    REJECTED            = 5     //包被驳回
    CHECKING            = 6     //包确认项检查中
    REVIEWING           = 7     //包预审
    COMMITTING          = 8     //包提审
    COMMITTINGPASS      = 9     //包提审通过
    CHECKINGREFUSED     = 10    //确认项检查被拒绝
    REVIEWINGREFUSED    = 11    //预审被拒绝
    COMMITTINGREFUSED   = 12    //提审状态被拒绝
    FAILEDBYSAMEVERSION = 13    //因为相同小版本号被拒绝
    SPECIALREJECTED     = 14    //包特殊流程被驳回,提交后直达提审
}

enum ProcessType {
    QA          = 1
    UG          = 2
    QA_MATERIAL = 3
    QA_PACKAGE  = 4
    PG          = 5
}

enum ContentType {
    REVIEW_CONTENT                  = 1 //预审说明
    REJECT_CONTENT                  = 2 //拒审说明,废弃,新的在review_refused表的solution
    MATERIAL_REJECTED_CHECKING      = 3 //物料在checking被拒绝填写的理由,下同
    PACKAGE_REJECTED_CHECKING       = 4 //包在checking被拒绝填写的理由,下同
    BOTH_REJECTED_CHECKING          = 5 //物料和包在checking被拒绝填写的理由
    MATERIAL_REJECTED_REVIEWING     = 6
    PACKAGE_REJECTED_REVIEWING      = 7
    BOTH_REJECTED_REVIEWING         = 8
    MATERIAL_REJECTED_COMMITTING    = 9
    PACKAGE_REJECTED_COMMITTIING    = 10
    BOTH_REJECTED_COMMITTING        = 11
    CHECKING_REFUSED                = 12
    REVIEWING_REFUSED               = 13
    COMMITTING_REFUSED              = 14
    MATERIAL_REJECTED_COMMITFAIL    = 15 //送审失败物料驳回
    PACKAGE_REJECTED_COMMITFAIL     = 16 //送审失败包驳回
    BOTH_REJECTED_COMMITFAIL        = 17 //送审失败全驳回
    MATERIAL_REJECTED_DEVELOPER     = 18 //开发者拒绝后物料驳回
    PACKAGE_REJECTED_DEVELOPER      = 19 //开发者拒绝后包驳回
    BOTH_REJECTED_DEVELOPER         = 20 //开发者拒绝后全驳回
    //送审失败暂不支持终止
}

enum AgeRatingType {
    NONE                = 1
    INFREQUENT_OR_MILD  = 2
    FREQUENT_OR_INTENSE = 3
}

enum KidsAgeBand {
    NULL           = 0  // 不面向儿童
    FIVE_AND_UNDER = 1
    SIX_TO_EIGHT   = 2
    NINE_TO_ELEVEN = 3
}

enum ConfigType {
    Settings = 1
    TCC      = 2
    Starling = 3
}

enum Region {
//https://bytedance.feishu.cn/wiki/wikcnQpQ3S1WuoPmxXHHBEZo0Gh#
    UNEXPECTED  = 0
    BOE         = 1 //国内boe
    CN          = 2 //国内
    SGALI       = 3 //新加坡阿里云
    MVAALI      = 4 //美东阿里云
    VA          = 5 //美东AWS
    GCP         = 6 //废弃 用I18N
    AWSFR       = 7 //德国法兰克福
    AWSIN       = 8 //印度孟买
    I18N        = 9 //美东GCP
    GALINC2     = 10 //游戏国内阿里云
    GALICG      = 11 //游戏新加坡阿里云
    SYKA        = 12 //顺义lark机房
    SYKA2       = 13 //顺义lark2机房
    BOEI18N     = 14 //BOE国际化机房
    SGSAAS1     = 15 //sgsass1larkidc
    SGEE        = 16 //sgee
    SGCOMM      = 17 //新加坡教育机房
}

enum PreviewInfoType {
    SCREENSHOT = 1  //图片
    PREVIEW    = 2  //视频
}

enum Platform {
    itc     = 1
    googleplay = 2
}

enum OperationType {
    TICKET_CREATE = 1       //预审单创建
    TICKET_STATEFLOW = 2    //预审单状态流转
    SYNC_MATERIAL = 3       //同步物料
    SYNC_PACKAGE = 4        //同步包
    SYNC = 5                //同步
    ACCEPT_TICKET = 6       //接单
    CHANGE_FLOW = 7         //修改预审单流程
    FILL_REJECT_INFO = 8    //填写拒审意见
    STAGED_RELEASE = 9      //分阶段放量
    RELEASE_PENDING = 10    //暂停放量
    FULL_RELEASE = 11       //直接全量
    SYNC_PACKAGE_SUCC = 12           //同步包成功
    SYNC_PACKAGE_FAIL = 13           //同步包失败
    SYNC_INTERNAL_SHARING = 14       //同步内部分享
    SYNC_INTERNAL_SHARING_SUCC = 15  //同步内部分享成功
    SYNC_INTERNAL_SHARING_FAIL = 16  //同步内部分享失败
    SYNC_MATERIAL_SUCC = 17          //同步物料成功
    SYNC_MATERIAL_FAIL = 18          //同步物料失败
    GP_REVIEW_PASS = 19              //gp审核通过
    GP_REVIEW_REJECT = 20            //gp审核被拒绝
    METADATA_MODIFY_IN_APPSTORE_REVIEWWAITING = 21  // 在预审单等待AppStore审核的时候修改了元数据
    REPLY_APPSTORE = 22  //回复了苹果商店
    METADATA_MODIFY_IN_APPSTORE_REJECTED = 23 // 应用被拒审时修改了元数据
    PHASE_RELEASE_CHANGE_TO_COMPLETE = 24 // 放量方式分阶段->全量
    PHASE_RELEASE_CHANGE_TO_PHASE = 25 // 放量方式全量->分阶段
    PREVIEW_ADD = 26    //增加预览视频/图片
    PREVIEW_SWAP = 27   //修改预览视频/图片
    PREVIEW_DELETE = 28 // 删除预览视频/图片
}

enum StateFlow {
    TO_SUBMIT = 1
    MATEDATA_TO_SUBMIT = 2
    PACKAGE_TO_SUBMIT = 3
    TO_CHECK = 4
    MATEDATA_TO_CHECK = 5
    PACKAGE_TO_CHECK = 6
    TO_REVIEW = 7
    TO_COMMIT = 8
    ITC_WITHDREW = 9
    ACCEPTED = 10
    APPEALED = 11
    COMMIT_FAIL = 12
}

// 飞书消息卡片回调按钮类型
enum CardButton {
	BLOCK_REPEAT_NOTIFY = 1 // 屏蔽按钮
	APPSTORE_CHANGE_TO_PHASE_RELEASE = 2 // AppStore全量切换到分阶段并放量按钮
	APPSTORE_CHANGE_TO_COMPLETE_RELEASE = 3 // AppStore分阶段切换到全量并放量按钮
	APPSTORE_START_RELEASE = 4 // AppStore开始放量按钮
	APPSTORE_PAUSE_RELEASE = 5 // AppStore暂停放量按钮
	APPSTORE_RESUME_RELEASE = 6 // AppStore恢复放量按钮
	APPSTORE_COMPLETE_RELEASE = 7 // AppStore全量放量按钮
}

enum AppStoreRejectType {
    UNDEFINED = 0 // 未定义
    URGENT_RELEASE = 1 //紧急提审
    MISTRIAL_OR_POLICY_ADJUSTMENT = 2 //误判或政策调整
    REVIEW_OMISSION = 3 //预审忽略
    UNREVIEW = 4 //未预审
}

enum AppStoreRejectProcessType {
    UNDONE = 0 //未处理
    APPEALING = 1 //申诉中
    DONE = 2 //处理完毕
    METADATA_MODIFIED = 3 //元数据修改
    PROCESSING = 4 //处理中
}

enum MaterialType {
    AppInfoLocalization = 1
    VersionLocalization = 2
    Preview = 3
}

enum UltramanGetSupportDeviceStatus {
    SUCCESS    = 1
    FAIL       = 2         // 非下述错误
    UNREGISTER = 3         // app_id,platform未在预审平台录入凭证
    PACKAGE_NOT_FOUND = 4  // gp后台草稿未找到对应包
    ULTRAMAN_ERROR = 5     // ultraman平台用例执行失败
    PACKAGE_DUPLICATE = 6  // gp后台草稿选择了多个包
}

enum SupportDevice {
    phone      = 1 // 手机
    tablet     = 2 // 平板
    tv         = 3 // 电视
    wearable   = 4 // 可穿戴设备
    car        = 5 // 车载
    chromebook = 6 // 可阅读设备？
}

//预审单
struct ReviewTicket {
    1: required i64 id,                                             //预审单ID
    2: required i64 app_id,                                         //app的id
    3: required string app_name,                                    //app名称
    4: required string creator,                                     //预审单创建人
    5: required string name,                                        //预审单名称
    6: required string app_version,                                 //app版本
    7: required StatusType status,                                  //该预审单现处状态
    8: optional string regulation_name,                             //app所属应用规则名称
    9: required MaterialStatus material_status,                     //物料状态
    10: required string create_time,                                //预审单创建时间
    11: optional i64 commit_time,                                   //提审时间 暂时废弃
    12: optional i64 approve_time,                                  //过审时间 暂时废弃
    13: optional list<ReviewTicketUser> review_ticket_user_list,    //五种角色的人列表
    14: optional list<Tag> tags,                                    //标签列表 暂时废弃 optional
    15: required ITCPkgUploadStatus package_status,                 //包状态
    16: optional bool isRejected,                                   //是否被拒审过
    17: optional i64 remaining_time_appealing,                      //预审单修改时间
    18: optional string group_id,                                   //发版群号
    19: optional bool is_emergency_flow,                            //是否是过紧急流程
    20: optional TicketType ticket_type,                            //预审单种类
    21: optional TicketFlow ticket_flow,                            //预审单状态流类型
    22: optional i64 main_id,                                       //检测主任务ID
    23: optional bool settings_config,                              //是否有配置setting
    24: optional bool tcc_config,                                   //是否有配置tcc
    25: optional double release_rating,                             //放量比例
    26: optional i64 pkg_id,                                        //检测任务包ID
    27: optional bool auto_commit                                  //是否自动提审
    28: optional bool starling_config_whats_new,                   // 是否在starling配置了whats_new
    29: optional bool starling_config_description,                 // 是否在starling配置了description
}
/* 预审单种类 */
enum TicketType {
    PLATFORMCREATED_IOS = 1,
    ROCKETCREATED_IOS = 2,
    PLATFORMCREATED_GP = 3,
    ROCKETCREATED_GP = 4,
}
/* 预审单状态流类型 */
enum TicketFlow {
    NORMAL_FLOW = 1, //正常流程
    EMERG_FLOW = 2, //紧急流程
}


enum GPUploadStatus {
    UNDONE = 1
    UPLOADING = 2
    FAIL = 3
    SUCCESS = 4
}

enum ArtifactType {
    AAB = 1
    APK = 2
    IPA = 3
}

enum StateFlowType {
    DRAFT = 1 //保存草稿
    PASS = 2 //通过
    REJECT = 3 //驳回
    REFUSE = 4 //终止
    COMMIT_FAIL = 5 //提审失败
    METADATA_PASS = 6 //元数据通过
    PACKAGE_PASS = 7 //二进制文件通过
    METADATA_REJECT = 8 //元数据驳回
    PACKAGE_REJECT = 9 //二进制文件驳回
    METADATA_REFUSE = 10 //元数据终止
    PACKAGE_REFUSE = 11 //二进制文件终止
    METADATA_WITHDRAW = 12 //元数据撤回
    PACKAGE_WITHDRAW = 13 //二进制文件撤回
    WITHDRAW = 14 //元数据&二进制文件 撤回
    METADATA_EMERG_REJECT = 15 //元数据紧急流程驳回
    PACKAGE_EMERG_REJECT = 16 //二进制文件紧急流程驳回
    EMERG_REJECT = 17 //紧急流程驳回
    APPSTORE_REJECT = 18 // AppStore拒审
    METADATA_EMERG_WITHDRAW = 19 //元数据紧急流程撤回
    PACKAGE_EMERG_WITHDRAW = 20 //二进制文件紧急流程撤回
    EMERG_WITHDRAW = 21 //元数据&二进制文件 紧急流程撤回
}

// AppStoreOpenAPIError
struct AppStoreErrors {
    1: optional list<AppStoreError> errors
}

struct AppStoreSource {
    1: required string pointer
}

struct AppStoreError {
    1: required string id,
    2: required string status,
    3: required string code,
    4: required string title,
    5: required string detail,
    6: optional map<string,map<string,list<AppStoreError>>> meta,
    7: optional AppStoreSource source,
}

/* >>>>>>>>>>>>>>>>>>>>>>> 应用规则/检查项设置相关 >>>>>>>>>>>>>>>>>>> */
/* ============== 应用规则页面 =============== */
//应用信息
struct AppInfo {
    1: required i64     app_id (go.tag="json:\"app_id\" query:\"app_id\" form:\"app_id\""),               //应用云平台的app_id
    2: required string  app_name (go.tag="json:\"app_name\" query:\"app_name\" form:\"app_name\""),           //app名称
    3: required string  platform (go.tag="json:\"platform\" query:\"platform\" form:\"platform\""),           //app所属平台
    4: required string  bundle_id (go.tag="json:\"bundle_id\" query:\"bundle_id\" form:\"bundle_id\""),          //app的bundle identifier
    5: required i64     regulation_id (go.tag="json:\"regulation_id\" query:\"regulation_id\" form:\"regulation_id\""),      //app所属应用规则
    6: required bool    in_effect (go.tag="json:\"in_effect\" query:\"in_effect\" form:\"in_effect\""),          //app是否启用
    7: required i64     bits_id (go.tag="json:\"bits_id\" query:\"bits_id\" form:\"bits_id\""),            //bits平台的app_id
    8: optional string  icon_url (go.tag="json:\"icon_url\" query:\"icon_url\" form:\"icon_url\""),           //应用的图标链接
}

// 应用规则
struct Regulation {
    1: required i64         id,                 //应用规则id
    2: required string      name,               //应用规则名称
    3: required list<i64>   app_id_list,        //应用规则绑定的应用id列表
    4: required string      user_info,          //最后一次操作用户信息
    5: required string      creator,            //应用规则创建人
    6: required string      last_modified_by,   //应用规则最后一次更新人
    7: required string      last_modified_at,   //应用规则最后一次更新时间
}

/* ============== 检查项页面 =============== */
// 检查项树结点
struct CheckItemNode {
    1: required i64 id,                      //检查项结点id 此id包括类目id 用层级+实际id的方式区分 "x-y"
    2: required string name,                    //检查项结点名称
    3: required list<CheckItemNode> children,   //检查项结点子结点
    4: optional CheckItemOptional extra,        //检查项结点额外字段
    5: optional string type
}

// 检查项树结点额外字段
struct CheckItemOptional {
    1: required string description,                 //检查项描述
    2: required string supplementary,               //检查项补充说明
    3: required string user_info,                   //最后一次操作用户信息
    4: required string creator,                     //检查项创建人
    5: required string last_modified_by,            //检查项最后一次更新人
    6: required string last_modified_at,            //检查项最后一次更新时间
    7: required list<string> regulation_name_list,  //当前引用该检查项的应用规则名称列表
}

/* ============== 应用规则编辑检查项弹窗 ============== */
struct CheckItemInRegulation {
    1: required i64 id,                                 //检查项id
    2: required string description,                     //检查项描述
    3: required string supplementary,                   //检查项补充说明
    4: required string first_heading,                   //检查项所属一级类目
    5: required string second_heading,                  //检查项所属二级类目
    6: required i64 priority,                           //检查项优先级
    7: required list<ProcessType> process_type_list,    //检查项所属流程列表
}

/* ============== 预审单检查项页面 =============== */
struct CheckItemInTicket {
    1: required i64 id,                     //检查项id
    2: required string description,         //检查项描述
    3: required string supplementary,       //检查项补充说明
    4: required string first_heading,       //检查项所属一级类目
    5: required string second_heading,      //检查项所属二级类目
    6: required i64 priority,               //检查项优先级
    7: required ProcessType process_type,  //检查项所属流程
    8: required string remark,              //检查项备注
    9: required CheckItemStatus status,     //检查项状态，默认为0
    10: required i64 global_checkitem_id, // 全局确认项id
    11: optional string type
}

// 预审单检查项审核结果
struct CheckItemsResult {
    1: required i64 ticket_id,
    2: required i64 check_item_id,
    3: required CheckItemStatus check_item_status,
    4: required i64 global_checkitem_id,
}
/* >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> */

struct ReviewTicketUser {
    1: required string operator,                    //人名
    2: required Role role,                          //这个人对于这个预审单的角色
    3: required string user_info                    //用户信息
}

struct AddReviewTicketReq {
    1: required i64 app_id,                         //appID
    2: required string app_name,                    //app名称
    3: required string platform // itc or googleplay
    4: required string creator,                     //预审单创建者
    5: required string app_version,                 //应用版本
    6: required string user_info,                   //预审单创建者用户信息
    7: optional i64 artifact_id,                    //提审包ID
    8: optional TicketFlow ticket_flow,             //预审单状态流类型
    9: optional i64 chosen_id,                      //使用原因条目id
    10: optional string remarks,                     //备注
    11: optional TicketType ticket_type,            //预审单类型:iOS还是GP
    12: optional bool auto_commit,                  //是否自动提审
    255: optional base.Base Base,
}

struct AddReviewTicketResp {
    1: optional ReviewTicket review_ticket,
    2: optional i64 ticket_id,
    3: optional string platform
    255: base.BaseResp BaseResp,
}

//查询用户负责的app
struct GetAppManagedByUserReq {
    1: required string user_key // 用户邮箱前缀，唯一标识
    255: base.Base Base
}

struct GetAppManagedByUserResp {
    1: required list<AppIdentity> app_identity,
    255: base.BaseResp BaseResp
}

//修改预审平台注册app信息(内部使用)
struct UpdateAppInfoReq {
    1: required i64     primary_id   //app在数据库中的业务无关主键
    2: optional i64     app_id       //app在应用云的id
    3: optional string  app_name     //app名称
    4: optional string  platform     //app所属平台
    5: optional string  bundle_id    //app的bundle identifier
    6: optional bool    in_effect    //app是否启用
    7: optional i64     bits_id      //app在bits平台的id
    8: optional string  icon_url     //app的图标下载链接
    255: base.Base Base
}

struct UpdateAppInfoResp {
    255: base.BaseResp BaseResp
}

//查询苹果后台当前版本和状态
struct QueryInflightVersionReq {
    1: required i64 app_id,
    2: required string app_version
    3: required string platform
    255: base.Base Base,
}

struct QueryInflightVersionResp {
    1: required i64 return_code,
    2: optional string reason,
    3: optional string inflight_version,
    255: base.BaseResp BaseResp,
}

struct QueryInflightVersionRespV2 {
    1: required string version,
    2: required string status,
    255: base.BaseResp BaseResp,
}

//分页查询预审单列表接口
struct QueryReviewTicketListReq {
    1: optional string title,
    2: optional list<StatusType> status,
    3: optional list<i64> app_id,
    4: optional list<string> version,
    5: optional list<i64> regulation_id,
	6: optional string start_commit_time,
	7: optional string end_commit_time,
	8: optional string start_approve_time,
	9: optional string end_approve_time,
	10: optional string start_create_time,
	11: optional string end_create_time,
	12: optional list<Tag> tags,
	13: optional list<ReviewTicketUser> users,
	14: required i64 page_num,
	15: required i64 page_size,
    255: base.Base Base,
}

//视图查询接口
struct QueryReviewTicketListViewReq {
    2: optional list<StatusType> status,
	13: optional list<ReviewTicketUser> users,
	14: required i64 page_num,
	15: required i64 page_size,
    255: base.Base Base,
}

struct QueryReviewTicketListResp {
    1: required i64 total,                          //查询到的结果总数
    2: required i64	page_num,                       //页码数
    3: required i64 page_size,                      //每页大小
    4: required list<ReviewTicket> review_ticket_list, //返回的预审单列表
    255: base.BaseResp BaseResp,
}

struct UploadMaterialReq {
    1: required i64 ticket_id,
    2: optional UserInfo user_info,
    255: base.Base Base,
}

struct UploadMaterialResp {
    1: required bool result,
    255: optional base.BaseResp BaseResp,
}

//上传物料包之后的返回结果
struct UploadMaterialStatusReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}

struct UploadMaterialHistory {
    1: optional MaterialStatus result
    2: optional string result_str
    3: optional string operator
    4: optional string err_msg
}

struct UploadMaterialStatusResponse {
    1: required MaterialStatus status
    2: required string status_str
    3: required list<UploadMaterialHistory> history
    255: optional base.BaseResp BaseResp,
}

// 同步提审包
struct UploadITCPkgReq {
    1: required i64 ticket_id,
    2: required string pkg_url,
    3: optional string user_key,
    255: base.Base Base,
}
struct UploadITCPkgResp {
    255: optional base.BaseResp BaseResp,
}

// 异步返回提审包上传状态
struct UploadITCPkgCallbackReq {
     1: required string return_code,
     2: required string err_type,
     3: required string err_msg_short,
     4: required string msg,
     5: required string extra //传给gameflow接口的信息，原封不动返回回来
     255: base.Base Base,
}

struct UploadITCPkgCallbackResp {
    255: optional base.BaseResp BaseResp,
}

const string UploadIOSPkgSuccess = "2"
const string UploadIOSPkgFailed = "1"

// 异步返回提审包上传状态
struct UploadITCPkgJenkinsCallbackReq {
    1: required string extra, //传给jenkins接口的信息，原封不动返回回来
    2: required string build_number,
    3: required string build_url,
    4: required string status,
    5: required string err_msg,
    255: base.Base Base,
}

struct UploadITCPkgJenkinsCallbackResp {
    255: optional base.BaseResp BaseResp,
}

struct UploadITCPkgExtra {
    1: required i64 ticket_id,
    2: required string user_key,
}

//查看同步提审包完成情况
struct GetITCPkgUploadResultReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}
struct GetITCPkgUploadResultResp {
    1: required ITCPkgUploadStatus status,
    2: required string msg,
    255: optional base.BaseResp BaseResp,
}

//触发/重试预审包重签名
struct TriggerResignJenkinsJobReq {
    1: required i64 ticket_id,
    2: required string pkg_url,
    255: base.Base Base,
}

struct TriggerArtifactResignJenkinsJob{
    1: required i64 aid,
    2: required string pkg_url,
    3: required string callback_url,
    255: base.Base Base,
}

struct TriggerResignJenkinsJobResp {
    1: optional string result,
    255: optional base.BaseResp BaseResp,
}

//得到预审包重签名结果
struct GetResignJobResultReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}
struct GetResignJobResultResp {
    1: required ResignJobStatus status,
    2: required string view_url,
    3: required string download_url,
    4: required string pkg_version,
    255: optional base.BaseResp BaseResp,
}

//Jenkins重签名任务回调, 返回buildNumber
struct BuildNumberCallbackReq {
    1: required i64 ticket_id,
    2: required i64 build_number,
    255: base.Base Base,
}
struct BuildNumberCallbackResp {
    255: optional base.BaseResp BaseResp,
}

//Jenkins重签名任务回调, 返回重签名结果
struct ResignJobCallbackReq {
    1: required i64 ticket_id,
    2: required i64 status,
    3: required string pkg_url,
    255: base.Base Base,
}

//Jenkins不需要ticketId重签名任务回调, 返回重签名结果
struct ArtifactResignJobCallbackReq {
    1: required i64 aid,
    2: required i64 status,
    3: required string pkg_url,
    4: required string callback_url,
    255: base.Base Base,
}

struct ResignJobCallbackResp {
    255: optional base.BaseResp BaseResp,
}

struct ArtifactResignJobCallBackResp {
    1: required string callback_url,
    2: required i64 status,
    3: required string ipa,
    255: optional base.BaseResp BaseResp,
}


/* >>>>>>>>>>>>>>>>>>>>>>> 应用规则/检查项设置相关 >>>>>>>>>>>>>>>>>>> */
// ================ 应用规则CURD
struct AddRegulationReq {
    1: required string regulation_name,
    2: required list<i64> app_id_list,
    3: required string  operator,
    4: required string user_info,
    5: optional string platform,
    255: base.Base Base,
}

struct UpdateRegulationReq {
    1: required i64 regulation_id,
    2: optional string regulation_name,
    3: optional list<i64> app_id_list,
    4: required string operator,
    5: required string user_info,
    255: base.Base Base,
}

struct SingleRegulationReq {
    1: required i64 regulation_id,
    255: base.Base  Base,
}

struct QueryRegulationResp {
    1: required list<Regulation> regulation_list,
    255: base.BaseResp BaseResp,
}

struct DeleteRegulationResp {
    255: base.BaseResp BaseResp,
}

// ================ 查询全量应用信息
struct QueryAppInfoListResp {
    1: required list<AppInfo> app_info_list,
    255: base.BaseResp BaseResp,
}
// ================ 查询应用规则包含的检查项列表
struct QueryCheckItemInRegulationResp {
    1: required list<CheckItemInRegulation> check_item_list,
    255: base.BaseResp BaseResp,
}
// ================ 删除应用规则所包含的检查项
struct DeleteCheckItemInRegulationReq {
    1: required i64 regulation_id,
    2: required i64 check_item_id,
    3: required string  operator,
    4: required string user_info,
    255: base.Base Base,
}

struct DeleteCheckItemInRegulationResp {
    255: base.BaseResp BaseResp,
}
// ================ 添加应用规则所包含的检查项
struct AddCheckItemInRegulationReq {
    1: required i64 regulation_id,
    2: required list<i64> check_item_id,
    3: required string  operator,
    4: required string user_info,
    5: optional string type
    6: optional string description
    7: optional string supplementary
    255: base.Base  Base,
}
// ================ 拖拽排序单个检查项
struct SortCheckItemInRegulationReq {
    1: required i64 regulation_id,
    2: required i64 check_item_id,
    3: required i64 target_check_item_id,
    4: required string  operator,
    5: required string user_info,
    255: base.Base  Base,
}

struct SortCheckItemInRegulaitonResp {
    255: base.BaseResp BaseResp,
}
// ================ 导入应用规则所包含的检查项
struct CopyCheckItemInRegulationReq {
    1: required i64 regulation_id,
    2: required i64 target_regulation_id,
    3: required string  operator,
    4: required string user_info,
    255: base.Base  Base,
}

struct ProcessTypeArr {
    1: required i64 check_item_id,
    2: optional list<i64> process_type,
}

// ================ 修改应用规则所包含的检查项的归属
struct ChangeProcessTypeReq {
    1: required i64 regulation_id,
    2: optional list<ProcessTypeArr> process_type_arr,
    3: required string  operator,
    4: required string user_info,
}

struct ChangeProcessTypeResp {
    255: base.BaseResp BaseResp,
}
// ================ 分类查询检查项
struct QueryCheckItemReq {
    1: required i64 node_id,
    2: required i64 node_level,
    255: base.Base  Base,
}

struct QueryCheckItemResp {
    1: required list<CheckItemNode> check_item_node_list,
    255: base.BaseResp  BaseResp,
}
// ================ 添加/编辑检查项
struct EditCheckItemReq {
    1: required i64 check_item_id,
    2: required i64 node_id,
    3: required string description,
    4: optional string supplementary,
    5: required string operator,
    6: required string user_info,
    255: base.Base  Base,
}
// ================ 添加类目 ***预置用
struct AddCheckItemCategoryReq {
    1: optional i64 parent_id, //默认为0：一级类目
    2: required string name,
    255: base.Base  Base,
}

struct AddCheckItemCategoryResp {
    255: base.BaseResp BaseResp,
}
// ================ 删除检查项
struct DeleteCheckItemReq {
    1: required i64 check_item_id,
    255: base.Base  Base,
}

struct DeleteCheckItemResp {
    255: base.BaseResp BaseResp,
}
// ================ 查询预审单所包含检查项列表
struct QueryCheckItemInTicketReq {
    1: required i64 ticket_id,
    2: required ProcessType process_type
    255: base.Base Base,
}

struct QueryCheckItemInTicketResp {
    1: required i64 ticket_id,
    2: required list<CheckItemInTicket> check_item_list,
    255: base.BaseResp BaseResp,
}
// ================ 添加预审单所包含检查项的备注
struct AddCheckItemRemarksReq {
    1: required i64 ticket_id,
    2: required i64 check_item_id,
    3: required string remarks,
    4: optional ProcessType status,
    5: optional i64 global_checkitem_id,
    255: base.Base Base,
}

struct AddCheckItemRemarksResp {
    1: required i64 ticket_id,
    2: required CheckItemInTicket check_item,
    255: base.BaseResp BaseResp,
}
// ================= 提交预审单所包含检查项的审核结果
struct CommitCheckItemResultReq {
    1: required i64 ticket_id,
    2: required ReviewResult result,
    3: required string operator,
    4: required list<CheckItemsResult> check_item_result_list,
    5: required string user_info,
    6: optional ProcessType status,
    7: optional string content
    255: base.Base Base,
}

struct OperationStateflow {
    1: required StatusType old_status,
    2: required StatusType new_status,
    3: optional ITCPkgUploadStatus package_old_status,
    4: optional ITCPkgUploadStatus package_new_status,
    5: optional MaterialStatus material_old_status,
    6: optional MaterialStatus material_new_status,
    7: required StateFlowType state_flow_type,
}

struct UpdateRecord {
    1: required string updated_column,
    2: required string old_value,
    3: required string new_value,
}

struct ChangeFlowReason {
    1: optional string reason,
    2: optional string remark,
}

struct OperationRecord {
    1: required OperationType operation_type,
    2: required UserInfo user_info
    3: optional OperationStateflow state_flow,
    4: optional string msg,
    5: required string operation_msg,
    // 更新的非多语言字段
    6: optional map<string,map<string,UpdateColumn>> update_column,
    // 更新的多语言字段
    7: optional map<string,map<string,map<string,UpdateColumn>>> update_localization_column,
    // 添加或删除的多语言
    8: optional LocaleUpdateInfo locale_update_info,
    9: required string operation_time,
    10: optional ChangeFlowReason change_flow_reason,
    // 有更新的图片/视频信息
    11: optional map<string,map<string,map<string,list<string>>>> update_preview_localization,
}

struct TicketWithOperationRecord {
    1: optional ReviewTicket review_ticket, //返回的预审单信息
    2: optional list<OperationRecord> operation_record, //操作记录列表
    3: optional StatusType reject_state_flow, //被驳回的节点
    4: optional MaterialStatus material_reject_state_flow //物料被驳回的节点
    5: optional ITCPkgUploadStatus package_reject_state_flow //包被驳回的节点
}

struct QueryTicketWithOperationRecordReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}

struct QueryTicketWithOperationRecordResp {
    1: optional TicketWithOperationRecord ticket_with_operation_record,
    255:required base.BaseResp BaseResp,
}

struct QueryReviewTicketReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}

struct QueryReviewTicketResp {
    1: required ReviewTicket review_ticket_info,
    255: base.BaseResp BaseResp,
}

struct TriggerCronJobResp {
    1: optional string snapshot,
    255: base.BaseResp BaseResp,
}

struct PermissionUser {
    1: required string user_key,    //负责人的key
    2: required string role,        //负责人的角色
    3: required string user_name,   //负责人的名字
    4: required string email,       //负责人的email
}

struct AppRoleReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}

struct GetAppRoleReq {
    1: required i64 app_id,
    2: required string platform,
    255: base.Base Base,
}

struct AppRoleResp {
    1: required list<PermissionUser> roles,
    2: optional TicketFlow ticket_flow,
    3: optional bool is_secret,
    255: base.BaseResp BaseResp,
}

struct ArtifactInfo {
    1: required string artifact_name,
    2: required string artifact_version,
    3: required string artifact_url,
    4: optional string linkmap_url,
    5: optional string dsym_url,
}

struct ArtifactInfoReq {
    1: required i64 ticket_id,
    255: base.Base Base,
}

struct ArtifactInfoResp {
    1: required ArtifactInfo artifact,
    255: base.BaseResp BaseResp,
}

struct QueryContentByIdReq{
    1:  required i64 id,
    255: required base.Base Base
}

struct DeleteTicketReq {
    1: required i64 ticket_id,
    2: required UserInfo user_info,
    255: required base.Base Base
}

struct DeleteTicketResp {
    255: base.BaseResp BaseResp,
}

struct FlushDataReq {
    1: optional i64 ticket_id,
    2: optional string interface,
    255: base.BaseResp BaseResp,
}

struct FlushDataResp{
    1: required bool res,
    255: base.BaseResp BaseResp,
}

struct RejectDetail {
    1: required string guideline
    2: required string reject_category
    3: required string reject_reason
}

struct TicketRejectReasonAndSolutionReq {
    1: required i64 ticket_id
    2: required string operator,
    3: optional list<RejectDetail> reject_detail,
    4: optional string solution,
    5: optional string remark,
    6: optional list<string> reject_type,
    255: required base.Base Base
}

struct TicketRejectReasonAndSolutionResp {
    255: required base.BaseResp BaseResp,
}

struct TicketRejectInformationReq {
    1: required i64 ticket_id,
}

struct AppStoreRejectConclusion {
    1: optional AppStoreRejectType reject_type,
    2: optional string reason,
    3: optional string remark,
    4: optional list<i64> kdb_ids,
    5: optional string solution,
}

struct AppStoreRejectInfo {
    1: optional list<AppStoreRejectMsg> message,
    2: required i64 ticket_id,
}

struct TicketRejectInformation {
    1: optional AppStoreRejectInfo reject_infos,
    2: required bool can_developer_add_note,
    3: optional list<RejectDetail> conclusions,
    4: required string operator,
    5: required AppStoreRejectProcessType process_type,
    6: required string id,
    7: optional StatusType rejection_status,
    8: required bool metadata_modified,
    9: optional string solution,
    10: optional string remark,
    11: optional bool analysis_by_preview,
    12: optional list<string> reject_type (go.tag="json:\"reject_type,omitempty\""),
}

struct TicketRejectInformationResp {
    1: optional list<TicketRejectInformation> ticket_information,
    255: required base.BaseResp BaseResp,
}

struct TicketOptimizeReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional UserInfo user_info,
    4: optional string reject_reason,
    255: required base.Base Base,
}

struct TicketOptimizeResp {
    255: required base.BaseResp BaseResp,
}

struct TicketAppealReq {
    1: required i64 ticket_id,
    2: optional UserInfo user_info,
    3: required string id,
    4: required string content,
    5: optional list<string> attachments
    255: required base.Base Base
}

struct TicketAppealResp {
    255: required base.BaseResp BaseResp,
}

struct TicketRelatedRejectInformatioinReq {
    1: required i64 app_id,
    2: required string app_version,
    255: required base.Base Base
}

struct SendAppealLarkResp {
    1: optional list<i64> ticket_list
    255: required base.BaseResp BaseResp,
}

struct AppSettleReq {
    1: required i64 app_id,         //应用云id
    2: required string app_name,    //中文名称
    3: required string platform,    //平台名,itc/googleplay
    4: required string bundle_id,   //苹果写苹果的bundle_id
    5: required list<PermissionUser> manager_user,  //app负责人
    6: required bool listed,        //是否已上架
    7: optional string account,    //登陆账号
    8: optional string password,  //登陆密码
    9: required string team,        //苹果公司主体,app对应的右上角的公司
    10: required string user_email,  //如果出现不用公司邮箱的情况还是得传送userEmail
    11: required string user_key,    //操作人记录
    12: optional string group_id,    //对应发版群
    13: optional bool secret,        //是不是保密项目
    14: optional string code_name,    //保密项目代号
    15: optional string appstore_name, //苹果商店名称
    16: required bool is_update,    //现在create和update是一个接口 需要判断是create还是update
    17: optional string applestore_id, //苹果商店对应id
    18: optional i64 bits_id,
    19: optional string phase_release_default, // 分阶段发布默认选项
    20: optional string release_type_default, // 版本发布默认选项
    21: optional string reset_mark_default, // 重设评分默认选项
    253: required string apple_id,    //废弃:苹果账号 使用登陆账号
    252: required string apple_password,  //废弃:苹果密码 使用登陆密码
    255: required base.Base Base
}

struct AppSettleResp {
    255: required base.BaseResp BaseResp,
}

struct RocketCreateTicketReq {
    1: required i64 app_id,
    2: required string app_version_string,
    3: required string artifact_name,
    4: required string artifact_url,
    5: required string user_key,
    6: required string user_info,
    7: required string appstore_build_version,
    255: required base.Base Base,
}

struct RocketCreateTicketResp {
    1: required i64 err_code,
    2: required string err_msg,
    3: required i64 ticket_id,
    255: required base.BaseResp BaseResp,
}

struct CheckItcPkgReq {
    1: required i64 ticket_id
    2: required string user_key
    3: required string user_info
    4: required ReviewResult result,
    255: required base.Base Base,
}

struct CheckItcPkgResp {
    255: required base.BaseResp BaseResp,
}

struct AppStorePkgStatusReq {
    1: required i64 ticket_id,
    255: required base.Base Base,
}

struct AppStorePkgStatusResp {
    1: required string status,
    255: required base.BaseResp BaseResp,
}

struct AppReleaseGroupReq {
    1: required i64 app_id,
    2: required string user_key,
    3: required string email
    255: required base.Base base,
}

struct AppReleaseGroupResp {
    1: required string result, //"success",""
    2: optional string group_id, //if success , return groupID
    255: required base.BaseResp BaseResp,
}

struct AddAppReleaseGroupReq {
    1: required i64 app_id,
    2: required string group_id,
    3: optional string platform,
    255: required base.Base Base,
}

struct AddAppReleaseGroupResp {
    255: required base.BaseResp BaseResp,
}

struct CommitFailRejectReq {
    1: required i64 ticket_id,
    2: required ReviewResult result,
    3: required string content,
    4: required string user_key,
    5: required string user_info,
    255: required base.Base Base,
}

struct CommitFailRejectResp {
    1: required string result
    255: required base.BaseResp BaseResp,
}

struct ReCommitReq {
    1: required i64 ticket_id,
    2: required string user_key,
    3: required string user_info,
    255: required base.Base Base,
}

struct ReCommitResp{
    1: required string result
    255: required base.BaseResp BaseResp,
}

enum CommitFailType {
	IAE_CPP_CONFLICT = 1
}

struct CommitFailMsgReq{
    1: required i64 ticket_id,
    255: required base.Base Base,
}

enum CommitFailEnum {
    TV_VERSION_EXIST = 1
}

struct CommitFailSolution {
    1: required string solution
    2: optional CommitFailEnum button_enum
}

struct CommitFailMsg {
    1: required string commit_fail_msg,
    2: optional list<CommitFailSolution> solutions,
}

struct CommitFailMsgResp{
    1: optional CommitFailMsg msg
    2: optional CommitFailType fail_type
    3: optional bool iae_cpp_deleted
    255: required base.BaseResp BaseResp,
}

struct IapInfo {
    1: required string adam_id, //条目唯一id
    2: required string reference_name, //副标题
    3: required string addon_type, //类型
    4: required string vendor_id, //产品ID
    5: required string iap_state, //状态
    6: required bool itc_submit_next_version, //是否勾选在当前版本中提审
}

struct IapAbstract {
    1: required string adam_id,
    2: required string reference_name,
    3: required string addon_type,
    4: required string vendor_id,
    5: required string iap_state,
    6: required i64 itc_submit_next_version,
}
struct IaeInfo {
    1: required string adam_id, //条目唯一id
    2: required string name, //标题
    3: required string short_desc, //类型
    4: required string publish_start, //产品ID
    5: required string iae_state, //状态
}

struct GetTicketIapListReq{
    1: required i64 ticket_id,
    255: required base.Base base,
}

struct GetTicketIapListResp {
    1: optional list<IapInfo> iap_list,
    255: required base.BaseResp BaseResp,
}

struct UploadReviewIapReq {
    1: required i64 ticket_id,
    2: required string user_key,
    3: required string user_info,
    4: optional list<IapInfo> chosen_ipas,
    255: required base.Base base,
}

struct UploadReviewIapResp {
   255: required base.BaseResp BaseResp,
}

struct UserInfo {
    1: required string user_key (go.tag="json:\"user_key\" query:\"user_key\" form:\"user_key\""),
    2: required string full_name (go.tag="json:\"full_name\" query:\"full_name\" form:\"full_name\""),
    3: required string email (go.tag="json:\"email\" query:\"email\" form:\"email\""),
}

struct AppInfoReq {
    1: optional string app_name,
    2: required i64 page_num,
    3: required i64 page_size,
    4: optional UserInfo user_info,
    255: required base.Base Base,
}

struct AppList {
    1: required string app_name,
    2: required i64 app_id,
    3: optional string logo,
    4: optional list<PermissionUser> role,
    5: required string platform,
    6: required bool permission,
}

struct AppInfoResp {
    1: optional list<AppList> app_list,
    2: required i64 total,
    255: required base.BaseResp BaseResp,
}

struct BaseInfoDraft {
    1: required bool secret,
    2: optional string code_name,
    3: optional string app_name,
    4: optional string appstore_name,
    5: required i64 app_id,
    6: required string bundle_id,
    7: required string team,
    8: required bool listed,
    9: required list<PermissionUser> role,
    10: required string platform,
    11: optional string group_id,
    12: optional string applestore_id, //苹果商店对应id
    13: optional string account,
    14: optional string password,
    15: optional i64 bits_id,
    16: optional string phase_release_default, // 分阶段发布默认选项
    17: optional string release_type_default, // 版本发布默认选项
    18: optional string reset_mark_default, // 重设评分默认选项
    19: optional string key_id
    20: optional string iss_id
    21: optional string p8file
}

struct RegulationDraft {
    1: optional i64 item_id,
    2: optional list<i64> process_type,
    3: optional string type
    4: optional string description
    5: optional string supplementary
}

struct UserCheckDraft {
    1: optional bool account_check,
    2: optional bool rocket_use,
    3: optional bool rocket_check,
}

enum SettingsStatus {
    UNREVIEW = 1 //未发起审核
    REVIEWING = 2 //审核中
    REVIEWING_PASS = 3 //审核通过
    REVIEWING_REFUSED = 4 //审核不通过
    REVIEWING_WITHDRAW = 5 //审核取消
    UNMATCH = 6 //记录的review_id未匹配上最新的review_id
    DELETED = 7 //变量已被删除
}

enum TCCStatus {
    UNSTART     = 1
    CHECKING    = 2
    SUCCESS     = 3
    FAIL        = 4
}

struct TicketSettingsConfig {
    1: required i64 ticket_id,
    2: required i64 item_id,
    3: required i64 var_id,
    4: required i64 review_id,
    5: required string config_name,
    6: required string variable_name,
    7: optional string platform_variable_value,
    8: optional string settings_variable_value,
    9: required SettingsStatus settings_status,
    10: required i64 flow,
    11: required string operator,
    12: required string description,
    13: required i64 var_type, // 标识该config是tcc还是settings
    14: required string initiator,
    15: required list<string> reviewers,
    16: optional i64 settings_var_type, // 标识该变量在settings平台的类型
    17: optional string deploy_template_name,
}

struct TicketTCCRecord {
    1: required i64 ticket_id,
    2: required i64 service_id,
    3: required Region region,
    4: required string value_key,
    5: required string value_type,
    6: required i64 flow,
    7: optional string tcc_value,
    8: optional string platform_value,
    9: required TCCStatus tcc_status,
    10: required string operator,
    11: required string reviewer,
    12: required i64 ID,
    13: required bool is_auto_change // 标识该字段是否为自动变更配置
}

struct GetTicketSettingsRecordReq {
    1: required i64 ticket_id,
    2: required string user_key,
    255: required base.Base base,
}

struct GetTicketSettingsRecordResp {
    1: optional list<TicketSettingsConfig> review_settings_list,
    2: optional list<TicketSettingsConfig> release_settings_list,
    255: required base.BaseResp BaseResp,
}

struct GetTicketTCCRecordReq {
    1: required i64 ticket_id,
    2: required string user_key,
    3: required string token,
    255: required base.Base base,
}

struct GetTicketTCCRecordResp {
    1: optional list<TicketTCCRecord> review_tcc_list,
    2: optional list<TicketTCCRecord> release_tcc_list,
    255: required base.BaseResp BaseResp,
}

struct SetTicketSettingsRecordReq {
    1: required i64 ticket_id,
    2: required list<TicketSettingsConfig> settings,
    3: required string user_key,
    255: required base.Base base,
}

struct SetTicketSettingsRecordResp {
    255: required base.BaseResp BaseResp,
}

struct SaveTicketTCCRecordReq {
    1: required i64 ticket_id,
    2: required list<TicketTCCRecord> tcc_list,
    3: required string token,
    4: required string user_key,
    255: required base.Base base,
}

struct SaveTicketTCCRecordResp {
    255: required base.BaseResp BaseResp,
}

struct ModifyTicketTCCRecordReq {
    1: required i64 ticket_id,
    2: required i64 flow
    3: required string token,
    4: required string user_key,
    5: optional list<i64> tcc_id,
    255: required base.Base base,
}

struct ModifyTicketTCCRecordResp {
    255: required base.BaseResp BaseResp,
}

struct ReleaseTicketSettingsConfigReq {
    1: required i64 ticket_id,
    2: required string value,
    3: required string user_key,
    255: required base.Base base,
}

struct ReleaseTicketSettingsConfigResp {
    255: required base.BaseResp BaseResp,
}

struct GetReleaseTicketSettingsValueReq {
    1: required i64 ticket_id,
    255: required base.Base base,
}

struct GetReleaseTicketSettingsValueResp {
    1: required string value,
    2: required bool editable,
    255: required base.BaseResp BaseResp,
}

struct AppSettingsConfig {
    1: required i64 app_id,
    2: optional i64 item_id,
    3: optional i64 var_id,
    4: optional string config_name, //配置名称
    5: optional string variable_name, //变量名称
    6: optional string initator //审核发起人邮箱
    7: optional list<string> reviewers //审核人邮箱
    8: required i64 usage_status, //状态,目前有打开和关闭两种
    9: optional i32 settings_var_type(go.tag="json:\"settings_var_type\""), // 在settings平台设置的变量类型
    10: optional i64 app_settings_deploy_template_id(go.tag="json:\"app_settings_deploy_template_id\"")
}

struct AppTCCConfig {
    1: required i64 app_id,
    2: optional Region region,
    3: optional i64 service_id,
    4: optional string value_key,
    5: optional string value_type,
    6: required i64 usage_status,
    7: optional i32 auto_change,
}

struct AppTccConfigIsValidReq {
    1: optional list<AppTCCConfig> app_tcc_config
    255: base.Base base
}

struct AppTccConfigIsValidResp {
	1: required bool is_valid
	2: required i64 service_id
	3: required string value_key
	255: base.BaseResp base_resp
}

struct AppStarlingConfig {
    1: required i64 app_id,
    2: required string platform,
    3: required string access_key,
    4: required string secret_key,
    5: required string project_id,
    6: optional string whats_new_name_space_id,
    7: optional string description_name_space_id,
    8: required i64 usage_status, //状态,目前有打开和关闭两种
}

struct RegularNotifyConf {
	1: required i32 usage_status,
    // 放量比例为多少时发送通知
    2: required i32 alert_rate,
    // 发送通知时是否需要同步暂停
    3: required i32 pause_when_alert,
}

// 苹果商店放量定量提醒配置项
struct AppStoreReleaseStateConfig {
	1: required i32 app_id,
	2: required string platform,
	3: optional list<RegularNotifyConf> config,
}

struct AppInfoSetReq {
    1: required i64 app_id,
    2: optional BaseInfoDraft base_info,
    3: optional list<RegulationDraft> regulation,
    4: optional UserCheckDraft user_check,
    5: optional list<AppSettingsConfig> app_settings_config,
    6: optional list<AppTCCConfig> app_tcc_config,
    7: optional string platform
    8: optional AppStarlingConfig app_starling_config
    9: optional AppStoreReleaseStateConfig app_release_notification_config,
    255: required base.Base Base,
}

struct AppConfigReq {
    1: optional list<AppSettingsConfig> app_settings_config,
    2: optional list<AppTCCConfig> app_tcc_config,
    3: required string user_key,
    4: required i64 app_id,
    5: required string token,
    6: required string platform,
    7: optional AppStarlingConfig app_starling_config,
    255: required base.Base Base,
}

struct AppConfigResp {
    255: required base.BaseResp BaseResp,
}

struct TicketReleaseCheckReq {
    1: required i64 app_id,
    2: required string app_version,
    3: optional i64 ticket_id,
    255: required base.Base Base,
}

struct TicketReleaseCheckResp {
    1: required i64 ret_code,
    2: required string msg,
    255: required base.BaseResp BaseResp,
}

struct AppInfoSetResp {
    255: required base.BaseResp BaseResp,
}

struct GetAppBaseInfoReq {
    1: required i64 app_id,
    2: required string platform
    255: required base.Base Base,
}

struct GetAppBaseInfoResp {
    1: required string app_name,
    2: required string bundle_id,
    3: required string platform,
    4: required bool in_effect,
    7: required string group_id,
    8: required string code_name,
    9: required string team,
    10: required string appstore_name,
    11: required bool secret,
    12: required list<PermissionUser> role,
    13: required bool listed,
    14: required i64 regulation,
    15: optional string appstore_id,
    16: optional string primary_language,
    17: optional string account,
    18: optional string password,
    19: required i64 bits_id,
    20: required i64 app_id
    21: optional string phase_release_default, // 分阶段发布默认选项
    22: optional string release_type_default, // 版本发布默认选项
    23: optional string reset_mark_default, // 重设评分默认选项
    24: optional string key_id
    25: optional string iss_id
    26: optional string p8file

    255: required base.BaseResp BaseResp,
}

struct UserCheckReq {
    1: required i64 app_id,
    2: required string operator,
    3: required UserCheckDraft user_check,
    4: required string platform,
    255: required base.Base Base,
}

struct UserCheckResp {
    255: required base.BaseResp BaseResp,
}

struct GetUserCheckReq {
    1: required i64 app_id,
    2: required string platform,
    255: required base.Base Base,
}

struct GetUserCheckResp {
    1: optional UserCheckDraft user_check,
    255: required base.BaseResp BaseResp,
}

struct GetAppConfigReq {
    1: required i64 app_id,
    2: required string platform,
    255: required base.Base Base,
}

struct GetAppConfigResp {
    1: optional list<AppSettingsConfig> app_settings_config,
    2: optional list<AppTCCConfig> app_tcc_config,
    3: optional AppStarlingConfig app_starling_config,
    255: required base.BaseResp BaseResp,
}

struct GetItemIDDetailReq {
    1: required list<i64> item_id_list,
    2: required i64 app_id,
    3: required string platform,
    255: required base.Base Base,
}

struct GetTCCKeyListReq {
    1: required i64 service_id,
    2: required Region region,
    3: required string token,
    4: optional i64 app_id,
    255: required base.Base Base,
}

struct GetTCCKeyListResp {
    1: required list<string> key_list,
    255: required base.BaseResp BaseResp,
}

struct GetTCCKeyDetailReq {
    1: required i64 service_id,
    2: required Region region,
    3: required string value_key,
    4: required string token,
    5: optional i64 app_id,
    255: required base.Base Base,
}

struct GetTCCKeyDetailResp {
    1: required string value_type,
    2: required string value,
    255: required base.BaseResp BaseResp,
}

struct SettingsVariable {
    1: required string variable_name,
    2: required i64 var_id,
    3: required i64 settings_var_type,
}

struct SettingsAdmin {
    1: required string uname,
}

struct ItemDetail {
    1: required string config_name,
    2: required i64 item_id,
    3: optional list<SettingsVariable> variable_list,
    4: optional list<SettingsAdmin> settings_admin,
    5: optional list<SettingsDeploy> settings_deploy_list,
}

struct GetItemIDDetailResp{
    1: required list<ItemDetail> item_detail_list,
    255: required base.BaseResp BaseResp,
}

struct BlockRepeatNotificationValue {
	1: required i64 ticket_id,
	2: required string user_email,
	3: required i32 block_duration,
	4: optional i64 notify_record_id,
}

struct AtBotEvent {
	1: required string app_id,
	2: required string chat_id,
	3: required string chat_type,
	4: required string employee_id,
	5: required bool is_mention,
	6: required string lark_version,
	7: required string message_id,
	8: required string msg_type,
	9: required string open_chat_id,
	10: required string open_id,
	11: required string open_message_id,
	12: required string parent_id,
	13: required string root_id,
	14: required string tenant_key,
	15: required string text,
	16: required string type,
	17: required string union_id,
	18: required string user,
	19: required string user_agent,
	20: required string user_open_id,
}

struct AtBotCallback {
	1: required string uuid,
	2: required string token,
	3: required string ts,
	4: required string type,
	5: required AtBotEvent event,
}

struct AtBotCallbackReq {
	1: required AtBotCallback bot_callback,
	255: required base.Base Base,
}

struct AtBotCallbackResp {
	255: required base.BaseResp BaseResp,
}

// 苹果商店放量相关按钮value
struct AppStorePhaseReleaseValue {
	1: required i64 app_id,
	2: required string app_name,
	3: required i64 ticket_id,
	4: required string app_version,
	5: required string appstore_version_id,
	6: required string release_type,
	7: required string group_id,
	8: required string url,
	9: required string platform,
}

// 约定：每个交互按钮的value都以新结构体来组织，然后以optional的形式作为
// 该结构体的字段。
// 每个交互按钮应该与CardButton中的一个常量对应。
struct ReviewLarkValue {
    1: required i64 ticket_id,
    2: required i64 diff_type,
    3: required i64 diff_old_status,
    4: required i64 diff_new_status,
    5: required string user_email,
    6: required i64 block_status,
    7: optional string button_action, // deprecated
    8: optional i64 app_id, // deprecated
    9: optional string appstore_version_id, // deprecated
    10: optional string app_name, // deprecated
    11: optional string app_version, // deprecated
    12: optional string release_type, // deprecated
    13: optional string group_id, // deprecated
    14: optional string url, // deprecated
    15: optional string platform, // deprecated
    16: optional CardButton button_type, // 作用与上面的button_action一样，都是判断按钮类型，上面的等到重构后就废弃了。
    // 屏蔽按钮回调value
    17: optional BlockRepeatNotificationValue block_repeat_notification_value,
    // Appstore release相关按钮回调value
    18: optional AppStorePhaseReleaseValue appstore_phase_release_value,
}

struct ReviewLarkAction {
    1: required ReviewLarkValue value,
    2: required string tag,
}

struct ReviewLarkCallback {
    1: required string open_id,
    2: required string user_id,
    3: required string open_message_id,
    4: required string tenant_key,
    5: required ReviewLarkAction action,
    6: optional string user_email,
}

struct ReviewLarkCallbackReq {
    1: required ReviewLarkCallback callback,
    255: required base.Base Base,
}

struct ReviewLarkCallbackResp {
    1: required string larkcontent,
    255: required base.BaseResp BaseResp,
}

// deprecated
struct PhaseReleaseInvokeBotCallBackV1Event {
	1: required string app_id,
	2: required string chat_id,
	3: required string chat_type,
	4: required string employee_id,
	5: required bool is_mention,
	6: required string lark_version,
	7: required string message_id,
	8: required string msg_type,
	9: required string open_chat_id,
	10: required string open_id,
	11: required string open_message_id,
	12: required string parent_id,
	13: required string root_id,
	14: required string tenant_key,
	15: required string text,
	16: required string type,
	17: required string union_id,
	18: required string user,
	19: required string user_agent,
	20: required string user_open_id,
}

struct OpenID2UserInfoReq {
	1: required string openID,
	255: required base.Base Base,
}

struct OpenID2UserInfoResp {
	1: required string avatar,
	2: required string name,
	3: required string email,
	4: required string user_id,
	5: required string open_id,
	6: required string message_id,
	7: required string chat_id,
	255: required base.BaseResp BaseResp,
}

struct SendEphemeralCardReq {
	1: required string groupID,
	2: required string openID,
	3: required string content,
	255: required base.Base Base,
}

struct SendEphemeralCardResp {
	255: required base.BaseResp BaseResp,
}

struct UpdateMaterialStatusReq {
    1: required i64 ticket_id,
    2: required MaterialStatus status,
    3: required string user_mail,
    255: required base.Base Base,
}

struct UpdateMaterialStatusResp {
    255: required base.BaseResp BaseResp,
}

struct UpdatePakcageStatusReq {
    1: required i64 ticket_id,
    2: required ITCPkgUploadStatus status,
    3: required string user_mail,
    255: required base.Base Base,
}

struct UpdatePackageStatusResp {
    255: required base.BaseResp BaseResp,
}

struct CheckTicketPkgUploadingStatusResp {
    1: optional list<i64> update_expired_ticket_id_list,
    255: required base.BaseResp BaseResp,
}

struct CheckTicketMaterialUploadingStatusResp {
    1: optional list<i64> download_expired_ticket_id_list,
    2: optional list<i64> update_expired_ticket_id_list,
    255: required base.BaseResp BaseResp,
}

struct ChangeTicketFlowReq {
    1: required i64 ticket_id,
    2: required string user_key,
    3: required string user_info,
    4: required i64 chosen_id,
    5: optional string remark,
    255: required base.Base Base,
}

struct ChangeTicketFlowResp {
    1: required string result,
    255: required base.BaseResp BaseResp,
}

struct EmergencyFlowReason {
    1: required i64 id,
    2: required string reason,
    3: optional string remarks,
    4: optional string operator,
    5: required string platform
}

struct EmergencyFlowReq {
    1: optional i64 ticket_id,
    255: required base.Base Base,
}

struct EmergencyFlowResp {
    1: optional list<EmergencyFlowReason> reason_list,
    2: optional EmergencyFlowReason ticket_reason,
    255: required base.BaseResp BaseResp,
}


struct QueryIapListReq {
    1: required i64 app_cloud_id,
    255: required base.Base Base,
}

struct SimpleIapList {
    1: required i64 app_cloud_id,
    2: required string update_time,
    3: required list<IapSimpleIapInfo> iap_list
}

struct QueryIapListResp {
    1: required SimpleIapList simpleIapList
    255: required base.BaseResp BaseResp,
}

struct IapSimpleIapInfo {
    1: required string adam_id,
    2: required string reference_name,
    3: required string addon_type,
    4: required string product_id,
    5: required string iap_state,
    6: optional i64 itc_submit_next_version,
}

struct RefreshIapListReq {
    255: required base.Base Base,
}

struct RefreshIapListResp {
    1: required string message,
    255: required base.BaseResp BaseResp,
}

struct RefreshAndQueryIapListReq {
    1: required i64 app_cloud_id,
    255: required base.Base Base,
}

struct RefreshAndQueryIapListResp {
    1: required SimpleIapList simpleIapList,
    255: required base.BaseResp BaseResp,
}

struct QueryIapDetailedReq {
    1: required string adam_id,
    255: required base.Base Base,
}

struct QueryIapDetailedFromTicketReq {
    1: required string adam_id,
    2: required i64 app_cloud_id,
    255: required base.Base Base,
}

struct IapDetailed {
    1: required string public_id,
    2: required string reference_name,
    3: required string addon_type,
    4: required string adam_id,
    5: required string product_id,
    6: required string iap_state,
    7: required bool cleared_for_sale,
    8: optional list<PricingInterval> pricing_intervals,
    9: optional list<Version> versions,
    10: required string review_notes,
    11: required string review_screenshot,
    12: required string merch_image,
    13: required bool content_hosting,
    14: required bool family_shareable,
    15: required string pricing_duration_type,
    16: required string family_name,
    17: optional list<PricingInterval> subscriptions,
    18: optional list<IntroOffer> intro_offers,
    19: optional list<PromoOffer> promo_offers,
    20: optional list<CampaignOffer> campaign_offers,
}

struct QueryIapDetailedResp {
    1: required IapDetailed iapDetailed,
    255: required base.BaseResp BaseResp,
}

struct PricingInterval {
    1: required string tier_stem,
    2: required string price_tier_effective_date,
    3: required string price_tier_end_date,
    4: required string country,
}

struct Version {
    1: required string LocaleCode,
    2: required string Name,
    3: required string Description,
}

struct IntroOffer {
    1: required string tier_stem,
    2: required string start_date,
    3: required string end_date,
    4: required string country,
    5: required string duration_type,
    6: required i64 num_of_periods,
    7: required string offer_mode_type,
}

struct PromoOffer {
    1: required string tier_stem,
    2: required string country,
    3: required string duration_type,
    4: required i64 num_of_periods,
    5: required string offer_mode_type,
    6: required string reference_name,
    7: required string product_code,
}

struct CampaignOffer {
    1: required string reference_name,
    2: required string campaign_id,
    3: required list<PromoOffer> offer_pricings,
}

struct GetAppTeamReq {
    1: required string platform
    255: required base.Base Base,
}

struct GetGPAppPackageReq {
    1: required string team,
    255: required base.Base Base,
}

struct AppTeamsResp{
    1: required list<string> teams,
    255: required base.BaseResp BaseResp,
}

struct CreateLocalizationLanguageReq {
    1: required string lang,
    2: required i64 ticket_id,
    3: required string user_key,
    255: required base.Base Base,
}

struct CreateLocalizationLanguageResp {
    255: required base.BaseResp BaseResp,
}

struct DeleteLocalizationLanguageReq {
    1: required string lang,
    2: required i64 ticket_id,
    3: required string user_key,
    255: required base.Base Base,
}

struct DeleteLocalizationLanguageResp {
    255: required base.BaseResp BaseResp,
}

struct UpdateReviewTicketMainIDReq {
    1: required i64 ticket_id
    2: required i64 main_id
    255: required base.Base Base
}
struct UpdateReviewTicketMainIDResp{
    255: required base.BaseResp BaseResp
}

struct GetDetectVersionListReq {
    1: required i64 main_id
    255: required base.Base Base
}

struct GetDetectVersionListResp {
    1: required list<PkgInfo> pkg_list
    255: required base.BaseResp BaseResp
}

struct PkgInfo {
    1: required i64 sub_task_id
    2: required string min_version
    3: required string pkg_name
    4: required string md5
}

struct GetDetectBasicInfoReq {
    1: required i64 sub_task_id
    255: required base.Base Base
}

struct GetDetectBasicInfoResp {
    1: required i64 report_status
    2: required i64 binary_tool_status
    3: required i64 basic_tool_status
    4: required i64 used_and_undeclared
    5: required i64 unused_and_declared
    6: required i64 p0_count
    7: required i64 sensitive_word_count
    8: required i64 privacy_api_count
    255: required base.BaseResp BaseResp
}

struct EditIssueRemarkReq {
    1: required i64 id
    2: required string issue_remark
    255: required base.Base Base
}

struct EditIssueRemarkResp {
    255: required base.BaseResp BaseResp
}

/* 查询版本任务下单个包 针对单个命中规则的问题详情 */
struct GetReviewIssueDetailReq{
    1: required i64 sub_task_id
    2: required i64 rule_id
	255: base.Base Base
}

struct GetReviewIssueDetailResp{
}

struct DetectFailedInfo {
    1: required string msg
    2: required string main_version
    3: required string sub_version
    4: required string user_info
}

struct ReviewDetectFailedCallBackReq {
    1: required i64 main_id
    2: required DetectFailedInfo info
    255: base.Base Base
}

struct ReviewDetectFailedCallBackResp {
    255: base.BaseResp BaseResp
}

struct TicketIDReq {
    1: required i64 ticket_id (go.tag = "json:\"ticket_id\" query:\"ticket_id\""),
    2: optional UserInfo user_info,
    255: required base.Base Base
}

struct AppIDReq {
    1: required list<AppIdentity> app_identity,
    255: required base.Base Base
}

struct AppReviewVersionResp {
    1: required map<AppIdentity,list<string>> version_map            //在平台发过的版本map
    255: required base.BaseResp BaseResp
}

struct AppRoleListResp {
    1: required list<ReviewTicketUser> role_list,    //五种角色的人列表
    255: required base.BaseResp BaseResp
}

struct AppSecretResp {
    1: required bool isSecret,
    255: required base.BaseResp BaseResp
}

struct PreviewInfoInitialReq {
    1: required string primary_language,
    2: required list<string> languages
    3: required i64 ticket_id,
    4: required i64 app_id
    255: required base.Base Base
}

struct PreviewInfo {
    1: required string id,
    2: required string url,
    3: optional list<string> sensitive,
}

struct PreviewInfos {
    1: required list<PreviewInfo> videos,
    2: required list<PreviewInfo> screenshots,
    3: required bool inherited,
}

struct PreviewInfoDevices {
    1: required map<string,PreviewInfos> device_map,
    2: required bool main_language,
}

struct PreviewInfoInitialResp {
    1: required map<string,PreviewInfoDevices> locale_map,
    2: optional list<string> msg,
    255: required base.BaseResp BaseResp
}

struct RefreshPreviewInfoReq {
    1: required i64 ticket_id,
    2: required string locale,
    3: required string device,
    4: required string primary_language,
    255: required base.Base Base
}

struct RefreshPreviewInfoResp {
    1: required PreviewInfos previewInfos,
    255: required base.BaseResp BaseResp
}

struct UploadPreviewInfoReq {
    1: required i64 ticket_id,
    2: required string locale,
    3: required string device,
    4: required string url,
    5: required PreviewInfoType preview_type,
    6: required list<string> ids,
    7: required string primary_language
    8: optional UserInfo user_info,
    255: required base.Base Base
}

struct UploadPreviewInfoResp {
    1: required string id,
    255: required base.BaseResp BaseResp,
}

struct DeletePreviewInfoReq {
    1: required i64 ticket_id,
    2: required string locale,
    3: required string device,
    4: required string id,
    5: required PreviewInfoType preview_type,
    6: optional UserInfo user_info,
    255: required base.Base Base
}

struct DeletePreviewInfoResp {
    255: required base.BaseResp BaseResp
}

struct SwapPreviewInfoReq {
    1: required i64 ticket_id,
    2: required string locale,
    3: required string device,
    4: required list<string> ids,
    5: required PreviewInfoType preview_type,
    6: optional UserInfo user_info,
    255: required base.Base Base
}

struct SwapPreviewInfoResp {
    255: required base.BaseResp BaseResp
}

struct PreviewInfoCommitCheckReq {
    1: required i64 ticket_id,
    2: required string primary_language,
    3: required i64 app_id,
    4: optional string locale,
    5: optional string device
    6: optional list<string> preview_ids,
    7: optional list<string> screenshot_ids,
    255: required base.Base Base
}

struct PreviewInfoCommitCheckResp {
    255: required base.BaseResp BaseResp
}

struct LocalizationLanguageReq {
    1: required string locale,
    2: required i64 ticket_id,
    3: required string user_key,
    255: required base.Base Base,
}

struct LocalizationLanguageResp {
    255: required base.BaseResp BaseResp
}

struct DownloadAllAppInfoFromAppStoreResp {
    255: required base.BaseResp BaseResp
}

struct OpenApiPreviewInfoStabilityTestReq{
    1: required string bundle_id,
    2: required string team,
    255: required base.Base Base,
}

struct OpenApiPreviewInfoStabilityTestResp {
    1: required string msg,
    255: required base.BaseResp BaseResp
}

struct GetReviewTicketAndStatusReq {
    1: required i64 app_id,
    2: required string app_version,
    3: required string platform,
}

struct GetReviewTicketAndStatusResp {
    1: required i64 ticket_id,
    2: required StatusType status,
    255: required base.BaseResp BaseResp
}

struct UpdatePkgIDReq{
    1: required i64 ticket_id
    2: required i64 pkg_id
    255: required base.Base Base,
}

struct UpdatePkgIDResp {
    255: required base.BaseResp BaseResp
}

struct AppStoreP8FileReq {
    1: required string team,
    2: required string p8file,
    3: required string iss_id,
    4: required string key_id,
    255: required base.Base Base,
}

struct AppStoreP8FileResp {
    255: required base.BaseResp BaseResp
}

struct GpCredentialsJsonReq {
    1: required string package_name,
    2: required string credentials,
    3: required string team,
    4: required string cookies,
    5: required string developer_id,
    6: required string gp_app_id,
    255: required base.Base Base,
}

struct GpCredentialsJsonResp {
    255: required base.BaseResp BaseResp
}

struct GetRegulationPermissionReq{
    1: required i64 regulation_id
    255: required base.Base Base,
}

// ================ 添加应用规则所包含的检查项和流程
struct AddCheckItemInRegulationAndProcessReq {
    1: required i64 regulation_id,
    2: optional list<ProcessTypeArr> process_list,
    3: required string user_info,
    4: required string operator,
    255: required base.Base Base,
}

struct AddCheckItemInRegulationAndProcessResp {
    255: required base.BaseResp BaseResp
}

struct AppIdentity {
    1: required i64 app_id,
    2: required string platform,
    3: optional i64 bits_id,
}

//分页查询预审单列表接口
struct QueryTicketListReq {
    1: optional string title,
    2: optional list<AppIdentity> app_identity,
    3: optional list<StatusType> status,
    4: optional list<string> versions,
	14: required i64 page_num,
	15: required i64 page_size,
    255: required base.Base Base,
}

struct GpTicketPackageStatus {
    1: required GPUploadStatus submit_package_status,
    2: optional string submit_package_err_msg,
    3: required GPUploadStatus inhouse_share_package_status,
    4: optional string inhouse_share_package_err_msg,
}

struct GetGpTicketPackageStatusResp {
    1: optional GpTicketPackageStatus gp_package_status
    255: required base.BaseResp BaseResp
}

struct GPTIcketMetadataStatus {
    1: required GPUploadStatus metadata_status,
    2: optional string metadata_err_msg,
}

struct GetGPTicketMetadataStatusResp {
    1: optional GPTIcketMetadataStatus gp_metadata_status
    255: required base.BaseResp BaseResp
}

struct GPArtiactInfo {
    1: required i64 artifact_id,
    2: optional string artifact_version,
    3: optional string artifact_url,
    4: optional ArtifactType artifact_type,
    5: optional string artifact_md5,
    6: optional string artifact_name,
    7: optional i64 artifact_size,
    8: optional string download_url,
}

struct GetGPTicketDetailResp {
    1: required string preview_description
    2: required string feature,
    3: required double release_rating,
    4: optional GPArtiactInfo artifact_info
    5: optional string gp_url
    6: optional map<string,list<UpdateRecord>> update_columns
    7: required i64 app_id,
    255: required base.BaseResp BaseResp
}

struct NewGPReviewTicketReq {
    1: required string feature,
    2: required double release_rating,
    3: required GPArtiactInfo artifact_info
    4: optional string gp_url
    5: required UserInfo user_info,
    6: required GPIconStatus gp_review_status,
    7: required i64 app_id,
    8: required string version
    255: required base.Base Base,
}

struct NewGPReviewTicketResp {
    1: required i64 ticket_id
    2: optional TicketType ticket_type
    255: required base.BaseResp BaseResp
}

struct TerminateGPReviewTicketReq {
    1: required i64 app_id,
    2: required string version
    3: optional UserInfo user_info
    255: required base.Base Base,
}

struct TerminateGPReviewTicketResp {
    1: required i64 ticket_id,
    255: required base.BaseResp BaseResp,
}

struct GPTicketDetailReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional string preview_description,
    4: optional string feature,
    5: optional double release_rating,
    6: optional GPArtiactInfo artifact_info,
    7: optional UserInfo user_info,
    255: required base.Base Base,
}

struct GPTicketDetailResp {
    255: required base.BaseResp BaseResp
}

struct GPTicketReview {
    1: optional list<CheckItemInTicket> check_item_result_list,
    2: optional list<i64> kdb_id,
    3: optional string content,
    4: optional Tag tag,
    5: optional list<string> guideline (go.tag = "json:\"guideline\" form:\"guideline\""),
    6: optional list<string> reason (go.tag = "json:\"reason\" form:\"reason\""),
}

struct GetGPTicketReviewResp {
    1: optional GPTicketReview gp_review_result,
    255: required base.BaseResp BaseResp
}

struct GPTicketReviewReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional list<CheckItemsResult> check_item_result,
    4: optional GPReviewResult review_result,
    5: optional UserInfo user_info,
    6: optional Tag tag
    255: required base.Base Base,
}

struct GPTIcketReviewResp {
    255: required base.BaseResp BaseResp
}

struct GPTicketCommit {
    1: optional list<CheckItemInTicket> check_item_result_list,
}

struct GetGPTicketCommitResp {
    1: optional GPTicketCommit gp_commit_result,
    255: required base.BaseResp BaseResp
}

struct GPReviewResult {
    1: optional list<i64> kdb_id,
    2: required string content,
}

struct GPTicketCommitReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional list<CheckItemsResult> check_item_result,
    4: optional UserInfo user_info,
    5: optional string reject_reason,
    255: required base.Base Base,
}

struct GPTicketCommitResp {
    255: required base.BaseResp BaseResp
}

struct GetGPCommitErrMsgResp {
    1: required string commit_err_msg
    255: required base.BaseResp BaseResp
}

struct GPCommitFailReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional UserInfo user_info,
    4: optional string reject_reason,
    255: required base.Base Base,
}

struct GPCommitFailResp {
    255: required base.BaseResp BaseResp
}

struct ChangeGPTicketFlowReq {
    1: required i64 ticket_id,
    2: required i64 chosen_id,
    3: optional string remarks,
    4: optional UserInfo user_info
    255: required base.Base Base,
}

struct ChangeGPTicketFlowResp {
    255: required base.BaseResp BaseResp
}

struct GPRetryUploadPackageReq {
    1: required i64 ticket_id,
    2: required UserInfo user_info,
    3: optional bool is_inhouse_share,  // true: 重试上传内部分享包, false: 重试上传包
    255: required base.Base Base,
}

struct GPRetryUploadPackageResp {
    255: required base.BaseResp BaseResp,
}

struct GPRetryUploadMetadataReq {
    1: required i64 ticket_id,
    2: required UserInfo user_info,
    255: required base.Base Base,
}

struct GPRetryUploadMetadataResp {
    255: required base.BaseResp BaseResp,
}

struct HandleGPQueueTaskReq{
    1: required string task_type,
    2: required string extra,
    255: required base.Base Base,
}

struct HandleGPQueueTaskResp {
    255: required base.BaseResp BaseResp,
}

struct TaskQueuePutReq{
    1: required string task_type,
    2: required string queue_key,
    3: required string unique_id,
    4: required binary data,
    255: required base.Base Base,
}

struct TaskQueuePutResp {
    255: required base.BaseResp BaseResp,
}

struct TaskQueueFinishReq{
    1: required string task_type,
    2: required string queue_key,
    3: required string unique_id,
    255: required base.Base Base,
}

struct TaskQueueFinishResp {
    255: required base.BaseResp BaseResp,
}

struct GPReviewStatusPollingReq{
    255: required base.Base Base,
}

struct GPReviewStatusPollingResp {
    255: required base.BaseResp BaseResp,
}

struct RefreshManagerUserReq{
    1: required i64 id,
    255: required base.Base Base,
}

struct RefreshManagerUserResp {
    255: required base.BaseResp BaseResp,
}

struct GetArtifactListReq {
    1: required i64 cloud_app_id,
    2: required list<ArtifactType> artifact_types,
    3: required string version,
    4: optional string update_version,
    5: optional string channel
    6: required i64 page_num,
    7: required i64 page_size,
    255: required base.Base Base,
}

struct ArtifactSimpleInfo {
    1: required i64 artifact_id,
    2: required string channel,
    3: required string creator,
    4: required string update_version,
    5: required i64 build_time,
    6: required i64 size,
    7: optional string dsym_url,
    8: optional string linkmap_url,
    9: optional bool has_aab,
}

struct GetArtifactListResp {
    1: optional list<ArtifactSimpleInfo> artifact_list,
    2: required i64 total,
    255: required base.BaseResp BaseResp,
}

struct AppStoreReviewDetail {
    1: required string contact_first_name,
    2: required string contact_last_name,
    3: required string contact_phone,
    4: required string contact_email,
    5: required bool demo_account_required,
    6: optional string demo_account_name,
    7: optional string demo_account_password,
    8: required string notes,
}

struct AppStoreAutoRelease {
    1: required bool is_auto_release,
    2: optional i64 time_before,
}

struct AgeRatingAttributes {
    1:  optional AgeRatingType violenceCartoonOrFantasy,
    2:  optional AgeRatingType violenceRealistic,
    3:  optional AgeRatingType violenceRealisticProlongedGraphicOrSadistic,
    4:  optional AgeRatingType profanityOrCrudeHumor,
    5:  optional AgeRatingType matureOrSuggestiveThemes,
    6:  optional AgeRatingType horrorOrFearThemes,
    7:  optional AgeRatingType medicalOrTreatmentInformation,
    8:  optional AgeRatingType alcoholTobaccoOrDrugUseOrReferences,
    9:  optional AgeRatingType gamblingSimulated,
    10: optional AgeRatingType sexualContentOrNudity,
    11: optional AgeRatingType sexualContentGraphicAndNudity,
    12: optional AgeRatingType contests,
    13: required bool unrestrictedWebAccess,
    14: required bool gambling,
    15: required bool seventeenPlus,
    16: optional KidsAgeBand kidsAgeBand,  // 面向儿童
}

struct AppStoreAppInfoLocalization {
    1: required string name,
    2: required string subtitle,
    3: required string locale,
    4: required string privacy_policy_url,
    5: optional map<string, list<list<i32>>> sensitive,
}

struct AppStoreVersionLocalization {
    1: required string locale,
    2: required string keywords,
    3: optional string whats_new,//第一次上架没有更新说明
    4: required string app_store_description,
    5: required string marketing_url,
    6: required string promotional_text,
    7: required string support_url,
    8: optional map<string, list<list<i32>>> sensitive,
}

struct AppStoreAttachment {
    1: required string attachment_name,
    2: required string attachment_url,
    3: optional i64 inherit_ticket,
}

struct AppStoreMetadataReq {
    1: optional map<string,AppStoreVersionLocalization> version_localization,
    2: optional map<string,AppStoreAppInfoLocalization> app_info_localization,
    3: optional AppStoreAttachment attachment,
    4: optional AppStoreReviewDetail review_detail,
    5: optional AgeRatingAttributes age_rating,
    6: optional AppStoreAutoRelease auto_release,
    7: optional string copyright,
    8: optional bool is_phased_release,
    9: optional bool is_reset_rating,
    10: optional string primary_locale,
    11: optional list<IapAbstract> iap_list
    12: required i64 ticket_id,
    13: optional UserInfo user_info,
    14: required StateFlowType state_flow_type,
    15: required string preview_description,
    255: required base.Base Base,
}

struct AppStoreMetadataResp {
    255: required base.BaseResp BaseResp,
}

struct AppStorePackageReq {
    1: optional ArtifactSimpleInfo artifact_info,
    2: required string url,
    3: optional string dsym_url
    4: optional string linkmap_url,
    5: required i64 ticket_id,
    6: optional UserInfo user_info,
    7: required StateFlowType state_flow_type,
    8: optional bool sync_artifact,
    255: required base.Base Base,
}

struct AppStorePackageResp {
    255: required base.BaseResp BaseResp,
}

struct LocaleUpdateInfo {
    1: optional list<string> created,
    2: optional list<string> deleted,
}

struct UpdateColumn {
    1: required string old_value,
    2: required string new_value,
}

struct GetAppStoreMetadataResp {
    1: optional map<string,AppStoreVersionLocalization> version_localization,
    2: optional map<string,AppStoreAppInfoLocalization> app_info_localization,
    3: optional AppStoreAttachment attachment,
    4: optional AppStoreReviewDetail review_detail,
    5: optional AgeRatingAttributes age_rating,
    6: optional AppStoreAutoRelease auto_release,
    7: required string copyright,
    8: required bool is_phased_release,
    9: required bool is_reset_rating,
    10: required string primary_locale,
    11: required bool is_submitted,
    12: optional list<IapAbstract> iap_list,
    13: optional string preview_description,
    // 有更新的非多语言信息
    14: optional map<string,map<string,UpdateColumn>> update_column,
    // 有更新的多语言信息
    15: optional map<string,map<string,map<string,UpdateColumn>>> update_localization_column,
    // 语言的新增与删除
    16: optional LocaleUpdateInfo locale_update_info,
    //新视频/图片结构，
    17: optional map<string,PreviewInfoDevices> locale_map,
    //是否上架过
    18: required bool listed,
    // 有更新的图片/视频信息
    19: optional map<string,map<string,map<string,list<string>>>> update_preview_localization,
    // 敏感词数量
    20: optional i32 total_sensitive_words,
    255: required base.BaseResp BaseResp,
}

struct GetAppStorePackageResp {
    1: optional ArtifactSimpleInfo artifact_info,
    2: required string url,
    3: optional string dysm_url,
    4: optional string link_map_url,
    5: required bool is_submitted,
    255: required base.BaseResp BaseResp,
}

struct AppStoreTicketMetadataCheckReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional list<CheckItemsResult> check_item_result,
    4: optional UserInfo user_info,
    5: optional string reject_reason,
    255: required base.Base Base,
}

struct AppStoreTicketCheckItem {
    1: optional list<CheckItemInTicket> check_item_result_list,
}

struct GetAppStoreTicketMetadataCheckResp {
    1: optional AppStoreTicketCheckItem appstore_metadata_check_result,
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketMetadataCheckResp {
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketPackageCheckReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional list<CheckItemsResult> check_item_result,
    4: optional UserInfo user_info,
    5: optional string reject_reason,
    255: required base.Base Base,
}

struct AppStoreTicketPackageCheckResp {
    255: required base.BaseResp BaseResp
}

struct GetAppStoreTicketPackageCheckResp {
    1: optional AppStoreTicketCheckItem appstore_package_check_result,
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketReview {
    1: optional list<CheckItemInTicket> check_item_result_list,
    2: optional list<i64> kdb_id
    3: optional string content
}

struct GetAppStoreTicketReviewResp {
    1: optional AppStoreTicketReview appstore_review_result,
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketReviewReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional list<CheckItemsResult> check_item_result,
    4: optional GPReviewResult review_result,
    5: optional UserInfo user_info,
    6: optional string reject_reason,
    7: optional list<string> guideline,
    8: optional list<string> reason,
    9: optional list<string> iae,
    10: optional list<IAE> iae_list,
    255: required base.Base Base,
}

struct AppStoreTicketReviewResp {
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketCommit {
    1: optional list<CheckItemInTicket> check_item_result_list,
}

struct GetAppStoreTicketCommitResp {
    1: optional AppStoreTicketCommit appstore_commit_result,
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketCommitReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional list<CheckItemsResult> check_item_result,
    4: optional UserInfo user_info,
    5: optional string reject_reason,
    6: optional list<string> iae,
    7: optional list<IAE> iae_list,
    255: required base.Base Base,
}

struct AppStoreTicketCommitResp {
    255: required base.BaseResp BaseResp
}

struct AppStoreTicketWithdrawReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional UserInfo user_info,
    4: required string reject_reason,
}

struct AppStorePackageCheckSameVersionReq {
    1: required i64 ticket_id,
    2: required StateFlowType state_flow_type,
    3: optional UserInfo user_info,
    4: optional string reject_reason,
}
struct ReviewBinaryDetectReq {
    1: required i64 ticket_id
    2: required i64 app_id
    3: required i64 platform
    4: required i64 bits_id
    5: required string package_url
    6: optional string apk_aab
    7: optional string linkmap_url
    8: optional string dsym_url
    9: optional string line_number_mapping
    10: optional string mapping_addr
    11: optional string resguard_addr
    12: optional string user_info
    255: required base.Base Base,
}

struct ReviewBinaryDetectResp {
    1: required i64 pkg_id
    255: required base.BaseResp BaseResp,
}

// 给星云那边用的获取上架AppStore上架地区接口
struct GetAppStoreAppShelfTerritoriesReq {
	1: required i32 app_id,
	255: required base.Base Base,
}

struct GetAppStoreAppShelfTerritoriesResp {
	1: required list<string> shelf_territories,
	2: required bool available_in_new_territories,
	255: required base.BaseResp BaseResp,
}

struct GetAppShelfTerritoriesReq {
    1: required i64 app_id,
    2: required string platform,
    255: required base.Base Base,
}

struct GetAppShelfTerritoriesResp {
    1: required bool available_in_new_territories
    2: required list<string> shelf_territories,
    3: required bool register
    255: required base.BaseResp BaseResp
}

struct AppShelfTerritoriesReq {
    1: required i64 app_id,
    2: required string platform,
    3: required bool available_in_new_territories
    4: required list<string> shelf_territories,
    255: required base.Base Base,
}

struct AppShelfTerritoriesResp {
    255: required base.BaseResp BaseResp
}

struct ReviewArtifactToRelease {
    1: required i64 artifact_id,
    2: required string artifact_url,
    3: required string artifact_version,
}

// 如果有预审单不一定有包信息和放量信息,所以为optional
struct ReviewTicketToRelease {
    1: required i64 ticket_id,
    2: required string app_version_string,
    3: required StatusType ticket_status
    4: optional ReviewArtifactToRelease artifact_info,
    5: optional bool is_phased_release,
    6: required bool submit_artifact
}

struct GetReviewTicketByAidAndVersionReq {
    1: required i64 app_id,
    2: required string version,
    255: required base.Base Base,
}
// 如果定位不到则不会有ticket_info字段
struct GetReviewTicketByAidAndVersionResp {
    1: optional ReviewTicketToRelease ticket_info,
    255: required base.BaseResp BaseResp
}

struct AppStorePollingResp {
    1: required string msg_id,
    2: required i64 polling_time,
    3: required string app_store_status,
    4: required string app_store_version,
    5: required i64 app_id,
    6: required string app_name,
    7: required string bundle_id
    255: required base.BaseResp BaseResp
}

struct AppStoreRejectToken {
    1: required string URL,
    2: required string name,
    3: required string asset_token,
}

struct AppStoreRejectQCRejectReason {
    1: required string section,
    2: required string description,
}

struct AppStoreRejectMsg {
    1: required string from,
    2: required i64 date,
    3: required string body,
    4: required bool apple_msg,
    5: required bool has_objectionable_content,
    6: optional list<AppStoreRejectToken> tokens,
    7: optional list<AppStoreRejectQCRejectReason> qc_rejection_reasons,
}

struct AppStoreAppRejectInfo {
    1: required bool actice,
    2: required bool binary_rejected,
    3: required bool can_developer_add_note,
    4: required bool metadata_rejected,
    5: required string version,
    6: required list<AppStoreRejectMsg> messages
    7: required string rejection_id,
}

struct AppStoreMockPollingResultReq {
    1: required StatusType app_store_status,
    2: required string app_store_version,
    3: required bool legal_status_flow,
    4: required i64 ticket_id,
    5: optional i64 app_id,
    6: optional AppStoreAppRejectInfo app_reject_info,
    255: required base.Base Base,
}



struct StarlingNameSpaceInfo {
    1: required i64 id,
    2: required string name,
}

struct GetStarlingNameSpaceInfoReq {
    1: required string access_key,
    2: required string secret_key,
    3: required string project_id,
    255: required base.Base Base,
}

struct GetStarlingNameSpaceInfoResp {
    1: optional list<StarlingNameSpaceInfo> name_space_list,
    255: required base.BaseResp BaseResp
}

struct StarlingContent {
    1: required string content,
    2: required bool last_version,
}

struct GetStarlingContentReq {
    1: required i64 ticket_id,
    2: required list<string> locales,
    3: required string value_key,
    4: optional i64 app_id,
    5: optional TicketType ticket_type,
    255: required base.Base Base,
}

struct GetStarlingContentResp {
    1: optional map<string,map<string,StarlingContent>> starling_content_map
    2: optional string project_id,
    3: optional string whats_new_space_id,
    4: optional string description_space_id,
    255: required base.BaseResp BaseResp
}

struct MetadataUploadStatusReq {
    1: required i64 ticket_id,
    2: required MaterialStatus status,
    255: required base.Base Base,
}

struct PackageUploadStatusReq {
    1: required i64 ticket_id,
    2: required ITCPkgUploadStatus status,
    255: required base.Base Base,
}

struct EmptyResp {
    255: required base.BaseResp BaseResp
}

struct GetAppStoreRegulerNotificationReq {
	1: required i32 app_id,
	2: required string platform,
	255: base.Base Base,
}

struct GetAppStoreRegularNotificationResp {
	1: optional AppStoreReleaseStateConfig app_store_release_State_config,
	255: base.BaseResp BaseResp,
}

enum RegularNotificationUsageStatus {
	ENABLE_ALERT = 1,
	DISABLE_REGULAR_NOTIFICATION = 2,
}

enum RegularNotificationPauseWhenAlert {
	NEED_PAUSE = 1,
	DO_NOT_PAUSE = 2,
}

// 添加定量提醒配置
struct SetAppStoreRegulerNotificationReq {
	1: required i32 app_id,
	2: required string platform,
	// 定量提醒配置项
	3: optional list<RegularNotifyConf> config,
	255: base.Base Base,
}

struct SetAppStoreRegularNotificationResp {
	255: base.BaseResp BaseResp,
}

// 检查应用是否配置发版群
struct CheckAppGroupReq {
	1: required i32 app_id,
	2: required string platform,
	255: base.Base Base,
}

struct CheckAppGroupResp {
	1: required bool is_set,
	255: base.BaseResp BaseResp,
}

// 定量提醒轮询
struct PollAppStoreReleaseStateReq {
	255: base.Base Base,
}

struct PollAppStoreReleaseStateResp {
	1: required string log_id, // 返回log_id，方便轮询出问题的时候定位日志
	255: base.BaseResp BaseResp,
}

struct RecommitInAppStoreRejectReq {
    1: required i64 ticket_id,
    2: optional UserInfo user_info,
    3: required StateFlowType state_flow_type,
    255: required base.Base Base,
}

struct GetGPStatusReq {
    1: optional i64 ticket_id,
    2: optional i64 app_id,
    3: optional string platform,
    255: required base.Base Base,
}

enum GPTrack {
    alpha = 1
    beta = 2
    internal = 3
    production = 4
}

enum GPIconStatus {
    unsubmit  = -1
    release   = 1
    review    = 2
    reject    = 3
}

struct GPVersionList {
    1: optional string version,
    2: optional string version_code,
    3: optional GPTrack track,
    4: optional string review_state,
    5: optional string online_state,
    6: optional GPIconStatus gp_review_status
}

struct GetGPStatusResp {
    1: optional list<GPVersionList> version_list,
    255: required base.BaseResp BaseResp
}

struct GetGPInstallUserNumReq {
    1: required i64 app_id,
    2: required string platform,
    255: required base.Base Base,
}

struct GetGPInstallUserNumResp {
    1: required i64 installs_user_num
    255: required base.BaseResp BaseResp
}

struct GetGPActiveDeviceNumReq {
    1: required i64 app_id,
    2: required string platform,
    255: required base.Base Base,
}

struct GetGPActiveDeviceNumResp {
    1: required i64 product_active_user_num,
    2: required i64 beta_tester_num,
    255: required base.BaseResp BaseResp
}

struct SupportedDeviceChange {
    1: required SupportDevice device,
    2: required i64 previously_supported,
    3: required i64 newly_unsupported,
    4: required i64 newly_supported,
}

struct GetGPSupportDeviceChangesReq {
    1: required i64 artifact_id,
    2: required string version,
    3: required string version_code,
    4: required i64 app_id,
    5: required string platform,
    6: optional string track,
    255: required base.Base Base,
}

struct GetGPSupportDeviceChangesResp {
    1: optional UltramanGetSupportDeviceStatus status,
    2: optional list<SupportedDeviceChange> supported_device_changes,
    3: required string message,
    255: required base.BaseResp BaseResp
}

enum SensitiveWordType {
	TXT = 1, // 文字物料中的敏感词
	PICTURE = 2, // 图片物料中的敏感词
}

enum SensitiveWordStatus {
	NOT_START = 0
	NO_WORDS = 1
	CONTAINS_WORDS = 2
}

struct ReviewHistoryAppVersion {
	1: required string version
	2: required bool rejected // 该版本是否被拒
}

struct ReviewHistoryTimeLine {
	1: required string status_chinese // 预审单状态流转，中文形式
	2: required string time // 预审单流转到status的时间
	3: required string color // 状态显示在前端的颜色
	4: required i32 index // 在timeline数组中的索引
	5: required StatusType status // 预审单状态流转，枚举形式
}

struct ReviewHistoryItem {
	1: required string version
	2: optional list<ReviewHistoryTimeLine> time_line
	3: optional list<string> review_conclusions // 预审结论
	4: required bool rejected // 该版本是否被拒审过
	5: optional list<TicketRejectInformation> reject_list
	6: required i64 ticket_id
	7: required string ticket_type
}

struct VersionsAndReviewHistories {
	1: required list<ReviewHistoryAppVersion> versions
	2: required list<ReviewHistoryItem> review_histories
}

enum VersionInterval {
	MIDDLE
	AFTER
}

struct GetReviewHistoriesReq {
	1: required i64 app_id
	2: optional string version
	3: optional VersionInterval interval
	255: optional base.Base base
}

struct GetReviewHistoriesResp {
	1: optional VersionsAndReviewHistories versions_and_review_histories
	255: optional base.BaseResp base_resp
}

struct AppIDAndPlatformReq {
	1: required i64 app_id
	2: required string platform
}

enum SettingsVarType {
	INT = 0
	FLOAT = 1
	STRING = 2
	LIST = 3
	DICT = 4
}
struct GetTerminalTicketStatusMapResp {
    1: required map<StatusType,bool> terminal_status_map,
    255: optional base.BaseResp base_resp
}

struct GetReviewTicketByArtifactIDReq {
    1: required i64 artifact_id,
	255: optional base.Base base
}
// 给发布那用的简单的预审单信息
struct SimpleReviewTicket {
    1: required i64 id,
    2: required StatusType status,
    3: required string version
    4: required bool is_phased_release,
    5: required bool can_submit_artifact,
}

struct GetReviewTicketByArtifactIDResp {
    1: optional SimpleReviewTicket review_ticket,
    2: optional SimpleReviewTicket last_ticket,
    255: optional base.BaseResp base_resp
}

struct TicketFlowInfo {
    1: required TicketFlow flow,
    2: optional i64 reason_id,
    3: optional string remarks,
}

struct OpenAPICreateReviewAppStoreTicketReq {
    1: required i64 artifact_id,
    2: optional TicketFlowInfo flow_info,
    3: required bool auto_commit,
    4: required bool sync_artifact,
    5: required string email,
	255: optional base.Base base
}

struct OpenAPICreateReviewAppStoreTicketResp {
    1: required i64 id,
    255: optional base.BaseResp base_resp
}

struct SyncAppTerrResp {
    255: optional base.BaseResp base_resp
}

enum GlobalCheckitemType {
	REJECT_RISK
}

enum CurrTicketStage {
	REVIEW
	REJECT
}

struct GetRejectReasonMetaReq {
	1: required CurrTicketStage stage
	255: optional base.Base base
}

struct RejectReasonMeta {
	1: optional list<string> reject_type (go.tag = "json:\"reject_type\" form:\"reject_type\"")
	2: optional list<string> category (go.tag = "json:\"category\" form:\"category\"")
	3: optional list<string> guideline (go.tag = "json:\"guideline\" form:\"guideline\"")
	4: optional list<string> reason (go.tag = "json:\"reason\" form:\"reason\"")
	5: optional list<string> recommend (go.tag = "json:\"recommend\" form:\"recommend\"")
}

struct GetRejectReasonMetaResp {
	1: optional RejectReasonMeta meta
	255: optional base.BaseResp base_resp
}

struct SettingsDeploy {
	1: required string name
	2: required i64 template_id
}

struct GetIAEReq {
	1: required i64 app_id
	2: required string platform
	255: optional base.Base base
}

struct IAE {
	1: required string id
	2: required string name
	3: required string short_desc
	4: required string publish_start
	5: optional string status
}

struct GetIAEResp {
	1: optional list<IAE> iae
	2: optional base.BaseResp base_resp
}

struct CancelReviewSubmissionReq {
	1: required i64 ticket_id
	255: optional base.Base base
}

struct CancelReviewSubmissionResp {
	255: optional base.BaseResp base_resp
}

struct CancelReviewSubmissionItemReq {
    1: required i64 ticket_id
    2: required string item_id
    3: optional UserInfo user_info,

    255: optional base.Base base
}

struct CancelReviewSubmissionItemResp {
	255: optional base.BaseResp base_resp
}

struct ReviewSubmissionItem {
    1: required string item_id
    2: required string name
    3: required i8 type
    4: required string status
    5: optional string remove_url
    6: optional string view_url
}

struct GetReviewTicketSubmissionResp {
    1: required string title
    2: required i64 submitted_time
    3: required string submitted_by
    4: required list<ReviewSubmissionItem> submitted_list

    255: optional base.BaseResp base_resp
}

struct Ping {
	1: required i64 ticket_id (go.tag="json:\"ticket_id\" query:\"ticket_id\"")
	2: required string name (go.tag="json:\"name\" query:\"name\"")
	3: required i32 age (go.tag="json:\"age\" query:\"age\"")
	4: required i64 platform (go.tag="json:\"platform\" query:\"platform\"")
}

struct Pong {
	1: required i64 ticket_id
	2: required string name
	3: required i32 age
}

struct AppRegulation {
	1: required i64 item_id
	2: required string supplementary
	3: required i64 priority
	4: required string description
	5: required string check_item_first_heading
	6: required string check_item_second_heading
	7: required list<i64> process_type
	8: optional string type
	9: required bool is_global
}

struct UserCheck {
	1: optional bool rocket_check
	2: optional bool rocket_use
	3: optional bool account_check
}

struct GetAppInfoReq {}

struct GetAppInfoResp {
	1: optional GetAppBaseInfoResp base_info
	2: optional list<AppRegulation> regulation
	3: optional UserCheck user_check
	4: optional list<AppSettingsConfig> app_settings_config
	5: optional list<AppTCCConfig> app_tcc_config
	6: optional AppStarlingConfig app_starling_config
	7: optional AppStoreReleaseStateConfig app_release_notification_config
}

struct GetCheckItemListReq {
	1: required i32 ticket_id (go.tag="json:\"ticket_id\" query:\"ticket_id\"")
	2: required string remarker_type (go.tag="json:\"remarker_type\" query:\"remarker_type\"")
}

struct GetCheckItemInRegulationReq {
	1: required i32 regulation_id (go.tag="json:\"regulation_id\" query:\"regulation_id\"")
}

struct SetCheckItemReq {
	1: required i64 item_id (go.tag="json:\"item_id\" query:\"item_id\"")
	2: required string general_id (go.tag="json:\"general_id\" query:\"general_id\"")
	3: required string description (go.tag="json:\"description\" query:\"description\"")
	4: required string supplementary (go.tag="json:\"supplementary\" query:\"supplementary\"")
}

struct GetCheckItemReq {
	1: required string general_id (go.tag="json:\"general_id\" query:\"general_id\"")
}

struct DelCheckItemReq {
	1: required i64 item_id (go.tag="json:\"item_id\" query:\"item_id\"")
}

struct AdamIDAndAppCloudIDReq {
	1: required i64 app_cloud_id (go.tag="json:\"app_cloud_id\" query:\"app_cloud_id\"")
	2: required string adam_id (go.tag="json:\"adam_id\" query:\"adam_id\"")
}

struct PackageUrlReq {
	1: required string package_url(go.tag="json:\"package_url\" query:\"package_url\"")
}

struct TeamReq {
	1: required string team(go.tag="json:\"team\" query:\"team\"")
}

struct CreateITCDetectReq {
	1: required i64 bits_id  (go.tag="json:\"bits_id\" query:\"bits_id\"")
	2: required string package_url  (go.tag="json:\"package_url\" query:\"package_url\"")
	3: required string apk_aab  (go.tag="json:\"apk_aab\" query:\"apk_aab\"")
	4: required string link_map_url  (go.tag="json:\"link_map_url\" query:\"link_map_url\"")
	5: required string dsym_url  (go.tag="json:\"dsym_url\" query:\"dsym_url\"")
	6: required string line_number_mapping  (go.tag="json:\"line_number_mapping\" query:\"line_number_mapping\"")
	7: required string mapping_addr  (go.tag="json:\"mapping_addr\" query:\"mapping_addr\"")
	8: required string resguard_addr (go.tag="json:\"resguard_addr\" query:\"resguard_addr\"")
	9: required i64 ticket_id (go.tag="json:\"ticket_id\" query:\"ticket_id\"")
	10: required i64 app_id (go.tag="json:\"app_id\" query:\"app_id\"")
	11: required i64 platform (go.tag="json:\"platform\" query:\"platform\"")
}

struct RetryDetectReq {
	1: required i64 pkg_id (go.tag="json:\"pkg_id\" query:\"pkg_id\"")
}

struct Conclusion {
	1: required list<string>  guideline (go.tag="json:\"guideline\" query:\"guideline\"")
	2: required list<string> reject_type (go.tag="json:\"reject_type\" query:\"reject_type\"")
	3: required list<string> reject_category (go.tag="json:\"reject_category\" query:\"reject_category\"")
	4: required list<string> reason (go.tag="json:\"reason\" query:\"reason\"")
	5: required string solution (go.tag="json:\"solution\" query:\"solution\"")
	6: required string remark (go.tag="json:\"remark\" query:\"remark\"")
}

struct AddRejectInfoReq {
	1: required i64 ticket_id (go.tag="json:\"ticket_id\" query:\"ticket_id\"")
	2: required string operator (go.tag="json:\"operator\" query:\"operator\"")
	3: required Conclusion conclusion (go.tag="json:\"conclusion\" query:\"conclusion\"")
}

struct AppVersionReq {
	1: required string app_version (go.tag="json:\"app_version\" query:\"app_version\"")
}

struct GetUserOpenIDReq {
	1: required string name (go.tag="json:\"name\" query:\"name\"")
	2: required string user_name (go.tag="json:\"user_name\" query:\"user_name\"")
}

struct RefreshVisitorAuthForReviewReq {
    1: optional list<i64> bits_app_ids
    255: optional base.Base base
}

struct RefreshVisitorAuthForReviewResp {
    1: required list<i64> succeed_apps
    2: required list<i64> failed_apps
    255: optional base.BaseResp base_resp
}

struct InitAuthRolesForReviewReq {
    1: optional list<i64> bits_app_ids
    255: optional base.Base Base
}

struct InitAuthRolesForReviewResp {
    1: required list<i64> succeed_apps
    2: required list<i64> failed_apps
    255: optional base.BaseResp BaseResp
}`,

	"artifact.thrift": `include "../../base.thrift"
include "common.thrift"

namespace go ep.artifact.manager.artifact
namespace py ep.artifact.manager.artifact


/**
 * 制品类型
 */
enum ArtifactType {
    // android 100-199
    APK     = 1,              //android宿主
    PLUGIN  = 2,              //插件
    HOTFIX  = 3,              //热修
    IPA = 4,                  //ios宿主
    FLUTTER = 6,              //flutter
    DMG = 7,                  //mac pc dmg
    EXE = 8,                   //windows pc exe
    ZIP = 9,                     // zip tar.gz
    PKG = 10,              // mac pc pkg
    SETTINGS = 11,     // settings
}

/**
* 包解析状态
**/
enum PkgParsedStatus {
    UNPARSED      = 0, // 未解析
    PARSING       = 1, // 解析中
    PARSE_SUCCESS = 2, // 解析成功
    PARSE_FAIL    = 3, // 解析失败
    PARSE_RETRY_ONCE     = 4, // 等待第一次重试
    PARSE_RETRY_TWICE    = 5, // 等待第二次重试
    PARSE_RETRY_TRICE    = 6, // 等待第三次重试
}

//其他一些附属产物
enum AssetType {
    // for apk
    APK_MAPPING = 100,       //mapping
    APK_RESMAPPING = 101,    //resmapping
    APK_ROBUST = 102,        //robust
    APK_AAB = 103,           //aab子包
    APK_LINE_NUMBER_MAPPING = 104,    //line_number_mapping
    APK_FPSSTACK = 105,      //fpsstack
    APK_PATCH = 106,
    APK_APKS = 107,  // aab to apks

    // for ipa
	IPA_LINKMAP = 200        //linkmap
	IPA_DYSM  = 201          //dysm
	IPA_LINK_MAP_ZIP  = 202  //link_map_zip
    IPA_NO_LTO = 203         // 非LTO包
    IPA_NO_LTO_LINKMAP = 204        //no-lto-linkmap
    IPA_NO_LTO_DYSM  = 205          //no-lto-dysm
    IPA_NO_LTO_LINK_MAP_ZIP  = 206  //no-lto-link_map_zip
    IPA_PROFILE_LOCK      = 207
    IPA_PODFILE      = 208 // profile 锁死每个依赖的版本和获取方式

    // for plugin
    PLUGIN_MAPPING = 300
    PLUGIN_PUBLIC_XML = 301
    PLUGINS_JSON_FILE = 302

    // for mac
    MAC_PKG = 400

    SYM = 501
    CAFE_FILE = 502
    EXPORT = 503
    PODSPEC = 504

    // others
    LARK_UPDATE_PKG = 800
    LARK_MACOS_ARM = 801 // arch
    CAKE_KNIFE = 802,   // https://bytedance.feishu.cn/wiki/wikcnFtFC7LQ7Vel92UmDYt6k8c
    BITSKY_MONOREPO_CONFIG = 803,

	UNKNOWN   = 999          //未知产物
}


/**
 * 制品来源
 */
enum ArtifactProvider {
    DEVOPS  = 1,      //发布平台
    LOCAL   = 2,      //本地上传
    ROCKET  = 3,      //rocket
    PANGOLIN = 4,     //穿山甲
    CONY     = 5,     //cony
    LIVE_SDK = 6,     //直播中台
    GITLAB_PIPELINE = 7,     //gitlab pipeline
    NEST = 8,         //nest
    ALPHA = 9,        //内测服务
    GAME = 10,        //游戏业务
    LARK_DOC = 11,    //飞书文档
    BITS = 12,    //Bits MR
    TouTiao = 13,    // 头条
    SEAHORSE = 14,   // 海马平台
    META = 15,      //制品库平台上传
    CI = 16,         //CI编译打包同步
    TRON = 17,         //TRON平台打包接入
    OPEN_API = 18,     // OpenAPI上传
    TEST_RELEASE = 19,  // 测试发布单上传
    RELEASE_WORKFLOW = 20 //发布流水线
}


// artifact中tags的可取值，以后用下面Tags枚举
const string Tag_AbiFilter = "abi_filter"
const string Tag_SocPlatform = "soc_platform"
const string Tag_HasPluginCoverage = "has_plugin_coverage"

/**
 * Tags
 */
enum Tags {
    CUSTOM_LIVE_SDK_VERSION = 1,     //直播中台包版本
    ROCKET_PUBLISH_DESC = 2,         //Rocket平台发布行为描述
    abi_filter = 3,                  //插件abi位数
    soc_platform = 4,                //插件芯片平台
    has_plugin_coverage = 5,         //插件覆盖率标志
    IOS_PKG_QRCODE_LINK = 6,         //iOS包二维码下载链接
    IOS_RESIGN_URL = 7,              //iOS重签包下载链接
    RELEASE_NOTE = 8,                //更新日志，主要字节内测包使用
    workflow_id = 9,                 //流水线id，用于给saveu检测拼接url
    LARK_UPDATE_PKG_URL = 10,        //lark更新包下载链接
    CPU_ARCH = 11,                   //CPU架构信息
    OPENAPI_SERVICE_NAME = 12,       //制品库OpenAPI调用方
    OVERSEA_OUTER_LINK = 13,           //海外app对应的海外链接
    IOS_SIGN_JOB_INFO = 14,           // ios重签名任务
    framework = 15,   // 编写包所用框架 flutter/uni-app
    RAW_SETUP_TOS_URL = 16 // 包的可用下载链接，海外包在这里放置国内链接
}

enum Framework {
    Default  = 0
    Electron = 1
    Flutter = 2
}

/**
 * 流转状态
 * 需讨论，包是否有状态
 * 包是原子的东西，理论上没有状态
 * 任务才有状态
 */
enum Status {

}


/**
* 和代码库关联的属性
**/
struct GitInfo {
    1: optional string git_repo;
    2: required i64 project_id;
    3: optional string branch;
    4: optional string commit;
    5: optional string author;
}

enum BuildJobStatus {
    RUNNING = 1,
    FAILURE = 2,
    SUCCESS = 3,
}

/**
* 和包构建过程相关的属性
**/
struct BuildInfo {
    1: optional i64 job_id;
    2: optional string job_url;
    3: optional string log_url;
    4: optional map<string, string> build_envs;
    5: optional i64 start_time;
    6: optional i64 end_time;
    7: optional BuildJobStatus job_status;
}

/**
* 云构建任务
**/
struct JobInfo {
    1: optional i64 job_id;
    2: optional string job_url;
    5: optional i64 start_time;
    6: optional i64 end_time;
    7: optional BuildJobStatus job_status;
}

/**
* 其他附属产物
**/
struct Asset {
    1: required string name;
    2: required AssetType asset_type;
    3: required string md5;
    4: required i64 size;
    5: required string download_url;
}


/**
 * 包的结构
 */
struct Artifact {
    1: required i64 id;                         //唯一id
    2: required string name;                    //命名
    3: required i64 build_time;                 //打包时间
    4: optional i64 size;                       //包大小
    5: optional string md5;                     //md5加密结果
    6: optional string sha1;                    //sha1加密结果
    7: required string download_url;             //下载链接
    8: required ArtifactType artifact_type;     //类型
    9: required ArtifactProvider provider;      //提供方
    10: optional string creator;                //创建人
    11: optional string desc;                   //详情描述
    12: optional i64 bits_app_id;               //bits应用id
    13: required string app_name;               //应用名称
    14: required common.Platform platform;      //平台
    15: optional GitInfo gitInfo;               //代码属性
    16: optional BuildInfo buildInfo;           //构建属性
    17: optional string bundle_id;              //包名
    18: optional string inner_version;          //对内版本号
    19: optional string outer_version;          //对外版本号
    20: optional string update_version;         //update版本号
    21: required string channel;                //渠道
    22: optional ITCInfo itcInfo;               //预审信息
    23: optional SecurityInfo securityInfo;     //安全漏洞信息
    24: optional string uuid;                   //uuid
    25: required i64 cloud_app_id;              //cloud应用id
    26: optional list<Asset> assets;            //其他产物
    27: required common.ReleaseEnv release_env; //场景
    28: optional i64 provider_package_id;       //包提供方的包ID,便于追溯
    29: optional string outer_link;              //包的外链地址
    30: optional i64 parent_artifact_id;         //artifact母包ID，非0值代表是通过母包产出的子包
    31: optional map<string, string> tags;       //tags
    32: optional list<string> notified_users; // 打包后需要通知的用户
    33: optional string pkg_type;               // 包类型(跟包管理平台的包类型对齐)
    34: optional string version_name;           //对外版本号,带.
    35: optional string min_sdk_version;
    36: optional string target_sdk_version;
    37: optional bool has_distributed; // 是否外发过
    38: optional bool alpha_test_public;                   // 字节内测上是否公开展示
    39: optional AlphaShelfStatus alpha_shelf_status;           //是否在字节内测上上架
    40: optional string label_name;             // 包的标签，可以为空或者空字符串
    41: optional string inner_version_name;     //对内版本名称
    42: optional bool disable_status        // 1/0 是/否禁用
    43: optional string disable_reason         // 禁用信息
    44: optional i64 is_beta;            //是否是内测包
    45: optional list<string> labels;  // 包的标签，支持多标签
    46: optional PkgParsedStatus pkg_parsed_status; // 包解析状态
    47: optional string saveu_callback_url; //上传包后可以通过回调通知的地址
}

enum AlphaShelfStatus {
    Init,           // 初始未上架状态
    OnShelf,        // 已上架状态
    OffShelf,       // 已下架状态
}

/**
 * 权限危险等级
 */
enum RiskLevel {
    High,        //高危
    Moderate,    //中危
    Low          //低危
}

/**
* 权限分析
**/
struct PackagePermission {
    1: required string name;
    2: required RiskLevel RiskLevel;
    3: optional bool GP_Flag;
    4: optional bool Privacy_Flag;
}

/**
 * 预审数据
 */
struct ITCInfo {
    1: required list<PackagePermission> permissions; //权限列表
}

/**
 * 安全漏洞信息
 */
struct SecurityInfo {
    1: required list<PackagePermission> permissions; //权限列表
}

// 制品之间的关系
// source [type] target
// 例如: 母包和子包的关系
// source is 关系 of target
struct ArtifactRelation {
    1: required i64 source_artifact_id;
    2: required i64 target_artifact_id;
    3: required ArtifactRelationType relation_type;
}

//制品关系种类
enum ArtifactRelationType {
    Parent = 1
}

/**
* 包的事件
* 1. 开始事件
* 2. 结束事件
* 3. 确认事件
**/
struct Event {
    1: required string name;
    2: required i32 create_time;
    4: optional string desc;
    5: optional string detail_url;
}

/**
* 包的流转事务
**/
struct Transaction {
    1: required string name;
    2: required i64 start_time;
    3: required i64 end_time;
    4: optional string desc;
    5: optional string detail_url;
}

//查询包列表的过滤器
struct ArtifactFilter {
    1: required i64 cloud_app_id; //cloud应用id
    2: optional i64 version_code; //对内版本号
    3: optional string creator; //打包人
    4: optional string update_version_code; //小版本号
    5: optional string channel; //渠道
    6: optional common.ReleaseEnv release_env; //环境
    7: optional i64 provider_package_id; //provider_package_id
    8: optional i64 bits_app_id; //bits应用id
    9: optional string outer_version; //对外版本号
    10: optional list<ArtifactType> artifact_types; //包类型
    11: optional string platform; //平台 
    12: optional list<string> pkg_type; //包类型(跟包管理平台展示的包类型对齐)
    13: optional string version_name; //对外版本
    14: optional list<i64> distribute_statuses; // 外发状态, 0是没有外发过，1是有发外过
    15: optional list<ArtifactProvider> provider_list; //提供方列表
    16: optional list<string> update_version_range; //宿主版本范围，list限制两位，第一位是版本下限，第二位是版本上限
    17: optional i64 build_job_id; //打包任务id
}


struct NewArtifactReq {
    1: required Artifact artifact,
    255: base.Base Base,
}

struct NewArtifactResp {
    1: i64 ArtifactId,
    255: base.BaseResp BaseResp,
}


struct QueryArtifactReq {
    1: required i64 artifact_id,
    255: base.Base Base,
}

struct QueryArtifactResp {
    1: optional Artifact artifact,
    255: base.BaseResp BaseResp,
}

struct QueryArtifactsReq {
    1: required list<i64> artifact_ids,
    255: base.Base Base,
}

struct QueryArtifactsResp {
    1: optional list<Artifact> artifacts
    255: base.BaseResp BaseResp,
}

struct FilterArtifactsReq {
    1: required ArtifactFilter filter,
    2: required common.Pagination pagination,
    255: base.Base Base,
}

struct FilterArtifactsResp {
    1: required list<Artifact> artifacts,
    2: optional i64 total,                          //查询到的结果总数
    255: base.BaseResp BaseResp,
}
//上传appstore
struct UploadIpaToAppStoreReq {
    1: required i64 artifact_id,
    255: base.Base Base,
}

struct UploadIpaToAppStoreResp {
    255: base.BaseResp BaseResp,
}

//上传穿山甲
struct UploadArtifactToPangolinReq {
    1: required i64 parent_artifact_id,
    2: optional list<i64> sub_artifact_ids,
    255: base.Base Base,
}

struct UploadArtifactToPangolinResp {
    255: base.BaseResp BaseResp,
}

struct ParseArtifactReq {
    1: required string download_url,    //包下载url
    255: base.Base Base,
}

struct ParseArtifactResp {
    1: required string name,            //包名称
    2: required string bundle_id,       //包逻辑名
    3: required i64    size,            //包大小
    4: required string update_version,  //包小版本号
    5: required string outer_version,   //包大版本号
    6: required string download_url,    //包下载url
    255: base.BaseResp BaseResp,
}

struct UpdateArtifactAfterUnpackingReq {
    1: required i64 artifact_id,   
    255: base.Base Base,
}

struct UpdateArtifactAfterUnpackingResp {
    255: base.BaseResp BaseResp,
}

struct UpdateArtifactResignUrlReq {
    1: required i64 artifact_id,  
    2: required string resign_url 
    255: base.Base Base,
}

struct UpdateArtifactResignUrlResp {
    255: base.BaseResp BaseResp,
}

struct UpdateArtifactDescriptionReq {
    1: required i64 artifact_id,  
    2: required string description 
    255: base.Base Base,
}

struct UpdateArtifactDescriptionResp {
    255: base.BaseResp BaseResp,
}

struct GetArtifactResignUrlReq {
    1: required i64 artifact_id,  
    255: base.Base Base,
}

struct GetArtifactResignUrlResp {
    1: required string resign_url 
    255: base.BaseResp BaseResp,
}

struct SimpleArtifact {
    1: required i64 id;                         //唯一id
    2: required string name;                    //命名
    3: required i64 build_time;                 //打包时间
    4: required string channel;                 //渠道
    5: required string update_version;          //update版本号
}

struct UpdateArtifactLabelReq {
    1: required i64 providerPackageId;
    2: required string label_name;
    3: optional string md5;

    255: base.Base Base,
}

struct UpdateArtifactLabelResp {
    1: optional i64 artifact_id;

    255: base.BaseResp BaseResp,
}

struct DisableArtifactReq {
    1: required i64 artifact_id;
    2: optional string disable_reason,

    255: base.Base Base,
}
struct DisableArtifactResp {
    255: base.BaseResp BaseResp,
}

struct UndisableArtifactReq {
    1: required i64 artifact_id;

    255: base.Base Base,
}
struct UndisableArtifactResp {
    255: base.BaseResp BaseResp,
}

struct GetArtifactHasAssociatedReq {
    1: required i64 artifact_id;

    255: base.Base Base,
}
struct GetArtifactHasAssociatedResp {
    1: required bool has_associated

    255: base.BaseResp BaseResp,
}
`,

	"common.thrift": `include "../../base.thrift"

namespace go ep.artifact.manager.common
namespace py ep.artifact.manager.common

//发布环境
enum ReleaseEnv {
    TEST = 1,      //需求测试
    GRAY = 2,      //灰度
    OFFICIAL = 3,  //正式
    RDGRAY = 4,    //独立技术灰度
    PRE_BUILD = 5, //预编译
    LR = 6,        //回归
    BETA = 7,      //beta
    INHOUSE = 8,   //内测
    BETA_TEST = 9, //众测
    LARK_INHOUSE = 10, //字节内测
}

/**
 * 平台类型
 */
enum Platform {
    ANDROID, //Android
    IOS, //ios
    MAC, //mac
    WINDOWS,//windows
    FLUTTER, //flutter
    LINUX, // linux
}

//分页组件
struct Pagination {
    1: required i64 page_index, //当前页码,从1开始
    2: required i64 page_size, //每页显示记录数
}

//更新类型
enum EditType {
    UPDATE, //update
    ADD,    //add
    DELETE  //delete
}

// CheckList 加急类型
enum PushType {
    none = 1, // 不加急
    lark = 2, // 仅应用内加急
    sms  = 3, // 应用内加急 + 短信加急
    call = 4, // 应用内加急 + 电话加急
}

/**
* lark action
**/
struct LarkAction {
    1: required map<string, string> value,
    2: required string tag,
}

/**
* lark callback
**/
struct LarkCallback {
    1: required string open_id,
    2: required string user_id,
    3: required string open_message_id,
    4: required string tenant_key,
    5: required LarkAction action,
}


struct HandleLarkCallbackReq {
    1: required LarkCallback callback,
    255: base.Base Base,
}

struct HandleLarkCallbackResp {
    1: required string larkcontent,
    255: base.BaseResp BaseResp,
}
`,

	"gp_release.thrift": `include "../../base.thrift"
include "artifact.thrift"
include "common.thrift"
include "release_rule.thrift"

namespace go ep.artifact.manager.gprelease
namespace py ep.artifact.manager.gprelease


enum GPUploadType {
    AAB         = 1,            //aab包
    APK         = 2,            //apk包
}

enum GPUploadStatus {
    Uploading   = 1,            //上传中
    Success     = 2,            //上传成功
    Fail        = 3,            //上传失败
    Pending     = 4,            //上传排队中
}

struct GPReleaseNoteDTO {
    1: required string              channel,
    2: required string              version_name,
    3: required string              version_code,
    4: required string              notes,
    5: required i64                 language_num,
    6: required string              status,
}

struct GPReleaseDTO {
    1: required i64                 app_id,
    2: required i64                 release_rule_id,
    3: required artifact.Artifact   artifact,
    4: required string              release_rate,
    5: required i64                 active_device_num,
    6: required i64                 target_install_num,
    7: required string              update_tip,
    8: required release_rule.ReleaseRuleStatus   status, //status
    9: required string              creator_email,
    10:required i64                 create_time,
}

struct GPInfoDTO {
    1: required i64                 app_id,
    2: required i64                 artifact_id,
    3: optional string              gp_app_id,
    4: optional i64                 gp_app_active_num;          // GP活跃用户数
    5: optional i64                 gp_upload_status;           // GP上传状态
    6: optional string              gp_upload_err_msg;          // GP上传失败原因
    7: optional i64                 gp_install_num;             // GP安装用户数
    8: optional string              gp_release_rate;            // GP产物已放量比例
    9: optional GPUploadType        gp_upload_type;             // GP上传类型
    10:optional string              gp_upload_channel;          // GP上传渠道
    11:optional GPUploadType        gp_internal_sharing_upload_type;// GP分享上传类型
    12:optional i64                 gp_internal_sharing_upload_status;// GP分享上传状态
    13:optional string              gp_internal_sharing_upload_err_msg;// GP分享上传失败原因
    14:optional string              gp_internal_sharing_url;    // GP分享链接
}

struct AddGPAppConfigReq {
    1: required i64                 app_id,
    2: required i64                 meta_app_id,
    3: required string              gp_app_id,
    4: required string              account,
    5: required string              password,
    6: required string              team,
    7: required string              credentials,
    8: required string              user_email,
    255: base.Base                  Base,
}

struct AddGPAppConfigResp {
    1: required string              gp_app_id,
    255: base.BaseResp              BaseResp,
}

struct GetGPReleaseNoteReq {
    1: required i64                 app_id (go.tag = "json:\"app_id\" form:\"app_id\""),
    2: required string              channel (go.tag = "json:\"channel\" form:\"channel\""),
    3: required i64                 meta_app_id (go.tag = "json:\"meta_app_id\" form:\"meta_app_id\""),
    255: base.Base                  Base,
}

struct GetGPReleaseNoteResp {
    1: required i64                 app_id,
    2: required string              channel,
    3: required list<GPReleaseNoteDTO>  releaseNotes,
    255: base.BaseResp              BaseResp,
}

struct UploadGPReq {
    1: required i64                 app_id,
    2: required i64                 artifact_id,
    3: required GPUploadType        upload_type,
    4: required string              channel,
    5: required bool                is_internal_sharing_upload,
    6: required i64                 meta_app_id,
    7: optional string              operator,
    255: base.Base                  Base,
}

struct UploadGPResp {
    255: base.BaseResp              BaseResp,
}

struct UploadGPCallbackReq {
    1: required string              extra (go.tag = "json:\"extra\" form:\"extra\""),
    2: required i64                 status (go.tag = "json:\"status\" form:\"status\""),
    3: required string              error_msg (go.tag = "json:\"error_msg\" form:\"error_msg\""),
    4: required string              internal_sharing_url (go.tag = "json:\"internal_sharing_url\" form:\"internal_sharing_url\""),
    255: base.Base                  Base,
}

struct UploadGPCallbackResp {
    255: base.BaseResp              BaseResp,
}

struct CreateGPReleaseReq {
    1: required i64                 app_id,
    2: required i64                 artifact_id,
    3: required string              release_rate,
    4: required string              update_tip,
    5: required string              env,
    6: required i64                 meta_app_id,
    7: optional i64                 integration_id,
    8: optional i64                 gray_id,
    9: optional list<release_rule.SlardarCfg> slardar_cfg,
    10: optional i64                gray_type = 0, // 0是普通灰度,1是独立灰度
    255: base.Base                  Base,
}

struct CreateGPReleaseResp {
    1: optional GPReleaseDTO        release,
    255: base.BaseResp              BaseResp,
}

struct GetGPReleaseReq {
    1: required i64                 app_id (go.tag = "json:\"app_id\" form:\"app_id\""),
    2: required i64                 release_rule_id (go.tag = "json:\"release_rule_id\" form:\"release_rule_id\""),
    3: required i64                 meta_app_id (go.tag = "json:\"meta_app_id\" form:\"meta_app_id\""),
    255: base.Base                  Base,
}

struct GetGPReleaseResp {
    1: optional GPReleaseDTO        release,
    255: base.BaseResp              BaseResp,
}

struct MockArtifactReq {
    1: required i64                 app_id, //云appId
    2: required i64                 origin_artifact_id, //原产物id
    3: required string              aab_download_url, //aab下载链接
    4: required string              apk_download_url, //apk下载链接
    5: required string              channel, //渠道
    6: required i64                 meta_app_id,
    255: base.Base                  Base,
}

struct MockArtifactResp {
    1: required i64                 artifact_id,
    255: base.BaseResp              BaseResp,
}

struct GetGPAppConfigReq {
    1: required i64                 app_id (go.tag = "json:\"app_id\" form:\"app_id\""),
    2: required i64                 meta_app_id (go.tag = "json:\"meta_app_id\" form:\"meta_app_id\""),
    255: base.Base                  Base,
}

struct GetGPAppConfigResp {
    1: required i64                 app_id,
    2: required string              gp_app_id,
    255: base.BaseResp              BaseResp,
}

struct GetGPInfoReq {
    1: required i64                 app_id (go.tag = "json:\"app_id\" form:\"app_id\""),
    2: required i64                 artifact_id (go.tag = "json:\"artifact_id\" form:\"artifact_id\""),
    3: required i64                 meta_app_id (go.tag = "json:\"meta_app_id\" form:\"meta_app_id\""),
    255: base.Base                  Base,
}

struct GetGPInfoResp {
    1: required GPInfoDTO           gpInfo,
    255: base.BaseResp              BaseResp,
}

struct ListGPChannelReq {
    1: required i64                 app_id (go.tag = "json:\"app_id\" form:\"app_id\""),
    2: required i64                 meta_app_id (go.tag = "json:\"meta_app_id\" form:\"meta_app_id\""),
    3: optional i64                 gray_type = 0 (go.tag = "json:\"gray_type\" form:\"gray_type\""),
    255: base.Base                  Base,
}

struct ListGPChannelResp {
    1: required list<string>        channel_list,
    255: base.BaseResp              BaseResp,
}

struct CronUpdateGPReleaseStatusReq {
    255: base.Base                  Base,
}

struct GetGPUploadLimitReq {
    1: required i64                 bits_app_id (go.tag = "json:\"bits_app_id\" form:\"bits_app_id\""),
    255: base.Base                  Base,
}

struct GetGPUploadLimitResp {
    1: required i64                 limit_num (go.tag = "json:\"limit_num\" form:\"limit_num\""),
    2: required i64                 upload_num (go.tag = "json:\"upload_num\" form:\"upload_num\""),
    3: required string              remain_time (go.tag = "json:\"remain_time\" form:\"remain_time\""),
    255: base.BaseResp              BaseResp,
}

struct ManualTriggerSerializerReq {
    1: required string              task_type (go.tag = "json:\"task_type\" form:\"task_type\""),
    2: required string              queue_key (go.tag = "json:\"queue_key\" form:\"queue_key\""),
    255: base.Base                  Base,
}

struct ManualTriggerSerializerResp {
    255: base.BaseResp              BaseResp,
}`,

	"release_rule.thrift": `include "../../base.thrift"
include "common.thrift"
include "slardar.thrift"

namespace go ep.artifact.manager.releaserule
namespace py ep.artifact.manager.releaserule
// ============================================  规则项的key  =========================================
// 注意：规则项的key尽量和settings参数保持一致，便于映射
// https://bytedance.feishu.cn/docs/doccnBZA7DtRxpKIFmqJHlarcSb
const  string  RuleItemKeyChannel           = "channel"                //渠道
const  string  RuleItemKeyDeviceID          = "device_id"              //设备id
const  string  RuleItemKeyVersionCode       = "version_code"           //版本号
const  string  RuleItemKeyUpdateVersionCode = "update_version_code"    //其他版本号
const  string  RuleItemKeyOSAPI             = "os_api"                 //os_api
const  string  RuleItemKeyIOSOSAPI          = "ios_os_api"             //IOS的os_api
const  string  RuleItemKeySN                = "sn"                     //设备序列号
const  string  RuleItemKeyCity              = "city"                   //城市
const  string  RuleItemKeyAbVersion         = "ab_version"             //ab_version
const  string  RuleItemKeyDeviceModel       = "device_model"           //机型
const  string  RuleItemKeyHostABI           = "host_abi"               //支持的cpu架构，32/64
const  string  RuleItemKeyPackageName       = "package_name"           //包名
const  string  RuleItemKeyTFMatchPolicy     = "tf_match_policy"        //tf优先

//配置项的类型
enum RuleItemValueType {
    INT = 1,       //int类型
    STRING = 2,    //string类型
}

// 规则项到类型的映射
const map <string, RuleItemValueType> RuleItemValueTypeMap = {
    // 产物属性

    // 规则
    RuleItemKeyChannel         : RuleItemValueType.STRING,
    RuleItemKeyDeviceID        : RuleItemValueType.INT,
    RuleItemKeyVersionCode     : RuleItemValueType.INT,
    RuleItemKeyUpdateVersionCode    : RuleItemValueType.INT,
    RuleItemKeyOSAPI           : RuleItemValueType.INT,
    RuleItemKeyIOSOSAPI        : RuleItemValueType.STRING,
    RuleItemKeySN              : RuleItemValueType.STRING,
    RuleItemKeyCity            : RuleItemValueType.STRING,
    RuleItemKeyAbVersion       : RuleItemValueType.INT,
    RuleItemKeyDeviceModel     : RuleItemValueType.STRING,
    RuleItemKeyHostABI         : RuleItemValueType.STRING,
    RuleItemKeyPackageName     : RuleItemValueType.STRING,
    RuleItemKeyTFMatchPolicy   : RuleItemValueType.STRING,
}

//针对一个配置项可添加的具体的计算逻辑
enum RuleItemFn {
    EQ     = 1,          //原值，equals
    NE  = 2,             //不等于,
    Range      = 3,      //需指定范围，[], boundary included
    In         = 4,      //白名单, in
    NotIn      = 5,      //黑名单, not_in
}

// 产物渠道，内测/灰度/正式
enum ArtifactChannelType {
    Test = 1, //内测
    Gray = 2, //灰度
    Official = 3, //正式
}

struct ArtifactMeta {
    1: required i64 cloud_app_id, #应用ID
    2: required i64 bits_app_id, #应用ID
    3: required i64 build_job_id,
    4: optional i64 artifact_id, #产物唯一ID
    5: optional i64 version_code,
    6: optional i64 update_version_code,
    7: optional i64 manifest_version_code, #对外版本号,
    8: optional string md5,
    9: optional string tf_download_url,   #tf下载链接
    10: optional i64 size,
    11: optional string cpu_abi, //cpu架构
    12: optional string download_url,
    13: optional string outer_version,
    14: optional string version_name,
    15: optional string channel,
    16: optional string platform,
    17: optional string name,
}

//配置项中某一个具体的配置类型，例如一个范围，或是一个集合
struct RuleItemUnit {
    1: required RuleItemFn fn,
    2: required string value, //实际的值
    3: required string ref,   //指向外部存储的id(支持多个)
}


//某一个具体的配置项
struct RuleItem {
    1: required string rule_item_key,
    2: required RuleItemValueType rule_item_value_type,
    3: required list<RuleItemUnit> rule_item_value,
    4: required map<string, string> extras, //一些额外的配置项参数，比如对于ab来说，需要ab_token
}

//slardar配置
struct SlardarCfg {
    1: required i64 rule_id, //id
    2: required string rule_name, //名称
}

//普通熔断配置
struct FuseTemplateCfg {
    1: required i64 rule_id, //id
    2: required string rule_name, //名称
    3: required slardar.FuseSource source,
}

//熔断配置
struct FuseCfg {
    1: optional list<SlardarCfg> slardar_cfg,
    2: optional string version_phase,
    3: optional i8 ios_remove_package,
    4: optional list<FuseTemplateCfg> fuse_template_cfg,
}

//适用于TF的灰度策略
struct TestFlightCfg {
    1: required i64 auto_delete_tf_user,       //是否自动删除tf用户
    2: required i64 deleted_tf_user_number,    //一次删除测试人员数目
    3: required string tf_group_id,            //分组id
    4: required i64 deleted_tf_user_max_times, //到达阈值后删除次数
    5: required i64 tf_user_max,               //测试人员阈值
}

// 命中规则后的升级策略
struct UpgradePolicy {
    1: required string popup_title, //弹窗标题
    2: required string upgrade_tips, //更新提示
    3: required string update_button_text, //升级按钮文字
    4: optional i64 pre_download, //预下载
    5: optional i64 force_upgrade, //强制升级
    6: optional i64 latency_seconds, //潜伏时间（秒)
    7: optional i64 interval_since_notify_update, //预下载最长等待时间（秒）
    8: optional i64 allow_popup, //是否弹窗
    9: optional i64 force_tips,  //新装三天内是否升级
}

//放量策略
struct ReleasePolicy {
    1: optional i64 release_max_no,        //最大放量个数
    2: optional i64 release_speed_hourly,  //每小时放量个数
    3: optional FuseCfg fuse_cfg,          //熔断配置
    4: optional i64 precise_release,       //精准放量
    5: optional TestFlightCfg test_flight_cfg, //TF控制策略，只针对ios
}

struct MatchPolicy {
    1: required list<RuleItem> rule_items,      // 规则项配置
}

//频控策略
enum FreqStrategyType {
    DEFAULT = 0,            // 默认
    FORCE_UPDATE = 1,       // 强制升级
    TEST_FLIGHT = 2,        // TF逻辑用户
    ALPHA_TEST_GUIDE = 3,   // 内测引导
    OLE_VERSION = 4,        // 老版本频控
    ALPHA_TEST = 5,         // 内测
    GRAY = 6,               // 灰度
    PROD = 7                // 正式
}

//频控策略状态
enum FreqStrategyStatus {
    PROCESSING = 0,         // 处理中
    PASSED = 1,             // 已通过
    EFFECT = 2,             // 已生效
    CANCELED = 3,           // 已取消
    REJECTED = 4,           // 已驳回
    DELETED = 5,            // 已删除
}

//频控审核操作类型
enum FreqReviewOpType {
    APPROVAL = 0,           // 通过审核
    REJECT = 1,             // 驳回审核
    CANCEL = 2,             // 取消审核
    SKIP = 3,               // 跳过审核
}

struct ReviewerConfItem {
	1: optional list<string> reviewers,
	2: optional bool require_all,
}

struct FreqReviewConfig {
    1: optional list<ReviewerConfItem> review_config_items,
}

// 时间频控
struct TimeFreqCfg {
    1: optional i64 time_duration,
    2: optional i64 time_max_popup_count,
}

// 版本频控
struct VersionFreqCfg {
    1: optional i64 version_range,
    2: optional i64 version_type,
    3: optional i64 version_max_popup_count,
}

// 新用户保护频控配置
struct NewUserProtectFreqCfg {
    1: optional i64 new_user_type,
    2: optional i64 new_user_duration,
}

// 免打扰频控配置
struct RejectFreqCfg {
    1: optional i64 reject_count,
    2: optional i64 reject_duration,
}

struct FreqStrategy {
    1: optional i64 id,

    2: optional FreqStrategyType strategy_type,

    3: optional bool only_wifi_popup,

    4: optional bool enable_time_control,
    5: optional TimeFreqCfg time_freq_cfg,

    6: optional bool enable_version_control,
    7: optional VersionFreqCfg version_freq_cfg,


    8: optional bool enable_new_user_protection,
    9: optional NewUserProtectFreqCfg new_user_protect_freq_cfg,

    10: optional bool enable_reject_control,
    11: optional RejectFreqCfg reject_freq_cfg,

    12: optional i64 old_version_duration,

    13: optional i64 tf_old_user_duration,
    14: optional string tf_title,
    15: optional string tf_content,

    16: optional FreqStrategyStatus status,
    17: optional bool enable,

}

struct FreqCfg {
    1: optional i64 id,
    2: optional i64 aid,
    3: optional common.Platform platform,
    4: optional list<FreqStrategy> strategies,
}

//单个发布单的状态
enum ReleaseRuleStatus {
    NEW = 0,        //新建，保留状态
    EFFECTED = 1,   //发布中
    SUSPENDED = 2,  //发布中断
    CANCELLED = 3,  //发布取消，不可逆
    FINISHED  = 4,  //发布完成
    REVIEW = 5,     //发布审核中
    REVIEW_REJECT = 6,     //发布拒绝
    REVIEW_APPROVE = 7,   // 发布通过
    EVALUATION = 8,   // 评估中
}

//某个发布版本的状态
enum VersionReleaseStatus {
    NONE = 0,       //未开始，没有一个发布单
    RUNNING = 1,    //进行中，有至少一个发布单没有结束
    DONE = 2,       //有发布单，并且全部结束
}

//详情
struct ReleaseRule {
    1: required i64 release_rule_id,                     // id
    2: required ReleaseRuleStatus release_rule_status, //status
    3: required ArtifactMeta artifact_meta,     // 产物元信息
    4: required MatchPolicy match_policy,       // 匹配策略
    5: optional ReleasePolicy release_policy,   // 控制策略
    6: required UpgradePolicy upgrade_policy,   // 升级策略
    7: required i64 real_release_no,            //(包的)实际放量
    8: required i64 created_at,
    9: required string creator,
    10: optional i64 release_version,      //灰度版本号
    11: optional i64 legacy_release_rule_id,  //老rocket对应的灰度或者正式规则
    12: optional i64 modified_update_version, //发布时修改的长版本号
    13: optional i64 bits_app_id,               // bits app_id
    14: optional i64 integration_id,            // bits integration id
    15: optional i64 gray_id,                   // bits 灰度轮次id
    16: optional i64 region,                    // 表明当前app是国内还是国外
    17: optional i64 gray_type = 0,                 // 0是普通灰度,1是独立灰度
    18: optional i64 release_type,
    101: optional i64 artifact_arch64_id,       // 64 位包 id
    102: optional string platform, //平台，android,iphone和ipad，小写，用于支持ipad设备
}

//用于列表页的简略信息展示
struct SimpleReleaseRule {
    1: required i64 release_rule_id,                     // id
    2: required ReleaseRuleStatus release_rule_status, //status
    3: required i64 artifact_id,     // 产物id
    4: required string artifact_name,
    5: required string artifact_channel,
    6: required i64 artifact_size,
    7: required i64 real_release_no,            //(包的)实际放量
    8: required i64 created_at,
    9: required string creator,
    10: optional string update_version_code,
    11: optional i64 release_no, //计划放量数
    12: optional string release_rate,   //放量比例
    13: optional i64 update_at,
    14: optional string gp_upload_channel,
    15: optional i64 release_type,
    16: optional map<string, string> artifact_tags, // 包的tag
    17: optional i64 remain_quota, //发布单保存后用来判断是否需要审批
    18: optional string version_name
}

//黑/白名单类型
enum SpecialListType {
    WHITELIST = 1, //白名单
    BLACKLIST = 2, //黑名单
}

// 黑/白名单
struct SpecialList {
    1: required i64 id,
    2: required i64 cloud_app_id,
    3: required string rule_item_key,
    4: required string name,
    5: required string value,
    6: required string description,
    7: required SpecialListType list_type,
    8: optional string operator,
    9: optional i64 use_file,
    10: optional string file_url,
}

struct CaseTestPlanExecutor {
    1: required string user_key,
    2: required string display_name,
    3: required string en_name,
}

//case用例信息
struct CaseTestPlanInfo {
    1: required i64 test_plan_id,
    2: required string test_plan_title,
    3: required i64 system_type,
    4: required string version,
    5: required i64 release_stage,
    6: required i64 rounds,
    7: optional i64 use_case_number,
    8: optional i64 executive_number,
    9: optional i64 state,
    10: optional list<CaseTestPlanExecutor> executor,
    11: optional i64 app_id,
}

// ============================================ service req & resp =========================================
struct AddReleaseRuleReq {
    1: required ReleaseRule rule,
    255: base.Base Base,
}

struct AddReleaseRuleResp {
    1: required i64 release_rule_id,
    2: required i64 gray_release_info_id,
    255: base.BaseResp BaseResp,
}

struct UpdateReleaseRuleReq {
    1: required ReleaseRule rule,
    2: required i64 release_rule_id,
    255: base.Base Base,
}

struct UpdateReleaseRuleResp {
    1: required i64 release_rule_id,
    2: required i64 gray_release_info_id,
    255: base.BaseResp BaseResp,
}

struct SaveReleaseRuleReq {
    1: required ReleaseRule rule,
    2: optional i64 release_rule_id = 0,
    255: base.Base Base,
}

struct SaveReleaseRuleResp {
    1: required i64 release_rule_id,
    2: required i64 gray_release_info_id,
    255: base.BaseResp BaseResp,
}

struct QueryReleaseRuleReq {
    1: required i64 release_rule_id,
    255: base.Base Base,
}

struct QueryReleaseRuleResp {
    1: optional ReleaseRule release_rule,
    255: base.BaseResp BaseResp,
}

struct QueryReleaseRuleDataReq {
    1: optional i64 gray_id = 0,
    2: optional i64 integration_id = 0,
    3: optional i64 gray_type = 0,
    255: base.Base Base,
}

struct ReleaseRuleStat {
    1: required i64 artifact_id,
    2: required i64 gray_id,
    3: required i64 integration_id,
    4: required bool run,
    5: required i64 plan_install_count,
    6: required i64 real_install_count,
}

struct QueryReleaseRuleDataResp {
    1: required list<ReleaseRuleStat> stat,
    255: base.BaseResp BaseResp,
}


struct CancelReleaseRuleReq {
    1: required i64 release_rule_id,
    255: base.Base Base,
}

struct CancelReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

struct SuspendReleaseRuleReq {
    1: required i64 release_rule_id,
    2: optional ReleaseRuleStatus target_status = ReleaseRuleStatus.SUSPENDED,
    255: base.Base Base,
}

struct SuspendReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

struct ResumeReleaseRuleReq {
    1: required i64 release_rule_id,
    255: base.Base Base,
}

struct ResumeReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

struct UpdateReleaseMaxNoReq {
    1: required i64 release_rule_id,
    2: required i64 release_max_no
    255: base.Base Base,
}

struct UpdateReleaseMaxNoResp {
    255: base.BaseResp BaseResp,
}

// 为飞书定制的相关 Release Rule 接口
struct AddNestReleaseRuleReq {
    1: required ReleaseRule rule,
    2: required string ReleaseEnv,
    3: required string Platform,
    255: base.Base Base,
}

struct AddNestReleaseRuleResp {
    1: required i64 ReleaseRuleId,
    255: base.BaseResp BaseResp,
}

struct GetNestReleaseRuleReq {
    1: required i64 Id,
    255: base.Base Base
}

struct GetNestReleaseRuleResp {
    1: optional ReleaseRule ReleaseRuleDO,
    255: base.BaseResp BaseResp,    
}

struct CancelNestReleaseRuleReq {
    1: required i64 ReleaseRuleId
    255: base.Base Base,
}

struct CancelNestReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

struct SuspendNestReleaseRuleReq {
    1: required i64 ReleaseRuleId
    2: optional ReleaseRuleStatus target_status = ReleaseRuleStatus.SUSPENDED,
    255: base.Base Base,
}

struct SuspendNestReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

struct ResumeNestReleaseRuleReq {
    1: required i64 ReleaseRuleId
    255: base.Base Base,
}

struct ResumeNestReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

// 异步返回 TF 上传状态
struct UploadITCPkgJenkinsCallbackReq {
    1: required string extra, //传给jenkins接口的信息，原封不动返回回来
    2: required string build_number,
    3: required string build_url,
    4: required string status,
    5: required string err_msg,
    255: base.Base Base,
}

struct UploadITCPkgJenkinsCallbackResp {
    255: optional base.BaseResp BaseResp,
}

struct UploadItcPkgReq {
    1: required i64 artifact_id,
    2: optional i64 meta_app_id,
    3: optional string tf_group_id,
    4: optional string tf_group_name,
    5: required string operator,
    6: required string type,
    255: base.Base Base,
}

struct UploadItcPkgResp {
    255: base.BaseResp BaseResp,
}

struct UploadTfReviewPkgBySourceReq {
    1: required i64 source_id,
    2: required string source,
    3: required string download_url,
    4: optional bool auto_tf_review
    255: base.Base Base,
}

struct UploadTfReviewPkgBySourceResp {
    1: required i64 tf_review_id,
    255: base.BaseResp BaseResp,
}

struct SetTFReviewFailureReq {
    1: required i64 tf_review_id,
    2: required string fail_reason,
    255: base.Base Base,
}

struct SetTFReviewFailureResp {
    255: base.BaseResp BaseResp,
}

// TF审核包状态
enum TFReviewPKGStatus {
    NOT_SUBMIT     = 0,
    SUBMITTED      = 1,
    SUBMIT_SUCCESS = 2,
    REVIEWED       = 3,
    FAILED         = 4,
    DONE           = 5,
}

struct UpdateTFReviewPKGStatusBySourceReq {
    1: required i64 source_id,
    2: required string source,
    3: required TFReviewPKGStatus tf_review_pkg_status,
    255: base.Base Base,
}

struct UpdateTFReviewPKGStatusBySourceResp {
    255: base.BaseResp BaseResp,
}

struct UploadItcGroupReq {
    1: required i64 artifact_id,
    2: required string tf_group_name,
    3: required string operator,
    4: optional string tf_group_id,
    5: optional string whats_new,
    6: optional bool auto_notify_enabled,
    255: base.Base Base,
}

struct UploadItcGroupResp {
    1: required GrayBuildTFGroupInfo tf_group,
    255: base.BaseResp BaseResp,
}

struct GetTfGroupsReq {
    1: required i64 artifact_id,
    255: base.Base Base,
}

struct TfGroupInfo {
    1: required string name,
    2: required string id,
    101: optional bool auto_delete_enabled,
    102: optional i64 auto_delete_threshold,
    103: optional i64 auto_delete_size,
    104: optional i64 group_priority,
    105: optional i64 tf_group_limit = 10000,
}

struct GetTfGroupsResp {
    1: required list<TfGroupInfo> groups,
    255: base.BaseResp BaseResp,
}

struct RollPollingTfGroupsReq {
    255: base.Base Base,
}

struct RollPollingTfGroupsResp {
    255: base.BaseResp BaseResp,
}

struct QueryItcPkgReviewStatusReq {
    255: base.Base Base,
}

struct QueryItcPkgReviewStatusResp {
    255: base.BaseResp BaseResp,
}

struct QueryItcPkgReviewStatusAppStoreReq {
    255: base.Base Base,
}

struct QueryItcPkgReviewStatusAppStoreResp {
    255: base.BaseResp BaseResp,
}

struct GetGrayBuildTFReviewStatusReq {
    1: required i64 artifact_id,
    255: base.Base Base,
}

struct GetGrayBuildTFReviewStatusResp {
    1: required string tf_review_status,
    2: required string fail_reason,
    3: required i64    tf_submit_status,
    255: base.BaseResp BaseResp,
}

struct GetGrayBuildTFGroupListReq {
    1: required i64 gray_id,
    2: required common.Pagination pagination,
    101: optional i64 bits_app_id,
    255: base.Base Base,
}

struct GrayBuildTFGroupInfo {
    1: required i64 id,
    2: required string tf_group_name,
    3: required string tf_review_status,
    4: required string tf_group_id,
    5: required string operator,
    6: required i64 created_at,
    7: required string tf_public_link,
    8: required i64 artifact_id,
    9: optional string fail_reason,
    10: optional bool auto_delete_enabled,
    11: optional i64 tf_tester_count,
    12: optional bool tf_public_link_enabled,
    13: optional string tf_build_id
    14: optional i64 auto_delete_threshold,
    15: optional i64 auto_delete_size,
    16: optional i64 group_priority,
    17: optional i64 tf_group_limit = 10000,
}

struct GetGrayBuildTFGroupListResp {
    1: required list<GrayBuildTFGroupInfo> tf_groups,
    2: required i64 total,
    255: base.BaseResp BaseResp,
}

struct GetGrayBuildTFPublicLinkReq {
    1: required i64 artifact_id,
    255: base.Base Base,
}

struct GetGrayBuildTFPublicLinkResp {
    1: required string tf_public_link
    2: optional string tf_review_status,
    3: optional string fail_reason,
    4: optional i64    tf_submit_status,
    255: base.BaseResp BaseResp,
}

struct GetTFPublicLinkAndStatusReq {
    1: required i64 artifact_id,
    2: required string source,
    255: base.Base Base,
}

struct GetTFPublicLinkAndStatusResp {
    1: required string tf_public_link
    2: required string tf_review_status
    3: required string fail_reason
    255: base.BaseResp BaseResp,
}

struct ResetTFQueryTimesReq {
    255: base.Base Base,
}

struct ResetTFQueryTimesResp {
    255: base.BaseResp BaseResp,
}


//熔断
struct FuseReleaseRuleReq {
    1: required i64 cloud_app_id,
    2: required i64 release_rule_id,
    3: required i64 gray_release_info_id,
    255: base.Base Base,
}

struct FuseReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

struct TeaFuseCallbackReq{
    1: required string tea_callback,
    255: base.Base Base,
}

struct TeaFuseCallbackResp{
    255: base.BaseResp BaseResp,
}

//熔断策略
struct QueryFuseCfgTemplateReq {
    1: required i64 cloud_app_id,
    2: required string platform,
    3: optional string version_phase = "",
    255: base.Base Base,
}

struct QueryFuseCfgTemplateResp {
    1: required list<SlardarCfg> slardar_cfg,
    255: base.BaseResp BaseResp,
}

//白/黑名单
struct QuerySpecialListReq {
    1: required i64 cloud_app_id,
    2: required string key,
    3: required SpecialListType list_type,
    4: optional i64 bits_app_id,
    255: base.Base Base,
}

struct QuerySpecialListResp {
    1: required list<SpecialList> special_list,
    255: base.BaseResp BaseResp,
}

struct GetSpecialListDetailReq {
    1: required string rule_item_key (go.tag = "json:\"rule_item_key\" form:\"rule_item_key\""),
    2: required i64 list_id (go.tag = "json:\"list_id\" form:\"list_id\""),
    255: base.Base Base,
}

struct GetSpecialListDetailResp {
    1: required SpecialList special_list,
    255: base.BaseResp BaseResp,
}

struct QueryInnerWhiteListReq{
    1: required i64 aid,
    2: required common.Platform platform,
    255: base.Base Base,
}

struct QueryInnerWhiteListResp{
    1: required list<string> did_white_list_name,
    255: base.BaseResp BaseResp,
}

//发布历史
struct QueryReleaseHistoryReq {
    1: required i64 cloud_app_id,
    2: required string version_code,  //大版本
    3: required string update_version_code, //小版本
    4: required common.ReleaseEnv release_env, //环境
    5: required common.Platform platform, //平台
    6: optional list<string> artifact_version_list, //小版本列表
    7: optional list<i64> artifact_id_list, //产物ID列表
    8: optional i64 integration_id,
    9: optional i64 gray_id,

    127: required common.Pagination pagination,
    255: base.Base Base,
}

struct QueryReleaseHistoryResp {
    1: required list<SimpleReleaseRule> release_history,
    2: required i64 total,
    255: base.BaseResp BaseResp,
}

//新增分组(白/黑)名单
struct AddSpecialListReq {
    1: required i64 cloud_app_id,
    2: required string key,
    3: required string name,
    4: required SpecialListType list_type,
    5: required string value,
    6: optional string Description,
    7: required i64 use_file,
    8: optional string file_url,
    9: required string operator,
    10: optional i64 bits_app_id,
    11: optional bool is_grouping,
    255: base.Base Base,
}

struct AddSpecialListResp {
    1: required i64 special_list_id,
    2: optional i64 white_list_id,
    255: base.BaseResp BaseResp,
}

struct BatchUpdateSpecialListReq {
    1: required string name,
    2: required string value,
    101: optional i64 ttl = 0, // 存活时间
    255: base.Base Base,
}

struct BatchUpdateSpecialListResp {
    255: base.BaseResp BaseResp,
}

struct DeleteWhiteListReq {
    1: required string name,
    255: base.Base Base,
}

struct DeleteWhiteListResp {
    255: base.BaseResp BaseResp,
}

struct EditSpecialListReq {
    1: required i64 cloud_app_id,
    2: required string key,
    3: required string name,
    4: required SpecialListType list_type,
    5: required string value,
    6: required string description,
    7: required string operator,
    8: required i64 id,
    9: required i64 use_file,
    255: base.Base Base,
}

struct EditSpecialListResp {
    255: base.BaseResp BaseResp,
}

struct DeleteSpecialListReq {
    1: required i64 id,
    2: required i64 cloud_app_id,
    3: required string key,
    4: required string operator,
    255: base.BaseResp Base,
}

struct DeleteSpecialListResp {
    255: base.BaseResp BaseResp,
}

//结束发布任务
struct EndReleaseRuleReq {
    1: required i64 cloud_app_id,
    2: required i64 gray_release_info_id,
    255: base.Base Base,
}

struct EndReleaseRuleResp {
    255: base.BaseResp BaseResp,
}

//查询发布单的统计
struct QueryVersionReleaseStatusReq {
    1: required i64 cloud_app_id,
    2: required i64 version,
    3: required common.Platform platform,
    4: optional list<string> version_list,
    255: base.Base Base,
}

struct QueryVersionReleaseStatusResp {
    1: required VersionReleaseStatus release_status,
    255: base.BaseResp BaseResp,
}

//查询发布单的统计
struct QueryReleaseRulesByIntegrationReq {
    1: required i64 IntegrationID,
    2: required i64 BitsAppID,
    255: base.Base Base,
}

struct QueryReleaseRulesByIntegrationResp {
    1: list<SimpleReleaseRule> GrayReleaseRules,
    2: list<SimpleReleaseRule> OfficialReleaseRules,
    255: base.BaseResp BaseResp,
}

//正式包
struct AddOfficalReleaseRuleReq {
    1: required ReleaseRule rule,
    255: base.Base Base,
}

struct AddOfficalReleaseRuleResp {
    1: required i64 release_rule_id,
    2: required i64 app_version_id,
    255: base.BaseResp BaseResp,
}

//正式包
struct SaveOfficalReleaseRuleReq {
    1: required ReleaseRule rule,
    2: optional i64 release_rule_id = 0,
    255: base.Base Base,
}

struct SaveOfficalReleaseRuleResp {
    1: required i64 release_rule_id,
    2: required i64 app_version_id,
    255: base.BaseResp BaseResp,
}

// 频控配置
struct QueryFreqCfgReq {
    1: optional i64 aid,
    2: optional common.Platform platform,
    255: base.Base Base,
}

struct QueryFreqCfgResp {
    1: optional FreqCfg freq_cfg,
    255: base.BaseResp BaseResp,
}

struct SubmitSaveFreqStrategyReq {
    1: optional i64 aid,
    2: optional common.Platform platform,
    3: optional FreqStrategy freq_strategy,
    4: optional string modify_reason,
    5: optional string username,
    6: optional string review_config,
    7: optional i64 bits_app_id,
    255: base.Base Base,
}

struct SubmitSaveFreqStrategyResp {
    1: optional i64 strategy_id,
    255: base.BaseResp BaseResp,
}

struct ApplyFreqStrategyReq {
    1: optional i64 id,
    255: base.Base Base,
}

struct ApplyFreqStrategyResp {
    255: base.BaseResp BaseResp,
}

struct HandleFreqReviewReq {
    1: optional i64 id,
    2: optional string username,
    3: optional FreqReviewOpType op_type,
    255: base.Base Base,
}

struct HandleFreqReviewResp {
    255: base.BaseResp BaseResp,
}

struct SaveFreqReviewConfigReq {
    1: optional string username,
    2: optional i64 aid,
    3: optional common.Platform platform,
    4: optional FreqReviewConfig review_config,
    255: base.Base Base,
}

struct SaveFreqReviewConfigResp {
    1: optional string username,
    2: optional i64 review_config_id,
    255: base.BaseResp BaseResp,
}

struct GetFreqReviewConfigReq {
    1: optional i64 aid,
    2: optional common.Platform platform,
    255: base.Base Base,
}

struct GetFreqReviewConfigResp {
    1: optional FreqReviewConfig review_config,
    255: base.BaseResp BaseResp,
}

struct ChangeFreqReviewStatusReq {
    1: optional i64 strategyId,
    2: optional string operator,
    3: optional FreqStrategyStatus status,
    255: base.Base Base,
}

struct ChangeFreqReviewStatusResp {
    1: optional i64 strategy_id,
    255: base.BaseResp BaseResp,
}

struct GetFreqCfgResp {
    1: optional list<FreqCfg> freq_cfg_list,
    255: base.BaseResp BaseResp,
}

struct AddOrUpdateFreqCfgReq {
    1: required FreqCfg freq_cfg,
    255: base.Base Base,
}

struct AddOrUpdateFreqCfgResp {
    1: optional i64 id,
    255: base.BaseResp BaseResp,
}

struct GetFreqReviewInfoReq {
    1: optional i64 strategy_id,
    255: base.Base Base,
}


struct GetFreqReviewInfoResp {
    1: optional string domain,
    2: optional string key,
    3: optional string applicant,
    4: optional string reviewProcess,
    5: optional i32 reviewLayerDepth,
    255: base.BaseResp BaseResp,
}

struct GetRemainQuotaReq {
    1: required i64 bits_app_id (go.tag = "json:\"bits_app_id\" form:\"bits_app_id\"")
    2: required string update_version_code (go.tag = "json:\"update_version_code\" form:\"update_version_code\"")
    3: required i64 integration_id (go.tag = "json:\"integration_id\" form:\"integration_id\"")
    4: required string channel (go.tag = "json:\"channel\" form:\"channel\"")
    5: required string user_name (go.tag = "json:\"user_name\" form:\"user_name\"")
    255: base.Base Base,
}

struct GetRemainQuotaResp {
    1: required i64 remain_quota
    2: required i64 current_version_quota
    3: required string business_tag_name
    255: base.BaseResp BaseResp,
}

struct QueryTestReleaseRuleReq {
    1: required i64 bits_app_id (go.tag = "json:\"bits_app_id\" form:\"bits_app_id\""),
    2: required i64 page_index (go.tag = "json:\"page_index\" form:\"page_index\""),
    3: required i64 page_size (go.tag = "json:\"page_size\" form:\"page_size\""),
    255: base.Base Base,
}

struct QueryTestReleaseRuleResp {
    1: required list<SimpleReleaseRule> release_history,
    2: required i64 total,
    255: base.BaseResp BaseResp,
}

struct RefreshSpecialListCacheReq {
    1: required i64 bits_app_id (go.tag = "json:\"bits_app_id\" form:\"bits_app_id\""),
    2: required string list_name (go.tag = "json:\"list_name\" form:\"list_name\""),
    3: required string rule_item_key (go.tag = "json:\"rule_item_key\" form:\"rule_item_key\""),
    255: base.Base Base,
}

struct RefreshSpecialListCacheResp {
    1: required i64 list_id,
    255: base.BaseResp BaseResp,
}

struct AllCaseTestManagerReq {
    1: required i64 bits_app_id,
    2: required string version,
    3: required i64 release_stage,
    4: required i64 rounds,
    255: base.Base Base,
}

struct AllCaseTestManagerResp {
    1: required list<CaseTestPlanInfo>  case_test_plan_infos,
    255: base.BaseResp BaseResp,
}

struct GetCaseTestManagerReq {
    1: required i64 bits_app_id,
    2: required string version,
    3: required i64 release_stage,
    4: required i64 rounds,
    255: base.Base Base,
}

struct GetCaseTestManagerResp {
    1: required list<CaseTestPlanInfo> case_test_plan_infos,
    255: base.BaseResp BaseResp,
}

struct UpsertCaseTestManagerReq {
    1: required i64 bits_app_id,
    2: required string version,
    3: required i64 release_stage,
    4: required i64 rounds,
    5: required list<i64> test_plan_ids,
    255: base.Base Base,
}

struct UpsertCaseTestManagerResp {
    255: base.BaseResp BaseResp,
}

struct GetEveningReleasePermissionReq {
    1: required i64 release_rule_id,
    255: base.Base Base,
}

struct GetEveningReleasePermissionResp {
    1: required bool does_user_have_permission,
    255: base.BaseResp BaseResp,
}

struct EveningReleaseApplicationReq {
    1: required i64 release_rule_id,
    255: base.Base Base,
}

struct EveningReleaseApplicationResp {
    255: base.BaseResp BaseResp,
}

struct GrayReleaseMonitorReq{
    255: base.Base Base,
}

struct GrayReleaseMonitorResp{
    255: base.BaseResp BaseResp,
}

struct UpdateDiffPackageManuallyReq {
    1: required string new_url,
    2: required string old_url,
    3: required i64 app_id,
    255: base.BaseResp BaseResp,
}

struct UpdateDiffPackageManuallyResp {
    1: required i64 task_id,
    255: base.BaseResp BaseResp,
}

struct CronUpdateDiffPackageReq {
    1: required i64 app_id,
    255: base.BaseResp BaseResp,
}

struct CronUpdateDiffPackageResp {
    255: base.BaseResp BaseResp,
}

struct UpdateDiffTaskStatusReq {
    1: required i64 task_id,
    255: base.BaseResp BaseResp,
}

struct UpdateDiffTaskStatusResp {
    1: required bool updated,
    2: required string message,
    255: base.BaseResp BaseResp,
}`,

	"review_service.thrift": `include "appstore_review.thrift"
include "gp_release.thrift"
include "../../base.thrift"

namespace go bytedance.artifact.review

struct BaseReq {
    255: required base.Base Base,
}

service ReviewService {
    /********* 预审单 *********/
    //新建预审单
    appstore_review.AddReviewTicketResp AddReviewTicket(1: required appstore_review.AddReviewTicketReq req),
    //查询苹果后台是否存在可编辑版本且处于准备提交状态
    appstore_review.QueryInflightVersionRespV2 QueryInflightVersion(1: required appstore_review.QueryInflightVersionReq req),
    // 查询苹果后台某个版本的包的状态
    appstore_review.AppStorePkgStatusResp QueryAppStorePkgStatus(1: required appstore_review.AppStorePkgStatusReq req),
    // 查询物料同步状态
    appstore_review.UploadMaterialStatusResponse UploadMaterialStatus(1:required appstore_review.UploadMaterialStatusReq req)
    // 手动触发同步物料
    appstore_review.UploadMaterialResp UploadMaterial(1:required appstore_review.UploadMaterialReq req),
    //同步提审包
    appstore_review.UploadITCPkgResp UploadITCPkg(1: required appstore_review.UploadITCPkgReq req),
    //确认App Store的包和同步失败的包是同一个包
    appstore_review.CheckItcPkgResp CheckItcPkg(1: required appstore_review.CheckItcPkgReq req),
    //异步返回提审包上传状态 发布有接口调用,后续删除
    appstore_review.UploadITCPkgCallbackResp UploadITCPkgCallback(1: required appstore_review.UploadITCPkgCallbackReq req),
    //jenkins异步返回提审包上传状态
    appstore_review.UploadITCPkgJenkinsCallbackResp UploadITCPkgJenkinsCallback(1: required appstore_review.UploadITCPkgJenkinsCallbackReq req),
    //查看同步提审包完成情况
    appstore_review.GetITCPkgUploadResultResp GetITCPkgUploadResult(1: required appstore_review.GetITCPkgUploadResultReq req),
    //重试预审包重签名
    appstore_review.TriggerResignJenkinsJobResp TriggerResignJob(1: required appstore_review.TriggerResignJenkinsJobReq req),
    //得到预审包重签名结果
    appstore_review.GetResignJobResultResp GetResignJobResult(1: required appstore_review.GetResignJobResultReq req),
    //Jenkins重签名任务回调, 返回buildNumber
    appstore_review.BuildNumberCallbackResp BuildNumberCallback(1: required appstore_review.BuildNumberCallbackReq req),
    //Jenkins重签名任务回调, 返回重签名结果
    appstore_review.ResignJobCallbackResp ResignJobCallback(1: required appstore_review.ResignJobCallbackReq req),
    //获取提审包的详细信息(小版本号,名字）
    appstore_review.ArtifactInfoResp GetArtifactInfo(1: required appstore_review.ArtifactInfoReq req),
    // lark phase release @机器人回调处理
    appstore_review.AtBotCallbackResp AtBotCallback(1: required appstore_review.AtBotCallbackReq req),
    // lark card button回调方法
    appstore_review.ReviewLarkCallbackResp LarkCardButtonCallback(1: required appstore_review.ReviewLarkCallbackReq req),
    // 根据openID查询用户信息
    appstore_review.OpenID2UserInfoResp QueryUserByOpenID(1: required appstore_review.OpenID2UserInfoReq req),
    // 向指定群中的用户发送临时卡片
    appstore_review.SendEphemeralCardResp SendEphemeralCard(1: required appstore_review.SendEphemeralCardReq req),
    /* iap相关 待删除/重构 */
    //查询app的IAP列表信息
    appstore_review.QueryIapListResp QueryIapList(1: required appstore_review.QueryIapListReq req),
    //刷新并查询app的IAP列表信息
    appstore_review.RefreshAndQueryIapListResp RefreshAndQueryIapList(1: required appstore_review.RefreshAndQueryIapListReq req),
    //查询IAP的详细信息
    appstore_review.QueryIapDetailedResp QueryIapDetailed(1: required appstore_review.QueryIapDetailedReq req),
    //刷新并查询IAP的详细信息
    appstore_review.QueryIapDetailedResp RefreshAndQueryIapDetailed(1: required appstore_review.QueryIapDetailedReq req),
    //刷新所有app的IAPlist
    appstore_review.RefreshIapListResp RefreshIapList(1: required BaseReq req),
    //从预审单详情页面查询iap详情
    appstore_review.QueryIapDetailedResp QueryIapDetailedFromTicket(1: required appstore_review.QueryIapDetailedFromTicketReq req),

    /*  设置重构  */
    // 设置页面列表页
    appstore_review.AppInfoResp GetAppSummary(1: required appstore_review.AppInfoReq req),
    // 获取应用基本信息
    appstore_review.GetAppBaseInfoResp GetAppBaseInfo(1: required appstore_review.GetAppBaseInfoReq req),
    // 获取应用接入确认信息
    appstore_review.GetUserCheckResp GetUserCheck (1: required appstore_review.GetUserCheckReq req),
    // 应用接入确认
    appstore_review.UserCheckResp UserCheck (1: required appstore_review.UserCheckReq req),
    //获取应用settings配置
    appstore_review.GetAppConfigResp GetAppConfig (1: required appstore_review.GetAppConfigReq req),
    //获取item_id对应的配置
    appstore_review.GetItemIDDetailResp GetItemIDDetail (1: required appstore_review.GetItemIDDetailReq req),
    //获取tcc的keylist
    appstore_review.GetTCCKeyListResp GetTCCKeyList (1: required appstore_review.GetTCCKeyListReq req),
    //获取tcc的key的value和value type
    appstore_review.GetTCCKeyDetailResp GetTCCKeyDetail (1: required appstore_review.GetTCCKeyDetailReq req),
    //增加对应app的个性化配置
    appstore_review.AppConfigResp Config (1: required appstore_review.AppConfigReq req),
    // 校验用户选择的tcc是否合法
    appstore_review.AppTccConfigIsValidResp AppTccConfigIsValid(1: required appstore_review.AppTccConfigIsValidReq req),
    //获取ticket对应settings配置
    appstore_review.GetTicketSettingsRecordResp GetTicketSettingsRecord (1: required appstore_review.GetTicketSettingsRecordReq req),
    //获取ticket对应tcc配置
    appstore_review.GetTicketTCCRecordResp GetTicketTCCsRecord (1: required appstore_review.GetTicketTCCRecordReq req)
    //保存当前tcc变量修改记录
    appstore_review.SaveTicketTCCRecordResp SaveTicketTCCRecord (1: required appstore_review.SaveTicketTCCRecordReq req)
    //发起tcc修改变量的正式修改
    appstore_review.ModifyTicketTCCRecordResp ModifyTicketTCCRecord (1: required appstore_review.ModifyTicketTCCRecordReq req)
    //增加ticket对应settings配置
    appstore_review.SetTicketSettingsRecordResp SetTicketSettingsRecord(1: required appstore_review.SetTicketSettingsRecordReq req),
    //确认预审单是否能发版
    appstore_review.TicketReleaseCheckResp TicketReleaseCheck(1: required appstore_review.TicketReleaseCheckReq req),
    //获得release输入的版本号(如果有)
    appstore_review.GetReleaseTicketSettingsValueResp GetReleaseTicketSettingsValue (1: required appstore_review.GetReleaseTicketSettingsValueReq req),

    /********* 应用规则/检查项设置相关 ************/
    //查询当前所有应用规则
    appstore_review.QueryRegulationResp QueryAllRegulations(1: required BaseReq req),
    //添加应用规则
    appstore_review.QueryRegulationResp AddRegulation(1: required appstore_review.AddRegulationReq req),
    //获取全量应用信息
    appstore_review.QueryAppInfoListResp GetAppInfoList(1: required BaseReq req),
    //获取某个app的负责人(ticket_id)
    appstore_review.AppRoleResp GetAppRole(1:required appstore_review.AppRoleReq req),
    //获取某个app的负责人(app_id)
    appstore_review.AppRoleResp GetAppRoleByAppId(1:required appstore_review.GetAppRoleReq req),
    //查询应用规则所包含的检查项
    appstore_review.QueryCheckItemInRegulationResp GetCheckItemInRegulation(1: required appstore_review.SingleRegulationReq req),
    //添加检查项到应用规则
    appstore_review.QueryCheckItemInRegulationResp AddCheckItemInRegulation(1: required appstore_review.AddCheckItemInRegulationReq req),
    //添加/编辑检查项
    appstore_review.QueryCheckItemResp EditCheckItem(1: required appstore_review.EditCheckItemReq req),
    //分类查询检查项树结点
    appstore_review.QueryCheckItemResp QueryCheckItem(1: required appstore_review.QueryCheckItemReq req),
    //查询单条检查项并拿到结点
    appstore_review.QueryCheckItemResp QuerySingleCheckItem(1: required appstore_review.QueryCheckItemReq req),
    //删除检查项
    appstore_review.DeleteCheckItemResp DeleteCheckItem(1: required appstore_review.DeleteCheckItemReq req),
    //查询预审单所包含的检查项列表
    appstore_review.QueryCheckItemInTicketResp QueryCheckItemInTicket(1: required appstore_review.QueryCheckItemInTicketReq req),
    //给检查项添加备注
    appstore_review.AddCheckItemRemarksResp AddCheckItemRemarks(1: required appstore_review.AddCheckItemRemarksReq req),
    /*****************************/
    //管理员删除单子
    appstore_review.DeleteTicketResp DeleteTicket(1: required appstore_review.DeleteTicketReq req),
    //负责人删除单子
    appstore_review.DeleteTicketResp ManagerDeleteTicket(1: required appstore_review.DeleteTicketReq req),
    //app入驻预审流程平台
    appstore_review.AppSettleResp AppSettle(1: required appstore_review.AppSettleReq req),
    //获得平台发版群
    appstore_review.AppReleaseGroupResp AppReleaseGroup(1: required appstore_review.AppReleaseGroupReq req),
    //增加平台发版群
    appstore_review.AddAppReleaseGroupResp AddAppReleaseGroup(1: required appstore_review.AddAppReleaseGroupReq req),
    //改变rocket创建的预审单流程
    appstore_review.ChangeTicketFlowResp ChangeTicketFlow(1: required appstore_review.ChangeTicketFlowReq req),
    //获取紧急流程原因list/某预审单使用紧急流程原因
    appstore_review.EmergencyFlowResp GetEmergencyFlow(1: required appstore_review.EmergencyFlowReq req),
    //获取AppStore接入公司主体
    appstore_review.AppTeamsResp GetAppsTeam(1: required appstore_review.GetAppTeamReq req),
    //获取GP接入包主体
    appstore_review.AppTeamsResp GetAppPackageNames(1: required appstore_review.GetGPAppPackageReq req),
    //新增语言
    appstore_review.CreateLocalizationLanguageResp CreateLocalizationLanguage(1: required appstore_review.CreateLocalizationLanguageReq req),
    //删除语言
    appstore_review.DeleteLocalizationLanguageResp DeleteLocalizationLanguage(1: required appstore_review.DeleteLocalizationLanguageReq req),
    //从苹果后台及时加载视频/图片信息
    appstore_review.PreviewInfoInitialResp PreviewInfosInitial (1: required appstore_review.PreviewInfoInitialReq req),
    //刷新苹果后台某个语言下某个设备的图片/视频信息
    appstore_review.RefreshPreviewInfoResp RefreshPreviewInfo (1: required appstore_review.RefreshPreviewInfoReq req),
    //上传视频/图片信息
    appstore_review.UploadPreviewInfoResp UploadPreviewInfo(1: required appstore_review.UploadPreviewInfoReq req),
    //删除视频/图片
    appstore_review.DeletePreviewInfoResp DeletePreviewInfo(1: required appstore_review.DeletePreviewInfoReq req),
    //交换图片/视频位置
    appstore_review.SwapPreviewInfoResp SwapPreviewInfo(1: required appstore_review.SwapPreviewInfoReq req),
    //物料提交前校验
    appstore_review.PreviewInfoCommitCheckResp PreviewInfoCommitCheck(1: required appstore_review.PreviewInfoCommitCheckReq req),
    //创建语言并校验
    appstore_review.LocalizationLanguageResp CreateLocalizationLocale(1: required appstore_review.LocalizationLanguageReq req),
    //删除语言并校验
    appstore_review.LocalizationLanguageResp DeleteLocalizationLocale(1: required appstore_review.LocalizationLanguageReq req),
    //openAPI 视频图片上传接口稳定性自动化测试
    appstore_review.OpenApiPreviewInfoStabilityTestResp OpenApiPreviewInfoStabilityTest (1: required appstore_review.OpenApiPreviewInfoStabilityTestReq req),

    appstore_review.SendAppealLarkResp SendRepeatLark(1: required BaseReq req),
    /***拒审流程***/
    appstore_review.EmptyResp AddTicketRejectReasonAndSolution(1: required appstore_review.TicketRejectReasonAndSolutionReq req),

    appstore_review.TicketRejectInformationResp GetTicketRejectInformation(1: required appstore_review.TicketRejectInformationReq req),

    appstore_review.EmptyResp TicketWithdrawal(1: required appstore_review.TicketOptimizeReq req),

    appstore_review.EmptyResp TicketOptimize(1: required appstore_review.TicketOptimizeReq req),

    appstore_review.AppRoleResp GetUserRoleFromUserTable (1: required appstore_review.AppRoleReq  req),
    // 预审单申诉
    appstore_review.EmptyResp TicketAppeal(1: required appstore_review.TicketAppealReq req),

    appstore_review.RocketCreateTicketResp RocketCreateTicket(1: required appstore_review.RocketCreateTicketReq req),

    /*提审失败*/
    appstore_review.CommitFailMsgResp CommitFailMsg(1: required appstore_review.CommitFailMsgReq req),
    //查询review_ticket详情
    appstore_review.QueryReviewTicketResp QueryReviewTicket(1: required appstore_review.QueryReviewTicketReq req),
    //刷数据
    appstore_review.FlushDataResp FlushData(1: required appstore_review.FlushDataReq req),
    //查询用户负责的app
    appstore_review.GetAppManagedByUserResp GetAppManagedByUser(1: required appstore_review.GetAppManagedByUserReq req),

    //根据 app_id 查找某个app下所有预审单 app_version 的接口，以供筛选
    appstore_review.AppReviewVersionResp GetAppReviewVersionList(1: required appstore_review.AppIDReq req),
    //判定是否是保密产品
    appstore_review.AppSecretResp GetAppSecret(1: required appstore_review.TicketIDReq req)
    //根据三元组获取预审单
    appstore_review.GetReviewTicketAndStatusResp GetReviewTicketAndStatus(1: required appstore_review.GetReviewTicketAndStatusReq req),

    //更新检测pkgID
    appstore_review.UpdatePkgIDResp UpdatePkgID(1: required appstore_review.UpdatePkgIDReq req)
    //AppStore P8文件
    appstore_review.AppStoreP8FileResp AppStoreP8File(1: required appstore_review.AppStoreP8FileReq req),
    //Gp credentials 文件
    appstore_review.GpCredentialsJsonResp GpCredentialsJson(1: required appstore_review.GpCredentialsJsonReq req),
    //获取确认项的操作权限
    appstore_review.AppRoleResp GetAppRegulationPermission(1: required appstore_review.GetRegulationPermissionReq req),
    //添加应用规则对应的确认项和所属流程
    appstore_review.AddCheckItemInRegulationAndProcessResp AddCheckItemInRegulationAndProcess(1: required appstore_review.AddCheckItemInRegulationAndProcessReq req),
    //新预审单列表页
    appstore_review.QueryReviewTicketListResp QueryTicketList(1: required appstore_review.QueryTicketListReq req),
    //创建gp预审单
    appstore_review.AddReviewTicketResp NewGpTicket(1: required appstore_review.AddReviewTicketReq req),
    //gp预审单和操作记录信息
    appstore_review.QueryTicketWithOperationRecordResp QueryTicketWithOperationRecord(1: required appstore_review.TicketIDReq req),
    //gp预审单包同步状态
    appstore_review.GetGPTicketMetadataStatusResp GetGPTicketMetadataStatus(1: required appstore_review.TicketIDReq req),
    //gp预审单元数据同步状态
    appstore_review.GetGpTicketPackageStatusResp GetGpTicketPackageStatus(1: required appstore_review.TicketIDReq req),
    //获取gp预审单提交页面基本信息
    appstore_review.GetGPTicketDetailResp GetGPTicketDetail (1: required appstore_review.TicketIDReq req),
    //提交gp预审单基本信息
    appstore_review.GPTicketDetailResp GPTicketDetail(1: required appstore_review.GPTicketDetailReq req),
    //获取gp预审单预审信息
    appstore_review.GetGPTicketReviewResp GetGPTicketReview(1: required appstore_review.TicketIDReq req),
    //提交gp预审单预审信息
    appstore_review.GPTIcketReviewResp GPTicketReview(1: required appstore_review.GPTicketReviewReq req),
    //获取gp预审单提审信息
    appstore_review.GetGPTicketCommitResp GetGPTicketCommit(1: required appstore_review.TicketIDReq req),
    //提交gp预审单提审信息
    appstore_review.GPTicketCommitResp GPTicketCommit(1: required appstore_review.GPTicketCommitReq req),
    //获取gp预审单提审失败信息
    appstore_review.GetGPCommitErrMsgResp GetGPCommitErrMsg(1: required appstore_review.TicketIDReq req),
    //gp预审单提审失败处理
    appstore_review.GPCommitFailResp GPCommitFail (1: required appstore_review.GPCommitFailReq req),
    //gp修改预审单流程类型
    appstore_review.ChangeGPTicketFlowResp ChangeGPTicketFlow(1: required appstore_review.ChangeGPTicketFlowReq req),
    //gp预审单提审失败处理
    appstore_review.GPRetryUploadPackageResp GPRetryUploadPackage(1: required appstore_review.GPRetryUploadPackageReq req),
    //gp预审单提审失败处理
    appstore_review.GPRetryUploadMetadataResp GPRetryUploadMetadata(1: required appstore_review.GPRetryUploadMetadataReq req),
    //处理Gp流程
    appstore_review.HandleGPQueueTaskResp HandleGPQueueTask(1: required appstore_review.HandleGPQueueTaskReq req)
    //上传GP包回调
    gp_release.UploadGPCallbackResp UploadGPPkgCallback(1: required gp_release.UploadGPCallbackReq req)
    //GP审核状态轮询
    appstore_review.GPReviewStatusPollingResp GPReviewStatusPolling(1: required appstore_review.GPReviewStatusPollingReq req)
    //刷新预审负责人 (更新到Kani)
    appstore_review.RefreshManagerUserResp RefreshManagerUser(1: required appstore_review.RefreshManagerUserReq req)
    //获取包接口
    appstore_review.GetArtifactListResp GetArtifactList(1: required appstore_review.GetArtifactListReq req),

    /* 以前相关接口写的稀碎 */
    //appStore预审单加在物料
    appstore_review.GetAppStoreMetadataResp InitAppStoreMetadata (1: required appstore_review.TicketIDReq req),
    //appStore获取预审单提交的物料
    appstore_review.GetAppStoreMetadataResp GetAppStoreMetadata (1: required appstore_review.TicketIDReq req),
    //appStore预审单提交物料
    appstore_review.AppStoreMetadataResp AppStoreMetadata (1: required appstore_review.AppStoreMetadataReq req),
    //appStore获取预审单提交的二进制文件
    appstore_review.GetAppStorePackageResp GetAppStorePackage (1: required appstore_review.TicketIDReq req),
    //appStore预审单提交二进制文件
    appstore_review.AppStorePackageResp AppStorePackage (1: required appstore_review.AppStorePackageReq req),
    //appStore获取预审单物料确认项检查结果
    appstore_review.GetAppStoreTicketMetadataCheckResp GetAppStoreTicketMetadataCheck (1: required appstore_review.TicketIDReq req),
    //appStore预审单物料确认项检查
    appstore_review.AppStoreTicketMetadataCheckResp AppStoreTicketMetadataCheck (1: required appstore_review.AppStoreTicketMetadataCheckReq req)
    //appStore获取预审单二进制文件确认项检查结果
    appstore_review.GetAppStoreTicketPackageCheckResp GetAppStoreTicketPackageCheck(1: required appstore_review.TicketIDReq req),
    //appStore预审单二进制文件确认项检查
    appstore_review.AppStoreTicketPackageCheckResp AppStoreTicketPackageCheck(1: required appstore_review.AppStoreTicketPackageCheckReq req),
    //appStore预审单预审
    appstore_review.AppStoreTicketReviewResp AppStoreTicketReview(1: required appstore_review.AppStoreTicketReviewReq req),
    //appStore获取预审单预审结果
    appstore_review.GetAppStoreTicketReviewResp GetAppStoreTicketReview(1: required appstore_review.TicketIDReq req),
    //appStore预审单提审
    appstore_review.AppStoreTicketCommitResp AppStoreTicketCommit(1: required appstore_review.AppStoreTicketCommitReq req),
    //appStore获取预审单提审结果
    appstore_review.GetAppStoreTicketCommitResp GetAppStoreTicketCommit(1: required appstore_review.TicketIDReq req),
    //发布根据aid和version获取AppStore预审单信息
    appstore_review.GetReviewTicketByAidAndVersionResp GetReviewTicketByAidAndVersion(1: required appstore_review.GetReviewTicketByAidAndVersionReq req),
    // 手动触发预审流程进行二进制检测
    appstore_review.ReviewBinaryDetectResp ReviewBinaryDetect(1: required appstore_review.ReviewBinaryDetectReq req)
    // 查询app在对应商店的上架地区，给星云用的OpenAPI
    appstore_review.GetAppStoreAppShelfTerritoriesResp GetAppStoreAppShelfTerritories(1: required appstore_review.GetAppStoreAppShelfTerritoriesReq req),
    // 查询app在对应商店的上架地区
    appstore_review.GetAppShelfTerritoriesResp GetAppShelfTerritories(1: required appstore_review.GetAppShelfTerritoriesReq req),
    // 修改app在对应商店的上架地区
    appstore_review.AppShelfTerritoriesResp AppShelfTerritories (1: required appstore_review.AppShelfTerritoriesReq req),
    //appStore预审单等待AppStore审核的时候有限度的修改元数据
    appstore_review.AppStoreMetadataResp AppStoreMetadataModifyInReviewWaiting(1: required appstore_review.AppStoreMetadataReq req),
    // 新轮询接口
    appstore_review.AppStorePollingResp AppStorePolling(1: required BaseReq req),
    // mock轮询接口
    appstore_review.AppStorePollingResp AppStoreMockPolling(1: required appstore_review.AppStoreMockPollingResultReq req),
    // 屏蔽重复通知的消息卡片按钮回调
    appstore_review.ReviewLarkCallbackResp BlockRepeatNotify(1: required appstore_review.ReviewLarkCallbackReq req),
    // 获取starling平台的name_space信息
    appstore_review.GetStarlingNameSpaceInfoResp GetStarlingNameSpaceInfo(1: required appstore_review.GetStarlingNameSpaceInfoReq req),
    // 获取starling平台配置的翻译内容
    appstore_review.GetStarlingContentResp GetStarlingContent(1: required appstore_review.GetStarlingContentReq req),
    // 修改元数据同步状态
    appstore_review.EmptyResp SetMetadataUploadStatus(1: required appstore_review.MetadataUploadStatusReq req),
    // 修改二进制文件同步状态
    appstore_review.EmptyResp SetPackageUploadStatus(1: required appstore_review.PackageUploadStatusReq req),
    // 获取AppStore放量定量比例消息卡片提醒配置
    appstore_review.GetAppStoreRegularNotificationResp GetAppStoreRegularNotificationConfig(1: required appstore_review.GetAppStoreRegulerNotificationReq req),
    // 配置AppStore放量定量比例消息卡片提醒
    appstore_review.SetAppStoreRegularNotificationResp SetAppStoreRegularNotificationConfig(1: required appstore_review.SetAppStoreRegulerNotificationReq req),
    // 检查groupID是否配置
    appstore_review.CheckAppGroupResp CheckAppGroup(1: required appstore_review.CheckAppGroupReq req),
    // 定量提醒轮询触发接口
    appstore_review.PollAppStoreReleaseStateResp PollAppStoreReleaseState(1: required appstore_review.PollAppStoreReleaseStateReq req),
    // 申诉时修改元数据
    appstore_review.AppStoreMetadataResp AppStoreMetadataInAppealing (1: required appstore_review.AppStoreMetadataReq req),
    // 修改完元数据后重新提审
    appstore_review.EmptyResp RecommitInAppStoreReject(1: required appstore_review.RecommitInAppStoreRejectReq req),
    // 获取GP审核状态
    appstore_review.GetGPStatusResp GetGPStatus(1: required appstore_review.GetGPStatusReq req),
    // 删除后台的TV version
    appstore_review.EmptyResp DeleteAppStoreTVVersion(1: required appstore_review.TicketIDReq req),
    // 获取GP应用安装人数
    appstore_review.GetGPInstallUserNumResp GetGPInstallUserNum(1:required appstore_review.GetGPInstallUserNumReq req),
    // 获取GP应用活跃设备数
    appstore_review.GetGPActiveDeviceNumResp GetGPActiveDeviceNum(1: required appstore_review.GetGPActiveDeviceNumReq req),
    // 发布创建GP预审(后审)单接口
    appstore_review.NewGPReviewTicketResp NewGPReviewTicket(1: required appstore_review.NewGPReviewTicketReq req),
    // 发布终止GP预审(后审)单接口
    appstore_review.TerminateGPReviewTicketResp TerminateGPReviewTicket(1: required appstore_review.TerminateGPReviewTicketReq req),
    // 获取GP包支持设备数变化list
    appstore_review.GetGPSupportDeviceChangesResp GetGPSupportDeviceChanges(1: required appstore_review.GetGPSupportDeviceChangesReq req),
    // 获取审核历史
    appstore_review.GetReviewHistoriesResp GetReviewHistories(1: required appstore_review.GetReviewHistoriesReq req),
    // 根据app_id和platform来查询app负责人和保密性
    appstore_review.AppRoleResp GetAppRoleByAppIDAndPlatform(1: required appstore_review.AppIDAndPlatformReq req)
    // 获取预审单终止状态map
    appstore_review.GetTerminalTicketStatusMapResp GetTerminalTicketStatusMap(1: required BaseReq req)
    // 根据artifact_id来获取对应版本当前预审单/上一个预审单
    appstore_review.GetReviewTicketByArtifactIDResp GetReviewTicketByArtifactID(1: required appstore_review.GetReviewTicketByArtifactIDReq req),
    // openAPI创建AppStore预审单
    appstore_review.OpenAPICreateReviewAppStoreTicketResp OpenAPICreateReviewAppStoreTicket(1: required appstore_review.OpenAPICreateReviewAppStoreTicketReq req),
    // app terr轮询
    appstore_review.SyncAppTerrResp SyncAppTerr(1: required BaseReq req)
    // 获取拒审关系对应表
    appstore_review.GetRejectReasonMetaResp GetRejectReasonMeta(1: required appstore_review.GetRejectReasonMetaReq req)
    // 获取IAE列表
    appstore_review.GetIAEResp GetIAE(1: required appstore_review.GetIAEReq req)
    // 撤审目前正在审核的
    appstore_review.CancelReviewSubmissionResp CancelReviewSubmission(1: required appstore_review.CancelReviewSubmissionReq req)
}

`,

	"slardar.thrift": `include "../../base.thrift"
include "common.thrift"
include "artifact.thrift"

namespace go ep.artifact.manager.slardar
namespace py ep.artifact.manager.slardar

enum FuseType {
    AUTO = 0,           //自动
    MANUAL = 1,          //手动
}

enum FuseSource {
    Slardar = 0,
    Skynet = 1,
}

enum Region {
    CN = 1,             //国内
    MALIVA = 2,         //美东
}

enum ReleaseType {
    GooglePlay = 1,     //GP发布
    Appstore = 2,       //Appstore发布
    Normal = 3,         //普通发布

    MuteGrey = 13,    //静默灰度发布
}

struct SlardarTemplateDTO {
    1: required i64 bits_slardar_template_id,
    2: required i64 bits_app_id,
    3: required i64 cloud_app_id,
    4: required i64 region,
    5: required common.Platform platform,
    6: required i64 slardar_template_id,
    7: required string slardar_template_name,
    8: required FuseType fuse_type,
    9: required string crash_type_label,
    10: optional FuseSource fuse_source,
}

struct ReleaseSlardarDTO {
    1: required i64 bits_release_slardar_id,
    2: required i64 slardar_id,
    3: required string slardar_name,
    4: required string slardar_url,
    5: required FuseType fuse_type,
}

struct ReleaseFuseDTO {
    1: required i64 bits_slardar_template_id,
    2: required i64 bits_slardar_id,
    3: required string version_name,
    4: required string update_version,
    5: required common.Platform platform,
    6: required FuseType fuse_type,
    7: required string fuse_name,
    8: required string fuse_reason,
    9: required i64 created_at,
    10: optional FuseSource source,
}

struct SyncSlardarTemplateReq {
    1: required i64 cloud_app_id,
    2: required i64 bits_app_id,
    3: required i64 region,
    4: required common.Platform platform,
    5: optional string version_phase = "",
    255: base.Base  Base,
}

struct SyncSlardarTemplateResp {
    255: base.BaseResp  BaseResp,
}

struct SwitchSlardarTemplateFuseTypeReq {
    1: required i64 bits_slardar_template_id
    4: required FuseType fuse_type,
    5: optional FuseSource fuse_source,
    255: base.Base  Base,
}

struct SwitchSlardarTemplateFuseTypeResp {
    255: base.BaseResp  BaseResp,
}

struct ListSlardarTemplateReq {
    1: required i64 bits_app_id (go.tag = "json:\"bits_app_id\" form:\"bits_app_id\""),
    2: optional string version_phase = "" (go.tag = "json:\"version_phase\" form:\"version_phase\""),
    255: base.Base  Base,
}

struct ListSlardarTemplateResp {
    1: string slardar_template_url,
    2: list<SlardarTemplateDTO> slardar_template_list,
    255: base.BaseResp  BaseResp,
}

struct ListReleaseSlardarReq {
    1: required i64 release_type (go.tag = "json:\"release_type\" form:\"release_type\""),
    2: required i64 release_id (go.tag = "json:\"release_id\" form:\"release_id\""),
    255: base.Base  Base,
}

struct ListReleaseSlardarResp {
    1: list<ReleaseSlardarDTO> slardar_list,
    255: base.BaseResp  BaseResp,
}

struct SearchReleaseFuseReq {
    1: required i64 bits_app_id (go.tag = "json:\"bits_app_id\" form:\"bits_app_id\""),
    2: required string fuzzy_search (go.tag = "json:\"fuzzy_search\" form:\"fuzzy_search\""),
    3: required string field (go.tag = "json:\"field\" form:\"field\""),
    4: required string direcation (go.tag = "json:\"direction\" form:\"direction\""),
    5: required i64 page_no (go.tag = "json:\"page_no\" form:\"page_no\""),
    6: required i64 page_size (go.tag = "json:\"page_size\" form:\"page_size\""),
    7: optional string version_phase = "" (go.tag = "json:\"version_phase\" form:\"version_phase\""),
    8: optional FuseSource source (go.tag = "json:\"fuse_source\" form:\"fuse_source\""),
    255: base.Base  Base,
}

struct SearchReleaseFuseResp {
    1: i64 total_count,
    2: list<ReleaseFuseDTO> fuse_list,
    255: base.BaseResp  BaseResp,
}

struct ManualFuseReleaseReq {
    1: required string fuse_name,
    2: required string fuse_reason,
    3: required i64 bits_release_slardar_id,
    4: required i64 artifact_id,
    5: required string operator,
    6: optional i64 alarm_time,
    7: optional string bits_release_url,
    255: base.Base  Base,
}

struct ManualFuseReleaseResp {
    255: base.BaseResp  BaseResp,
}

struct SlardarCallbackReq {
    1: required string slardar_callback, // slardar回传的是json字符串
    255: base.Base  Base,
}

struct SlardarCallbackResp {
    255: base.BaseResp  BaseResp,
}

struct DeleteSlardarRuleReq {
    1: required i64 cloud_app_id,
    2: required i64 integration_id,
    3: required common.Platform platform,
    4: required i64 region,
    5: optional string version_phase = "",
    255: base.Base  Base,
}

struct DeleteSlardarRuleResp {
    255: base.BaseResp  BaseResp,
}

struct AutoAddFuseRuleReq {
    1: optional list<i64> relase_rule_ids,
    255: base.Base  Base,
}

struct AutoAddFuseRuleResp {
    255: base.BaseResp  BaseResp,
}`,
}
