// Code generated by Kitex v1.0.6. DO NOT EDIT.

package googleplayconsoleservice

import (
	BytedanceReleaseGoogle_play_console "code.byted.org/clientQA/artifact-manager/kitex_gen/bytedance/release/google_play_console"
	"code.byted.org/kite/kitex/byted"
	"code.byted.org/kite/kitex/server"
)

// NewServer creates a server.Server with the given handler and options.
func NewServer(handler BytedanceReleaseGoogle_play_console.GooglePlayConsoleService, opts ...server.Option) server.Server {
	byted.SetServerThriftFiles(IDLs)

	var options []server.Option
	options = append(options, byted.ServerSuite(serviceInfo()))
	options = append(options, opts...)

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}

// NewServerWithBytedConfig creates a server.Server with the given handler and options.
func NewServerWithBytedConfig(handler BytedanceReleaseGoogle_play_console.GooglePlayConsoleService, config *byted.ServerConfig, opts ...server.Option) server.Server {
	byted.SetServerThriftFiles(IDLs)

	var options []server.Option
	options = append(options, byted.ServerSuiteWithConfig(serviceInfo(), config))
	options = append(options, opts...)

	svr := server.NewServer(options...)
	if err := svr.RegisterService(serviceInfo(), handler); err != nil {
		panic(err)
	}
	return svr
}
