package application

import (
	"code.byted.org/bits/release_hotfix/domain"
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/gopkg/lang/slices"
	CIHotfix "code.byted.org/overpass/bytedance_bits_optimus/kitex_gen/bytedance/bits/hotfix"

	"code.byted.org/bits/release_hotfix/container"
	"code.byted.org/bits/release_hotfix/domain/model"
	"code.byted.org/bits/release_hotfix/domain/mysql"
	"code.byted.org/bits/release_hotfix/err_code"
	"code.byted.org/bits/release_hotfix/service"

	"code.byted.org/gopkg/gopool"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"

	"code.byted.org/lang/gg/choose"

	"code.byted.org/bits/release_hotfix/utils"
)

func CreateHotfix(ctx context.Context, req *hotfix.CreateHotfixRequest) (resp *hotfix.CreateHotfixResponse, err error) {
	// radar平台上报指标
	defer func() {
		go func() {
			defer utils.Recover(ctx)
			code := choose.If(err == nil, 1, 0)
			service.ReportSystemAvailableMetrics(ctx, req.HotfixInfo.BitsAppId, service.MetricsType_CreateHotfixTask, code, utils.GetUserEmail(ctx))
		}()
	}()

	ctx = context.WithValue(ctx, utils.UserScope, service.GetAppScope(ctx, req.GetHotfixInfo().GetBitsAppId()))
	dbHotfixTaskInfo := &mysql.HotfixTaskModel{}
	dbHotfixTaskInfo.GetInfo(ctx, req.GetHotfixInfo())
	if dbHotfixTaskInfo.HotfixType == 0 {
		dbHotfixTaskInfo.HotfixType = 1
	}

	c := container.GetContainer()
	// 先创建db 任务
	id, err := c.Mysql.CreateTask(ctx, dbHotfixTaskInfo)
	if err != nil {
		logs.CtxError(ctx, "[CreateHotfix]CreateHotfix err: %+v", err)
		return nil, err
	}
	dbHotfixTaskInfo.ID = uint(id)

	logs.CtxInfo(ctx, "[CreateHotfix]db info: %+v", dbHotfixTaskInfo)

	// 去ci创建审批
	res, err := service.CreateHotfix(ctx, dbHotfixTaskInfo)
	if err != nil {
		logs.CtxError(ctx, "[CreateHotfix]get ci hotfix info err: %+v", err)
		return nil, err
	}
	dbHotfixTaskInfo.DevId = &res.DevId
	dbHotfixTaskInfo.TicketId = res.TicketID

	// 获取人员信息
	userInfo, err := GetUserInChat(ctx, dbHotfixTaskInfo)
	if err != nil {
		logs.CtxError(ctx, "[CreateHotfix]GetUserInChat error", err)
		return nil, err
	}

	// 创建群聊
	groupName := fmt.Sprintf("BitsHotfix[%+v-%+v](%v)%+v", dbHotfixTaskInfo.CloudAppId, dbHotfixTaskInfo.Platform, dbHotfixTaskInfo.TaskName, dbHotfixTaskInfo.ID)
	users := make([]string, 0)
	users = append(users, userInfo.TechUsers...)
	users = append(users, userInfo.RelatedUsers...)
	users = append(users, userInfo.RDs...)
	users = append(users, userInfo.QAs...)
	users = append(users, userInfo.BMs...)
	chatId, err := utils.CreateGroupWithUsers(ctx, utils.SaveUBotType, groupName, users, dbHotfixTaskInfo.IssueBehavior)
	if err != nil {
		logs.CtxError(ctx, "[CreateHotfix]CreateGroupWithUsers err: %+v", err)
		return nil, err
	}
	dbHotfixTaskInfo.LarkGroupId = chatId

	logs.CtxInfo(ctx, "[CreateHotfix]after get lark group: %+v, %+v", dbHotfixTaskInfo, groupName)

	// 最后更新数据库信息
	if dbHotfixTaskInfo.IsTestHotfix == domain.HotfixOfficalTask {
		_, err = c.Mysql.UpdateTask(ctx, dbHotfixTaskInfo, []string{"approval_status", "task_status"}, nil)
	} else if dbHotfixTaskInfo.IsTestHotfix == domain.HotfixTestTask {
		//如果是测试热修任务，审批直接通过
		taskStatus := "DEVELOPING"
		approvalStatus := "PASS"
		dbHotfixTaskInfo.TaskStatus = &taskStatus
		dbHotfixTaskInfo.ApprovalStatus = &approvalStatus
		logs.CtxInfo(ctx, "[CreateHotfix]test task update mysql: %+v", dbHotfixTaskInfo)
		_, err = c.Mysql.UpdateTask(ctx, dbHotfixTaskInfo, nil, nil)
	}

	if err != nil {
		logs.CtxError(ctx, "[CreateHotfix]UpdateHotfix err: %+v", err)
		return nil, err
	}

	logs.CtxInfo(ctx, "user info rd: %+v, qa: %+v, tech: %+v,related: %+v", userInfo.RDs, userInfo.QAs, userInfo.TechUsers, userInfo.RelatedUsers)
	// 发消息卡片
	gopool.CtxGo(ctx, func() {
		SendCreateHotfixLarkMessage(ctx, dbHotfixTaskInfo, userInfo)
	})

	// 接入one site权限
	gopool.CtxGo(ctx, func() {
		service.SetHotfixOneSitePermission(ctx, dbHotfixTaskInfo, userInfo)
	})

	// 头条需求：将他们的机器人拉到群里
	if slices.Contains([]int64{6589, 315828, 13, 35, 4830}, dbHotfixTaskInfo.CloudAppId) {
		gopool.CtxGo(ctx, func() {
			defer utils.Recover(ctx)
			err = utils.AddBotTOChat(ctx, utils.SaveUBotType, *chatId, []string{utils.IdeaBotType, utils.MeeseeksBotType})
			if err != nil {
				logs.CtxError(ctx, "[CreateHotfix]AddBotTOChat err: %+v", err)
			}
		})
	}

	// 返回
	resp = hotfix.NewCreateHotfixResponse()
	resp.SetId(int64(dbHotfixTaskInfo.ID))
	return resp, nil
}

func UpdateTaskApprovalStatus(ctx context.Context, req *hotfix.UpdateHotfixApproveStatusRequest) (resp *hotfix.UpdateHotfixApproveStatusResponse, err err_code.ErrCode) {
	resp = hotfix.NewUpdateHotfixApproveStatusResponse()
	// 开启事务
	con := container.GetContainer()
	statusChanged := false
	reminder := ""
	chatID := ""
	taskStatus := ""
	sErr := con.ExecuteTransaction(ctx, func(ctx context.Context, c *container.Container) error {
		// 查询当前任务
		dbTaskInfo, e := c.Mysql.QueryTask(ctx, req.Id)
		if e != nil {
			return e
		}
		if utils.PtrToString(dbTaskInfo.TaskStatus) != "WAITING_FOR_APPROVAL" {
			return nil
		}

		reminder = dbTaskInfo.HotfixCreator
		chatID = utils.PtrToString(dbTaskInfo.LarkGroupId)
		switch req.ApprovalStatus {
		case CIHotfix.ApproveStatus_PASS.String():
			taskStatus = "DEVELOPING"
		case CIHotfix.ApproveStatus_TERMINATED.String():
			taskStatus = "APPROVAL_FAILED"
		default:
			return errors.New("unknown approval status")
		}

		e = c.Mysql.UpdateTaskWithFilter(ctx, int64(dbTaskInfo.ID), map[string]interface{}{
			"approval_status": req.ApprovalStatus,
			"task_status":     taskStatus,
		})
		if e != nil {
			return e
		}
		statusChanged = true
		return nil
	})

	if sErr != nil {
		logs.CtxError(ctx, "[UpdateTaskApprovalStatus]transaction error: %v", sErr)
		err = err_code.NewServiceErrCode(-1, sErr)
		return
	}

	// TODO 状态改变，发送消息卡片
	if statusChanged {
		go func() {
			utils.Recover(ctx)
			SendApprovalStatusChangeCard(ctx, reminder, taskStatus, chatID)
		}()
	}
	return resp, nil
}

func UpdateHotfixTask(ctx context.Context, req *hotfix.UpdateHotfixRequest) (resp *hotfix.UpdateHotfixResponse, err error) {
	ctx = context.WithValue(ctx, utils.UserScope, service.GetAppScope(ctx, req.GetHotfixInfo().GetBitsAppId()))
	resp = hotfix.NewUpdateHotfixResponse()
	// 获取请求中信息
	dbTaskInfo := &mysql.HotfixTaskModel{}
	dbTaskInfo.GetInfo(ctx, req.GetHotfixInfo())
	dbTaskInfo.ID = uint(req.Id)

	// 重制审批流
	ret, err := service.ResetTaskApprove(ctx, dbTaskInfo)
	if err != nil {
		logs.CtxError(ctx, "[UpdateHotfixTask]ResetTaskApprove error: %v", err)
		err = err_code.NewServiceErrCode(-1, err)
		return
	}

	// 重置相关信息
	taskStatus := "WAITING_FOR_APPROVAL"
	approvalStatus := "PENDING"
	dbTaskInfo.TaskStatus = &taskStatus
	dbTaskInfo.ApprovalStatus = &approvalStatus
	dbTaskInfo.TicketId = ret.TicketID

	// 全量更新，忽略更新hotfix type, group id, dev id, created time
	c := container.GetContainer()
	_, err = c.Mysql.UpdateTask(ctx, dbTaskInfo, []string{"hotfix_type", "lark_group_id", "dev_id", "created_at"},
		nil)
	if err != nil {
		logs.CtxError(ctx, "[UpdateHotfixTask]UpdateTask err: %+v", err)
		err = err_code.NewServiceErrCode(-1, err)
		return
	}
	dbTaskInfo, err = c.Mysql.QueryTask(ctx, int64(dbTaskInfo.ID))
	if err != nil {
		logs.CtxError(ctx, "[UpdateHotfixTask]QueryTask err: %+v", err)
		err = err_code.NewServiceErrCode(-1, err)
		return
	}

	// 获取人员信息
	userInfo, err := GetUserInChat(ctx, dbTaskInfo)
	if err != nil {
		logs.CtxError(ctx, "[UpdateHotfixTask]GetUserInChat error", err)
		return
	}

	// 拉人入群
	users := make([]string, 0)
	users = append(users, userInfo.TechUsers...)
	users = append(users, userInfo.RelatedUsers...)
	users = append(users, userInfo.RDs...)
	users = append(users, userInfo.QAs...)
	users = append(users, userInfo.BMs...)
	_, err = utils.AddUsersToChat(ctx, utils.SaveUBotType, utils.PtrToString(dbTaskInfo.LarkGroupId), users)
	if err != nil {
		logs.CtxError(ctx, "[UpdateHotfixTask]AddUsersToChat err: %+v, id: %v", err, req.Id)
		return
	}

	// 发送通知
	gopool.CtxGo(ctx, func() {
		SendCreateHotfixLarkMessage(ctx, dbTaskInfo, userInfo)
	})

	return
}

func SendCreateHotfixLarkMessage(ctx context.Context, hotfixInfo *mysql.HotfixTaskModel, userInfo *model.UserInChatInfo) {
	// 构造卡片信息
	details := ConstructHotfixLarkMessageDetail(ctx, hotfixInfo, userInfo)

	// 发卡片
	messageID, err := utils.SendHotfixIOSApplyCard(ctx, details, *hotfixInfo.LarkGroupId)
	if err != nil {
		logs.CtxError(ctx, "[SendCreateHotfixLarkMessage]SendHotfixIOSApplyCard error: %+v", err)
		return
	}
	// 记录消息
	c := container.GetContainer()
	entity := mysql.HotfixLarkMessage{
		HotfixTaskID: int(hotfixInfo.ID),
		MessageID:    messageID,
		ChatID:       utils.PtrToString(hotfixInfo.LarkGroupId),
	}
	err = c.Mysql.CreateLarkMessage(ctx, &entity)
	if err != nil {
		logs.CtxError(ctx, "[SendCreateHotfixLarkMessage]CreateLarkMessage error: %+v", err)
	}
	return
}

func ConstructHotfixLarkMessageDetail(ctx context.Context, taskInfo *mysql.HotfixTaskModel, userInfo *model.UserInChatInfo) *model.CreateHotfixTaskLarkCard {
	details := model.CreateHotfixTaskLarkCard{
		HotfixDescription: taskInfo.TaskName,
		HotfixMr:          taskInfo.MrUrl,
		HotfixVersions:    taskInfo.Versions,
	}

	details.HotfixApplicant = fmt.Sprintf("<at email=%+v ></at>", utils.UserName2Email(taskInfo.HotfixCreator))

	var RdsAt []string
	for _, u := range userInfo.RDs {
		RdsAt = append(RdsAt, fmt.Sprintf("<at email=%+v ></at>", utils.UserName2Email(u)))
	}
	details.HotfixRD = strings.Join(RdsAt, ",")

	var QAsAt []string
	for _, u := range userInfo.QAs {
		QAsAt = append(QAsAt, fmt.Sprintf("<at email=%+v ></at>", utils.UserName2Email(u)))
	}
	details.HotfixQA = strings.Join(QAsAt, ",")

	var Techs []string
	for _, u := range userInfo.TechUsers {
		Techs = append(Techs, fmt.Sprintf("<at email=%+v ></at>", utils.UserName2Email(u)))
	}
	details.HotfixTech = strings.Join(Techs, ",")

	var RelatedAt []string
	for _, u := range userInfo.RelatedUsers {
		RelatedAt = append(RelatedAt, fmt.Sprintf("<at email=%+v ></at>", utils.UserName2Email(u)))
	}
	details.HotfixBusiness = strings.Join(RelatedAt, ",")

	// 设置跳转链接，先获取space id
	spaceID, _, err := service.BitsAppId2SpaceId(ctx, int(taskInfo.BitsAppId))
	if err != nil {
		logs.CtxError(ctx, "ConstructHotfixLarkMessageDetail can not get space id: %v", err)
		details.HotfixTaskLink = fmt.Sprintf(utils.GetBitsDomain()+utils.Hotfix_Task_Url, taskInfo.ID, taskInfo.BitsAppId)
	} else {
		details.HotfixTaskLink = fmt.Sprintf(utils.GetBitsDomain()+utils.Hotfix_Task_New_url, spaceID, taskInfo.ID)
	}

	if taskInfo.HotfixType == int8(hotfix.HotfixType_IOSHotfix) || taskInfo.HotfixType == int8(hotfix.HotfixType_IOSSDKHotfix) {
		details.HotfixManual = utils.IOSManual
	} else {
		details.HotfixManual = utils.AndroidManual
	}

	return &details
}

func SendApprovalStatusChangeCard(ctx context.Context, reminder, approvalStatus, chatID string) {
	detail := &model.HotfixTaskApprovalChangeCard{
		HotfixApproveTime:   time.Now().Format("2006-01-02 15:04:05"),
		HotfixApproveStatus: approvalStatus,
		HotfixReminder: choose.If(strings.Contains(reminder, "@"), fmt.Sprintf("<at email=%+v</at>", reminder),
			fmt.Sprintf("<at email=%+v ></at>", utils.UserName2Email(reminder))),
	}
	// 发卡片
	sCtx := context.WithValue(ctx, utils.UserScope, utils.GetScopeFromUser(reminder))
	_, err := utils.SendApprovalStatusChangeCard(sCtx, detail, chatID)
	if err != nil {
		logs.CtxError(ctx, "[SendCreateHotfixLarkMessage]SendHotfixIOSApplyCard error: %+v", err)
		return
	}
}

// QueryHotfixTaskInfoByPatchArtifactID 根据热修包artifact id查询热修任务相关信息
func QueryHotfixTaskInfoByPatchArtifactID(ctx context.Context, req *hotfix.QueryHotfixTaskInfoByPatchArtifactIDReq) (r *hotfix.QueryHotfixTaskInfoByPatchArtifactIDResp, err err_code.ErrCode) {
	r = hotfix.NewQueryHotfixTaskInfoByPatchArtifactIDResp()
	r.TaskInfo = hotfix.NewHotfixTaskInfoForCustomerTest()
	logs.CtxInfo(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]start, req: %v", req)

	// 查询artifact 表，获取publish id
	c := container.GetContainer()
	art, e := c.Mysql.QueryArtifactByID(ctx, req.GetPatchArtifactId())
	if e != nil {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]QueryArtifactByID error: %v", e)
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}
	publishIDStr, ok := art.Tags["publish_id"].(string)
	if !ok {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]can not find publish id, tags: %v", art.Tags)
		e = fmt.Errorf("can not find publish id")
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}
	publishID, e := strconv.Atoi(publishIDStr)
	if e != nil {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]atoi publish id error: %v", e)
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}

	// 查询版本相关信息
	publish, e := c.Mysql.QueryHotfixPublish(ctx, int64(publishID))
	if e != nil {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]QueryHotfixPublish error: %v", e)
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}
	version, e := c.Mysql.QueryVersionByID(ctx, publish.VersionID)
	if e != nil {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]QueryVersionByID error: %v", e)
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}
	r.TaskInfo.Version = version.VersionLong
	r.TaskInfo.TaskId = version.HotfixTaskID

	// 查询热修任务相关信息
	hotfixTask, e := c.Mysql.QueryTask(ctx, version.HotfixTaskID)
	if e != nil {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]QueryVersionByID error: %v", e)
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}
	r.TaskInfo.TaskName = hotfixTask.TaskName
	r.TaskInfo.Creator = hotfixTask.HotfixCreator
	r.TaskInfo.ChatId = utils.PtrToString(hotfixTask.LarkGroupId)

	// 获取跳转链接
	spaceID, _, e := service.BitsAppId2SpaceId(ctx, int(hotfixTask.BitsAppId))
	if e != nil {
		logs.CtxError(ctx, "[QueryHotfixTaskInfoByPatchArtifactID]BitsAppId2SpaceId error: %v", e)
		err = err_code.NewServiceErrCode(-1, e)
		return r, err
	}
	if publish.BuildID != 0 {
		r.TaskInfo.ViewLink = fmt.Sprintf(utils.GetBitsDomain()+utils.Hotfix_Publish_Url, spaceID, hotfixTask.ID,
			publish.VersionID, publish.BuildID, publish.BuildID)
	} else {
		r.TaskInfo.ViewLink = fmt.Sprintf(utils.GetBitsDomain()+utils.Hotfix_Publish_Url, spaceID, hotfixTask.ID,
			publish.VersionID, publishID, publishID)
	}
	return r, nil
}
