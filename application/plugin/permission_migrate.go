package plugin

import (
	"context"
	"strconv"
	"strings"
	"sync"

	"code.byted.org/bits/release_hotfix/container"
	"code.byted.org/bits/release_hotfix/domain/mysql"
	"code.byted.org/bits/release_hotfix/err_code"
	"code.byted.org/bits/release_hotfix/service"
	"code.byted.org/bits/release_hotfix/utils"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
	"code.byted.org/overpass/bytedance_bits_meta/kitex_gen/bytedance/bits/meta"
)

func RefreshPluginPermission2Onesite(ctx context.Context, req *hotfix.RefreshPluginPermission2OnesiteReq) (*hotfix.DefaultResp, err_code.ErrCode) {
	resp := hotfix.NewDefaultResp()

	c := container.GetContainer()
	plugins, err := c.Mysql.QueryPluginByFilter(ctx, mysql.PluginFilter{
		AppId: req.AppId,
		Id:    req.PluginId,
	}, nil, nil)
	if err != nil {
		return resp, err_code.NewServiceErrCode(-1, err)
	}

	wg := sync.WaitGroup{}
	ch := make(chan struct{}, 50)

	for idx := range plugins {
		plugin := plugins[idx]
		ch <- struct{}{}
		wg.Add(1)
		go func(ctx context.Context, plugin *mysql.Plugin) {
			defer utils.Recover(ctx)
			defer func() {
				<-ch
				wg.Done()
			}()
			err := createPluignResource(ctx, plugin, req.GetRoleTypes(), strings.Split(plugin.PluginQa, ","))
			if err != nil {
				logs.CtxError(ctx, "createPluignResource failed, err = %v", err)
			}
		}(ctx, plugin)
	}
	wg.Wait()

	return resp, nil
}

func createPluignResource(ctx context.Context, plugin *mysql.Plugin, roleTypes, pluginReleaseReviewer []string) error {
	creator := utils.GetUsernameByEmail(plugin.Ownername)
	role2UserName := map[string][]string{}
	if len(roleTypes) == 0 {
		roleTypes = []string{service.Role_PluginQAOwner, service.Role_PluginReleaseReviewer, service.Role_PluginRDOwner}
	}

	for _, role := range roleTypes {
		var users []string
		switch role {
		case service.Role_PluginQAOwner:
			users = strings.Split(plugin.PluginQa, ",")
		case service.Role_PluginReleaseReviewer:
			if len(pluginReleaseReviewer) == 0 {
				continue
			} else {
				users = pluginReleaseReviewer
			}
		case service.Role_PluginRDOwner:
			users = strings.Split(plugin.PluginRd, ",")
		default:
			continue
		}
		role2UserName[role] = users
	}
	spaceId, err := service.CloudAppIdPlatform2SpaceId(ctx, int(plugin.AppId), meta.TechnologyStack_Android)
	if err != nil {
		logs.CtxError(ctx, "[createPluignResource] CloudAppIdPlatform2SpaceId(%d) failed, err = %v", plugin.AppId, err)
		return err
	}

	err = service.CreateOnesiteResource(ctx, service.ResourceType_Plugin, strconv.Itoa(int(plugin.ID)), strconv.Itoa(spaceId), creator, role2UserName)
	if err != nil {
		logs.CtxError(ctx, "[createPluignResource] CreateOnesiteResource(%d) failed, err = %v", plugin.ID, err)
		return err
	}

	return nil
}
