package application

import (
	"code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
	"context"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"testing"
)

func TestCreateHotfix(t *testing.T) {
	s := `{
  "HotfixInfo": {
    "task_name": "111",
    "business_group_id": 1696,
    "business_group_name": "test",
    "feasibility_assessment": 1,
    "versions": [
      "46393"
    ],
    "hotfix_expect_time": 1725543268,
    "tech_owner": "[\"wenhaozhe\"]",
    "hotfix_rd": [
      "wenhaozhe"
    ],
    "hotfix_qa": [
      "wenhaozhe"
    ],
    "hotfix_related": "[\"wenhaozhe\"]",
    "bits_app_id": 2020092282,
    "bits_group_id": 2020092282,
    "platform": "Android",
    "cloud_app_id": 4830,
    "hotfix_creator": "wenh<PERSON><PERSON><PERSON>",
    "hotfix_type": 3
  }
}`
	req := &hotfix.CreateHotfixRequest{
		//HotfixInfo: &hotfix.Hotfix{
		//	TaskName: "whz_test115",
		//	BusinessGroupId: 1696,
		//
		//},
	}
	ctx := context.Background()
	jsoniter.UnmarshalFromString(s, &req)
	resp, err := CreateHotfix(ctx, req)
	fmt.Println(resp, err)
}
