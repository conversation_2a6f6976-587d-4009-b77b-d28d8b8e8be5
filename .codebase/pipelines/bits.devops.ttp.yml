
template: go
name: bits.devops.ttp
clone_depth: 1
spec: c1.2xlarge
go_version: "1.22"
enable_cache: true
env: boe
test_execution_version: v2
enable_proxy: true
enable_profile_upload: true
enable_mongo: true
enable_redis: true
enable_mysql: true
image: hub.byted.org/base/bazel.codebase_ci_base.debian12:1ecdb9d50f53624be69b2b4dd6aad1bf
mysql_database: halo_test
mysql_password: root
envs:
  KMS_ZONE: boe
  CONFIG_PROFILE: boe
  TESTING_PREFIX: offline

caches:
  - backend: ebs
    key: hagrid-bits_devops_ttp-ci
    size: 64
    git_clean: "-dfx"

trigger:
  change:
    # 对于目标分支是feat/xxx的MR，不跑 ci
    # 一般来说，大家开发时，源分支是feat/xxx, 目标分支是main或者release/**
    # 目标分支通常不会是feat/xxx
    # 但是由于其他原因，有的feat/xxx会常驻主干，并且有main --> feat/xxx 的open MR
    # 这个MR是不需要跑CI的。
    source-branches: [ "!main" ]
    paths:
      - ".codebase/pipelines/bits.devops.ttp.yml"
      - "app/ttp/ttp-server/**"

commands:
  - export # 打印当前所有的环境变量
  - export PATH=$PATH:$HOME/go/bin
  - go install code.byted.org/kite/kitex/tool/cmd/kitex@v1.17.2
  - bash scripts/copy_pb_for_ide_indexing.sh
  - cd app/ttp/ttp-server
  - "[ -e kitex-all.sh ] && bash kitex-all.sh || true"
  - make test
  - cp coverage.out $CI_WORKSPACE

coverage_config: # 覆盖率卡口配置
  line_percent: 0                    # 全量行覆盖率（常用）
  diff_percent: 0                    # 增量覆盖率（常用）

codecov_subproject_name: "bits.devops.ttp"
