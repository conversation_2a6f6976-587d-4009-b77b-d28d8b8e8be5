package scm

import (
	"context"
	"errors"
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/libs/middleware/restymw"
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/lang/gg/gresult"
	json "github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"github.com/google/go-querystring/query"
)

type client struct {
	cn *resty.Client
	eu *resty.Client
	us *resty.Client
}

func New() Api {
	cn := resty.New().
		SetRetryCount(3).
		SetTimeout(60 * time.Second).
		SetBaseURL("https://scm.byted.org").
		SetJSONMarshaler(json.Marshal).
		SetJSONUnmarshaler(json.Unmarshal).
		SetPreRequestHook(restymw.SetPreRequestHook).
		OnBeforeRequest(restymw.SetMetaHeader).
		OnAfterResponse(restymw.OnAfterResponse)

	us := resty.New().
		SetRetryCount(3).
		SetTimeout(60 * time.Second).
		SetBaseURL("https://scm-ttp-us.byted.org").
		SetJSONMarshaler(json.Marshal).
		SetJSONUnmarshaler(json.Unmarshal).
		SetPreRequestHook(restymw.SetPreRequestHook).
		OnBeforeRequest(restymw.SetMetaHeader).
		OnAfterResponse(restymw.OnAfterResponse)

	eu := resty.New().
		SetRetryCount(3).
		SetTimeout(60 * time.Second).
		SetBaseURL("https://scm.tiktoke.org").
		SetJSONMarshaler(json.Marshal).
		SetJSONUnmarshaler(json.Unmarshal).
		SetPreRequestHook(restymw.SetPreRequestHook).
		OnBeforeRequest(restymw.SetMetaHeader).
		OnAfterResponse(restymw.OnAfterResponse)

	return &client{
		cn: cn,
		eu: eu,
		us: us,
	}
}

func (client *client) c(cp *ControlPlane) *resty.Client {
	if cp == nil {
		return client.cn
	}

	switch *cp {
	case ControlPlaneCN, ControlPlaneEU:
		return client.cn
	//case ControlPlaneEU:
	//	return client.eu
	case ControlPlaneUS:
		return client.us
	default:
		return client.cn
	}
}

type GetReposByNamesRequest struct {
	RepoNames []string `url:"repo_names"`
}

type Repo struct {
	Id       int64  `json:"id"`
	Name     string `json:"name"`
	GitUrl   string `json:"git_url"`   // ******************:devinfra/hagrid.git
	RepoName string `json:"repo_name"` // e.g. devinfra/hagrid
}

func (client *client) GetReposByNames(ctx context.Context, repoNames []string, opts ...Opt) gresult.R[[]*Repo] {
	// repoName, e.g. bits/cd/api

	o := new(Options)
	for _, opt := range opts {
		opt(o)
	}

	req := &GetReposByNamesRequest{RepoNames: repoNames}

	params, _ := query.Values(req)
	u := "/api/v2/repos/by_names"

	response := make([]*Repo, 0, len(repoNames))
	c := client.c(o.ControlPlane)
	resp, err := c.R().
		SetContext(ctx).
		SetQueryParamsFromValues(params).
		SetResult(&response).
		Get(u)
	if err != nil {
		return gresult.Err[[]*Repo](err)
	}
	if resp.IsError() {
		return gresult.Err[[]*Repo](errors.New(conv.UnsafeBytesToString(resp.Body())))
	}
	return gresult.OK(response)
}

type RepoVersion struct {
	Version        string `json:"version"` // 版本号
	Repos          int64  `json:"repos"`   // repo id
	BaseCommitHash string `json:"base_commit_hash"`
	Status         string `json:"status"`
	Type           string `json:"type"`
}

func (client *client) GetRepoVersion(ctx context.Context, repoId int64, version string, opts ...Opt) gresult.R[*RepoVersion] {
	o := new(Options)
	for _, opt := range opts {
		opt(o)
	}

	u := "/api/repos/{repo_id}/versions/{version}"

	response := new(RepoVersion)
	c := client.c(o.ControlPlane)
	resp, err := c.
		R().
		SetContext(ctx).
		SetPathParams(map[string]string{"repo_id": strconv.FormatInt(repoId, 10), "version": version}).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[*RepoVersion](err)
	}
	if resp.IsError() {
		return gresult.Err[*RepoVersion](errors.New(conv.UnsafeBytesToString(resp.Body())))
	}
	return gresult.OK(response)
}

type ListRepoVersionsRequest struct {
	ReposId       int    `url:"repos_id,omitempty"`
	RepoName      string `url:"repo_name,omitempty"`
	Branch        string `url:"branch,omitempty"`
	Type          string `url:"type,omitempty"` // 版本类型，online/offline/test/private/discard。
	Status        string `url:"status,omitempty"`
	StatusAArch64 string `url:"status_aarch64,omitempty"`  // 分别表示 x86_64 和 aarch64 两个架构的版本状态，build_ok/build_failed/building/prepare。
	TypeList      string `url:"type_list,omitempty"`       // 可以选择多个类型做过滤，例如 type_list=offline,online。
	CreateDateGte string `url:"create_date_gte,omitempty"` // 创建时间在某个日期之后，例如 create_date_gte=2020-01-03。
	CreateDateLt  string `url:"create_date_lt,omitempty"`  // 创建时间在某个日期之前，例如 create_date_lt= 2020-01-03。
	Version       string `url:"version,omitempty"`         // 版本号过滤，例如 version=1.0.0.1。
	Commit        string `url:"commit,omitempty"`          // 按照 commit 过滤，前缀无效，需要完整的 commit id。
	Limit         int    `url:"limit,omitempty"`
	Offset        int    `url:"offset,omitempty"`
}

type ListRepoVersionsResponse struct {
	Count   int            `json:"count"`
	Results []*RepoVersion `json:"results"`
}

func (client *client) ListRepoVersions(ctx context.Context, req *ListRepoVersionsRequest, opts ...Opt) gresult.R[*ListRepoVersionsResponse] {
	o := new(Options)
	for _, opt := range opts {
		opt(o)
	}

	u := "/api/v2/versions/"

	params, err := query.Values(req)
	if err != nil {
		return gresult.Err[*ListRepoVersionsResponse](err)
	}
	response := new(ListRepoVersionsResponse)
	c := client.c(o.ControlPlane)
	resp, err := c.
		R().
		SetContext(ctx).
		SetQueryParamsFromValues(params).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[*ListRepoVersionsResponse](err)
	}
	if resp.IsError() {
		return gresult.Err[*ListRepoVersionsResponse](errors.New(conv.UnsafeBytesToString(resp.Body())))
	}
	return gresult.OK(response)
}
