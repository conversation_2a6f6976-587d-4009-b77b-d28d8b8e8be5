/**
 * @Date: 2024/1/8
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package lark

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/bits/hephaestus/pkg/jsons"
)

func TestMessages(t *testing.T) {
	ctx := context.Background()
	client := New()

	t.Run("SendRawMessage", func(t *testing.T) {
		t.SkipNow()

		receiveIdType := IdTypeEmail
		request := &SendRawMessageReq{
			ReceiveID: "<EMAIL>",
			MsgType:   MsgTypeText,
			Content:   `{"text":"liutao test"}`,
		}

		response := client.SendRawMessage(ctx, receiveIdType, request).Must()
		fmt.Println(jsons.Stringify(response))
	})
}
