/**
 * @Date: 2023/12/7
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package lark

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/lark/httpreader"
	"net/url"
	"path/filepath"

	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/lang/gg/gresult"
)

//type UploadImageRequest struct {
//	ImageType ImageType `json:"image_type,omitempty"` // 图片类型, 示例值: "message", 可选值有: message: 用于发送消息, avatar: 用于设置头像
//	Image     []byte    `json:"image,omitempty"`      // 图片内容, 注意: 上传的图片大小不能超过10MB, 示例值: 二进制文件
//}

type UploadImageResponse struct {
	ImageKey string `json:"image_key,omitempty"` // 图片的key
}

func (client *client) UploadImage(ctx context.Context, imageType ImageType, filename string, content []byte) gresult.R[string] {
	u := "/open-apis/im/v1/images"

	values := url.Values{}
	values.Set("image_type", string(imageType))
	response := new(Response[*UploadImageResponse])
	resp, err := client.httpclient.
		R().
		SetContext(ctx).
		SetFormDataFromValues(values).
		SetFileReader("image", filename, bytes.NewBuffer(content)).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[string](fmt.Errorf("failed to send request to lark: %w", err))
	}
	if resp.IsError() {
		return gresult.Err[string](errors.New(conv.UnsafeBytesToString(resp.Body())))
	}
	data, err := response.Unwrap().Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	return gresult.OK(data.ImageKey)
}

func (client *client) UploadImageByUrl(ctx context.Context, imageType ImageType, url string) gresult.R[string] {
	httpReader := httpreader.New()
	content, err := httpReader.ReadBody(ctx, url).Get()
	if err != nil {
		return gresult.Err[string](err)
	}

	return client.UploadImage(ctx, imageType, filepath.Base(url), content)
}
