package mongo

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"

	"code.byted.org/devinfra/hagrid/libs/errors"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DB struct {
	Version            string
	UserName           string
	Password           string
	InitDatabase       string
	Silent             bool
	SupportTransaction bool

	pool     *dockertest.Pool
	resource *dockertest.Resource
}

func (db *DB) init() {
	if db.Version == "" {
		db.Version = "5.0.26"
	}
}

func (db *DB) Start() error {
	db.init()
	pool, err := dockertest.NewPool("")
	if err != nil {
		return errors.StatusInternalServerError.Wrap(err, "Could not create docker pool")
	}
	db.pool = pool
	log.V2.Info().StrKV("Client", fmt.Sprintf("%+v", pool.Client)).Emit()
	if err := pool.Client.Ping(); err != nil {
		return errors.StatusInternalServerError.Wrap(err, "Could not connect to docker")
	}
	dockerOptions := &dockertest.RunOptions{
		Repository: "mongo",
		Tag:        db.Version,
	}
	if db.UserName != "" {
		dockerOptions.Env = append(dockerOptions.Env, "MONGO_INITDB_ROOT_USERNAME="+db.UserName)
	}
	if db.Password != "" {
		dockerOptions.Env = append(dockerOptions.Env, "MONGO_INITDB_ROOT_PASSWORD="+db.Password)
	}
	if db.InitDatabase != "" {
		dockerOptions.Env = append(dockerOptions.Env, "MONGO_INITDB_DATABASE="+db.InitDatabase)
	}
	if db.SupportTransaction {
		dockerOptions.Cmd = []string{"--replSet", "rs0", "--bind_ip_all", "--port", "27017"}
		dockerOptions.ExtraHosts = []string{"host.docker.internal:host-gateway"}
		dockerOptions.Mounts = []string{"mongo_data:/data/db", "mongo_config:/data/configdb"}
	}
	db.resource, err = pool.RunWithOptions(dockerOptions, func(config *docker.HostConfig) {
		// set AutoRemove to true so that stopped container goes away by itself
		config.AutoRemove = true
		config.RestartPolicy = docker.RestartPolicy{
			Name: "no",
		}
		config.PortBindings = map[docker.Port][]docker.PortBinding{}
		config.PortBindings["27017/tcp"] = []docker.PortBinding{
			{
				HostIP:   "0.0.0.0",
				HostPort: "27017",
			},
		}
	})
	if err != nil {
		return errors.StatusInternalServerError.Wrap(err, "Could not start resource")
	}

	db.Print()

	if db.SupportTransaction {
		if err := InitReplica(db.resource.Container.ID); err != nil {
			return err
		}
	}

	err = pool.Retry(func() error {
		port := db.resource.GetPort("27017/tcp")
		uri := fmt.Sprintf("mongodb://localhost:%s", port)
		if db.UserName != "" || db.Password != "" {
			uri = fmt.Sprintf("mongodb://%s:%s@localhost:%s", db.UserName, db.Password, port)
		}
		log.V2.Info().Str("Connecting to MongoDB...").StrKV("uri", uri).Emit()
		dbClient, err := mongo.Connect(
			context.TODO(),
			options.Client().ApplyURI(uri),
		)
		defer func() {
			if dbClient != nil {
				if err := dbClient.Disconnect(context.TODO()); err != nil {
					panic(err)
				}
			}
		}()
		if err != nil {
			return err
		}
		log.V2.Info().Str("Ping MongoDB...").StrKV("uri", uri).Emit()
		return dbClient.Ping(context.TODO(), nil)
	})

	if err != nil {
		return errors.StatusInternalServerError.Wrap(err, "Could not connect to mongo")
	}
	return nil
}

func (db *DB) Stop() error {
	if db.pool != nil && db.resource != nil {
		if err := db.pool.Purge(db.resource); err != nil {
			return errors.StatusInternalServerError.Wrap(err, "Could not purge resource")
		}
	}
	return nil
}

func (db *DB) Print() {
	if db.Silent {
		return
	}
	log.V2.Info().StrKVs("Version:", db.Version, "UserName:", db.UserName, "Password:", db.Password, "InitDatabase:", db.InitDatabase).Emit()
	b, err := json.MarshalIndent(db.resource.Container, "", "  ")
	if err != nil {
		panic(err)
	}
	log.V2.Info().StrKV("Docker:", string(b)).Emit()
}

// WithMongoDB helps on writing TestMain easier.
func WithMongoDB(db *DB, m *testing.M) int {
	// TODO: in long term, we should update the mongodb in CI to support replica.
	if _, isCI := os.LookupEnv("CI_WORKSPACE"); isCI {
		if db.SupportTransaction {
			os.Exit(0)
		}
		// CI has non-replica DB available already
	} else {
		if err := db.Start(); err != nil {
			panic(err)
		}
	}
	defer func() {
		if err := db.Stop(); err != nil {
			log.V2.Error().Str(fmt.Sprintf("DB stop: %+v", err)).Emit()
		}
	}()

	return m.Run()
}
