# config service error code
[Integration_multi]
#通用错误
123000 = ["ErrParams", "params err", "params err", "0"]
123001 = ["ErrZkFailed","zk lock failed","zk lock failed","0"]

#集成区
123101 = ["ErrReCreateIntegration", "recreate integration", "the release ticket has binded integration, can not recreate", "0"]
123102 = ["ErrCanNotFinishIntegration", "can not finish integration", "integration can not be finished", "0"]
123103 = ["ErrNoVersionInIntegration", "no version in integration", "there is no version created", "0"]
123104 = ["ErrNoDevTaskRelatedIntegration", "no related integration", "no related integration", "0"]
123105 = ["ErrStatusCheckFailed","integration status is invalid","integration status is invalid","0"]
123106 = ["ErrPreCheckIntegrationFailed","finish integration pre-check fail","finish integration pre-check fail","0"]
123108 = ["ErrAutoRemoveHasRunning","has running please wait","has running please wait","0"]
123109 = ["ErrAutoRemoveWithRevert","has revert dev task","has revert dev task","0"]
123110 = ["ErrAutoRemoveDevTask","exist some dev task can't remove","exist some dev task can't remove","0"]
123111 = ["ErrSendNotificationForMerge","send notification err","send notification err","0"]
123112 = ["ErrDifferentTargetBranchForOneChange", "different target branch for one change", "different target branch for one change", "0"]
#其他
123301 = ["ErrLockCheck","lock or finish integration check failed","lock or finish integration check failed","0"]
123302 = ["ErrUnLockCheck","unlock check failed","unlock check failed","0"]
123303 = ["ErrCreateVersion","create artifact verion failed","create artifact verion failed","0"]
123304 = ["ErrStartIntegrationCheck","start integration check failed","start integration check failed","0"]
123305 = ["ErrCreateMrCheck","create mr check failed","create mr check failed","0"]
123306 = ["ErrChangeNoDeploy","change have no deploy","change have no deploy","0"]
123107 = ["ErrOperateDev","dev can not change in the integration status","dev can not change in the integration status","0"]
123308 = ["ErrDevNoChanges","dev task have no changes","dev task have no changes","0"]
123309 = ["ErrSyncBranch","sync branch failed","sync branch failed","0"]
123310 = ["ErrSyncBranchConflict","sync branch failed because of conflict","sync branch failed because of conflict","0"]
123311 = ["ErrSyncBranchForbidden","sync branch failed because of permission","sync branch failed because of permission","0"]
123312 = ["ErrCreateIntegrationBranchByProtectedRule", "create integration branch by protected rule failed", "create integration branch by protected rule failed", "0"]





#底层git错误
123501 = ["ErrLockBranch","lock branch failed","lock branch failed","0"]
123502 = ["ErrUnLockBranch","unlock branch failed","unlock branch failed","0"]
123503 = ["ErrCreatBranch","create branch failed","create branch failed","0"]
123504 = ["ErrChangeBranchOfMR","change branch of mr failed","change branch of mr failed","0"]


#后端异常监控
123600 = ["ErrBindAndUnBindIntegration","","","0"]
123601 = ["ErrChangeIntegration","","","0"]
123602 = ["ErrAddDeleteChange","","","0"]
123603 = ["ErrUpdateIntegrationWorkBranch","","","0"]
123604 = ["ErrCreateReleaseTicket","","","0"]
123605 = ["ErrUpdateIntegration","","","0"]
123606 = ["ErrStartIntegration","","","0"]
123607 = ["ErrReOpenIntegration","","","0"]
123608 = ["ErrCancelOpenIntegration","","","0"]
123609 = ["ErrFinishIntegration","","","0"]
123610 = ["ErrTerminalIntegration","","","0"]
123611 = ["ErrGetIntegrationList","","","0"]
123612 = ["ErrGetIntegrationInfo","","","0"]
123613 = ["ErrGetIntegrationBasicInfo","","","0"]
123614 = ["ErrGetIntegrationByDevTask","","","0"]
123615 = ["ErrGetActiveReleaseBranches","","","0"]
123616 = ["ErrIntegrationStatusLock","","","0"]
123617 = ["ErrDBOperation","","","0"]
123618 = ["ErrGetDevTaskList","","","0"]
123619 = ["ErrGetChangeSkipDetails","","","0"]
123620 = ["ErrGetChange","","","0"]
123621 = ["ErrGetMrInfo","","","0"]
123622 = ["ErrGetCodeChangeGitlabMr","","","0"]
123623 = ["ErrGetCommitInfo","","","0"]
123624 = ["ErrGetMergeBase","","","0"]
123625 = ["ErrGetAllMergeRequestByBranch","","","0"]
123626 = ["ErrCompareRefs","","","0"]
123627 = ["ErrGetBranch","","","0"]
123628 = ["ErrCreateBranch","","","0"]
123629 = ["ErrProtectBranch","","","0"]
123630 = ["ErrDeleteBranch","","","0"]
123631 = ["ErrUpdateChange","","","0"]
123632 = ["ErrGetRealArchiveBranch","","","0"]
123633 = ["ErrGetLinkByVersion","","","0"]
123635 = ["ErrGetLatestVersionNumByIntegrationID","","","0"]
123636 = ["ErrGetVersionsByIntegrationId","","","0"]
123637 = ["ErrGetLatestVersionByIntegrationId","","","0"]
123638 = ["ErrGetDetailByIntegrationVersion","","","0"]
123639 = ["ErrGetSameBranchCheckItemsWithoutSelf","","","0"]
123640 = ["ErrGetSameBranchProjectsByIntegrationIds","","","0"]
123641 = ["ErrGetDevTasksByChangeIds","","","0"]
123642 = ["ErrGetRelatedDevTaskID","","","0"]
123643 = ["ErrGetDevTaskListByPage","","","0"]
123644 = ["ErrGetDevTaskInfo","","","0"]
123645 = ["ErrGetDevTaskListV2","","","0"]
123646 = ["ErrBatchGetDevWorkflow","","","0"]
123647 = ["ErrCloseDevTask","","","0"]
123648 = ["ErrGetDevTaskMetaInfoByIds","","","0"]
123649 = ["ErrGetReleaseTicketFromCache","","","0"]
123650 = ["ErrGetStartConfig","","","0"]
123651 = ["ErrGetFinishConfig","","","0"]
123652 = ["ErrGetReleaseTicketBasicInfoByIDs","","","0"]
123653 = ["ErrGetActiveReleaseTicketByProjects","","","0"]
123654 = ["ErrGetBranchingModelConfig","","","0"]
123656 = ["ErrGetMRCheckInfoByVersion","","","0"]
123657 = ["ErrCreateMRCheckVersion","","","0"]
123658 = ["ErrGetMRCheckListByVersion","","","0"]
123659 = ["ErrGetStartCommitAndTime","","","0"]
123660 = ["ErrGetEndCommitAndTime","","","0"]
123661 = ["ErrCanFinishIntegrationCheck","","","0"]
123662 = ["ErrSameBranchCheck","","","0"]
123663 = ["ErrCodeBehindCheck","","","0"]
123664 = ["ErrCodeMergeCheck","","","0"]
123665 = ["ErrControlPlaneFilterCheck","","","0"]
123666 = ["ErrSameBranchCheckForIntegration","","","0"]
123667 = ["ErrSameBranchCheckForDev","","","0"]
123668 = ["ErrSameBranchProjectsForRelease","","","0"]
123669 = ["ErrGetCodeMergeCheckDetail","","","0"]
123670 = ["ErrGetSameBranchCheckDetail","","","0"]
123671 = ["ErrCloseMergingDevTask","","","0"]
123672 = ["ErrCreateTargetBranchOfChanges","","","0"]
123673 = ["ErrSetCheckoutCommitByRepoId","","","0"]
123674 = ["ErrCheckIntegrationStatus","","","0"]
123675 = ["ErrCanMergeCheck","","","0"]
123676 = ["ErrGetChangeItemWithoutRevertByIntegrationId","","","0"]
123677 = ["ErrSetBranchUsed","","","0"]
123678 = ["ErrInitBranchUseLockRootPath","","","0"]
123679 = ["ErrCreateBranchUseLockRootPath","","","0"]
123680 = ["ErrRemoveBranchUsed","","","0"]
123681 = ["ErrGetAllBranchUsed","","","0"]
123682 = ["ErrGetAllChangeItemByDevTaskId","","","0"]
123683 = ["ErrReleaseTicketListenHandler","","","0"]
123684 = ["ErrGetChangeItemsByDevTaskId","","","0"]
123685 = ["ErrCheckIntegrationBranchUse","","","0"]
123686 = ["ErrGetTeamFlowByIntegrationId","","","0"]
123687 = ["ErrGetConfigByIntegrations","","","0"]
123688 = ["ErrGetBranchModelByReleaseTicketIds","","","0"]
123689 = ["ErrGetTeamFlowById","","","0"]
123690 = ["ErrRevertProjects","","","0"]
123691 = ["ErrGetRevertProjects","","","0"]
123692 = ["ErrUpdateReopenReason","","","0"]
