// Code generated by go generate; DO NOT EDIT.
package bits_err

var STORAGE = &struct {
	PermissionDenied          *BitsErr
	CreateKeyHasExist         *BitsErr
	PsmVersionOutdated        *BitsErr
	CallTccErr                *BitsErr
	InvalidTccControlPlane    *BitsErr
	InvalidReleaseTarget      *BitsErr
	GetTccDirPathFailed       *BitsErr
	TccNamespaceNotFound      *BitsErr
	ClearStoragePsmDataFailed *BitsErr
	DeployTargetIsEmpty       *BitsErr
	ReleaseTicketTerminated   *BitsErr
	NoConfigChange            *BitsErr
	ConfigIsDeploying         *BitsErr
	TCCENVAbormal             *BitsErr
	ConfigNameHasExistInTcc   *BitsErr
	DeployEnvIsEmpty          *BitsErr
	OnlineTccConfigIsDeleted  *BitsErr
	ErrDeleteTccChangeItem    *BitsErr
	ErrInitTccEnv             *BitsErr
	ErrImportMultiTccConfigs  *BitsErr
	ErrUpdateTccConfig        *BitsErr
	TccCheckFailed            *BitsErr
	TccConfigSizeMaxLimit     *BitsErr
}{
	&BitsErr{code: 134001, msg: "permission denied", tipMsg: "permission denied", slaFlag: 0},
	&BitsErr{code: 134002, msg: "the key to be created already exists", tipMsg: "the key to be created already exists", slaFlag: 0},
	&BitsErr{code: 134003, msg: "the psm version outdated", tipMsg: "the psm version outdated", slaFlag: 0},
	&BitsErr{code: 134004, msg: "call tcc service err", tipMsg: "call tcc service err", slaFlag: 0},
	&BitsErr{code: 134005, msg: "Invalid params, only online tcc control planes are supported", tipMsg: "Invalid params, only online tcc control planes are supported", slaFlag: 0},
	&BitsErr{code: 134006, msg: "Invalid params, BOE/PPE/online release targets cannot be duplicated for different release sources for the same PSM", tipMsg: "Invalid params, BOE/PPE/online release targets cannot be duplicated for different release sources for the same PSM", slaFlag: 0},
	&BitsErr{code: 134007, msg: "Get TCC dir path failed", tipMsg: "Get TCC dir path failed", slaFlag: 0},
	&BitsErr{code: 134008, msg: "Can not found TCC namespace on current control plane", tipMsg: "Can not found TCC namespace on current control plane", slaFlag: 0},
	&BitsErr{code: 134009, msg: "Failed to clear TCC configuration changes", tipMsg: "Failed to clear TCC configuration changes", slaFlag: 0},
	&BitsErr{code: 134010, msg: "TCC Deploy target is empty", tipMsg: "TCC Deploy target is empty", slaFlag: 0},
	&BitsErr{code: 134011, msg: "The release ticket has terminated", tipMsg: "The release ticket has terminated", slaFlag: 0},
	&BitsErr{code: 134012, msg: "TCC PSM no config changed", tipMsg: "TCC PSM no config changed", slaFlag: 0},
	&BitsErr{code: 134013, msg: "TCC config is deploying, please check", tipMsg: "TCC config is deploying, please check", slaFlag: 0},
	&BitsErr{code: 134014, msg: "TCC env is abnormal", tipMsg: "please check env exist and status is normal", slaFlag: 0},
	&BitsErr{code: 134015, msg: "Config with the same name already exists in the current region of TCC", tipMsg: "Please modify the config name", slaFlag: 0},
	&BitsErr{code: 134016, msg: "Tcc deploy env is empty", tipMsg: "Tcc deploy env is empty", slaFlag: 0},
	&BitsErr{code: 134017, msg: "Online TCC config is deleted, update is forbidden", tipMsg: "Online TCC config is deleted, update is forbidden", slaFlag: 0},
	&BitsErr{code: 134018, msg: "Err DeleteTccChangeItemn", tipMsg: "Err DeleteTccChangeItemn", slaFlag: 0},
	&BitsErr{code: 134019, msg: "Err InitTccEnv", tipMsg: "Err InitTccEnv", slaFlag: 0},
	&BitsErr{code: 134020, msg: "Err ImportMultiTccConfigs", tipMsg: "Err ImportMultiTccConfigs", slaFlag: 0},
	&BitsErr{code: 134021, msg: "Err UpdateTccConfig", tipMsg: "Err UpdateTccConfig", slaFlag: 0},
	&BitsErr{code: 134022, msg: "Tcc check failed", tipMsg: "Tcc check failed", slaFlag: 0},
	&BitsErr{code: 134023, msg: "Tcc config size exceeds the maximum limit", tipMsg: "Tcc config size exceeds the maximum limit", slaFlag: 0},
}
