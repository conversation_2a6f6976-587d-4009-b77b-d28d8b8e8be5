// Code generated by go generate; DO NOT EDIT.
package bits_err

var BITSPERMISSION = &struct {
	ErrPermissionCheckFailure   *BitsErr
	ErrPermissionApplyFailure   *BitsErr
	ErrPermissionServiceFailure *BitsErr
}{
	&BitsErr{code: 137001, msg: "check permission failure", tipMsg: "check permission failure", slaFlag: 1},
	&BitsErr{code: 137002, msg: "apply permission failure", tipMsg: "apply permission failure", slaFlag: 1},
	&BitsErr{code: 137003, msg: "permission service error,", tipMsg: "permission service error", slaFlag: 1},
}
