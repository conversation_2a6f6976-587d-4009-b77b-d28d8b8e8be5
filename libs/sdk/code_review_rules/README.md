# CaC - code review 规则配置

| 文件                       | 功能              |
|--------------------------|-----------------|
| .bits/config.yml         | 仓库级别人员配置        |
| /foo/bar/OWNERS.bits.yml | 目录级别人员与 CR 规则配置 |

# 文件说明

## .bits/config.yml

格式如下：

```yaml
code_review:
  mode: strict
  maintainers:
    - liutao.rs
    - <EMAIL>
  reviewers_required_amount: 2
  maintainers_required_approvals: 3
  pick_strategy: pick_first
```

| 参数                             | 说明                                                                |
|--------------------------------|-------------------------------------------------------------------|
| mode                           | 枚举, 可选值有 `strict` 和 `loose`，表示严格模式或宽松模式                           |
| maintainers                    | 仓库的维护人员，支持使用邮箱或者邮箱前缀                                              |
| reviewers_required_amount      | 发起 Code Review，reviewers 最少参与人数                                   |
| maintainers_required_approvals | 发起 Code Review，maintainers 最少需要参与 review 的人数                <br/> |
| pick_strategy                  | 枚举，可选值有: `pick_first` 和 `pick_all`，表示 CR 规则匹配策略                   |

## foo/bar/OWNERS.bits.yml

格式如下:

```yaml
code_review_config:
  maintainers:
    - <EMAIL>
    - <EMAIL>

code_review_rule:
  files:
    - pattern: .*.java
      reviewers:
        - a
        - <EMAIL>
      required_approvals: 1
  target_branches:
    - pattern: release_.*
      reviewers:
        - <EMAIL>
        - <EMAIL>
      required_approvals: 1
```

| 参数                                                             | 说明                                         |
|----------------------------------------------------------------|--------------------------------------------|
| code_review_config.maintainers                                 | 文件所在目录的 maintainers， 支持使用邮箱和邮箱前缀           |
| code_review_rule.files                                         | CR 文件规则                                    |
| code_review_rule.target_branches                               | CR 目标分支规则                                  |
| code_review_rule.(files \| target_branches).pattern            | 文件名匹配规则，正则表达式                              |
| code_review_rule.(files \| target_branches).reviewers          | 匹配到规则后，自动邀请的 reviewers                     |
| code_review_rule.(files \| target_branches).required_approvals | 匹配到规则后，至少通过 review 的人数, 注意此字段最小值是 1，系统自动选取 |
