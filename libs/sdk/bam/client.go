package bam

import (
	bapi "code.byted.org/bam/bapi_hertz/service/bapi_service"
	"sync"
)

var c bapi.Client
var once sync.Once

func GetClient() bapi.Client {
	if c == nil {
		once.Do(func() {
			c = NewClient()
		})
	}
	return c
}
func NewClient() bapi.Client {
	host := "https://byteapi.byted.org"
	aksk := bapi.AkSK{
		Ak:  "Bits",
		Key: "MOR42StHFl5UEh9u",
		Iv:  "UUztItv8TwUUU36P",
	}
	username := "yourusername"

	// 1. 创建client
	client, err := bapi.NewBapiServiceClient(host,
		bapi.WithTLS(),                // 支持https
		bapi.WithErrorDeciderMV(),     // 检测BAM接口响应错误
		bapi.WithAkSK(aksk, username)) // 配置AKSK
	if err != nil {
		panic(err)
	}
	return client
}
