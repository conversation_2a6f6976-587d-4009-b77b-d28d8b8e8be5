package kitexmw

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/libs/bits_err"

	"code.byted.org/kite/kitex/pkg/endpoint"
	"code.byted.org/kite/kitex/pkg/kerrors"
	"gorm.io/gorm"
)

// ConvertErr2BitsErr covert error to bits_err
// Notice: 这个中间件建议放在所有中间件的最后面
func ConvertErr2BitsErr(next endpoint.Endpoint) endpoint.Endpoint {
	return func(ctx context.Context, request, response interface{}) error {
		err := next(ctx, request, response)

		// gorm.ErrRecordNotFound 转换成 bits_err
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return bits_err.COMMON.ErrRecordNotFound
		}

		// kitex 错误 unwarp 成 bits_err
		if v, ok := err.(*kerrors.DetailedError); ok {
			if cause := v.Unwrap(); cause != nil {
				if bitsErr, ok := cause.(bits_err.Err); ok {
					return bitsErr
				}
			}
		}

		return err
	}
}
