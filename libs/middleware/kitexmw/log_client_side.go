/**
 * @Date: 2023/4/10
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package kitexmw

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/mtctx"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/byted/kitexutil"
	"code.byted.org/kite/kitex/pkg/endpoint"
	"code.byted.org/kite/kitex/pkg/rpcinfo"
)

// LogClientSideRequestResponse 记录 rpc client 的日志
// usage:
//
//	options := []client.Option{client.WithMiddleware(LogClientSideRequestResponse)}
//	MustNewClient(PSM, options...)
var LogClientSideRequestResponse endpoint.Middleware = func(next endpoint.Endpoint) endpoint.Endpoint {
	return func(ctx context.Context, req, resp interface{}) (err error) {
		caller, _ := kitexutil.GetCaller(ctx)
		callee, _ := GetCallee(ctx)
		fromMethod, _ := kitexutil.GetCallerHandlerMethod(ctx)
		toMethod, _ := kitexutil.GetMethod(ctx)

		now := time.Now()
		err = next(ctx, req, resp)
		cost := time.Since(now)
		tenancy := mtctx.GetTenancy(ctx)

		defer func() {
			logger := log.V2.Info()
			if err != nil {
				log.V2.Warn()
			}
			logger.With(ctx).
				Str(fmt.Sprintf("[client call chain] %s (%s) -> %s (%s)", caller, fromMethod, callee, toMethod)).
				KVs("request", jsonify(req), "response", jsonify(resp), "cost", cost.String(), "tenancy", tenancy).
				Error(err).
				Emit()
		}()

		return err
	}
}

func GetCallee(ctx context.Context) (string, bool) {
	defer func() { recover() }()

	ri := rpcinfo.GetRPCInfo(ctx)
	if ri == nil {
		return "", false
	}
	return ri.To().ServiceName(), true
}
