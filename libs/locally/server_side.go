/**
 * @Date: 2022/9/14
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package locally

import (
	"net"
	"os"

	"code.byted.org/gopkg/env"
	"github.com/cloudwego/kitex/pkg/registry"
	"github.com/cloudwego/kitex/server"
	"github.com/kitex-contrib/registry-consul"
)

func UseConsulServiceDiscoveryAtServerSide() []server.Option {
	if env.IsBoe() || env.IsPPE() || env.IsProduct() {
		return make([]server.Option, 0)
	}

	consulAddr := os.Getenv("CONSUL_ADDRESS")
	r, err := consul.NewConsulRegister(consulAddr)
	if err != nil {
		panic(err)
	}

	serviceAddr, _ := net.ResolveTCPAddr("tcp", os.Getenv("SERVICE_ADDRESS"))
	options := []server.Option{
		server.WithRegistry(r),
		server.WithRegistryInfo(&registry.Info{ServiceName: env.PSM(), Weight: 1}),
		server.WithServiceAddr(serviceAddr),
	}
	return options
}
