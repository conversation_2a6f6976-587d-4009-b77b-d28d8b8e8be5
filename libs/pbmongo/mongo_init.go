package pbmongo

import (
	"context"
	"fmt"
	"os"
	"time"

	"code.byted.org/bytedoc/mongo-go-driver/event"
	"code.byted.org/bytedoc/mongo-go-driver/mongo"
	"code.byted.org/bytedoc/mongo-go-driver/mongo/options"
	"code.byted.org/bytedoc/mongo-go-driver/mongo/readconcern"
	"code.byted.org/bytedoc/mongo-go-driver/mongo/readpref"
	"code.byted.org/bytedoc/mongo-go-driver/mongo/writeconcern"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
)

type MongoCfg struct {
	URI            string        `yaml:"uri"`
	DBPsm          string        `yaml:"db_psm"`
	DBName         string        `yaml:"db_name"`
	Timeout        time.Duration `yaml:"timeout"` // operation timeout
	ConnectTimeout time.Duration `yaml:"connect_timout"`
	Write          bool          `yaml:"write"`
}

func CreateMongoDB(cfg *MongoCfg) (*mongo.Database, error) {
	option, err := getOptions(cfg)
	if err != nil {
		return nil, err
	}

	cli, err := mongo.NewClient(option)
	if err != nil {
		log.V2.Error().Str("mongodb client init failed").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().Str("mongodb client init success").Emit()

	ctx, cancel := context.WithTimeout(context.Background(), cfg.ConnectTimeout)
	defer cancel()

	if err = cli.Connect(ctx); err != nil {
		log.V2.Error().Str("mongodb client connection failed").Error(err).Emit()
		return nil, err
	}
	log.V2.Info().With(ctx).Str("mongodb client connection success").Emit()

	// Ping once even Ping() reduces application resilience to check the config correctness.
	if err := cli.Ping(ctx, nil); err != nil {
		log.V2.Error().Error(err).Str("mongodb client Ping failed").Emit()
		return nil, err
	}
	log.V2.Info().Str("mongodb client Ping success").Emit()

	return cli.Database(cfg.DBName), nil
}

func getMongoURI(cfg *MongoCfg) string {
	if cfg.URI != "" {
		return cfg.URI
	}
	var uri string
	if _, isCI := os.LookupEnv("CI_WORKSPACE"); isCI {
		// in CI
		uri = "mongodb://mongo:27017"
	} else if env.IsProduct() || env.IsBoe() {
		// BOE / TCE
		uri = fmt.Sprintf("mongodb+consul+token://%s/%s?authSource=%s", cfg.DBPsm, cfg.DBName, cfg.DBName)
	} else {
		// local
		uri = "mongodb://127.0.0.1:27017/"
	}
	log.V2.Info().KV("mongodb uri", uri).Emit()
	return uri
}

func getOptions(cfg *MongoCfg) (*options.ClientOptions, error) {
	uri := getMongoURI(cfg)
	cmdMonitor := &event.CommandMonitor{
		Started: func(_ context.Context, event *event.CommandStartedEvent) {
			log.V2.Info().KV("database name", event.DatabaseName).KV("collection name", event.CollectionName).
				KV("command name", event.CommandName).KV("command", event.Command).Emit()
		},
	}

	r, err := ProtoSupportRegistry()
	if err != nil {
		// This should never happen
		log.V2.Error().Str("failed to create ProtoSupportRegistry").Emit()
		return nil, err
	}
	option := options.Client().ApplyURI(uri).SetMonitor(cmdMonitor).SetRegistry(r)
	// local, return the instance's most recent data
	option.SetReadConcern(readconcern.Local())

	if cfg.Write {
		// write majority confirmation
		wc := writeconcern.New(writeconcern.WMajority())
		option.SetWriteConcern(wc)

		// read primary db
		option.SetReadPreference(readpref.Primary())
	} else {
		// read secondary db
		option.SetReadPreference(readpref.Secondary())
	}

	return option, nil
}
