load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "handler",
    srcs = [
        "echo.go",
        "path.go",
        "ping.go",
        "query.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/libs/hertz/util/demo_service/biz/handler",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/hertzutil/demo:demo_go_proto",
        "@org_byted_code_middleware_hertz//pkg/app",
        "@org_byted_code_middleware_hertz//pkg/common/utils",
        "@org_byted_code_middleware_hertz_ext_v2//binding",
    ],
)
