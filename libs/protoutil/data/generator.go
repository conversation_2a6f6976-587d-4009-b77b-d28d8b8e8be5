package data

import (
	"fmt"
	"github.com/golang/protobuf/ptypes/wrappers"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/types/dynamicpb"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"time"
)

type Option int32

const (
	Option_Default   = 0
	Option_Map_Key   = 1
	Option_Map_Value = 2
)

type Data interface {
	NewInt32() int32
	NewInt64() int64
	NewUint32() uint32
	NewUint64() uint64
	NewFloat32() float32
	NewFloat64() float64
	NewBytes() []byte
	NewString() string
	NewBool() bool
	NewTime() time.Time
	NewDuration() time.Duration

	SetOption(Option)
}

type StringStack struct {
	stack []string
	seen  map[string]bool
}

func NewStringStack() *StringStack {
	return &StringStack{
		stack: make([]string, 0),
		seen:  make(map[string]bool),
	}
}

func (s *StringStack) Push(str string) bool {
	if !s.seen[str] {
		s.stack = append(s.stack, str)
		s.seen[str] = true
		return true
	} else {
		return false
	}
}

func (s *StringStack) Pop() string {
	if len(s.stack) == 0 {
		return "" // Or handle empty stack as needed
	}
	top := s.stack[len(s.stack)-1]
	s.stack = s.stack[:len(s.stack)-1]
	delete(s.seen, top)
	return top
}

func (s *StringStack) IsEmpty() bool {
	return len(s.stack) == 0
}

type Generator struct {
	Data  Data
	Stack *StringStack
}

func NewGenerator(data Data) *Generator {
	return &Generator{
		Data:  data,
		Stack: NewStringStack(),
	}
}

func (g *Generator) Generate(in proto.Message) (proto.Message, error) {
	desc := in.ProtoReflect().Descriptor()
	dm, err := g.NewDynamicProto(desc)
	if err != nil {
		return nil, err
	}

	//out := reflect.New(reflect.ValueOf(in).Elem().Type()).Interface().(proto.Message)
	if in.ProtoReflect().IsValid() {
		out := proto.Clone(in)
		proto.Merge(out, dm)
		return out, nil
	}
	return dm, nil
}

func (g *Generator) NewDynamicProto(desc protoreflect.MessageDescriptor) (*dynamicpb.Message, error) {
	return g.newDynamicProto(desc)
}

func (g *Generator) getFieldValue(fd protoreflect.FieldDescriptor) (protoreflect.Value, error) {
	switch fd.FullName() {
	case "google.protobuf.Timestamp":
		t := timestamppb.New(g.Data.NewTime())
		return protoreflect.ValueOf(t), nil
	case "google.protobuf.Duration":
		d := durationpb.New(g.Data.NewDuration())
		return protoreflect.ValueOf(d), nil
	case "google.protobuf.Any":
		s, _ := anypb.New(&wrappers.StringValue{Value: "<PROTOBUF_ANY>"})
		return protoreflect.ValueOf(s), nil
	case "google.protobuf.Value":
		s := structpb.NewStringValue("<PROTOBUF_VALUE>")
		return protoreflect.ValueOf(s), nil
	}
	switch fd.Kind() {
	case protoreflect.Int32Kind:
		return protoreflect.ValueOfInt32(g.Data.NewInt32()), nil
	case protoreflect.Int64Kind:
		return protoreflect.ValueOfInt64(g.Data.NewInt64()), nil
	case protoreflect.Sint32Kind:
		return protoreflect.ValueOfInt32(g.Data.NewInt32()), nil
	case protoreflect.Sint64Kind:
		return protoreflect.ValueOfInt64(g.Data.NewInt64()), nil
	case protoreflect.Uint32Kind:
		return protoreflect.ValueOfUint32(g.Data.NewUint32()), nil
	case protoreflect.Uint64Kind:
		return protoreflect.ValueOfUint64(g.Data.NewUint64()), nil
	case protoreflect.Fixed32Kind:
		return protoreflect.ValueOfUint32(g.Data.NewUint32()), nil
	case protoreflect.Fixed64Kind:
		return protoreflect.ValueOfUint64(g.Data.NewUint64()), nil
	case protoreflect.Sfixed32Kind:
		return protoreflect.ValueOfInt32(g.Data.NewInt32()), nil
	case protoreflect.Sfixed64Kind:
		return protoreflect.ValueOfInt64(g.Data.NewInt64()), nil
	case protoreflect.FloatKind:
		return protoreflect.ValueOfFloat32(g.Data.NewFloat32()), nil
	case protoreflect.DoubleKind:
		return protoreflect.ValueOfFloat64(g.Data.NewFloat64()), nil
	case protoreflect.StringKind:
		return protoreflect.ValueOfString(g.Data.NewString()), nil
	case protoreflect.BoolKind:
		return protoreflect.ValueOfBool(g.Data.NewBool()), nil
	case protoreflect.EnumKind:
		return protoreflect.ValueOfEnum(g.chooseEnumValue(fd.Enum().Values())), nil
	case protoreflect.BytesKind:
		return protoreflect.ValueOfBytes(g.Data.NewBytes()), nil
	case protoreflect.MessageKind:
		msg := fd.Message()
		switch msg.FullName() {
		case "google.protobuf.Timestamp":
			t := timestamppb.New(g.Data.NewTime())
			return protoreflect.ValueOf(t.ProtoReflect()), nil
		case "google.protobuf.Duration":
			d := durationpb.New(g.Data.NewDuration())
			return protoreflect.ValueOf(d.ProtoReflect()), nil
		case "google.protobuf.Any":
			s, _ := anypb.New(&wrappers.StringValue{Value: "<PROTOBUF_ANY>"})
			return protoreflect.ValueOf(s.ProtoReflect()), nil
		case "google.protobuf.Value":
			s := structpb.NewStringValue("<PROTOBUF_VALUE>")
			return protoreflect.ValueOf(s.ProtoReflect()), nil
		}
		// process recursively (if we have more stacks to give...)
		if g.Stack.Push(string(msg.FullName())) {
			defer g.Stack.Pop()
			rm, err := g.newDynamicProto(msg)
			if err != nil {
				return protoreflect.Value{}, err
			}
			return protoreflect.ValueOfMessage(rm), nil
		}
		return protoreflect.Value{}, nil
	default:
		return protoreflect.Value{}, fmt.Errorf("unexpected type: %v", fd.Kind())
	}
}

func (g *Generator) chooseEnumValue(values protoreflect.EnumValueDescriptors) protoreflect.EnumNumber {
	ln := values.Len()
	if ln <= 1 {
		return 0
	}

	value := values.Get(1)
	return value.Number()
}
func (g *Generator) chooseOneOfField(oneOf protoreflect.OneofDescriptor) protoreflect.FieldDescriptor {
	return oneOf.Fields().Get(0)
}

func (g *Generator) newDynamicProto(desc protoreflect.MessageDescriptor) (*dynamicpb.Message, error) {
	// decide which fields in each OneOf will be populated in advance
	populatedOneOfField := map[protoreflect.Name]protoreflect.FieldNumber{}
	oneOfs := desc.Oneofs()
	for i := 0; i < oneOfs.Len(); i++ {
		oneOf := oneOfs.Get(i)
		populatedOneOfField[oneOf.Name()] = g.chooseOneOfField(oneOf).Number()
	}

	dm := dynamicpb.NewMessage(desc)
	fds := desc.Fields()
	for k := 0; k < fds.Len(); k++ {
		fd := fds.Get(k)

		// If a field is in OneOf, check if the field should be populated
		if oneOf := fd.ContainingOneof(); oneOf != nil {
			populatedFieldNum := populatedOneOfField[oneOf.Name()]
			if populatedFieldNum != fd.Number() {
				continue
			}
		}

		if fd.IsList() {
			list := dm.Mutable(fd).List()
			n := 1
			for i := 0; i < n; i++ {
				value, err := g.getFieldValue(fd)
				if err != nil {
					return nil, err
				}
				if value.Interface() != nil {
					list.Append(value)
				}
			}
			dm.Set(fd, protoreflect.ValueOfList(list))
			continue
		}
		if fd.IsMap() {
			mp := dm.Mutable(fd).Map()
			n := 1
			for i := 0; i < n; i++ {
				g.Data.SetOption(Option_Map_Key)
				key, err := g.getFieldValue(fd.MapKey())
				g.Data.SetOption(Option_Default)
				if err != nil {
					return nil, err
				}
				g.Data.SetOption(Option_Map_Value)
				value, err := g.getFieldValue(fd.MapValue())
				g.Data.SetOption(Option_Default)
				if err != nil {
					return nil, err
				}
				if key.Interface() != nil && value.Interface() != nil {
					mp.Set(protoreflect.MapKey(key), value)
				}
			}
			dm.Set(fd, protoreflect.ValueOfMap(mp))
			continue
		}

		value, err := g.getFieldValue(fd)
		if err != nil {
			return nil, err
		}
		if value.Interface() != nil {
			dm.Set(fd, value)
		}
	}

	return dm, nil
}
