package repo_upgrades

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestAndroidOneKit(t *testing.T) {
	t.Run("NewAndroidOneKitFromAndroidAssociateArtifacts", func(t *testing.T) {
		artifacts := []*AndroidAssociateArtifact{
			{
				RepoId:     51020,
				GroupId:    "com.bytedance.ai",
				ArtifactId: "multimodalkit-common-tob",
				ModuleName: ":multi-modal-kit:common",
				Version:    "0.0.1-alpha.1",
			},
		}
		onekit := NewAndroidOneKitFromAndroidAssociateArtifacts(artifacts)
		assert.Equal(t, 1, len(onekit.Dependencies))
		assert.Equal(t, "com.bytedance.ai", onekit.Dependencies[0].GroupId)
		assert.Equal(t, ":multi-modal-kit:common", onekit.Dependencies[0].ComponentName)
		assert.Equal(t, 1, len(onekit.Dependencies[0].Flavors))
		assert.Equal(t, "multimodalkit-common-tob", onekit.Dependencies[0].Flavors[0].ArtifactId)
		assert.Equal(t, "0.0.1-alpha.1", onekit.Dependencies[0].Flavors[0].Version)

		artifacts = []*AndroidAssociateArtifact{
			{
				RepoId:     18396,
				GroupId:    "com.component.demo",
				ArtifactId: "detail-tob",
				ModuleName: ":detail",
				Version:    "0.0.1.m-alpha.12",
			},
			{
				RepoId:     18396,
				GroupId:    "com.component.demo",
				ArtifactId: "detail-tob",
				ModuleName: ":detail",
				Version:    "0.0.1.d-alpha.12",
			},
			{
				RepoId:     18396,
				GroupId:    "com.component.demo",
				ArtifactId: "detail-tob",
				ModuleName: ":detail",
				Version:    "0.0.1.t-alpha.12",
			},
		}
		onekit = NewAndroidOneKitFromAndroidAssociateArtifacts(artifacts)
		assert.Equal(t, 1, len(onekit.Dependencies))
		assert.Equal(t, "com.component.demo", onekit.Dependencies[0].GroupId)
		assert.Equal(t, ":detail", onekit.Dependencies[0].ComponentName)
		assert.Equal(t, 3, len(onekit.Dependencies[0].Flavors))
		assert.Equal(t, "detail-tob", onekit.Dependencies[0].Flavors[0].ArtifactId)
		assert.Equal(t, "0.0.1.m-alpha.12", onekit.Dependencies[0].Flavors[0].Version)
		assert.Equal(t, "detail-tob", onekit.Dependencies[0].Flavors[1].ArtifactId)
		assert.Equal(t, "0.0.1.d-alpha.12", onekit.Dependencies[0].Flavors[1].Version)
		assert.Equal(t, "detail-tob", onekit.Dependencies[0].Flavors[2].ArtifactId)
		assert.Equal(t, "0.0.1.t-alpha.12", onekit.Dependencies[0].Flavors[2].Version)
	})
}
