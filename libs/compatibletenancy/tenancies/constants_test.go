/**
 * @Date: 2023/11/22
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package tenancies

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTenancy(t *testing.T) {
	t.Run("FromString dcar", func(t *testing.T) {
		s := "dcar"

		got := FromString(s).Must()
		want := TenancyDcarlife

		if got != want {
			t.Errorf("FromString() = %v, want %v", got, want)
		}
	})

	t.Run("FromString", func(t *testing.T) {
		s := ""

		got := FromString(s).IsNil()
		want := true

		if got != want {
			t.Errorf("FromString() = %v, want %v", got, want)
		}
	})

	t.Run("FromUsernameOrEmail", func(t *testing.T) {
		username := "liutao.rs"
		tenancy := FromUsernameOrEmail(username)
		assert.Equal(t, tenancy, TenancyBytedance)

		username = "liutao.rs__dcar"
		tenancy = FromUsernameOrEmail(username)
		assert.Equal(t, tenancy, TenancyDcarlife)

		email := "<EMAIL>"
		tenancy = FromUsernameOrEmail(email)
		assert.Equal(t, tenancy, TenancyBytedance)

		email = "<EMAIL>"
		tenancy = FromUsernameOrEmail(email)
		assert.Equal(t, tenancy, TenancyDcarlife)
	})
}
