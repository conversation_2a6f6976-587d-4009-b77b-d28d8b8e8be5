/**
 * @Date: 2023/11/7
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package emails

import "testing"

func TestTrimSuffix(t *testing.T) {
	type args struct {
		email string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "empty string return empty",
			args: args{
				email: "",
			},
			want: "",
		},
		{
			name: "username is email, return",
			args: args{
				email: "ltoddy",
			},
			want: "ltoddy",
		},
		{
			name: "normal email",
			args: args{
				email: "<EMAIL>",
			},
			want: "ltoddy",
		},
		{
			name: "dcar email",
			args: args{
				email: "<EMAIL>",
			},
			want: "liutao.rs__dcar",
		},
		{
			name: "bytedance email",
			args: args{
				email: "<EMAIL>",
			},
			want: "liutao.rs",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := TrimSuffix(tt.args.email); got != tt.want {
				t.<PERSON>rrorf("TrimSuffix() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWithSuffix(t *testing.T) {
	type args struct {
		username string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "empty string return empty",
			args: args{
				username: "",
			},
			want: "",
		},
		{
			name: "username is email",
			args: args{
				username: "<EMAIL>",
			},
			want: "<EMAIL>",
		},
		{
			name: "normal username",
			args: args{
				username: "liutao.rs",
			},
			want: "<EMAIL>",
		},
		{
			name: "known tenancy username",
			args: args{
				username: "liutao.rs__dcar",
			},
			want: "<EMAIL>",
		},
		{
			name: "unknown tenancy username",
			args: args{
				username: "liutal.rs__unknown",
			},
			want: "<EMAIL>",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := WithSuffix(tt.args.username); got != tt.want {
				t.Errorf("WithSuffix() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidate(t *testing.T) {
	type args struct {
		email string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "empty string",
			args: args{
				email: "",
			},
			want: false,
		},
		{
			name: "normal email",
			args: args{
				email: "<EMAIL>",
			},
			want: true,
		},
		{
			name: "normal username",
			args: args{
				email: "liutao.rs",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Validate(tt.args.email); got != tt.want {
				t.Errorf("Validate() = %v, want %v", got, tt.want)
			}
		})
	}
}
