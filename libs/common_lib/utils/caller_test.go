package utils

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

func Test_MCallWithLimit(t *testing.T) {
	type args struct {
		ctx             context.Context
		in              []int
		call            CallFunc[int, string]
		concurrentLimit int
	}
	tests := []struct {
		name    string
		args    args
		mocks   func()
		want    []string
		wantErr bool
	}{
		{
			name: "Test concurrent limit zero",
			args: args{
				ctx:             context.Background(),
				in:              []int{1, 2, 3},
				call:            func(ctx context.Context, input int) (string, error) { return fmt.Sprintf("%d", input), nil },
				concurrentLimit: 0,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "Test successful call",
			args: args{
				ctx:             context.Background(),
				in:              []int{1, 2, 3},
				call:            func(ctx context.Context, input int) (string, error) { return fmt.Sprintf("%d", input), nil },
				concurrentLimit: 2,
			},
			want:    []string{"1", "2", "3"},
			wantErr: false,
		},
		{
			name: "Test call with error",
			args: args{
				ctx:             context.Background(),
				in:              []int{1, 2, 3},
				call:            func(ctx context.Context, input int) (string, error) { return "", fmt.Errorf("error") },
				concurrentLimit: 2,
			},
			want:    []string{"", "", ""},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockey.PatchConvey(tt.name, t, func() {
				if tt.mocks != nil {
					tt.mocks()
				}
				got, err := MCallWithLimit(tt.args.ctx, tt.args.in, tt.args.call, tt.args.concurrentLimit)
				So(err != nil, ShouldEqual, tt.wantErr)
				So(got, ShouldResemble, tt.want)
			})
		})
	}
}
