package utils

import (
	"code.byted.org/lang/gg/gresult"
	"errors"
	"testing"
)

// TestRetryable
// @title TestRetryable
// @description 正常流程
func TestRetryable(t *testing.T) {
	data := 1
	NewRetryable(func(t int) gresult.R[any] {
		data += 1
		return gresult.Err[any](errors.New("fail"))
	}, func(r gresult.R[any]) bool {
		if r.Is<PERSON>rr() && r.Err().Error() == "fail" {
			return true
		}
		return false
	}, func(i int) gresult.R[bool] {
		// 补偿逻辑执行成功
		data = 0
		return gresult.OK(true)
	}).Retry(data)

	if data != 1 {
		t.<PERSON><PERSON><PERSON>("normal flow error")
	}
}

// TestRetryableCheckNotPass
// @title TestRetryableCheckNotPass
// @description 检查项没有通过直接执行一次原始逻辑结束
func TestRetryableCheckNotPass(t *testing.T) {
	data := 1
	NewRetryable(func(t int) gresult.R[any] {
		data += 1
		return gresult.Err[any](errors.New("fail"))
	}, func(r gresult.R[any]) bool {
		return false
	}, func(i int) gresult.R[bool] {
		// 补偿逻辑执行成功
		data = 0
		return gresult.OK(true)
	}).Retry(data)

	if data != 2 {
		t.Errorf("retryable check not pass error")
	}
}

// TTestRetryableCheckNotPassWithOriginalResp
// @title TestRetryableCheckNotPassWithOriginalResp
// @description 检查项没有通过直接执行一次原始逻辑结束, 最终结果返回原始数据
func TestRetryableCheckNotPassWithOriginalResp(t *testing.T) {
	data := 1
	res := NewRetryable(func(t int) gresult.R[any] {
		data += 1
		return gresult.Err[any](errors.New("fail"))
	}, func(r gresult.R[any]) bool {
		return false
	}, func(i int) gresult.R[bool] {
		// 补偿逻辑执行成功
		data = 0
		return gresult.OK(true)
	}).Retry(data)

	resp, err := res.Get()

	if resp != nil {
		t.Errorf("retryable check not pass with original resp error")
		return
	}
	if err == nil {
		t.Errorf("retryable check not pass with original resp error")
		return
	}
	if err.Error() != "fail" {
		t.Errorf("retryable check not pass with original resp error")
		return
	}
}
