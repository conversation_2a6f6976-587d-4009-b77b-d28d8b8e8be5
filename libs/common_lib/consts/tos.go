package consts

const (
	// boe
	Tos_BitsBoeBucket          = "bits"
	Tos_BitsBoeBucketAccessKey = "CJFBGXL1PXNQJ2X34U2K"

	// 线上
	Tos_BitsBucket          = "bits-normal"
	Tos_BitsBucketAccessKey = "B7AZN0CF3HE2WSRD5LEU"

	// ttclient-android 的 tos
	Tos_AndroidBucket          = "ttclient-android-crashinfo"
	Tos_AndroidBucketAccessKey = "NTD082DDQDJZ2TTS38KS"
	Tos_AndroidBaseUrl         = "http://tosv.byted.org/obj/ttclient-android-crashinfo/"

	//tos bucket1
	Tos_ArchBucket          = "toutiao.ios.arch"
	Tos_ArchBucketAccessKey = "MJMETJODXZF7FZLFY3VT"
	Tos_ArchBaseUrl         = "http://tosv.byted.org/obj/toutiao.ios.arch/"
	ToolModuleName          = "mpaas/tool"

	//tos bucket2
	Tos_BinaryBucket          = "iosbinary"
	Tos_BinaryBucketAccessKey = "UK8JN1U3GCZ2EVKYJF1V"

	Tos_IOSFrameworkBucket    = "staticanalysisresult"
	Tos_IOSFrameworkAccessKey = "C5V4TROQGXMCTPXLIJFT"
)
