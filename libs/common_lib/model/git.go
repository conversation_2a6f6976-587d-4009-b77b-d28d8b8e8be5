package model

import (
	"time"

	"code.byted.org/codebase/sdk"
)

type CodebaseEventType string

const (
	EventTypeChange      CodebaseEventType = "change"
	EventTypePush        CodebaseEventType = "push"
	EventTypeCheckRun    CodebaseEventType = "check_run"
	EventTypeCheckSuite  CodebaseEventType = "change_check_suite"
	EventTypeApp         CodebaseEventType = "app"  // 主要是 App 的安装卸载，App 可以在安装、卸载的时候创建一些资源
	EventTypeSkip        CodebaseEventType = "skip" // 事件发生在用户 skip check run 或者 check suite 时
	EventTypeMultiReview CodebaseEventType = "multi_review"
)

// 消息体 https://bytedance.feishu.cn/docs/doccne8Fs3ZcW0EVIUtZ15lqLXg
type GitHookMessage struct {
	EventType CodebaseEventType `json:"event_type"`
	Body      string            `json:"body"`
}

type ChangeAction string

const (
	ChangeActionCreate  ChangeAction = "create"  // change 被新创建
	ChangeActionUpdate               = "update"  // change 的信息被更新（title，description 等）
	ChangeActionPush                 = "push"    // change push 了新的commits
	ChangeActionAbandon              = "abandon" // change 被关闭
	ChangeActionRestore              = "restore" // change 重新开启
	ChangeActionSubmit               = "submit"  // change 被合并
)

// X-Codebase-Event: change
type ChangeEvent struct {
	Repo   *Repository   `json:"repository,omitempty"`
	From   *Change       `json:"from,omitempty"` // 只有当 change 信息修改时（changeAction=update）非空,保留原来的信息，model 只包含原有的 title，description
	Change *Change       `json:"change,omitempty"`
	User   *User         `json:"user,omitempty"` // 触发事件的用户，sso 的用户身份,action=create，创建 change 的用户，action=push，发起 push 事件的user，action=update，编辑 title 的用户 ... ...
	Action *ChangeAction `json:"action,omitempty"`
}

// X-Codebase-Event: push
type PushEvent struct {
	Ref       string      `json:"ref,omitempty"`        // ref, ref/head/master
	BeforeSHA string      `json:"before_sha,omitempty"` // push 之前 branch 上的最后 sha
	AfterSHA  string      `json:"after_sha,omitempty"`  // push 的 commits 中 最新的 sha
	Commits   []string    `json:"commits,omitempty"`    // 如果一次 push 1个或多个 commit，会在里面是一个 list，最大 20 个
	Repo      *Repository `json:"repository,omitempty"` //
	Sender    *User       `json:"sender,omitempty"`     // 发起 push 的人，sso 的用户身份
	// Changes   []*Change   `json:"changes,omitempty"`    // 如果 ref 正好存在于一个或多个 change 中，这里为非空
}

// X-Codebase-Event: check_run
type CheckRunEvent struct {
	CheckRun           *sdk.CheckRun       `json:"check_run,omitempty"`
	Action             *string             `json:"action,omitempty"` // 增加 skip
	Repo               *Repository         `json:"repository,omitempty"`
	Sender             *User               `json:"sender,omitempty"` // 发起 event 的用户，当 action=rerequest 时，为触发 rerun 的用户，当action=skip 时，为 skip 的用户
	RequestedOperation *RequestedOperation `json:"requested_operation,omitempty"`
	Skip               *Skipped            `json:"skip,omitempty"`
}
type RequestedOperation struct {
	Identifier string `json:"identifier"` // 用户触发的 button 名称，由 App 定义
}

type MergeRequestEvent struct {
	ObjectKind string `json:"object_kind"`
	User       struct {
		Name      string `json:"name"`
		Username  string `json:"username"`
		AvatarUrl string `json:"avatar_url"`
	} `json:"user"`
	Project struct {
		Id                int64  `json:"id"`
		Name              string `json:"name"`
		Description       string `json:"description"`
		WebUrl            string `json:"web_url"`
		AvatarUrl         string `json:"avatar_url"`
		GitSshUrl         string `json:"git_ssh_url"`
		GitHttpUrl        string `json:"git_http_url"`
		Namespace         string `json:"namespace"`
		VisibilityLevel   int64  `json:"visibility_level"`
		PathWithNamespace string `json:"path_with_namespace"`
		DefaultBranch     string `json:"default_branch"`
		Homepage          string `json:"homepage"`
		Url               string `json:"url"`
		SshUrl            string `json:"ssh_url"`
		HttpUrl           string `json:"http_url"`
	} `json:"project"`
	Repository struct {
		Name        string `json:"name"`
		Url         string `json:"url"`
		Description string `json:"description"`
		Homepage    string `json:"homepage"`
	} `json:"repository"`
	ObjectAttributes struct {
		Id              int64  `json:"id"`
		TargetBranch    string `json:"target_branch"`
		SourceBranch    string `json:"source_branch"`
		SourceProjectId int64  `json:"source_project_id"`
		AuthorId        int64  `json:"author_id"`
		AssigneeId      int64  `json:"assignee_id"`
		Title           string `json:"title"`
		CreatedAt       string `json:"created_at"`
		UpdatedAt       string `json:"updated_at"`
		MilestoneId     int64  `json:"milestone_id"`
		State           string `json:"state"`
		MergeStatus     string `json:"merge_status"`
		TargetProjectId int64  `json:"target_project_id"`
		Iid             int64  `json:"iid"`
		Description     string `json:"description"`
		Source          struct {
			Name              string `json:"name"`
			Description       string `json:"description"`
			WebUrl            string `json:"web_url"`
			AvatarUrl         string `json:"avatar_url"`
			GitSshUrl         string `json:"git_ssh_url"`
			GitHttpUrl        string `json:"git_http_url"`
			Namespace         string `json:"namespace"`
			VisibilityLevel   int64  `json:"visibility_level"`
			PathWithNamespace string `json:"path_with_namespace"`
			DefaultBranch     string `json:"default_branch"`
			Homepage          string `json:"homepage"`
			Url               string `json:"url"`
			SshUrl            string `json:"ssh_url"`
			HttpUrl           string `json:"http_url"`
		} `json:"source"`
		Target struct {
			Name              string `json:"name"`
			Description       string `json:"description"`
			WebUrl            string `json:"web_url"`
			AvatarUrl         string `json:"avatar_url"`
			GitSshUrl         string `json:"git_ssh_url"`
			GitHttpUrl        string `json:"git_http_url"`
			Namespace         string `json:"namespace"`
			VisibilityLevel   int64  `json:"visibility_level"`
			PathWithNamespace string `json:"path_with_namespace"`
			DefaultBranch     string `json:"default_branch"`
			Homepage          string `json:"homepage"`
			Url               string `json:"url"`
			SshUrl            string `json:"ssh_url"`
			HttpUrl           string `json:"http_url"`
		} `json:"target"`
		LastCommit struct {
			Id        string `json:"id"`
			Message   string `json:"message"`
			Timestamp string `json:"timestamp"`
			Url       string `json:"url"`
			Author    struct {
				Name  string `json:"name"`
				Email string `json:"email"`
			} `json:"author"`
		} `json:"last_commit"`
		WorkInProgress bool   `json:"work_in_progress"`
		Url            string `json:"url"`
		Action         string `json:"action"`
		Assignee       struct {
			Name      string `json:"name"`
			Username  string `json:"username"`
			AvatarUrl string `json:"avatar_url"`
		} `json:"assignee"`
		OldRev string `json:"oldrev"`
	} `json:"object_attributes"`
	Labels []struct {
		Id          int64  `json:"id"`
		Title       string `json:"title"`
		Color       string `json:"color"`
		ProjectId   int64  `json:"project_id"`
		CreatedAt   string `json:"created_at"`
		UpdatedAt   string `json:"updated_at"`
		Template    bool   `json:"template"`
		Description string `json:"description"`
		Type        string `json:"type"`
		GroupId     int64  `json:"group_id"`
	} `json:"labels"`
	Changes struct {
		UpdatedById interface{} `json:"updated_by_id"`
		UpdatedAt   interface{} `json:"updated_at"`
		Labels      struct {
			Previous []struct {
				Id          int64  `json:"id"`
				Title       string `json:"title"`
				Color       string `json:"color"`
				ProjectId   int64  `json:"project_id"`
				CreatedAt   string `json:"created_at"`
				UpdatedAt   string `json:"updated_at"`
				Template    bool   `json:"template"`
				Description string `json:"description"`
				Type        string `json:"type"`
				GroupId     int64  `json:"group_id"`
			} `json:"previous"`
			Current []struct {
				Id          int64  `json:"id"`
				Title       string `json:"title"`
				Color       string `json:"color"`
				ProjectId   int64  `json:"project_id"`
				CreatedAt   string `json:"created_at"`
				UpdatedAt   string `json:"updated_at"`
				Template    bool   `json:"template"`
				Description string `json:"description"`
				Type        string `json:"type"`
				GroupId     int64  `json:"group_id"`
			} `json:"current"`
		} `json:"labels"`
		MergeStatus interface{} `json:"merge_status"`
	} `json:"changes"`
	Commits []*MergeRequestEventCommit `json:"commits,omitempty"`
}

type MergeRequestEventCommit struct {
	Id        string    `json:"id"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
	Url       string    `json:"url"`
	Author    struct {
		Name  string `json:"name"`
		Email string `json:"email"`
	} `json:"author"`
	Added    []string `json:"added,omitempty"`
	Modified []string `json:"modified,omitempty"`
	Removed  []string `json:"removed,omitempty"`
}

// 文档: https://bytedance.feishu.cn/docs/doccnEFfE1EL8osbeYP6OVl3Eab#lbIuJE
// X-Codebase-Event: change_check_suite
type CheckSuiteEvent struct {
	CheckSuite   *sdk.CheckSuite `json:"check_suite,omitempty"`
	Action       *string         `json:"action,omitempty"` // 增加 skip,skip detail 见 check_suite.skip
	Repo         *Repository     `json:"repository,omitempty"`
	Sender       *User           `json:"sender,omitempty"` // 触发 event 用户，action=skip 为 skip 用户，action=rerequest 为触发 rerun 的用户
	Change       *Change         `json:"change,omitempty"`
	Skip         *Skipped        `json:"skip,omitempty"`
	ChangeAction *string         `json:"change_action,omitempty"`
}

type AppInstallAction string

const (
	AppInstallType   = AppInstallAction("install")
	AppUninstallType = AppInstallAction("uninstall")
)

// X-Codebase-Event: app
type AppEvent struct {
	Action      AppInstallAction `json:"action"`                 // install or uninstall
	App         *App             `json:"app,omitempty"`          // app information
	Repo        *Repository      `json:"repository,omitempty"`   // repository information
	InstalledAt *time.Time       `json:"installed_at,omitempty"` // 安装时间，install 和 uninstall 都会出现
	UninstallAt *time.Time       `json:"uninstall_at,omitempty"` // 卸载时间，只有 action = uninstall 的时候才会出现
}

// X-Codebase-Event: skip
type SkipEvent struct {
	Type         *string         `json:"type"`                    // skip 的类型，check_run 或者 check_suite
	App          *App            `json:"app,omitempty"`           // 接收 skip event 的 App
	SkippedBy    *User           `json:"skipped_by,omitempty"`    // 发起 skip 的用户
	ReasonDetail string          `json:"reason_detail,omitempty"` // skip 的理由
	CheckRun     *sdk.CheckRun   `json:"check_run,omitempty"`     // 如果 type=check_run，该字段非空
	CheckSuite   *sdk.CheckSuite `json:"check_suite,omitempty"`   // 如果 type=check_suite，该字段非空
}

// X-Codebase-Event: multi_review
type MultiReviewAction string

const (
	MultiReviewActionApproved    = MultiReviewAction("approved")
	MultiReviewActionDisapproved = MultiReviewAction("disapproved")
	MultiReviewActionRevoked     = MultiReviewAction("revoked")
	MultiReviewActionCommented   = MultiReviewAction("commented") // 不清楚commented有没有在用
	MultiReviewActionComment     = MultiReviewAction("comment")
)

type MultiReviewEvent struct {
	Action   MultiReviewAction `json:"action"`
	Reviewer *User             `json:"reviewer,omitempty"`
	Reviews  []Review          `json:"reviews,omitempty"`
}
type ReviewStatus string

const (
	ReviewStatusPending = ReviewStatus("pending")
	ReviewStatusPassed  = ReviewStatus("passed")
)

type Review struct {
	Change   Change       `json:"change"`
	Sha      string       `json:"sha"`
	Status   ReviewStatus `json:"status"`
	Comments []Comment    `json:"comments"`
}

type Repository struct {
	ID             *int64  `json:"id,omitempty"`
	Name           *string `json:"name"`
	Platform       *string `json:"platform"`
	ExternalURL    *string `json:"external_url"`
	GitURL         *string `json:"git_url"`
	GitLabFullName *string `json:"gitlab_full_name,omitempty"`
}

type User struct {
	ID       *int64  `json:"id,omitempty"`
	Name     *string `json:"name,omitempty"`
	Email    *string `json:"email,omitempty"`
	Username *string `json:"username,omitempty"`
}

type Change struct {
	ID             *int64            `json:"id,omitempty"`        // change id
	Author         *User             `json:"author,omitempty"`    // change 创建用户
	Submitter      *User             `json:"submitter,omitempty"` // 发起 change submit 的用户，如果没有 submit，为空
	Source         *sdk.ChangeSource `json:"source,omitempty"`
	Target         *sdk.ChangeSource `json:"target,omitempty"`
	Title          *string           `json:"title,omitempty"`
	Description    *string           `json:"description,omitempty"`
	Status         *sdk.ChangeStatus `json:"status,omitempty"` // change 的状态，open，submit，closed 等
	CreatedAt      *sdk.Timestamp    `json:"created_at,omitempty"`
	UpdatedAt      *sdk.Timestamp    `json:"updated_at,omitempty"`
	LastReviewedAt *sdk.Timestamp    `json:"last_reviewed_at,omitempty"`
	ExternalUrl    *string           `json:"external_url,omitempty"`
	AutoSubmit     bool              `json:"auto_submit,omitempty"`    // 是否开启了自动合入
	AutoSubmitter  *User             `json:"auto_submitter,omitempty"` // 开启了自动合入的用户，只有 auto_submit=true 时非空

	UserCanApprove *bool       `json:"user_can_approve,omitempty"` // event 中不存在
	HasApproved    *bool       `json:"has_approved,omitempty"`     // change 是否被 review 并且 approve 了
	PatchSets      []*PatchSet `json:"patch_sets,omitempty"`       // change 的 patch set 信息
}

type App struct {
	ID              *int64         `json:"id,omitempty"`
	Owner           *User          `json:"owner,omitempty"`
	Name            *string        `json:"name,omitempty"`
	Secret          *string        `json:"secret,omitempty"`
	Summary         *string        `json:"summary,omitempty"`
	Description     *string        `json:"description,omitempty"`
	DescriptionHTML *string        `json:"description_html,omitempty"`
	ExternalURL     *string        `json:"external_url,omitempty"`
	WebhookURL      *string        `json:"webhook_url,omitempty"`
	WebhookSecret   *string        `json:"webhook_secret,omitempty"`
	Private         bool           `json:"private"`
	CreatedAt       *sdk.Timestamp `json:"created_at,omitempty"`
	UpdatedAt       *sdk.Timestamp `json:"updated_at,omitempty"`
}

type Commit struct {
	SHA     string         `json:"sha"`
	Message string         `json:"message"`
	Author  *Author        `json:"author"` // 新增，git blame 中的 author 信息
	Url     string         `json:"url"`    // 新增，commit 信息 url，跳转到 gitlab，madeira 页面
	Time    *sdk.Timestamp `json:"time"`   // 新增，commit 创建时间，来自 git blame
}

// 新增，记录 git blame 的 user 信息
type Author struct {
	Name  string `json:"name"`  // commit 创作者 name，来自 git blame
	Email string `json:"email"` // commit 创作者 email，来自 git blame
}

type PatchSet struct {
	Number    int            `json:"number"`
	Uploader  *User          `json:"uploader"`
	SHA       string         `json:"sha"`
	BaseSHA   string         `json:"base_sha"`
	CreatedAt *sdk.Timestamp `json:"created_at"`
}

// 展示的已经被 skip 的信息
type Skipped struct {
	SkippedBy    *User  `json:"skipped_by,omitempty"`
	ReasonDetail string `json:"custom_detail,omitempty"`
}

type Comment struct {
	ID               int       `json:"id"`
	Content          string    `json:"content"`
	ThreadID         int       `json:"thread_id"`
	Author           User      `json:"author"`
	PatchsetNum      int       `json:"patchset_num"`
	Side             string    `json:"side"`
	LeftPatchsetNum  int       `json:"left_patchset_num"`
	RightPatchsetNum int       `json:"right_patchset_num"`
	LeftSha          string    `json:"left_sha"`
	RightSha         string    `json:"right_sha"`
	Path             string    `json:"path"`
	StartLine        int       `json:"start_line"`
	StartColumn      int       `json:"start_column"`
	EndLine          int       `json:"end_line"`
	EndColumn        int       `json:"end_column"`
	Draft            bool      `json:"draft"`
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
	Resolved         bool      `json:"resolved"`
	InReplyTo        int       `json:"in_reply_to"`
}
