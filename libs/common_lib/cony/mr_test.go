package cony

import (
	"code.byted.org/gopkg/logs"
	"context"
	"code.byted.org/devinfra/hagrid/libs/common_lib/consts"
	"testing"
)

func TestCreateConyMr(t *testing.T) {
	defer logs.Flush()
	type args struct {
		AppEnName    string
		Title        string
		MrType       consts.MergeRequestType
		Reviewer     []string
		Qa           []string
		Repositories []*MrRepository
		RemoveSource bool
	}

	skip := true
	arr := []args{
		{
			"componentize_demo11",
			"测试 MR",
			consts.MergeRequestTypeMerge,
			[]string{"<EMAIL>"},
			nil,
			[]*MrRepository{
				{
					35918,
					"test_zcs3",
					"alpha",
					&skip,
				},
			},
			false,
		},
	}

	tests := make([]struct {
		name    string
		args    args
		wantErr bool
	}, 0)

	for _, condition := range arr {

		tests = append(tests, struct {
			name    string
			args    args
			wantErr bool
		}{
			args: condition,
		})
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CreateConyMr(ctx, tt.args.AppEnName, tt.args.Title, tt.args.MrType, tt.args.Reviewer, tt.args.Qa, tt.args.Repositories, tt.args.RemoveSource)
			if err != nil {
				t.Errorf("CreateConyMr() error %v", err)
				return
			}
			t.Logf("test ok %+v", got)
		})
	}
}

//func TestGetOptimusJWTToken(t *testing.T) {
//	defer logs.Flush()
//
//	ctx := context.Background()
//	t.Run("test", func(t *testing.T) {
//		got, err := GetOptimusJWTToken(ctx)
//		if err != nil {
//			t.Errorf("GetOptimusJWTToken() error %v", err)
//			return
//		}
//		t.Logf("test ok %+v", got)
//	})
//}

func TestGetConyMrDiff(t *testing.T) {
	defer logs.Flush()

	ctx := context.Background()
	t.Run("test", func(t *testing.T) {
		res, err := GetConyMrDiffInfo(ctx, 114467, 1959)
		if err != nil {
			t.Errorf("TestGetConyMrDiff() error %v", err)
			return
		}
		t.Logf("test ok %+v", res)
	})
}

func TestGetConyMrDetailInfo(t *testing.T) {
	defer logs.Flush()

	ctx := context.Background()
	t.Run("test", func(t *testing.T) {
		res, err := GetConyMrDetailInfo(ctx, 114467, 2308)
		if err != nil {
			t.Errorf("TestGetConyMrDetailInfo() error %v", err)
			return
		}
		t.Logf("test ok %+v", res)
	})
}

func TestForceMergeConyMr(t *testing.T) {
	defer logs.Flush()

	ctx := context.Background()
	t.Run("test", func(t *testing.T) {
		err := ForceMergeConyMr(ctx, nil, 50671, 442)
		if err != nil {
			t.Errorf("ForceMergeConyMr() error %v", err)
			return
		}
		t.Logf("test ok")
	})
}
