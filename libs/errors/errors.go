package errors

import (
	stderrors "errors"
	"fmt"
)

const (
	maxMsgLen        = 1024
	maxServiceMsgLen = 256
)

var (
	optErrorWithFullPath = true
)

func Init(optionErrorWithFullPath bool) {
	optErrorWithFullPath = optionErrorWithFullPath
}

type Error interface {
	Service() ServiceID
	Status() StatusCode
	Error() string
	Is(err error) bool
	Unwrap() error
}

// New returns an error with the supplied message.
// New also records the Stack trace at the point it was called.
// It is intended to keep same signature as "go errors.New()" for easy migration.
// It is better to use StatusCode.New()
func New(msg string) Error {
	return &errorInstance{
		ServiceID:  currentService,
		StatusCode: StatusUndefined,
		Message:    truncateErrorMsg(msg),
		Stack:      callers(3),
	}
}

// Newf formats according to a format specifier and returns the string as a value that satisfies error.
// Newf also records the Stack trace at the point it was called.
// It is better to use StatusCode.Newf()
func Newf(format string, values ...any) Error {
	msg := fmt.Sprintf(format, values...)
	return &errorInstance{
		ServiceID:  currentService,
		StatusCode: StatusUndefined,
		Message:    truncateErrorMsg(msg),
		Stack:      callers(3),
	}
}

// Wrap returns an error annotating err with a Stack trace at the point Wrap is called, and the supplied message.
// If err is nil, Wrap returns nil.
// It is intended to keep same signature as "github.com/pkg/errors" for easy migration.
// It is better to use StatusCode.Wrap()
func Wrap(err error, info string) Error {
	sc := Status(err)
	return throw(sc, err, truncateErrorMsg(info), 1)
}

// Wrapf returns an error annotating err with a Stack trace at the point Wrapf is called, and the format specifier.
// If err is nil, Wrapf returns nil.
// It is intended to keep same signature as "github.com/pkg/errors" for easy migration.
// It is better to use StatusCode.Wrapf()
func Wrapf(err error, format string, values ...any) Error {
	msg := truncateErrorMsg(fmt.Sprintf(format, values...))
	sc := Status(err)
	return throw(sc, err, msg, 1)
}

func Service(err error) ServiceID {
	if err == nil {
		return ServiceID(0)
	}
	var e Error
	if stderrors.As(err, &e) && e != nil {
		return e.Service()
	}
	return ServiceID(0)
}

// Status returns StatusCode. This is useful for StatusCode population.
func Status(err error) StatusCode {
	if err == nil {
		return StatusOK
	}
	var e Error
	if stderrors.As(err, &e) && e != nil {
		return e.Status()
	}
	return StatusUndefined
}

// Unwrap implements standard go errors.Unwrap method.
func Unwrap(err error) error {
	return stderrors.Unwrap(err)
}

// Is implements standard go errors.Is method.
func Is(err, target error) bool { return stderrors.Is(err, target) }

// As implements standard go errors.As method.
func As[T error](err error, target *T) bool { return stderrors.As(err, target) }
