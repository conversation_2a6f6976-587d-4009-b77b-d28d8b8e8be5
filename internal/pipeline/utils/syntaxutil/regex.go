package syntaxutil

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/dlclark/regexp2"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
)

func IsItemsRegexAllowed(context context.Context, rules []string, items ...string) bool {
	if len(rules) == 0 {
		return true
	}
	// if items length is 0, it still can be triggered
	// when rules have '.*'
	if len(items) == 0 {
		return gslice.Contains(rules, ".*") || gslice.Contains(rules, "*")
	}

	res := false // default not allowed

	for _, rule := range rules {
		exp := fmt.Sprintf("^%s$", rule) // add default protection before and after rule
		r, err := regexp2.Compile(exp, regexp2.Multiline)
		if err != nil {
			logs.CtxError(context, "failed to compile regexp %s, error: %s", exp, err)
			return false
		}
		r.MatchTimeout = time.Second // timeout 1s

		for _, item := range items {
			// replace \n as " " to solve "\n\n is not working as expected"
			item = strings.Replace(item, "\n", " ", -1)
			// replace " " before and after item, to solve \n at both end of item
			item = strings.TrimSpace(item)
			match, err := r.MatchString(item)
			if err != nil {
				// if regex is too complex, will be timeout
				logs.CtxError(context, "timeout error for regex %s, error: %s", exp, err)
				return false
			}
			res = res || match
			if res {
				return res
			}
		}
	}
	return res
}
