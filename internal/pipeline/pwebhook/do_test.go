package pwebhook

import (
	"context"
	"testing"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func Test_SetAndGetWebhookHttpEvent(t *testing.T) {

	// 初始化 SQLITe 内存子数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {

		t.Fatalf("Failed to connect database: %v", err)
	}
	// 自动迁移创建表结构
	err = db.AutoMigrate(&WebhookHttpEvent{})
	if err != nil {
		t.Fatalf("Failed to migrate database: %v", err)
	}
	// 初始化仓库
	repo := &WebhookHttpEventRepo{db: db}
	ctx := context.Background()
	err = repo.SetWebhookHttpEvent(ctx, &WebhookHttpEvent{
		EventType:          "pipeline",
		Level:              "notice",
		FromPipelineRunID:  1,
		FromPipelineRunSeq: 2,
		FromRunStatus:      "running",
		FromPipelineRunURL: "https://www.baidu.com",
		FromPipelineID:     1,
		LogID:              "123",
		ToHTTPCode:         200,
		ToHTTPURL:          "https://www.baidu.com",
	})

	err = repo.SetWebhookHttpEvent(ctx, &WebhookHttpEvent{
		EventType:          "pipeline",
		Level:              "warning",
		FromPipelineRunID:  1,
		FromPipelineRunSeq: 1,
		FromRunStatus:      "running",
		FromPipelineRunURL: "https://www.baidu.com",
		FromPipelineID:     1,
		LogID:              "123",
		ToHTTPCode:         200,
		ToHTTPURL:          "https://www.baidu.com",
	})

	if err != nil {
		t.Fatalf("Failed to set webhook http event: %v", err)
	}

	event, err := repo.GetWebhookHttpEventsWithSortLevel(ctx, 1, 1, 1)
	if err != nil {
		t.Fatalf("Failed to get webhook http event: %v", err)
	}
	if event[0].Level != "warning" {
		t.Fatalf("Failed to get webhook http event: %v", err)
	}

}
