package yamlv1

import (
	"fmt"
	"testing"

	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"github.com/stretchr/testify/assert"
)

func Test_ParseOldCiPipeline(t *testing.T) {
	yamlFile := `name: pipeline
trigger: [push, change]
jobs:
  job_0:
    steps:
      - id: foo
        uses: bar
`

	pplPB, err := ParseOldCIPipeline([]byte(yamlFile))

	utils.AssertEqual(t, err, nil)
	fmt.Println(pplPB)

	yamlFile = `name: pipeline
trigger: [push, change]
jobs:
  job_0:
    steps:
      - id: foo
        uses: bar
`
	pplPB, err = ParseOldCIPipeline([]byte(yamlFile))

	utils.AssertEqual(t, err, nil)

	yamlFile = `name: pipeline
trigger: 
  change:
    paths:
      - "*.json"
    notification:
      when: [ failure ]
      to: [ "zhangsan.zs", "600112233445566778899" ]
jobs:
  job_0:
    image: debian
    steps:
      - uses: bar
    services:
      - id: s1
        commands:
          - sleep 3600
      - id: s2
        image: hub.byted.org/ee/mysql
`
	pplPB, err = ParseOldCIPipeline([]byte(yamlFile))
	utils.AssertEqual(t, err, nil)
	fmt.Println(pplPB)

	yamlFile = `name: mocked UI E2E for vke web
trigger: [change]
jobs:
  ui_e2e_job:
    envs:
      # 标识 CI 运行环境
      CI: "true"
      VKE_ENV: "dev-rd"
      VKE_PASSWORD: ${{VKE_PASSWORD}}
    name: ui e2e job triggered by merged
    image: hub.byted.org/codebase/paas_eden_v2_ci_pagepass_e2e:342a219307a2c67ac9f68cc96e42cd39
    steps:
      - name: start local web
        commands:
          - . /etc/profile
          - npm config set registry https://bnpm.byted.org
          - npm install @ies/eden-cd -g
          # some default expression variables
          - echo ${{Head.SHA}}
          - echo ${{Head.Branch}}
          - echo ${{Repo.Name}}
          # some default environment variables
          - echo $CI_REPO_NAME
          - echo $CI_PIPELINE_NAME
          - echo $CI_JOB_NAME
          - echo $CI_STEP_NAME
          - emo install --filter container-service-web
          - yarn start &
          - ./apps/mocked-ui-e2e-vke/wait-for-localserver.sh
      - name: run mocked ui e2e
        continue-on-error: true
        commands:
          - emo install --filter mocked-ui-e2e-vke && cd ./apps/mocked-ui-e2e-vke && yarn run:e2e:mock --headless
      - name: collect result of mocked ui e2e
        uses: actions/upload-artifact
        inputs:
          name: pagepass-result
          path: ./apps/mocked-ui-e2e-vke/e2e/result
`
	pplPB, err = ParseOldCIPipeline([]byte(yamlFile))
	utils.AssertEqual(t, err, nil)
	fmt.Println(pplPB)

	yamlFile = `name: "broker 自动检测"
trigger: change

jobs:
  broker-ci-auto-check-job:
    runs-on:
      env: boe
    name: auto check
    image: "hub.byted.org/codebase/ci_go_1_18:latest"
    envs:
      - ENV=staging
      - GOCACHE=/go/cache
      - TCE_PSM=suite.usearch.broker
      - RUNTIME_IDC_NAME=boe
      - RUNTIME_UNIT_NAME=boecn
      - ENV=staging
    steps:
      - id: checksum
        uses: actions/checksum@v1
        inputs:
          paths:
            - go.mod
            - go.sum
      - id: cache
        uses: actions/cache
        inputs:
          key: go-auto-check-${{Repo.ID}}-${{Steps.checksum.Outputs.hash}}
          paths:
            - /go/pkg/mod # GOPATH 所对应 pkg/mod 目录
            - /go/cache   # GOCACHE 所对应 cache 目录
          restore_keys:
            - go-auto-check-${{Repo.ID}}-${{Steps.checksum.Outputs.hash}}
            - go-auto-check-${{Repo.ID}}
      - id: autocheck
        name: Run Auto Check Test
        commands:
          - make go-run-auto-check

`
	pplPB, err = ParseOldCIPipeline([]byte(yamlFile))
	utils.AssertEqual(t, err, nil)
	fmt.Println(pplPB)
}

func Test_BadOldCIPipeline(t *testing.T) {
	// invalid trigger
	yamlFile := `name: pipeline
trigger: true
jobs:
  job_0:
    image: debian
    steps:
      - uses: bar
`
	pplPB, err := ParseOldCIPipeline([]byte(yamlFile))

	utils.AssertNotEqual(t, err, nil)
	utils.AssertErrMsgContain(t, err, "invalid trigger format")
	fmt.Println(pplPB)

}

var exampleYaml = []byte(`
name: Diff  # example comment
trigger:
  push:
  change:
    branches: [master, release]
    types: submit
jobs:
  test_name_01:
    image: aaa_bbb_ccc
    allow-push: true
    steps:
      - uses: actions/diff@v1
        inputs:
          auto: true  # example comment
          watch_result: true
          sid: "0001"
          size: 1919
          idc: cn
      - uses: docker://xxx
        commands:
          - ls
          - pwd
      - commands:
          - ls
    services:
      - id: s1
        name: ss1
        image: ha
      - id: s2
        image: hah
        commands:
          - whoami
          - date -u
          - true
  test_name_02:
    image: aaa_bbb_ccc
    allow-push: true
    steps:
      - uses: actions/diff@v1
        inputs:
          auto: true  # example comment
          watch_result: true
          sid: "0001"
          size: 1919
          idc: cn
      - uses: docker://xxx
        commands:
          - ls
          - pwd
      - commands:
          - ls
    services:
      - id: s1
        name: ss1
        image: ha
      - id: s2
        image: hah
        commands:
          - whoami
          - date -u
          - true
  test_name_03:
    image: aaa_bbb_ccc
    allow-push: true
    steps:
      - uses: actions/diff@v1
        inputs:
          auto: true  # example comment
          watch_result: true
          sid: "0001"
          size: 1919
          idc: cn
      - uses: docker://xxx
        commands:
          - ls
          - pwd
      - commands:
          - ls
    services:
      - id: s1
        name: ss1
        image: ha
      - id: s2
        image: hah
        commands:
          - whoami
          - date -u
          - true
`)

func Test_exampliePipeline(t *testing.T) {
	for i := 0; i < 10; i++ {
		pplPB, err := ParseOldCIPipeline([]byte(exampleYaml))
		utils.AssertEqual(t, err, nil)
		for i, job := range pplPB.Stages[0].Jobs {
			expectID := fmt.Sprintf("test_name_0%d", i+1)
			assert.Equal(t, job.Id, expectID)
		}
	}

	for i := 0; i < 10; i++ {
		pplPB, err := ParseOldCIPipeline([]byte(exampleYaml))
		utils.AssertEqual(t, err, nil)
		for i, job := range pplPB.Stages[0].Jobs {
			expectID := fmt.Sprintf("test_name_0%d", i+1)
			assert.Equal(t, job.Id, expectID)
		}
	}
}

func Test_rewriteIf(t *testing.T) {
	tests := []struct {
		name   string
		ifExpr string
		want   string
	}{
		{
			name:   "empty if",
			ifExpr: "",
			want:   "",
		},
		{
			name:   "true is always",
			ifExpr: "true",
			want:   "${{always()}}",
		},
		{
			name:   "if is an expression",
			ifExpr: "${{Inputs.Key == '123'}}",
			want:   "${{ always() && bool(Inputs.Key == '123') }}",
		},
		{
			name:   "if is a string literal",
			ifExpr: "<true>",
			want:   `${{ always() && bool("<true>") }}`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, rewriteIf(tt.ifExpr), "rewriteIf(%v)", tt.ifExpr)
		})
	}
}
