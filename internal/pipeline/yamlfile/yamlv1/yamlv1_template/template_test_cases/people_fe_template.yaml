inputs:
  image:
    type: string
  test:
    type: string
    default: ""
  triggered_branches:
    type: array
    items:
      type: string
  triggered_paths:
    type: array
    items:
      type: string
  coverprofile:
    description: the path of coverprofile
    type: string
    default: "coverage/clover.xml"
  env:
    type: string
    default: online
  minimum_coverage:
    type: string
    default: "10%"
  ignore_sonar:
    description: whether ignore sonar check result
    type: boolean
    default: false
  manual:
    description: whether manual to start the pipeline
    type: boolean
    default: false
name: "[EE-People FE CI]"
trigger:
  change:
    branches:
      - master
      - unpack: $[[ Inputs["triggered_branches"] ]]
    paths:
      - unpack: $[[ Inputs["triggered_paths"] ]]
jobs:
  people-ci-job:
    name: "people standard front end ci pipeline"
    manual: $[[ Inputs["manual"] ]]
    image: $[[ Inputs["image"] ]]
    runs-on:
      env: $[[ Inputs["env"] ]]
    steps:
      - id: test
        name: run fe ut test
        commands:
          - $[[ Inputs["test"] ]]
      - id: coverage
        name: fe ut coverage result
        uses: actions/codecov@v1
        inputs:
          file: $[[ Inputs["coverprofile"] ]]
          fail_ci_if_error: true
          config:
            status:
              project:
                full_coverage:
                  coverage_strategy: branch
                  minimum_coverage: $[[ Inputs["minimum_coverage"] ]]
                  if_not_found: "warning"
          ignore_sonar: $[[ Inputs["ignore_sonar"] ]]
