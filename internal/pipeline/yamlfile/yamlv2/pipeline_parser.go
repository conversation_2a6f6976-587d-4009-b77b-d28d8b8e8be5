package yamlv2

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/ghodss/yaml"

	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	validation2 "code.byted.org/devinfra/hagrid/internal/pipeline/validation"
	"code.byted.org/devinfra/hagrid/internal/pipeline/yamlfile/expression"
	"code.byted.org/devinfra/hagrid/internal/pipeline/yamlfile/yamlutils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/infrapb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/lang/gg/gslice"
)

func ParsePipeline(data []byte) (*dslpb.Pipeline, error) {
	// 预解析表达式语法是否合规
	if err := expression.Validate(string(data)); err != nil {
		return nil, errors.Errorf("ParsePipeline Validate err %v", err)
	}

	pplYaml := new(dslpb.PipelineYaml)
	jsonBytes, err := yaml.YAMLToJSON(data)
	if err != nil {
		return nil, errors.Errorf("ParsePipeline YAMLToJSON err %v", err)
	}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(jsonBytes, pplYaml)
	if err != nil {
		return nil, errors.Errorf("ParsePipeline YAMLToJSON err %v", err)
	}

	err = validation2.ValidatePipelineYaml(pplYaml)
	if err != nil {
		return nil, errors.Errorf("ParsePipeline ValidatePipelineYaml err %v", err)
	}
	pplPB, err := parsePipeline(pplYaml, data)
	if err != nil {
		return pplPB, errors.Errorf("ParsePipeline parsePipeline err %v", err)
	}
	err = validation2.ValidatePipeline(pplPB)
	return pplPB, err
}

func parsePipeline(pplYaml *dslpb.PipelineYaml, data []byte) (*dslpb.Pipeline, error) {
	pplRaw, err := parsePipelineRawData(data)
	if err != nil {
		return nil, errors.New("failed to parse pipeline raw data")
	}
	triggers, err := parseTriggers(pplYaml.Triggers)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse triggers field")
	}
	notifications, err := parseNotifications(pplYaml.Notifications)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse notifications field")
	}
	ppl := &dslpb.Pipeline{
		SchemaVersion: pplYaml.SchemaVersion,
		Name:          ParseI18nName(pplRaw.Name, ""),
		Desc:          ParseI18nName(pplRaw.Desc, ""),
		ExprSyntax:    pplYaml.ExprSyntax,
		Concurrency:   parseConcurrency(pplYaml.Concurrency),
		Env:           pplYaml.Env,
		VarPreference: pplYaml.VarPreference,
		Triggers:      triggers,
		Notifications: notifications,
	}

	stageJobSeq, jobSeq, err := yamlutils.GetJobSequence(data)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to parse yaml file to get job list")
	}
	if len(pplYaml.Stages) > 0 {
		if t, err := parseStages(pplYaml.Stages, stageJobSeq, pplRaw.Stages); err != nil {
			return nil, err
		} else {
			ppl.Stages = append(ppl.Stages, t...)
		}
	} else if pplYaml.Jobs != nil {
		jobs, err := parseJobs(pplYaml.Jobs, jobSeq, pplRaw.Jobs)
		if err != nil {
			return nil, err
		}
		defaultStage := &dslpb.Stage{
			Id:   "stage1",
			Name: utils.GetI18nPB("stage1"),
			If:   "",
			Jobs: jobs,
		}
		ppl.Stages = append(ppl.Stages, defaultStage)
	}
	return ppl, nil
}

func parseStages(stagesYaml []*dslpb.StageYaml, stageJobSeq map[string][]string, stagesRaw []*RawStage) ([]*dslpb.Stage, error) {
	var stages []*dslpb.Stage
	for i, stageYaml := range stagesYaml {
		var (
			jobs []*dslpb.Job
			err  error
		)
		if stageYaml.Jobs != nil {
			jobs, err = parseJobs(stageYaml.Jobs, stageJobSeq[stageYaml.Id], stagesRaw[i].Jobs)
			if err != nil {
				return nil, err
			}
		}

		stage := &dslpb.Stage{
			Id:   stageYaml.Id,
			Name: ParseI18nName(stagesRaw[i].Name, stageYaml.Id),
			If:   stageYaml.If,
			Jobs: jobs,
		}
		stages = append(stages, stage)
	}

	return stages, nil
}

func parseJobs(jobMap map[string]*dslpb.JobYaml, jobSeq []string, jobsRaw map[string]*RawJob) ([]*dslpb.Job, error) {
	newJobList := make([]*dslpb.Job, 0)
	for _, jobID := range jobSeq {
		job := jobMap[jobID]
		if job == nil {
			return nil, errors.New("yaml jobs verify failed")
		}
		job.Id = jobID

		if err := yamlutils.CheckImageForbidden(job.Image); err != nil {
			return nil, err
		}

		stepIdMap := make(map[string]bool) // step 可能是容器，和 service 在同一网络下，所以需要避免 id 冲突
		// normalize steps
		for i := range job.Steps {
			if job.Steps[i].Id == "" {
				// If step id is empty, use action or "Step X" as default step name
				job.Steps[i].Id = fmt.Sprintf("%s-step_%d", job.Id, i+1)
				if job.Steps[i].Name == "" {
					switch {
					case job.Steps[i].Uses == yamlutils.SelfActionReferenceKeyword:
						job.Steps[i].Name = "Self"
					case yamlutils.IsAction(job.Steps[i].Uses):
						job.Steps[i].Name = job.Steps[i].Uses
					default:
						job.Steps[i].Name = fmt.Sprintf("Step %d", i+1)
					}
				}
				job.Steps[i].Name = strings.Trim(job.Steps[i].Name, "")
			} else if job.Steps[i].Name == "" {
				// If step id is not empty, use step id as default step name
				job.Steps[i].Name = job.Steps[i].Id
			}
			if _, ok := stepIdMap[job.Steps[i].Id]; ok {
				return nil, fmt.Errorf("the IDs of steps and services should be distinct: step '%s'", job.Steps[i].Id)
			}
			stepIdMap[job.Steps[i].Id] = true
			if len(job.Steps[i].Uses) == 0 {
				if err := yamlutils.CheckCommands(job.Steps[i].Commands); err != nil {
					return nil, errors.Wrapf(err, "invalid commands of step '%s'", job.Steps[i].Id)
				}
			}
		}
		// normalize services
		for i := range job.Services {
			if _, ok := stepIdMap[job.Services[i].Id]; ok {
				return nil, errors.Errorf("the IDs of steps and services should be distinct: service '%s'", job.Services[i].Id)
			}
			if err := yamlutils.CheckImageForbidden(job.Services[i].Image); err != nil {
				return nil, err
			}
			stepIdMap[job.Services[i].Id] = true
			if job.Services[i].Name == "" {
				job.Services[i].Name = job.Services[i].Id
			}
		}
		jobPB, err := ParseJob(job, jobsRaw[jobID])
		if err != nil {
			return nil, err
		}
		newJobList = append(newJobList, jobPB)
	}
	return newJobList, nil
}

func ParseJob(jobYaml *dslpb.JobYaml, jobRawName interface{}) (*dslpb.Job, error) {
	if jobYaml == nil {
		return nil, nil
	}
	notifications, err := parseNotifications(jobYaml.Notifications)
	if err != nil {
		return nil, err
	}
	autoCache := true
	if jobYaml.AutoCache != nil && !*jobYaml.AutoCache {
		autoCache = false
	}
	autoGoModuleProxy := true
	if jobYaml.AutoGoModuleProxy != nil && !*jobYaml.AutoGoModuleProxy {
		autoGoModuleProxy = false
	}
	// auto_git_lfs_cache
	autoGitLFSCache := false
	if jobYaml.AutoGitLfsCache != nil && *jobYaml.AutoGitLfsCache {
		autoGitLFSCache = true
	}
	job := &dslpb.Job{
		Id:                   jobYaml.Id,
		Name:                 ParseI18nName(jobRawName, jobYaml.Id),
		If:                   jobYaml.If,
		IfSkip:               jobYaml.IfSkip,
		DependsOn:            jobYaml.DependsOn,
		Manual:               jobYaml.Manual,
		Uses:                 jobYaml.Uses,
		Inputs:               jobYaml.Inputs,
		RunsOn:               parseJobYamlRunsOn(jobYaml.RunsOn),
		Env:                  jobYaml.Env,
		Steps:                parseJobYamlSteps(jobYaml.Steps),
		Services:             jobYaml.Services,
		SupportTimeout:       jobYaml.SupportTimeout,
		Timeout:              jobYaml.Timeout,
		InputArtifacts:       parseJobYamlInputArtifacts(jobYaml.InputArtifacts),
		OnTimeout:            yamlutils.GetJobOnTimeoutFromStr(jobYaml.OnTimeout),
		OnFailed:             yamlutils.GetJobOnFailedFromStr(jobYaml.OnFailed),
		OnIgnored:            yamlutils.GetJobOnIgnoredFromStr(jobYaml.OnIgnored),
		Retry:                jobYaml.Retry,
		ManualOperations:     jobYaml.ManualOperations,
		Notifications:        notifications,
		AutoCache:            autoCache,
		AutoGoModuleProxy:    autoGoModuleProxy,
		AutoGitLfsCache:      autoGitLFSCache,
		RunEnv:               jobYaml.RunEnv,
		DisableOperations:    yamlutils.GetDisableOperationsFromStr(jobYaml.DisableOperations),
		JobRunOperations:     parseJobRunOperations(jobYaml.JobRunOperations),
		JobRunOperationsUsed: jobYaml.JobRunOperationsUsed,
	}
	if job.RunsOn == nil {
		job.RunsOn = &dslpb.RunnerSpec{}
	}
	if job.OnFailed == dslpb.OnFailed_ON_FAILED_RETRY && job.Retry == nil {
		job.Retry = &dslpb.OnFailedRetryPolicy{}
	}
	job.RunsOn.Image = jobYaml.Image
	job.RunsOn.WorkingDirectory = jobYaml.WorkingDirectory
	return job, nil
}

func parseConcurrency(concurrency *dslpb.Concurrency) *dslpb.Concurrency {
	if concurrency == nil {
		return &dslpb.Concurrency{
			Max:         -1,
			NewRunFirst: false,
		}
	}
	return concurrency
}

func ParseI18nName(name interface{}, id string) *infrapb.I18NString {
	switch nm := name.(type) {
	case string:
		if nm != "" {
			return utils.GetI18nPB(nm)
		}
	case map[string]interface{}:
		if len(nm) > 0 {
			nameMap := make(map[string]string, 0)
			for k, v := range nm {
				if v != nil {
					nameMap[k] = fmt.Sprintf("%v", v)
				}
			}
			return utils.GetI18nPBFromMap(nameMap)
		}
	case map[string]string:
		if len(nm) > 0 {
			return utils.GetI18nPBFromMap(nm)
		}
	}
	return utils.GetI18nPB(id)
}

func parsePipelineRawData(data []byte) (*RawPipeline, error) {
	pplRaw := new(RawPipeline)
	err := yaml.Unmarshal(data, pplRaw)
	if err != nil {
		return nil, err
	}
	return pplRaw, nil
}

func parseJobYamlInputArtifacts(artifactList []*dslpb.ArtifactFilterForYaml) []*dslpb.ArtifactFilter {
	return gslice.Map(artifactList, func(m *dslpb.ArtifactFilterForYaml) *dslpb.ArtifactFilter {
		filter := &dslpb.ArtifactFilter{FromJobId: m.FromJobId}
		switch m.Provider {
		case "scm":
			filter.Provider = dslpb.ExternalArtifactProvider_EXTERNAL_ARTIFACT_PROVIDER_SCM
		default:
			filter.Provider = dslpb.ExternalArtifactProvider_EXTERNAL_ARTIFACT_PROVIDER_UNSPECIFIED
		}
		return filter
	})
}

func parseNotifications(notificationYaml []*dslpb.NotificationYaml) ([]*dslpb.Notification, error) {
	if len(notificationYaml) == 0 {
		return nil, nil
	}
	notis := make([]*dslpb.Notification, 0)
	for _, notiYaml := range notificationYaml {
		notiType, err := getNotificationTypeFromStr(notiYaml.Type)
		if err != nil {
			return nil, err
		}
		switch notiType {
		case dslpb.NotificationType_NOTIFICATION_TYPE_LARK:
			lark := notiYaml.Lark
			if lark == nil {
				continue
			}
			method, err := getNotificationMethodFromStr(lark.NotificationMethod)
			if err != nil {
				return nil, err
			}
			groupMethod, err := getNotificationMethodFromStr(lark.GroupNotificationMethod)
			if err != nil {
				return nil, err
			}
			noti := &dslpb.Notification{
				Name: notiYaml.Name,
				Type: dslpb.NotificationType_NOTIFICATION_TYPE_LARK,
				When: &dslpb.NotifyWhen{
					Status:  notiYaml.GetWhen().GetStatus(),
					Timeout: int32(notiYaml.GetWhen().GetTimeout()),
				},
				Lark: &dslpb.LarkNotification{
					NotifierTypes:           lark.NotifierTypes,
					Users:                   lark.Users,
					Groups:                  lark.Groups,
					NotificationMethod:      method,
					GroupNotificationMethod: groupMethod,
					Cards:                   lark.Cards,
					Repeat:                  lark.Repeat,
				},
			}
			notis = append(notis, noti)
		case dslpb.NotificationType_NOTIFICATION_TYPE_WEBHOOK:
			webhook := notiYaml.Webhook
			if webhook == nil {
				continue
			}
			actionType, err := getWebhookActionTypeFromStr(webhook.ActionType)
			if err != nil {
				return nil, err
			}
			noti := &dslpb.Notification{
				Name: notiYaml.Name,
				Type: dslpb.NotificationType_NOTIFICATION_TYPE_WEBHOOK,
				When: &dslpb.NotifyWhen{
					Status: notiYaml.When.Status,
				},
				Webhook: &dslpb.Webhook{
					ActionType:     actionType,
					HttpAction:     webhook.HttpAction,
					PipelineAction: webhook.PipelineAction,
				},
			}
			notis = append(notis, noti)
		default:
			// ignore for now
		}
	}
	return notis, nil
}

func getNotificationMethodFromStr(method string) (dslpb.NotificationMethod, error) {
	switch method {
	case constvar.NotificationMethod_Normal, "":
		return dslpb.NotificationMethod_NOTIFICATION_METHOD_UNSPECIFIED, nil
	case constvar.NotificationMethod_Mentioned:
		return dslpb.NotificationMethod_NOTIFICATION_METHOD_MENTIONED, nil
	case constvar.NotificationMethod_Urgent:
		return dslpb.NotificationMethod_NOTIFICATION_METHOD_URGENT, nil
	default:
		return dslpb.NotificationMethod_NOTIFICATION_METHOD_UNSPECIFIED, errors.Errorf("notification method %s is not supported", method)
	}
}

func getNotificationTypeFromStr(notificationType string) (dslpb.NotificationType, error) {
	switch notificationType {
	case constvar.NotificationType_Lark:
		return dslpb.NotificationType_NOTIFICATION_TYPE_LARK, nil
	case constvar.NotificationType_Webhook:
		return dslpb.NotificationType_NOTIFICATION_TYPE_WEBHOOK, nil
	default:
		return dslpb.NotificationType_NOTIFICATION_TYPE_UNSPECIFIED, errors.Errorf("notification type %s is not supported", notificationType)
	}
}

func parseTriggers(triggerYaml []*dslpb.TriggerYaml) ([]*dslpb.Trigger, error) {
	if len(triggerYaml) == 0 {
		return nil, nil
	}
	triggers := make([]*dslpb.Trigger, 0)
	for _, trigger := range triggerYaml {
		pushTrigger := trigger.GetGitPush()
		mrTrigger := trigger.GetMr()
		if pushTrigger != nil {
			events := make([]dslpb.GitPushEvent, 0)
			for _, e := range pushTrigger.Events {
				pushEvent, err := getPushEventFromStr(e)
				if err != nil {
					return nil, err
				}
				events = append(events, pushEvent)
			}
			patternSyntax, err := getPatternSyntaxFromStr(pushTrigger.PatternSyntax)
			if err != nil {
				return nil, err
			}
			t := &dslpb.Trigger_GitPush{GitPush: &dslpb.GitPushTrigger{
				Repository:      pushTrigger.Repository,
				Events:          events,
				PatternSyntax:   patternSyntax,
				Branches:        pushTrigger.Branches,
				Tags:            pushTrigger.Tags,
				Paths:           pushTrigger.Paths,
				CommitMessages:  pushTrigger.CommitMessages,
				TimeRestriction: pushTrigger.TimeRestriction,
			}}
			triggers = append(triggers, &dslpb.Trigger{
				Trigger:  t,
				Disabled: trigger.Disabled,
			})
		} else if mrTrigger != nil {
			events := make([]dslpb.MREvent, 0)
			for _, e := range mrTrigger.Events {
				mrEvent, err := getMrEventFromStr(e)
				if err != nil {
					return nil, err
				}
				events = append(events, mrEvent)
			}
			patternSyntax, err := getPatternSyntaxFromStr(mrTrigger.PatternSyntax)
			if err != nil {
				return nil, err
			}
			t := &dslpb.Trigger_Mr{Mr: &dslpb.MRTrigger{
				// TODO repo_name暂时为空，是否需要拼接
				Repository:       mrTrigger.Repository,
				Events:           events,
				PatternSyntax:    patternSyntax,
				SourceBranches:   mrTrigger.SourceBranches,
				TargetBranches:   mrTrigger.TargetBranches,
				Paths:            mrTrigger.Paths,
				IncrementalPaths: mrTrigger.IncrementalPaths,
				MrTitles:         mrTrigger.MrTitles,
				CommitMessages:   mrTrigger.CommitMessages,
				TimeRestriction:  mrTrigger.TimeRestriction,
			}}
			triggers = append(triggers, &dslpb.Trigger{
				Trigger:  t,
				Disabled: trigger.Disabled,
			})
		}
	}
	return triggers, nil
}

func getMrEventFromStr(e string) (dslpb.MREvent, error) {
	switch e {
	case constvar.Mr_Closed, "MR_EVENT_CLOSED":
		return dslpb.MREvent_MR_EVENT_CLOSED, nil
	case constvar.Mr_Updated, "MR_EVENT_UPDATED":
		return dslpb.MREvent_MR_EVENT_UPDATED, nil
	case constvar.Mr_Reopened, "MR_EVENT_REOPENED":
		return dslpb.MREvent_MR_EVENT_REOPENED, nil
	case constvar.Mr_Pushed, "MR_EVENT_PUSHED":
		return dslpb.MREvent_MR_EVENT_PUSHED, nil
	case constvar.Mr_Opened, "MR_EVENT_OPENED":
		return dslpb.MREvent_MR_EVENT_OPENED, nil
	case constvar.Mr_Merged, "MR_EVENT_MERGED":
		return dslpb.MREvent_MR_EVENT_MERGED, nil
	default:
		return dslpb.MREvent_MR_EVENT_UNSPECIFIED, errors.Errorf("mr event type %s is not supported", e)
	}
}

func getPushEventFromStr(e string) (dslpb.GitPushEvent, error) {
	switch e {
	case constvar.Push_Pushed, "GIT_PUSH_EVENT_PUSHED":
		return dslpb.GitPushEvent_GIT_PUSH_EVENT_PUSHED, nil
	case constvar.Push_Deleted, "GIT_PUSH_EVENT_DELETED":
		return dslpb.GitPushEvent_GIT_PUSH_EVENT_DELETED, nil
	case constvar.PushTag_Pushed, "GIT_PUSH_TAG_EVENT_PUSHED":
		return dslpb.GitPushEvent_GIT_PUSH_EVENT_TAG_PUSHED, nil
	default:
		return dslpb.GitPushEvent_GIT_PUSH_EVENT_UNSPECIFIED, errors.Errorf("push event type %s is not supported", e)
	}
}

func getPatternSyntaxFromStr(syntax string) (dslpb.PatternSyntax, error) {
	switch syntax {
	case constvar.PatternSyntax_Glob, "PATTERN_SYNTAX_GLOB":
		return dslpb.PatternSyntax_PATTERN_SYNTAX_GLOB, nil
	case constvar.PatternSyntax_Regex, "PATTERN_SYNTAX_REGEX":
		return dslpb.PatternSyntax_PATTERN_SYNTAX_REGEX, nil
	case "":
		return dslpb.PatternSyntax_PATTERN_SYNTAX_GLOB, nil
	default:
		return dslpb.PatternSyntax_PATTERN_SYNTAX_UNSPECIFIED, errors.Errorf("pattern syntax %s is not supported", syntax)
	}
}

func parseJobYamlRunsOn(runnerSpecYaml *dslpb.RunnerSpecYaml) *dslpb.RunnerSpec {
	if runnerSpecYaml == nil {
		return nil
	}
	return &dslpb.RunnerSpec{
		Engine:             yamlutils.RunnerEngineStr2PB(runnerSpecYaml.Engine),
		ResourceCombo:      runnerSpecYaml.ResourceCombo,
		Bytenv:             runnerSpecYaml.Bytenv,
		Resource:           runnerSpecYaml.Resource,
		Labels:             runnerSpecYaml.Labels,
		Image:              runnerSpecYaml.Image,
		WorkingDirectory:   runnerSpecYaml.WorkingDirectory,
		UseWarmupContainer: runnerSpecYaml.UseWarmupContainer,
	}
}

func parseJobYamlSteps(stepsYaml []*dslpb.StepYaml) []*dslpb.Step {
	stepsPB := make([]*dslpb.Step, 0)
	for _, stepYaml := range stepsYaml {
		stepsPB = append(stepsPB, &dslpb.Step{
			Id:                   stepYaml.Id,
			Name:                 stepYaml.Name,
			If:                   stepYaml.If,
			ParallelWithNextStep: stepYaml.ParallelWithNextStep,
			OnFailed:             yamlutils.GetJobOnFailedFromStr(stepYaml.OnFailed),
			Retry:                stepYaml.Retry,
			Uses:                 stepYaml.Uses,
			Inputs:               stepYaml.Inputs,
			Commands:             stepYaml.Commands,
		})
	}
	return stepsPB
}

func parseJobRunOperations(elements []*dslpb.JobRunOperationWithPermissionYaml) []*dslpb.JobRunOperationWithPermission {
	resultPB := make([]*dslpb.JobRunOperationWithPermission, 0)
	for _, element := range elements {
		resultPB = append(resultPB, &dslpb.JobRunOperationWithPermission{
			SupportOperation: yamlutils.GetJobRunOperationFromStr(element.SupportOperation),
			AllowRoles:       element.AllowRoles,
			AllowUsernames:   element.AllowUsernames,
		})
	}
	return resultPB
}

func ParseNotificationsPBToYaml(notificationsPB []*dslpb.Notification) ([]*dslpb.NotificationYaml, error) {
	// 过滤前端传过来的多余字段
	for i, notification := range notificationsPB {
		if notification == nil {
			continue
		}
		if notification.Type == dslpb.NotificationType_NOTIFICATION_TYPE_LARK {
			notificationsPB[i].Webhook = nil
		}
		if notification.Type == dslpb.NotificationType_NOTIFICATION_TYPE_WEBHOOK {
			notificationsPB[i].Lark = nil
		}
	}

	if notificationsPB == nil {
		return nil, nil
	}
	notisYaml := make([]*dslpb.NotificationYaml, 0)
	for _, notiPB := range notificationsPB {
		notiYaml := &dslpb.NotificationYaml{
			Name: notiPB.Name,
			Type: yamlutils.NotificationType2Str(notiPB.Type),
			// todo 看 notiPB 还有 id、template_notification_id 但NotificationYaml没定义
		}
		if notiPB.When != nil {
			notiYaml.When = &dslpb.WhenYaml{
				Status:  notiPB.When.Status,
				Timeout: uint32(notiPB.When.Timeout),
			}
		}
		switch notiPB.Type {
		case dslpb.NotificationType_NOTIFICATION_TYPE_LARK:
			if notiPB.Lark == nil {
				break
			}
			notiYaml.Lark = &dslpb.LarkYaml{
				Users:                   notiPB.Lark.Users,
				Groups:                  notiPB.Lark.Groups,
				NotificationMethod:      yamlutils.NotificationMethod2Str(notiPB.Lark.NotificationMethod),
				GroupNotificationMethod: yamlutils.NotificationMethod2Str(notiPB.Lark.GroupNotificationMethod),
				Repeat:                  notiPB.Lark.Repeat,
				Cards:                   notiPB.Lark.Cards,
				NotifierTypes:           notiPB.Lark.NotifierTypes,
			}
		case dslpb.NotificationType_NOTIFICATION_TYPE_WEBHOOK:
			if notiPB.Webhook == nil {
				break
			}
			notiYaml.Webhook = &dslpb.WebhookYaml{
				ActionType:     yamlutils.NotificationWebhookActionType2Str(notiPB.Webhook.ActionType),
				HttpAction:     notiPB.Webhook.HttpAction,
				PipelineAction: notiPB.Webhook.PipelineAction,
			}
		default:
			// ignore for now
		}
		notisYaml = append(notisYaml, notiYaml)
	}
	return notisYaml, nil
}

func getWebhookActionTypeFromStr(actionType string) (dslpb.WebhookActionType, error) {
	switch actionType {
	case constvar.NotificationType_Webhook_ActionType_HTTP:
		return dslpb.WebhookActionType_WEBHOOK_ACTION_TYPE_HTTP, nil
	case constvar.NotificationType_Webhook_ActionType_Pipeline:
		return dslpb.WebhookActionType_WEBHOOK_ACTION_TYPE_PIPELINE, nil
	default:
		return dslpb.WebhookActionType_WEBHOOK_ACTION_TYPE_UNSPECIFIED, errors.Errorf("webhook action type %s is not supported", actionType)
	}
}
