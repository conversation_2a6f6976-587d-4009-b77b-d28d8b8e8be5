load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "kms",
    srcs = [
        "kms.go",
        "local.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/internal/varstore/provider/kms",
    visibility = ["//:__subpackages__"],
    deps = [
        "//internal/varstore/config",
        "//pkg/tools/fatalerr",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_bytecycle_go_middlewares_infra//initializer",
        "@org_byted_code_security_kms_v2_sdk_golang//clientv2",
    ],
)
