package service

import (
	"context"
	"testing"

	"code.byted.org/bytecycle/go-middlewares/infra/initializer"
	"code.byted.org/bytecycle/go-middlewares/infra/storage/xgorm"
	"code.byted.org/devinfra/hagrid/internal/varstore/dal"
	"code.byted.org/devinfra/hagrid/internal/varstore/domain/dassociation"
	tf "code.byted.org/devinfra/hagrid/internal/varstore/testfactory"
	pb "code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"github.com/stretchr/testify/require"
)

func TestAssociationService_MGetWithGroupByQuery(t *testing.T) {
	s := Association
	storageCleanup(t)

	const (
		groupId         = 199
		workspaceId     = 19
		bitsWorkspaceId = 109
		devModeId       = 99
	)

	var (
		group = tf.NewVarGroup(func(g *pb.VarGroup) {
			g.GroupId = groupId
			g.WorkspaceId = workspaceId
		})

		key1 = NewWorkspaceAssociationKey()
		ga1  = tf.NewVarGroupAssociation(func(ga *pb.VarGroupAssociation) {
			ga.GroupId = groupId
			ga.WorkspaceId = workspaceId
			ga.BitsWorkspaceId = bitsWorkspaceId
			ga.Scene = pb.AssociationScene_ASSOCIATION_SCENE_WORKSPACE
			ga.AssociationKey = key1.RawKey
		})

		key2 = NewDevModeAssociationKey(devModeId)
		ga2  = tf.NewVarGroupAssociation(func(ga *pb.VarGroupAssociation) {
			ga.GroupId = groupId
			ga.WorkspaceId = workspaceId
			ga.BitsWorkspaceId = bitsWorkspaceId
			ga.Scene = pb.AssociationScene_ASSOCIATION_SCENE_DEV_MODE
			ga.AssociationKey = key2.RawKey
		})
	)

	// prepare group & associations
	require.NoError(t, s.group.Add(ctx, group, false), "add group")
	require.NoError(t, s.association.Add(ctx, ga1), "add association 1")
	require.NoError(t, s.association.Add(ctx, ga2), "add association 2")

	t.Run("query by keys", func(t *testing.T) {
		gotA, gotG, err := s.MGetWithGroupByQuery(ctx, &pb.QueryVarGroupByAssociationReq{
			WorkspaceId:           workspaceId,
			Keys:                  []*pb.AssociationKey{key1, key2},
			Scenes:                []pb.AssociationScene{key1.Scene}, // should be ignored
			FlattenVarToSnapshots: false,
		})

		require.NoError(t, err, "query")
		tf.SortById(gotA)
		tf.WantProtoSliceEqual(t, []*pb.VarGroupAssociation{ga1, ga2}, gotA)
		tf.WantProtoSliceEqual(t, []*pb.VarGroup{group}, gotG)
	})

	t.Run("query by keys from bits", func(t *testing.T) {
		gotA, gotG, err := s.MGetWithGroupByQuery(ctx, &pb.QueryVarGroupByAssociationReq{
			BitsWorkspaceId:       bitsWorkspaceId,
			Keys:                  []*pb.AssociationKey{key1, key2},
			Scenes:                []pb.AssociationScene{key1.Scene}, // should be ignored
			FlattenVarToSnapshots: false,
			FromBitsGw:            true,
		})

		require.NoError(t, err, "query from bits")
		tf.SortById(gotA)
		tf.WantProtoSliceEqual(t, []*pb.VarGroupAssociation{ga1, ga2}, gotA)
		tf.WantProtoSliceEqual(t, []*pb.VarGroup{group}, gotG)
	})

	t.Run("query by scenes", func(t *testing.T) {
		gotA, gotG, err := s.MGetWithGroupByQuery(ctx, &pb.QueryVarGroupByAssociationReq{
			WorkspaceId:           workspaceId,
			Scenes:                []pb.AssociationScene{key1.Scene},
			FlattenVarToSnapshots: false,
		})

		require.NoError(t, err, "query")
		tf.WantProtoSliceEqual(t, []*pb.VarGroupAssociation{ga1}, gotA)
		tf.WantProtoSliceEqual(t, []*pb.VarGroup{group}, gotG)
	})

	t.Run("query by scenes from bits", func(t *testing.T) {
		gotA, gotG, err := s.MGetWithGroupByQuery(ctx, &pb.QueryVarGroupByAssociationReq{
			BitsWorkspaceId:       bitsWorkspaceId,
			Scenes:                []pb.AssociationScene{key1.Scene},
			FlattenVarToSnapshots: false,
			FromBitsGw:            true,
		})

		require.NoError(t, err, "query from bits")
		tf.WantProtoSliceEqual(t, []*pb.VarGroupAssociation{ga1}, gotA)
		tf.WantProtoSliceEqual(t, []*pb.VarGroup{group}, gotG)
	})
}

func TestAssociationService_Cover(t *testing.T) {
	s := Association
	t.Run("cover associations of group", func(t *testing.T) {
		xgorm.WithCleanup(t, s.db)
		ga1 := tf.NewVarGroupAssociation()
		ga2 := tf.NewVarGroupAssociation()
		gas := []*pb.VarGroupAssociation{
			ga1, ga2,
		}
		require.NoError(t, s.ReplaceAssociations(ctx, gas, 1))
	})
}

func NewDevModeAssociationKey(devModeId uint64) *pb.AssociationKey {
	key := &pb.AssociationKey{
		Scene: pb.AssociationScene_ASSOCIATION_SCENE_DEV_MODE,
		Key: &pb.AssociationKey_DevMode{DevMode: &pb.DevModeAssociationKey{
			DevModeId: devModeId,
		}},
	}
	_ = dassociation.BuildKey(key)
	return key
}

func NewWorkspaceAssociationKey() *pb.AssociationKey {
	key := &pb.AssociationKey{
		Scene: pb.AssociationScene_ASSOCIATION_SCENE_WORKSPACE,
		Key: &pb.AssociationKey_Workspace{Workspace: &pb.WorkspaceAssociationKey{
			WorkspaceGlobal: true,
		}},
	}

	_ = dassociation.BuildKey(key)
	return key
}

// AssociationService.DelGroupIfNoAssociation 单元测试
func TestAssociationService_DelGroupIfNoAssociation(t *testing.T) {
	initializer.MustInitializeWithDeps(Module)
	storageCleanup(t)
	group1 := tf.NewVarGroup(func(g *pb.VarGroup) {
	})
	group2 := tf.NewVarGroup(func(g *pb.VarGroup) {
	})
	ga1 := tf.NewVarGroupAssociation(func(ga *pb.VarGroupAssociation) {})
	// 创建一个测试用的上下文
	ctx := context.Background()
	// 如果没有group应该直接返回
	err := Association.DelGroupIfNoAssociation(ctx, []uint64{}, "test")
	if err != nil {
		t.Errorf("unexpected error: %v", err)
	}
	// 正常情况下，如果不存在关联关系，删除变量组, 最终应该group2 被删除，group1存在
	require.NoError(t, dal.Group.Add(ctx, group1, false), "add group 1")
	require.NoError(t, dal.Group.Add(ctx, group2, false), "add group 2")
	ga1.GroupId = group1.GroupId
	require.NoError(t, dal.GroupAssociation.Add(ctx, ga1), "add association")
	require.NoError(t, Association.DelGroupIfNoAssociation(ctx, []uint64{group1.GroupId, group2.GroupId}, "test"), "DelGroupIfNoAssociation")
	_, err = Group.Get(ctx, group1.GroupId)
	require.NoError(t, err, "get group")
	_, err = Group.Get(ctx, group2.GroupId)
	if err == nil {
		t.Errorf("group2 should be deleted")
	}
}

func TestAssociationService_MDel(t *testing.T) {
	ctx := context.Background()
	initializer.MustInitializeWithDeps(Module)
	storageCleanup(t)
	keys := []*pb.VarGroupAssociation{
		{Id: 1},
		{Id: 2},
	}
	err := Association.MDel(ctx, keys)
	require.NoError(t, err, "delete association")
}
