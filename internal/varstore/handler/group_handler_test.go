package handler

import (
	"context"
	"reflect"
	"testing"

	"github.com/cloudwego/kitex/pkg/remote/trans/nphttp2/codes"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"code.byted.org/bits/monkey"
	pb "code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz/authzservice"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz/authzservice_mock"
	"code.byted.org/kite/kitex/client/callopt"

	authzProvider "code.byted.org/devinfra/hagrid/internal/varstore/provider/authz"
	tf "code.byted.org/devinfra/hagrid/internal/varstore/testfactory"
)

func TestCreateVarGroup(t *testing.T) {
	s := Group

	t.Run("custom group", func(t *testing.T) {
		storageCleanup(t)

		req := newCreateVarGroupReq()
		resp, err := s.CreateVarGroup(ctx, req)
		require.NoError(t, err, "create group")

		got := resp.Group
		assert.NotEmpty(t, got.GroupId, "group id")
		for _, vd := range got.VarDefinitions {
			assert.NotEmpty(t, vd.FullName, "fill full name")
			assert.NotEmpty(t, vd.DefaultValue.RawJson, "fill raw json")
		}
	})

	t.Run("system group", func(t *testing.T) {
		storageCleanup(t)

		req := &pb.CreateVarGroupReq{
			Name:        &pb.StringInMultiLang{Value: "build"},
			Namespace:   pb.VarGroupNamespace_VAR_GROUP_NAMESPACE_SYS,
			Provider:    pb.SysProvider_SYS_PROVIDER_BUILD,
			KeyPrefix:   "sys.build.",
			Constraints: []pb.VarGroupConstraint{pb.VarGroupConstraint_VAR_GROUP_CONSTRAINT_MUST_COMPATIBLE_DEF},
			CreatedBy:   "ByteCycle",
		}
		resp, err := s.CreateVarGroup(ctx, req)
		require.NoError(t, err, "create group")
		got := resp.Group
		assert.NotEmpty(t, got.GroupId, "group id")
		assert.EqualValues(t, 4, got.Constraints, "group constraints")

		_, err = s.CreateVarGroup(ctx, req)
		tf.WantErrorStatus(t, err, codes.FailedPrecondition, "system provider uniq")
	})

	t.Run("create by copy", func(t *testing.T) {
		storageCleanup(t)

		req := &pb.CreateVarGroupReq{CopyFromGroupId: 404}
		_, err := s.CreateVarGroup(ctx, req)
		tf.WantErrorStatus(t, err, codes.NotFound, "copy from a non-exist group")

		group1 := prepareVarGroup(t)
		req = &pb.CreateVarGroupReq{
			WorkspaceId:     group1.WorkspaceId + 1001,
			CopyFromGroupId: group1.GroupId,
			CreatedBy:       "sss",
		}
		resp, err := s.CreateVarGroup(ctx, req)
		require.NoError(t, err, "create group")

		got := resp.Group
		assert.NotEqualf(t, group1.GroupId, got.GroupId, "group id")
		assert.NotEqualf(t, group1.WorkspaceId, got.WorkspaceId, "overwrite workspace")
		assert.Equal(t, req.CreatedBy, got.CreatedBy, "created_by")
	})
}

func TestUpdateVarGroup(t *testing.T) {
	s := Group

	t.Run("update desc", func(t *testing.T) {
		storageCleanup(t)

		group := prepareVarGroup(t, func(req *pb.CreateVarGroupReq) {
			req.Name = &pb.StringInMultiLang{Value: "a group", Cn: "中文", En: "英文"}
			req.Description = req.Name
		})
		newDesc := &pb.StringInMultiLang{Value: "updated", Cn: "中文", En: "updated"}
		const anotherOne = "another"
		req := &pb.UpdateVarGroupReq{
			GroupId:     group.GroupId,
			Description: newDesc,
			UpdatedBy:   anotherOne,
		}
		resp, err := s.UpdateVarGroup(ctx, req)
		require.NoError(t, err, "update group")
		got := resp.Group
		assert.Equal(t, newDesc, got.Description, "group description")
		assert.Equal(t, anotherOne, got.UpdatedBy, "group updated by")
		assert.NotEmpty(t, got.Name.Value, "group name unchanged")
		assert.EqualValues(t, 1, got.Version, "group version unchanged")
	})

	t.Run("nothing to update", func(t *testing.T) {
		storageCleanup(t)
		group := prepareVarGroup(t)
		req := &pb.UpdateVarGroupReq{GroupId: group.GroupId, UpdatedBy: "dummy", Description: &pb.StringInMultiLang{}}
		_, err := Group.UpdateVarGroup(ctx, req)
		tf.WantErrorStatus(t, err, codes.InvalidArgument)
	})

	t.Run("update 404", func(t *testing.T) {
		req := &pb.UpdateVarGroupReq{
			GroupId:     1235,
			Description: &pb.StringInMultiLang{Value: "value"},
		}
		_, err := Group.UpdateVarGroup(ctx, req)
		tf.WantErrorStatus(t, err, codes.NotFound, "update a non-exist group")
	})

	t.Run("empty desc", func(t *testing.T) {
		storageCleanup(t)

		group := prepareVarGroup(t, func(req *pb.CreateVarGroupReq) {
			req.Name = &pb.StringInMultiLang{Value: "a group", Cn: "中文", En: "英文"}
			req.Description = req.Name
		})

		const anotherOne = "another"
		req := &pb.UpdateVarGroupReq{
			GroupId:   group.GroupId,
			UpdatedBy: anotherOne,
		}
		resp, err := s.UpdateVarGroup(ctx, req)
		require.NoError(t, err, "update group")
		got := resp.Group
		assert.Equal(t, anotherOne, got.UpdatedBy, "group updated by")
		assert.Equal(t, "", got.Description.Value, "group description")
		assert.NotEmpty(t, got.Name.Value, "group name unchanged")
		assert.EqualValues(t, 1, got.Version, "group version unchanged")
	})
}

func TestUpdateVarGroupVars(t *testing.T) {
	s := Group

	t.Run("update 404", func(t *testing.T) {
		req := &pb.UpdateVarGroupVarsReq{
			GroupId:        1235,
			VarDefinitions: nil,
		}
		_, err := s.UpdateVarGroupVars(ctx, req)
		tf.WantErrorStatus(t, err, codes.NotFound, "update a non-exist group")
	})

	t.Run("overwrite", func(t *testing.T) {
		storageCleanup(t)

		group := prepareVarGroup(t)
		const anotherOne = "another"
		vd := tf.NewVarDef("age", "199")
		req := &pb.UpdateVarGroupVarsReq{
			GroupId:        group.GroupId,
			PreVersion:     group.Version,
			VarDefinitions: []*pb.VarDefinition{vd},
			Overwrite:      true,
			CommitMessage:  "overwrite with age",
			UpdatedBy:      anotherOne,
		}
		resp, err := Group.UpdateVarGroupVars(ctx, req)
		require.NoError(t, err, "update group vars")
		got := resp.Group
		assert.EqualValues(t, 2, got.Version, "group version incr")
		tf.WantProtoSliceEqual(t, req.VarDefinitions, got.VarDefinitions, "group vars")
	})

	// todo: append.

	t.Run("stale version", func(t *testing.T) {
		storageCleanup(t)

		group := prepareV2VarGroup(t)
		req := &pb.UpdateVarGroupVarsReq{
			GroupId:        group.GroupId,
			PreVersion:     group.Version - 1,
			VarDefinitions: []*pb.VarDefinition{},
			Overwrite:      true,
		}
		_, err := Group.UpdateVarGroupVars(ctx, req)
		tf.WantErrorStatus(t, err, codes.FailedPrecondition)
	})
}

func TestGetVarGroup(t *testing.T) {
	s := Group
	storageCleanup(t)
	group := prepareV2VarGroup(t)

	t.Run("get latest", func(t *testing.T) {
		resp, err := s.GetVarGroup(ctx, &pb.GetVarGroupReq{GroupId: group.GroupId})
		require.NoError(t, err, "get group")
		got := resp.Group
		tf.WantProtoEqual(t, group, got)
	})

	t.Run("get by version", func(t *testing.T) {
		resp, err := s.GetVarGroup(ctx, &pb.GetVarGroupReq{
			GroupId: group.GroupId,
			Version: group.Version - 1,
		})
		require.NoError(t, err, "get group")
		got := resp.Group
		assert.EqualValues(t, group.Version-1, got.Version, "get specified version")
	})

	t.Run("get 404", func(t *testing.T) {
		_, err := s.GetVarGroup(ctx, &pb.GetVarGroupReq{GroupId: 404})
		tf.WantErrorStatus(t, err, codes.NotFound, "get a non-exist group")
	})

	t.Run("get version 404", func(t *testing.T) {
		_, err := s.GetVarGroup(ctx, &pb.GetVarGroupReq{
			GroupId: group.GroupId,
			Version: group.Version + 1,
		})

		tf.WantErrorStatus(t, err, codes.NotFound, "get a non-exist version")
	})
}

func TestQueryVarGroup(t *testing.T) {
	s := Group
	storageCleanup(t)
	group := prepareVarGroup(t)
	req := &pb.QueryVarGroupReq{GroupIds: []uint64{group.GroupId}}
	resp, err := s.QueryVarGroup(ctx, req)
	require.NoError(t, err, "query group")
	assert.NotEmpty(t, resp.Groups, "query group")
}

func TestDeleteVarGroup(t *testing.T) {
	const (
		user1 = "alice"
		user2 = "bob"
	)

	s := Group
	storageCleanup(t)
	group := prepareVarGroup(t, func(req *pb.CreateVarGroupReq) {
		req.CreatedBy = user1
	})

	t.Run("permission denied", func(t *testing.T) {
		_, err := s.DeleteVarGroup(ctx, &pb.DeleteVarGroupReq{
			GroupId:   group.GroupId,
			DeletedBy: user2,
		})
		tf.WantErrorStatus(t, err, codes.PermissionDenied, "delete by non-creator")
	})

	t.Run("delete 404", func(t *testing.T) {
		_, err := s.DeleteVarGroup(ctx, &pb.DeleteVarGroupReq{
			GroupId:   404,
			DeletedBy: user1,
		})
		tf.WantErrorStatus(t, err, codes.NotFound, "delete a non-exist group")
	})

	t.Run("delete", func(t *testing.T) {
		_, err := s.DeleteVarGroup(ctx, &pb.DeleteVarGroupReq{
			GroupId:   group.GroupId,
			DeletedBy: user1,
		})
		require.NoError(t, err, "delete")

		_, err = s.GetVarGroup(ctx, &pb.GetVarGroupReq{GroupId: group.GroupId})
		tf.WantErrorStatus(t, err, codes.NotFound, "group deleted")
	})
}

func TestEncryptSensitiveValue(t *testing.T) {
	const (
		user1  = "alice"
		user2  = "bob"
		secret = "secret"
	)

	s := Group
	resp, err := s.EncryptSensitiveValue(ctx, &pb.EncryptSensitiveValueReq{
		Plain:     secret,
		EncryptBy: user1,
	})
	require.NoError(t, err, "encrypt")

	got, err := s.DecryptSensitiveValue(ctx, &pb.DecryptSensitiveValueReq{
		Cipher:    resp.Cipher,
		DecryptBy: user1,
	})
	require.NoError(t, err, "decrypt by owner")
	assert.EqualValues(t, secret, got.Plain, "decrypted plain text")

	_, err = s.DecryptSensitiveValue(ctx, &pb.DecryptSensitiveValueReq{
		Cipher:    resp.Cipher,
		DecryptBy: user2,
	})
	tf.WantErrorStatus(t, err, codes.Unauthenticated, "decrypted by non-owner")

	ctrl := gomock.NewController(t)

	mockClient := authzservice_mock.NewMockClient(ctrl)
	monkey.Patch(authzProvider.GetClient, func() authzservice.Client {
		return mockClient
	})
	monkey.PatchInstanceMethod(reflect.TypeOf(mockClient), "BatchCheckPermission", func(ctx context.Context, Req *authz.BatchCheckPermissionRequest, callOptions ...callopt.Option) (r *authz.BatchCheckPermissionResponse, err error) {
		return &authz.BatchCheckPermissionResponse{Results: []*authz.AuthzResult{
			{
				Result: true,
			},
		}}, nil
	})

	got, err = s.DecryptSensitiveValue(ctx, &pb.DecryptSensitiveValueReq{
		Cipher:     resp.Cipher,
		DecryptBy:  user2,
		PipelineId: 1,
		FromBitsGw: true,
	})
	require.NoError(t, err, "decrypt by owner")
	assert.EqualValues(t, secret, got.Plain, "decrypted plain text")
}
