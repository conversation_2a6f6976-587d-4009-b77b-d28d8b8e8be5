package dentity

import (
	"encoding/json"

	pb "code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/lang/gg/gslice"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// ProtoJsonMarshalSlice 对一组 protobuf message 进行 json 编码.
// 对于每个 protobuf message, 使用 protojson 编码.
func ProtoJsonMarshalSlice[T proto.Message](messages []T) ([]byte, error) {
	raw := make([]json.RawMessage, 0, len(messages))
	for i, def := range messages {
		data, err := protojson.Marshal(def)
		if err != nil {
			desc := def.ProtoReflect().Descriptor()
			return nil, errors.Wrapf(err, "marshal json %s[%d]", desc.Name(), i)
		}
		raw = append(raw, data)
	}

	data, err := json.Marshal(raw)
	if err != nil {
		return nil, errors.Wrap(err, "marshal json var_definitions")
	}
	return data, nil
}

// ProtoJsonUnmarshalSlice 对一组 protobuf message 进行 json 解码.
// 对于每个 protobuf message, 使用 protojson 解码.
func ProtoJsonUnmarshalSlice[T proto.Message, S []T](data []byte, messagesPtr *S) error {
	if len(data) == 0 || messagesPtr == nil {
		return nil
	}
	var raw []json.RawMessage
	if err := json.Unmarshal(data, &raw); err != nil {
		return errors.Wrap(err, "unmarshal json array")
	}

	var m T
	pr := m.ProtoReflect()
	messages := make(S, 0, len(raw))
	for i, r := range raw {
		message := pr.New().Interface()
		opt := protojson.UnmarshalOptions{DiscardUnknown: true}
		if err := opt.Unmarshal(r, message); err != nil {
			return errors.Wrapf(err, "unmarshal json var_definitions[%d]", i)
		}
		messages = append(messages, message.(T))
	}

	*messagesPtr = messages
	return nil
}

func CompatibleOption(vd *pb.VarDefinition) {
	if vd.GetUiOption().GetInputType() != pb.UIInputType_UI_INPUT_TYPE_SELECT &&
		vd.GetUiOption().GetInputType() != pb.UIInputType_UI_INPUT_TYPE_MULTI_SELECT {
		return
	}

	if len(vd.GetUiOption().GetNewOptions()) == 0 && len(vd.GetUiOption().GetOptions()) != 0 {
		vd.UiOption.NewOptions = gslice.Map(vd.GetUiOption().GetOptions(), func(op string) *pb.Option {
			return &pb.Option{
				Name: op,
			}
		})
	}

	return
}
