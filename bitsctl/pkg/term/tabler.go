/**
 * @Date: 2022/7/27
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package term

import (
	"os"

	"github.com/jedib0t/go-pretty/v6/table"
)

func NewTabler(title string, header table.Row, body []table.Row) table.Writer {
	writer := table.NewWriter()
	writer.SetOutputMirror(os.Stdout)
	writer.SetStyle(table.StyleLight)
	writer.SetTitle(title)

	writer.AppendHeader(header)

	for _, row := range body {
		writer.AppendRow(row)
		writer.AppendSeparator()
	}

	return writer
}
