package git

import "fmt"

func (t *Git) ShowCommits(base, head string, patch bool) (string, error) {
	args := []string{
		`-c`,
		`color.ui=always`,
		`--no-pager`,
		`log`,
	}
	if patch {
		args = append(args, "-p")
	}
	args = append(args, []string{
		fmt.Sprintf("%v..%v", base, head),
		"--",
	}...)
	output, err := RunGitCommand(RunGitCommandParameters{
		Args:    args,
		OnError: "throw",
	})
	if err != nil {
		return "", err
	}
	return output, nil
}
