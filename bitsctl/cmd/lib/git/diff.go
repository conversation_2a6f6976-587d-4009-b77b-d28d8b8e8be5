package git

import "strings"

func (t *Git) DetectStagedChanges() (bool, error) {
	ret, err := RunGitCommand(RunGitCommandParameters{
		Args:    []string{`--no-pager`, `diff`, `--no-ext-diff`, `--shortstat`, `--cached`},
		OnError: "throw",
	})
	if err != nil {
		return false, err
	}
	return len(ret) > 0, nil
}

func (t *Git) GetUnstagedChanges() (string, error) {
	ret, err := RunGitCommand(RunGitCommandParameters{
		Args: []string{
			`-c`,
			`color.ui=always`,
			`--no-pager`,
			`diff`,
			`--no-ext-diff`,
			`--stat`,
		},
		OnError: "throw",
	})
	if err != nil {
		return "", err
	}
	return ret, nil
}

func (t *Git) ShowDiff(left, right string) (string, error) {
	ret, err := RunGitCommand(RunGitCommandParameters{
		Args: []string{
			`-c`,
			`color.ui=always`,
			`--no-pager`,
			`diff`,
			`--no-ext-diff`,
			left,
			right,
			`--`,
		},
		OnError: "throw",
	})
	if err != nil {
		return "", err
	}
	return ret, nil
}

func (t *Git) IsDiffEmpty(left, right string) (bool, error) {
	ret, err := RunGitCommand(RunGitCommandParameters{
		Args: []string{
			`--no-pager`,
			`diff`,
			`--no-ext-diff`,
			`--shortstat`,
			left,
			right,
			`--`,
		},
		OnError: "throw",
	})
	if err != nil {
		return false, err
	}
	return len(ret) == 0, nil
}

func (t *Git) GetStagedChanges() (string, error) {
	return RunGitCommand(RunGitCommandParameters{
		Args:    []string{`--no-pager`, `diff`, `--cached`},
		OnError: "throw",
	})
}

func (t *Git) GetDiffFiles(sha1, sha2 string) ([]string, error) {
	ret, err := RunGitCommand(RunGitCommandParameters{
		Args: []string{
			`--no-pager`,
			`diff`,
			`--name-only`,
			sha1,
			sha2,
		},
		OnError: "throw",
	})
	if err != nil {
		return nil, err
	}
	return strings.Split(ret, "\n"), nil
}
