load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "terminal_os",
    srcs = [
        "dawin.go",
        "linux.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/logger/terminal_os",
    visibility = ["//visibility:public"],
    deps = select({
        "@io_bazel_rules_go//go/platform:android": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:darwin": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:freebsd": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:ios": [
            "@org_golang_x_sys//unix",
        ],
        "@io_bazel_rules_go//go/platform:linux": [
            "@org_golang_x_sys//unix",
        ],
        "//conditions:default": [],
    }),
)
