package bitsapi

import (
	"database/sql"
	"database/sql/driver"
	"errors"
	"fmt"
	"net/http"

	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/lang/gg/gresult"
	"github.com/google/go-querystring/query"
	"github.com/tidwall/gjson"
)

/** */

type CreateBitsDevContributionGitlabMRConfig struct {
	RepoPath string `json:"repo_path"`
}
type CreateBitsDevContributionRequest struct {
	DevID          int64                                    `json:"dev_basic_id"`
	Title          string                                   `json:"title"`
	UserName       string                                   `json:"username"`
	SourceBranch   string                                   `json:"source_branch"`
	TargetBranch   string                                   `json:"target_branch"`
	GitlabMRConfig *CreateBitsDevContributionGitlabMRConfig `json:"gitlab_mr_config"`
}

type CreateBitsDevContributionResponse struct {
	ContributionID int64  `json:"contribution_id"`
	URL            string `json:"url"`
	Status         string `json:"status"` // failed succeeded
	Msg            string `json:"msg"`
}

func (client *client) CreateBitsDevContribution(request *CreateBitsDevContributionRequest) gresult.R[*CreateBitsDevContributionResponse] {
	u := "/api/smr/v2/contribution/create"
	response := new(RawResponse[*CreateBitsDevContributionResponse])
	resp, err := client.httpclient.
		R().
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[*CreateBitsDevContributionResponse](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*CreateBitsDevContributionResponse](errors.New(errMsg))
	}

	ret, err := response.Unwrap().Get()
	if err == nil && ret != nil && ret.Status == "failed" {
		errMsg := fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), ret.Msg)
		return gresult.Err[*CreateBitsDevContributionResponse](errors.New(errMsg))
	}
	return response.Unwrap()
}

/** */

type GetBitsDevContributionListRequest struct {
	ContributionID int64 `json:"contribution_id" url:"contribution_id"`
}

type GetBitsDevContributionListResponse struct {
	Title          string `json:"title"`
	Author         string `json:"author"`
	Status         string `json:"status"`
	BizConfig      string `json:"biz_config"`
	DevID          int64  `json:"dev_basic_id"`
	CreateTime     int64  `json:"create_time"`
	UpdateTime     int64  `json:"update_time"`
	ContributionID int64  `json:"contribution_id"`
}

func (client *client) GetBitsDevContributionList(request *GetBitsDevContributionListRequest) gresult.R[[]*GetBitsDevContributionListResponse] {
	u := "/api/smr/v2/contribution/related_code_change"
	response := new(RawResponse[[]*GetBitsDevContributionListResponse])
	query, err := query.Values(request)
	if err != nil {
		return gresult.Err[[]*GetBitsDevContributionListResponse](err)
	}
	resp, err := client.httpclient.
		R().
		SetQueryParamsFromValues(query).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[[]*GetBitsDevContributionListResponse](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[[]*GetBitsDevContributionListResponse](errors.New(errMsg))
	}
	return response.Unwrap()
}

/** */

type GetBitsDevContributionInfoRequest struct {
	ContributionID int64 `json:"contribution_id" url:"contribution_id"`
	//Username       string `json:"username" url:"username"`
}

type GetBitsDevContributionInfoResponse struct {
	DevId          int    `json:"dev_id"`
	Author         string `json:"author"`
	Status         string `json:"status"`
	CodeChangeId   int    `json:"code_change_id"`
	BizConfig      string `json:"biz_config"`
	DevBasicId     int    `json:"dev_basic_id"`
	CreateTime     int    `json:"create_time"`
	UpdateTime     int    `json:"update_time"`
	ContributionId int64  `json:"contribution_id"`
	Title          string `json:"title"`
	SourceBranch   string `json:"source_branch"`
	TargetBranch   string `json:"target_branch"`
	ChecksStatus   string `json:"checks_status"`
	LarkGroupId    string `json:"lark_group_id"`
	RepoPath       string `json:"repo_path"`
	Url            string `json:"url"`
	ChangeId       int64  `json:"change_id"`
	GroupName      string `json:"group_name"`
	BaseCommit     string `json:"base_commit"`
	WIP            *bool  `json:"wip"`
}

// todo wangfei
func (client *client) GetBitsDevContributionInfo(request *GetBitsDevContributionInfoRequest) gresult.R[*GetBitsDevContributionInfoResponse] {
	u := "/api/smr/v2/contribution/info"
	response := new(RawResponse[*GetBitsDevContributionInfoResponse])
	query, err := query.Values(request)
	if err != nil {
		return gresult.Err[*GetBitsDevContributionInfoResponse](err)
	}
	resp, err := client.httpclient.
		R().
		SetQueryParamsFromValues(query).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[*GetBitsDevContributionInfoResponse](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*GetBitsDevContributionInfoResponse](errors.New(errMsg))
	}
	return response.Unwrap()
}

/** */
type FilterOption string

const (
	FilterOptionStack  = "stacked"
	FilterOptionNormal = ""
)

type GetBitsDevAvailableContributionsRequest struct {
	RepoPath     string       `json:"repo_path" url:"repo_path"`
	SourceBranch string       `json:"source_branch,omitempty" url:"source_branch,omitempty"`
	DevID        int64        `json:"dev_basic_id,omitempty" url:"dev_basic_id,omitempty"`
	FilterOption FilterOption `json:"filter_option,omitempty" url:"filter_option,omitempty"`
	Author       string       `json:"author" url:"author,omitempty"`
}
type ReviewsCount struct {
	Total           int `json:"total"`
	PassNumber      int `json:"pass_number"`
	RejectionNumber int `json:"rejection_number"`
}
type GetBitsDevAvailableContributionsResponse struct {
	Title          string       `json:"title"`
	Author         string       `json:"author"`
	Status         string       `json:"status"`
	BizConfig      string       `json:"biz_config"`
	DevID          int64        `json:"dev_basic_id"`
	CreateTime     int64        `json:"create_time"`
	UpdateTime     int64        `json:"update_time"`
	SourceBranch   string       `json:"source_branch"`
	TargetBranch   string       `json:"target_branch"`
	ContributionID int64        `json:"contribution_id"`
	ReviewerCount  ReviewsCount `json:"reviewer_count"`
	CommentNumber  int64        `json:"comment_number"`
	ReviewStatus   string       `json:"review_status"`
	URL            string       `json:"url"`
}

func (client *client) GetBitsDevAvailableContributions(request *GetBitsDevAvailableContributionsRequest) gresult.R[[]*GetBitsDevAvailableContributionsResponse] {
	u := "/api/smr/v2/contribution/available_code_changes"
	response := new(RawResponse[[]*GetBitsDevAvailableContributionsResponse])
	query, err := query.Values(request)
	if err != nil {
		return gresult.Err[[]*GetBitsDevAvailableContributionsResponse](err)
	}
	resp, err := client.httpclient.
		R().
		SetQueryParamsFromValues(query).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[[]*GetBitsDevAvailableContributionsResponse](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[[]*GetBitsDevAvailableContributionsResponse](errors.New(errMsg))
	}
	return response.Unwrap()
}

/** */
type PostBitsDevContributionInfoOptions struct {
	TargetBranch string `json:"target_branch,omitempty"`
	Title        string `json:"title,omitempty"`
	Description  string `json:"description,omitempty"`
	Wip          *bool  `json:"wip,omitempty"`
	Draft        *bool  `json:"draft,omitempty"`
}
type PostBitsDevContributionInfoRequest struct {
	ContributionID int64                               `json:"contribution_id"`
	UpdateOptions  *PostBitsDevContributionInfoOptions `json:"update_options,omitempty"`
}

func (client *client) PostBitsDevContributionInfo(request *PostBitsDevContributionInfoRequest) gresult.R[*string] {
	u := "/api/smr/v2/contribution/info"
	response := new(RawResponse[*string])
	resp, err := client.httpclient.
		R().
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[*string](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*string](errors.New(errMsg))
	}

	return response.Unwrap()
}

type GetBitsForceOverrideInfoRequest struct {
	DevBasicID int64 `json:"dev_basic_id" url:"dev_basic_id"`
}

type GetBitsForceOverrideInfoReponse struct {
	IfNeed bool   `json:"if_need"`
	Action string `json:"action"`
	Branch string `json:"branch"`
}

func (client *client) GetBitsForceOverrideInfo(request *GetBitsForceOverrideInfoRequest) gresult.R[*GetBitsForceOverrideInfoReponse] {
	u := "/api/smr/v2/contribution/after_submission_process"
	response := new(RawResponse[*GetBitsForceOverrideInfoReponse])
	query, err := query.Values(request)
	resp, err := client.httpclient.
		R().
		SetQueryParamsFromValues(query).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[*GetBitsForceOverrideInfoReponse](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*GetBitsForceOverrideInfoReponse](errors.New(errMsg))
	}
	return response.Unwrap()
}

type PostCloseChangeRequest struct {
	ContributionId int64 `json:"contribution_id"`
}

func (client *client) PostCloseChange(request *PostCloseChangeRequest) gresult.R[*string] {
	u := "/api/smr/v2/contribution/close"
	response := new(RawResponse[*string])
	resp, err := client.httpclient.
		R().
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[*string](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*string](errors.New(errMsg))
	}
	return response.Unwrap()
}

type GitlabMrCreationConfig struct {
	ProjectID          *int64 `json:"project_id"`
	RemoveSourceBranch *bool  `json:"remove_source_branch"`
}

type CreateDevChangeConfig struct {
	Title          string                  `json:"title"`
	SourceBranch   string                  `json:"source_branch"`
	TargetBranch   string                  `json:"target_branch"`
	BaseCommit     string                  `json:"base_commit"`
	RepoPath       string                  `json:"repo_path"`
	WIP            *bool                   `json:"wip"`
	GitlabMRConfig *GitlabMrCreationConfig `json:"gitlab_mr_config"`
}

type CreateDevChangesRequest struct {
	DevBasicID    int64                    `json:"dev_basic_id"`
	Username      string                   `json:"username"`
	CreateConfigs []*CreateDevChangeConfig `json:"create_configs"`
}

type CreationStatus int64

const (
	CreationStatus_succeeded CreationStatus = 1
	CreationStatus_failed    CreationStatus = 2
)

func (p CreationStatus) String() string {
	switch p {
	case CreationStatus_succeeded:
		return "succeeded"
	case CreationStatus_failed:
		return "failed"
	}
	return "<UNSET>"
}

func CreationStatusFromString(s string) (CreationStatus, error) {
	switch s {
	case "succeeded":
		return CreationStatus_succeeded, nil
	case "failed":
		return CreationStatus_failed, nil
	}
	return CreationStatus(0), fmt.Errorf("not a valid CreationStatus string")
}

func CreationStatusPtr(v CreationStatus) *CreationStatus { return &v }

func (p CreationStatus) MarshalText() ([]byte, error) {
	return []byte(p.String()), nil
}

func (p *CreationStatus) UnmarshalText(text []byte) error {
	q, err := CreationStatusFromString(string(text))
	if err != nil {
		return err
	}
	*p = q
	return nil
}

func (p *CreationStatus) Scan(value interface{}) (err error) {
	var result sql.NullInt64
	err = result.Scan(value)
	*p = CreationStatus(result.Int64)
	return
}

func (p *CreationStatus) Value() (driver.Value, error) {
	if p == nil {
		return nil, nil
	}
	return int64(*p), nil
}

type DevChangeInfo struct {
	ContributionID int64          `json:"contributionID"`
	SourceBranch   string         `json:"sourceBranch"`
	Url            string         `json:"url"`
	Status         CreationStatus `json:"status"`
	Msg            string         `json:"msg"`
}

type CreateDevChangesResponse struct {
	DevChanges []*DevChangeInfo `json:"devChanges"`
}

func (client *client) CreateDevChanges(request *CreateDevChangesRequest) gresult.R[[]*DevChangeInfo] {
	u := "/api/smr/v2/dev/changes/create"
	response := new(RawResponse[[]*DevChangeInfo])
	resp, err := client.httpclient.
		R().
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[[]*DevChangeInfo](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[[]*DevChangeInfo](errors.New(errMsg))
	}

	return response.Unwrap()
}

type ContributionCodeChangeUpdateOptions struct {
	Title              *string `json:"title"`
	Description        *string `json:"description"`
	TargetBranch       *string `json:"target_branch"`
	RemoveSourceBranch *bool   `json:"remove_source_branch"`
	Wip                *bool   `json:"wip"`
	BaseCommit         *string `json:"base_commit"`
}

type UpdateDevChange struct {
	ContributionID int64                                `json:"contributionID"`
	Options        *ContributionCodeChangeUpdateOptions `json:"options"`
}

type UpdateSMRDevChangesRequest struct {
	Username string             `json:"username"`
	Changes  []*UpdateDevChange `json:"changes"`
}

func (client *client) UpdateDevChanges(request *UpdateSMRDevChangesRequest) gresult.R[*string] {
	u := "/api/smr/v2/dev/changes/update"
	response := new(RawResponse[*string])
	resp, err := client.httpclient.
		R().
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[*string](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*string](errors.New(errMsg))
	}

	return response.Unwrap()
}

type GetDevChangesRequest struct {
	DevBasicID int64 `url:"dev_basic_id"`
}

type DevChange struct {
	Id           int64  `json:"id"`
	DevId        int64  `json:"devId"`
	CodeChangeId int64  `json:"codeChangeId"`
	DevBasicId   int64  `json:"devBasicId"`
	ChangeId     int64  `json:"changeId"`
	CreatedAt    int64  `json:"createdAt"`
	UpdatedAt    int64  `json:"updatedAt"`
	Title        string `json:"title"`
	Author       string `json:"author"`
	Status       string `json:"status"`
	Type         string `json:"type"`
	RepoPath     string `json:"repoPath"`
	LarkGroupId  string `json:"larkGroupId"`
	BizConfig    string `json:"bizConfig"`
	DevBasicType string `json:"devBasicType"`
	BaseCommit   string `json:"baseCommit"`
	HeadCommit   string `json:"headCommit"`
	SourceBranch string `json:"sourceBranch"`
	TargetBranch string `json:"targetBranch"`
	URL          string `json:"url"`
	WIP          *bool  `json:"wip"`
}

type GetDevChangesResponse struct {
	DevChanges []*DevChange `json:"devChanges"`
}

func (client *client) GetDevChanges(request *GetDevChangesRequest) gresult.R[*GetDevChangesResponse] {
	u := "/api/smr/v2/dev/changes"
	response := new(RawResponse[*GetDevChangesResponse])
	q, err := query.Values(request)
	if err != nil {
		return gresult.Err[*GetDevChangesResponse](err)
	}
	resp, err := client.httpclient.
		R().
		SetQueryParamsFromValues(q).
		SetResult(response).
		Get(u)
	if err != nil {
		return gresult.Err[*GetDevChangesResponse](err)
	}
	if resp.StatusCode() != http.StatusOK {
		errMsg := gjson.Get(conv.UnsafeBytesToStr(resp.Body()), "message").String()
		errMsg = fmt.Sprintf("logid: %v %v", resp.Header().Get("X-Tt-Logid"), errMsg)
		return gresult.Err[*GetDevChangesResponse](errors.New(errMsg))
	}
	return response.Unwrap()
}
