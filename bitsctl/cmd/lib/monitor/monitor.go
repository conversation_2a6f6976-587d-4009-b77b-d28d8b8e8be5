package monitor

import (
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/archive_log"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/preconditions"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/session"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/spiffy"
	"encoding/json"
	"fmt"
	"github.com/mitchellh/go-homedir"
	"net"
	"os"
	"path"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/errors"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/git"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/version"
	"github.com/go-resty/resty/v2"
)

const (
	Success     int8 = 0
	Fail             = 1
	Crash            = 2
	Interrupted      = 3
)

type EventResult struct {
	Err        error
	ResultType int8
}

type Spin struct {
	StartTime int64  `json:"start"`
	EndTime   int64  `json:"end"`
	Result    int8   `json:"cmd_result"`
	Cmd       string `json:"cmd"`
	ErrMsg    string `json:"err_msg"`
	ErrName   string `json:"err_name"`
	Ip        string `json:"ip"`
	Author    string `json:"author"`
	Host      string `json:"host"`
	Repo      string `json:"repo"`
	Version   string `json:"version"`
	EventID   string `json:"event_id"`
	EventType int64  `json:"event_type"`
	EventTime int64  `json:"event_time"`
	isSend    bool
}

var s = Spin{}

func CurrentSpin() *Spin {
	return &s
}

func (spin *Spin) Start() {
	spin.StartTime = time.Now().UnixMilli()
}

func (spin *Spin) End(result EventResult) {
	spin.composeResult(result)
	spin.send()
	spin.sendToDevExpert(result)
	spin.isSend = true
}

func (spin *Spin) IsEnd() bool {
	return spin.isSend
}

func (spin *Spin) composeResult(result EventResult) {
	spin.composeEventInfo()
	spin.composeGitInfo()
	spin.composeDeviceInfo()
	spin.composeCMDInfo()
	spin.composeError(result)
}

func (spin *Spin) composeError(result EventResult) {
	spin.Result = result.ResultType
	if result.Err == nil {
		return
	}
	if err, is := errors.IsCommonError(result.Err); is {
		spin.ErrName = err.Name
	}
	if _, is := errors.IsCrashError(result.Err); is {
		spin.ErrName = "Crash"
	}

	if err, is := errors.IsBitsCommandError(result.Err); is {
		spin.ErrName = err.Reason.Type.String()
		return
	}
	spin.ErrMsg = result.Err.Error()
}

func (spin *Spin) composeGitInfo() {
	g := git.ComposeGit()
	remote, _ := g.GetRemote()
	ss := session.New()
	user, err := ss.CurrentUser().Get()
	if err == nil {
		spin.Author = user.Email
	}
	spin.Repo = remote
}

func (spin *Spin) composeDeviceInfo() {
	spin.Ip = getIP()
	spin.Host = getDeviceName()
}

func (spin *Spin) composeCMDInfo() {
	spin.Version = version.Version()
	spin.Cmd = CurrentCMDString()
}

func CurrentCMDString() string {
	command := os.Args[1:]
	cmd := "bit"
	for _, s := range command {
		cmd += " "
		cmd += s
	}
	return cmd
}

func (spin *Spin) composeEventInfo() {
	t := time.Now().UnixMilli()
	spin.EndTime = t
	spin.EventType = 300001
	spin.EventID = "Bit_CLI_Operation"
	spin.EventTime = t
}

func getIP() string {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return ""
	}
	defer func(conn net.Conn) {
		if conn == nil {
			return
		}
		_ = conn.Close()
	}(conn)
	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String()
}

func getDeviceName() string {
	name, _ := os.Hostname()
	return name
}

type metrics struct {
	Event string `json:"event"`
}

type record struct {
	Metrics metrics `json:"metrics"`
	Product int     `json:"product"`
}

func (spin *Spin) send() {
	r, err := spin.composeBody()
	if err != nil {
		return
	}
	client := resty.New()
	headers := map[string]string{
		"Content-Type": "application/json",
		"Cookie":       "mobile_dev_platform_sid=89bba59d362db8c247133c3aa9c4d04d",
	}
	_, _ = client.R().
		SetHeaders(headers).
		SetBody(r).
		Post("https://bits.bytedance.net/api/metrics/collectV2")
}

type DevExpertAction struct {
	Name         string `json:"name"`
	ErrorMessage string `json:"error_message"`
	ErrorCode    int64  `json:"error_code"`
}

type DevExpertUser struct {
	Email string `json:"email"`
}

type DevExpertReq struct {
	Timestamp int64           `json:"timestamp"`
	Action    DevExpertAction `json:"action"`
	ExtraData interface{}     `json:"extra_data"`
	User      DevExpertUser   `json:"user"`
}

func (spin *Spin) sendToDevExpert(result EventResult) {
	if result.ResultType == Success {
		return
	}
	req := DevExpertReq{
		Timestamp: time.Now().Unix(),
		Action: DevExpertAction{
			Name:         spin.Cmd,
			ErrorMessage: spin.ErrMsg,
			ErrorCode:    int64(result.ResultType),
		},
		ExtraData: spin,
		User: DevExpertUser{
			Email: spin.Author,
		},
	}

	reqBytes, err := json.Marshal(req)
	if err != nil {
		return
	}
	//file, err := generateOncallFile()
	//if err != nil {
	//	return
	//}
	//open, err := os.Open(file)
	//if err != nil {
	//	return
	//}
	//defer func(open *os.File) {
	//	err := open.Close()
	//	if err != nil {
	//		return
	//	}
	//}(open)

	log, err := spiffy.GetLog().Load(nil)
	if err != nil {
		return
	}
	logs := log.HelperFunctions.QueryLogs()
	if len(logs) == 0 {
		_ = log.Update(func(data *[]spiffy.Log) error {
			return nil
		})
	}
	logBytes, err := json.Marshal(logs)
	if err != nil {
		return
	}
	headers := map[string]string{
		"x-app-id":  "44",
		"x-app-key": "c1d7673b67b892fc291709caaa52586a",
	}
	client := resty.New()
	_, _ = client.
		R().
		SetHeaders(headers).
		SetMultipartFields(&resty.MultipartField{
			Param:       "digest",
			ContentType: "application/json",
			Reader:      strings.NewReader(string(reqBytes)),
		}).
		//SetMultipartFields(&resty.MultipartField{
		//	Param:       "log",
		//	FileName:    path.Base(file),
		//	ContentType: "application/zip",
		//	Reader:      open,
		//}).
		SetMultipartFields(&resty.MultipartField{
			Param:       "error_log",
			ContentType: "application/json",
			Reader:      strings.NewReader(string(logBytes)),
		}).
		Post("https://es-expert.bytedance.net/dev_insight/api/v1/error/report")
}

func (spin *Spin) composeBody() (interface{}, error) {
	// send
	event, err := json.Marshal(spin)
	if err != nil {
		return nil, err
	}
	m := metrics{
		string(event),
	}
	r := record{
		m,
		0,
	}
	return r, nil
}

func generateOncallFile() (string, error) {

	var files []string
	pre := preconditions.Preconditions{}
	relativeTo, err := pre.GetRepoGitPathPrecondition()
	if err != nil {
		return "", err
	}

	// add dev_and_change file
	files = append(files, path.Join(relativeTo, ".bit_dev_change"))

	//file, err := reflogFile()
	//if err == nil {
	//	files = append(files, file)
	//}
	//current := time.Now()
	//for i := 0; i < 30; i++ {
	//	c := current.Add(time.Duration(-24*i) * time.Hour)
	//	timeString := c.Format("2006-01-02")
	//	fileName := path.Join(relativeTo, ".bit", "log", timeString)
	//	if _, err := os.Stat(fileName); err == nil {
	//		files = append(files, fileName)
	//	}
	//}

	home, err := homedir.Dir()
	if err != nil {
		return "", err
	}
	gitUtil := git.ComposeGit()
	url, _ := gitUtil.GetRemote()
	group, project, _ := gitUtil.ParseGitRepo(url)
	fullPath := fmt.Sprintf("%v/%v", group, project)
	dest := path.Join(home, ".bit", "logs", fullPath)
	target, err := archive_log.Export(files, dest)
	if err != nil {
		return "", err
	}
	return target, nil
}

func reflogFile() (string, error) {
	file, err := os.CreateTemp("", "reflog")
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {

		}
	}(file)
	gitUtil := git.ComposeGit()
	reflog, err := gitUtil.Reflog()
	if err != nil {
		return "", err
	}
	_, err = file.WriteString(reflog)
	if err != nil {
		return "", err
	}
	return file.Name(), err

}
