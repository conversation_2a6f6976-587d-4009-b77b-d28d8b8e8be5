package version_commands

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/bitsctl/cmd/version"
	"github.com/spf13/cobra"
)

func NewVersionCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "version",
		Short: "Check the current version. // 查看当前版本",
		Long: `Check the current version.
 查看当前版本`,
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Println(version.Version())
			return nil
		},
	}
	return cmd
}
