package change_commands

import (
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/actions/bits_change"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/context"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/runner"
	"github.com/spf13/cobra"
)

func NewBottomCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "bottom",
		Short: "Switch to the bottom change // 切换到栈底 change",
		Long:  `Switch to the bottom change // 切换到栈底 change`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runner.Bit(cmd, args, func(context *context.Context) error {
				return bits_change.CheckoutChangeWithDirection(context, bits_change.Bottom)
			})
		},
	}
	return cmd
}
