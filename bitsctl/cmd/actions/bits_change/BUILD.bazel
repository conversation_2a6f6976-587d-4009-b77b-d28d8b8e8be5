load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "bits_change",
    srcs = [
        "checkout.go",
        "create.go",
        "delete.go",
        "list.go",
        "set_parent.go",
        "show.go",
        "title.go",
        "uncommit.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/bitsctl/cmd/actions/bits_change",
    visibility = ["//visibility:public"],
    deps = [
        "//bitsctl/cmd/actions",
        "//bitsctl/cmd/actions/bits_contribution",
        "//bitsctl/cmd/lib/bitsapi",
        "//bitsctl/cmd/lib/context",
        "//bitsctl/cmd/lib/engine",
        "//bitsctl/cmd/lib/errors",
        "//bitsctl/cmd/lib/prompt",
        "//bitsctl/cmd/lib/spiffy",
        "@com_github_alecaivazis_survey_v2//:survey",
        "@com_github_alecaivazis_survey_v2//terminal",
        "@com_github_mgutz_ansi//:ansi",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gslice",
    ],
)
