load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cac",
    srcs = [
        "code_review_find_rule.go",
        "code_review_format.go",
        "code_review_strict_check.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/bitsctl/cmd/actions/cac",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/sdk/code_review_rules",
        "@com_github_sergi_go_diff//diffmatchpatch",
        "@com_github_sirupsen_logrus//:logrus",
        "@com_github_spf13_cobra//:cobra",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
        "@org_byted_code_lang_gg//gresult",
    ],
)
