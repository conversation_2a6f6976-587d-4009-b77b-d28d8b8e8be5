package actions

import (
	"fmt"
	"os"
	"strings"

	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/context"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/utils"
)

var FILE_NAME = "bit_stack_edit"
var FILE_FOOTER = []string{
	"#",
	"# Stack will be rearranged on trunk to match the above order.",
}

type createStackEditFileOps struct {
	branchNames []string
	tmpDir      string
}

func createStackEditFile(opts createStackEditFileOps, context *context.Context) (string, error) {
	lines := opts.branchNames
	for i, j := 0, len(lines)-1; i < j; i, j = i+1, j-1 {
		lines[i], lines[j] = lines[j], lines[i]
	}
	var trunk *string
	err := error(nil)
	if trunk, err = context.MetaCache.Trunk(); err != nil {
		return "", err
	}
	s := fmt.Sprintf("# %v (trunk, shown for orientation)", utils.SafeStr(trunk))
	lines = append(lines, s)
	lines = append(lines, FILE_FOOTER...)
	fileContents := strings.Join(lines, "\n")
	if err = os.WriteFile(opts.tmpDir, []byte(fileContents), 0644); err != nil {
		return "", err
	}
	return opts.tmpDir, err
}

func parseEditFile(filePath string) ([]string, error) {
	byteArr := make([]byte, 0)
	err := error(nil)
	if byteArr, err = os.ReadFile(filePath); err != nil {
		return nil, err
	}
	content := strings.TrimSpace(string(byteArr))
	lines := strings.Split(string(content), "\n")
	for i, j := 0, len(lines)-1; i < j; i, j = i+1, j-1 {
		lines[i], lines[j] = lines[j], lines[i]
	}
	branches := make([]string, 0)
	for _, line := range lines {
		idx := strings.Index(line, "#")
		if idx >= 0 {
			branches = append(branches, strings.TrimSpace(line[:idx]))
		} else {
			branches = append(branches, line)
		}
	}
	ret := make([]string, 0)
	for _, b := range branches {
		if len(b) > 0 {
			ret = append(ret, b)
		}
	}
	return ret, nil
}
