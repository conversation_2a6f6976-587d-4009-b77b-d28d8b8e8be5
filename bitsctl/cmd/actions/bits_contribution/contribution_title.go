package bits_contribution

import (
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/prompt"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gslice"
	"fmt"
	"regexp"
	"strings"

	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/context"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/errors"
	bstring "code.byted.org/gopkg/lang/strings"
	"code.byted.org/gopkg/lang/v2/slicex"
	"github.com/AlecAivazis/survey/v2"
	"github.com/AlecAivazis/survey/v2/terminal"
	"github.com/mgutz/ansi"
)

type getContributionTitleArgs struct {
	branchName     string
	contributionID int64
	EditMRTitle    bool
}

const titleReg = "^(?:feat|fix|docs|style|build|refactor|test|chore)(\\(.+\\))?[:：]\\s*"

var re = regexp.MustCompile(titleReg)

func getTTMRTitle(title string) string {
	ret := re.ReplaceAllString(title, "")
	ret = strings.TrimSpace(ret)
	return choose.If(len(ret) == 0, title, ret)
}

func getContributionTitle(args getContributionTitleArgs, context *context.Context) (string, error) {

	title := ""
	changeContext, err := context.MetaCache.GetChangeContext(args.branchName)
	if err == nil && changeContext.Current != nil {
		title = changeContext.Current.Title
	}
	// 取 change 第一个 commit 作为 title
	if len(title) == 0 || strings.HasSuffix(args.branchName, title) {
		yellow := ansi.Yellow
		reset := ansi.Reset

		context.Splog.NewLine()

		// 获取第一个 commit 作为 title
		arr, err := context.MetaCache.GetAllCommits(args.branchName, "READABLE")
		if err != nil {
			return "", err
		}
		if len(arr) == 0 {
			goto setTitle
		}

		if len(arr) == 1 {
			goto setCommit
		}

		// 输出检测到当前 change 没有标题
		context.Splog.Info(fmt.Sprintf("Detected that current change (%v) has no title, FYI: Here's the commit list:", args.branchName))

		context.Splog.NewLine()

		// 高亮提示依据 commit 信息输入标题
		arr = gslice.ReverseClone(arr)
		gslice.ForEach(arr, func(commit string) {
			context.Splog.Info(fmt.Sprintf("🌲 %v%v%v", yellow, commit, reset))
		})

		context.Splog.NewLine()
		context.Splog.NewLine()

		if !context.Interactive {
			goto setCommit
		}

		title, err = prompt.AskForString(fmt.Sprintf("Please input MR title ([Enter] to skip):"), func(val string) error {
			return nil
		})

		if err != nil {
			return "", err
		}

	setCommit:
		if len(title) == 0 || strings.HasSuffix(args.branchName, title) {
			title = getTTMRTitle(strings.Split(arr[0], " - ")[1])
		}
		// 输出 change 及 title 信息
		context.Splog.Info(fmt.Sprintf("Change: %v\nTitle: %v", args.branchName, title))

		return title, nil
	}
setTitle:
	// 空 change 则用 change 标题
	if len(title) == 0 {
		newPrefix, _ := context.MetaCache.ChangePrefix()
		var rm string
		if strings.HasPrefix(args.branchName, newPrefix) {
			rm = newPrefix
		}
		title = bstring.RemoveString(args.branchName, rm)
	}
	return title, nil
}

func customChangeTitle(context *context.Context, infos []getContributionInfoForBranchesReturnType) ([]getContributionInfoForBranchesReturnType, error) {
	if len(infos) == 0 {
		return infos, nil
	}
	choices := slicex.Map(infos, func(from getContributionInfoForBranchesReturnType) string {
		return from.Title
	})

	if len(choices) == 0 {
		return infos, nil
	}

	reset := ansi.Reset
	blue := ansi.Blue
	context.Splog.Info(fmt.Sprintf("%vYou can edit change title before submit to bits, it will be title of bits mr%v\n", blue, reset))
	var chosen []string
	prompt := &survey.MultiSelect{
		Message: "Which title you want to edit?",
		Options: choices,
	}
	err := survey.AskOne(prompt, &chosen, survey.WithPageSize(20))
	if err == terminal.InterruptErr {
		return nil, errors.NewKilledError()
	} else if err != nil {
		return nil, err
	}
	if len(chosen) == 0 {
		return infos, nil
	}

	return getTitle(infos, chosen)
}

func getTitle(infos []getContributionInfoForBranchesReturnType, chosen []string) ([]getContributionInfoForBranchesReturnType, error) {
	titleMaps := map[string]string{}
	for _, oldTitle := range chosen {
		msg := fmt.Sprintf("New title for (%v)", oldTitle)
		var newTitle string
		prompt := &survey.Input{
			Message: msg,
		}
		err := survey.AskOne(prompt, &newTitle)
		if err != nil {
			return infos, nil
		}
		titleMaps[oldTitle] = newTitle
	}
	var ret []getContributionInfoForBranchesReturnType
	for _, info := range infos {
		if newTitle, has := titleMaps[info.Title]; has {
			info.Title = newTitle
		}
		ret = append(ret, info)
	}
	return ret, nil
}
