package sync

import (
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/context"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/git"
	"code.byted.org/devinfra/hagrid/bitsctl/cmd/lib/preconditions"
	"code.byted.org/lang/gg/choose"
)

type ActionParams struct {
	Force              bool
	Delete             bool
	ShowDeleteProgress bool
	Restack            bool
	Branch             string
	MergeTarget        bool
	RebaseTarget       bool
	FromCreate         bool
}

func Action(ctx *context.Context, devBasicID int64, params *ActionParams) error {
	item := preconditions.Preconditions{}
	err := item.UncommittedTrackedChangesPrecondition()
	if err != nil {
		return err
	}

	dev := ComposeSyncInfo(ctx, devBasicID)
	if dev.IsErr() {
		return dev.Err()
	}

	err = SyncDevInfo(ctx)
	if err != nil {
		return err
	}

	err = PullTrunk(
		ctx,
		params.Branch,
		params.MergeTarget,
		choose.If(params.RebaseTarget, git.Rebase, git.Merge),
	)
	if err != nil {
		return err
	}

	PullChanges(ctx, dev.Value())

	return nil
}
