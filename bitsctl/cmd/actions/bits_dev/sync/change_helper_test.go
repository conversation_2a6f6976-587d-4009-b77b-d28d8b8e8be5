package sync

import (
	"reflect"
	"testing"
)

func TestHasDiverge(t *testing.T) {
	type args struct {
		local  []*ChangeInfo
		remote []*ChangeInfo
	}
	tests := []struct {
		name string
		args args
		want []*ChangeInfo
	}{
		{
			name: "no diverge",
			args: args{
				local: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "2",
					},
					{
						SourceBranch: "3",
					},
				},
				remote: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "2",
					},
					{
						SourceBranch: "3",
					},
				},
			},
			want: []*ChangeInfo{},
		},
		{
			name: "has diverge",
			args: args{
				local: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "4",
					},
					{
						SourceBranch: "5",
					},
				},
				remote: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "2",
					},
					{
						SourceBranch: "3",
					},
				},
			},
			want: []*ChangeInfo{
				{
					SourceBranch: "4",
				},
				{
					SourceBranch: "5",
				},
			},
		},
		{
			name: "fore diverge",
			args: args{
				local: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "4",
					},
					{
						SourceBranch: "5",
					},
				},
				remote: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "4",
					},
					{
						SourceBranch: "5",
					},
					{
						SourceBranch: "6",
					},
				},
			},
			want: []*ChangeInfo{},
		},
		{
			name: "fore diverge",
			args: args{
				local: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
					{
						SourceBranch: "4",
					},
					{
						SourceBranch: "5",
					},
				},
				remote: []*ChangeInfo{
					{
						SourceBranch: "1",
					},
				},
			},
			want: []*ChangeInfo{
				{
					SourceBranch: "4",
				},
				{
					SourceBranch: "5",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := diffChanges(tt.args.local, tt.args.remote)

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("diffChanges() got = %v, want %v", got, tt.want)
			}
		})
	}
}
