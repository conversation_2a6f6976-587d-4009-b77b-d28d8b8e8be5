package translation

import (
	"regexp"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/lang/gg/gslice"
)

func validateNotification(notification *dslpb.Notification) bool {
	if notification == nil {
		return false
	}

	if notification.Name == "" {
		return false
	}

	if len(notification.GetWhen().GetStatus()) == 0 && notification.GetWhen().GetTimeout() == 0 {
		return false
	}
	switch notification.Type {
	case dslpb.NotificationType_NOTIFICATION_TYPE_LARK:

	case dslpb.NotificationType_NOTIFICATION_TYPE_WEBHOOK:
		return validateWebhook(notification.GetWebhook())
	default:
		return false
	}

	return true
}

func validateWebhook(webhook *dslpb.Webhook) bool {

	if webhook.HttpAction == nil || webhook.ActionType != dslpb.WebhookActionType_WEBHOOK_ACTION_TYPE_HTTP {
		return false
	}
	if webhook.GetHttpAction().Method == "" {
		return false
	}
	if webhook.GetHttpAction().Url == "" {
		return false
	}

	return true
}

var varRegexp = regexp.MustCompile(`{{.*}}`)

func moveVarInUserToTypes(notification *dslpb.Notification) {
	if notification.GetLark() == nil {
		return
	}

	var (
		users []string

		vars []string
	)

	for _, user := range notification.GetLark().GetUsers() {
		if !varRegexp.Match([]byte(user)) {
			users = append(users, user)
			continue
		}
		vars = append(vars, user)
	}

	if len(vars) == 0 {
		return
	}

	notification.GetLark().Users = users
	notification.GetLark().NotifierTypes = append(notification.GetLark().NotifierTypes, vars...)
}

func TranslatePipelineLarkNotifications(attrs PluginAttrs) []*dslpb.Notification {
	var notifications []*dslpb.Notification
	larkN := &dslpb.LarkNotification{
		Users:         attrs.ExtraNotifiers,
		NotifierTypes: translateNotifierTypes(attrs.NotifierTypes),
		Groups: gslice.Filter(attrs.NotifyGroup, func(s string) bool {
			return s != ""
		}),
		NotificationMethod:      buildNotificationMethod(attrs.AtNotifier, false),
		GroupNotificationMethod: buildNotificationMethod(attrs.AtBuilder, attrs.UrgentNotifyTimeout),
		Cards: gslice.Map(attrs.ExtraContentList, func(elem LarkCard) *dslpb.LarkCard {
			return &dslpb.LarkCard{
				Title:   elem.ContentTitle,
				Content: elem.ContentValue,
			}
		}),
	}
	if statusList := translateNotifyStatus(attrs.NotifyStatus, pipelineNotifyStatusMap); len(statusList) > 0 {
		notification := &dslpb.Notification{
			Name: "默认流水线事件",
			Type: dslpb.NotificationType_NOTIFICATION_TYPE_LARK,
			Lark: larkN,
			When: &dslpb.NotifyWhen{Status: statusList},
		}
		if validateNotification(notification) {
			moveVarInUserToTypes(notification)
			notifications = append(notifications, notification)
		}
	}

	if attrs.NotifyTimeout > 0 {
		notification := &dslpb.Notification{
			Name: "默认流水线事件",
			Type: dslpb.NotificationType_NOTIFICATION_TYPE_LARK,
			Lark: larkN,
			When: &dslpb.NotifyWhen{Timeout: int32(attrs.NotifyTimeout)},
		}
		if validateNotification(notification) {
			moveVarInUserToTypes(notification)
			notifications = append(notifications, notification)
		}
	}

	return notifications
}
