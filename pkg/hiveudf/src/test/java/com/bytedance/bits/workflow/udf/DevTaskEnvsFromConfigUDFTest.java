package com.bytedance.bits.workflow.udf;

import org.junit.Test;

public class DevTaskEnvsFromConfigUDFTest {
    @Test
    public void testUDF() {
        String jsonString = "{\"devContext\": {}, \"variableRecord\": {\"projectAssignIds\": [20282388483586], \"systemAssignmentId\": 20282388483842}, \"templateSnapShotId\": 1}";
        String result = DevTaskEnvsFromConfigUDF.parseEnvConfig(jsonString);
        System.out.println(result);
    }
}
