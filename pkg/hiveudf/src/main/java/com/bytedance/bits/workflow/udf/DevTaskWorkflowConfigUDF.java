package com.bytedance.bits.workflow.udf;
import org.apache.hadoop.hive.ql.exec.UDF;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import java.util.Iterator;

public class DevTaskWorkflowConfigUDF  extends UDF  {
    public String evaluate(String config) {
        return parseDevTaskWorkflowConfig(config);
    }

    public static String parseDevTaskWorkflowConfig(String jsonString) {
        try {
            // 创建 JSONObject 对象
            JSONObject jsonObject = new JSONObject(jsonString);

            // 获取 devTaskWorkflowConfig 对象
            JSONObject devTaskConfig = jsonObject.getJSONObject("Workflow").getJSONObject("workflowConfig").getJSONObject("devTaskWorkflowConfig");

            // 构建输出字符串
            StringBuilder output = new StringBuilder();
            devTaskConfig.keys().forEachRemaining(key -> {
                Object value = null;
                try {
                    value = devTaskConfig.get((String) key);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                // 检查值是否为基础类型（String, Boolean, Integer, Double等）
                if (value instanceof Boolean || value instanceof Number || value instanceof String) {
                    output.append(key).append("=").append(value).append(",");
                }
            });
            // 去除最后一个逗号
            if (output.length() > 0) {
                output.setLength(output.length() - 1);
            }

            return output.toString();
        } catch (JSONException e) {
            return "Exception:" + e.getMessage(); // 返回空数组以表示错误
        }

    }
}
