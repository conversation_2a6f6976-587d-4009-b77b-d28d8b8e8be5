load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "jirasdk",
    srcs = [
        "api.go",
        "client.go",
        "options.go",
        "projects.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pkg/jirasdk",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_go_resty_resty_v2//:resty",
        "@com_github_pkg_errors//:errors",
        "@com_github_tidwall_gjson//:gjson",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//stream",
    ],
)

go_test(
    name = "jirasdk_test",
    srcs = ["projects_test.go"],
    embed = [":jirasdk"],
    tags = ["originally-unchecked"],
    deps = ["@org_byted_code_bits_hephaestus//pkg/jsons"],
)
