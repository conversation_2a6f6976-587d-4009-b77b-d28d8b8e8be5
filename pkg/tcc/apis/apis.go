package apis

import "context"

//go:generate mockgen -destination mock/apis.go -package mock code.byted.org/devinfra/hagrid/pkg/tcc/apis Api

type Api interface {
	GetConfigById(ctx context.Context, id int64, token string, options ...Option) (config ConfigResp, err error)
	GetDirListByNameSpace(ctx context.Context, nameSpace string, token string, options ...Option) (resp DirListResp, err error)
	GetConfigVersionById(ctx context.Context, id, version int64, token string, options ...Option) (resp ConfigVersionResp, err error)
	GetConfigList(ctx context.Context, searchConfigReq SearchConfigReq, token string, options ...Option) (resp SearchConfigResp, err error)
	GetAllConfigList(ctx context.Context, searchConfigReq SearchConfigReq, token string, options ...Option) (resp SearchConfigResp, err error)
	GetNameSpace(ctx context.Context, nameSpace string, token string, options ...Option) (resp NameSpaceResp, err error)
	GetDeploymentById(ctx context.Context, id int64, token string, options ...Option) (resp DeploymentResp, err error)
	OperateCustomStep(ctx context.Context, req OperateCustomStepReq, token string, options ...Option) (resp OperateCustomStepResp, err error)
	GetTagList(ctx context.Context, nsID int64, token string, options ...Option) (resp GetTagListResp, err error)

	UpdateConfig(ctx context.Context, updateReq UpdateConfigReq, token string, options ...Option) (resp UpsertConfigResp, err error) //暂时没用到
	UpdateConfigV2(ctx context.Context, updateReq UpdateConfigReqV2, token string, options ...Option) (resp UpsertConfigResp, err error)
	CreateConfig(ctx context.Context, updateReq CreateConfigReq, token string, options ...Option) (resp UpsertConfigResp, err error) //暂时没用到
	CreateConfigV2(ctx context.Context, updateReq CreateConfigReqV2, token string, options ...Option) (resp UpsertConfigResp, err error)
	UpdateConfigStatus(ctx context.Context, updateReq UpdateConfigStatusReq, token string, options ...Option) (resp UpdateConfigStatusResp, err error)
	ImportConfig(ctx context.Context, importReq ImportConfigReq, token string, options ...Option) (resp ImportConfigResp, err error)

	ViewClearValue(ctx context.Context, req ViewClearValueReq, token string, options ...Option) (resp ViewClearValueResp, err error)

	GetDeployStrategy(ctx context.Context, req GetDeployStrategyReq, token string, options ...Option) (resp GetDeployStrategyResp, err error)             //暂时没用到
	GetDeployStrategyList(ctx context.Context, req GetDeployStrategyListReq, token string, options ...Option) (resp GetDeployStrategyListResp, err error) //暂时没用到

	// ********** 内部api 不走字节云网关 **********

	// CreateEnv 用于更新环境里的tcc服务
	CreateEnv(ctx context.Context, req CreateEnvReq, token string, options ...Option) (resp CreateEnvResp, err error) //暂时没用到
	CreateDraftEnv(ctx context.Context, createReq CreateDraftReq, token string, options ...Option) (resp CreateDraftResp, err error)
	OperateDeployment(ctx context.Context, getReq OperateDeploymentReq, token string, options ...Option) (resp OperateDeploymentResp, err error)
	// 扩展环境中region
	ExtendRegion(ctx context.Context, req ExtendRegionReq, token string, options ...Option) (resp ExtendRegionResp, err error)
	DeploymentCreateCheck(ctx context.Context, deployReq DeployCreateCheckReq, token string, options ...Option) (resp DeployCreateCheckResp, code int, err error)
	DeploymentRollbackCheck(ctx context.Context, rollbackReq DeployRollbackCheckReq, token string, options ...Option) (resp DeployRollbackCheckResp, code int, err error)
}
