package apis

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
)

type TccClientWrapper struct {
	CNTccClient   Api
	BOETccClient  Api
	I18NTccClient Api
	EUTccClient   Api
	USTccClient   Api
}

var tccClientWrapperClient *TccClientWrapper

func NewTCCClientWrapper() *TccClientWrapper {
	if tccClientWrapperClient == nil {
		tccClientWrapperClient = &TccClientWrapper{
			CNTccClient:   newClient(sharedpb.TccControlPlane_TCC_CONTROL_PLANE_CN),
			BOETccClient:  newClient(sharedpb.TccControlPlane_TCC_CONTROL_PLANE_BOE),
			I18NTccClient: newClient(sharedpb.TccControlPlane_TCC_CONTROL_PLANE_I18N),
			EUTccClient:   newClient(sharedpb.TccControlPlane_TCC_CONTROL_PLANE_EU_TTP),
			USTccClient:   newClient(sharedpb.TccControlPlane_TCC_CONTROL_PLANE_US_TTP),
		}
	}
	return tccClientWrapperClient
}
func (t TccClientWrapper) Get(plane sharedpb.TccControlPlane) Api {
	switch plane {
	case sharedpb.TccControlPlane_TCC_CONTROL_PLANE_CN:
		return t.CNTccClient
	case sharedpb.TccControlPlane_TCC_CONTROL_PLANE_BOE:
		return t.BOETccClient
	case sharedpb.TccControlPlane_TCC_CONTROL_PLANE_I18N:
		return t.I18NTccClient
	case sharedpb.TccControlPlane_TCC_CONTROL_PLANE_EU_TTP:
		return t.EUTccClient
	case sharedpb.TccControlPlane_TCC_CONTROL_PLANE_US_TTP:
		return t.USTccClient
	default:
		return t.CNTccClient
	}
}
