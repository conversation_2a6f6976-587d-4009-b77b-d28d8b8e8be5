package pvariable

import (
	"reflect"
	"testing"

	tf "code.byted.org/devinfra/hagrid/app/pipelineapi/biz/testfactory"
	pb "code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"github.com/stretchr/testify/assert"
)

func TestFillVarGroupByAssign(t *testing.T) {

	type args struct {
		group       *pb.VarGroup
		assignments []*pb.VarAssignment
	}
	assigns := tf.GetAssignments()
	assigns[0].Name = "custom.bbb"
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test",
			args: args{
				group:       tf.GetGroupPbWithoutGroupId(),
				assignments: tf.GetAssignments(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FillVarGroupByAssign(tt.args.group, tt.args.assignments)
			assert.Equal(t, tt.args.group.VarDefinitions[0].GetDefaultValue().GetText(), "bb", "can not replace when kind not existed")
		})
	}
}

func TestFillVarGroupByAssign2(t *testing.T) {

	type args struct {
		group       *pb.VarGroup
		assignments []*pb.VarAssignment
	}
	assigns := tf.GetAssignments()
	assigns[0].Name = "custom.bbb"
	tests := []struct {
		name string
		args args
	}{
		{
			name: "test replace",
			args: args{
				group:       tf.GetGroupPbWithoutGroupId(),
				assignments: tf.GetAssignments2(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			FillVarGroupByAssign(tt.args.group, tt.args.assignments)
			assert.Equal(t, tt.args.group.VarDefinitions[0].GetDefaultValue().GetText(), "old", "replace fail")
		})
	}
}

func TestVarGroupToInputs(t *testing.T) {
	type args struct {
		group *pb.VarGroup
	}
	tests := []struct {
		name string
		args args
		want []*pb.VarAssignEntry
	}{
		{
			name: "test",
			args: args{
				group: tf.GetGroupPbWithoutGroupId(),
			},
			want: []*pb.VarAssignEntry{
				{
					Name: "custom.bbb",
					Value: &pb.VarValue{
						Value: &pb.VarValue_Text{
							Text: "bb",
						},
						RawJson: "bb",
					},
					GroupVersion: 2,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := VarGroupToInputs(tt.args.group); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VarGroupToInputs() = %v, want %v", got, tt.want)
			}
		})
	}
}
