package pvariable

import (
	"context"
	"reflect"
	"testing"

	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	leafboatsdk "code.byted.org/pipeline/go-sdk"
	"gotest.tools/v3/assert"
)

func Test_AssignsToVariables(t *testing.T) {
	ctx := context.Background()
	type args struct {
		assigns []*varstorepb.VarAssignment
	}
	tests := []struct {
		name string
		args args
		want []*leafboatsdk.Variable
	}{
		{
			name: "",
			args: args{
				assigns: []*varstorepb.VarAssignment{
					{
						Name: "custom.key",
						Value: &varstorepb.VarValue{
							Value: &varstorepb.VarValue_Text{Text: "test_value"},
						},
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Kind: 3,
							},
						},
					},
					{
						Name: "sys.pipeline.pipeline_name",
						Value: &varstorepb.VarValue{
							Value: &varstorepb.VarValue_Text{Text: "test_name"},
						},
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Kind: 3,
							},
						},
					},
				},
			},
			want: []*leafboatsdk.Variable{{
				Key: "custom",
				Value: map[string]interface{}{
					"key": "test_value",
				},
				Secret: false,
			}, {
				Key: "sys",
				Value: map[string]interface{}{
					"pipeline": map[string]interface{}{
						"pipeline_name": "test_name",
					},
				},
				Secret: false,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _ := AssignsToVariables(ctx, tt.args.assigns)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestFlattenValue(t *testing.T) {
	ctx := context.Background()
	type args struct {
		assign *varstorepb.VarAssignment
	}
	tests := []struct {
		name    string
		args    args
		want    interface{}
		wantErr bool
	}{
		{
			name: "test text",
			args: args{
				assign: &varstorepb.VarAssignment{
					Name: "custom.text",
					Value: &varstorepb.VarValue{
						Value: &varstorepb.VarValue_Text{Text: "text"},
					},
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Kind:               varstorepb.VarKind_VAR_KIND_STRING,
							NeedLoadWhenRender: false,
						},
					},
				},
			},
			want:    "text",
			wantErr: false,
		},
		{
			name: "test text2",
			args: args{
				assign: &varstorepb.VarAssignment{
					Name: "custom.text",
					Value: &varstorepb.VarValue{
						Value: &varstorepb.VarValue_Text{Text: "text"},
					},
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Kind:               varstorepb.VarKind_VAR_KIND_STRING,
							NeedLoadWhenRender: true,
						},
					},
				},
			},
			want:    "text",
			wantErr: false,
		}, {
			name: "test array",
			args: args{
				assign: &varstorepb.VarAssignment{
					Name: "custom.array",
					Value: &varstorepb.VarValue{
						Value: &varstorepb.VarValue_JsonArray{JsonArray: "[]"},
					},
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Kind:               varstorepb.VarKind_VAR_KIND_ARRAY,
							NeedLoadWhenRender: false,
						},
					},
				},
			},
			want:    "[]",
			wantErr: false,
		},
		{
			name: "test array2",
			args: args{
				assign: &varstorepb.VarAssignment{
					Name: "custom.array",
					Value: &varstorepb.VarValue{
						Value: &varstorepb.VarValue_JsonArray{JsonArray: "[]"},
					},
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Kind:               varstorepb.VarKind_VAR_KIND_ARRAY,
							NeedLoadWhenRender: true,
						},
					},
				},
			},
			want:    []interface{}{},
			wantErr: false,
		}, {
			name: "test array3",
			args: args{
				assign: &varstorepb.VarAssignment{
					Name: "custom.array",
					Value: &varstorepb.VarValue{
						Value: &varstorepb.VarValue_JsonArray{JsonArray: "fasfas"},
					},
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Kind:               varstorepb.VarKind_VAR_KIND_ARRAY,
							NeedLoadWhenRender: true,
						},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FlattenValueWithAssign(ctx, tt.args.assign)
			if (err != nil) != tt.wantErr {
				t.Errorf("FlattenValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FlattenValue() got = %v, want %v", got, tt.want)
			}
		})
	}
}
