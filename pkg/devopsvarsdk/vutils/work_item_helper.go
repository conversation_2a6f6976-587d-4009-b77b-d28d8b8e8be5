package vutils

import (
	"code.byted.org/devinfra/hagrid/pkg/devopsvarsdk/vconsts"
	"code.byted.org/devinfra/hagrid/pkg/devopsvarsdk/vmodel"
)

func GetWorkItemPlatformName(enum vmodel.WorkItemPlatform) string {
	switch enum {
	case vmodel.WorkItemPlatformMeego:
		return vconsts.MeegoPlatform
	case vmodel.WorkItemPlatformJira:
		return vconsts.JiraPlatform
	default:
		// unspecified platform
		return ""
	}
}
