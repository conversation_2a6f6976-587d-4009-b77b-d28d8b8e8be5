package vutils

import (
	"fmt"
	"reflect"
	"testing"

	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/lang/gg/gslice"
	jsoniter "github.com/json-iterator/go"
	"github.com/stretchr/testify/require"
)

func TestNewVarValue(t *testing.T) {
	str := "test0"
	strVal, err := NewVarValue(str)
	require.NoError(t, err)
	require.Equal(t, strVal.GetText(), str)

	boolean := true
	booleanVal, err := NewVarValue(boolean)
	require.NoError(t, err)
	require.Equal(t, booleanVal.GetBoolean(), boolean)

	i := 1
	intVal, err := NewVarValue(i)
	require.NoError(t, err)
	require.Equal(t, intVal.GetNumber(), int64(i))

	f := 1.2
	floatVal, err := NewVarValue(f)
	require.NoError(t, err)
	require.Equal(t, floatVal.GetText(), fmt.Sprintf("%f", f))

	sli := []int{1, 2, 3}
	sliceVal, err := NewVarValue(sli)
	raw, _ := jsoniter.MarshalToString(sli)
	require.NoError(t, err)
	require.Equal(t, sliceVal.GetJsonArray(), raw)

	type TestStruct struct {
		A string `json:"a"`
		B int    `json:"b"`
		C []int  `json:"c"`
	}
	s := TestStruct{
		A: "1",
		B: 2,
		C: []int{1, 2, 3},
	}
	structVal, err := NewVarValue(s)
	raw, _ = jsoniter.MarshalToString(s)
	require.NoError(t, err)
	require.Equal(t, structVal.GetJsonObject(), raw)

	m := map[string]string{
		"a": "1",
	}
	mapVal, err := NewVarValue(m)
	raw, _ = jsoniter.MarshalToString(m)
	require.NoError(t, err)
	require.Equal(t, mapVal.GetJsonObject(), raw)

}

func TestMapToAssignEntries(t *testing.T) {
	type args struct {
		vars        map[string]interface{}
		varProvider string
	}
	tests := []struct {
		name    string
		args    args
		want    []*varstorepb.VarAssignEntry
		wantErr bool
	}{
		{
			args: args{
				vars: map[string]interface{}{
					"str": "test",
					"num": 1,
					"array": []int{
						1,
						2,
						3,
					},
					"object": map[string]interface{}{"a": "bv"},
				},
				varProvider: GetVarProviderPrefix(varstorepb.SysProvider_SYS_PROVIDER_DEVELOPMENT_TASK),
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := MapToAssignEntries(tt.args.vars, tt.args.varProvider)
			if (err != nil) != tt.wantErr {
				t.Errorf("MapToAssignEntries() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			gotM := gslice.ToMap(got, func(elem *varstorepb.VarAssignEntry) (string, string) {
				return elem.GetName(), elem.String()
			})

			for k, _ := range tt.args.vars {
				v, exist := gotM[fmt.Sprintf("sys.development_task.%s", k)]
				if !exist {
					t.Errorf("MapToAssignEntries() key %s not exist", k)
				}
				t.Log("key:", k, "value:", v)
			}
		})
	}
}

func TestVariableToEntry(t *testing.T) {
	type args struct {
		v *release_ticket_sharedpb.Variable
	}
	tests := []struct {
		name string
		args args
		want *varstorepb.VarAssignEntry
	}{
		{
			args: args{&release_ticket_sharedpb.Variable{
				Definition: &varstorepb.VarDefinition{FullName: "test"},
				GroupId:    1,
				Version:    2,
			}},
			want: &varstorepb.VarAssignEntry{
				Name:         "test",
				GroupId:      1,
				GroupVersion: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := VariableToEntry(tt.args.v); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("VariableToEntry() = %v, want %v", got, tt.want)
			}
		})
	}
}
