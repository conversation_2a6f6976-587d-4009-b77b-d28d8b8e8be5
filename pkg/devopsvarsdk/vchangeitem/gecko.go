package vchangeitem

import (
	"context"
	"fmt"
	"strconv"

	commonUtil "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
)

const (
	KEY_IES_GECKOLIST = "ies_geckolist"
	KEY_TT_GECKOLIST  = "tt_geckolist"
)
const (
	KEY_GECKO_ENV_TYPE      = "env_type" // 内测配置 0 线上配置 1
	KEY_GECKO_APP_ID        = "gecko_app_id"
	KEY_GECKO_APP_NAME      = "app_name"
	KEY_ENV_LANE            = "env_lane"   // 泳道环境
	KEY_CHANNEL_ID          = "channel_id" // channel_id
	KEY_CHANNEL_NAME        = "channel_name"
	KEY_DEPLOY_ENV_ID       = "deploy_env_id" // 部署区域id
	KEY_GECKO_PACKAGE_TYPES = "gecko_package_types"
	KEY_REGION              = "region"
	KEY_SCM_PATH            = "scm_path" // scm 产物路径
)

var GeckoRegion = map[int32]string{
	1: "cn",
	2: "boe",
	3: "i18n",
	4: "boe-i18n",
	5: "tt-row",
	6: "us-ttp",
	7: "eu-ttp",
}

type Gecko struct {
	*Common
}

func NewGecko(common *Common) ChangeItemVarGenerator {
	return &Gecko{Common: common}
}

func (t *Gecko) GenerateChangeItemVar(ctx context.Context) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	for _, builder := range t.GetVariableBuilders() {
		m, err := builder(ctx)
		if err != nil {
			return nil, err
		}

		result = gmap.Merge(result, m)
	}

	return result, nil
}

func (t *Gecko) GetVariableBuilders() []ChangeItemVariableBuilder {
	return []ChangeItemVariableBuilder{
		t.Common.genEnvPlatformVars,
		t.genProjectOwners,
		t.genBaseProjectInfoVarsForProjectTicketCompatible,
		t.genBaseProjectInfoVars,
		t.genSCMVars,
		t.genGeckoOfficialDeployVars,
		t.genEnvProjectConfigVars,
	}
}
func (t *Gecko) genGeckoOfficialDeployVars(ctx context.Context) (map[string]interface{}, error) {
	variables := make(map[string]interface{})
	if !t.IsDeploy {
		return variables, nil
	}
	deployContent := t.ChangeItem.Content
	if deployContent == nil {
		return variables, nil
	}
	deployStrategy := deployContent.GetStrategy()
	if deployStrategy == nil {
		return variables, nil
	}
	geckoStrategy := deployStrategy.GetGeckoStrategy()
	if geckoStrategy == nil {
		logs.CtxInfo(ctx, "[genGeckoOfficialDeployVars] geckoStrategy is nil")
		return variables, nil
	}
	variables[KEY_IES_GECKOLIST] = gslice.FilterMap(geckoStrategy.ChannelItems, func(f *paaspb.ChannelItem) (map[string]interface{}, bool) {
		if f.Platform != paaspb.GeckoPlatformType_GECKO_PLATFORM_TYPE_IES {
			return nil, false
		}
		res := map[string]interface{}{
			KEY_GECKO_ENV_TYPE: f.GetGeckoEnvType().Number(),
			KEY_GECKO_APP_ID:   f.GetGeckoAppId(),
			KEY_GECKO_APP_NAME: f.GetGeckoAppName(),
			KEY_CHANNEL_ID:     strconv.FormatUint(f.GetGeckoChannelId(), 10),
			KEY_CHANNEL_NAME:   f.GetGeckoChannelName(),
			KEY_DEPLOY_ENV_ID:  strconv.FormatUint(f.GetGeckoDeploymentId(), 10),
			KEY_GECKO_PACKAGE_TYPES: gslice.FilterMap(f.GetResourceTypes(), func(f paaspb.GeckoResourceType) (string, bool) {
				switch f {
				case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_ONLINE:
					return "1", true
				case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_OFFLINE:
					return "0", true
				default:
					return "-1", false
				}
			}),
			KEY_REGION: GeckoRegion[int32(f.GetRegion().Number())],
		}
		if f.GetScmArtifactPath() != "" {
			res[KEY_SCM_PATH] = f.GetScmArtifactPath()
		}
		return res, true
	})
	variables[KEY_TT_GECKOLIST] = gslice.FilterMap(geckoStrategy.ChannelItems, func(f *paaspb.ChannelItem) (map[string]interface{}, bool) {
		if f.Platform != paaspb.GeckoPlatformType_GECKO_PLATFORM_TYPE_TT {
			return nil, false
		}
		res := map[string]interface{}{
			KEY_GECKO_ENV_TYPE: f.GetGeckoEnvType().Number(),
			KEY_GECKO_APP_ID:   f.GetGeckoAppId(),
			KEY_GECKO_APP_NAME: f.GetGeckoAppName(),
			KEY_CHANNEL_ID:     strconv.FormatUint(f.GetGeckoChannelId(), 10),
			KEY_GECKO_PACKAGE_TYPES: gslice.FilterMap(f.GetResourceTypes(), func(f paaspb.GeckoResourceType) (string, bool) {
				switch f {
				case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_ONLINE:
					return "1", true
				case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_OFFLINE:
					return "0", true
				default:
					return "-1", false
				}
			}),
			KEY_REGION: GeckoRegion[int32(f.GetRegion().Number())],
		}
		if f.GetScmArtifactPath() != "" {
			res[KEY_SCM_PATH] = f.GetScmArtifactPath()
		}
		return res, true
	})
	return variables, nil
}

func (t *Gecko) genEnvProjectConfigVars(ctx context.Context) (map[string]interface{}, error) {
	variables := make(map[string]interface{})
	if t.IsDeploy {
		return variables, nil
	}
	envProjectConfigs := t.ChangeItem.EnvProjectConfigs
	if len(envProjectConfigs) == 0 {
		return variables, nil
	}
	iesList := make([]map[string]interface{}, 0)
	ttList := make([]map[string]interface{}, 0)
	for _, projectConfig := range envProjectConfigs {
		if projectConfig.GetProjectType() != sharedpb.ProjectType_PROJECT_TYPE_HYBRID || projectConfig.GetGeckoProjectConfig() == nil {
			logs.CtxInfo(ctx, "it is not valid gecko project configuration, skip generating the variable for it: %s", commonUtil.ToJson(projectConfig))
			continue
		}
		channelCfgs := projectConfig.GetGeckoProjectConfig().GetChannelItems()
		ies := gslice.FilterMap(channelCfgs, func(f *paaspb.ChannelItem) (map[string]interface{}, bool) {
			if f.Platform != paaspb.GeckoPlatformType_GECKO_PLATFORM_TYPE_IES {
				return nil, false
			}
			fullLaneID := ""
			if f.GetLaneId() != "" {
				fullLaneID = choose.If(f.GetOverwritePrefix() != "", f.GetOverwritePrefix()+f.GetLaneId(), f.GetLaneType()+f.GetLaneId())
			}
			res := map[string]interface{}{
				KEY_GECKO_ENV_TYPE: f.GetGeckoEnvType().Number(),
				KEY_GECKO_APP_ID:   f.GetGeckoAppId(),
				KEY_GECKO_APP_NAME: f.GetGeckoAppName(),
				KEY_ENV_LANE:       fmt.Sprintf("{\"envLane\":\"%s\"}", fullLaneID),
				KEY_CHANNEL_ID:     strconv.FormatUint(f.GetGeckoChannelId(), 10),
				KEY_CHANNEL_NAME:   f.GetGeckoChannelName(),
				KEY_DEPLOY_ENV_ID:  strconv.FormatUint(f.GetGeckoDeploymentId(), 10),
				KEY_GECKO_PACKAGE_TYPES: gslice.FilterMap(f.GetResourceTypes(), func(f paaspb.GeckoResourceType) (string, bool) {
					switch f {
					case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_ONLINE:
						return "1", true
					case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_OFFLINE:
						return "0", true
					default:
						return "-1", false
					}
				}),
				KEY_REGION: GeckoRegion[int32(f.GetRegion().Number())],
			}
			if f.GetScmArtifactPath() != "" {
				res[KEY_SCM_PATH] = f.GetScmArtifactPath()
			}
			return res, true
		})
		iesList = append(iesList, ies...)
		tt := gslice.FilterMap(channelCfgs, func(f *paaspb.ChannelItem) (map[string]interface{}, bool) {
			if f.Platform != paaspb.GeckoPlatformType_GECKO_PLATFORM_TYPE_TT {
				return nil, false
			}
			fullLaneID := ""
			if f.GetLaneId() != "" {
				fullLaneID = choose.If(f.GetOverwritePrefix() != "", f.GetOverwritePrefix()+f.GetLaneId(), f.GetLaneType()+f.GetLaneId())
			}
			res := map[string]interface{}{
				KEY_GECKO_ENV_TYPE: f.GetGeckoEnvType().Number(),
				KEY_GECKO_APP_ID:   f.GetGeckoAppId(),
				KEY_GECKO_APP_NAME: f.GetGeckoAppName(),
				KEY_ENV_LANE:       fmt.Sprintf("{\"envLane\":\"%s\"}", fullLaneID),
				KEY_CHANNEL_ID:     strconv.FormatUint(f.GetGeckoChannelId(), 10),
				KEY_GECKO_PACKAGE_TYPES: gslice.FilterMap(f.GetResourceTypes(), func(f paaspb.GeckoResourceType) (string, bool) {
					switch f {
					case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_ONLINE:
						return "1", true
					case paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_OFFLINE:
						return "0", true
					default:
						return "-1", false
					}
				}),
				KEY_REGION: GeckoRegion[int32(f.GetRegion().Number())],
			}
			if f.GetScmArtifactPath() != "" {
				res[KEY_SCM_PATH] = f.GetScmArtifactPath()
			}
			return res, true
		})
		ttList = append(ttList, tt...)
	}
	variables[KEY_IES_GECKOLIST] = iesList
	variables[KEY_TT_GECKOLIST] = ttList
	return variables, nil
}
