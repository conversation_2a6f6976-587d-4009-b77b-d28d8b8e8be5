package fatalerr

import (
	"fmt"

	"code.byted.org/gopkg/logs"
)

// Fatalf print fatal log and then panic
func Fatalf(format string, v ...interface{}) {
	msg := fmt.Sprintf(format, v...)

	logs.Fatalf(msg)
	logs.Flush()

	panic(msg)
}

func PanicOnError(err error, msgAndArgs ...interface{}) {
	if err == nil {
		return
	}

	msg := fmt.Sprintf("unexpected error:\n%+v\nmesssage:%s", err, messageFromMsgAndArgs(msgAndArgs))

	logs.Fatalf(msg)
	logs.Flush()

	panic(msg)
}

func messageFromMsgAndArgs(msgAndArgs ...interface{}) string {
	if len(msgAndArgs) == 0 || msgAndArgs == nil {
		return ""
	}
	if len(msgAndArgs) == 1 {
		msg := msgAndArgs[0]
		if msgAsStr, ok := msg.(string); ok {
			return msgAsStr
		}
		return fmt.Sprintf("%+v", msg)
	}
	if len(msgAndArgs) > 1 {
		return fmt.Sprintf(msgAndArgs[0].(string), msgAndArgs[1:]...)
	}
	return ""
}
