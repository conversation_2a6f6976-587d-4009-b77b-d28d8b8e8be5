// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/pkg/gecko (interfaces: GeckoIESSDK)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gecko "code.byted.org/devinfra/hagrid/pkg/gecko"
	gomock "github.com/golang/mock/gomock"
)

// MockGeckoIESSDK is a mock of GeckoIESSDK interface.
type MockGeckoIESSDK struct {
	ctrl     *gomock.Controller
	recorder *MockGeckoIESSDKMockRecorder
}

// MockGeckoIESSDKMockRecorder is the mock recorder for MockGeckoIESSDK.
type MockGeckoIESSDKMockRecorder struct {
	mock *MockGeckoIESSDK
}

// NewMockGeckoIESSDK creates a new mock instance.
func NewMockGeckoIESSDK(ctrl *gomock.Controller) *MockGeckoIESSDK {
	mock := &MockGeckoIESSDK{ctrl: ctrl}
	mock.recorder = &MockGeckoIESSDKMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGeckoIESSDK) EXPECT() *MockGeckoIESSDKMockRecorder {
	return m.recorder
}

// GetTicketInfo mocks base method.
func (m *MockGeckoIESSDK) GetTicketInfo(arg0 context.Context, arg1, arg2 string) (gecko.WorkflowStatusEnum, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(gecko.WorkflowStatusEnum)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketInfo indicates an expected call of GetTicketInfo.
func (mr *MockGeckoIESSDKMockRecorder) GetTicketInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketInfo", reflect.TypeOf((*MockGeckoIESSDK)(nil).GetTicketInfo), arg0, arg1, arg2)
}
