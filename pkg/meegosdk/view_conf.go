package meegosdk

import (
	"context"

	"github.com/pkg/errors"
)

type ListViewConfsRequest struct {
	WorkItemTypeKey string        `json:"work_item_type_key,omitempty"` // 必填 工作项类型
	ViewIds         []string      `json:"view_ids,omitempty"`           // 选填 视图id列表，查询视图id对应的视图配置，若指定视图id，其余筛选条件不会起作用
	CreatedBy       string        `json:"created_by,omitempty"`         // 选填 可根据该字段筛选，筛选出指定用户创建的视图
	CreatedAt       *TimeInterval `json:"created_at,omitempty"`         // 选填 可以根据该字段筛选，筛选出创建时间在该时间区间的视图配置信息
	PageSize        int           `json:"page_size,omitempty"`          // 选填 分页大小，最大页大小是10，默认给10
	PageNum         int           `json:"page_num,omitempty"`           // 选填 页码，默认给1
}

func (client *client) ListViewConfs(ctx context.Context, userKey string, projectKey string, request *ListViewConfsRequest) ([]*ViewConf, error) {
	u := baseurl(ctx) + "/open_api/{project_key}/view_conf/list"

	response := new(RawResponse[[]*ViewConf])
	resp, err := client.httpclient.
		R().
		SetContext(ctx).
		SetHeader("X-USER-KEY", userKey).
		SetPathParam("project_key", projectKey).
		SetBody(request).
		SetResult(response).
		SetToMethod("ListViewConfs").
		Post(u)
	if err != nil {
		return nil, errors.WithMessage(err, "failed to send request to meego")
	}

	values, err := response.Unwrap(resp.StatusCode())
	if err != nil {
		return nil, err
	}
	return values, nil
}

func (client *client) ListViewConfsByEmail(ctx context.Context, email string, projectKey string, request *ListViewConfsRequest) ([]*ViewConf, error) {
	user, err := client.GetUserByEmail(ctx, email)
	if err != nil {
		return nil, err
	}

	return client.ListViewConfs(ctx, user.UserKey, projectKey, request)
}
