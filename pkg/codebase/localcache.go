package codebase

import (
	"sync"
	"time"

	"github.com/bluele/gcache"
	"github.com/pkg/errors"
)

type LocalCache struct {
	c    gcache.Cache
	lock sync.RWMutex
}

// NewLocalCache returns a new LocalCache. `size` is the size limit for the total of cache items.
func NewLocalCache(size int) *LocalCache {
	return &LocalCache{c: gcache.New(size).LRU().Build()}
}

// Has returns whether the key exists in the cache.
func (lc *LocalCache) Has(key string) bool {
	return lc.c.Has(key)
}

// Get gets a value by key. It returns gcache.KeyNotFoundError if the key doesn't exist.
func (lc *LocalCache) Get(key string) (interface{}, error) {
	return lc.c.Get(key)
}

func GetCacheValue[T any](lc *LocalCache, key string) (T, error) {
	var value T
	v, err := lc.c.Get(key)
	if err != nil {
		return value, err
	}
	value, ok := v.(T)
	if !ok {
		return value, errors.Errorf("value is %T, not %T", v, value)
	}
	return value, nil
}

// Set sets a key-value pair.
func (lc *LocalCache) Set(key string, value interface{}) error {
	lc.lock.Lock()
	defer lc.lock.Unlock()
	return lc.c.Set(key, value)
}

// SetWithExpire sets a key-value pair with expiration.
func (lc *LocalCache) SetWithExpire(key string, value interface{}, expiresIn time.Duration) error {
	lc.lock.Lock()
	defer lc.lock.Unlock()
	return lc.c.SetWithExpire(key, value, expiresIn)
}

// Del deletes a key-value pair.
func (lc *LocalCache) Del(key string) bool {
	lc.lock.Lock()
	defer lc.lock.Unlock()
	return lc.c.Remove(key)
}

// Lock tries to acquire a lock. It returns true on success, otherwise false.
func (lc *LocalCache) Lock(key, value string, expiresIn time.Duration) (bool, error) {
	lc.lock.Lock()
	defer lc.lock.Unlock()
	if lc.c.Has(key) {
		return false, nil
	}
	if err := lc.c.SetWithExpire(key, value, expiresIn); err != nil {
		return false, errors.WithMessage(err, "failed to set with expire")
	}
	return true, nil
}

// Unlock frees a lock.
func (lc *LocalCache) Unlock(key, value string) (bool, error) {
	lc.lock.Lock()
	defer lc.lock.Unlock()
	v, err := lc.c.Get(key)
	if err != nil {
		return false, errors.WithMessage(err, "failed to get item by key")
	}
	if vs, ok := v.(string); !ok || vs != value {
		return false, nil
	}
	return lc.c.Remove(key), nil
}

// ExpireLockIn sets the expiry time to the given key. It only succeeds if the value matches.
func (lc *LocalCache) ExpireLockIn(key, value string, expiresIn time.Duration) (bool, error) {
	lc.lock.Lock()
	defer lc.lock.Unlock()
	v, err := lc.c.Get(key)
	if err != nil {
		return false, errors.WithMessage(err, "failed to get item by key")
	}
	if vs, ok := v.(string); !ok || vs != value {
		return false, nil
	}
	if err := lc.c.SetWithExpire(key, value, expiresIn); err != nil {
		return false, errors.WithMessage(err, "failed to set with expire")
	}
	return true, nil
}
