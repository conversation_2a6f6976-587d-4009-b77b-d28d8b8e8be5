package leafboat

import (
	"context"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	json "github.com/bytedance/sonic"
	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/metainfo"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/pipeline/expression/v2"
	enginesdk "code.byted.org/pipeline/go-sdk"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/model"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/winpath"
	"code.byted.org/devinfra/hagrid/pkg/pvariable"
)

type Manual int8

const (
	defaultWorkSpace                = "/home/<USER>"
	defaultWindowsWorkSpace         = `C:\code`
	workspaceVolumeName             = "workspace"
	defaultContainerName            = "default"
	defaultTimeout                  = 7200              // 2h 7200s
	maxSelfHostScriptTimeoutSeconds = 60 * 60 * 24 * 15 // 15 days
	maxScriptTimeout                = 60 * 60 * 6       // 6h

	cnDockerDomain  = "hub.byted.org/"
	useDockerHub    = "use-hub.byted.org/"
	aliVADockerHub  = "aliyun-va-hub.byted.org/"
	runOnEnvBOEi18n = "boei18n"

	ManualNotRequired Manual = iota // doesn't need operation
	ManualOperated                  // need operation and has been operated
	ManualRequired                  // need operation
)

func CompileEnginePipeline(ctx context.Context, pipeline *entity.Pipeline, username string,
	pipelineRun *entity.PipelineRun, space *rpcpb.SpaceDetail, compileExtraField *model.CompileExtraField, varAssigns []*varstorepb.VarAssignment) (*enginesdk.PipelineDSL, error) {
	actor := username
	// HARD-CODE: Set an admin identity to grant full-clone permission.
	//   1. "system" is usually a bot user and has no permission to clone.
	//   2. the testing of repo inf/typhoon-blade sucks and requires full-clone permission.
	if actor == "system" {
		actor = "admin"
	}
	enginePipeline := &enginesdk.PipelineDSL{
		Name:  pipeline.Name,
		Actor: actor,
	}
	stages, err := CompileEngineStage(pipeline.Stages, space, pipelineRun, compileExtraField)
	if err != nil {
		return nil, err
	}
	enginePipeline.Stages = stages
	if space == nil {
		logs.CtxInfo(ctx, "workspace info nil,use mock work_space data")
		space = &rpcpb.SpaceDetail{
			Name:       "mock_workspace_name",
			BytetreeId: 924103,
		}
	}
	// todo 服务型原子需要填入 历史遗留的环境编译数据，可加上判断
	// Interpreter 这里是写死的python，实际上有两种 Jet & Python
	// Python 为{{...}}, JET 为 ${{...}}
	enginePipeline.Interpreter = "python"
	enginePipeline.ContextBuilder = enginesdk.ByteCycleContextBuilder

	variables, err := CompileVariables(ctx, pipeline, pipelineRun, space, varAssigns, username)
	if err != nil {
		return nil, err
	}
	enginePipeline.Variables = variables

	return enginePipeline, nil
}

func CompileEngineStage(stages []*dslpb.Stage, space *rpcpb.SpaceDetail, pipelineRun *entity.PipelineRun, compileExtraField *model.CompileExtraField) ([]*enginesdk.StageDSL, error) {
	stageDsls := make([]*enginesdk.StageDSL, 0)
	for _, stage := range stages {
		stageDSL := &enginesdk.StageDSL{
			UID:  stage.Id,
			Name: utils.GetDefaultString(stage.Name),
			If:   stage.If,
		}
		Jobs, err := CompileEngineJob(stage.Jobs, space, pipelineRun, compileExtraField)
		if err != nil {
			return nil, err
		}
		stageDSL.Jobs = Jobs
		stageDsls = append(stageDsls, stageDSL)
	}
	return stageDsls, nil
}

func CompileEngineJob(jobs []*dslpb.Job, space *rpcpb.SpaceDetail, pipelineRun *entity.PipelineRun, compileExtraField *model.CompileExtraField) ([]*enginesdk.JobDSL, error) {
	jobDsls := make([]*enginesdk.JobDSL, 0)
	var err error
	for _, job := range jobs {
		var jobDsl *enginesdk.JobDSL
		if isServerJob(job) {
			if jobDsl, err = compileServerJobDsl(job); err != nil {
				return nil, err
			}

			if jobDsl.Atom != nil {
				jobDsl.Atom.InputArtifacts, err = compileJobArtifactInputs(job)
				if err != nil {
					return nil, errors.WithMessagef(err, "failed to compile job %s artifact inputs", job.Id)
				}
			}

		} else if isScriptJob(job) {
			if jobDsl, err = compileScriptJobDsl(job, space, pipelineRun, compileExtraField); err != nil {
				return nil, err
			}
		}

		compileEngineJobOnFailedAction(job, jobDsl)
		compileEngineJobOnTimeoutAction(job, jobDsl)
		compileEngineJobOnIgnoredAction(job, jobDsl)

		// 拼接 jobs
		jobDsls = append(jobDsls, jobDsl)
	}
	return jobDsls, nil
}

func compileEngineJobOnFailedAction(job *dslpb.Job, jobDsl *enginesdk.JobDSL) {
	switch job.OnFailed {
	case dslpb.OnFailed_ON_FAILED_IGNORE:
		jobDsl.ErrorPolicy = enginesdk.IgnoreError
	case dslpb.OnFailed_ON_FAILED_CONTINUE:
		jobDsl.ErrorPolicy = enginesdk.ContinueOnError
	case dslpb.OnFailed_ON_FAILED_RETRY:
		if job.Retry != nil {
			jobDsl.Retry = int(job.Retry.Max)
			jobDsl.RetryIntervalInSec = int(job.Retry.Interval)
		}
	case dslpb.OnFailed_ON_FAILED_UNSPECIFIED:
		return
	}
}

func compileEngineJobOnTimeoutAction(job *dslpb.Job, jobDsl *enginesdk.JobDSL) {
	switch job.OnTimeout {
	case dslpb.OnTimeout_ON_TIMEOUT_FAIL:
		jobDsl.TimeoutPolicy = enginesdk.FailOnTimeout
	case dslpb.OnTimeout_ON_TIMEOUT_SKIP:
		jobDsl.TimeoutPolicy = enginesdk.SkipOnTimeout
	case dslpb.OnTimeout_ON_TIMEOUT_CANCEL:
		jobDsl.TimeoutPolicy = enginesdk.CancelOnTimeout
	case dslpb.OnTimeout_ON_TIMEOUT_UNSPECIFIED:
		return
	}
	jobDsl.TimeoutInSec = int(job.Timeout)
}

func compileEngineJobOnIgnoredAction(job *dslpb.Job, jobDsl *enginesdk.JobDSL) {
	if len(job.If) == 0 {
		return
	}
	switch job.OnIgnored {
	case dslpb.OnIgnored_ON_IGNORED_ABORT:
		jobDsl.IgnorePolicy = enginesdk.AbortOnIgnored
	case dslpb.OnIgnored_ON_IGNORED_CONTINUE:
		jobDsl.IgnorePolicy = enginesdk.ContinueOnIgnored
	case dslpb.OnIgnored_ON_IGNORED_UNSPECIFIED:
		return
	}
}

func isServerJob(job *dslpb.Job) bool {
	return len(job.Uses) > 0 && len(job.Steps) == 0
}

func isScriptJob(job *dslpb.Job) bool {
	return len(job.Uses) == 0 || len(job.Steps) > 0
}

func isCloudBuild(space *rpcpb.SpaceDetail, compileExtraField *model.CompileExtraField) bool {
	if space == nil {
		return false
	} else {
		if space.Type == constvar.SpaceTypeCLIENT {
			return true
		}
		if compileExtraField != nil && compileExtraField.CloudBuildCompileExtraField != nil && compileExtraField.CloudBuildConfigMap != nil {
			if slices.Contains(compileExtraField.CloudBuildConfigMap.GraySpaces, space.Id) {
				return true
			}
		}
	}
	return false
}

func compileServerJobDsl(job *dslpb.Job) (*enginesdk.JobDSL, error) {
	jobDsl := &enginesdk.JobDSL{
		UID:          job.Id,
		Name:         utils.GetDefaultString(job.Name),
		If:           getJobIfCondition(job),
		IfSkip:       job.IfSkip,
		TimeoutInSec: int(job.Timeout),
		Blocked:      job.Manual,
		Retry:        0,
		DependsOn:    job.DependsOn,
	}
	if jobAtom, err := getJobAtomRef(job); err != nil {
		return nil, err
	} else {
		jobDsl.Atom = jobAtom
	}
	return jobDsl, nil
}

func getJobAtomRef(job *dslpb.Job) (*enginesdk.JobAtomRef, error) {
	if len(job.Uses) == 0 {
		return nil, nil
	}
	atomUniqueId, atomVersion := utils.GetUsesUniqueIDVersion(job.Uses)
	if len(atomUniqueId) == 0 {
		return nil, nil
	}
	input, err := compileJobInput(job)
	if err != nil {
		return nil, err
	}
	stepInputs, err := compileJobStepsInput(job)
	if err != nil {
		return nil, err
	}
	return &enginesdk.JobAtomRef{
		UniqID:     atomUniqueId,
		Version:    atomVersion,
		Inputs:     input,
		StepInputs: stepInputs,
		RunEnv:     job.RunEnv,
	}, nil
}

func compileJobArtifactInputs(job *dslpb.Job) ([]enginesdk.InputArtifactFilter, error) {
	filterList := make([]enginesdk.InputArtifactFilter, 0)
	for _, filter := range job.InputArtifacts {
		filterList = append(filterList, enginesdk.InputArtifactFilter{
			FromJobID: filter.FromJobId,
			Provider:  entity.ConvertExternalArtifactProviderFromPb(filter.Provider),
		})
	}
	return filterList, nil
}

func compileJobInput(job *dslpb.Job) (enginesdk.Inputs, error) {
	jobInput, err := utils.ConvertPBStructToMap(job.Inputs)
	if err != nil {
		return nil, err
	}
	return jobInput, nil
}

func compileJobStepsInput(job *dslpb.Job) (map[string]enginesdk.Inputs, error) {
	stepInputs := map[string]enginesdk.Inputs{}
	for _, step := range job.Steps {
		stepInput, err := utils.ConvertPBStructToMap(step.Inputs)
		if err != nil {
			return nil, err
		}
		stepInputs[step.Id] = stepInput
	}
	return stepInputs, nil
}

func compileEngineStepDsl(job *dslpb.Job) []*enginesdk.StepDSL {
	// TODO 记录下；当前ScriptAtom的Job不需要compile step里数据，后续看是否需要解析step里数据
	if len(job.Uses) > 0 {
		return nil
	}
	// Compile service steps.
	serviceSteps := make([]*enginesdk.StepDSL, 0, len(job.Services))
	for _, service := range job.Services {
		serviceSteps = append(serviceSteps, compileServiceStep(service))
	}

	stepDsls := make([]*enginesdk.StepDSL, 0, len(job.Steps))
	for _, step := range job.Steps {
		var stepDSL *enginesdk.StepDSL
		if step.Uses == "" {
			stepDSL = compileCommandsStep(step)
		} else if !isAction(step.Uses) {
			stepDSL = compileUseDockerStep(step)
		} else {
			stepDSL = compileActionStep(step)
		}
		stepDsls = append(stepDsls, stepDSL)
	}
	// Compile dependencies.
	for last, i := -1, 0; i < len(job.Steps); {
		j := i
		for job.Steps[j].ParallelWithNextStep && j+1 < len(job.Steps) {
			j++
		}
		// The last group is [last, i).
		// The current group is [i, j].
		// Set deps with the last group if the current group has two or more steps.
		if i != j {
			deps := make([]string, 0, i-last)
			if last == -1 {
				deps = []string{"-"}
			} else {
				for k := last; k < i; k++ {
					deps = append(deps, job.Steps[k].Id)
				}
			}
			for k := i; k <= j; k++ {
				stepDsls[k].DependsOn = deps
			}
		}
		last, i = i, j+1
	}
	if len(serviceSteps) > 0 && len(stepDsls) > 0 {
		stepDsls[0].DependsOn = []string{"-"}
	}

	return append(serviceSteps, stepDsls...)
}

func compileDefaultVolumeMount() []enginesdk.VolumeMount {
	return []enginesdk.VolumeMount{
		// For sharing cache.
		{Name: "tmp", MountPath: "/tmp"},
		// For toutiao.
		{Name: "sslib", MountPath: "/toutiao/ss_lib/", ReadOnly: true},
		{Name: "ssconf", MountPath: "/toutiao/ss_conf/", ReadOnly: true},
		{Name: "chadc", MountPath: "/toutiao/chadc/", ReadOnly: true},
		{Name: "pyutil", MountPath: "/toutiao/pyutil/", ReadOnly: true},
		{Name: "ssthriftgen", MountPath: "/toutiao/ss_thrift_gen/", ReadOnly: true},
		{Name: "consul_agent", MountPath: "/opt/tmp/consul_agent/", ReadOnly: true},
		{Name: "databus_data", MountPath: "/opt/tmp/databus_data/", ReadOnly: true},
		{Name: "ttlogagent", MountPath: "/opt/tmp/ttlogagent/", ReadOnly: true},
		// Only mount consul socket to enable consul, and exclude metrics.
		{Name: "consul_sock", MountPath: "/opt/tmp/sock/consul.sock", ReadOnly: true},
		// Mount on non-standard directory, user soft link to /opt/tmp if he needs.
		{Name: "opt_tmp", MountPath: "/opt/tmp_codebase_ci", ReadOnly: true},
	}
}
func compileWorkspaceVolumeMount(job *dslpb.Job) enginesdk.VolumeMount {
	return enginesdk.VolumeMount{
		Name:      workspaceVolumeName,
		MountPath: getWorkspace(job.RunsOn.WorkingDirectory, constvar.PlatformLinux),
		ReadOnly:  false,
	}
}
func getWorkspace(workDir string, platform string) string {
	if filepath.IsAbs(workDir) {
		return workDir
	}
	if winpath.IsAbs(workDir) {
		return workDir
	}
	if platform == constvar.PlatformWindows {
		return defaultWindowsWorkSpace
	}
	return defaultWorkSpace
}
func compileDefaultVolumes() []enginesdk.Volume {
	return []enginesdk.Volume{
		// For sharing cache.
		{Name: "tmp"},
		// For toutiao.
		{Name: "sslib", HostPath: "/opt/tiger/ss_lib/"},
		{Name: "ssconf", HostPath: "/opt/tiger/ss_conf/"},
		{Name: "chadc", HostPath: "/opt/tiger/chadc/"},
		{Name: "pyutil", HostPath: "/opt/tiger/pyutil/"},
		{Name: "ssthriftgen", HostPath: "/opt/tiger/ss_thrift_gen/"},
		{Name: "consul_agent", HostPath: "/opt/tmp/consul_agent/"},
		{Name: "databus_data", HostPath: "/opt/tmp/databus_data/"},
		{Name: "ttlogagent", HostPath: "/opt/tmp/ttlogagent/"},
		{Name: "consul_sock", HostPath: "/opt/tmp/sock/consul.sock"},
		{Name: "opt_tmp", HostPath: "/opt/tmp"},
	}
}
func compileWorkspaceVolume() enginesdk.Volume {
	return enginesdk.Volume{
		Name: workspaceVolumeName,
	}
}

func compileDockerImage(job *dslpb.Job, rawImage string) string {
	if job.RunsOn == nil || job.RunsOn.Bytenv != runOnEnvBOEi18n {
		return rawImage
	}
	// Replace hub.byted.org to aliyun-va-hub.byted.org for the job runs in boei18n.
	switch {
	case strings.HasPrefix(rawImage, cnDockerDomain):
		return aliVADockerHub + rawImage[len(cnDockerDomain):]
	case strings.HasPrefix(rawImage, useDockerHub):
		return aliVADockerHub + rawImage[len(useDockerHub):]
	default:
		return rawImage
	}
}

func getStepContainerName(step *dslpb.Step) string {
	return step.Id
}

func compileDefaultHostMount() []enginesdk.HostMount {
	return []enginesdk.HostMount{
		{HostPath: "/opt/tiger/ss_lib", MountPath: "/toutiao/ss_lib", ReadOnly: true},
		{HostPath: "/opt/tiger/ss_conf", MountPath: "/toutiao/ss_conf", ReadOnly: true},
		{HostPath: "/opt/tiger/chadc", MountPath: "/toutiao/chadc", ReadOnly: true},
		{HostPath: "/opt/tiger/pyutil", MountPath: "/toutiao/pyutil", ReadOnly: true},
		{HostPath: "/opt/tiger/ss_thrift_gen", MountPath: "/toutiao/ss_thrift_gen", ReadOnly: true},
		{HostPath: "/opt/tmp", MountPath: "/opt/tmp", ReadOnly: true},
	}
}

func compileUserHostMounts(volumeMounts []*dslpb.VolumeMount) []enginesdk.HostMount {
	if len(volumeMounts) == 0 {
		return nil
	}
	var hostMounts []enginesdk.HostMount
	for _, vm := range volumeMounts {
		hostMounts = append(hostMounts, enginesdk.HostMount{
			HostPath:  vm.HostPath,
			MountPath: vm.Name,
			ReadOnly:  false,
		})
	}
	return hostMounts
}

func compileServiceStep(service *dslpb.ContainerService) *enginesdk.StepDSL {
	s := &enginesdk.StepDSL{
		UID:                    service.Id,
		Name:                   service.Name,
		Disabled:               false,
		ErrorPolicy:            enginesdk.IgnoreError,
		Container:              service.Id,
		Detached:               true,
		UseContainerEntrypoint: true,
		UseContainerWorkingDir: true,
		DependsOn:              []string{"-"},
		Envs:                   service.Env,
	}
	if len(service.Commands) > 0 {
		// The service use custom entrypoint
		s.Commands = service.Commands
		s.UseContainerEntrypoint = false
	}
	// TODO:对齐
	// if len(service.Args) > 0 {
	//	// UseContainerEntrypoint=true, commands is the arguments to container's entrypoint
	//	s.Commands = service.Args
	// }
	return s
}

func compileCommandsStep(step *dslpb.Step) *enginesdk.StepDSL {
	lbStep := &enginesdk.StepDSL{
		UID:         step.Id,
		Name:        step.Name,
		Disabled:    false,
		ErrorPolicy: compileErrorPolicy(step),
		If:          step.If,
		Commands:    step.Commands,
		Envs: map[string]string{
			"CI_STEP_ID":   step.Id,
			"CI_STEP_NAME": step.Name,
		},
	}
	return lbStep
}

func compileErrorPolicy(step *dslpb.Step) enginesdk.ErrorPolicy {
	if step.OnFailed == dslpb.OnFailed_ON_FAILED_IGNORE {
		return enginesdk.IgnoreError
	}
	if step.OnFailed == dslpb.OnFailed_ON_FAILED_CONTINUE {
		return enginesdk.ContinueOnError
	}
	return enginesdk.AbortOnError
}

func compileActionStep(step *dslpb.Step) *enginesdk.StepDSL {
	lbStep := &enginesdk.StepDSL{
		UID:         step.Id,
		Name:        step.Name,
		ErrorPolicy: compileErrorPolicy(step),
		If:          step.If,
		Envs: map[string]string{
			"CI_STEP_ID":   step.Id,
			"CI_STEP_NAME": step.Name,
		},
	}
	atomUniqueId, atomVersion := utils.GetUsesUniqueIDVersion(step.Uses)

	inputs, _ := utils.ConvertPBStructToMap(step.Inputs)
	lbStep.Action = &enginesdk.ActionRef{
		UID: atomUniqueId,
		// RepoName: atomUniqueId,
		Inputs:  inputs,
		Version: atomVersion,
	}
	lbStep.Commands = nil // Clear user-defined commands.
	return lbStep
}

func compileUseDockerStep(step *dslpb.Step) *enginesdk.StepDSL {
	lbStep := &enginesdk.StepDSL{
		UID:         step.Id,
		Name:        step.Name,
		Disabled:    false,
		ErrorPolicy: compileErrorPolicy(step),
		Container:   getStepContainerName(step),
		If:          step.If,
		Commands:    step.Commands,
		Envs: map[string]string{
			"CI_STEP_ID":   step.Id,
			"CI_STEP_NAME": step.Name,
		},
	}
	return lbStep
}

func determinePlatform(job *dslpb.Job) (platform string, isContainer bool) {
	if job.RunsOn == nil {
		// Fallback to Linux container.
		return constvar.PlatformLinux, true
	}
	// TODO: 对齐
	switch job.RunsOn.Engine {
	case dslpb.RunnerEngine_RUNNER_ENGINE_DOCKER:
		return constvar.PlatformLinux, true
	case dslpb.RunnerEngine_RUNNER_ENGINE_LINUX:
		return constvar.PlatformLinux, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_LINUX_VM:
		return constvar.PlatformLinux, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_MAC_VM:
		return constvar.PlatformMacos, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_WINDOWS_VM:
		return constvar.PlatformWindows, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_WIN_CONTAINER:
		return constvar.PlatformWindows, true
	case dslpb.RunnerEngine_RUNNER_ENGINE_LINUX_CONTAINER:
		return constvar.PlatformLinux, true
	case dslpb.RunnerEngine_RUNNER_ENGINE_LINUX_RAW:
		return constvar.PlatformLinux, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_LINUX_STARTO_VM:
		return constvar.PlatformLinux, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_MACOS_CONTAINER:
		return constvar.PlatformMacos, true
	case dslpb.RunnerEngine_RUNNER_ENGINE_MACOS_RAW:
		return constvar.PlatformMacos, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_MACOS_STARTO_VM:
		return constvar.PlatformMacos, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_MAC:
		return constvar.PlatformMacos, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_BIG_MAC:
		return constvar.PlatformMacos, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_WINDOWS_CONTAINER:
		return constvar.PlatformWindows, true
	case dslpb.RunnerEngine_RUNNER_ENGINE_WINDOWS_RAW:
		return constvar.PlatformWindows, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_WINDOWS_STARTO_VM:
		return constvar.PlatformWindows, false
	case dslpb.RunnerEngine_RUNNER_ENGINE_KUBERNETES:
		return constvar.PlatformLinux, false
	}
	// Default to Linux container.
	return constvar.PlatformLinux, true
}

func GetWorkingDirectory(runsOn *dslpb.RunnerSpec, platform string) string {
	var workDir string
	if runsOn != nil {
		workDir = runsOn.WorkingDirectory
	}
	if filepath.IsAbs(workDir) {
		return workDir
	}
	if winpath.IsAbs(workDir) {
		return workDir
	}
	if platform == constvar.PlatformWindows {
		return strings.ReplaceAll(filepath.Join(defaultWindowsWorkSpace, workDir), "/", `\`)
	}
	return filepath.Join(defaultWorkSpace, workDir)
}

func getJobIfCondition(job *dslpb.Job) string {
	var ifCond string
	trimExpr := func(expr string) string {
		if strings.HasPrefix(expr, "{{") {
			expr = strings.TrimPrefix(expr, "{{")
			expr = strings.TrimSuffix(expr, "}}")
		} else if strings.HasPrefix(expr, "${{") {
			expr = strings.TrimPrefix(expr, "${{")
			expr = strings.TrimSuffix(expr, "}}")
		}
		return expr
	}

	switch {
	case job.If != "" && job.ExtraIf != "":
		// 用户使用状态函数时, 忽略 extraIf.
		// NOTICE: 仅 Bits 流水线场景允许 extraIf, 暂时 hard code BC 语法.
		if expression.RefPipelineStatusFunc(job.If, expression.SyntaxBC) {
			return job.If
		}
		mainIf := trimExpr(job.If)
		extraIf := trimExpr(job.ExtraIf)
		ifCond = fmt.Sprintf("{{(%s)&&(%s)}}", extraIf, mainIf)

	case job.If != "":
		return job.If

	case job.ExtraIf != "":
		return job.ExtraIf
	}

	return ifCond
}

func fillCustomVars(ctx context.Context, pipeline *entity.Pipeline, buildCtx map[string]any, spaceId uint64, varAssigns []*varstorepb.VarAssignment) error {
	spaceIds, err := tcc.GetSpaceIdsBuildCtxHasCustomVars(ctx)
	if err != nil {
		logs.CtxError(ctx, "failed to get TccSpaceIdsBuildCtxHasCustomVars with err:%v", err)
	}
	allowCustomVarsInBuildContext := false
	varOption := pipeline.GetVarOptionPB()
	if varOption != nil && varOption.AllowCustomVarsInBuildContext {
		allowCustomVarsInBuildContext = true
	}
	if !slices.Contains(spaceIds, spaceId) && !allowCustomVarsInBuildContext {
		return nil
	}
	params := map[string]any{}
	workspaceParams := map[string]any{}
	for _, varA := range varAssigns {
		if strings.HasPrefix(varA.GetName(), constvar.SysPrefix) {
			continue
		}
		value, err := pvariable.FlattenValueWithAssign(ctx, varA)
		if err != nil {
			return err
		}
		if varA.CustomScope == varstorepb.CustomScope_CUSTOM_SCOPE_WORKSPACE {
			workspaceParams[pvariable.TrimCustomVarPrefix(varA.GetName())] = value
		} else {
			params[pvariable.TrimCustomVarPrefix(varA.GetName())] = value
		}
	}
	buildCtx["params"] = params
	buildCtx["workspace_params"] = workspaceParams
	return nil
}

func CompileVariables(ctx context.Context, pipeline *entity.Pipeline, pipelineRun *entity.PipelineRun, space *rpcpb.SpaceDetail, varAssigns []*varstorepb.VarAssignment, username string) ([]*enginesdk.Variable, error) {
	runParams := make(map[string]any, 0)
	if len(pipelineRun.RunParams) > 0 {
		if err := json.Unmarshal(pipelineRun.RunParams, &runParams); err != nil {
			return nil, errors.Wrap(err, "unmarshal pipeline run params to map")
		}
	}
	buildCtx := newBuildContext(ctx, pipeline, pipelineRun, space, username)
	err := fillCustomVars(ctx, pipeline, buildCtx, space.Id, varAssigns)
	if err != nil {
		return nil, err
	}
	variables, err := pvariable.AssignsToVariables(ctx, varAssigns)
	if err != nil {
		return nil, err
	}
	variables = append(variables, &enginesdk.Variable{
		Key: "context",
		Value: map[string]any{
			"build": gmap.Union(runParams, buildCtx), // NOTICE: values in buildCtx take priority.
		},
	})

	// Try to evaluate expressions in variables.
	executor := expression.NewBCExecutor()

	// Convert leafboat variables to variable map for executor.
	vm := gslice.ToMap(variables, func(t *enginesdk.Variable) (string, any) {
		return t.Key, t.Value
	})
	for _, v := range variables {
		// Check variable references in v's Value.
		refs, err := executor.VariableReferencesInAny(v.Value)
		if err != nil || len(refs) == 0 {
			continue
		}

		result, err := executor.ExecuteAny(v.Value, vm)
		if err != nil {
			logs.CtxWarn(ctx, "ignore expression execute error in variables, variable_key=%s, err=%v", v.Key, err)
			continue
		}
		logs.CtxInfo(ctx, "set variable to evaluated value, run_id=%d, variable_key=%s", pipelineRun.RunId, v.Key)
		v.Value = result
	}

	return variables, nil
}

func newBuildContext(ctx context.Context, pipeline *entity.Pipeline, pipelineRun *entity.PipelineRun, space *rpcpb.SpaceDetail, username string) map[string]any {
	buildContext := map[string]any{
		"build_id":              pipelineRun.RunId,
		"build_no":              pipelineRun.RunSeq,
		"build_url":             GetPipelineRunUrl(pipelineRun, pipeline),
		"user":                  username,
		"username":              username,
		"trigger_user":          username,
		"workspace_id":          pipeline.SpaceID,
		"workspace":             space.Identification,
		"workspace_title":       space.Identification,
		"workspace_psm_node_id": space.BytetreeId,
		"build_reason":          "mock_build_reason",
		"platform":              "bytecycle",
		"stream_id":             pipeline.PipelineID,
		"stream_name":           pipeline.Name,
		"stream_url":            GetPipelineUrl(pipeline),
		"start_time":            time.Now().Unix(),
		"is_bits":               true,
	}
	if jwtToken, ok := metainfo.GetValue(ctx, constvar.ContextKey_JwtToken); ok {
		buildContext["__jwt__"] = jwtToken
	}
	return buildContext
}
