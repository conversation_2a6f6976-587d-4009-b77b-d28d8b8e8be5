package scm

import (
	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
	"github.com/pkg/errors"
)

const (
	GitSourceGitLab = "gitlab"
	VersionMajor    = "major"
	VersionMinor    = "minor"
	VersionPatch    = "patch"
	VersionFix      = "fix"
)

type SDK struct {
	domain        string
	paasToken     string
	idc           string
	defaultHeader map[string]string
	httpCli       *httpclient.BytedHttpClient
}

func GetDomainByRegion(region string) (string, error) {
	domain, ok := domainMap[region]
	if !ok {
		return "", errors.Errorf("unsupported region %s", region)
	}
	return domain, nil
}

func NewSDKWithTokenAndHeader(token string, idc string, defaultHttpHeader map[string]string) *SDK {
	domain, err := GetDomainByRegion(idc)
	if err != nil {
		panic(err)
	}

	httpCli := httpclient.MustNewBytedHttpClient(httpclient.WithUseRemovePPEEnvHeaderMW)
	httpCli.SetHeaders(defaultHttpHeader)
	return &SDK{
		domain:    domain,
		paasToken: token,
		idc:       idc,
		httpCli:   httpCli,
	}
}
