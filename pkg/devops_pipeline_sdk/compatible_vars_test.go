package devops_pipeline_sdk

import (
	"context"
	"reflect"
	"strings"
	"testing"

	"code.byted.org/bits/monkey"
	"code.byted.org/canal/bytecycle_sdk/resource"
	"code.byted.org/canal/bytecycle_sdk/template/model"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
)

func TestGetCompatibleVarsUpgradeMap(t *testing.T) {
	type args struct {
		templateTags []model.Tags
		varProvider  varstorepb.SysProvider
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		{
			name: "test1",
			args: args{
				templateTags: nil,
				varProvider:  varstorepb.SysProvider_SYS_PROVIDER_DEVELOPMENT_TASK,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetCompatibleVarsUpgradeMap(tt.args.templateTags, tt.args.varProvider)
			for k, v := range got {
				if !strings.HasPrefix(v, getVarProviderPrefix(tt.args.varProvider)) {
					t.Errorf("GetCompatibleVarsUpgradeMap()  key= %v val=%v", k, v)
				}
				t.Log("k:", k, "v:", v)
			}
		})
	}
}

func TestGetUserCustomCompatibleVarsUpgradeMap(t *testing.T) {
	var (
		ctx        = context.Background()
		normalKeys = []string{
			"context.params.aaa",
			"context.params.v23vv",
			"context.params.abc_d1",
			"context.params.a_b_C",
		}

		abnormalKeys = []string{
			"context.params.需要ppe",
			"context.params.a[b]",
		}

		systemKeys = []string{
			"context.params.feature_id",
			"template.need_boe",
			"context.build.requirements",
		}
	)

	normalM := GetUserCustomCompatibleVarsUpgradeMap(ctx, normalKeys)
	require.Zero(t, len(normalM))

	abnormalM := GetUserCustomCompatibleVarsUpgradeMap(ctx, abnormalKeys)
	require.Equal(t, len(abnormalM), len(abnormalKeys))

	systemM := GetUserCustomCompatibleVarsUpgradeMap(ctx, systemKeys)
	require.Zero(t, len(systemM))
}

func Test_isFeatureDeliveryOrReleaseTrainTemplate(t *testing.T) {
	type args struct {
		templateTags []model.Tags
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "feature delivery",
			args: args{templateTags: []model.Tags{{
				Type: "activity_phase",
				Tag:  resource.ActivityPhaseFeatureDevelop,
			}, {
				Type: "region",
				Tag:  "boe",
			}}},
			want: true,
		},
		{
			name: "release train",
			args: args{templateTags: []model.Tags{{
				Type: "activity_phase",
				Tag:  resource.ActivityPhaseTrainDeploy,
			}, {
				Type: "region",
				Tag:  "ppe",
			}}},
			want: true,
		},
		{
			name: "other",
			args: args{templateTags: []model.Tags{{
				Type: "activity_phase",
				Tag:  resource.ActivityPhaseCronjobDeploy,
			}, {
				Type: "region",
				Tag:  "ppe",
			}}},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isFeatureDeliveryOrReleaseTrainTemplate(tt.args.templateTags); got != tt.want {
				t.Errorf("isFeatureDeliveryOrReleaseTrainTemplate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getProjectVarsListByPSMs(t *testing.T) {
	t.Skip()
	type args struct {
		ctx      context.Context
		projects []*resource.PSMProject
	}
	tests := []struct {
		name    string
		args    args
		want    []*resource.BatchGetProjectVarsListData
		wantErr bool
	}{
		{
			name: "tce",
			args: args{
				ctx: context.Background(),
				projects: []*resource.PSMProject{{
					PSM:         "canal.feature.api",
					ProjectType: "tce",
				}},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getProjectVarsListByPSMs(tt.args.ctx, tt.args.projects)
			if (err != nil) != tt.wantErr {
				t.Errorf("getProjectVarsListByPSMs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}

func Test_getOldProjectVars(t *testing.T) {
	type args struct {
		ctx             context.Context
		projectType     sharedpb.ProjectType
		projectUniqueID string
		projectName     string
		username        string
		templateTags    []model.Tags
	}
	p := monkey.Patch(getProjectVarsListByPSMs, func(
		ctx context.Context,
		projects []*resource.PSMProject,
	) ([]*resource.BatchGetProjectVarsListData, error) {
		if len(projects) == 0 {
			return nil, errors.New("no project")
		}
		var res []*resource.BatchGetProjectVarsListData

		for _, project := range projects {
			if project.PSM == "a.b.c" && project.ProjectType == "tce" {
				res = append(res, &resource.BatchGetProjectVarsListData{
					VarsList: []*resource.Vars{{
						VarKey:   "context.project.a",
						VarValue: "va",
					}, {
						VarKey:   "context.project.b",
						VarValue: "vb",
					}},
				})
			}
		}
		if len(res) == 0 {
			return nil, errors.New("no project")
		}
		return res, nil
	})
	defer p.Unpatch()
	tests := []struct {
		name    string
		args    args
		want    map[string]interface{}
		wantErr bool
	}{
		{
			name: "not support type",
			args: args{
				ctx:             context.Background(),
				projectType:     sharedpb.ProjectType_PROJECT_TYPE_SIDECAR,
				projectUniqueID: "a.b.c",
				projectName:     "a.b.c",
				username:        "lizeyu.wyxhh",
				templateTags: []model.Tags{{
					Type: "activity_phase",
					Tag:  resource.ActivityPhaseFeatureTest,
				}},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "tce",
			args: args{
				ctx:             context.Background(),
				projectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				projectUniqueID: "a.b.c",
				projectName:     "a.b.c",
				username:        "lizeyu.wyxhh",
				templateTags: []model.Tags{{
					Type: "activity_phase",
					Tag:  resource.ActivityPhaseFeatureDeploy,
				}},
			},
			want: map[string]interface{}{
				"context.project.a": "va",
				"context.project.b": "vb",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GetOldProjectVars(
				tt.args.ctx,
				tt.args.projectType,
				tt.args.projectUniqueID,
				tt.args.projectName,
				tt.args.username,
			)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOldProjectVars() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetOldProjectVars() got = %v, want %v", got, tt.want)
			}
		})
		t.Run(tt.name, func(t *testing.T) {
			_, err := GetOldProjectVars(
				tt.args.ctx,
				tt.args.projectType,
				"",
				tt.args.projectName,
				tt.args.username,
			)
			if (err != nil) != false {
				return
			}
		})
	}
}
