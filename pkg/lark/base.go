package lark

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	lark "github.com/larksuite/oapi-sdk-go/v3"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/devinfra/hagrid/pkg/tcc"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/gopkg/tccclient"
)

type Client struct {
	lark      *lark.Client
	globalTCC *tccclient.ClientV2
}

var appID string
var appSecret string

const (
	larkAPPKey = "lark_app"
)

// MustInit app id/app secret以加密配置的形式存放在onesite全局的tcc配置服务中，所以需要先初始化golbal tcc
func MustInit() *Client {
	globalTCC := tcc.MustInitGlobalTCC()
	value, err := globalTCC.Get(context.Background(), larkAPPKey)
	if err != nil {
		panic(errors.WithMessage(err, "fail to get lark app config"))
	}
	idAndSecret := strings.Split(value, ":")
	if len(idAndSecret) != 2 {
		panic(fmt.Errorf("invalid lark app config:%v", value))
	}
	appID = idAndSecret[0]
	appSecret = idAndSecret[1]
	lark.FeishuBaseUrl = "https://fsopen.bytedance.net"
	l := lark.NewClient(idAndSecret[0], idAndSecret[1], lark.WithEnableTokenCache(true))
	return &Client{lark: l, globalTCC: globalTCC}
}

func (c *Client) getTenantAccessToken(ctx context.Context) string {
	resp := new(GetTenantAccessTokenRes)

	httpClient := resty.New().
		SetBaseURL("https://fsopen.bytedance.net").
		SetRetryCount(3).
		SetTimeout(10 * time.Second)
	_, err := httpClient.
		R().
		SetBody(GetTenantAccessTokenReq{
			APPID:     appID,
			APPSecret: appSecret,
		}).
		SetResult(resp).
		SetHeaders(map[string]string{
			"Content-Type": "application/json; charset=utf-8",
		}).
		Post("open-apis/auth/v3/tenant_access_token/internal")
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return ""
	}
	return resp.TenantAccessToken
}

func (c *Client) GetUserIDByUsername(ctx context.Context, username string, userIdType string) string {
	// 创建请求对象
	req := larkcontact.NewBatchGetIdUserReqBuilder().
		UserIdType(userIdType).
		Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().
			Emails([]string{emails.WithSuffix(username)}).
			Build()).
		Build()

	// 发起请求
	resp, err := c.lark.Contact.User.BatchGetId(ctx, req)

	// 处理错误
	if err != nil {
		return ""
	}

	// 服务端错误处理
	if !resp.Success() || resp.Data == nil || len(resp.Data.UserList) < 1 || resp.Data.UserList[0].UserId == nil {
		return ""
	}

	return *(resp.Data.UserList[0].UserId)
}

func (c *Client) GetDepartmentIDsByUsername(ctx context.Context, username string) string {

	userID := c.GetUserIDByUsername(ctx, username, "user_id")
	if userID == "" {
		return ""
	}
	req := larkcontact.NewGetUserReqBuilder().
		UserId(userID).
		UserIdType("user_id").
		DepartmentIdType("department_id").
		Build()

	// 发起请求
	resp, err := c.lark.Contact.User.Get(ctx, req)

	// 处理错误
	if err != nil || resp == nil || !resp.Success() || resp.Data == nil || resp.Data.User == nil {
		return ""
	}

	if len(resp.Data.User.DepartmentIds) < 1 {
		return ""
	}

	return resp.Data.User.DepartmentIds[0]
}
