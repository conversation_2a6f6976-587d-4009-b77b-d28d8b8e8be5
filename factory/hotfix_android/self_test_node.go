package hotfix_android

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/gopkg/logs"
	"gorm.io/datatypes"
	"gorm.io/gorm"

	"code.byted.org/bits/release_hotfix/container"
	"code.byted.org/bits/release_hotfix/domain"
	"code.byted.org/bits/release_hotfix/domain/model"
	"code.byted.org/bits/release_hotfix/domain/mysql"
	"code.byted.org/bits/release_hotfix/service"
	"code.byted.org/bits/release_hotfix/utils"
)

type HotfixAndroidSelfTestNode struct {
	model.NodeEntity
	Parameter *HotfixAndroidTestNodeParams
}

type HotfixAndroidTestNodeParams struct {
	AllowList           string                        `json:"allow_list"`        // 逗号分隔的device_id, 表示当前节点的白名单
	AllAllowList        string                        `json:"all_allow_list"`    // 全部白名单， 不同节点不一样
	CheckList           model.CheckList               `json:"check_list"`        // key：[self_test, functionality_test, compatibility:[0, 1, 2]
	Status              int                           `json:"status"`            // 节点状态
	HotfixPkgID         int64                         `json:"hotfix_pkg_id"`     // saveu热修包id
	HotfixPkgStatus     int                           `json:"hotfix_pkg_status"` // saveu热修包状态
	HotfixID            int64                         `json:"hotfix_id"`         // saveu hotfix id
	HotfixStatus        int                           `json:"hotfix_status"`     // 热修的状态
	CreatePkgNodeParams *HotfixAndroidCreatePkgParams `json:"-"`                 // 配置热修包节点参数
	SaveUHotfix         *mysql.SaveUHotfixModel       `json:"-"`                 // 热修
	SaveUHotfixPkg      *mysql.SaveUHotfixPkgModel    `json:"-"`                 // 热修包
	Frame               string                        `json:"frame"`             // 架构框架 robust, reparo
	PatchClassInfo      []string                      `json:"patch_class_info"`  // patch包修复的类信息
}

func NewHotfixAndroidSelfTestNode(ctx context.Context, bizID int64, taskName string) (*HotfixAndroidSelfTestNode, error) {
	logs.CtxInfo(ctx, "[NewHotfixAndroidSelfTestNode]start")
	c := container.GetContainer()
	node, err := c.Mysql.BitsHotfixInterface.QueryNodeByPublishIDAndTaskNameV0(ctx, bizID, taskName)
	if err != nil {
		logs.CtxError(ctx, "[NewHotfixAndroidSelfTestNode]QueryNodeByPublishIDAndTaskNameV0 error: %v, bizID: %v, taskName: %v", err, bizID, taskName)
		return nil, err
	}
	if node.ID == 0 { // 未插入节点
		node.BusinessID = bizID
		node.NodeName = taskName
	}
	entity, err := M2HotfixAndroidSelfTestNode(ctx, node)
	if err != nil {
		logs.CtxError(ctx, "[NewHotfixAndroidSelfTestNode]M2HotfixAndroidSelfTestNode error: %v, bizID: %v, taskName: %v", err, bizID, taskName)
		return nil, err
	}
	// 补全关联信息
	err = entity.queryAll(ctx)
	return entity, err
}

func NewHotfixAndroidSelfTestNodeV1(ctx context.Context, bizID int64, taskName string) (*HotfixAndroidSelfTestNode, error) {
	c := container.GetContainer()
	node, err := c.Mysql.BitsHotfixInterface.QueryNodeByPublishIDAndTaskName(ctx, bizID, taskName)
	if err != nil {
		logs.CtxError(ctx, "[NewHotfixAndroidSelfTestNodeV1]QueryNodeByPublishIDAndTaskName error: %v, bizID: %v, taskName: %v", err, bizID, taskName)
		return nil, err
	}
	entity, err := M2HotfixAndroidSelfTestNode(ctx, node)
	if err != nil {
		logs.CtxError(ctx, "[NewHotfixAndroidSelfTestNodeV1] M2SelfTestEntity error: %v, bizID: %v, taskName: %v", err, bizID, taskName)
		return nil, err
	}

	err = entity.queryAll(ctx)
	return entity, err
}

func HotfixAndroidSelfTestEntity2M(ctx context.Context, node *HotfixAndroidSelfTestNode) (*mysql.HotfixNodeModel, error) {
	var p map[string]interface{}
	p, err := utils.StructToMap(ctx, node.Parameter)
	if err != nil {
		return nil, err
	}
	return &mysql.HotfixNodeModel{
		Model:        gorm.Model{ID: uint(node.NodeEntity.NodeID)},
		BusinessID:   node.GetNodeEntity().BusinessID,
		StartTraceID: node.GetNodeEntity().StartTraceID,
		NodeName:     node.NodeName,
		Status:       node.GetNodeEntity().Status,
		HotfixTaskID: node.GetNodeEntity().HotfixTaskID,
		Parameters:   datatypes.JSONMap(p),
	}, err
}

func M2HotfixAndroidSelfTestNode(ctx context.Context, m *mysql.HotfixNodeModel) (*HotfixAndroidSelfTestNode, error) {
	node := model.NodeEntity{
		NodeID:       int64(m.ID),
		BusinessID:   m.BusinessID,
		StartTraceID: m.StartTraceID,
		NodeName:     m.NodeName,
		Status:       m.Status,
		HotfixTaskID: m.HotfixTaskID,
	}
	e := &HotfixAndroidTestNodeParams{}
	err := utils.MapToStruct(ctx, m.Parameters, e)
	if err != nil {
		return nil, err
	}
	return &HotfixAndroidSelfTestNode{
		NodeEntity: node,
		Parameter:  e,
	}, err
}

func (node *HotfixAndroidSelfTestNode) queryAll(ctx context.Context) error {
	c := container.GetContainer()
	// 查询创建包节点信息
	createPkgM, err := c.Mysql.QueryNodeByPublishIDAndTaskName(ctx, node.NodeEntity.BusinessID, domain.HotfixAndroidCreatePkg)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]QueryNodeByPublishIDAndTaskName error: %v", err)
		return err
	}
	createPkgNode, err := M2HotfixAndroidCreatePkgNode(ctx, createPkgM)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]M2HotfixAndroidCreatePkgNode error: %v", err)
		return err
	}
	node.Parameter.CreatePkgNodeParams = createPkgNode.Parameter
	node.Parameter.Frame = createPkgNode.Parameter.Frame
	node.Parameter.PatchClassInfo = createPkgNode.Parameter.PatchClassInfo
	// 查询任务信息
	node.HotfixTaskID = createPkgNode.HotfixTaskID
	taskInfo, err := c.Mysql.QueryTask(ctx, createPkgNode.HotfixTaskID)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]QueryTask error: %v", err)
		return err
	}
	node.HotfixTaskInfo = &model.HotfixTaskBaseInfo{
		TaskName:       taskInfo.TaskName,
		Platform:       taskInfo.Platform,
		ChatID:         utils.PtrToString(taskInfo.LarkGroupId),
		CloudAppID:     int(taskInfo.CloudAppId),
		TaskID:         int(node.HotfixTaskID),
		ApprovalStatus: utils.PtrToString(taskInfo.ApprovalStatus),
		BitsAppId:      taskInfo.BitsAppId,
		HotfixOwner:    utils.PtrToString(taskInfo.HotfixOwner),
	}
	logs.CtxInfo(ctx, "[HotfixAndroidCreatePkgNode.queryAll]baseInfo=%+v", node.HotfixTaskInfo)
	// 查询saveu hotfix
	if node.Parameter.CreatePkgNodeParams.HotfixID == 0 {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]hotfix is nil")
		err = fmt.Errorf("[HotfixAndroidSelfTestNode.queryAll]hotfix is nil")
		return err
	}
	node.Parameter.SaveUHotfix, err = c.Mysql.QueryHotfix(ctx, node.Parameter.CreatePkgNodeParams.HotfixID)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]get hotfix error: %v", err)
		return err
	}
	node.Parameter.HotfixID = node.Parameter.SaveUHotfix.ID
	node.Parameter.HotfixStatus = node.Parameter.SaveUHotfix.Status
	// 查询saveu hotfix pkg
	if node.Parameter.CreatePkgNodeParams.HotfixPkgID == 0 {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]hotfix pkg is nil")
		err = fmt.Errorf("[HotfixAndroidSelfTestNode.queryAll]hotfix pkg is nil")
		return err
	}
	node.Parameter.SaveUHotfixPkg, err = c.Mysql.QueryHotfixPkg(ctx, node.Parameter.CreatePkgNodeParams.HotfixPkgID)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.queryAll]get hotfix error: %v", err)
		return err
	}
	node.Parameter.HotfixPkgID = node.Parameter.SaveUHotfixPkg.ID
	node.Parameter.AllAllowList = node.Parameter.SaveUHotfixPkg.AllowList
	node.Parameter.HotfixPkgStatus = node.Parameter.SaveUHotfixPkg.Status
	return nil
}

func (node *HotfixAndroidSelfTestNode) Start(ctx context.Context) (bool, error) {
	if node.GetNodeEntity().Status == domain.Running {
		return true, nil
	}
	node.GetNodeEntity().Status = domain.Running
	node.Parameter.Status = domain.Running
	nodeModel, err := HotfixAndroidSelfTestEntity2M(ctx, node)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.Start]HotfixAndroidSelfTestEntity2M error: %v", err)
		return false, err
	}

	c := container.GetContainer()
	_, err = c.Mysql.CreateOrUpdateNode(ctx, nodeModel)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.Start]CreateOrUpdateNode error: %v", err)
		return false, err
	}
	return true, nil
}

func (node *HotfixAndroidSelfTestNode) Name(ctx context.Context) string {
	return node.NodeName
}

func (node *HotfixAndroidSelfTestNode) Running(ctx context.Context) (bool, error) {
	return node.NodeEntity.Status == domain.Running || node.NodeEntity.Status == domain.NotYetCheck, nil
}

func (node *HotfixAndroidSelfTestNode) Close(ctx context.Context) (bool, error) {
	return true, nil
}

func (node *HotfixAndroidSelfTestNode) Reset(ctx context.Context) (bool, error) {
	return true, nil
}

func (node *HotfixAndroidSelfTestNode) Status(ctx context.Context) (int, error) {
	return node.NodeEntity.Status, nil
}

func (node *HotfixAndroidSelfTestNode) StartRelease(ctx context.Context) (bool, error) {
	c := container.GetContainer()
	if node.Parameter.AllAllowList == "" && node.Parameter.AllowList == "" {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode]StartRelease node don't have allow list, node=%+v", *node)
		return false, fmt.Errorf("[HotfixAndroidSelfTestNode]StartRelease node don't have allow list")
	}
	if node.Parameter.AllAllowList == "" {
		node.Parameter.AllAllowList = node.Parameter.AllowList
	}
	// 添加白名单,使包生效
	node.Parameter.SaveUHotfixPkg.AllowList = node.Parameter.AllAllowList
	if node.Parameter.SaveUHotfixPkg.Status == domain.ReleaseHotfixPkgNotEffect {
		node.Parameter.SaveUHotfixPkg.Status = domain.ReleaseHotfixPkgInEffect
	}
	_, err := c.Mysql.CreateOrUpdateHotfixPkg(ctx, node.Parameter.SaveUHotfixPkg)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode]StartRelease UpdateHotfixPackage failed: %+v, node: %+v", err, node)
		return false, fmt.Errorf("[HotfixAndroidSelfTestNode]StartRelease UpdateHotfixPackage failed: %v", err)
	}
	node.Parameter.HotfixPkgStatus = node.Parameter.SaveUHotfixPkg.Status
	return true, nil
}

func (node *HotfixAndroidSelfTestNode) SetParameter(ctx context.Context, IParameters interface{}, checkHandler domain.StatusChangeHandler) error {
	if IParameters == nil {
		return nil
	}
	parameters, ok := IParameters.(*HotfixAndroidTestNodeParams)
	if !ok {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode]SetParameter input type not match: %T===>%T", IParameters, HotfixAndroidTestNodeParams{})
		return fmt.Errorf("[HotfixAndroidSelfTestNode]SetParameter invalid input: %+v, %T", IParameters, IParameters)
	}
	logs.CtxInfo(ctx, "[HotfixAndroidSelfTestNode]SetParameter parameter: %+v, node=%+v", IParameters, *node)
	diff, err := utils.PlainJsonHasDiff(node.Parameter, parameters)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode]SetParameter: %+v, node=%+v, diff failed", IParameters, node)
		return err
	}
	if !diff && node.NewStatus == node.GetNodeEntity().Status {
		logs.CtxInfo(ctx, "[HotfixAndroidSelfTestNode]SetParameter parameter has not been changed: %+v=%+v", node.Parameter, parameters)
		return nil
	}
	o := node.Parameter.AllowList
	oldStatus := node.GetNodeEntity().Status
	node.NewStatus = parameters.Status
	node.GetNodeEntity().Status = node.NewStatus
	node.Parameter = parameters
	(node.Parameter).ReAllowList(o)
	if checkHandler != nil {
		// 需要同步调用
		logs.CtxInfo(ctx, "[HotfixAndroidSelfTestNode]SetParameter AllAllowList: %+v", node.Parameter.AllAllowList)
		err = checkHandler(ctx)
		if err != nil {
			return err
		}
	}
	// 更新node节点
	nodeModel, err := HotfixAndroidSelfTestEntity2M(ctx, node)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.SetParameter]HotfixAndroidSelfTestEntity2M error: %v", err)
		return err
	}
	c := container.GetContainer()
	_, err = c.Mysql.CreateOrUpdateNode(ctx, nodeModel)
	if err != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.SetParameter]CreateOrUpdateNode error: %v", err)
		return err
	}

	// 通过后发消息卡片并上报ci
	if node.NewStatus == domain.Passed && oldStatus != domain.Passed {
		go func() {
			defer utils.Recover(ctx)
			err := service.AfterNodeEnd(ctx, service.AfterNodeEndInfo{
				HotfixTaskID: node.HotfixTaskID,
				PublishID:    node.BusinessID(ctx),
				ChatID:       node.HotfixTaskInfo.ChatID,
				Notice:       node.NodeName + "Pass",
				Operator:     utils.GetUserEmail(ctx),
				BitsAppID:    node.HotfixTaskInfo.BitsAppId,
			})
			if err != nil {
				logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.SetParameter]AfterNodeEnd error: %v", err)
			}
		}()
	}

	go func() { // 回调CD
		defer utils.Recover(ctx)
		if node.GetNodeEntity().Status == domain.Passed {
			atomExecID, err := strconv.ParseInt(node.StartTraceID, 10, 64)
			if err != nil {
				logs.CtxError(ctx, "[HotfixAndroidSelfTestNode.SetParameter]parse job id error: %v", err)
				return
			}
			service.CDCheckpointCallback(ctx, model.CheckpointCheckItemStatus_PASSED, atomExecID, nil)
		}
	}()
	return err
}

func (node *HotfixAndroidSelfTestNode) GetNodeEntity() *model.NodeEntity {
	return &node.NodeEntity
}

func (node *HotfixAndroidSelfTestNode) StatusChangedFunc(ctx context.Context) domain.StatusChangeHandler {
	return func(ctx context.Context) error {
		if node.NewStatus != node.GetNodeEntity().Status {
			logs.CtxInfo(ctx, "[HotfixAndroidSelfTestNode]StatusChangedFunc %+v, status from %+v ===> %+v", node.Name(ctx), node.GetNodeEntity().Status, node.NewStatus)
		}
		if _, err := node.StartRelease(ctx); err != nil {
			return err
		}
		return nil
	}
}

func (node *HotfixAndroidSelfTestNode) GetParameterStr(ctx context.Context) map[string]string {
	res := make(map[string]string)
	x, e := json.Marshal(node.Parameter)
	if e != nil {
		logs.CtxError(ctx, "[HotfixAndroidSelfTestNode]GetParameter error: %+v", e)
		return res
	}
	logs.CtxInfo(ctx, "[HotfixAndroidSelfTestNode]GetParameter ret: %+v", string(x))
	res[domain.PkgSettingsKey] = string(x)
	return res
}

func (node *HotfixAndroidSelfTestNode) BusinessID(ctx context.Context) int64 {
	return node.NodeEntity.BusinessID
}

func (node *HotfixAndroidSelfTestNode) Decode(ctx context.Context, parameters map[string]string) (interface{}, error) {
	s, ok := parameters[domain.PkgSettingsKey]
	if !ok { // 都不存在的话
		return nil, fmt.Errorf("[HotfixAndroidSelfTestNode.Decode]can not find parameters： key=%+v, parameter=%+v", domain.PkgSettingsKey, parameters)
	}
	settings := &HotfixAndroidTestNodeParams{}
	err := json.Unmarshal([]byte(s), settings)
	if err != nil {
		return nil, fmt.Errorf("[HotfixAndroidSelfTestNode.Decode]unexpect parameters: %+v", parameters)
	}
	logs.CtxInfo(ctx, "[HotfixAndroidSelfTestNode.Decode]: %v", settings)
	if settings.Status == domain.NotYetCheck {
		settings.Status = domain.Running
	}
	// 自测只需要关注以下字段
	res := node.Parameter.Copy()
	res.AllowList = settings.AllowList
	res.CheckList = settings.CheckList
	res.Status = settings.Status
	if res.Status == domain.Passed {
		// 如果状态是通过需要满足以下条件
		if res.CheckList.Compatibility != 1 || res.CheckList.FunctionalityTest != 1 || res.AllowList == "" {
			return nil, fmt.Errorf("[HotfixAndroidSelfTestNode.Decode]self test not match，please check checklist and allow list： %+v", parameters)
		}
	}
	return res, err
}

func (node *HotfixAndroidSelfTestNode) Init(ctx context.Context, startTraceID string, taskName string, bizID int64) {
	node.StartTraceID = startTraceID
	node.NodeName = taskName
	node.NodeEntity.BusinessID = bizID
}

func (node *HotfixAndroidSelfTestNode) Offline(ctx context.Context) error {
	pkg := node.Parameter.SaveUHotfixPkg
	if pkg == nil {
		return fmt.Errorf("[HotfixAndroidSelfTestNode.Offline]impossible: saveHotfixPkg is nil")
	}
	if pkg.ID == 0 {
		logs.CtxWarn(ctx, "[HotfixAndroidSelfTestNode.Offline]pkg.ID == 0 %v", utils.ToJsonSimple(node))
		return nil
	}

	if pkg.Status != domain.ReleaseHotfixPkgInEffect {
		logs.CtxWarn(ctx, "[HotfixAndroidSelfTestNode.Offline]pkg.Status != ReleaseHotfixPkgInEffect %v", utils.ToJsonSimple(node))
		return nil
	}

	c := container.GetContainer()
	if err := c.Mysql.SaveUInterface.SetHotfixPkgStatus(ctx, pkg.ID, domain.ReleaseHotfixPkgOffline); err != nil {
		return err
	}
	err := service.ClosePublish(ctx, node.BusinessID(ctx))
	if err != nil {
		return err
	}
	return nil
}

func (settings *HotfixAndroidTestNodeParams) ReAllowList(nodeOriginal string) {
	originalAllDid := strings.Split(settings.AllAllowList, ",") // 原来全部放量的
	dids := strings.Split(settings.AllowList, ",")              // 当前的节点新的did
	nodeOriginalDid := strings.Split(nodeOriginal, ",")         // 修改前的节点的did
	hashOri := make(map[string]struct{}, len(nodeOriginalDid))  // 修改前节点list的hash
	for _, did := range nodeOriginalDid {
		hashOri[did] = struct{}{}
	}
	hash := make(map[string]struct{}, len(originalAllDid)) // 所有list的hash
	for _, did := range originalAllDid {
		hash[did] = struct{}{}
	}
	hashCur := make(map[string]struct{}, len(dids)) // 新的list的hash
	for _, did := range dids {
		hashCur[did] = struct{}{}
	}
	res := make([]string, 0)
	for did := range hashCur {
		_, ok1 := hashOri[did]
		_, ok2 := hash[did]
		if !ok1 && !ok2 {
			res = append(res, did)
		}
	}
	needDel := make(map[string]struct{})
	for did := range hashOri {
		if _, ok := hashCur[did]; !ok {
			needDel[did] = struct{}{}
		}
	}

	for did := range hash {
		_, d := needDel[did]
		if !d {
			res = append(res, did)
		}
	}
	settings.AllAllowList = strings.Join(res, ",")
}

func (settings *HotfixAndroidTestNodeParams) Copy() *HotfixAndroidTestNodeParams {
	res := &HotfixAndroidTestNodeParams{}
	res.SaveUHotfixPkg = settings.SaveUHotfixPkg
	res.SaveUHotfix = settings.SaveUHotfix
	res.AllAllowList = settings.AllAllowList
	res.CreatePkgNodeParams = settings.CreatePkgNodeParams
	res.HotfixID = settings.HotfixID
	res.HotfixStatus = settings.HotfixStatus
	res.HotfixPkgID = settings.HotfixPkgID
	res.HotfixPkgStatus = settings.HotfixPkgStatus
	return res
}
