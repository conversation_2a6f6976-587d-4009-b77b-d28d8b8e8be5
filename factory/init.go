package factory

import (
	"code.byted.org/bits/release_hotfix/domain"
	"code.byted.org/bits/release_hotfix/domain/model"
	"code.byted.org/bits/release_hotfix/factory/flutter_node"
	"code.byted.org/bits/release_hotfix/factory/hotfix_android"
	"code.byted.org/bits/release_hotfix/factory/hotfix_harmony"
	"code.byted.org/bits/release_hotfix/factory/hotfix_ios_node"
	"code.byted.org/bits/release_hotfix/factory/plugin_android"
	"code.byted.org/bits/release_hotfix/utils"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
	"context"
	"fmt"
)

type IHotfixNode interface {
	Start(ctx context.Context) (bool, error) // 节点任务开始,需要包的信息
	Name(ctx context.Context) string
	Running(ctx context.Context) (bool, error) // 是否正在运行中
	Close(ctx context.Context) (bool, error)   // 任务关闭
	Reset(ctx context.Context) (bool, error)   // 状态机回退
	Status(ctx context.Context) (int, error)
	StartRelease(ctx context.Context) (bool, error)
	SetParameter(ctx context.Context, parameters interface{}, checkHandler domain.StatusChangeHandler) error
	GetNodeEntity() *model.NodeEntity
	StatusChangedFunc(ctx context.Context) domain.StatusChangeHandler
	GetParameterStr(ctx context.Context) map[string]string
	Init(ctx context.Context, startTraceID string, taskName string, bizID int64)
	BusinessID(ctx context.Context) int64
	Decode(ctx context.Context, parameters map[string]string) (interface{}, error)
	Offline(ctx context.Context) error
}

type GrayReleaseNodeImpl interface {
	ChangeToAuto(ctx context.Context, email string) error
	ChangeToManual(ctx context.Context, email string) error
	StepToNext(ctx context.Context, email string) error
	StepToPause(ctx context.Context, email string) error
	StepToContinue(ctx context.Context, email string) error
}

type IPluginNode interface {
	Start(ctx context.Context, param map[string]string) (bool, error) // 节点任务开始,需要包的信息
	Running(ctx context.Context) (bool, error)                        // 是否正在运行中
	Close(ctx context.Context) (bool, error)                          // 任务关闭
	Reset(ctx context.Context) (bool, error)                          // 状态机回退
	Status(ctx context.Context) (int, error)
	SetParameter(ctx context.Context, parameters interface{}) error
	GetParameterStr(ctx context.Context) map[string]string
	Decode(ctx context.Context, parameters map[string]string) (interface{}, error)
	Offline(ctx context.Context) error
	Prepare(ctx context.Context) (bool, error)
	Name(ctx context.Context) string
}

func NewIHotfixIOSTaskNodeEntity(ctx context.Context, startTraceID string, taskName string, bizID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.ConfigurePkg:
		e, err = hotfix_ios_node.NewHotfixTaskPkgEntity(ctx, bizID, taskName)
	case domain.SelfTest:
		e, err = hotfix_ios_node.NewHotfixSelfTestEntity(ctx, bizID, taskName)
	case domain.QATest:
		e, err = hotfix_ios_node.NewHotfixQaTestEntity(ctx, bizID, taskName)
	case domain.Release:
		e, err = hotfix_ios_node.NewHotfixReleaseEntity(ctx, bizID, taskName)
	case domain.AutoTest:
		e, err = hotfix_ios_node.NewHotfixAutoTestEntity(ctx, bizID, taskName)

	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	if err == nil {
		// 新建的话补充这部分信息 如果已经存在的直接更新这部分，没啥副作用
		e.Init(ctx, startTraceID, taskName, bizID)
	}
	return
}

func NewIFlutterTaskNodeEntity(ctx context.Context, startTraceID string, taskName string, bizID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.ConfigurePkg:
		e, err = flutter_node.NewFlutterTaskPkgEntity(ctx, bizID, taskName)
	case domain.SelfTest:
		e, err = flutter_node.NewFlutterSelfTestEntity(ctx, bizID, taskName)
	case domain.QATest:
		e, err = flutter_node.NewFlutterQATestEntity(ctx, bizID, taskName)
	case domain.Release:
		e, err = flutter_node.NewFlutterReleaseEntity(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	if err == nil {
		// 新建的话补充这部分信息 如果已经存在的直接更新这部分，没啥副作用
		e.Init(ctx, startTraceID, taskName, bizID)
	}
	return
}

func NewIHotfixAndroidTaskNodeEntity(ctx context.Context, startTraceID string, taskName string, bizID int64, pipelineID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.HotfixAndroidCreatePkg:
		e, err = hotfix_android.NewHotfixAndroidCreatePkgNode(ctx, bizID, taskName, pipelineID)
	case domain.SelfTest:
		e, err = hotfix_android.NewHotfixAndroidSelfTestNode(ctx, bizID, taskName)
	case domain.QATest:
		e, err = hotfix_android.NewHotfixAndroidQATestNode(ctx, bizID, taskName)
	case domain.Release:
		e, err = hotfix_android.NewHotfixAndroidReleaseNode(ctx, bizID, taskName)
	case domain.PatchConflictCheck:
		e, err = hotfix_android.NewHotfixAndroidPatchConflictNode(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	if err == nil {
		// 新建的话补充这部分信息 如果已经存在的直接更新这部分，没啥副作用
		e.Init(ctx, startTraceID, taskName, bizID)
	}
	return
}

// 鸿蒙新建热修节点
func NewIHotfixHarmonyTaskNodeEntity(ctx context.Context, startTraceID string, taskName string, bizID int64, pipelineID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.HotfixHarmonyCreatePkg:
		e, err = hotfix_harmony.NewHotfixHarmonyCreatePkgNode(ctx, bizID, taskName, pipelineID)
	case domain.SelfTest:
		e, err = hotfix_harmony.NewHotfixHarmonySelfTestNode(ctx, bizID, taskName)
	case domain.QATest:
		e, err = hotfix_harmony.NewHotfixHarmonyQATestNode(ctx, bizID, taskName)
	case domain.Release:
		e, err = hotfix_harmony.NewHotfixHarmonyReleaseNode(ctx, bizID, taskName)
	case domain.PatchConflictCheck:
		e, err = hotfix_harmony.NewHotfixHarmonyPatchConflictNode(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	if err == nil {
		// 新建的话补充这部分信息 如果已经存在的直接更新这部分，没啥副作用
		e.Init(ctx, startTraceID, taskName, bizID)
	}
	return
}

func NewIHotfixIOSTaskNodeEntityV1(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.ConfigurePkg:
		e, err = hotfix_ios_node.NewHotfixTaskPkgEntityV1(ctx, bizID, taskName)
	case domain.SelfTest:
		e, err = hotfix_ios_node.NewHotfixSelfTestEntityV1(ctx, bizID, taskName)
	case domain.QATest:
		e, err = hotfix_ios_node.NewHotfixQaTestEntityV1(ctx, bizID, taskName)
	case domain.Release:
		e, err = hotfix_ios_node.NewHotfixReleaseEntityV1(ctx, bizID, taskName)
	case domain.AutoTest:
		e, err = hotfix_ios_node.NewHotfixAutoTestEntityV1(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}

func NewIFlutterTaskNodeEntityV1(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.ConfigurePkg:
		e, err = flutter_node.NewFlutterTaskPkgEntityV1(ctx, bizID, taskName)
	case domain.SelfTest:
		e, err = flutter_node.NewFlutterSelfTestEntityV1(ctx, bizID, taskName)
	case domain.QATest:
		e, err = flutter_node.NewFlutterQATestEntityV1(ctx, bizID, taskName)
	case domain.Release:
		e, err = flutter_node.NewFlutterReleaseEntityV1(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}

func NewIHotfixAndroidTaskNodeEntityV1(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.HotfixAndroidCreatePkg:
		e, err = hotfix_android.NewHotfixAndroidCreatePkgNodeV1(ctx, bizID, taskName)
	case domain.SelfTest:
		e, err = hotfix_android.NewHotfixAndroidSelfTestNodeV1(ctx, bizID, taskName)
	case domain.QATest:
		e, err = hotfix_android.NewHotfixAndroidQATestNodeV1(ctx, bizID, taskName)
	case domain.Release:
		e, err = hotfix_android.NewHotfixAndroidReleaseNodeV1(ctx, bizID, taskName)
	case domain.PatchConflictCheck:
		e, err = hotfix_android.NewHotfixAndroidPatchConflictNodeV1(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}

func NewIHotfixHarmonyTaskNodeEntityV1(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	switch taskName {
	case domain.HotfixHarmonyCreatePkg:
		e, err = hotfix_harmony.NewHotfixHarmonyCreatePkgNodeV1(ctx, bizID, taskName)
	case domain.SelfTest:
		e, err = hotfix_harmony.NewHotfixHarmonySelfTestNodeV1(ctx, bizID, taskName)
	case domain.QATest:
		e, err = hotfix_harmony.NewHotfixHarmonyQATestNodeV1(ctx, bizID, taskName)
	case domain.Release:
		e, err = hotfix_harmony.NewHotfixHarmonyReleaseNodeV1(ctx, bizID, taskName)
	case domain.PatchConflictCheck:
		e, err = hotfix_harmony.NewHotfixHarmonyPatchConflictNodeV1(ctx, bizID, taskName)
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}

func MockHotfixIOSTaskNodeEntity(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	node := model.NodeEntity{
		NodeID:       0,
		BusinessID:   bizID,
		StartTraceID: "",
		NodeName:     taskName,
		Status:       0,
		HotfixTaskID: 0,
	}

	switch taskName {
	case domain.ConfigurePkg:
		e = &hotfix_ios_node.HotfixTaskPkgEntity{
			NodeEntity: node,
			Parameters: hotfix_ios_node.NewPkgSettings(&node),
		}
	case domain.SelfTest:
		e = &hotfix_ios_node.SelfTestEntity{
			NodeEntity: node,
			Settings:   hotfix_ios_node.NewSettings(&node),
		}
	case domain.QATest:
		e = &hotfix_ios_node.QATestEntity{
			NodeEntity: node,
			Settings:   hotfix_ios_node.NewSettings(&node),
		}
	case domain.Release:
		e = &hotfix_ios_node.HotfixReleaseEntity{
			NodeEntity: node,
			Settings:   hotfix_ios_node.NewSettings(&node),
		}
	case domain.AutoTest:
		e = &hotfix_ios_node.HotfixAutoTestEntity{
			NodeEntity:          node,
			HotfixAutoTestParam: hotfix_ios_node.HotfixAutoTestParam{},
		}
	default:
		return nil, fmt.Errorf("[MockHotfixIOSTaskNodeEntity]unknow taskName: %+v", taskName)
	}
	return e, nil
}

func MockFlutterTaskNodeEntity(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	node := model.NodeEntity{
		NodeID:       0,
		BusinessID:   bizID,
		StartTraceID: "",
		NodeName:     taskName,
		Status:       0,
		HotfixTaskID: 0,
	}
	switch taskName {
	case domain.ConfigurePkg:
		e = &flutter_node.FlutterTaskPkgEntity{
			NodeEntity: node,
			Parameters: &flutter_node.FlutterPkgSettings{
				Node: &node,
			},
		}
	case domain.SelfTest:
		e = &flutter_node.FlutterSelfTestEntity{
			NodeEntity: node,
			FlutterSettings: flutter_node.FlutterSettings{
				Node: &node,
			},
		}
	case domain.QATest:
		e = &flutter_node.FlutterQATestEntity{
			NodeEntity: node,
			FlutterSettings: flutter_node.FlutterSettings{
				Node: &node,
			},
		}
	case domain.Release:
		e = &flutter_node.FlutterReleaseEntity{
			NodeEntity: node,
			FlutterSettings: flutter_node.FlutterSettings{
				Node: &node,
			},
		}
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}

func MockHotfixAndroidTaskNodeEntity(ctx context.Context, taskName string, bizID int64) (e IHotfixNode, err error) {
	node := model.NodeEntity{
		NodeID:       0,
		BusinessID:   bizID,
		StartTraceID: "",
		NodeName:     taskName,
		Status:       0,
		HotfixTaskID: 0,
	}

	switch taskName {
	case domain.HotfixAndroidCreatePkg:
		e = &hotfix_android.HotfixAndroidCreatePkgNode{
			NodeEntity: node,
			Parameter:  &hotfix_android.HotfixAndroidCreatePkgParams{},
		}
	case domain.SelfTest:
		e = &hotfix_android.HotfixAndroidSelfTestNode{
			NodeEntity: node,
			Parameter:  &hotfix_android.HotfixAndroidTestNodeParams{},
		}
	case domain.QATest:
		e = &hotfix_android.HotfixAndroidQATestNode{
			NodeEntity: node,
			Parameter:  &hotfix_android.HotfixAndroidTestNodeParams{},
		}
	case domain.Release:
		e = &hotfix_android.HotfixAndroidReleaseNode{
			NodeEntity: node,
			Parameter:  &hotfix_android.HotfixAndroidReleaseNodeParameter{},
		}
	case domain.PatchConflictCheck:
		e = &hotfix_android.HotfixAndroidPatchConflictNode{
			NodeEntity:             node,
			PatchConflictNodeParam: hotfix_android.PatchConflictNodeParam{},
		}
	default:
		return nil, fmt.Errorf("[MockHotfixAndroidTaskNodeEntity]unknow taskName: %+v", taskName)
	}
	return e, nil
}

func NodeAction(ctx context.Context, node IHotfixNode, action domain.Action) (bool, error) {
	logs.CtxInfo(ctx, "[NodeAction]start, action: %v", action)
	var (
		res bool
		err error
	)
	switch action {
	case domain.NodeStart:
		res, err = node.Start(ctx)
	case domain.NodeIsEnd:
		res, err = node.Running(ctx)
	case domain.NodeClose, domain.NodeReset:
		res, err = node.Close(ctx)
	default:
		logs.CtxError(ctx, "[NodeAction]unknown action: %+v", action)
	}
	if err != nil {
		logs.CtxError(ctx, "[NodeAction]error: %+v, res = %+v", err, res)
	}
	return res, err
}

func GetNodeEntityDetail(ctx context.Context, node IHotfixNode, r *hotfix.HotfixNodeParametersDetailResponse) {
	entity := node.GetNodeEntity()
	if entity.Status == domain.NotYetCheck {
		entity.Status = domain.Running
	}
	r.Status = utils.Int32ToPtr(int32(entity.Status))
	r.TaskName = utils.StrToPtr(string(entity.NodeName))
	r.BusinessId = int64(entity.BusinessID)
	r.HotfixTaskId = utils.Int64ToPtr(int64(entity.HotfixTaskID))
	r.Parameters = node.GetParameterStr(ctx)
	r.StartTraceId = entity.StartTraceID

	return
}

func NewIPluginNodeEntity(ctx context.Context, taskName string, atomExecutionID int64, withDefault bool) (node IPluginNode, err error) {
	switch taskName {
	case domain.PluginPackageTask:
		node, err = plugin_android.NewGetPluginPkgNode(ctx, atomExecutionID, withDefault)
	case domain.PluginReleaseTask:
		node, err = plugin_android.NewPluginReleaseNode(ctx, atomExecutionID, withDefault)
	case domain.PluginConfigRuleTask:
		node, err = plugin_android.NewPluginConfigRuleNode(ctx, atomExecutionID, withDefault)
	case domain.PluginInnerTestTask:
		node, err = plugin_android.NewInnerTestNode(ctx, atomExecutionID, withDefault)
	case domain.PluginAutoTestTask:
		node, err = plugin_android.NewPluginAutoTestNode(ctx, atomExecutionID, withDefault)
	case domain.FollowVersionPluginCheckTask:
		node, err = plugin_android.NewFollowVersionPluginCheckNode(ctx, atomExecutionID, withDefault)

	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}

func NodePluginAction(ctx context.Context, node IPluginNode, action domain.Action, param map[string]string) (bool, error) {
	logs.CtxInfo(ctx, "[NodeAction]start, action: %v", action)
	var (
		res bool
		err error
	)
	switch action {
	case domain.NodeStart:
		res, err = node.Start(ctx, param)
	case domain.NodeIsEnd:
		res, err = node.Running(ctx)
	case domain.NodeClose, domain.NodeReset:
		res, err = node.Close(ctx)
	default:
		logs.CtxError(ctx, "[NodeAction]unknown action: %+v", action)
	}
	if err != nil {
		logs.CtxError(ctx, "[NodeAction]error: %+v, res = %+v", err, res)
	}
	return res, err
}

func MockPluginTaskNodeEntity(ctx context.Context, taskName string, atomExecID int64) (e IPluginNode, err error) {
	switch taskName {
	case domain.PluginPackageTask:
		e = &plugin_android.GetPluginPkgNode{
			Param: plugin_android.GetPluginPkgNodeParameter{
				TaskName:        taskName,
				AtomExecutionID: atomExecID,
			},
		}
	case domain.PluginReleaseTask:
		e = &plugin_android.PluginReleaseNode{
			Param: plugin_android.PluginReleaseNodeParameter{
				TaskName:        taskName,
				AtomExecutionID: atomExecID,
			},
		}
	default:
		return nil, fmt.Errorf("unknow taskName: %+v", taskName)
	}
	return
}
