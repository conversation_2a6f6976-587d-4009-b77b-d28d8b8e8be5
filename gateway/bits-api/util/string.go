package util

import (
	"strconv"

	json "github.com/bytedance/sonic"
)

func IsInStrList(item string, strList []string) bool {
	for _, str := range strList {
		if item == str {
			return true
		}
	}
	return false
}

func ToJson(value interface{}) string {
	jsonByte, err := json.Marshal(value)
	if err != nil {
		return "failed in ToJson()"
	}

	return string(jsonByte)
}

func ToString(obj interface{}) string {
	if obj == nil {
		return ""
	}
	var ret string
	switch obj.(type) {
	case string: // 原本就是string类型的直接返回
		ret = obj.(string)
	case int:
		val := obj.(int)
		ret = strconv.FormatInt(int64(val), 10)
	case int32:
		val := obj.(int32)
		ret = strconv.FormatInt(int64(val), 10)
	case int64:
		val := obj.(int64)
		ret = strconv.FormatInt(int64(val), 10)
	case bool:
		val := obj.(bool)
		ret = strconv.FormatBool(val)
	case float32:
		val := obj.(float32)
		ret = strconv.FormatFloat(float64(val), 'f', -1, 64)
	case float64:
		val := obj.(float64)
		ret = strconv.FormatFloat(val, 'f', -1, 64)
	default: // 序列化成string
		ret = ToJson(obj)
	}
	return ret
}
