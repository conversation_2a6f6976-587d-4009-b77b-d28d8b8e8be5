load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "bitsctl",
    srcs = ["router.go"],
    importpath = "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/bitsctl",
    visibility = ["//visibility:public"],
    deps = [
        "//gateway/bits-api/kitex_gen/bytedance/bits/git_server",
        "//gateway/bits-api/pkg/rpc",
        "//gateway/bits-api/pkg/tcc",
        "@com_github_gin_gonic_gin//:gin",
        "@org_byted_code_gopkg_lang//conv",
    ],
)
