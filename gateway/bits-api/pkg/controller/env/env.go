package env

import (
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
)

func GetEnvInfoForDeployAtom(c *gin.Context) httpR.HttpRun {
	var req = new(dev.GetEnvInfoForDeployAtomRequest)
	err := c.ShouldBindJSON(req)
	if err != nil {
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddError(err))
	}
	if req.GetProjectUniqueId() == "" || req.GetEnvName() == "" || req.GetControlPlane() == "" {
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput)
	}
	resp := dev.GetEnvInfoForDeployAtomResponse{Input: &dev.DeploymentInput{
		AuthType:                   1,
		CancelPreUpgradeTicket:     true,
		ClusterNamesSwitch:         "user_defined",
		CreateOrUpdatePreOperateOn: true,
		KeepDays:                   15,
		ResourceType:               "short",
		RolloutStrategy:            "default",
		ShouldNotAddEnvPrefix:      true,
		SkipIfScmVersionNochange:   true,
	}}
	if req.GetDevBasicId() != 0 && req.GetDevNodeId() != 0 {
		req.ControlPlane = MapControlPlaneToDev(req.ControlPlane).String()
		r, err := rpc.DevTaskCoreClient.GetEnvInfoForDeployAtom(c, req)
		if err != nil {
			return util.UniformResponseWithError(c, err)
		}

		resp.Input.EnvManagers = r.Input.EnvManagers
		resp.Input.EnvName = r.Input.EnvName
		resp.Input.IsSingleIdc = r.Input.IsSingleIdc
		resp.Input.SingleIdc = r.Input.SingleIdc
		resp.Input.PsmList = r.Input.PsmList
		resp.Input.TargetClusterConfigs = r.Input.TargetClusterConfigs
		resp.Input.EnableHotDeploy = r.Input.EnableHotDeploy
		resp.Input.EnablePpeDebug = r.Input.EnablePpeDebug
		resp.Input.EnvType = r.Input.EnvType
	} else if req.GetRtId() != 0 && req.GetStageId() != 0 {
		r, err := rpc.CdClient.GetEnvInfoForDeployAtom(c, &release_ticketpb.GetEnvInfoForDeployAtomReq{
			RtId:            uint64(req.GetRtId()),
			RtStageId:       uint64(req.GetStageId()),
			ProjectUniqueId: req.GetProjectUniqueId(),
			ControlPlane:    MapControlPlaneToRt(req.GetControlPlane()),
			EnvName:         req.GetEnvName(),
		})
		if err != nil {
			return util.UniformResponseWithError(c, err)
		}

		resp.Input.EnvManagers = r.Input.EnvManagers
		resp.Input.EnvName = r.Input.EnvName
		resp.Input.IsSingleIdc = r.Input.IsSingleIdc
		resp.Input.SingleIdc = r.Input.SingleIdc
		resp.Input.PsmList = r.Input.PsmList
		resp.Input.TargetClusterConfigs = r.Input.TargetClusterConfigs
		resp.Input.EnableHotDeploy = r.Input.EnableHotDeploy
		resp.Input.EnablePpeDebug = r.Input.EnablePpeDebug
		resp.Input.EnvType = r.Input.EnvType
	} else {
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput)
	}

	return httpR.StatusOK(resp)
}

func MapControlPlaneToDev(cp string) dev.ControlPlane {
	switch cp {
	case "I18N", "I18N(I18N-TT)":
		return dev.ControlPlane_CONTROL_PLANE_I18N
	case "CN":
		return dev.ControlPlane_CONTROL_PLANE_CN
	case "TTP":
		return dev.ControlPlane_CONTROL_PLANE_TTP
	case "US-TTP":
		return dev.ControlPlane_CONTROL_PLANE_US_TTP
	case "EU-TTP":
		return dev.ControlPlane_CONTROL_PLANE_EU_TTP
	case "I18N-BD":
		return dev.ControlPlane_CONTROL_PLANE_I18N_BD
	default:
		return dev.ControlPlane_CONTROL_PLANE_UNSPECIFIED
	}

}

func MapControlPlaneToRt(cp string) sharedpb.ControlPlane {
	switch cp {
	case "I18N", "I18N(I18N-TT)":
		return sharedpb.ControlPlane_CONTROL_PLANE_I18N
	case "CN":
		return sharedpb.ControlPlane_CONTROL_PLANE_CN
	case "TTP":
		return sharedpb.ControlPlane_CONTROL_PLANE_TTP
	case "US-TTP":
		return sharedpb.ControlPlane_CONTROL_PLANE_US_TTP
	case "EU-TTP":
		return sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP
	case "I18N-BD":
		return sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD
	default:
		return sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED
	}
}
