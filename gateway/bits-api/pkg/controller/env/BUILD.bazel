load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "env",
    srcs = [
        "biz_configs.go",
        "env.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/env",
    visibility = ["//visibility:public"],
    deps = [
        "//gateway/bits-api/kitex_gen/bits/devops/continuous_deployment",
        "//gateway/bits-api/kitex_gen/bytedance/bits/dev",
        "//gateway/bits-api/pkg/rpc",
        "//gateway/bits-api/util",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//libs/bits_err",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_hallokael_httpr//:httpR",
        "@org_byted_code_gin_ginex//:ginex",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
