package devtask

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bits/gateway/api"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bits/gateway/compatible"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/lang/gg/gslice"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func GetGitlabAuthorizeState(c *gin.Context) httpR.HttpRun {
	req := &git_server.GitlabLoginStateRequest{
		Username: util.GetUserName(c),
	}
	resp, err := rpc.GitClient.GitlabLoginStateRequest(c, req)
	if err != nil {
		return util.UniformResponseWithError(c, err)
	}
	res := &api.GetGitlabAuthorizeStateResponse{
		Login: resp.GetLogin(),
	}
	return httpR.StatusOK(res)
}

func GetDefaultProjects(c *gin.Context) httpR.HttpRun {
	var query = api.NewGetDefaultProjectsRequest()
	if err := c.ShouldBind(query); err != nil {
		return util.UniformResponseWithError(c, err)
	}
	req := &dev.MockRequest{
		Scenario: dev.MockEvent_default_projects.String(),
		DefaultProjectsReq: &dev.MockGetDefaultProjects{
			SpaceId: query.GetSpaceId(),
		},
	}
	resp, err := rpc.DevTaskCoreClient.Mock(c, req)
	if err != nil {
		return util.UniformResponseWithError(c, err)
	}

	res := &compatible.GetDefaultProjectsResponse{
		Projects: gslice.Map(resp.GetProjects(), deployProjectInfoToCompatibleBcProjectInfo),
	}
	return httpR.StatusOK(res)
}
