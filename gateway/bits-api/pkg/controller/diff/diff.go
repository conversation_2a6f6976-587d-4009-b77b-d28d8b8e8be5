package diff

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/tcc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

type diffCheckPipelineInfo struct {
	PipelineId int64  `json:"pipelineID"`
	Status     string `json:"status"`
	Msg        string `json:"msg"`
}
type getDiffCheckResultResp struct {
	Finished bool                        `json:"finished"`
	Data     *optimus.MrDiffCheckResult_ `json:"data"`
	Pipeline *diffCheckPipelineInfo      `json:"pipeline"`
}

type threeQueryBinding struct {
	MrId      *int64 `json:"mr_id" form:"mr_id"`
	DevId     *int64 `json:"dev_id" form:"dev_id"`
	ProjectId *int64 `json:"project_id" form:"project_id"`
	Iid       *int64 `json:"iid" form:"iid"`
}

func GetDiffCheckResult(ctx *gin.Context) httpR.HttpRun {
	query := threeQueryBinding{}
	if ctx.ShouldBindQuery(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	var res *optimus.GetMrDiffCheckResultResp
	var err error
	if query.MrId != nil {
		res, err = rpc.OptimusClient.GetMrDiffCheckResult_(ctx, &optimus.GetMrDiffCheckResultReq{MrId: *query.MrId})
	} else if query.DevId != nil {
		res, err = rpc.OptimusClient.GetDevDiffCheckResult_(ctx, &optimus.GetDevDiffCheckResultReq{DevId: *query.DevId})
	} else if query.ProjectId != nil && query.Iid != nil {
		res, err = rpc.OptimusClient.GetDiffCheckResultByProjectIdIID(ctx, &optimus.GetDiffCheckResultByProjectIdIIDReq{ProjectId: *query.ProjectId, Iid: *query.Iid})
	} else {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddError(err))
	}
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	if res.Result_ != nil {
		gslice.SortBy(res.Result_.Node, func(a, b *optimus.DiffCheckNode) bool {
			return a.Id < b.Id
		})
		gslice.SortBy(res.Result_.Edge, func(a, b *optimus.DiffCheckEdge) bool {
			if a.To == b.To {
				return a.From < b.From
			}
			return a.To < b.To
		})
		gslice.SortBy(res.Result_.Starts, func(a, b *optimus.DiffCheckNode) bool {
			return a.Id < b.Id
		})
	}
	return httpR.StatusOK(getDiffCheckResultResp{
		Finished: res.Result_ != nil,
		Data:     res.Result_,
		Pipeline: &diffCheckPipelineInfo{
			PipelineId: res.PipelineInfo.PipelineId,
			Status:     res.PipelineInfo.Status,
			Msg:        res.PipelineInfo.Msg,
		},
	})
}

type argosMonitor struct {
	threeQueryBinding
	Query *optimus.AffectedMethodsQuery `json:"query"`
}

func ArgosMonitor(ctx *gin.Context) httpR.HttpRun {
	query := &argosMonitor{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	token := util.GetUserToken(ctx)
	if token == "" {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	req := &optimus.GetAffectedMethodsMonitorReq{
		Target: &optimus.DiffCheckTargetQuery{
			MrId:  query.MrId,
			DevId: query.DevId,
		},
		Query:     query.Query,
		UserToken: token,
	}
	if query.ProjectId != nil && query.Iid != nil {
		req.Target.ProjectIdIid = &optimus.ProjectIdIIDQuery{
			ProjectId: *query.ProjectId,
			Iid:       *query.Iid,
		}
	}
	res, err := rpc.OptimusClient.GetAffectedMethodsMonitor(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	for k, v := range res.Items {
		res.Items[k].Method = gslice.Map(v.Method, func(f *optimus.AffectedMethodMonitorItem) *optimus.AffectedMethodMonitorItem {
			gslice.SortBy(f.Node, func(a, b *optimus.DiffCheckNode) bool {
				return a.Id < b.Id
			})
			return f
		})
		gslice.SortBy(res.Items[k].Method, func(a, b *optimus.AffectedMethodMonitorItem) bool {
			return a.Method < b.Method
		})
	}
	return httpR.StatusOK(res.Items)
}

func GetDiffCheckGraySpaces(ctx *gin.Context) httpR.HttpRun {
	res, err := tcc.GetDiffCheckGraySpaces(ctx)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(res)
}

type workflowCallbackReq struct {
	EventsType string `json:"events_type"`
	Data       struct {
		Pipeline *workflow.Pipeline `json:"pipeline"`
		Job      []*workflow.Job    `json:"job"`
	} `json:"data"`
}

func Callback(ctx *gin.Context) httpR.HttpRun {
	req := &workflowCallbackReq{}
	if err := ctx.ShouldBind(req); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	if _, err := rpc.OptimusClient.DiffCheckCallback(ctx, &optimus.DiffCheckCallbackReq{
		Pipeline: req.Data.Pipeline,
		Job:      req.Data.Job,
	}); err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK("ok")
}

type invokeQuery struct {
	ProjectId           int64 `form:"project_id" json:"project_id"`
	Iid                 int64 `form:"iid" json:"iid"`
	AutoApplyPermission bool  `form:"auto_apply_permission" json:"auto_apply_permission"`
}

func Invoke(ctx *gin.Context) httpR.HttpRun {
	query := invokeQuery{}
	if ctx.ShouldBind(&query) != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	user := util.GetUserName(ctx)
	if user == "" {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrUserNotLogIn)
	}
	// 检测仓库有没有权限,没有就申请
	if query.AutoApplyPermission && !checkBitsBotPermission(ctx, query.ProjectId) {
		logs.CtxWarn(ctx, "no permission, try to apply")
		err := applyPermission(ctx, query.ProjectId)
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}

	}
	res, err := rpc.OptimusClient.InvokeDiffCheckManually(ctx, &optimus.InvokeDiffCheckManuallyReq{
		Target: &optimus.DiffCheckTargetQuery{ProjectIdIid: &optimus.ProjectIdIIDQuery{
			ProjectId: query.ProjectId,
			Iid:       query.Iid,
		}},
		Username: user,
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(res.PipelineID)
}
