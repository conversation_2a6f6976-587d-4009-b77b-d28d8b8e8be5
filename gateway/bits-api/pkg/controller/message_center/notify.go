package message_center

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/message_center"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func GetNotificationsByGroupName(ctx *gin.Context) httpR.HttpRun {
	query := struct {
		GroupName string `form:"group_name"`
		Page      int64  `form:"page"`
		PageSize  int64  `form:"page_size"`
	}{}
	err := ctx.ShouldBind(&query)
	if err != nil {
		return httpR.StatusBadRequest(err.Error())
	}
	req := &message_center.GetNotificationsByGroupNameQuery{
		GroupName: query.GroupName,
		Page:      query.Page,
		PageSize:  query.PageSize,
	}
	resp, err := rpc.MessageCenterClient.GetNotificationsByGroupName(ctx, req)

	if err != nil {
		logs.CtxError(ctx, "GetNotificationsByGroupName false err: %v", err.Error())
		return httpR.StatusBadRequest(err.Error())
	}
	answer := make([]*Notification, 0)
	for _, item := range resp.Notifications {
		respBody := convertNotificationToResponse(item)
		answer = append(answer, respBody)
	}
	return httpR.StatusOK(model.CommonResult{
		"notifications": answer,
		"total":         resp.Total,
		"success":       resp.Success,
	})
}
func CreateBitsNotification(ctx *gin.Context) httpR.HttpRun {
	query := struct {
		GroupName    string       `form:"group_name"`
		Notification Notification `form:"notification"`
	}{}
	err := ctx.ShouldBind(&query)
	if err != nil {
		return httpR.StatusBadRequest(err.Error())
	}
	username := util.GetUserName(ctx)
	item := convertNotificationToRequest(&query.Notification, username)
	req := &message_center.CreateBitsNotificationQuery{
		GroupName:    query.GroupName,
		Notification: item,
	}
	resp, err := rpc.MessageCenterClient.CreateBitsNotification(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "CreateNotificationsBy false err: %v", err.Error())
		return httpR.StatusBadRequest(err.Error())
	}
	return httpR.StatusOK(model.CommonResult{
		"success": resp.Success,
	})
}
func SetNotificationOnline(ctx *gin.Context) httpR.HttpRun {
	query := &message_center.SetNotificationOnlineQuery{}
	err := ctx.ShouldBind(&query)
	if err != nil {
		return httpR.StatusBadRequest(err.Error())
	}
	username := util.GetUserName(ctx)
	query.UserName = username
	resp, err := rpc.MessageCenterClient.SetNotificationOnline(ctx, query)
	if err != nil {
		logs.CtxError(ctx, "SetNotificationOnline false err: %v", err.Error())
		return httpR.StatusBadRequest(err.Error())
	}
	return httpR.StatusOK(model.CommonResult{
		"success": resp.Success,
	})
}
func SetNotificationOffline(ctx *gin.Context) httpR.HttpRun {
	query := &message_center.SetNotificationOfflineQuery{}
	err := ctx.ShouldBind(&query)
	if err != nil {
		return httpR.StatusBadRequest(err.Error())
	}
	username := util.GetUserName(ctx)
	query.UserName = username
	resp, err := rpc.MessageCenterClient.SetNotificationOffline(ctx, query)
	if err != nil {
		logs.CtxError(ctx, "SetNotificationOffline false err: %v", err.Error())
		return httpR.StatusBadRequest(err.Error())
	}
	return httpR.StatusOK(model.CommonResult{
		"success": resp.Success,
	})
}
func GetOnlineNotificationLatest(ctx *gin.Context) httpR.HttpRun {
	query := struct {
		GroupName string `form:"group_name"`
	}{}
	err := ctx.ShouldBind(&query)
	if err != nil {
		return httpR.StatusBadRequest(err.Error())
	}
	fmt.Println(query)
	req := &message_center.GetOnlineNotificationLatestByGroupNameQuery{
		GroupName: query.GroupName,
	}
	resp, err := rpc.MessageCenterClient.GetOnlineNotificationLatestByGroupName(ctx, req)

	if err != nil {
		logs.CtxError(ctx, "GetOnlineNotificationLatest false err: %v", err.Error())
		return httpR.StatusBadRequest(err.Error())
	}
	if resp.Notification == nil || len(resp.Notification.GroupName) == 0 {
		return httpR.StatusOK(model.CommonResult{
			"notification": nil,
			"success":      resp.Success,
		})
	}
	respNotify := convertNotificationToResponse(resp.Notification)
	return httpR.StatusOK(model.CommonResult{
		"notification": respNotify,
		"success":      resp.Success,
	})
}
