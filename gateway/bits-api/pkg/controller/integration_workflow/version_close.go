package integration_workflow

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/integration_workflow"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

type succeedRequest struct {
	ObjectAttributes struct {
		TargetBranch           string   `json:"target_branch"`
		ProjectGroup           string   `json:"project_group"`
		Author                 string   `json:"author"`
		VersionCloseReportType []string `json:"version_close_report_type"`
		Version                string   `json:"version"`
		ReleaseBranch          string   `json:"release_branch"`
		Id                     int64    `json:"id"`
	} `json:"object_attributes"`
	Event string `json:"event"`
}

func VersionCloseSucceeded(c *gin.Context) httpR.HttpRun {
	req := succeedRequest{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		logs.CtxError(c, "bind request failed:%+v", err)
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	_, err = rpc.IntegrationWorkflowClient.CodeFrozenSucceed(c, &integration_workflow.CodeFrozenSucceedReq{
		Id:                     req.ObjectAttributes.Id,
		TargetBranch:           req.ObjectAttributes.TargetBranch,
		ProjectGroup:           req.ObjectAttributes.ProjectGroup,
		Author:                 req.ObjectAttributes.Author,
		VersionCloseReportType: req.ObjectAttributes.VersionCloseReportType,
		Version:                req.ObjectAttributes.Version,
		ReleaseBranch:          req.ObjectAttributes.ReleaseBranch,
	})
	if err != nil {
		logs.CtxError(c, "rpc failed:%+v", err)
		return util.UniformResponseWithError(c, err)
	}
	return httpR.StatusOK(nil)
}

type endRequest struct {
	ObjectAttributes struct {
		Id int64 `json:"id"`
	} `json:"object_attributes"`
}

func VersionCloseEnd(c *gin.Context) httpR.HttpRun {
	req := endRequest{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		logs.CtxError(c, "bind request failed:%+v", err)
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	_, err = rpc.IntegrationWorkflowClient.CodeFrozenEnd(c, &integration_workflow.CodeFrozenEndReq{
		Id: req.ObjectAttributes.Id,
	})
	if err != nil {
		logs.CtxError(c, "rpc failed:%+v", err)
		return util.UniformResponseWithError(c, err)
	}
	return httpR.StatusOK(nil)
}

//CodeFrozenVersionMr

type versionMrRequest struct {
	ReleaseID int64 `json:"release_id"`
	MrID      int64 `json:"mr_id"`
}

func VersionMr(c *gin.Context) httpR.HttpRun {
	req := versionMrRequest{}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		logs.CtxError(c, "bind request failed:%+v", err)
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	_, err = rpc.IntegrationWorkflowClient.HandleReleaseMR(c, &integration_workflow.HandleReleaseMRRequest{
		MrId:      req.MrID,
		ReleaseId: req.ReleaseID,
	})
	if err != nil {
		logs.CtxError(c, "rpc failed:%+v", err)
		return util.UniformResponseWithError(c, err)
	}
	return httpR.StatusOK(nil)
}
