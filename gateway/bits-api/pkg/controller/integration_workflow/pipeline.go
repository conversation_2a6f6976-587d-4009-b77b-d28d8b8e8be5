package integration_workflow

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/integration_workflow"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func GetPipelineResults(c *gin.Context) httpR.HttpRun {
	req := make([]string, 0)
	err := c.ShouldBindJSON(&req)
	if err != nil {
		logs.CtxError(c, "bind request failed:%+v", err)
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	resp, err := rpc.IntegrationWorkflowClient.GetPipelineResultByCommit(c, &integration_workflow.GetPipelineResultByCommitRequest{
		CommitIds: req,
	})
	if err != nil {
		logs.CtxError(c, "rpc failed:%+v", err)
		return util.UniformResponseWithError(c, err)
	}
	if resp.BaseResp.StatusCode != 0 {
		return httpR.StatusInternalServerError(resp.BaseResp.StatusMessage)
	}
	return httpR.StatusOK(resp.PipelineResults)
}

func GetRcCollaborators(c *gin.Context) httpR.HttpRun {
	var req struct {
		MrID int64 `json:"mr_id" form:"mr_id"`
	}
	err := c.BindQuery(&req)
	if err != nil {
		logs.CtxError(c, "bind request failed:%+v", err)
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.IntegrationWorkflowClient.GetRcMrCollaborators(c, &integration_workflow.GetRcMrCollaboratorsRequest{
		MrId: req.MrID,
	})
	if err != nil {
		logs.CtxError(c, "rpc failed:%+v", err)
		return util.UniformResponseWithError(c, err)
	}
	if err != nil {
		return util.UniformResponseWithError(c, err)
	}
	return httpR.StatusOK(resp.GetRcMrCollaborators())
}

func ConyUsage(c *gin.Context) httpR.HttpRun {
	var req struct {
		URL    string `json:"url" form:"url"`
		Params string `json:"params" form:"params"`
	}
	err := c.ShouldBindJSON(&req)
	if err != nil {
		logs.CtxError(c, "bind request failed:%+v", err)
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	_, err = rpc.IntegrationWorkflowClient.ConyUsage(c, &integration_workflow.ConyUsageRequest{
		Url:   req.URL,
		Param: req.Params,
	})
	if err != nil {
		logs.CtxError(c, "rpc failed:%+v", err)
		return util.UniformResponseWithError(c, err)
	}
	return httpR.StatusOK(nil)
}
