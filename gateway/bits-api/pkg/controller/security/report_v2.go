package security

import (
	BytedanceBitsSecurity "code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/security"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"

	"code.byted.org/gin/ginex"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func ReportV2IsV2(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewIsV2Request()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.IsV2(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ReportV2JobResult(c *gin.Context) httpR.HttpRun {
	if err := CheckJwtToken(c); err != nil {
		logs.Warnf("[ReportV2JobResult] CheckJwtToken failed: %v", err)
		return httpR.StatusForbidden(err)
	}

	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewReportV2JobResultRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.ReportV2JobResult_(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ReportV2JobRuleIssues(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewReportV2JobRuleIssuesRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.ReportV2JobRuleIssues(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ApproveV2ApplyInfo(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApproveV2ApplyInfoRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.ApproveV2ApplyInfo(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ApproveV2Apply(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApproveV2ApplyRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	_, err = rpc.BitsSecurityClient.ApproveV2Apply(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(nil)
}

func ApplyReasons(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApplyReasonsRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.ApproveGetApplyReasons(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ApproveV2BasicInfo(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApproveV2BasicInfoRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.ApproveV2BasicInfo(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ApproveV2ApproveInfo(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApproveV2ApproveInfoRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	resp, err := rpc.BitsSecurityClient.ApproveV2ApproveInfo(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(resp.Data)
}

func ApproveV2Approve(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApproveV2ApproveRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	_, err = rpc.BitsSecurityClient.ApproveV2Approve(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(nil)
}

func ApproveV2Notify(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsSecurity.NewApproveV2NotifyRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusBadRequest(err.Error())
	}

	_, err = rpc.BitsSecurityClient.ApproveV2Notify(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return httpR.StatusInternalServerError(err.Error())
	}

	return httpR.StatusOK(nil)
}
