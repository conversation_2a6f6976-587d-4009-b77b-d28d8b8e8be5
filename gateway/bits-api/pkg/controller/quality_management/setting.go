package quality_management

import (
	BytedanceBitsQualityManagement "code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/quality_management"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gin/ginex"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func GetAppMetricConfig(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsQualityManagement.NewAppMetricConfigRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	resp, err := rpc.QualityManagementClient.GetAppMetricConfig(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp.Data)
}
