package atom_market

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/overpass/bytedance_bits_atom_market/kitex_gen/bytedance/bits/atom_market"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

// GetMetas ...
func GetMetas(ctx *gin.Context) httpR.HttpRun {
	metaQuery := &getAtomQuery{}
	if err := ctx.ShouldBindQuery(metaQuery); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.GetAtomMetasRequest{}
	req.SetCertification(&metaQuery.Certification)
	req.SetName(&metaQuery.Name)
	req.SetClass(&metaQuery.Class)
	req.SetPage(&metaQuery.Page)
	req.SetCount(&metaQuery.Count)
	req.SetIsPublished(&metaQuery.IsPublished)
	req.SetPageSize(&metaQuery.PageSize)
	req.SetTechStack(&metaQuery.TechStack)
	req.SetAppId(&metaQuery.AppID)
	req.SetFavoritesFirst(&metaQuery.FavoritesFirst)
	req.SetOrigin(&metaQuery.Origin)
	req.SetAtomType(&metaQuery.AtomType)
	resp, err := rpc.AtomMarketClient.GetAtomMetas(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}

	response := &AtomMetasResponse{
		List:  resp.GetList(),
		Total: resp.GetTotal(),
	}
	return httpR.StatusOK(response)
}

func GetAtomMetasForPipeline(ctx *gin.Context) httpR.HttpRun {
	query := &GetMetasForPipelineQuery{}
	if err := ctx.ShouldBindQuery(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.GetAtomMetasForPipelineRequest{
		Page:     query.Page,
		PageSize: query.PageSize,
		AppId:    query.AppID,
		Name:     &query.Name,
	}
	resp, err := rpc.AtomMarketClient.GetAtomMetasForPipeline(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	response := &AtomMetasResponse{
		List:  resp.GetList(),
		Total: resp.GetTotal(),
	}
	return httpR.StatusOK(response)
}

func GetMeta(ctx *gin.Context) httpR.HttpRun {
	query := &GetMetaQuery{}
	if err := ctx.ShouldBindQuery(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.GetAtomMetaRequest{
		Id: query.ID,
	}
	resp, err := rpc.AtomMarketClient.GetAtomMeta(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(resp.AtomMeta)
}

func GetMetaByEnglishName(ctx *gin.Context) httpR.HttpRun {
	query := &GetMetaByEnglishNameQuery{}
	if err := ctx.ShouldBindQuery(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.GetAtomMetaByEnglishNameRequest{
		EnglishName: query.EnglishName,
	}
	resp, err := rpc.AtomMarketClient.GetAtomMetaByEnglishName(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(resp.AtomMeta)
}

func CreateMeta(ctx *gin.Context) httpR.HttpRun {
	meta := &atom_market.CreateAtomMetaInfo{}
	if err := ctx.ShouldBindJSON(meta); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.CreateAtomMetaRequest{
		AtomMeta: meta,
	}
	_, err := rpc.AtomMarketClient.CreateAtomMeta(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(nil)
}

func UpdateMeta(ctx *gin.Context) httpR.HttpRun {
	meta := &atom_market.UpdateAtomMetaInfo{}
	if err := ctx.ShouldBindJSON(meta); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.UpdateAtomMetaRequest{
		AtomMeta: meta,
	}
	_, err := rpc.AtomMarketClient.UpdateAtomMeta(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(nil)
}

func DeleteMeta(ctx *gin.Context) httpR.HttpRun {
	query := &DeleteQuery{}
	if err := ctx.ShouldBindQuery(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	req := &atom_market.DeleteAtomMetaRequest{
		Id: query.ID,
	}
	_, err := rpc.AtomMarketClient.DeleteAtomMeta(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(nil)
}

func CreateMetaFavorite(ctx *gin.Context) httpR.HttpRun {
	req := &atom_market.CreateAtomMetaFavoriteRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	_, err := rpc.AtomMarketClient.CreateAtomMetaFavorite(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(nil)
}

func DeleteMetaFavorite(ctx *gin.Context) httpR.HttpRun {
	req := &atom_market.DeleteAtomMetaFavoriteRequest{}
	if err := ctx.ShouldBindJSON(req); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	_, err := rpc.AtomMarketClient.DeleteAtomMetaFavorite(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return httpR.StatusOK(nil)
}
