package approve

import (
	BytedanceBitsApprove "code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/approve"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gin/ginex"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

func ListTasks(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsApprove.NewListTaskByPageRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.BitsApproveClient.ListTaskByPage(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp.Data)
}

func OAGetFlowVirtualInfoForecast(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsApprove.NewOAGetFlowVirtualInfoForecastRequest()
	err := c.BindQuery(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.BitsApproveClient.OAGetFlowVirtualInfoForecast(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp)
}

func BuildChat(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsApprove.NewBuildChatRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.BitsApproveClient.BuildChat(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp)
}

func GetApproveInfo(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsApprove.NewGetApproveInfoRequest()
	err := c.ShouldBind(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.BitsApproveClient.GetApproveInfo(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp.Data)
}

func ListRelatedUsers(c *gin.Context) httpR.HttpRun {
	rpcContext := ginex.RPCContext(c)
	param := BytedanceBitsApprove.NewListApproveRelatedUsersRequest()
	err := c.BindQuery(param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithBitsErr(c, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.BitsApproveClient.ListApproveRelatedUsers(rpcContext, param)
	if err != nil {
		logs.CtxError(rpcContext, err.Error())
		return util.UniformResponseWithError(c, err)
	}

	return httpR.StatusOK(resp.Data)
}
