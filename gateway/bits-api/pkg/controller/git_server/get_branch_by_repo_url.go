/**
 * @Date: 2023/8/17
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package git_server

import (
	"fmt"
	"sync"
	"time"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/git_api"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/lang/sets"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
	"golang.org/x/sync/errgroup"
)

func GetBranchByRepoUrl(ctx *gin.Context) httpR.HttpRun {
	req := git_server.NewGetBranchByRepoUrlRequest()
	if err := ctx.ShouldBindQuery(req); err != nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrParams)
	}

	query := &git_server.GetBranchV2Request{
		GitAddress: gptr.OfNotZero(req.GetProjectUrl()),
		Name:       req.GetBranch(),
	}
	resp, err := rpc.NextCodeClient.GetBranchV2(ctx, query)
	if err != nil {
		return httpR.StatusOK(git_server.NewGetBranchByRepoUrlResponse())
	}

	result := git_server.GetBranchByRepoUrlResponse{
		Branch: &git_server.CodebaseBranch{
			Name: resp.GetBranch().GetName(),
			Commit: &git_server.CodeBaseCommit{
				Sha:     resp.GetBranch().GetCommit().GetId(),
				Message: resp.GetBranch().GetCommit().GetMessage(),
				Author: &git_server.CodeBaseCommitAuthor{
					Name:  resp.GetBranch().GetCommit().GetAuthor().GetName(),
					Email: resp.GetBranch().GetCommit().GetAuthor().GetEmail(),
					Date:  time.Now().String(),
				},
				Committer: &git_server.CodeBaseCommiiter{
					Name:  resp.GetBranch().GetCommit().GetCommitter().GetName(),
					Email: resp.GetBranch().GetCommit().GetCommitter().GetEmail(),
					Date:  time.Now().String(),
				},
			},
		},
	}

	return httpR.StatusOK(result)
}

func GetUserHasMrPermission(ctx *gin.Context) httpR.HttpRun {
	req := git_api.NewGetUserHasMRPermissionRequest()
	if err := ctx.ShouldBindQuery(req); err != nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrParams)
	}

	query := &git_server.GetUserHasMRPermissionRequest{
		RepoUrl:    fmt.Sprintf("code.byted.org/%v", req.GetRepoPath()),
		Username:   util.GetUserName(ctx),
		Permission: req.GetPermission(),
	}
	resp, err := rpc.GitClient.GetUserHasMRPermission(ctx, query)
	if err != nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrForbidden)
	}
	return httpR.StatusOK(&git_api.GetUserHasMRPermissionResponse{HasPermission: resp.GetSuccess()})
}

func GetUserHasMrPermissions(ctx *gin.Context) httpR.HttpRun {
	req := git_api.NewGetUserHasMRPermissionsRequest()
	if err := ctx.ShouldBind(req); err != nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrParams)
	}

	//userName := req.Username

	permissionChunk := gslice.Chunk(req.Permissions, 8)
	mutex := sync.Mutex{}
	permissionsResps := make([]*git_api.GetUserHasMRPermissionsResponseItem, 0)

	for idx, arr := range permissionChunk {
		if idx > 0 {
			time.Sleep(1 * time.Second) // 每组请求之后 sleep 1 秒
		}

		//
		var eg errgroup.Group

		for _, val := range arr {
			permissionReq := val
			eg.Go(func() error {

				query := &git_server.GetUserHasMRPermissionRequest{
					RepoUrl:    fmt.Sprintf("code.byted.org/%v", permissionReq.RepoPath),
					Username:   permissionReq.Username,
					Permission: permissionReq.Permission,
				}
				resp, err := rpc.GitClient.GetUserHasMRPermission(ctx, query)
				if err != nil {
					// 请求失败先按照有权限处理
					mutex.Lock()
					defer mutex.Unlock()
					permissionsResps = append(permissionsResps, &git_api.GetUserHasMRPermissionsResponseItem{
						RepoPath:      permissionReq.RepoPath,
						Permission:    permissionReq.Permission,
						HasPermission: true,
						Username:      permissionReq.Username,
					})
					return err
				}

				mutex.Lock()
				defer mutex.Unlock()
				permissionsResps = append(permissionsResps, &git_api.GetUserHasMRPermissionsResponseItem{
					RepoPath:      permissionReq.RepoPath,
					Permission:    permissionReq.Permission,
					HasPermission: resp.Success,
					Username:      permissionReq.Username,
				})
				return nil
			})
		}

		if err := eg.Wait(); err != nil {
			continue
		}
	}

	resp := &git_api.GetUserHasMRPermissionsResponse{}
	resp.Permissions = make([]*git_api.GetUserHasMRPermissionsResponseItem, 0)

	for _, v := range permissionsResps {
		resp.Permissions = append(resp.Permissions, v)
	}
	return httpR.StatusOK(resp)
}

func GetUserHasMergePermission(ctx *gin.Context) httpR.HttpRun {
	req := git_api.NewGetUserHasMergePermissionRequest()
	if err := ctx.ShouldBindQuery(req); err != nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrParams)
	}

	//
	permissionReq := &git_server.GetRepoPermissionsRequest{
		RepoId:   req.RepoId,
		Username: util.GetUserName(ctx),
	}

	logs.CtxInfo(ctx, "will call get repo permissions (%v)", utils.ToJson(permissionReq))
	permissionResp, err := rpc.GitClient.GetRepoPermissions(ctx, permissionReq)
	logs.CtxInfo(ctx, "did call get repo permissions (%v) (%v) (%v)", utils.ToJson(permissionReq), utils.ToJson(permissionResp), err)
	if err != nil || permissionResp == nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrForbidden)
	}

	/**
	{
		"hasPermission": false,
		"reasons": [{
			"type": "no_one_can_merge", // 谁都不能合入 解决办法: 提示目标分支谁都不能合入
			"namePattern": "te*",
		},{
			"type": "developer_can_merge", // developer 可以合入 解决办法: 申请 developer 权限
			"namePattern": "te*",
		},{
			"type": "master_can_merge", // master 可以合入 解决办法: 申请 master 权限
			"namePattern": "te*",
		},{
			"type": "users_and_groups_can_merge", // 指定的人可以合入 解决办法: 指定的人可以合入
			"namePattern": "te*",
			"users": ["zhangchunsheng"]
		},{
			"type": "you_are_reporter", // 用户是 reporter 解决办法: 申请 developer 权限
		},]
	}
	*/

	ret := &git_api.GetUserHasMergePermissionResponse{}
	ret.HasPermission = true
	ret.Reasons = make([]*git_api.UserMergePermissionReasons, 0)
	//
	if !gslice.Contains([]string{"owner", "master", "developer"}, permissionResp.Permission) {
		ret.HasPermission = false
		ret.Reasons = append(ret.Reasons, &git_api.UserMergePermissionReasons{
			Type: git_api.UserMergePermissionReasonTypeYouAreReporter,
		})
	}

	//
	branchReq := &git_server.MListProtectedBranchesRequest{
		RepoId:   req.RepoId,
		Branches: []string{req.TargetBranch},
	}
	logs.CtxInfo(ctx, "will call list protected branches (%v)", utils.ToJson(branchReq))
	branchResp, err := rpc.GitClient.MListProtectedBranches(ctx, branchReq)
	logs.CtxInfo(ctx, "did call list protected branches (%v) (%v) (%v)", utils.ToJson(branchReq), utils.ToJson(branchResp), err)
	if err != nil {
		if !ret.HasPermission {
			return httpR.StatusOK(ret)
		}
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrForbidden)
	}

	// 没有设置
	if branchResp == nil || len(branchResp.ProtectedBranches) <= 0 {
		logs.CtxInfo(ctx, "protected branches not set (%v)", utils.ToJson(req))
		return httpR.StatusOK(ret)
	}
	protectedBranchSettings, ok := branchResp.ProtectedBranches[req.TargetBranch]
	if !ok || len(protectedBranchSettings) <= 0 {
		logs.CtxInfo(ctx, "protected branches not set for branch (%v)", utils.ToJson(req))
		return httpR.StatusOK(ret)
	}

	// 1. 如果规则名和分支名完全一样，这个最优先
	// 2. 如果都是正则匹配，取最早创建的
	var protectedBranchSetting *git_server.ProtectedBranchSettings
	for _, val := range protectedBranchSettings {
		if val.NamePattern == req.TargetBranch {
			protectedBranchSetting = val
			break
		}
	}
	if protectedBranchSetting == nil {
		protectedBranchSetting = protectedBranchSettings[0]
	}

	// 谁都不能合入
	if len(protectedBranchSetting.MergeAccess) == 0 {
		logs.CtxInfo(ctx, "protected branch set no one (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
		ret.Reasons = append(ret.Reasons, &git_api.UserMergePermissionReasons{
			Type:        git_api.UserMergePermissionReasonTypeNoOneCanMerge,
			NamePattern: protectedBranchSetting.NamePattern,
		})
		return httpR.StatusOK(ret)
	}

	// 具有 Developer 角色或更高级别的用户
	if len(protectedBranchSetting.MergeAccess) == 1 &&
		sets.NewStringSetFromSlice(protectedBranchSetting.MergeAccess[0].RequiredPermissions).Equal(
			sets.NewStringSetFromSlice([]string{"ReadRepository", "WriteRepository"})) {

		if gslice.Contains([]string{"owner", "master", "developer"}, permissionResp.Permission) {
			return httpR.StatusOK(ret)
		} else {
			logs.CtxInfo(ctx, "protected branch set developer (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
			ret.Reasons = append(ret.Reasons, &git_api.UserMergePermissionReasons{
				Type:        git_api.UserMergePermissionReasonTypeDeveloperCanMerge,
				NamePattern: protectedBranchSetting.NamePattern,
			})
			return httpR.StatusOK(ret)
		}
	}

	// 具有 Master 角色或更高级别的用户
	if len(protectedBranchSetting.MergeAccess) == 1 &&
		sets.NewStringSetFromSlice(protectedBranchSetting.MergeAccess[0].RequiredPermissions).Equal(
			sets.NewStringSetFromSlice([]string{"ReadRepository", "WriteRepository", "ConfigureRepository"})) {

		if gslice.Contains([]string{"owner", "master"}, permissionResp.Permission) {
			return httpR.StatusOK(ret)
		} else {
			logs.CtxInfo(ctx, "protected branch set master (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
			ret.Reasons = append(ret.Reasons, &git_api.UserMergePermissionReasons{
				Type:        git_api.UserMergePermissionReasonTypeMasterCanMerge,
				NamePattern: protectedBranchSetting.NamePattern,
			})
			return httpR.StatusOK(ret)
		}
	}

	// 指定用户/用户组
	if len(protectedBranchSetting.MergeAccess) > 0 {
		matched := false
		users := make([]string, 0)
		for _, v := range protectedBranchSetting.MergeAccess {
			if v.UserType == "personal" {
				if v.User != nil && len(v.User.Username) > 0 && v.User.Username == util.GetUserName(ctx) {
					matched = true
				}
				if v.User != nil && len(v.User.Username) > 0 {
					users = append(users, v.User.Username)
				}
			}
		}
		if matched {
			return httpR.StatusOK(ret)
		} else {
			logs.CtxInfo(ctx, "protected branch set users and user group (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
			ret.Reasons = append(ret.Reasons, &git_api.UserMergePermissionReasons{
				Type:        git_api.UserMergePermissionReasonTypeUsersAndGroupsCanMergeCanMerge,
				NamePattern: protectedBranchSetting.NamePattern,
				Users:       users,
			})
			return httpR.StatusOK(ret)
		}
	}

	return httpR.StatusOK(ret)
}

func GetUserHasBranchPermission(ctx *gin.Context) httpR.HttpRun {
	req := git_api.NewGetUserHasBranchPermissionRequest()
	if err := ctx.ShouldBindQuery(req); err != nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrParams)
	}

	//
	permissionReq := &git_server.GetRepoPermissionsRequest{
		RepoId:   req.RepoId,
		Username: util.GetUserName(ctx),
	}

	logs.CtxInfo(ctx, "will call get repo permissions (%v)", utils.ToJson(permissionReq))
	permissionResp, err := rpc.GitClient.GetRepoPermissions(ctx, permissionReq)
	logs.CtxInfo(ctx, "did call get repo permissions (%v) (%v) (%v)", utils.ToJson(permissionReq), utils.ToJson(permissionResp), err)
	if err != nil || permissionResp == nil {
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrForbidden)
	}

	/**
	{
		"hasPermission": false,
		"reasons": [{
			"type": "no_one_can_create", // 谁都不能创建 解决办法: 提示分支谁都不能创建
			"namePattern": "te*",
		},{
			"type": "developer_can_create", // developer 可以创建 解决办法: 申请 developer 权限
			"namePattern": "te*",
		},{
			"type": "master_can_create", // master 可以创建 解决办法: 申请 master 权限
			"namePattern": "te*",
		},{
			"type": "users_and_groups_can_create", // 指定的人可以创建 解决办法: 指定的人可以创建
			"namePattern": "te*",
			"users": ["zhangchunsheng"]
		},{
			"type": "you_are_reporter", // 用户是 reporter 解决办法: 申请 developer 权限
		},]
	}
	*/

	ret := &git_api.GetUserHasBranchPermissionResponse{}
	ret.HasPermission = true
	ret.Reasons = make([]*git_api.UserBranchPermissionReasons, 0)
	//
	if !gslice.Contains([]string{"owner", "master", "developer"}, permissionResp.Permission) {
		ret.HasPermission = false
		ret.Reasons = append(ret.Reasons, &git_api.UserBranchPermissionReasons{
			Type: git_api.UserBranchPermissionReasonTypeYouAreReporter,
		})
	}

	//
	branchReq := &git_server.MListProtectedBranchesRequestV2{
		RepoId:   req.RepoId,
		Branches: []string{req.Branch},
	}
	logs.CtxInfo(ctx, "will call list protected branches v2 (%v)", utils.ToJson(branchReq))
	branchResp, err := rpc.GitClient.MListProtectedBranchesV2(ctx, branchReq)
	logs.CtxInfo(ctx, "did call list protected branches v2 (%v) (%v) (%v)", utils.ToJson(branchReq), utils.ToJson(branchResp), err)
	if err != nil {
		if !ret.HasPermission {
			return httpR.StatusOK(ret)
		}
		return util.UniformResponseWithError(ctx, bits_err.GITSERVER.ErrForbidden)
	}

	// 没有设置
	if branchResp == nil || len(branchResp.ProtectedBranches) <= 0 {
		logs.CtxInfo(ctx, "protected branches not set (%v)", utils.ToJson(req))
		return httpR.StatusOK(ret)
	}
	protectedBranchSettings, ok := branchResp.ProtectedBranches[req.Branch]
	if !ok || len(protectedBranchSettings) <= 0 {
		logs.CtxInfo(ctx, "protected branches not set for branch (%v)", utils.ToJson(req))
		return httpR.StatusOK(ret)
	}

	// 1. 如果规则名和分支名完全一样，这个最优先
	// 2. 如果都是正则匹配，取最早创建的
	var protectedBranchSetting *git_server.ProtectedBranchSettingsV2
	for _, val := range protectedBranchSettings {
		if val.NamePattern == req.Branch {
			protectedBranchSetting = val
			break
		}
	}
	if protectedBranchSetting == nil {
		protectedBranchSetting = protectedBranchSettings[0]
	}

	// 谁都不能创建
	if len(protectedBranchSetting.CreateAccess) == 0 {
		logs.CtxInfo(ctx, "protected branch set no one (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
		ret.HasPermission = false
		ret.Reasons = append(ret.Reasons, &git_api.UserBranchPermissionReasons{
			Type:        git_api.UserBranchPermissionReasonTypeNoOneCanCreate,
			NamePattern: protectedBranchSetting.NamePattern,
		})
		return httpR.StatusOK(ret)
	}

	// 具有 Developer 角色或更高级别的用户
	if len(protectedBranchSetting.CreateAccess) == 1 &&
		sets.NewStringSetFromSlice(protectedBranchSetting.CreateAccess[0].RequiredPermissions).Equal(
			sets.NewStringSetFromSlice([]string{"ReadRepository", "WriteRepository"})) {

		if gslice.Contains([]string{"owner", "master", "developer"}, permissionResp.Permission) {
			return httpR.StatusOK(ret)
		} else {
			logs.CtxInfo(ctx, "protected branch set developer (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
			ret.HasPermission = false
			ret.Reasons = append(ret.Reasons, &git_api.UserBranchPermissionReasons{
				Type:        git_api.UserBranchPermissionReasonTypeDeveloperCanCreate,
				NamePattern: protectedBranchSetting.NamePattern,
			})
			return httpR.StatusOK(ret)
		}
	}

	// 具有 Master 角色或更高级别的用户
	if len(protectedBranchSetting.CreateAccess) == 1 &&
		sets.NewStringSetFromSlice(protectedBranchSetting.CreateAccess[0].RequiredPermissions).Equal(
			sets.NewStringSetFromSlice([]string{"ReadRepository", "WriteRepository", "ConfigureRepository"})) {

		if gslice.Contains([]string{"owner", "master"}, permissionResp.Permission) {
			return httpR.StatusOK(ret)
		} else {
			logs.CtxInfo(ctx, "protected branch set master (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
			ret.HasPermission = false
			ret.Reasons = append(ret.Reasons, &git_api.UserBranchPermissionReasons{
				Type:        git_api.UserBranchPermissionReasonTypeMasterCanCreate,
				NamePattern: protectedBranchSetting.NamePattern,
			})
			return httpR.StatusOK(ret)
		}
	}

	// 指定用户/用户组
	if len(protectedBranchSetting.CreateAccess) > 0 {
		matched := false
		users := make([]string, 0)
		for _, v := range protectedBranchSetting.CreateAccess {
			if v.UserType == "personal" {
				if v.User != nil && len(v.User.Username) > 0 && v.User.Username == util.GetUserName(ctx) {
					matched = true
				}
				if v.User != nil && len(v.User.Username) > 0 {
					users = append(users, v.User.Username)
				}
			}
		}
		if matched {
			return httpR.StatusOK(ret)
		} else {
			logs.CtxInfo(ctx, "protected branch set users and user group (%v) (%v)", utils.ToJson(req), utils.ToJson(protectedBranchSetting))
			ret.HasPermission = false
			ret.Reasons = append(ret.Reasons, &git_api.UserBranchPermissionReasons{
				Type:        git_api.UserBranchPermissionReasonTypeUsersAndGroupsCanCreate,
				NamePattern: protectedBranchSetting.NamePattern,
				Users:       users,
			})
			return httpR.StatusOK(ret)
		}
	}

	return httpR.StatusOK(ret)
}
