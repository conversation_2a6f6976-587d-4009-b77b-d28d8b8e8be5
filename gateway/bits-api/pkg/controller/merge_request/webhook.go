package merge_request

import (
	"context"
	"sort"
	"strings"
	"sync"

	"code.byted.org/devinfra/hagrid/libs/bits_err"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

type webhookCategory struct {
	ID    int64  `json:"id"`
	Name  string `json:"name"`
	Title string `json:"title"`
}
type webhookEvent struct {
	ID          int64            `json:"id"`
	Name        string           `json:"name"`
	Title       string           `json:"title"`
	Params      string           `json:"params"`
	Description string           `json:"description"`
	Category    *webhookCategory `json:"category"`
}
type webhookUser struct {
	ZhName string `json:"zh_name"`
	EnName string `json:"en_name"`
	Avatar string `json:"avatar"`
}
type webhookSubRecord struct {
	ID        int64         `json:"id"`
	URL       string        `json:"url"`
	GroupName string        `json:"group_name"`
	Event     *webhookEvent `json:"event"`
	User      *webhookUser  `json:"user"`
}

func GetWebhookEvent(ctx *gin.Context) httpR.HttpRun {
	query := &struct {
		Page       int   `form:"page" binding:"required"`
		PageSize   int   `form:"page_size" binding:"required"`
		CategoryID int64 `form:"category_id"`
	}{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	data, err := rpc.OptimusClient.GetWebHookEvent(ctx, &optimus.GetWebHookEventsQuery{
		CategoryID: &query.CategoryID,
		Page:       int32(query.Page),
		PageSize:   int32(query.PageSize),
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}

	if data == nil || data.List == nil {
		return httpR.StatusOK(nil)
	}

	answer := make([]*webhookEvent, 0)
	for _, event := range data.List {
		info := &webhookEvent{
			ID:          event.ID,
			Name:        event.Name,
			Title:       event.Title,
			Description: event.Descriptions,
			Params:      event.Params,
		}
		if event.Category != nil {
			category := &webhookCategory{
				ID:    event.Category.ID,
				Name:  event.Category.Name,
				Title: event.Category.Title,
			}
			info.Category = category
		}
		answer = append(answer, info)
	}
	return httpR.StatusOK(answer)
}

func GetWebhookCategory(ctx *gin.Context) httpR.HttpRun {
	query := &struct {
		Page     int `form:"page" binding:"required"`
		PageSize int `form:"page_size" binding:"required"`
	}{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	data, err := rpc.OptimusClient.GetWebHookCategories(ctx, &optimus.GetWebHookCategoriesQuery{
		Page:     int32(query.Page),
		PageSize: int32(query.PageSize),
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}

	if data == nil || data.List == nil {
		return httpR.StatusOK(nil)
	}

	answer := make([]*webhookCategory, 0)
	for _, category := range data.List {
		answer = append(answer, &webhookCategory{
			ID:    category.ID,
			Name:  category.Name,
			Title: category.Title,
		})
	}

	return httpR.StatusOK(answer)
}

type checkWebhookURLQuery struct {
	URL string `form:"url" binding:"required"`
}

func CheckWebhookURL(ctx *gin.Context) httpR.HttpRun {
	query := &checkWebhookURLQuery{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	rpcQuery := optimus.NewCheckWebhookURLQuery()
	rpcQuery.URL = query.URL
	resp, err := rpc.OptimusClient.CheckWebhookURL(ctx, rpcQuery)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return util.UniformResponseWithBaseResp(ctx, resp.BaseResp, resp)
}

type hookRecords []*webhookSubRecord

func (s hookRecords) Len() int                  { return len(s) }
func (s hookRecords) Swap(i, j int)             { s[i], s[j] = s[j], s[i] }
func (s hookRecords) Less(i, j int) bool        { return s[i].ID > s[j].ID }
func (s hookRecords) List() []*webhookSubRecord { return s }

func GetWebhookSubRecord(ctx *gin.Context) httpR.HttpRun {
	query := &struct {
		Page       int    `form:"page" binding:"required"`
		PageSize   int    `form:"page_size" binding:"required"`
		GroupName  string `form:"group_name" binding:"required"`
		CategoryID int64  `form:"category_id"`
		EventID    int64  `form:"event_id"`
	}{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	params := &optimus.GetWebHookSubscribeRecordQuery{
		GroupName: query.GroupName,
		Page:      int32(query.Page),
		PageSize:  int32(query.PageSize),
	}
	if query.CategoryID != 0 {
		params.CategoryID = &query.CategoryID
	}
	if query.EventID != 0 {
		params.EventID = &query.EventID
	}

	data, err := rpc.OptimusClient.GetWebHookSubscribeRecord(ctx, params)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}

	if data == nil || data.List == nil {
		return httpR.StatusOK(nil)
	}

	var answer hookRecords
	wg := sync.WaitGroup{}
	for _, record := range data.List {
		info := &webhookSubRecord{
			ID:        record.ID,
			URL:       record.URL,
			GroupName: record.GroupName,
		}
		wg.Add(1)
		lock := sync.Mutex{}
		go func(ctx context.Context, record *optimus.WebHookSubscribeRecord, info *webhookSubRecord) {
			if user, err := rpc.OptimusClient.GetUserInfo(ctx, &optimus.GetUserInfoQuery{
				EnName: record.Author,
			}); user != nil && user.Info != nil {
				userInfo := user.Info
				info.User = &webhookUser{
					ZhName: userInfo.ZhName,
					Avatar: userInfo.Avatar,
				}
				if len(userInfo.EnName) != 0 {
					info.User.EnName = strings.Split(userInfo.EnName, "@")[0]
				}
			} else if err != nil {
				utils.LogCtxError(ctx, err.Error())
			}
			if record.Event != nil {
				info.Event = &webhookEvent{
					ID:          record.Event.ID,
					Name:        record.Event.Name,
					Title:       record.Event.Title,
					Description: record.Event.Descriptions,
					Params:      record.Event.Params,
				}
				// 查找对应的category
				if record.Event.Category != nil {
					info.Event.Category = &webhookCategory{
						ID:    record.Event.Category.ID,
						Name:  record.Event.Category.Name,
						Title: record.Event.Category.Title,
					}
				}
			}
			lock.Lock()
			answer = append(answer, info)
			defer func() {
				wg.Done()
				lock.Unlock()
			}()
		}(utils.CopyKiteXContext(ctx), record, info)
	}
	wg.Wait()
	sort.Stable(answer)
	return httpR.StatusOK(map[string]interface{}{
		"list":  answer,
		"total": data.Total,
	})
}
func DeleteWebhookSubRecord(ctx *gin.Context) httpR.HttpRun {
	query := struct {
		ID int64 `json:"id" binding:"required"`
	}{}

	if err := ctx.ShouldBind(&query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}

	resp, err := rpc.OptimusClient.RemoveWebHookSubscribeRecord(ctx, &optimus.RemoveWebHookSubscribeRecordQuery{
		RecordID:       query.ID,
		ActionUsername: util.GetUserName(ctx),
	})
	if err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}

	return util.UniformResponseWithBaseResp(ctx, resp.GetBaseResp(), nil)
}
func CreateWebhookSubRecord(ctx *gin.Context) httpR.HttpRun {
	params := struct {
		EventID   int64  `json:"event_id" binding:"required"`
		URL       string `json:"url" binding:"required"`
		GroupName string `json:"group_name" binding:"required"`
	}{}

	username := util.GetUserName(ctx)
	if err := ctx.ShouldBind(&params); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput.AddErrMsg(err.Error()))
	}
	data, err := rpc.OptimusClient.CreateWebHookSubscribeRecord(ctx, &optimus.CreateWebHookSubscribeRecordQuery{
		ActionUsername: username,
		GroupName:      params.GroupName,
		URL:            params.URL,
		EventID:        params.EventID,
	})

	if err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}

	return util.UniformResponseWithBaseResp(ctx, data.GetBaseResp(), data.InsertID)
}
