package merge_request

import (
	"strings"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/util"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"github.com/gin-gonic/gin"
	"github.com/hallokael/httpR"
)

/**
tag 的 color 常用的有
	blue red roseRed green purple darkYellow darkGreen gray

tag 相关的数据表
	dev_tags: 研发流程开发任务的 tag 绑定关系
	optimus_gitlab_mergerequest_tags: 客户端 mr 的 tag 绑定关系
	optimus_tags: tag 的 color, catalog, group, creator, en_name 等设置信息
*/

type createTagQuery struct {
	TagName          string `json:"tag_name" binding:"required"`
	Color            string `json:"color" binding:"required"`
	Catalog          string `json:"catalog" binding:"required"`
	GroupProjectName string `json:"group_project_name" binding:"required"`
}
type listTagQuery struct {
	GroupProjectName string  `form:"group_project_name"`
	Catalogs         *string `form:"catalogs"`
}

type tag struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Color            string `json:"color"`
	Catalog          string `json:"catalog"`
	GroupProjectName string `json:"group_project_name"`
	Creator          string `json:"creator"`
	Reason           string `json:"reason"`
	Removable        bool   `json:"removable"`
}

func CreateTag(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	query := createTagQuery{}
	if err := ctx.ShouldBind(&query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	res, err := rpc.OptimusClient.CreateMrTags(ctx, &optimus.CreateMrTagsQuery{
		Name:             query.TagName,
		Color:            query.Color,
		Catalog:          query.Catalog,
		GroupProjectName: query.GroupProjectName,
		Creator:          username,
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	resp := &tag{
		ID:               res.Id,
		Name:             res.Name,
		Color:            res.Color,
		Catalog:          res.Catalog,
		GroupProjectName: res.GroupProjectName,
		Creator:          res.Creator,
	}
	return util.UniformResponseWithBaseResp(ctx, res.BaseResp, resp)
}

func ListTag(ctx *gin.Context) httpR.HttpRun {
	userLocale := util.GetRequestLocale(ctx)
	query := listTagQuery{}
	if err := ctx.ShouldBind(&query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	getMrTagQuery := &optimus.ListMrTagQuery{
		GroupName: query.GroupProjectName,
	}
	if query.Catalogs != nil {
		getMrTagQuery.Catalogs = strings.Split(*query.Catalogs, ",")
	}
	res, err := rpc.OptimusClient.ListMrTag(ctx, getMrTagQuery)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	resp := make([]*tag, 0)
	for _, mrTag := range res.List {
		t := &tag{
			ID:               mrTag.Id,
			Name:             mrTag.Name,
			Color:            mrTag.Color,
			Catalog:          mrTag.Catalog,
			GroupProjectName: mrTag.GroupProjectName,
			Creator:          mrTag.Creator,
		}
		if userLocale == util.UserLocaleEn && len(mrTag.GetEnName()) > 0 {
			t.Name = mrTag.GetEnName()
		}
		resp = append(resp, t)
	}
	return util.UniformResponseWithBaseResp(ctx, res.BaseResp, resp)
}

type deleteTagQuery struct {
	ID               *int64  `json:"id"`
	Name             *string `json:"name"`
	GroupProjectName *string `json:"group_project_name"`
}

func DeleteTag(ctx *gin.Context) httpR.HttpRun {
	query := deleteTagQuery{}
	_ = ctx.ShouldBind(&query)
	if query.ID == nil && (query.Name == nil || query.GroupProjectName == nil) {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	req := optimus.DeleteMrTagsQuery{}
	if query.ID != nil {
		req.Id = query.ID
	} else {
		req.ProjectName = query.GroupProjectName
		req.Name = query.Name
	}
	res, err := rpc.OptimusClient.DeleteMrTags(ctx, &req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return util.UniformResponseWithBaseResp(ctx, res.BaseResp, "ok")
}

type getTagQuery struct {
	MrID      int64 `form:"mr_id"`
	DevID     int64 `form:"dev_id"`
	ProjectID int64 `form:"project_id"`
	IID       int64 `form:"iid"`
}

func GetItemTags(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	query := &getTagQuery{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	req := &optimus.GetMrInfoQuery{
		MrID:      query.MrID,
		ProjectID: &query.ProjectID,
		IID:       &query.IID,
		DevID:     &query.DevID,
	}
	res, err := rpc.OptimusClient.GetMrTags(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	resp := make([]*tag, 0)
	if len(res.List) == 0 {
		return util.UniformResponseWithBaseResp(ctx, res.BaseResp, resp)
	}
	for _, mrTag := range res.List {
		detail := &tag{
			ID:               mrTag.Id,
			Name:             mrTag.Name,
			Color:            mrTag.Color,
			Catalog:          mrTag.Catalog,
			GroupProjectName: mrTag.GroupProjectName,
			Creator:          mrTag.Creator,
			Removable:        false,
		}
		resp = append(resp, detail)
	}
	if query.DevID > 0 && query.MrID == 0 {
		DevBasic, err := rpc.DevClient.GetDevSmallMRDevBasicInfo(ctx, &dev.GetDevSmallMRDevBasicInfoRequest{DevID: query.DevID})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		for _, item := range resp {
			if username == DevBasic.Author && item.Catalog == "user" {
				item.Removable = true
			}
		}
	}
	if query.DevID == 0 {
		mr, err := rpc.OptimusClient.GetMainMrInfo(ctx, &optimus.GetMrMainInfoQuery{MrID: query.MrID})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		for _, item := range resp {
			if username == mr.Info.AuthorName && item.Catalog == "user" {
				item.Removable = true
			}
		}
	}

	return httpR.StatusOK(resp)
}

type bindTagQuery struct {
	MrId      int64  `json:"mr_id"`
	DevId     int64  `json:"dev_id"`
	Reason    string `json:"reason"`
	TagName   string `json:"tag_name"`
	GroupName string `json:"group_name"`
}

func BindTag(ctx *gin.Context) httpR.HttpRun {
	query := bindTagQuery{}
	if err := ctx.ShouldBind(&query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	req := &optimus.BindMrTagQuery{
		MrID:    query.MrId,
		TagName: query.TagName,
		Reason:  &query.Reason,
	}
	if query.DevId > 0 && query.MrId == 0 {
		devBasic, err := rpc.DevClient.GetDevSmallMRDevBasicInfo(ctx, &dev.GetDevSmallMRDevBasicInfoRequest{
			DevID: query.DevId,
		})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		req.GroupName = &devBasic.GroupName
		req.DevID = &query.DevId
	}
	res, err := rpc.OptimusClient.BindMrTag(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return util.UniformResponseWithBaseResp(ctx, res.BaseResp, "ok")
}

type batchBindTagQuery struct {
	MrId     int64    `json:"mr_id"`
	DevID    int64    `json:"dev_id"`
	Reason   string   `json:"reason"`
	TagNames []string `json:"tag_names"`
}

func BatchBindTag(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrPermissionDenied)
	}
	query := batchBindTagQuery{}
	if err := ctx.ShouldBind(&query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	groupName := ""
	if query.DevID > 0 && query.MrId == 0 {
		devBasic, err := rpc.DevClient.GetDevSmallMRDevBasicInfo(ctx, &dev.GetDevSmallMRDevBasicInfoRequest{
			DevID: query.DevID,
		})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		groupName = devBasic.GetGroupName()
	}

	for _, tagName := range query.TagNames {
		tagName := tagName
		req := &optimus.BindMrTagQuery{
			MrID:    query.MrId,
			TagName: tagName,
			Reason:  &query.Reason,
		}
		if query.DevID > 0 && query.MrId == 0 {
			req.DevID = &query.DevID
			req.GroupName = &groupName
		}
		res, err := rpc.OptimusClient.BindMrTag(ctx, req)
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		} else if res.GetBaseResp().StatusCode != 0 {
			return util.UniformResponseWithBaseResp(ctx, res.BaseResp, "ok")
		}
	}
	return util.UniformResponseWithBaseResp(ctx, nil, "ok")
}

func RemoveTagBinding(ctx *gin.Context) httpR.HttpRun {
	username := util.GetUserName(ctx)
	if len(username) == 0 {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrPermissionDenied)
	}
	query := bindTagQuery{}
	if err := ctx.ShouldBind(&query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	req := &optimus.BindMrTagQuery{
		MrID:    query.MrId,
		TagName: query.TagName,
	}
	if len(query.GroupName) > 0 {
		req.GroupName = &query.GroupName
	}
	if query.DevId > 0 && query.MrId == 0 {
		devBasic, err := rpc.DevClient.GetDevSmallMRDevBasicInfo(ctx, &dev.GetDevSmallMRDevBasicInfoRequest{
			DevID: query.DevId,
		})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		req.DevID = &query.DevId
		req.GroupName = &devBasic.GroupName
	}
	res, err := rpc.OptimusClient.RemoveMrTag(ctx, req)
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	return util.UniformResponseWithBaseResp(ctx, res.BaseResp, "ok")
}

type getMrInfoQuery struct {
	MrID      int64 `json:"mr_id"`
	DevID     int64 `json:"dev_id"`
	ProjectID int64 `json:"project_id"`
	IID       int64 `json:"iid"`
}

func GetTags(ctx *gin.Context) httpR.HttpRun {
	userLocale := util.GetRequestLocale(ctx)
	username := util.GetUserName(ctx)
	query := &getMrInfoQuery{}
	if err := ctx.ShouldBind(query); err != nil {
		return util.UniformResponseWithBitsErr(ctx, bits_err.COMMON.ErrInvalidInput)
	}
	res, err := rpc.OptimusClient.GetMrTags(ctx, &optimus.GetMrInfoQuery{
		MrID:      query.MrID,
		ProjectID: &query.ProjectID,
		IID:       &query.IID,
		DevID:     &query.DevID,
		Base:      nil,
	})
	if err != nil {
		return util.UniformResponseWithError(ctx, err)
	}
	ans := make([]*tag, 0)
	if len(res.List) == 0 {
		return util.UniformResponseWithBaseResp(ctx, res.BaseResp, ans)
	}
	for _, mrTag := range res.List {
		detail := &tag{
			ID:               mrTag.GetId(),
			Name:             mrTag.GetName(),
			Color:            mrTag.GetColor(),
			Catalog:          mrTag.GetCatalog(),
			GroupProjectName: mrTag.GetGroupProjectName(),
			Creator:          mrTag.GetCreator(),
			Reason:           mrTag.GetReason(),
			Removable:        false,
		}
		if userLocale == util.UserLocaleEn {
			detail.Name = mrTag.GetEnName()
		}
		ans = append(ans, detail)
	}

	if query.DevID == 0 {
		mr, err := rpc.OptimusClient.GetMainMrInfo(ctx, &optimus.GetMrMainInfoQuery{MrID: query.MrID})
		if err != nil {
			return util.UniformResponseWithError(ctx, err)
		}
		for _, item := range ans {
			if username == mr.Info.AuthorName && item.Catalog == "user" {
				item.Removable = true
			}
		}
	}

	return util.UniformResponseWithBaseResp(ctx, res.BaseResp, ans)
}
