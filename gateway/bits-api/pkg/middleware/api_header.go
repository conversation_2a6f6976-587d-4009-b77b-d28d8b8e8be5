package middleware

import (
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/kitex_gen/bytedance/bits/meta"
	api_consts "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/consts"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/rpc"
	"github.com/gin-gonic/gin"
	"code.byted.org/devinfra/hagrid/libs/common_lib/consts"
	"code.byted.org/devinfra/hagrid/libs/common_lib/model"
)

func HandleApiHeader(c *gin.Context) {
	header := model.ApiHeader{
		FromOneSite: c.GetHeader(api_consts.HeaderOneSite) == "1",
		MetaAppId:   conv.Int64Default(c.Request.Header.Get(api_consts.HeaderAuthAppId), 0),
	}

	// space_id
	if appId := header.MetaAppId; appId > 0 {
		resp, err := rpc.MetaClient.QueryAppSimpleInfoById(c, &meta.QueryAppSimpleInfoByIdRequest{AppId: appId})
		if err == nil && resp != nil && resp.GetAppInfo() != nil {
			header.SpaceId = resp.GetAppInfo().GetSpaceId()
		}
	}

	if header.MetaAppId > 0 {
		c.Set(consts.ContextKey_Headers, header)
	}
}
