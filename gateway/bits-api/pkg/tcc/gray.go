package tcc

import (
	"context"
	"math/rand"

	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"
)

func GetDiffCheckGraySpaces(ctx context.Context) ([]int64, error) {
	res, err := GrayClient.Get(ctx, "diff_check_gray")
	if err != nil {
		logs.CtxError(ctx, "failed to get tcc config:%s", err.Error())
		return nil, err
	}
	config := make([]int64, 0)
	err = json.UnmarshalString(res, &config)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshal tcc config:%s", err.Error())
		return nil, err
	}
	return config, nil
}

type NextCodeAPIGrayConfig struct {
	GrayRatio int `json:"gray_ratio"`
}

func GetNextCodeAPIGray(ctx context.Context, interfaceName string) bool {
	grayConfig := make(map[string]NextCodeAPIGrayConfig)
	res, err := GrayClient.Get(ctx, "next_code_gray_config")
	if err != nil {
		logs.CtxError(ctx, "failed to get tcc config:%s", err.Error())
		return false
	}
	err = json.UnmarshalString(res, &grayConfig)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshal tcc config:%s", err.Error())
		return false
	}

	interfaceGray, ok := grayConfig[interfaceName]
	if !ok {
		logs.CtxInfo(ctx, "fail to get interface gray config %v", interfaceName)
		return false
	}

	return rand.Intn(10) <= interfaceGray.GrayRatio
}
