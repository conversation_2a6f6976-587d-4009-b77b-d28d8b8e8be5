load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "urls",
    srcs = [
        "access.go",
        "admin.go",
        "ai.go",
        "appcenter.go",
        "approve.go",
        "atom_market.go",
        "bridge.go",
        "build_master.go",
        "calendar.go",
        "code_aosp.go",
        "code_bot.go",
        "code_check_mock.go",
        "code_frozen.go",
        "code_review.go",
        "component.go",
        "component_platform.go",
        "config_service.go",
        "cony.go",
        "cronjob.go",
        "dependency_parse.go",
        "deploy.go",
        "dev.go",
        "dev_task.go",
        "diff.go",
        "engine.go",
        "env.go",
        "feature.go",
        "gatekeeper.go",
        "gerrit.go",
        "gerrit_dev.go",
        "git_server.go",
        "help_center.go",
        "horizon.go",
        "hotfix.go",
        "iam.go",
        "init.go",
        "integratin_multi.go",
        "integration.go",
        "integration_v2.go",
        "integration_workflow.go",
        "lark.go",
        "merge_request.go",
        "message_center.go",
        "meta.go",
        "mini_app.go",
        "mr_package.go",
        "onesite_config.go",
        "onesite_feature_gate_config.go",
        "onesite_message_config.go",
        "package_analysis.go",
        "pipeline.go",
        "pipeline_template.go",
        "qa_review.go",
        "quality_management.go",
        "raw_json_render.go",
        "repo.go",
        "rule_manage.go",
        "security.go",
        "smr.go",
        "smr_v2.go",
        "tag.go",
        "testing.go",
        "timeline.go",
        "tools.go",
        "trains.go",
        "ttp.go",
        "urls_def.go",
        "webhook.go",
        "workflow.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/urls",
    visibility = ["//visibility:public"],
    deps = [
        "//gateway/bits-api/pkg/consts",
        "//gateway/bits-api/pkg/controller/access",
        "//gateway/bits-api/pkg/controller/admin",
        "//gateway/bits-api/pkg/controller/ai",
        "//gateway/bits-api/pkg/controller/app_center",
        "//gateway/bits-api/pkg/controller/approve",
        "//gateway/bits-api/pkg/controller/atom_market",
        "//gateway/bits-api/pkg/controller/bitsctl",
        "//gateway/bits-api/pkg/controller/bridge",
        "//gateway/bits-api/pkg/controller/build_master",
        "//gateway/bits-api/pkg/controller/calender",
        "//gateway/bits-api/pkg/controller/code_aosp/branch",
        "//gateway/bits-api/pkg/controller/code_aosp/build",
        "//gateway/bits-api/pkg/controller/code_aosp/gerrit",
        "//gateway/bits-api/pkg/controller/code_aosp/obs_task",
        "//gateway/bits-api/pkg/controller/code_aosp/repo_project",
        "//gateway/bits-api/pkg/controller/code_bot",
        "//gateway/bits-api/pkg/controller/code_bot/tools",
        "//gateway/bits-api/pkg/controller/code_frozen",
        "//gateway/bits-api/pkg/controller/code_review",
        "//gateway/bits-api/pkg/controller/component",
        "//gateway/bits-api/pkg/controller/component_platform",
        "//gateway/bits-api/pkg/controller/config_service",
        "//gateway/bits-api/pkg/controller/cony",
        "//gateway/bits-api/pkg/controller/cron_job",
        "//gateway/bits-api/pkg/controller/dependency_parse",
        "//gateway/bits-api/pkg/controller/deploy",
        "//gateway/bits-api/pkg/controller/dev",
        "//gateway/bits-api/pkg/controller/dev/small_mr_v2",
        "//gateway/bits-api/pkg/controller/dev_task",
        "//gateway/bits-api/pkg/controller/devops_settings",
        "//gateway/bits-api/pkg/controller/devtask",
        "//gateway/bits-api/pkg/controller/diff",
        "//gateway/bits-api/pkg/controller/engine",
        "//gateway/bits-api/pkg/controller/env",
        "//gateway/bits-api/pkg/controller/feature",
        "//gateway/bits-api/pkg/controller/gatekeeper",
        "//gateway/bits-api/pkg/controller/gerrit_dev",
        "//gateway/bits-api/pkg/controller/git",
        "//gateway/bits-api/pkg/controller/git_server",
        "//gateway/bits-api/pkg/controller/help_center",
        "//gateway/bits-api/pkg/controller/horizon",
        "//gateway/bits-api/pkg/controller/hotfix",
        "//gateway/bits-api/pkg/controller/iam",
        "//gateway/bits-api/pkg/controller/integration",
        "//gateway/bits-api/pkg/controller/integration/calender",
        "//gateway/bits-api/pkg/controller/integration/gray",
        "//gateway/bits-api/pkg/controller/integration/official",
        "//gateway/bits-api/pkg/controller/integration/version_close",
        "//gateway/bits-api/pkg/controller/integration_multi",
        "//gateway/bits-api/pkg/controller/integration_v2",
        "//gateway/bits-api/pkg/controller/integration_workflow",
        "//gateway/bits-api/pkg/controller/lark",
        "//gateway/bits-api/pkg/controller/merge_request",
        "//gateway/bits-api/pkg/controller/message_center",
        "//gateway/bits-api/pkg/controller/meta",
        "//gateway/bits-api/pkg/controller/mini_app",
        "//gateway/bits-api/pkg/controller/mr_package",
        "//gateway/bits-api/pkg/controller/onesite_config",
        "//gateway/bits-api/pkg/controller/package_analysis",
        "//gateway/bits-api/pkg/controller/pipeline",
        "//gateway/bits-api/pkg/controller/pipeline_template",
        "//gateway/bits-api/pkg/controller/qa_review",
        "//gateway/bits-api/pkg/controller/quality_management",
        "//gateway/bits-api/pkg/controller/repo",
        "//gateway/bits-api/pkg/controller/rule_manage",
        "//gateway/bits-api/pkg/controller/security",
        "//gateway/bits-api/pkg/controller/tag",
        "//gateway/bits-api/pkg/controller/testing",
        "//gateway/bits-api/pkg/controller/timeline",
        "//gateway/bits-api/pkg/controller/trains",
        "//gateway/bits-api/pkg/controller/ttp",
        "//gateway/bits-api/pkg/controller/workflow",
        "//gateway/bits-api/pkg/dal/kv",
        "//gateway/bits-api/pkg/middleware",
        "//gateway/bits-api/pkg/model",
        "//gateway/bits-api/pkg/tcc",
        "//gateway/bits-api/util",
        "//libs/auth/constdef",
        "//libs/common_lib/consts",
        "//libs/common_lib/model",
        "//libs/common_lib/utils",
        "//libs/middleware/ginexmw",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_hallokael_httpr//:httpR",
        "@org_byted_code_gin_ginex//:ginex",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_kv_redis_v6//pkg",
    ],
)
