package urls

import (
	"net/http"

	"code.byted.org/gopkg/facility/set"
)

var jsonContentType = set.NewStringSet("application/json; charset=utf-8")

type RawJsonRender struct {
	Data string
}

func (sp RawJsonRender) Render(w http.ResponseWriter) error {
	writeContentType(w, jsonContentType.ToList())
	w.Write([]byte(sp.Data))
	return nil
}

func (sp RawJsonRender) WriteContentType(w http.ResponseWriter) {
	writeContentType(w, jsonContentType.ToList())
}

func writeContentType(w http.ResponseWriter, value []string) {
	header := w.Header()
	if val := header["Content-Type"]; len(val) == 0 {
		header["Content-Type"] = value
	}
}
