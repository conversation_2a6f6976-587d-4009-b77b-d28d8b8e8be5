package urls

import (
	"net/http"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/approve"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
)

var ApproveUrls = model.GroupUrl{
	GroupName: "security",
	Prefix:    "/api/approve",
	Urls: []model.Url{
		{
			////Comment:     "审批列表页",
			Methods:     []string{http.MethodPost},
			Path:        "/list_tasks",
			HandlerFunc: approve.ListTasks,
		},
		{
			////Comment:     "前端获取流程图",
			Methods:     []string{http.MethodGet},
			Path:        "/get_flow_virtual_info",
			HandlerFunc: approve.OAGetFlowVirtualInfoForecast,
		},
		{
			////Comment:     "构建审批群",
			Methods:     []string{http.MethodPost},
			Path:        "/build_chat",
			HandlerFunc: approve.BuildChat,
		},
		{
			////Comment:     "获取审批基本信息",
			Methods:     []string{http.MethodPost},
			Path:        "/get_approve_info",
			HandlerFunc: approve.GetApproveInfo,
		},
		{
			////Comment:     "拉取审批流相关用户",
			Methods:     []string{http.MethodPost},
			Path:        "/list_related_users",
			HandlerFunc: approve.ListRelatedUsers,
		},
	},
}
