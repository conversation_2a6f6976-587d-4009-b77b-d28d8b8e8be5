package urls

import (
	"net/http"

	api_consts "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/consts"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/devtask"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
	"code.byted.org/devinfra/hagrid/libs/auth/constdef"
)

var DevTaskV2Urls = model.GroupUrl{
	GroupName: "Bits dev task",
	Prefix:    "/api/v1/dev/task",
	Urls: []model.Url{
		{
			//Comment:     "create dev task",
			Methods:     []string{http.MethodPost},
			Path:        "/create",
			HandlerFunc: devtask.Create,
		},
		{
			//Comment:     "check create dev task permission",
			Methods:     []string{http.MethodGet},
			Path:        "/create/permission",
			HandlerFunc: devtask.QueryCreateDevTaskPermission,
		},
		{
			//Comment:     "get basic info of dev task",
			Methods:     []string{http.MethodGet},
			Path:        "/basic",
			HandlerFunc: devtask.QueryBasic,
		},
		{
			//Comment:     "get related info of dev task",
			Methods:     []string{http.MethodGet},
			Path:        "/related",
			HandlerFunc: devtask.QueryRelatedInfo,
		},
		{
			//Comment:     "get change info of dev task",
			Methods:     []string{http.MethodGet},
			Path:        "/changes/content",
			HandlerFunc: devtask.QueryDevChangeInfo,
		},
		{
			//Comment:     "get change list of dev task",
			Methods:     []string{http.MethodGet},
			Path:        "/changes",
			HandlerFunc: devtask.GetChangeList,
		},
		{
			//Comment:     "close dev task",
			Methods:     []string{http.MethodPost},
			Path:        "/close",
			HandlerFunc: devtask.CloseDevTask,
		},
		{
			//Comment:     "bind work item",
			Methods:     []string{http.MethodPost},
			Path:        "/workitem/bind",
			HandlerFunc: devtask.BindWorkItems,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "unbind work item",
			Methods:     []string{http.MethodPost},
			Path:        "/workitem/unbind",
			HandlerFunc: devtask.UnBindWorkItems,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "bind integration",
			Methods:     []string{http.MethodPost},
			Path:        "/integration/bind",
			HandlerFunc: devtask.BindIntegration,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.BindReleaseTicket,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "unbind integration",
			Methods:     []string{http.MethodPost},
			Path:        "/integration/unbind",
			HandlerFunc: devtask.UnbindIntegration,
		},
		{
			//Comment:     "edit changes",
			Methods:     []string{http.MethodPost},
			Path:        "/changes/edit",
			HandlerFunc: devtask.UpdateDevChanges,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "check edit dev task permission",
			Methods:     []string{http.MethodGet},
			Path:        "/edit/permission",
			HandlerFunc: devtask.QueryEditDevTaskPermissionRequest,
		},
		{
			//Comment:     "query workflow",
			Methods:     []string{http.MethodGet},
			Path:        "/workflow",
			HandlerFunc: devtask.GetWorkflow,
		},
		{
			//Comment:     "query workflow task",
			Methods:     []string{http.MethodGet},
			Path:        "/workflow/task",
			HandlerFunc: devtask.GetWorkflowTask,
		},
		{
			//Comment:     "pass workflow stage",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/stage/pass",
			HandlerFunc: devtask.PassWorkflowStage, // 内部做鉴权
		},
		{
			//Comment:     "skip workflow stage",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/stage/skip",
			HandlerFunc: devtask.SkipWorkflowStage,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "rollback workflow stage",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/stage/rollback",
			HandlerFunc: devtask.RollbackWorkflowStage,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "skip workflow task",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/task/skip",
			HandlerFunc: devtask.SkipWorkflowTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "pass workflow task",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/task/pass",
			HandlerFunc: devtask.PassWorkflowTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.CompleteDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "check pass workflow task permission",
			Methods:     []string{http.MethodGet},
			Path:        "/workflow/task/pass/permission",
			HandlerFunc: devtask.QueryPassWorkflowTaskPermissionRequest,
		},
		{
			//Comment:     "retry workflow task",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/task/retry",
			HandlerFunc: devtask.RetryWorkflowTask,
		},
		{
			//Comment:     "get merging info",
			Methods:     []string{http.MethodGet},
			Path:        "/change/merging",
			HandlerFunc: devtask.GetMeringInfo,
		},
		{
			//Comment:     "update dev task basic info",
			Methods:     []string{http.MethodPost},
			Path:        "/basic/edit",
			HandlerFunc: devtask.UpdateDevTaskBasic,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "update dev task basic info",
			Methods:     []string{http.MethodPost},
			Path:        "/basic/partial-basic-info",
			HandlerFunc: devtask.UpdatePartialDevTaskBasicInfo,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "update dev task basic info",
			Methods:     []string{http.MethodGet},
			Path:        "/basic/partial-basic-info",
			HandlerFunc: devtask.GetPartialDevTaskBasicInfo,
		},
		{
			//Comment:     "get dev task list info",
			Methods:     []string{http.MethodPost},
			Path:        "/search",
			HandlerFunc: devtask.SearchDevTask,
		},
		{
			//Comment:     "get dev task list info",
			Methods:     []string{http.MethodPost},
			Path:        "/list_with_change",
			HandlerFunc: devtask.QueryDevTaskIdsWithChanges,
		},
		{
			//Comment:     "set auto merge",
			Methods:     []string{http.MethodPost},
			Path:        "/merge/auto/mark",
			HandlerFunc: devtask.SetDevTaskAutoMerge,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.MergeDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "check auto merge permission",
			Methods:     []string{http.MethodGet},
			Path:        "/merge/auto/permission",
			HandlerFunc: devtask.QueryMergePermission,
		},
		{
			//Comment:     "search branch with project",
			Methods:     []string{http.MethodGet},
			Path:        "/branches",
			HandlerFunc: devtask.SearchBranch,
		},
		{
			//Comment:     "search branch with project",
			Methods:     []string{http.MethodPost},
			Path:        "/branches",
			HandlerFunc: devtask.GetRepoBranches,
		},
		{
			//Comment:     "edit change merge config",
			Methods:     []string{http.MethodPost},
			Path:        "/change/config/edit",
			HandlerFunc: devtask.EditDevChange,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devChangeId",
					Permission: constdef.EditDevelopmentTask,
					IdGetter:   devtask.GetDevBasicIdByChangeId,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "start workflow task",
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/task/start",
			HandlerFunc: devtask.StartWorkflowTask,
		},
		{
			//Comment:     "check changes submittable",
			Methods:     []string{http.MethodGet},
			Path:        "/changes/submittable",
			HandlerFunc: devtask.CheckChangeSubmittable,
		},
		{
			//Comment:     "remove dev change",
			Methods:     []string{http.MethodPost},
			Path:        "/change/remove",
			HandlerFunc: devtask.RemoveDevChange,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "changeId",
					Permission: constdef.EditDevelopmentTask,
					IdGetter:   devtask.GetDevBasicIdByChangeId,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "get dev task vars",
			Methods:     []string{http.MethodGet},
			Path:        "/vars",
			HandlerFunc: devtask.GetDevTaskVars,
		},
		{
			//Comment:     "bridge models to exchange id",
			Methods:     []string{http.MethodGet},
			Path:        "/change/bridge",
			HandlerFunc: devtask.BridgeModels,
		},
		{
			//Comment:     "bind integration in batch",
			Methods:     []string{http.MethodPost},
			Path:        "/integration/batch/bind",
			HandlerFunc: devtask.BindIntegrationInBatch,
		},
		{
			//Comment:     "check bind integration in batch permission",
			Methods:     []string{http.MethodPost},
			Path:        "/integration/batch/bind/permission",
			HandlerFunc: devtask.QueryBindIntegrationPermissionInBatch,
		},
		{
			//Comment:     "cancel bc pipeline",
			Methods:     []string{http.MethodPost},
			Path:        "/pipeline/bc/cancel",
			HandlerFunc: devtask.CancelBcPipeline,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "run bc pipeline",
			Methods:     []string{http.MethodPost},
			Path:        "/pipeline/bc/run",
			HandlerFunc: devtask.RunBcPipeline,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "update bc pipeline",
			Methods:     []string{http.MethodPost},
			Path:        "/pipeline/bc/update",
			HandlerFunc: devtask.UpdateBcPipeline,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "query bc pipeline run vars",
			Methods:     []string{http.MethodPost},
			Path:        "/pipeline/bc/vars",
			HandlerFunc: devtask.QueryBcPipelineRunVars,
		},
		{
			//Comment:     "query bc pipeline change items",
			Methods:     []string{http.MethodPost},
			Path:        "pipeline/bc/changeItems",
			HandlerFunc: devtask.QueryBcPipelineChangeItems,
		},
		{
			//Comment:     "query bc pipeline to update",
			Methods:     []string{http.MethodPost},
			Path:        "/pipeline/bc/update/query",
			HandlerFunc: devtask.QueryBcPipelineToUpdate,
		},
		{
			//Comment:     "add dev changes",
			Methods:     []string{http.MethodPost},
			Path:        "/changes/add",
			HandlerFunc: devtask.AddDevChanges,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "get dev change merge config",
			Methods:     []string{http.MethodGet},
			Path:        "/change/merge/config",
			HandlerFunc: devtask.GetDevChangeMergeConfig,
		},
		{
			//Comment:     "update workItems",
			Methods:     []string{http.MethodPost},
			Path:        "/workitems/update",
			HandlerFunc: devtask.UpdateWorkItems,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "get gitlab authorize state",
			Methods:     []string{http.MethodGet},
			Path:        "/auth/gitlab/state",
			HandlerFunc: devtask.GetGitlabAuthorizeState,
		},
		{
			//Comment:     "update dev task vars",
			Methods:     []string{http.MethodPost},
			Path:        "/vars",
			HandlerFunc: devtask.UpdateDevTaskVars,
		},
		{
			//Comment:     "merge target of changes",
			Methods:     []string{http.MethodPost},
			Path:        "/changes/sync",
			HandlerFunc: devtask.MergeDevChangesTarget,
		},
		{
			//Comment:     "update env lane configs",
			Methods:     []string{http.MethodPost},
			Path:        "/env/lanes",
			HandlerFunc: devtask.UpdateEnvLaneConfigs,
		},
		{
			//Comment:     "update env project configs",
			Methods:     []string{http.MethodPost},
			Path:        "/env/projects",
			HandlerFunc: devtask.UpdateEnvProjectConfigs,
		},
		{
			//Comment:     "get env configs",
			Methods:     []string{http.MethodGet},
			Path:        "/env",
			HandlerFunc: devtask.GetDevTaskEnvConfig,
		},
		{
			//Comment:     "get change basic info, smr & dev task both used",
			Methods:     []string{http.MethodGet},
			Path:        "/change/basic",
			HandlerFunc: devtask.GetDevChangeBasicInfo,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/pipeline/bc/recreate",
			HandlerFunc: devtask.ReCreateBcPipeline,
		},
		{
			//Comment:     "get rerun pipeline changes",
			Methods:     []string{http.MethodGet},
			Path:        "/pipeline/bc/changes/rerun",
			HandlerFunc: devtask.GetRerunPipelineChange,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/pipeline/bc/history/task",
			HandlerFunc: devtask.GetTaskPipelineHistory,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/project/dependency",
			HandlerFunc: devtask.UpdateDevProjectDependency,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/related/tasks",
			HandlerFunc: devtask.GetRelatedDevTaskID,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/revert",
			HandlerFunc: devtask.CreateRevertDevTask,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/info/creation",
			HandlerFunc: devtask.GetCreateInfo,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/repo/operate/permission",
			HandlerFunc: devtask.QueryDevTaskRepoOperatePermission,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/checks/integration",
			HandlerFunc: devtask.CheckIntegration,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/checks/mr",
			HandlerFunc: devtask.CheckMrExisted,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/projects/default",
			HandlerFunc: devtask.GetDefaultProjects,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/updater",
			HandlerFunc: devtask.GetDevTaskUpdater,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/space/branch",
			HandlerFunc: devtask.GetSpaceBranch,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/changes/submit",
			HandlerFunc: devtask.SubmitDevChanges,
		}, {
			Methods:     []string{http.MethodGet},
			Path:        "/trigger",
			HandlerFunc: devtask.GetDevTaskTrigger,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/trigger",
			HandlerFunc: devtask.UpdateDevTaskTrigger,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/review/basic/batch/get",
			HandlerFunc: devtask.BatchGetDevTaskReviewBasicInfo,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/init",
			HandlerFunc: devtask.InitDevTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/init/progress",
			HandlerFunc: devtask.GetInitDevTaskRecords,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/config/draft",
			HandlerFunc: devtask.IsSpaceEnableDraft,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/draft",
			HandlerFunc: devtask.GetDevTaskDraft,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/draft",
			HandlerFunc: devtask.SetDevTaskDraft,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/split",
			HandlerFunc: devtask.SplitDevTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/remind/review",
			HandlerFunc: devtask.RemindDevTaskToReview,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicID",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			//Comment:     "update dev task lark group relation",
			Methods:     []string{http.MethodPost},
			Path:        "/lark_group/relation",
			HandlerFunc: devtask.UpdateDevTaskLarkGroupRelation,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/changes/merge",
			HandlerFunc: devtask.MergeChanges,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/checks/action",
			HandlerFunc: devtask.CheckUserAction,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/checks/action/batch",
			HandlerFunc: devtask.BatchCheckUserAction,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/choice/get",
			HandlerFunc: devtask.GetDefaultChoice,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/choice/set",
			HandlerFunc: devtask.SetDefaultChoice,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/flag/force/finish",
			HandlerFunc: devtask.SetForceFinishFlag,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/changes/code/reopen",
			HandlerFunc: devtask.ReopenCodeChanges,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/finish",
			HandlerFunc: devtask.FinishDevTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/projects/progress",
			HandlerFunc: devtask.GetControlPlaneProjectProgress,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/lock",
			HandlerFunc: devtask.LockDevTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/unlock",
			HandlerFunc: devtask.UnlockDevTask,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/basic/batch",
			HandlerFunc: devtask.BatchGetDevTaskBasicInfo,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/projects/apply/test",
			HandlerFunc: devtask.ApplyTestControlPlaneProjects,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/projects/merge",
			HandlerFunc: devtask.MergeControlPlaneProjects,
			Permission: model.UrlPermission{
				PermissionType: api_consts.PermissionTypeResource,
				PermissionRequired: &model.UrlResource{
					Resource:   constdef.ResourceDevTicket,
					IdName:     "devBasicId",
					Permission: constdef.EditDevelopmentTask,
				},
				Scope: model.PermissionScope_All,
			},
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/workflow/stage/batch/pass",
			HandlerFunc: devtask.PassWorkflowInBatch,
		},
	},
}
