package urls

import (
	"net/http"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/ai"

	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
	"code.byted.org/devinfra/hagrid/libs/common_lib/consts"
)

/*
	管理员后台操作相关接口
*/

var AIUrls = model.GroupUrl{
	GroupName: "ai",
	Prefix:    consts.URIPath_AI,
	Urls: []model.Url{
		{
			Methods:     []string{http.MethodPost},
			Path:        "/cli/diff/message/summary",
			HandlerFunc: ai.CreateCLIDiffMessageSummary,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/cli/diff/summary",
			HandlerFunc: ai.CreateCLIDiffSummary,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/change/diff/summary",
			HandlerFunc: ai.BuildChangeDiffSummary,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/change/diff/summary",
			HandlerFunc: ai.GetChangeDiffSummary,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/chat/conversation",
			HandlerFunc: ai.CreateConversation,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/chat/conversation/list",
			HandlerFunc: ai.GetConversationList,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/chat/history/list",
			HandlerFunc: ai.GetChatHistoryList,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/chat/message/like",
			HandlerFunc: ai.AddMessageLike,
		},
		{
			Methods:     []string{http.MethodDelete},
			Path:        "/chat/message/like",
			HandlerFunc: ai.RemoveMessageLike,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/chat/message/dislike",
			HandlerFunc: ai.AddMessageDislike,
		},
		{
			Methods:     []string{http.MethodDelete},
			Path:        "/chat/message/dislike",
			HandlerFunc: ai.RemoveMessageDislike,
		},
		{
			Methods:     []string{http.MethodGet},
			Path:        "/chat/env_name",
			HandlerFunc: ai.GetEnvName,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/chat/stop/message/send",
			HandlerFunc: ai.StopMessageSend,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/chat/tool_call/punish",
			HandlerFunc: ai.PunishToolCall,
		},
		{
			Methods:     []string{http.MethodPost},
			Path:        "/chat/todo_list/get",
			HandlerFunc: ai.GetTodoList,
		},
	},
}
