package urls

import (
	"net/http"

	api_consts "code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/consts"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/package_analysis"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
)

var PackageSizeUrls = model.GroupUrl{
	GroupName: "package size",
	Prefix:    "/api/package_size", // 非 api TLB不能映射
	Urls: []model.Url{
		/*
			包大小相关接口
		*/
		{
			//Comment:     "增加或者更新check",
			Methods:     []string{http.MethodPost},
			Path:        "/upsert_check",
			HandlerFunc: package_analysis.PackageSizeUpsertCheck,
			Permission:  model.UrlPermission{PermissionType: api_consts.PermissionTypeSettingPackageSize, AppIdFieldName: api_consts.AppIdFieldAppId},
		},
		{
			//Comment:     "获取check",
			Methods:     []string{http.MethodPost},
			Path:        "/get_check",
			HandlerFunc: package_analysis.PackageSizeGetCheck,
		},
		{
			//Comment: "获取检测报告",
			Methods: []string{http.MethodPost},
			Path:    "/get_report",
			//UseCache:     true,
			//CacheTimeOut: 48 * time.Hour,
			HandlerFunc: package_analysis.PackageSizeGetReport,
		},
		{
			//Comment:     "触发检测",
			Methods:     []string{http.MethodPost},
			Path:        "/execute",
			HandlerFunc: package_analysis.PackageSizeExecute,
		},
		{
			//Comment:     "包大小检测回调bits",
			Methods:     []string{http.MethodPost},
			Path:        "/report_callback",
			HandlerFunc: package_analysis.PackageSizeReportCallback,
		},
		{
			//Comment:     "提供给陈少铭-获取dev_task最新一次构建的包大小delta",
			Methods:     []string{http.MethodPost},
			Path:        "/get_dev_task_last_report",
			HandlerFunc: package_analysis.PackageSizeGetLastReportByDevTaskId,
		},
		{
			//Comment:     "上报jenkins地址的等信息",
			Methods:     []string{http.MethodPost},
			Path:        "/jenkins_info_callback",
			HandlerFunc: package_analysis.PackageSizeJenkinsInfoCallback,
		},
		{
			//Comment:     "包大小检测回调bits",
			Methods:     []string{http.MethodPost},
			Path:        "/report_callback_v2",
			HandlerFunc: package_analysis.ReportCallBackV2,
		},
		{
			//Comment:     "获取有app_id和对应的pipeline_ids",
			Methods:     []string{http.MethodPost},
			Path:        "/get_pipeline_info",
			HandlerFunc: package_analysis.GetPipelineInfo,
		},
		{
			//Comment:     "获取base和target包信息用于展示",
			Methods:     []string{http.MethodPost},
			Path:        "/get_package_size_info",
			HandlerFunc: package_analysis.GetPackageSizeInfo,
		},
		{
			//Comment:     "获取所有版本号",
			Methods:     []string{http.MethodPost},
			Path:        "/get_editions",
			HandlerFunc: package_analysis.GetEditions,
		},
		{
			//Comment:     "获取某个版本的包信息",
			Methods:     []string{http.MethodPost},
			Path:        "/get_edition_package",
			HandlerFunc: package_analysis.GetEditionPackageSize,
		},
		{
			//Comment:     "各业务线随版本的变化情况",
			Methods:     []string{http.MethodPost},
			Path:        "/get_edition_change",
			HandlerFunc: package_analysis.GetEditionChange,
		},
		{
			//Comment:     "各业务线随时间的变化情况",
			Methods:     []string{http.MethodPost},
			Path:        "/get_time_change",
			HandlerFunc: package_analysis.GetTimeChange,
		},
	},
}
