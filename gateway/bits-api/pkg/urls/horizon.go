/**
 * @Date: 2022/10/13
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package urls

import (
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/controller/horizon"
	"code.byted.org/devinfra/hagrid/gateway/bits-api/pkg/model"
	"net/http"
)

var HorizonUrl = model.GroupUrl{
	GroupName: "Horizon",
	Prefix:    "/api/horizon",
	Urls: []model.Url{
		{
			Methods:     []string{http.MethodPost},
			Path:        "/preview/lark_url",
			HandlerFunc: horizon.LarkUrlPreview,
			NoWrapper:   true,
			CustomResp:  true,
		},
	},
}
