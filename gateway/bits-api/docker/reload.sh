#!/bin/bash

export CONSUL_HTTP_HOST=************
export GIN_MODE=release
export RUNTIME_IDC_NAME=boe
export SEC_TOKEN_STRING=1
export is_bits_testing=1 #测试

pgrep dlv | awk '{print $1}' | xargs -I pid kill -9 pid
lsof -i:2345 | sed "1d" | awk '{print $2}' | xargs -I pid kill -9 pid
lsof -i:6789 | sed "1d" | awk '{print $2}' | xargs -I pid kill -9 pid

DEBUG_BIN=output/bits-api
go build -gcflags "all=-N -l" -o $DEBUG_BIN
nohup dlv --listen=:2345 --headless=true --api-version=2 --accept-multiclient exec "./$DEBUG_BIN" -- -port 6789 -conf-dir conf -log-dir log -psm bytedance.mobile.bits_api &

# 防止过早退出脚本导致命令执行失败
sleep 1