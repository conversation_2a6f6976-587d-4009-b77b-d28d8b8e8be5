package rpc

import (
	"time"

	"github.com/cloudwego/kitex/pkg/remote/codec/thrift"

	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/backends/cachyper"
	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/backends/gitserver"
	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/options"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/component_platform/server/historyservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/component_platform/server/reposervice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/devops/deploy/deploybridgeservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/devops/ttp/ttpservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/gatekeeper/process/gatekeeperservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/integration/multi/integrationservice"
	optimusCoreService "code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/optimus/core/optimusservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bits/optimus/infra/optimusinfraservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/code_bot/codebotservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/config_service/configservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/dependency_parse/dependencyparseservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/dev/devservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/feature/featureservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/git_server/gitservice"
	integration "code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/integration/integrationservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/integration_workflow/integrationworkflowservice"
	metaService "code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/meta/metaservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/mr_package/mrpackageservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/optimus/optimusservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/qa_review/qareviewservice"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/rd_process/tasks/tasksservice"
	yacrservice "code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/yacr/codereviewservice"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz/authzservice"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cdpb/cdrpc"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/identity/rpcpb/identityservice"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreopb/oreorpc"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb/spacerpcapi"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/translationpb/translationservice"
	"code.byted.org/devinfra/hagrid/pbgen/rpc/bits/authzrpc"
	kitexmw2 "code.byted.org/devinfra/hagrid/pkg/middlewares/kitexmw"
	"code.byted.org/kite/kitex/byted/transmeta"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/kite/kitex/transport"
	"code.byted.org/overpass/bytedance_bits_build_master/rpc/bytedance_bits_build_master"
	"code.byted.org/overpass/bytedance_bits_code_bot/rpc/bytedance_bits_code_bot"
	"code.byted.org/overpass/bytedance_bits_config_service/rpc/bytedance_bits_config_service"
	"code.byted.org/overpass/bytedance_bits_feature/rpc/bytedance_bits_feature"
	"code.byted.org/overpass/bytedance_bits_message_center/rpc/bytedance_bits_message_center"
	"code.byted.org/overpass/bytedance_bits_optimus/rpc/bytedance_bits_optimus"
	"code.byted.org/overpass/bytedance_bits_workflow/rpc/bytedance_bits_workflow"
	"code.byted.org/overpass/common/option/clientoption"
	"code.byted.org/overpass/ep_artifact_manager/kitex_gen/ep/artifact/manager/releaseservice"
	"code.byted.org/overpass/toutiao_passport_id_relation/kitex_gen/toutiao/passport/id_relation/idrelationservice"
)

var (
	Repo                      = reposervice.MustNewClient("bits.apps.component_server", options.NewRPCOptions()...)
	History                   = historyservice.MustNewClient("bits.apps.component_server", options.NewRPCOptions()...)
	TTPServer                 = ttpservice.MustNewClient("bits.devops.ttp", options.NewRPCOptions()...)
	DeployBridgeClient        = deploybridgeservice.MustNewClient("bits.devops.deploy", options.NewRPCOptions()...)
	GitServerApi              = gitserver.New()
	CachyperApi               = cachyper.New()
	GitServerRPCClient        = gitservice.MustNewClient("bytedance.bits.git_server", options.NewRPCOptions()...)
	OptimusClient             optimusservice.Client
	OptimusCoreClient         optimusCoreService.Client
	MetaClient                metaService.Client
	FeatureClient             featureservice.Client
	DevClient                 devservice.Client
	OptimusInfraClient        optimusinfraservice.Client
	QaReviewClient            qareviewservice.Client
	IntegrationClient         integration.Client
	IntegrationWorkflowClient integrationworkflowservice.Client
	ConfigServiceClient       configservice.Client
	DependencyParseClient     dependencyparseservice.Client
	CodeBotClient             codebotservice.Client
	BitsIdentityClient        identityservice.Client
	IdRelationClient          idrelationservice.Client
	ReleaseClient             releaseservice.Client
	YACRClient                yacrservice.Client
	BcReleaseClient           cdrpc.Client
	BcPipelineClient          oreorpc.Client
	IntegrationMultiClient    integrationservice.Client
	SpaceClient               spacerpcapi.Client
	CDRpcClient               cdrpc.Client
	ProcessClient             tasksservice.Client
	TranslationClient         translationservice.Client
	GateKeeperClient          gatekeeperservice.Client
	MrPackageClient           mrpackageservice.Client
	AuthzClient               authzservice.Client
)

func Init() {
	transmeta.SetReadBizStatusErr(true)

	bytedance_bits_feature.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(8 * time.Second),
	)
	bytedance_bits_workflow.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(30 * time.Second),
	)
	bytedance_bits_optimus.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(8 * time.Second),
	)
	bytedance_bits_config_service.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(8 * time.Second),
	)
	bytedance_bits_code_bot.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(8 * time.Second),
	)
	bytedance_bits_build_master.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(8*time.Second),
		clientoption.WithReqRespLogsInfo(),
	)
	bytedance_bits_message_center.InitDefaultClientOptions(
		clientoption.WithRPCTimeout(8 * time.Second),
	)
	OptimusClient = optimusservice.MustNewClient("bytedance.bits.optimus", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	OptimusCoreClient = optimusCoreService.MustNewClient("bits.optimus.core", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	MetaClient = metaService.MustNewClient("bytedance.bits.meta", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	FeatureClient = featureservice.MustNewClient("bytedance.bits.feature", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	DevClient = devservice.MustNewClient("bytedance.bits.dev", client.WithRPCTimeout(30*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	OptimusInfraClient = optimusinfraservice.MustNewClient("bits.optimus.infra", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	QaReviewClient = qareviewservice.MustNewClient("bytedance.bits.qa_review", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	IntegrationClient = integration.MustNewClient(
		"bytedance.bits.integration",
		// client.WithRPCTimeout(30*time.Second),
		client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)),
		client.WithTransportProtocol(transport.Framed),
	)
	IntegrationWorkflowClient = integrationworkflowservice.MustNewClient(
		"bytedance.bits.integration_workflow", client.WithRPCTimeout(8*time.Second),
		client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed),
	)
	ConfigServiceClient = configservice.MustNewClient(
		"bytedance.bits.config_service", client.WithRPCTimeout(8*time.Second),
		client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed),
	)
	DependencyParseClient = dependencyparseservice.MustNewClient("bytedance.bits.dependency_parse", client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	CodeBotClient = codebotservice.MustNewClient("bytedance.bits.code_bot", client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	BitsIdentityClient = identityservice.MustNewClient("bits.identity.rpc", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	IdRelationClient = idrelationservice.MustNewClient(
		"toutiao.passport.id_relation", client.WithConnectTimeout(20*time.Second),
		client.WithRPCTimeout(60*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed),
	)
	ReleaseClient = releaseservice.MustNewClient(
		"ep.artifact.manager", client.WithConnectTimeout(20*time.Second), client.WithRPCTimeout(60*time.Second),
	)
	YACRClient = yacrservice.MustNewClient("bytedance.bits.yacr", client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))

	BcReleaseClient = cdrpc.MustNewClient("bits.cd.rpc")

	IntegrationMultiClient = integrationservice.MustNewClient("bits.integration.multi", client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))

	BcPipelineClient = oreorpc.MustNewClient("bits.oreo.rpc")

	SpaceClient = spacerpcapi.MustNewClient("bits.space.rpc")

	CDRpcClient = cdrpc.MustNewClient("bits.cd.rpc")

	ProcessClient = tasksservice.MustNewClient("bytedance.bits.rd_process_tasks", client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	TranslationClient = translationservice.MustNewClient("bits.translation.rpc")
	GateKeeperClient = gatekeeperservice.MustNewClient("bits.gatekeeper.process", client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))
	MrPackageClient = mrpackageservice.MustNewClient("bytedance.bits.mr_package", client.WithRPCTimeout(8*time.Second), client.WithPayloadCodec(thrift.NewThriftCodecWithConfig(thrift.FrugalRead|thrift.FrugalWrite)), client.WithTransportProtocol(transport.Framed))

	kitexRpcOpts := []client.Option{
		client.WithMiddleware(kitexmw.LogClientSideRequestResponse),
	}
	AuthzClient = authzrpc.MustNewClient(append(kitexRpcOpts, client.WithMiddleware(kitexmw2.AuthzCheckPermissionMetricsMW))...)
}
