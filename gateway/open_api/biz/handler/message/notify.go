package message

import (
	"context"
	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/util"
	"code.byted.org/devinfra/hagrid/gateway/open_api/http"
	json "github.com/bytedance/sonic"

	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/overpass/bytedance_bits_message_center/kitex_gen/bytedance/bits/message_center"
	"code.byted.org/overpass/bytedance_bits_message_center/rpc/bytedance_bits_message_center"
)

type Notification struct {
	GroupName   string   ` json:"group_name"`
	Title       string   ` json:"title"`
	Detail      string   ` json:"detail"`
	TabType     []string ` json:"tab_type"`
	Status      int64    ` json:"status"`
	OfflineTime string   ` json:"offline_time"`
	ID          int64    ` json:"id"`
	MessageID   *string  ` json:"message_id"`
	OnlineTime  *string  ` json:"online_time"`
	Link        *string  ` json:"link"`
	SendTo      *string  ` json:"send_to"`
	CreateTime  string   ` json:"create_time"`
	UpdateTime  string   ` json:"update_time"`
	Operator    string   ` json:"operator"`
}

var defaultSendTo string = "{\"to_inboxes\":[{\"name\":\"all_members\",\"cate\":\"all_members\"}],\"to_devices\":[]}"

func convertNotificationToRequest(notify *Notification, userName string) *message_center.BitsNotification {
	notifyTabType, _ := json.Marshal(notify.TabType)

	answer := &message_center.BitsNotification{
		GroupName:   notify.GroupName,
		Title:       notify.Title,
		Detail:      notify.Detail,
		Status:      notify.Status,
		OfflineTime: notify.OfflineTime,
		ID:          notify.ID,
		MessageID:   notify.MessageID,
		OnlineTime:  notify.OnlineTime,
		Link:        notify.Link,
		SendTo:      &defaultSendTo,
		TabType:     string(notifyTabType),
		Operator:    userName,
	}
	return answer
}
func convertNotificationToResponse(notify *message_center.BitsNotification) *Notification {
	answer := &Notification{
		GroupName:   notify.GroupName,
		Title:       notify.Title,
		Detail:      notify.Detail,
		Status:      notify.Status,
		OfflineTime: notify.OfflineTime,
		ID:          notify.ID,
		MessageID:   notify.MessageID,
		OnlineTime:  notify.OnlineTime,
		Link:        notify.Link,
		SendTo:      notify.SendTo,
		Operator:    notify.Operator,
	}
	if notify.CreateTime != nil {
		answer.CreateTime = *notify.CreateTime
	}
	if notify.UpdateTime != nil {
		answer.UpdateTime = *notify.UpdateTime
	}
	tabType := make([]string, 0)
	_ = json.Unmarshal([]byte(notify.TabType), &tabType)
	answer.TabType = tabType
	return answer
}
func GetNotificationsByGroupName(c context.Context, ctx *app.RequestContext) {
	query := struct {
		GroupName string `query:"group_name" vd:"@:len($)>0"`
		Limit     *int64 `query:"limit"`
	}{}
	err := binding.BindAndValidate(ctx, &query)
	if err != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	req := &message_center.GetNotificationsByGroupNameQuery{
		GroupName: query.GroupName,
		Page:      1,
		PageSize:  1000,
	}
	if query.Limit != nil {
		if *query.Limit > 1000 {
			*query.Limit = 1000
		}
		req.PageSize = *query.Limit
	}
	resp, err := bytedance_bits_message_center.RawCall.GetNotificationsByGroupName(c, req)
	if err != nil {
		logs.CtxError(c, "GetNotificationsByGroupName false err: %v", err.Error())
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	answer := make([]*Notification, 0)
	for _, item := range resp.Notifications {
		answer = append(answer, convertNotificationToResponse(item))
	}
	http.ResponseOk(ctx, answer)
}
func GetOnlineNotificationLatest(c context.Context, ctx *app.RequestContext) {
	query := struct {
		GroupName string `query:"group_name" vd:"@:len($)>0"`
	}{}
	err := binding.BindAndValidate(ctx, &query)
	if err != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	req := &message_center.GetOnlineNotificationLatestByGroupNameQuery{
		GroupName: query.GroupName,
	}
	resp, err := bytedance_bits_message_center.RawCall.GetOnlineNotificationLatestByGroupName(c, req)
	if err != nil {
		logs.CtxError(c, "GetOnlineNotificationLatest false err: %v", err.Error())
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	if resp.Notification == nil || len(resp.Notification.GroupName) == 0 {
		http.ResponseOk(ctx, nil)
		return
	}
	http.ResponseOk(ctx, convertNotificationToResponse(resp.Notification))
}

func CreateBitsNotification(c context.Context, ctx *app.RequestContext) {
	query := struct {
		GroupName    string       `json:"group_name" vd:"@:len($)>0"`
		Notification Notification `json:"notification"`
	}{}
	err := binding.BindAndValidate(ctx, &query)
	if err != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	username := util.GetUserName(ctx)
	item := convertNotificationToRequest(&query.Notification, username)
	req := &message_center.CreateBitsNotificationQuery{
		GroupName:    query.GroupName,
		Notification: item,
	}
	_, err = bytedance_bits_message_center.RawCall.CreateBitsNotification(c, req)
	if err != nil {
		logs.CtxError(c, "CreateNotificationsBy false err: %v", err.Error())
		http.ResponseCustom(ctx, 500, err.Error(), nil)
		return
	}
	http.ResponseOk(ctx, "OK")
}
func SetNotificationOnline(c context.Context, ctx *app.RequestContext) {
	query := struct {
		ID        int64  `json:"id" vd:"@:$>0"`
		GroupName string `json:"group_name" vd:"@:len($)>0"`
	}{}
	err := binding.BindAndValidate(ctx, &query)
	if err != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	username := util.GetUserName(ctx)
	req := &message_center.SetNotificationOnlineQuery{
		ID:        query.ID,
		UserName:  username,
		GroupName: &query.GroupName,
	}
	_, err = bytedance_bits_message_center.RawCall.SetNotificationOnline(c, req)
	if err != nil {
		logs.CtxError(c, "SetNotificationOnline false err: %v", err.Error())
		http.ResponseCustom(ctx, 500, err.Error(), nil)
		return
	}
	http.ResponseOk(ctx, "OK")
}
func SetNotificationOffline(c context.Context, ctx *app.RequestContext) {
	query := struct {
		ID        int64  `json:"id" vd:"@:$>0"`
		GroupName string `json:"group_name" vd:"@:len($)>0"`
	}{}
	err := binding.BindAndValidate(ctx, &query)
	if err != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	username := util.GetUserName(ctx)
	req := &message_center.SetNotificationOfflineQuery{
		ID:        query.ID,
		UserName:  username,
		GroupName: &query.GroupName,
	}
	_, err = bytedance_bits_message_center.RawCall.SetNotificationOffline(c, req)
	if err != nil {
		logs.CtxError(c, "SetNotificationOffline false err: %v", err.Error())
		http.ResponseCustom(ctx, 500, err.Error(), nil)
		return
	}
	http.ResponseOk(ctx, "OK")
}
