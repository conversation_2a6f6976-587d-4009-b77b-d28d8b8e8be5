package user

import (
	"context"
	"fmt"
	optimusClient "code.byted.org/devinfra/hagrid/gateway/open_api/biz/service/optimus"
	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/util"
	"code.byted.org/devinfra/hagrid/gateway/open_api/http"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/overpass/bytedance_bits_meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/overpass/bytedance_bits_meta/rpc/bytedance_bits_meta"
)

func GetMemberInfo(c context.Context, ctx *app.RequestContext) {
	req := &struct {
		UserName string `query:"user_name" vd:"@:len($)>0"`
		AppId    int64  `query:"app_id" vd:"@:$>0"`
	}{}
	if binding.BindAndValidate(ctx, &req) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	request := &meta.GetSingleAppMemberInfoRequest{
		UserName: req.UserName,
		AppId:    req.AppId,
	}
	resp, err := bytedance_bits_meta.RawCall.GetSingleAppMemberInfo(c, request)
	if err != nil {
		logs.CtxError(c, "GetMemberInfo GetSingleAppMemberInfo failed err: %v", err.Error())
		http.ResponseCustom(ctx, 500, "get user info failed", nil)
		return
	}
	if resp == nil {
		http.ResponseCustom(ctx, 400, "get user info null", nil)
		return
	}
	http.ResponseOk(ctx, resp)
}
func JoinUserGroup(c context.Context, ctx *app.RequestContext) {
	req := &struct {
		Username      string `json:"username" vd:"@:len($)>0"`
		GroupName     string `json:"group_name" vd:"@:len($)>0"`
		UserGroupName string `json:"user_group_name" vd:"@:len($)>0"`
	}{}

	if binding.BindAndValidate(ctx, &req) != nil {
		http.ResponseCustom(ctx, 400, "invalid params", nil)
		return
	}
	params := map[string]interface{}{
		"username":      req.Username,
		"project_group": req.GroupName,
		"group_name":    req.UserGroupName,
	}
	username := util.GetUserNameFromToken(ctx)
	path := fmt.Sprintf("/api/v1/users/group/relation/reset")
	client := &optimusClient.OptimusClient{Path: path}
	_, err := client.DoPost(c, username, params, 1, time.Second*60*5)
	if err != nil {
		logs.CtxError(c, "join user group failed params=%v, error=%v", params, err.Error())
		http.ResponseCustom(ctx, 500, err.Error(), nil)
		return
	}
	http.ResponseOk(ctx, "OK")
}
