package workflow

import (
	"code.byted.org/devinfra/hagrid/gateway/open_api/http"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/overpass/bits_devops_pipeline_template/kitex_gen/bits/devops/pipeline_template"
	"code.byted.org/overpass/bits_devops_pipeline_template/rpc/bits_devops_pipeline_template"
	"code.byted.org/overpass/bytedance_bits_workflow/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/overpass/bytedance_bits_workflow/rpc/bytedance_bits_workflow"
	"context"
)

// 获取 pipeline
type getPipelineQuery struct {
	PipelineId int64 `query:"pipelineId,required" vd:"@:$>0"`
}

func GetPipelineDetail(c context.Context, ctx *app.RequestContext) {
	param := getPipelineQuery{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	resp, err := bytedance_bits_workflow.GetPipeline(c, param.PipelineId)
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp.Pipeline)
}

// TriggerTemplatePipelineRequest 触发模板请求
type TriggerTemplatePipelineRequest struct {
	TemplateId    int64             `json:"templateId"`
	TriggerType   string            `json:"triggerType"`
	Operator      string            `json:"operator"`
	ProjectId     int64             `json:"projectId"`
	ProjectURL    string            `json:"projectURL"`
	ProjectBranch string            `json:"projectBranch"`
	ProjectCommit *string           `json:"projectCommit,omitempty"`
	Env           map[string]string `json:"env,omitempty"`
	PipelineName  *string           `json:"pipelineName,omitempty"`
	AppId         int64             `json:"appId"`
}

// TriggerTemplatePipeline 触发模板pipeline
func TriggerTemplatePipeline(c context.Context, ctx *app.RequestContext) {
	param := TriggerTemplatePipelineRequest{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	info := &workflow.TriggerTemplatePipelineInfo{
		TemplateId:    param.TemplateId,
		TriggerType:   param.TriggerType,
		Operator:      param.Operator,
		ProjectId:     param.ProjectId,
		ProjectURL:    param.ProjectURL,
		ProjectBranch: param.ProjectBranch,
		ProjectCommit: param.ProjectCommit,
		Env:           param.Env,
		PipelineName:  param.PipelineName,
		AppId:         param.AppId,
	}

	if param.ProjectCommit != nil {
		info.ProjectCommit = param.ProjectCommit
	}

	if param.Env != nil {
		info.Env = param.Env
	}

	if param.PipelineName != nil {
		info.PipelineName = param.PipelineName
	}

	resp, err := bytedance_bits_workflow.TriggerTemplatePipeline(c, info)
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp.Pipeline)
}

func CancelPipeline(c context.Context, ctx *app.RequestContext) {
	param := workflow.CancelPipelineRequest{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	resp, err := bytedance_bits_workflow.CancelPipeline(c, param.PipelineId)
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp.Pipeline)
}

func RetryPipeline(c context.Context, ctx *app.RequestContext) {
	param := workflow.RetryPipelineRequest{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	resp, err := bytedance_bits_workflow.RetryPipeline(c, param.PipelineId)
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp.Pipeline)
}

type triggerJobReq struct {
	workflow.TriggerJobInfo
	JobSettings interface{} `json:"jobSettings,omitempty"`
	Params      interface{} `json:"params,omitempty"`
}

type triggerFreePipelineReq struct {
	PipelineInfo *workflow.TriggerPipelineInfo `json:"pipelineInfo"`
	JobInfos     []*triggerJobReq              `json:"jobInfos"`
}

func TriggerFreePipeline(c context.Context, ctx *app.RequestContext) {
	param := triggerFreePipelineReq{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	jobInfos := make([]*workflow.TriggerJobInfo, 0)
	for idx := range param.JobInfos {
		info := param.JobInfos[idx]
		if info.JobSettings != nil {
			jobSettings := utils.ToJson(info.JobSettings)
			info.TriggerJobInfo.JobSettings = &jobSettings
		}
		if info.Params != nil {
			params := utils.ToJson(info.Params)
			info.TriggerJobInfo.Params = &params
		}
		jobInfos = append(jobInfos, &info.TriggerJobInfo)
	}

	resp, err := bytedance_bits_workflow.RawCall.TriggerFreePipeline(c, &workflow.TriggerFreePipelineRequest{
		PipelineInfo: param.PipelineInfo,
		JobInfos:     jobInfos,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp.Pipeline)
}

func TriggerPipelineTemplate(c context.Context, ctx *app.RequestContext) {
	param := pipeline_template.TriggerTemplateRequest{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	resp, err := bits_devops_pipeline_template.RawCall.TriggerTemplate(c, &param)
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp)
}

func TriggerPipeline(c context.Context, ctx *app.RequestContext) {
	param := workflow.TriggerPipelineInfo{}
	if binding.BindAndValidate(ctx, &param) != nil {
		http.ResponseCustom(ctx, 400, "params error", nil)
		return
	}
	resp, err := bytedance_bits_workflow.TriggerPipeline(c, &param)
	if err != nil {
		http.ResponseInternalServerError(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp.Pipeline)
}
