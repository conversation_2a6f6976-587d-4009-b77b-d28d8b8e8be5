load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "pipeline",
    srcs = [
        "request.go",
        "response.go",
        "router.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/gateway/open_api/biz/handler/pipeline",
    visibility = ["//visibility:public"],
    deps = [
        "//gateway/open_api/biz/util",
        "@org_byted_code_middleware_hertz//pkg/app",
        "@org_byted_code_middleware_hertz_ext_v2//binding",
        "@org_byted_code_overpass_bits_devops_pipeline_server//rpc/bits_devops_pipeline_server",
    ],
)
