package rule_manager

import (
	"code.byted.org/devinfra/hagrid/gateway/open_api/http"
	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/overpass/bytedance_bits_rule_management/kitex_gen/bytedance/bits/rule_management"
	"code.byted.org/overpass/bytedance_bits_rule_management/rpc/bytedance_bits_rule_management"
	"context"
)

func ListAllBpccRule(c context.Context, ctx *app.RequestContext) {
	request := rule_management.NewListAllBpccRuleRequest()
	if err := binding.BindAndValidate(ctx, request); err != nil {
		http.ResponseBadRequest(ctx, err.Error())
		return
	}
	resp, err := bytedance_bits_rule_management.RawCall.ListAllBpccRule(c, request)
	if err != nil {
		logs.CtxError(c, err.Error())
		http.ResponseBadRequest(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp)
	return
}

func ListEngineRule(c context.Context, ctx *app.RequestContext) {
	request := rule_management.NewEngineRuleListRequest()
	if err := binding.BindAndValidate(ctx, request); err != nil {
		http.ResponseBadRequest(ctx, err.Error())
		return
	}
	resp, err := bytedance_bits_rule_management.RawCall.EngineRuleList(c, request)
	if err != nil {
		logs.CtxError(c, err.Error())
		http.ResponseBadRequest(ctx, err)
		return
	}
	http.ResponseOk(ctx, resp)
	return
}
