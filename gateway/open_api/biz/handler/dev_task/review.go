package dev_task

import (
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"context"
	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/util"
	"code.byted.org/devinfra/hagrid/gateway/open_api/http"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/yacr"
)

type addReviewerItem struct {
	Name   string `json:"name" vd:"@:len($)>0"`
	Reason string `json:"reason"`
}

type addReviewerReq struct {
	ChangeID  int64             `json:"change_id" vd:"@:$>0"`
	Reviewers []addReviewerItem `json:"reviewers"  vd:"@:len($)>0"`
	CanRemove bool              `json:"can_remove"`
}

func AddReviewer(c context.Context, ctx *app.RequestContext) {
	req := addReviewerReq{}
	if binding.BindAndValidate(ctx, &req) != nil {
		http.ResponseCustom(ctx, 400, "invalid params", nil)
		return
	}
	username := util.GetUserNameFromToken(ctx)
	if len(username) == 0 {
		http.ResponseNotAuthorizedRequest(ctx, "no username")
		return
	}
	bridgeResp, err := rpc.DevClient.BridgeBetweenModels(c, &dev.BridgeBetweenModelsRequest{
		ChangeId: &req.ChangeID,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	ctbResp, err := rpc.DevClient.GetContributionCodeChange(c, &dev.GetContributionCodeChangeRequest{
		ContributionID: &bridgeResp.ContributionId,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	_, err = rpc.YACRClient.AddReviewersByOpenAPI(c, &yacr.AddReviewersByOpenAPIReq{
		DevId: ctbResp.Contribution.DevID,
		Reviewers: gslice.Map(req.Reviewers, func(r addReviewerItem) *yacr.OpenAPIApprover {
			return &yacr.OpenAPIApprover{
				Name:     r.Name,
				Reason:   &r.Reason,
				Operator: username,
			}
		}),
		CanRemove: req.CanRemove,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	http.ResponseOk(ctx, "ok")
}

type ReviewerStatusInfo struct {
	Username string `json:"username"`
	Status   string `json:"status"`
	Message  string `json:"message"`
}

func GetReviewersStatus(c context.Context, ctx *app.RequestContext) {
	req := struct {
		ChangeID int64 `query:"change_id" vd:"@:$>0"`
	}{}
	if binding.BindAndValidate(ctx, &req) != nil {
		http.ResponseCustom(ctx, 400, "invalid params", nil)
		return
	}
	bridgeResp, err := rpc.DevClient.BridgeBetweenModels(c, &dev.BridgeBetweenModelsRequest{
		ChangeId: &req.ChangeID,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	ctbResp, err := rpc.DevClient.GetContributionCodeChange(c, &dev.GetContributionCodeChangeRequest{
		ContributionID: &bridgeResp.ContributionId,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	reviewResp, err := rpc.YACRClient.GetReviewApproveStatus(c, &yacr.GetReviewApproveStatusReq{
		DevId: ctbResp.Contribution.DevID,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	answer := make([]*ReviewerStatusInfo, 0)
	if len(reviewResp.ApproveStatus) > 0 {
		answer = adaptReviewersApprovalInfoToResp(reviewResp.ApproveStatus)
	}
	http.ResponseOk(ctx, answer)
}
