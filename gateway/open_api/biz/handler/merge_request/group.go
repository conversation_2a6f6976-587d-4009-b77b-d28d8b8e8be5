package merge_request

import (
	"context"

	"code.byted.org/devinfra/hagrid/gateway/open_api/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/gateway/open_api/http"
	"code.byted.org/devinfra/hagrid/gateway/open_api/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/overpass/bytedance_bits_optimus/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/overpass/bytedance_bits_optimus/rpc/bytedance_bits_optimus"
	json "github.com/bytedance/sonic"
)

type getRepoConfigByRepoNameQuery struct {
	GroupName string `query:"group_name"  vd:"@:len($)>0"`
	Flag      int16  `query:"flag"`
}

type pipelineTriggerTimingInfo struct {
	Commit bool `json:"commit"`
	Review bool `json:"review"`
	NewMr  bool `json:"new_mr"`
	Wip    bool `json:"wip"`
}
type jenkinsInfo struct {
	HostUrl    string `json:"host_url"`
	Job        string `json:"job"`
	AdminName  string `json:"admin_name"`
	AdminToken string `json:"admin_token"`
}
type projectReposConfig struct {
	ID                       int64                      `json:"id"`
	ProjectID                int64                      `json:"project_id"`
	CodebaseRepoID           int64                      `json:"codebase_repo_id"`
	JobAnalysisEnable        bool                       `json:"job_analysis_enable"`
	PipelineToken            string                     `json:"pipeline_token"`
	SkipPipeline             bool                       `json:"skip_pipeline"`
	CustomizedReview         map[string]interface{}     `json:"customized_review"`
	Reviewers                []string                   `json:"reviewers"`
	AutoPublish              bool                       `json:"auto_publish"`
	IsBizPod                 bool                       `json:"is_biz_pod"`
	SourceUrl                string                     `json:"source_url"`
	ReviewerNumber           int64                      `json:"reviewer_number"`
	VersionBranchPattern     string                     `json:"version_branch_pattern"`
	PodspecName              string                     `json:"podspec_name"`
	AutoDeleteMergedBranches bool                       `json:"auto_delete_merged_branches"`
	AutoDeleteOldBranches    bool                       `json:"auto_delete_old_branches"`
	Podfile                  string                     `json:"podfile"`
	PipelineForBranch        []interface{}              `json:"pipeline_for_branch"`
	CustomCiVars             map[string]interface{}     `json:"custom_ci_vars"`
	PipelineTriggerTiming    *pipelineTriggerTimingInfo `json:"pipeline_trigger_timing"`
	SkipPublicCommentsRegex  string                     `json:"skip_public_comments_regex"`
	BinaryType               int16                      `json:"binary_type"`
	Jenkins                  *jenkinsInfo               `json:"jenkins"`
	PipelineType             int16                      `json:"pipeline_type"`
	AutoPublishType          int16                      `json:"auto_publish_type"`
	FileReviewerMap          map[string]interface{}     `json:"file_reviewer_map"`
	AppType                  int16                      `json:"app_type"`
	DependencyLock           string                     `json:"dependency_lock"`
	IntegrationType          int16                      `json:"integration_type"`
	Name                     string                     `json:"project_show_name"`
	GreyBranchPattern        string                     `json:"grey_branch_pattern"`
	CreateMrGroup            bool                       `json:"create_mr_group"`
	RockBinarySource         map[string]interface{}     `json:"rock_binary_source"`
	MrControlUrl             string                     `json:"mr_control_url"`
	DisableOutdatedMerge     bool                       `json:"disable_outdated_merge"`
	MonitoredBranchPattern   string                     `json:"monitored_branch_pattern"`
	ConySettings             map[string]interface{}     `json:"cony_settings"`
	GeckoPath                string                     `json:"gecko_path"`
	GeckoAppProjects         map[string]interface{}     `json:"gecko_app_projects"`
	ScmID                    int64                      `json:"scm_id"`
	XcodeProjectPath         string                     `json:"xcode_project_path"`
	IsActived                bool                       `json:"is_actived"`
	EnableTranslationCheck   bool                       `json:"enable_translation_check"`
	ConflictDetectLevel      string                     `json:"conflict_detect_level"`
	IntegrationFilePath      string                     `json:"integration_file_path"`
	AfterCheckEnable         bool                       `json:"after_check_enable"`
	PipelineTemplateID       int64                      `json:"pipeline_template_id"`
	AfterCheckType           string                     `json:"after_check_type"`
	RcBranchSettings         map[string]interface{}     `json:"rc_branch_settings"`
	CustomAfterCheckUrl      string                     `json:"custom_after_check_url"`
	BuildPackageTemplateID   int64                      `json:"build_package_template_id"`
	FlutterModule            bool                       `json:"flutter_module"`
	MultiComponent           bool                       `json:"multi_component"`
	PatchTemplateID          int64                      `json:"patch_template_id"`
	AutoUpdateSeer           bool                       `json:"auto_update_seer"`
	PodChangeValidateEnable  bool                       `json:"pod_change_validate_enable"`
	GitRepoAddr              string                     `json:"git_url"`
	GitlabGroup              string                     `json:"gitlab_group"`
	MainGroup                string                     `json:"main_group"`
	IsRootProject            bool                       `json:"root_project"`
	ProjectName              string                     `json:"project_name"`
	CustomComponentVersion   bool                       `json:"custom_component_version"`
	PodSeerEnable            int16                      `json:"pod_seer_enable"`
	PodChangePipelineEnable  bool                       `json:"pod_change_pipeline_enable"`
	BitsPipelineConfigId     int64                      `json:"bits_pipeline_config_id"`
}

func GetGroupProjects(c context.Context, ctx *app.RequestContext) {
	query := &getRepoConfigByRepoNameQuery{}
	if err := binding.BindAndValidate(ctx, query); err != nil {
		http.ResponseCustom(ctx, 400, "params error", err.Error())
		return
	}
	configs, err := bytedance_bits_optimus.RawCall.GetConfigsByProjectName(c, &optimus.GetConfigsByProjectNameQuery{
		GroupName: query.GroupName,
		Flag:      query.Flag,
		Base:      nil,
	})
	if err != nil {
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	answer := make(map[int64]*projectReposConfig, 0)
	if configs == nil || configs.List == nil {
		http.ResponseOk(ctx, answer)
		return
	}
	answer = convertProjectListToResponse(ctx, configs)
	http.ResponseOk(ctx, answer)
}

func convertProjectListToResponse(ctx *app.RequestContext, configs *optimus.GetConfigsByProjectNameResponse) map[int64]*projectReposConfig {
	answer := make(map[int64]*projectReposConfig, 0)
	for _, item := range configs.List {
		customizedReview := make(map[string]interface{}, 0)
		reviewers := make([]string, 0)
		pipelineForBranch := make([]interface{}, 0)
		customCiVars := make(map[string]interface{}, 0)
		pipelineTriggerTiming := &pipelineTriggerTimingInfo{}
		jenkins := &jenkinsInfo{}
		fileReviewerMap := make(map[string]interface{}, 0)
		rockBinarySource := make(map[string]interface{}, 0)
		conySettings := make(map[string]interface{}, 0)
		geckoProjects := make(map[string]interface{}, 0)
		rcBranchSettings := make(map[string]interface{}, 0)
		_ = json.Unmarshal([]byte(item.CustomizedReview), &customizedReview)
		_ = json.Unmarshal([]byte(item.Reviewers), &reviewers)
		_ = json.Unmarshal([]byte(item.PipelineForBranch), &pipelineForBranch)
		_ = json.Unmarshal([]byte(item.CustomCiVars), &customCiVars)
		_ = json.Unmarshal([]byte(item.PipelineTriggerTiming), pipelineTriggerTiming)
		_ = json.Unmarshal([]byte(item.Jenkins), jenkins)
		_ = json.Unmarshal([]byte(item.FileReviewerMap), &fileReviewerMap)
		_ = json.Unmarshal([]byte(item.RockBinarySource), &rockBinarySource)
		_ = json.Unmarshal([]byte(item.ConySettings), &conySettings)
		_ = json.Unmarshal([]byte(item.GeckoAppProjects), &geckoProjects)
		_ = json.Unmarshal([]byte(item.RcBranchSettings), &rcBranchSettings)
		answer[item.ProjectID] = &projectReposConfig{
			ID:                       item.ID,
			ProjectID:                item.ProjectID,
			CodebaseRepoID:           item.CodebaseRepoID,
			JobAnalysisEnable:        item.JobAnalysisEnable,
			PipelineToken:            item.PipelineToken,
			SkipPipeline:             item.SkipPipeline,
			CustomizedReview:         customizedReview,
			Reviewers:                reviewers,
			AutoPublish:              item.AutoPublish,
			IsBizPod:                 item.IsBizPod,
			ReviewerNumber:           item.ReviewerNumber,
			VersionBranchPattern:     item.VersionBranchPattern,
			PodspecName:              item.PodspecName,
			AutoDeleteMergedBranches: item.AutoDeleteMergedBranches,
			AutoDeleteOldBranches:    item.AutoDeleteOldBranches,
			Podfile:                  item.PodFile,
			PipelineForBranch:        pipelineForBranch,
			CustomCiVars:             customCiVars,
			PipelineTriggerTiming:    pipelineTriggerTiming,
			SkipPublicCommentsRegex:  item.SkipPublicCommentsRegex,
			BinaryType:               item.BinaryType,
			Jenkins:                  jenkins,
			PipelineType:             item.PipelineType,
			AutoPublishType:          item.AutoPublishType,
			FileReviewerMap:          fileReviewerMap,
			AppType:                  item.AppType,
			DependencyLock:           item.DependencyLock,
			IntegrationType:          item.IntegrationType,
			Name:                     item.ProjectShowName,
			GreyBranchPattern:        item.GreyBranchPattern,
			CreateMrGroup:            item.CreateMrGroup,
			RockBinarySource:         rockBinarySource,
			DisableOutdatedMerge:     item.DisableOutDatedMerge,
			MonitoredBranchPattern:   item.MonitoredBranchPattern,
			ConySettings:             conySettings,
			GeckoPath:                item.GeckoPath,
			GeckoAppProjects:         geckoProjects,
			ScmID:                    item.ScmID,
			XcodeProjectPath:         item.XcodeProjectPath,
			IsActived:                item.IsActived,
			EnableTranslationCheck:   item.EnableTranslationCheck,
			ConflictDetectLevel:      item.ConflictDetectLevel,
			IntegrationFilePath:      item.IntegrationFilePath,
			AfterCheckEnable:         item.AfterCheckEnable,
			PipelineTemplateID:       item.PipelineTemplateID,
			AfterCheckType:           item.AfterCheckType,
			RcBranchSettings:         rcBranchSettings,
			BuildPackageTemplateID:   item.BuildPackageTemplateID,
			FlutterModule:            item.FlutterModule,
			MultiComponent:           item.MultiComponent,
			PatchTemplateID:          item.PatchTemplateID,
			AutoUpdateSeer:           item.AutoUpdateSeer,
			PodChangeValidateEnable:  item.PodChangeValidateEnable,
			GitlabGroup:              item.GitlabGroup,
			MainGroup:                item.MainGroup,
			IsRootProject:            item.RootProject,
			ProjectName:              item.ProjectName,
			GitRepoAddr:              item.GitUrl,
			PodSeerEnable:            *item.PodSeerEnable,
			PodChangePipelineEnable:  item.GetPodChangePipelineEnable(),
		}
		if item.CustomComponentVersion != nil {
			answer[item.ProjectID].CustomComponentVersion = *item.CustomComponentVersion
		}
	}
	return answer
}

// cony setting all
type ConySettingsAll struct {
	BmReview            bool   `json:"bm_review"`
	CalendarWorkspaceID int    `json:"calendar_workspace_id"`
	DevelopBranch       string `json:"develop_branch"`
	Feature             struct {
		RocketBiz   int    `json:"rocket_biz"`
		RocketToken string `json:"rocket_token"`
		Type        string `json:"type"`
	} `json:"feature"`
	FeatureSwitches struct {
		CanUseLink      bool `json:"can_use_link"`
		IntegrationArea bool `json:"integration_area"`
		Mbox            bool `json:"mbox"`
		Release         bool `json:"release"`
	} `json:"feature_switches"`
	GreyBranch   string `json:"grey_branch"`
	IosCertScope string `json:"ios_cert_scope"`
	Jira         struct {
		Config string `json:"config"`
		URL    string `json:"url"`
	} `json:"jira"`
	ReleaseBranch  string `json:"release_branch"`
	ReleaseVersion struct {
		CancelTime struct {
			Hour   int `json:"hour"`
			Minute int `json:"minute"`
		} `json:"cancel_time"`
		EnvelopeDateAlias  string        `json:"envelope_date_alias"`
		EventName          string        `json:"event_name"`
		GroupNumbers       []interface{} `json:"group_numbers"`
		MeegoProjectKey    string        `json:"meego_project_key"`
		OpenReleaseVersion bool          `json:"openReleaseVersion"`
		PublishDateAlias   string        `json:"publish_date_alias"`
		QaBmAlias          string        `json:"qa_bm_alias"`
		RdBmAlias          string        `json:"rd_bm_alias"`
		ReleaseTime        struct {
			Hour   int `json:"hour"`
			Minute int `json:"minute"`
		} `json:"release_time"`
		VersionPattern string `json:"version_pattern"`
		WarnTime       struct {
			Hour   int `json:"hour"`
			Minute int `json:"minute"`
		} `json:"warn_time"`
	} `json:"release_version"`
	ReverseFlowEnable bool `json:"reverse_flow_enable"`
}

// bm_review_config
type BMReviewConfig struct {
	QaBm struct {
		Branch []string `json:"branch"`
		MinNum int      `json:"min_num"`
	} `json:"qa_bm"`
	RdBm struct {
		Branch []string `json:"branch"`
		MinNum int      `json:"min_num"`
	} `json:"rd_bm"`
}

type GetOptimusServerConfigMainQuery struct {
	GroupName string `form:"GroupName" query:"GroupName" binding:"required"`
}

func GetOptimusServerConfigMain(c context.Context, ctx *app.RequestContext) {

	query := &GetOptimusServerConfigMainQuery{}
	if err := binding.BindAndValidate(ctx, query); err != nil {
		http.ResponseCustom(ctx, 400, "params error", err.Error())
		return
	}

	falseValue := false
	request := &config_service.GetOptimusServerConfigMainQuery{
		GroupName: query.GroupName,
		ItemOnly:  &falseValue,
	}

	resp, err := rpc.ConfigServiceClient.GetOptimusServerConfigMain(c, request)
	if err != nil {
		logs.CtxError(c, "GetOptimusServerConfigMain err: %v", err.Error())
		http.ResponseInternalServerError(ctx, err.Error())
		return
	}
	if resp.ConfigMain != nil && resp.ConfigMain.ConfigGroup != nil {
		if resp.ConfigMain.ConfigGroup.ConySettings != nil {
			if *resp.ConfigMain.ConfigGroup.ConySettings == "" || *resp.ConfigMain.ConfigGroup.ConySettings == "{}" {
				conySetting := ConySettingsAll{}
				conySettingByte, _ := json.Marshal(conySetting)
				conySettingStr := string(conySettingByte)
				resp.ConfigMain.ConfigGroup.ConySettings = &conySettingStr
			}
		}
		if resp.ConfigMain.ConfigGroup.BmReviewConfig == nil {
			bmConfig := &BMReviewConfig{}
			bmReviewConfigByte, _ := json.Marshal(bmConfig)
			bmReviewConfigStr := string(bmReviewConfigByte)
			resp.ConfigMain.ConfigGroup.BmReviewConfig = &bmReviewConfigStr
		}
	}
	http.ResponseOk(ctx, resp)
}
