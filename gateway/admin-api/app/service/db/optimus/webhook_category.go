package optimus

import (
	"context"

	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/model/optimus"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/pkg/database"
)

func GetWebhookCategories(ctx context.Context, page, pageSize int) ([]*optimus.WebHookCategory, error) {
	list := make([]*optimus.WebHookCategory, 0)

	res := database.Connector.Optimus.Slave.NewRequest(ctx).Where("is_delete != 1").Offset((page - 1) * pageSize).Limit(pageSize).Find(&list)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}
	return list, nil
}

func GetWebhookCategoryByID(ctx context.Context, ID int64) (*optimus.WebHookCategory, error) {
	info := &optimus.WebHookCategory{}

	res := database.Connector.Optimus.Slave.NewRequest(ctx).Where("id = ? AND is_delete != 1", ID).Find(info)

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}
	return info, nil
}

func CreateWebhookCategory(ctx context.Context, category *optimus.WebHookCategory) (int64, error) {
	res := database.Connector.Optimus.Master.NewRequest(ctx).Model(&optimus.WebHookCategory{}).Create(category)

	if res.Error != nil {
		return 0, res.Error
	}

	return category.ID, nil
}

func UpdateWebhookCategory(ctx context.Context, category *optimus.WebHookCategory, ID int64) error {
	res := database.Connector.Optimus.Master.NewRequest(ctx).Model(&optimus.WebHookCategory{}).Where("id = ?", ID).Update(category)

	if res.Error != nil {
		return res.Error
	}

	return nil
}

func DeleteWebhookCategory(ctx context.Context, ID int64) error {
	res := database.Connector.Optimus.Master.NewRequest(ctx).Model(&optimus.WebHookCategory{}).Where("id = ?", ID).Update(&optimus.WebHookCategory{
		IsDelete: int8(1),
	})

	if res.Error != nil {
		return res.Error
	}

	return nil
}

func GetWebhookCategoryByName(ctx context.Context, name string) (*optimus.WebHookCategory, error) {
	info := &optimus.WebHookCategory{}

	res := database.Connector.Optimus.Slave.NewRequest(ctx).Where("is_delete != 1 AND name = ?", name).Find(&info)
	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}
	return info, nil
}
