load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "ad",
    srcs = ["index.go"],
    importpath = "code.byted.org/devinfra/hagrid/gateway/admin-api/app/controller/v1/ad",
    visibility = ["//visibility:public"],
    deps = [
        "//gateway/admin-api/app/model/bits",
        "//gateway/admin-api/app/service/cache",
        "//gateway/admin-api/app/service/db/bits",
        "//gateway/admin-api/app/utils",
        "//gateway/admin-api/app/utils/consts",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_middleware_hertz//pkg/app",
        "@org_byted_code_middleware_hertz//pkg/common/utils",
        "@org_byted_code_middleware_hertz_ext_v2//binding",
    ],
)
