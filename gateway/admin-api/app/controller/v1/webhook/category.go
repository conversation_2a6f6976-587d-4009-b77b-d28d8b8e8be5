package webhook

import (
	"context"
	"strconv"

	optimusModel "code.byted.org/devinfra/hagrid/gateway/admin-api/app/model/optimus"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/service/db/optimus"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/utils"
	"code.byted.org/devinfra/hagrid/gateway/admin-api/app/utils/consts"
	"code.byted.org/middleware/hertz/pkg/app"
	hutils "code.byted.org/middleware/hertz/pkg/common/utils"
	"code.byted.org/middleware/hertz_ext/v2/binding"
)

func GetCategory(c context.Context, ctx *app.RequestContext) {
	query := struct {
		Page     int `query:"page" binding:"required"`
		PageSize int `query:"page_size" binding:"required"`
	}{}
	if err := binding.BindAndValidate(ctx, &query); err != nil {
		utils.Response(c, ctx, consts.ResponseError, err.Error())
		return
	}

	answer, _ := optimus.GetWebhookCategories(c, query.Page, query.PageSize)
	if answer == nil {
		utils.Response(c, ctx, consts.ResponseSuccess, answer)
		return
	}

	total, _ := optimus.CountWebhookCategory(c)
	utils.Response(c, ctx, consts.ResponseSuccess, hutils.H{
		"list":  answer,
		"total": total,
	})
}

func CreateCategory(c context.Context, ctx *app.RequestContext) {
	body := struct {
		Name  string `json:"name"`
		Title string `json:"title"`
	}{}

	if err := binding.BindAndValidate(ctx, &body); err != nil {
		utils.Response(c, ctx, consts.ResponseError, err.Error())
		return
	}
	// 判断是否重名
	if info, _ := optimus.GetWebhookCategoryByName(c, body.Name); info != nil {
		utils.Response(c, ctx, consts.ResponseError, "`name` already exists, don't create again")
		return
	}
	insertID, err := optimus.CreateWebhookCategory(c, &optimusModel.WebHookCategory{
		Name:     body.Name,
		Title:    body.Title,
		IsDelete: 0,
	})
	if err != nil {
		utils.Response(c, ctx, consts.ResponseError, "failed to create")
		return
	}

	utils.Response(c, ctx, consts.ResponseSuccess, insertID)
}

func UpdateCategory(c context.Context, ctx *app.RequestContext) {
	body := struct {
		Name  string `json:"name"`
		Title string `json:"title"`
	}{}

	if err := binding.BindAndValidate(ctx, &body); err != nil {
		utils.Response(c, ctx, consts.ResponseError, err.Error())
		return
	}
	// 判断是否重名
	if info, _ := optimus.GetWebhookCategoryByName(c, body.Name); info != nil {
		utils.Response(c, ctx, consts.ResponseError, "`name` already exists, don't create again")
		return
	}

	idStr := ctx.Param("id")
	ID, _ := strconv.ParseInt(idStr, 10, 64)
	err := optimus.UpdateWebhookCategory(c, &optimusModel.WebHookCategory{
		Name:  body.Name,
		Title: body.Title,
	}, ID)

	if err != nil {
		utils.Response(c, ctx, consts.ResponseError, "failed to update")
		return
	}
	utils.Response(c, ctx, consts.ResponseSuccess, nil)
}

func DeleteCategory(c context.Context, ctx *app.RequestContext) {
	idStr := ctx.Param("id")
	ID, _ := strconv.ParseInt(idStr, 10, 64)
	// 判断event是否存在
	if category, _ := optimus.GetWebhookCategoryByID(c, ID); category == nil {
		utils.Response(c, ctx, consts.ResponseError, "`category_id` not exists")
		return
	}
	err := optimus.DeleteWebhookCategory(c, ID)
	if err != nil {
		utils.Response(c, ctx, consts.ResponseError, "failed to update")
		return
	}
	utils.Response(c, ctx, consts.ResponseSuccess, nil)
}
