package handler

import (
	"code.byted.org/devinfra/hagrid/infra/var_store/kitex_gen/bytedance/bits/var_store"
	"code.byted.org/devinfra/hagrid/infra/var_store/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/infra/var_store/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/infra/var_store/pkg/data"
	"code.byted.org/gopkg/logs/v2"
	"context"
)

func SaveEnv(ctx context.Context, req *var_store.SaveEnvReq) (*var_store.SaveEnvResp, error) {
	caller := ""
	if req.Base != nil {
		caller = req.Base.Caller
	}
	repo := data.NewVarStoreRepo(mysql.Main)
	repo.Begin(ctx)
	old, err := repo.GetVarStoreWithLock(ctx, req.Namespace.String(), req.Entity, uint64(req.Id), req.Var)
	if err != nil {
		logs.V1.CtxError(ctx, "failed to get var store with lock, key:%s,err:%s", data.GetVarStoreKey(req.Namespace.String(), req.Entity, uint64(req.Id), req.Var), err.Error())
		repo.Rollback()
		return nil, err
	}
	var newStore *model.VarStore
	if old == nil { // 新创建
		newStore, err = repo.CreateVarStore(ctx, req.Namespace.String(), req.Entity, uint64(req.Id), req.Var, req.Value, &caller)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to create var store, key:%s,err:%s", data.GetVarStoreKey(req.Namespace.String(), req.Entity, uint64(req.Id), req.Var), err.Error())
			repo.Rollback()
			return nil, err
		}
		logs.CtxInfo(ctx, "var store created,id:%d", newStore.ID)
	} else { // 更新
		newStore = old
		newStore.Creator = caller
		newStore.Value = req.Value
		err = repo.SaveVarStore(ctx, newStore)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to save var store, key:%s,err:%s", data.GetVarStoreKey(req.Namespace.String(), req.Entity, uint64(req.Id), req.Var), err.Error())
			repo.Rollback()
			return nil, err
		}
		logs.CtxInfo(ctx, "var store updated,id:%d", old.ID)
	}
	err = repo.Commit()
	if err != nil {
		logs.V1.CtxError(ctx, "failed to commit, err:%s", err.Error())
		return nil, err
	}
	_ = data.SetVarStoreCache(ctx, newStore)
	return &var_store.SaveEnvResp{IsNew: old == nil}, nil
}

func SaveEnvBatch(ctx context.Context, req *var_store.SaveEnvBatchReq) error {
	caller := ""
	if req.Base != nil {
		caller = req.Base.Caller
	}
	repo := data.NewVarStoreRepo(mysql.Main)
	repo.Begin(ctx)
	for key, value := range req.Vars {
		ns, entity, id, varName, err := data.ParseVarStoreKey(key)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to parse key:%s,err:%s", key, err.Error())
			return err
		}
		old, err := repo.GetVarStoreWithLock(ctx, ns, entity, id, varName)
		if err != nil {
			logs.V1.CtxError(ctx, "failed to get var store with lock, key:%s,err:%s", data.GetVarStoreKey(ns, entity, id, varName), err.Error())
			repo.Rollback()
			return err
		}
		if old == nil { // 新创建
			m, err := repo.CreateVarStore(ctx, ns, entity, id, varName, value, &caller)
			if err != nil {
				logs.V1.CtxError(ctx, "failed to create var store, key:%s,err:%s", data.GetVarStoreKey(ns, entity, id, varName), err.Error())
				repo.Rollback()
				return err
			}
			logs.CtxInfo(ctx, "var store created,id:%d", m.ID)
		} else { // 更新
			old.Creator = caller
			old.Value = value
			err = repo.SaveVarStore(ctx, old)
			if err != nil {
				logs.V1.CtxError(ctx, "failed to save var store, key:%s,err:%s", data.GetVarStoreKey(ns, entity, id, varName), err.Error())
				repo.Rollback()
				return err
			}
			logs.CtxInfo(ctx, "var store updated,id:%d", old.ID)
		}
	}
	err := repo.Commit()
	if err != nil {
		logs.V1.CtxError(ctx, "failed to commit, err:%s", err.Error())
		return err
	}
	for key, value := range req.Vars {
		_ = data.SetVarStoreCacheByKey(ctx, key, value)
	}
	return nil
}
