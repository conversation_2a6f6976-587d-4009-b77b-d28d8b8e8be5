package initialization

import (
	"code.byted.org/devinfra/hagrid/infra/notification/pkg/conf"
	"code.byted.org/devinfra/hagrid/infra/notification/pkg/dal"
	"code.byted.org/devinfra/hagrid/infra/notification/pkg/mq"
	"code.byted.org/devinfra/hagrid/infra/notification/pkg/urls"

	"code.byted.org/gin/ginex"
	"code.byted.org/gopkg/logs"
)

func Init() *ginex.Engine {
	conf.Init()
	mq.Init()
	dal.Init()
	ginex.Init()
	return urls.Init()
}

func Close() {
	logs.Flush()
}
