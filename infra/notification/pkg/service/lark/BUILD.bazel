load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "lark",
    srcs = [
        "bot.go",
        "chat.go",
        "image_upload.go",
        "query.go",
        "send.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/notification/pkg/service/lark",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/notification/pkg/consts",
        "//infra/notification/pkg/dal/kv",
        "//infra/notification/pkg/dal/tcc",
        "//infra/notification/pkg/model",
        "//infra/notification/pkg/service/cache",
        "//libs/common_lib/consts",
        "//libs/common_lib/model",
        "//libs/common_lib/model/lark_model",
        "//libs/common_lib/utils",
        "//libs/thirdparty-sdks/lark",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_davecgh_go_spew//spew",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_larksuite_botframework_go//SDK/common",
        "@com_github_larksuite_botframework_go//SDK/message",
        "@com_github_larksuite_botframework_go//SDK/protocol",
        "@org_byted_code_gin_ginex//:ginex",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "lark_test",
    srcs = [
        "go_test.go",
        "query_test.go",
    ],
    embed = [":lark"],
    tags = ["known-to-fail"],
    deps = [
        "//infra/notification/pkg/dal/tcc",
        "//infra/notification/pkg/model",
        "@com_github_gin_gonic_gin//:gin",
    ],
)
