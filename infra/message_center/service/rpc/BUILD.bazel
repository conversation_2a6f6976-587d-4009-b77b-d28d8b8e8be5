load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "rpc",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/message_center/service/rpc",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/message_center/kitex_gen/bytedance/bits/config_service/configservice",
        "//libs/common_lib/middleware",
        "//libs/common_lib/utils",
        "//libs/locally",
        "//libs/middleware/kitexmw",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_overpass_bytedance_bits_meta//rpc/bytedance_bits_meta",
        "@org_byted_code_overpass_common//option/clientoption",
    ],
)
