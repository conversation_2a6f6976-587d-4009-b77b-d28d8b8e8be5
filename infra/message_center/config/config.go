package config

import (
	"fmt"
	"os"
	"path/filepath"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"github.com/spf13/viper"
)

type Redis struct {
	PSM  string `mapstructure:"psm"`
	Host string `mapstructure:"host"`
	Port int    `mapstructure:"port"`
}
type CommonDbClient struct {
	PSM      string `mapstructure:"psm"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	DB       string `mapstructure:"db"`
}
type MysqlItem struct {
	DB     string         `mapstructure:"db"`
	Prefix string         `mapstructure:"prefix"`
	Master CommonDbClient `mapstructure:"master"`
	Slave  CommonDbClient `mapstructure:"slave"`
}
type mysql struct {
	MessageCenter *MysqlItem `mapstructure:"message_center"`
}

type notify struct {
	AppID string `mapstructure:"app_id"`
}
type rocketMQ struct {
	PSM         string `mapstructure:"psm"`
	ClusterName string `mapstructure:"cluster_name"`
	Topic       string `mapstructure:"topic"`
}

type larkDetail struct {
	AppID string `mapstructure:"app_id"`
}

type larkConfig struct {
	Bits *larkDetail `mapstructure:"bits"`
}

type AppConfig struct {
	Mysql    mysql      `mapstructure:"mysql"`
	Redis    Redis      `mapstructure:"redis"`
	RocketMQ rocketMQ   `mapstructure:"rocketmq"`
	ESConfig esConfig   `mapstructure:"es"`
	Notify   notify     `mapstructure:"notify"`
	Lark     larkConfig `mapstructure:"lark"`
}

type esConfig struct {
	PSM     string `json:"psm"`
	Index   string `json:"index"`
	User    string `json:"user"`
	Pass    string `json:"pass"`
	Cluster string `json:"cluster"`
}

var App AppConfig

func Init(str string) {
	logs.Info("IDC %s IsProduct %t", env.IDC(), env.IsProduct())

	v := viper.New()
	configFile := "conf/local.json"

	if os.Getenv("TOE_ENV") == "pre" {
		configFile = "conf/pre.json"
	} else if env.IsBoe() {
		configFile = "conf/boe.json"
	} else if env.IsProduct() {
		configFile = "conf/prod.json"
	}
	configFile = fmt.Sprintf("%s/%s", str, configFile)
	p, _ := filepath.Abs(os.Getenv("CONF_DIR") + configFile)
	v.SetConfigFile(p)

	if err := v.ReadInConfig(); err != nil {
		logs.Error("common_error", err.Error())
		panic(err)
	}

	if err := v.Unmarshal(&App); err != nil {
		logs.Error("common_error", err.Error())
		panic(err)
	}
}
