package main

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/message_center/kitex_gen/bytedance/bits/message_center"
	"code.byted.org/devinfra/hagrid/infra/message_center/pkg/handlers"
)

// MessageServiceImpl implements the last service interface defined in the IDL.
type MessageServiceImpl struct{}

// SendMessage implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SendMessage(ctx context.Context, req *message_center.SendMessageQuery) (r *message_center.SendMessageResponse, err error) {
	return handlers.SendMessage(ctx, req)
}

// AddTemplate implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) AddTemplate(ctx context.Context, req *message_center.AddTemplateQuery) (*message_center.AddTemplateResponse, error) {
	return handlers.AddTemplate(ctx, req)
}

// GetTemplateByName Deprecated
// GetTemplateByName implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetTemplateByName(ctx context.Context, req string) (*message_center.GetTemplateByNameResponse, error) {
	return handlers.GetTemplateByName(ctx, req)
}

// DeleteTemplate implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) DeleteTemplate(ctx context.Context, id int64) (err error) {
	return handlers.DeleteTemplate(ctx, id)
}

// UpdateTemplate implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) UpdateTemplate(ctx context.Context, req *message_center.UpdateTemplateQuery) (err error) {
	return handlers.UpdateTemplate(ctx, req)
}

// AddEvent implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) AddEvent(ctx context.Context, req *message_center.AddEventQuery) (r *message_center.AddEventResponse, err error) {
	return handlers.AddEvent(ctx, req)
}

// DeleteEvent implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) DeleteEvent(ctx context.Context, id int64) (err error) {
	return handlers.DeleteEvent(ctx, id)
}

// MuteEvent implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) MuteEvent(ctx context.Context, username string, eventId int64) (resp *message_center.SendMessageResponse, err error) {
	return handlers.MuteEvent(ctx, username, eventId)
}

// SubscribeEvent implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SubscribeEvent(ctx context.Context, username string, eventId int64) (resp *message_center.SendMessageResponse, err error) {
	return handlers.SubscribeEvent(ctx, username, eventId)
}

// DeleteRule implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) DeleteRule(ctx context.Context, username string, eventId int64) (err error) {
	return handlers.DeleteRules(ctx, username, eventId)
}

// GetEventList implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetEventList(ctx context.Context, _ *message_center.VoidStruct) (resp []*message_center.Event, err error) {
	return handlers.ListEventHide(ctx)
}

// GetTemplateList implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetTemplateList(ctx context.Context, _ *message_center.VoidStruct) (resp *message_center.GetTemplateResponseListResponse, err error) {
	return handlers.GetTemplateList(ctx)
}

// GetTemplateById implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetTemplateById(ctx context.Context, id int64) (resp *message_center.Template, err error) {
	return handlers.GetTemplateById(ctx, id)
}

// RegisterCallback implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) RegisterCallback(ctx context.Context, req *message_center.RegisterCallbackQuery) (resp int64, err error) {
	return handlers.RegisterCallback(ctx, req)
}

// RunCallback implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) RunCallback(ctx context.Context, req *message_center.RunCallbackQuery) (err error) {
	return handlers.RunCallback(ctx, req)
}

// UpdateEvent implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) UpdateEvent(ctx context.Context, req *message_center.UpdateEventQuery) (err error) {
	return handlers.UpdateEvent(ctx, req)
}

// CheckEventStatus implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) CheckEventStatus(ctx context.Context, username string, eventId int64) (resp message_center.EventStatus, err error) {
	return handlers.CheckEventStatus(ctx, username, eventId)
}

// MultiUserCheckEventStatus implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) MultiUserCheckEventStatus(ctx context.Context, usernames []string, eventId int64) (resp *message_center.MultiUserCheckEventStatusResponse, err error) {
	return handlers.MultiUserCheckEventStatus(ctx, usernames, eventId)
}

// GetEventByType implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetEventByType(ctx context.Context, eventType string) (resp []int64, err error) {
	return handlers.GetEventByType(ctx, eventType)
}

// GetEventType implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetEventType(ctx context.Context, typeName string) (resp *message_center.EventType, err error) {
	return handlers.GetEventType(ctx, typeName)
}

// SearchEvent implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SearchEvent(ctx context.Context, req *message_center.SearchEventQuery) (resp *message_center.SearchEventResp, err error) {
	e, total, err := handlers.SearchEvent(ctx, req)
	return &message_center.SearchEventResp{Events: e, Total: total}, err
}

// GetEventTypeList implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetEventTypeList(ctx context.Context, _ *message_center.VoidStruct) (resp []*message_center.EventType, err error) {
	return handlers.GetEventTypeList(ctx)
}

// UpdateEventType implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) UpdateEventType(ctx context.Context, req *message_center.UpdateEventTypeQuery) (err error) {
	return handlers.UpdateEventType(ctx, req)
}

// AddEventType implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) AddEventType(ctx context.Context, req *message_center.AddEventTypeQuery) (id int64, err error) {
	return handlers.AddEventType(ctx, req)
}

// CreateBitsNotification implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) CreateBitsNotification(ctx context.Context, req *message_center.CreateBitsNotificationQuery) (resp *message_center.CreateBitsNotificationResponse, err error) {
	return handlers.CreateNotify(ctx, req)
}

// SetNotificationOnline implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SetNotificationOnline(ctx context.Context, req *message_center.SetNotificationOnlineQuery) (resp *message_center.SetNotificationOnlineResponse, err error) {
	return handlers.SetNotificationOnline(ctx, req)
}

// SetNotificationOffline implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SetNotificationOffline(ctx context.Context, req *message_center.SetNotificationOfflineQuery) (resp *message_center.SetNotificationOfflineResponse, err error) {
	return handlers.SetNotificationOffline(ctx, req)
}

// GetNotificationsByGroupName implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetNotificationsByGroupName(ctx context.Context, req *message_center.GetNotificationsByGroupNameQuery) (resp *message_center.GetNotificationsByGroupNameResponse, err error) {
	return handlers.GetNotificationByGroupName(ctx, req)
}

// GetOnlineNotificationLatestByGroupName implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetOnlineNotificationLatestByGroupName(ctx context.Context, req *message_center.GetOnlineNotificationLatestByGroupNameQuery) (resp *message_center.GetOnlineNotificationLatestByGroupNameResponse, err error) {
	return handlers.GetOnlineNotificationLatestByGroupName(ctx, req)
}

// SendMessageV2 implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SendMessageV2(ctx context.Context, req *message_center.SendMessageQueryV2) (resp *message_center.SendMessageResponseV2, err error) {
	return handlers.SendMessageV2(ctx, req)
}

// GetEventListAdmin implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) GetEventListAdmin(ctx context.Context, _ *message_center.VoidStruct) (resp []*message_center.Event, err error) {
	return handlers.ListEvent(ctx)
}

// SendOneSiteMessage implements the MessageServiceImpl interface.
func (s *MessageServiceImpl) SendOneSiteMessage(ctx context.Context, req *message_center.SendOneSiteMessageQuery) (resp *message_center.SendOneSiteMessageResponse, err error) {
	return handlers.SendOneSiteMessage(ctx, req)
}
