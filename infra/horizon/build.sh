#!/usr/bin/env bash

if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
  bash ${BUILD_PATH}/scripts/pre-build.scm.sh
fi

RUN_NAME="bits.infra.horizon"

mkdir -p output/bin output/conf
cp script/bootstrap.sh script/settings.py output
chmod +x output/bootstrap.sh
cp conf/kitex.yml output/conf

bash ${BUILD_PATH}/scripts/bazel_scm_preparation.sh
bazel build //infra/horizon:horizon
cp ${BUILD_PATH}/bazel-bin/infra/horizon/horizon_/horizon output/bin/${RUN_NAME}

# go build -v -o output/bin/${RUN_NAME}

if [[ $OLD_PWD != "" ]]; then
  cp -r output $OLD_PWD
  bash ${BUILD_PATH}/scripts/post-build.scm.sh
fi
