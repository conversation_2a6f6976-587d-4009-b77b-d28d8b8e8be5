load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mocktools",
    srcs = [
        "meta.go",
        "optimus.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/horizon/mocktools",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/horizon/kitex_gen/bytedance/bits/graph",
        "//infra/horizon/kitex_gen/bytedance/bits/meta",
        "//infra/horizon/kitex_gen/bytedance/bits/optimus",
        "//infra/horizon/pkg/backends/metaapi",
        "//infra/horizon/pkg/backends/optimusapi",
        "@org_byted_code_lang_gg//gresult",
    ],
)
