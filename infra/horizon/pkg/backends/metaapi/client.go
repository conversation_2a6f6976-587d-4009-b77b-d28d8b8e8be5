/**
 * @Date: 2022/10/13
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package metaapi

import (
	"context"
	"strings"

	"code.byted.org/devinfra/hagrid/infra/horizon/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/horizon/kitex_gen/bytedance/bits/meta/metaservice"
	"code.byted.org/devinfra/hagrid/infra/horizon/pkg/backends"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"github.com/pkg/errors"
)

type client struct {
	inner metaservice.Client
}

var _ Api = &client{}

func New() Api {
	options := backends.NewRPCOptions(false)

	return &client{
		inner: metaservice.MustNewClient("bytedance.bits.meta", options...),
	}
}

func (client *client) QueryAppByName(ctx context.Context, englishName string) gresult.R[*meta.AppSimpleInfo] {
	request := &meta.QueryAppSimpleInfoByEnglishNameRequest{EnglishName: englishName}
	response, err := client.inner.QueryAppSimpleInfoByEnglishName(ctx, request)

	backends.Trace(ctx, "QueryAppByName", request, response, err)
	if err != nil {
		return gresult.Err[*meta.AppSimpleInfo](err)
	}
	return gresult.OK(response.App)
}

var ErrNoAccess = errors.New("no access")

func (client *client) QueryUserAppPermission(ctx context.Context, email string, appId int64, permission string) error {
	request := &meta.QueryUserAppPermissionRequest{
		User:       email, // email
		AppId:      gptr.Of(appId),
		Permission: permission,
	}
	response, err := client.inner.QueryUserAppPermission(ctx, request)
	backends.Trace(ctx, "QueryUserAppPermission", request, response, err)
	if err != nil {
		return err
	}
	if !response.HasPermission {
		return ErrNoAccess
	}
	return nil
}

func (client *client) QueryLarkUserByOpenId(ctx context.Context, id string) gresult.R[*meta.User] {
	request := &meta.QueryLarkUserRequest{Id: id, Idtype: meta.IdType_OPEN_ID}
	response, err := client.inner.QueryLarkUser(ctx, request)
	backends.Trace(ctx, "QueryLarkUserByOpenId", request, response, err)
	if err != nil {
		return gresult.Err[*meta.User](err)
	}
	return gresult.OK(response.User)
}

func (client *client) QueryLarkId(ctx context.Context, email string) gresult.R[*meta.Id] {
	if !strings.Contains(email, "@") {
		email = email + "@bytedance.com"
	}
	request := &meta.QueryLarkIdByEmailRequest{Email: email}
	response, err := client.inner.QueryLarkIdByEmail(ctx, request)
	if err != nil {
		return gresult.Err[*meta.Id](err)
	}
	backends.Trace(ctx, "QueryLarkId", request, response, err)
	return gresult.OK(response.Id)
}
