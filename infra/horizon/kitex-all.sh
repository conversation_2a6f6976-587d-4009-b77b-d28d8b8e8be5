#! /usr/bin/env bash

kitex --disable-self-update -module code.byted.org/devinfra/hagrid -service bits.infra.horizon -thrift no_fmt ../../idls/infra/horizon/service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../idls/app/optimus.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../idls/infra/meta/service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../idls/infra/git_server/service.thrift
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../idls/app/rd-process/dev/dev.thrift
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../idls/app/integration_multi.thrift
