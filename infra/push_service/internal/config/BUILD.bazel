load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "config",
    srcs = ["config.go"],
    embedsrcs = [
        "boe.yaml",
        "prod.yaml",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/push_service/internal/config",
    visibility = ["//infra/push_service:__subpackages__"],
    deps = [
        "//infra/push_service/pkg/frontier",
        "//infra/push_service/pkg/mongo",
        "//infra/push_service/pkg/mq",
        "//infra/push_service/pkg/utils",
        "@org_byted_code_gopkg_env//:env",
    ],
)
