load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "config.go",
        "copy.go",
        "json.go",
        "map.go",
        "string.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/push_service/pkg/utils",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_jinzhu_copier//:copier",
        "@com_github_json_iterator_go//:go",
        "@com_github_spf13_viper//:viper",
    ],
)
