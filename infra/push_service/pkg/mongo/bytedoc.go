package mongo

import (
	"context"
	"fmt"
	"log"

	"code.byted.org/bytedoc/mongo-go-driver/mongo"
	"code.byted.org/bytedoc/mongo-go-driver/mongo/options"
)

type Config struct {
	PSM string `yaml:"psm"`
	DB  string `yaml:"db"`
}

func (c *Config) URI() string {
	return fmt.Sprintf("mongodb+consul+token://%s/%s?connectTimeoutMS=3000", c.PSM, c.DB)
}

func MustInitByteDoc(cfg *Config) *mongo.Database {
	if cfg == nil {
		return nil
	}
	client, err := mongo.Connect(context.Background(), options.Client().ApplyURI(cfg.URI()))
	if err != nil {
		log.Fatalf("init bytedoc err: %v", err)
	}

	return client.Database(cfg.DB)
}
