package meego

import (
	"context"
	"code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/meego"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/mtctx"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/tenancies"

	"code.byted.org/gopkg/logs/v2/log"
)

var MeegoAPIClient meego.Api

func Init() {
	MeegoAPIClient = meego.NewClient()
}

func GetMeegoUserKey(ctx context.Context, email, originKey string) string {
	// 最开始我联系 Meego，在外场也创建了 bits 这个用户，因为内场也是有的，他是一个虚拟账户。
	// 但是由于安全原因，在外场，虚拟用户是无法使用的。

	tenancy := mtctx.GetTenancy(ctx)
	if tenancy == tenancies.TenancyBytedance { // 内场逻辑不变，按照旧逻辑使用
		return originKey
	}

	user, err := MeegoAPIClient.QueryUserByEmail(ctx, email)
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to get meego user").Error(err).KV("email", email).Emit()
		return originKey // 兜底
	}
	return user.UserKey
}
