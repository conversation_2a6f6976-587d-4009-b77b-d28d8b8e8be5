package user_task

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/feature/config"
	"code.byted.org/devinfra/hagrid/infra/feature/kitex_gen/bytedance/bits/feature"
	"code.byted.org/devinfra/hagrid/infra/feature/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/infra/feature/pkg/consts"
)

func GetControllerObject(platform consts.PlatformName) Controller {
	var controller Controller
	switch platform {
	case consts.PLATFORM_MEEGO:
		controller = &Meego{}
	case consts.PLATFORM_SLARDAR:
		controller = &Slardar{}
	case consts.PLATFORM_TESTCASE:
		controller = NewTestCase()
	case consts.PLATFORM_JIRA:
		controller = &Jira{
			Token:  config.App.FeatureConfig.Jira.Token,
			Schema: config.App.FeatureConfig.Jira.Domain,
		}
	}
	if controller == nil {
		controller = &Meego{}
	}
	return controller
}

type Controller interface {
	GetProjects(ctx context.Context, email string) ([]*Project, error)                                                            // 获取所有项目
	GetTaskDetail(ctx context.Context, projectID string, taskID string, taskType string, userKey string) (*UserTaskInfo, error)   // 获取任务详情
	GetTaskDetailV2(ctx context.Context, projectID string, taskID string, taskType string, userKey string) (*UserTaskInfo, error) // 获取任务详情
	SearchTasks(ctx context.Context, query *UserTasksQuery2) (tasks []*UserTaskInfo, err error, page *int64, count *int64)        // 搜索任务
	GetUserTasks(ctx context.Context, query *UserTasksQuery) (tasks []*UserTaskInfo, err error, page *int64, count *int64)        // 获取用户的任务
	CreateConfig(ctx context.Context, config *Config) (int64, error)                                                              // 创建配置
	UpdateConfig(ctx context.Context, config *Config, ID int64) error                                                             // 更新配置
	DeleteConfig(ctx context.Context, configID int64) error                                                                       // 删除配置
	GetConfigs(ctx context.Context, groupName string, taskType *consts.TaskType) ([]*Config, error)                               // 获取项目下的所有配置
	TransRPCConfigModelToGo(config *feature.FeatureConfigDetail) *ConfigDetail                                                    // rpc to go
	SyncVersion(ctx context.Context, taskID, projectName string, mergeRequest *optimus.MrMainInfo)
	ListAcrossProjectWorkItems(ctx context.Context, user, projectKey, taskType, name string, statuses []string, page, pageSize int64, onlyMe bool) ([]*WorkItem, int64, error)
	ListWorkItemsByPageView(ctx context.Context, user, projectKey, viewId, taskType, name string, statuses []string, page, pageSize int64, aboutUser *string) ([]*WorkItem, int64, error)
}
