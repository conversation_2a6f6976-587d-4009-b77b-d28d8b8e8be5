package lark_handler

import (
	"context"
	"errors"
	"strings"

	"code.byted.org/gopkg/logs"
	v1 "github.com/larksuite/oapi-sdk-go/service/task/v1"
	"code.byted.org/devinfra/hagrid/infra/feature/kitex_gen/bytedance/bits/feature"
	"code.byted.org/devinfra/hagrid/infra/feature/service/lark"
)

func CreateLarkTask(ctx context.Context, req *feature.CreateLarkTaskRequest) (resp *feature.LarkTaskResponse, err error) {
	taskCreateResult, err := lark.CreateTask(ctx, transferRPCTask2LarkTask(req.Task))
	if err != nil {
		logs.CtxError(ctx, "CreateLarkTask, error=%s", err.Error())
		return
	}
	// 如果设置了提醒时间
	if req.IsSetRelativeFireMinute() {
		err = lark.CreateTaskReminders(ctx, taskCreateResult.Task.Id, int(req.GetRelativeFireMinute()))
		if err != nil {
			logs.CtxError(ctx, "CreateLarkTask CreateTaskReminders, error=%s", err.Error())
		}
	}
	// 如果设置了执行人
	for _, collaborator := range req.Collaborators {
		if strings.Contains(collaborator, "@") {
			err = lark.AddTaskCollaborator(ctx, taskCreateResult.Task.Id, collaborator)
			if err != nil {
				logs.CtxError(ctx, "CreateLarkTask AddTaskCollaborator, error=%s", err.Error())
				return
			}
		}
	}

	resp = &feature.LarkTaskResponse{Task: transferLarkTask2RPCTask(taskCreateResult.Task)}
	return
}

func DeleteLarkTask(ctx context.Context, req *feature.DeleteLarkTaskRequest) (resp *feature.EmptyResponse, err error) {
	resp = feature.NewEmptyResponse()
	err = lark.DeleteTask(ctx, req.GetTaskId())
	return
}

func UpdateLarkTask(ctx context.Context, req *feature.UpdateLarkTaskRequest) (resp *feature.LarkTaskResponse, err error) {
	if !req.IsSetTask() {
		err = errors.New("task not set")
		logs.CtxError(ctx, "UpdateLarkTask, error=%s", err.Error())
		return nil, err
	}
	// 确定更新字段
	var updateFields []string
	if req.Task.IsSetSummary() {
		updateFields = append(updateFields, "summary")
	}
	if req.Task.IsSetDescription() {
		updateFields = append(updateFields, "description")
	}
	if req.Task.IsSetExtra() {
		updateFields = append(updateFields, "extra")
	}
	if req.Task.IsSetDue() {
		updateFields = append(updateFields, "due")
	}
	if req.Task.IsSetOrigin() {
		updateFields = append(updateFields, "origin")
	}
	if req.Task.IsSetCanEdit() {
		updateFields = append(updateFields, "can_edit")
	}
	if req.Task.IsSetCustom() {
		updateFields = append(updateFields, "custom")
	}
	if len(updateFields) == 0 {
		err = errors.New("len(updateFields)==0")
		logs.CtxError(ctx, "UpdateLarkTask updateFields == 0, error=%s", err.Error())
		return
	}
	r, err := lark.UpdateTask(ctx, req.GetTaskId(), transferRPCTask2LarkTask(req.Task), updateFields)
	if err != nil {
		logs.CtxError(ctx, "UpdateLarkTask UpdateTask, error=%s", err.Error())
		return
	}
	resp = &feature.LarkTaskResponse{Task: transferLarkTask2RPCTask(r.Task)}
	return
}

func CompleteLarkTask(ctx context.Context, req *feature.ChangeLarkTaskCompleteRequest) (resp *feature.EmptyResponse, err error) {
	resp = feature.NewEmptyResponse()
	if req.Complete {
		err = lark.CompleteTask(ctx, req.GetTaskId())
	} else {
		err = lark.UncompleteTask(ctx, req.GetTaskId())
	}
	return
}

func GetLarkTask(ctx context.Context, req *feature.GetLarkTaskRequest) (resp *feature.LarkTaskResponse, err error) {
	r, err := lark.GetTask(ctx, req.GetTaskId())
	if err != nil {
		logs.CtxError(ctx, "GetLarkTask get task,error=%s", err.Error())
		return
	}
	resp = &feature.LarkTaskResponse{Task: transferLarkTask2RPCTask(r.Task)}
	return
}

func transferRPCTask2LarkTask(rpcTask *feature.LarkTask) (larkTask *v1.Task) {
	if rpcTask == nil {
		return
	}
	larkTask = &v1.Task{
		Id:           rpcTask.GetId(),
		Summary:      rpcTask.GetSummary(),
		Description:  rpcTask.GetDescription(),
		CompleteTime: rpcTask.GetCompleteTime(),
		CreatorId:    rpcTask.GetCreatorId(),
		Extra:        rpcTask.GetExtra(),
		CreateTime:   rpcTask.GetCreateTime(),
		UpdateTime:   rpcTask.GetUpdateTime(),
		CanEdit:      rpcTask.GetCanEdit(),
		Custom:       rpcTask.GetCustom(),
	}
	if rpcTask.IsSetDue() {
		larkTask.Due = &v1.Due{
			Time:     rpcTask.Due.GetTime(),
			Timezone: rpcTask.Due.GetTimezone(),
			IsAllDay: rpcTask.Due.GetIsAllDay(),
		}
	}
	if rpcTask.IsSetOrigin() {
		larkTask.Origin = &v1.Origin{
			PlatformI18nName: rpcTask.Origin.GetPlatformI18nName(),
		}
		if rpcTask.Origin.IsSetHref() {
			larkTask.Origin.Href = &v1.Href{
				Url:   rpcTask.Origin.Href.GetUrl(),
				Title: rpcTask.Origin.Href.GetTitle(),
			}
		}
	}
	return
}

func transferLarkTask2RPCTask(larkTask *v1.Task) (rpcTask *feature.LarkTask) {
	if larkTask == nil {
		return
	}
	rpcTask = &feature.LarkTask{
		Id:           &larkTask.Id,
		Summary:      &larkTask.Summary,
		Description:  &larkTask.Description,
		CompleteTime: &larkTask.CompleteTime,
		CreatorId:    &larkTask.CreatorId,
		Extra:        &larkTask.Extra,
		CreateTime:   &larkTask.CreateTime,
		UpdateTime:   &larkTask.UpdateTime,
		CanEdit:      &larkTask.CanEdit,
		Custom:       &larkTask.Custom,
	}
	if larkTask.Due != nil {
		rpcTask.Due = &feature.LarkTaskDue{
			Time:     larkTask.Due.Time,
			Timezone: &larkTask.Due.Timezone,
			IsAllDay: &larkTask.Due.IsAllDay,
		}
	}
	if larkTask.Origin != nil {
		rpcTask.Origin = &feature.LarkTaskOrigin{
			PlatformI18nName: larkTask.Origin.PlatformI18nName,
		}
		if larkTask.Origin.Href != nil {
			rpcTask.Origin.Href = &feature.LarkTaskOriginHref{
				Url:   &larkTask.Origin.Href.Url,
				Title: &larkTask.Origin.Href.Title,
			}
		}
	}
	return
}
