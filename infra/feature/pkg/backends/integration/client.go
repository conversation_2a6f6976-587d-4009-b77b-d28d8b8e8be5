/**
 * @Date: 2023/6/28
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package integration

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/infra/feature/kitex_gen/bits/integration/multi"
	"code.byted.org/devinfra/hagrid/infra/feature/kitex_gen/bits/integration/multi/integrationservice"
	"code.byted.org/devinfra/hagrid/infra/feature/pkg/backends/options"
	"code.byted.org/devinfra/hagrid/infra/feature/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/feature/pkg/data"
	"code.byted.org/lang/gg/gresult"
)

type client struct {
	multi integrationservice.Client
}

func New() Api {
	opts := options.NewRPCOptions()
	multiClient := integrationservice.MustNewClient("bits.integration.multi", opts...)

	return &client{
		multi: multiClient,
	}
}

func (client *client) GetIntegrationAllDevTaskList(ctx context.Context, id int64, brief bool) gresult.R[[]*multi.DevTaskInfo] {
	response, err := client.multi.GetIntegrationAllDevTaskList(ctx, &multi.GetIntegrationAllDevTaskListReq{
		IntegrationId: id,
		Brief:         &brief,
	})
	if err != nil {
		return gresult.Err[[]*multi.DevTaskInfo](err)
	}
	return gresult.OK(response.DevTaskList)
}

func (client *client) GetIntegrationTaskByIntegrationId(ctx context.Context, integrationId int64) gresult.R[*entity.DevIntegrationTask] {
	response, err := data.GetDevIntegrationTaskByIntegrationId(ctx, integrationId)
	if err != nil {
		return gresult.Err[*entity.DevIntegrationTask](err)
	}
	if response == nil {
		return gresult.Err[*entity.DevIntegrationTask](errors.New("empty response"))
	}
	return gresult.OK(response)
}
