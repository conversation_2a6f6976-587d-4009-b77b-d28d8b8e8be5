load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cd",
    srcs = [
        "api.go",
        "client.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/feature/pkg/backends/cd",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/cd:cd_go_proto_xrpc_and_kitex_CDRPC",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//infra/feature/pkg/backends/options",
        "@org_byted_code_lang_gg//gresult",
    ],
)
