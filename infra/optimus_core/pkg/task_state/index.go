package task_state

import (
	eventbus "code.byted.org/eventbus/client-go"
	"code.byted.org/eventbus/client-go/kitutil"
	"code.byted.org/eventbus/client-go/legacy"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bits_optimus_core/kitex_gen/bits/optimus/core"
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"code.byted.org/devinfra/hagrid/infra/optimus_core/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/optimus_core/pkg/data"
	"code.byted.org/devinfra/hagrid/infra/optimus_core/pkg/model"
	"code.byted.org/devinfra/hagrid/infra/optimus_core/pkg/service/core_state"
	"code.byted.org/devinfra/hagrid/infra/optimus_core/pkg/utils"
	"strings"
)

type taskData struct {
	Index      int   // 批处理的数据下标
	MaxOrderID int64 // 顺序
	Data       *legacy.ConsumerEvent
}

func BatchRun(ctx context.Context, events *eventbus.BatchEvent) *legacy.BatchError {
	batchError := eventbus.NewBatchError()
	errorsOrder := make([]error, events.BatchSize)

	// 找到每个business_id + task_name + action order_id 最大的那个任务
	tasks := make(map[int64]map[string]*taskData, 0)

	for idx, event := range events.Events {
		stateData := &model.OptimusCoreTaskStateModel{}
		_ = json.Unmarshal(event.Value, stateData)
		optimusCoreTaskData := stateData.OptimusCoreTaskModel

		businessID := optimusCoreTaskData.BusinessID
		action := optimusCoreTaskData.Action
		taskName := optimusCoreTaskData.TasksName
		orderID := optimusCoreTaskData.OrderID

		if tasks[businessID] == nil {
			tasks[businessID] = map[string]*taskData{}
		}
		key := fmt.Sprintf("%s@#@%s", taskName, action)
		if tasks[businessID][key] == nil {
			tasks[businessID][key] = &taskData{
				Index:      idx,
				MaxOrderID: optimusCoreTaskData.OrderID,
				Data:       event,
			}
		}

		if tasks[businessID][key].MaxOrderID < orderID {
			tasks[businessID][key].MaxOrderID = orderID
			tasks[businessID][key].Data = event
		}
	}

	for _, bData := range tasks {
		for key, event := range bData {
			arr := strings.Split(key, "@#@")
			if len(arr) < 2 {
				continue
			}
			action := arr[1]
			ctx = kitutil.NewCtxWithLogID(ctx, event.Data.Headers.LogId)
			logs.CtxInfo(ctx, "optimus_core_state consume event key:%s, data=%v, order_id=%d, logid=%s", action, string(event.Data.Value), event.MaxOrderID, event.Data.Headers.LogId)
			err := run(ctx, action, event.Data.Value)
			logs.CtxInfo(ctx, "optimus_core_state consume result event key:%s, data=%v, error:=%v", action, string(event.Data.Value), err)
			errorsOrder[event.Index] = err
		}
	}

	// 设置数据的错误和正确
	batchError.AddErrors(errorsOrder)
	return batchError
}

func run(ctx context.Context, action string, stateByteData []byte) error {
	stateData := &model.OptimusCoreTaskStateModel{}
	err := json.Unmarshal(stateByteData, stateData)
	if err != nil {
		logs.CtxInfo(ctx, err.Error())
	}

	// 更新最新状态
	hasError := int8(-1)
	downstreamResponse := stateData.DownStreamResponse
	upstreamTask := stateData.OptimusCoreTaskModel

	startTraceID := utils.GetTaskStartTraceID(stateData.Context, upstreamTask.TasksName)

	if len(startTraceID) == 0 {
		logs.CtxWarn(ctx, "start_trace_id is empty, context=%s, task_name=%s", stateData.Context, upstreamTask.TasksName)
		return nil
	}

	for _, message := range downstreamResponse.Data {
		if message.Level > consts.TASK_LOG_LEVEL_INFO {
			hasError = 1
		}
		core_state.BuildLog(ctx, stateData.TaskID, upstreamTask, message, startTraceID) // 记录log
	}
	isEnd := int8(-1)
	if downstreamResponse.FinalState {
		isEnd = 1
	}

	// 记录state
	flow, err := data.GetCustomByteFlowByByteFlowID(ctx, upstreamTask.StateMachineID, true)
	if err != nil {
		logs.CtxError(ctx, err.Error())
	}
	if flow == nil {
		return nil
	}
	if action == string(consts.TASK_ACTION_CLOSE) {
		isEnd = int8(core.TaskState_Closed)
	}
	err = core_state.UpdateState(ctx, upstreamTask.BusinessID, flow.ID, stateData.TaskID, isEnd, hasError, stateData.TraceID, startTraceID)
	if err != nil {
		logs.CtxError(ctx, err.Error())
	}

	return nil
}
