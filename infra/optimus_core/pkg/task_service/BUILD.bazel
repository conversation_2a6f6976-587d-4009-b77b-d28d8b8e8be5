load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "task_service",
    srcs = ["index.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/optimus_core/pkg/task_service",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/optimus_core/pkg/cache",
        "//infra/optimus_core/pkg/consts",
        "//infra/optimus_core/pkg/model",
        "//infra/optimus_core/pkg/service/core_task",
        "//infra/optimus_core/pkg/service/down_stream_call",
        "//infra/optimus_core/pkg/utils",
        "//infra/optimus_core/service/mq/optimus_core_state",
        "@com_github_bytedance_sonic//:sonic",
        "@in_gopkg_resty_v1//:resty_v1",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "task_service_test",
    srcs = ["index_test.go"],
    embed = [":task_service"],
    tags = ["known-to-fail"],
    deps = [
        "//infra/optimus_core/config",
        "//infra/optimus_core/pkg/model",
        "//infra/optimus_core/service/cache",
        "//infra/optimus_core/service/database",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
