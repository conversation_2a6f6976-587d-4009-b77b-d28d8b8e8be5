load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "database",
    srcs = [
        "index.go",
        "init.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/canal_delivery_binlog/service/database",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/canal_delivery_binlog/pkg/config",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_mysql_driver//:mysql-driver",
    ],
)
