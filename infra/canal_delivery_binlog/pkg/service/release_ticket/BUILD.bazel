load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "release_ticket",
    srcs = ["index.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/canal_delivery_binlog/pkg/service/release_ticket",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/canal_delivery_binlog/model",
        "//infra/canal_delivery_binlog/pkg/config",
        "//infra/canal_delivery_binlog/pkg/data",
        "//infra/canal_delivery_binlog/service/cache",
        "//infra/canal_delivery_binlog/service/mq/producer/bits_es",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/cd/release_ticket/sharedpb",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/cd/release_ticketpb",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_lang_gg//collection/set",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_rocketmq_rocketmq_go_proxy//pkg/types",
    ],
)

go_test(
    name = "release_ticket_test",
    srcs = ["index_test.go"],
    embed = [":release_ticket"],
    deps = [
        "//infra/canal_delivery_binlog/model",
        "//infra/canal_delivery_binlog/pkg/config",
        "//infra/canal_delivery_binlog/pkg/data",
        "//infra/canal_delivery_binlog/service/cache",
        "//infra/canal_delivery_binlog/service/mq/producer",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
    ],
)
