package binlog

import (
	"code.byted.org/devinfra/hagrid/infra/canal_delivery_binlog/pkg/config"
	"code.byted.org/gopkg/dbus"
	"code.byted.org/gopkg/logs"
	rocketConfig "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/config"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/log"
	"time"
)

type dbusHandler interface {
	dbus.MysqlIncrementEventHandler
	Tables() []string
}
type mqHandler struct {
	*config.MqConfig
	dbusHandler
}

func Start() {

	logConfig := log.NewDefaultRocketMQLogConfig()
	logConfig.Level = logs.LevelInfo
	logConfig.Logger = logs.DefaultLogger()
	log.InitRocketMQLog(logConfig)

	conf := rocketConfig.NewDefaultConsumerConfig(config.App.RocketMq.CanalDelivery.Consumer.Group, config.App.RocketMq.CanalDelivery.Consumer.Topic, config.App.RocketMq.CanalDelivery.Consumer.ClusterName)
	conf.RetryTimes = 3
	conf.MaxReconsumeTime = 3
	conf.RetryInterval = time.Second
	conf.Orderly = false
	conf.ClientConfigEpoch = time.Now().Unix()
	consumer := dbus.NewDefaultMysqlIncrementConsumerByConf(conf)
	ctrl := &CanalDeliveryMysqlHandler{}
	consumer.RegisterEventHandler(ctrl, ctrl.Tables()...)
	if err := consumer.Start(time.Millisecond * 200); err != nil {
		logs.Error("binlog_error:%s", err.Error())
		panic(err)
	} else {
		logs.Info("consumer stop without error.")
	}

}
