load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "track",
    srcs = [
        "api.go",
        "client.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/workflow/execution_engine/pkg/backends/track",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/workflow/execution_engine/kitex_gen/bits/track",
        "//infra/workflow/execution_engine/kitex_gen/bits/track/trackservice",
        "//libs/middleware/kitexmw",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_kite_kitex//client",
    ],
)
