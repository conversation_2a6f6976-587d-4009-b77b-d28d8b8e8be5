load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "cache",
    srcs = [
        "execution_access.go",
        "start_times.go",
        "task.go",
        "task_action_frequency.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/workflow/internal/dal/redis/cache",
    visibility = ["//infra/workflow:__subpackages__"],
    deps = [
        "//infra/workflow/internal/dal/redis/dictionary",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_kv_redis_v6//:redis-v6",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//optional",
    ],
)

go_test(
    name = "cache_test",
    srcs = [
        "task_action_frequency_test.go",
        "task_test.go",
    ],
    embed = [":cache"],
    deps = [
        "//infra/workflow/internal/dal/mysql/entity",
        "//infra/workflow/internal/dal/redis",
        "//infra/workflow/internal/dal/redis/dictionary",
        "//libs/connections/redis",
        "@com_github_stretchr_testify//assert",
    ],
)
