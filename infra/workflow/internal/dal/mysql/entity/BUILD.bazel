load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "entity",
    srcs = [
        "execution.go",
        "execution_task.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/workflow/internal/dal/mysql/entity",
    visibility = ["//infra/workflow:__subpackages__"],
    deps = [
        "@com_github_tidwall_gjson//:gjson",
        "@io_gorm_gorm//schema",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
        "@org_byted_code_byteflow_base//asl",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "entity_test",
    srcs = ["execution_test.go"],
    embed = [":entity"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
    ],
)
