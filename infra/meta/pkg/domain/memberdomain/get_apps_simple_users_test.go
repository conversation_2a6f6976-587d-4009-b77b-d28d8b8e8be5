package memberdomain

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/infra/meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	"github.com/stretchr/testify/assert"
)

func TestGetAppsSimpleUsers(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustConnectAtLocally()
	db := sqlite.MustInitialize()
	_ = db.AutoMigrate(new(entity.AppMember))
	appMemberRepository := repository.NewAppMemberRepository(db)
	domain := NewMemberDomain(rdb, db, db, nil)

	t.Run("从 db 中查询数据, 并根据 app id 返回用户", func(t *testing.T) {
		members := []*entity.AppMember{
			entity.NewAppMember(123, "<EMAIL>", entity.AppMemberRoleManager),
			entity.NewAppMember(234, "<EMAIL>", entity.AppMemberRoleManager),
			entity.NewAppMember(123, "<EMAIL>", entity.AppMemberRoleManager),
		}
		err := appMemberRepository.BatchCreate(ctx, members, 4)
		assert.Nil(t, err)

		defer func() {
			_ = appMemberRepository.DeleteByAppId(ctx, 123)
			_ = appMemberRepository.DeleteByAppId(ctx, 234)
		}()

		req := &meta.GetAppsSimpleUsersRequest{
			AppIds: []int64{123, 234},
			Role:   meta.AppMemberRole_Manager,
		}
		resp := domain.GetAppsSimpleUsers(ctx, req).Must()

		assert.Equal(t, 2, len(resp.AppMembers))
		assert.ElementsMatch(t, []string{"<EMAIL>", "<EMAIL>"}, resp.AppMembers[123])
		assert.ElementsMatch(t, []string{"<EMAIL>"}, resp.AppMembers[234])
	})
}
