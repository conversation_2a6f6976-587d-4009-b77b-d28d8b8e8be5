package authdomain

import (
	"context"
	"testing"
	"time"

	"code.byted.org/devinfra/hagrid/infra/meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	"github.com/stretchr/testify/assert"
)

func TestGetUserTokenInfo(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustConnectAtLocally()
	db := sqlite.MustInitialize()
	_ = db.AutoMigrate(new(entity.UserToken))
	userTokenRepository := repository.NewUserTokenRepository(db)
	domain := NewAuthDomain(rdb, db, nil, nil, nil)

	t.Run("当 request 为空的时候,返回 error¬", func(t *testing.T) {
		req := &meta.GetUserTokenInfoRequest{
			Username: "",
		}

		err := domain.GetUserTokenInfo(ctx, req).Err()

		assert.ErrorIs(t, err, bits_err.COMMON.ErrInvalidInput)
	})

	t.Run("如果可以从 db 中查询到 token, 那么得到对应的数据", func(t *testing.T) {
		token := entity.NewUserToken("a", entity.UserTokenTypeCodebase, "a", time.Now())
		id := userTokenRepository.Create(ctx, token).Must()
		defer func() { _ = userTokenRepository.DeleteById(ctx, id) }()

		req := &meta.GetUserTokenInfoRequest{
			Username:  "a",
			TokenType: meta.UserTokenType_codebase,
		}

		resp := domain.GetUserTokenInfo(ctx, req).Must()

		assert.Equal(t, 1, len(resp.UserTokenInfos))
	})
}
