package appdomain

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/infra/meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
)

func VerifyQueryAppSimpleInfoByIdRequest(req *meta.QueryAppSimpleInfoByIdRequest) error {
	if req.AppId <= 0 {
		return bits_err.COMMON.ErrInvalidInput
	}
	return nil
}

func (domain *AppDomain) QueryAppSimpleInfoById(ctx context.Context, req *meta.QueryAppSimpleInfoByIdRequest) gresult.R[*meta.QueryAppSimpleInfoByIdResponse] {
	if err := VerifyQueryAppSimpleInfoByIdRequest(req); err != nil {
		return gresult.Err[*meta.QueryAppSimpleInfoByIdResponse](err)
	}

	app, err := domain.appService.FindAppById(ctx, req.AppId).Get()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return gresult.Err[*meta.QueryAppSimpleInfoByIdResponse](bits_err.META.ErrAppNotFound)
		}
		log.V2.Info().With(ctx).Str("failed to find app").Error(err).KV("app id", req.AppId).Emit()
		return gresult.Err[*meta.QueryAppSimpleInfoByIdResponse](err)
	}

	resp := &meta.QueryAppSimpleInfoByIdResponse{
		AppInfo: TransformToAppSimpleInfo(app),
	}
	return gresult.OK(resp)
}
