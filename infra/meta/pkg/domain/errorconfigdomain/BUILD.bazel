load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "errorconfigdomain",
    srcs = ["domain.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/meta/pkg/domain/errorconfigdomain",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/meta/kitex_gen/bytedance/bits/meta",
        "//infra/meta/pkg/dal/mysql/entity",
        "//infra/meta/pkg/dal/mysql/repository",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "errorconfigdomain_test",
    srcs = ["domain_test.go"],
    embed = [":errorconfigdomain"],
    deps = [
        "//infra/meta/kitex_gen/bytedance/bits/meta",
        "//infra/meta/pkg/dal/mysql/entity",
        "//infra/meta/pkg/dal/mysql/repository",
        "//libs/connections/sqlite",
        "@com_github_stretchr_testify//assert",
    ],
)
