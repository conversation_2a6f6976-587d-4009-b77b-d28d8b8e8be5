/**
 * @Date: 2022/9/2
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package cache

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/mysql/entity"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
	"code.byted.org/kv/redis-v6"
	"code.byted.org/lang/gg/optional"
)

type AppInfoCache struct {
	rdb *goredis.Client
}

func NewAppInfoCache(rdb *goredis.Client) *AppInfoCache {
	return &AppInfoCache{
		rdb: rdb,
	}
}

func (cache *AppInfoCache) Create(ctx context.Context, id int64, value *entity.AppInfo, expiration time.Duration) {
	key1 := fmt.Sprintf("meta:app-info:id:%d", id)
	key2 := fmt.Sprintf("meta:app-info:english_name:%s", value.EnglishName)
	log.V2.Info().With(ctx).Str("[AppInfoCache.Create]").KVs("key1", key1, "key2", key2).Emit()

	pipeline := func(pipeliner redis.Pipeliner) error {
		if err := pipeliner.Set(key1, value, expiration).Err(); err != nil {
			return err
		}
		if err := pipeliner.Set(key2, value, expiration).Err(); err != nil {
			return err
		}

		return nil
	}

	_, err := cache.
		rdb.
		WithContext(ctx).
		Pipelined(pipeline)

	if err != nil {
		log.V2.Warn().With(ctx).Str("failed to create app cache").Error(err).KV("value", value).Emit()
	}
}

func (cache *AppInfoCache) FindById(ctx context.Context, id int64) optional.O[*entity.AppInfo] {
	key1 := fmt.Sprintf("meta:app-info:id:%d", id)
	log.V2.Info().With(ctx).Str("[AppInfoCache.FindById]").KVs("key1", key1).Emit()

	value := new(entity.AppInfo)
	err := cache.rdb.
		WithContext(ctx).
		Get(key1).
		Scan(value)

	if err != nil {
		log.V2.Info().With(ctx).Str("failed to find app").Error(err).KV("key", key1).Emit()
		return optional.Nil[*entity.AppInfo]()
	}
	return optional.OK(value)
}

func (cache *AppInfoCache) FindByEnglishName(ctx context.Context, englishName string) optional.O[*entity.AppInfo] {
	key2 := fmt.Sprintf("meta:app-info:english_name:%s", englishName)
	log.V2.Info().With(ctx).Str("[AppInfoCache.FindByEnglishName]").KVs("key2", key2).Emit()

	value := new(entity.AppInfo)
	err := cache.rdb.
		WithContext(ctx).
		Get(key2).
		Scan(value)

	if err != nil {
		log.V2.Info().With(ctx).Str("failed to find app").Error(err).KV("key", key2).Emit()
		return optional.Nil[*entity.AppInfo]()
	}
	return optional.OK(value)
}

func (cache *AppInfoCache) DeleteById(ctx context.Context, id int64) {
	cache.FindById(ctx, id).
		IfOK(func(value *entity.AppInfo) { cache.delete(ctx, value) })
}

func (cache *AppInfoCache) DeleteByEnglishName(ctx context.Context, englishName string) {
	cache.FindByEnglishName(ctx, englishName).
		IfOK(func(value *entity.AppInfo) { cache.delete(ctx, value) })
}

func (cache *AppInfoCache) delete(ctx context.Context, value *entity.AppInfo) {
	key1 := fmt.Sprintf("meta:app-info:id:%d", value.Id)
	key2 := fmt.Sprintf("meta:app-info:english_name:%s", value.EnglishName)

	err := cache.rdb.
		WithContext(ctx).
		Del(key1, key2).
		Err()
	if err != nil {
		log.V2.Warn().Str("failed to delete app cache").Error(err).KVs("key1", key1, "key2", key2).Emit()
	}
}
