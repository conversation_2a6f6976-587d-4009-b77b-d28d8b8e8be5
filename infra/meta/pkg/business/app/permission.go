package app

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/devinfra/hagrid/infra/meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/dal/kv"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/service/iam"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/service/member"
	organization2 "code.byted.org/devinfra/hagrid/infra/meta/pkg/service/organization"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/service/people"
	"code.byted.org/devinfra/hagrid/infra/meta/pkg/service/permission"
	common_utils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/facility/slice"
	"code.byted.org/gopkg/logs"
	"gorm.io/gorm"
)

const (
	USER_HAS_PERMISSION_FLAG = "true"
)

func QueryUserAppPermission(ctx context.Context, req *meta.QueryUserAppPermissionRequest) (*meta.QueryUserAppPermissionResponse, error) {
	resp := &meta.QueryUserAppPermissionResponse{
		HasPermission: false,
		RbacUrl:       "",
		KaniUrl:       "",
	}
	if env.IsBoe() { // 经过董雪商量，受表数据依赖影响，虽然基本完成，但直接跳过
		resp.HasPermission = true
		return resp, nil
	}

	// 检查参数，如果没有提供 AppID 就从 GroupName 转换过来
	if req.AppId == nil && req.GroupName == nil {
		rErr := fmt.Errorf("app_id and group_name cannnot both be nil")
		common_utils.LogCtxInfo(ctx, "query permission failed, err: %v", rErr)
		return resp, rErr
	}
	if req.AppId == nil {
		appInfos, err := QueryAppInfosByEnglishName(ctx, []string{*req.GroupName})
		if err != nil || len(appInfos) != 1 {
			rErr := fmt.Errorf("none of multi apps with group_name = %v", *req.GroupName)
			common_utils.LogCtxInfo(ctx, "query permission failed, err: %v", rErr)
			return resp, rErr
		}
		appId := appInfos[0].AppId
		req.AppId = &appId
	}
	username := emails.WithSuffix(req.User)

	if checkUserPermissionFromCache(ctx, username, req.Permission, *req.AppId) {
		resp.HasPermission = true
		common_utils.LogCtxInfo(ctx, "satisfy cache")
		return resp, nil
	}

	if checkIamPermission(ctx, req.GetAppId(), req.GetPermission(), username) {
		resp.HasPermission = true
		common_utils.LogCtxInfo(ctx, "%v has iam permission", username)
		return resp, nil
	}

	// 获取权限配置
	permissionConfig := permission.GetPermissionConfigByName(ctx, req.Permission)
	if permissionConfig == nil {
		rErr := fmt.Errorf("permission %v not found", req.Permission)
		common_utils.LogCtxInfo(ctx, "query permission failed, err: %v", rErr)
		return resp, rErr
	}

	// 填充两种权限申请地址
	resp.RbacUrl = GetAppPersonnelURL(*req.AppId)
	resp.KaniUrl = kaniRequestPermissionURL(*req.AppId, permissionConfig.KaniActionName)

	// 全局管理员
	admins := member.BitsAdmins(ctx)
	if slice.StringContains(admins, username) {
		resp.HasPermission = true
		markUserWithPermission(ctx, username, req.Permission, *req.AppId)
		common_utils.LogCtxInfo(ctx, "%v is global admin", username)
		return resp, nil
	}

	// 先检查权限的白名单
	if slice.StringContains(permissionConfig.WhiteList, username) {
		resp.HasPermission = true
		markUserWithPermission(ctx, username, req.Permission, *req.AppId)
		common_utils.LogCtxInfo(ctx, "satisfy permission whitelist")
		return resp, nil
	}

	// 再检查能不能用 RBAC 的模型满足权限
	if organization2.CheckUserHasRole(ctx, username, *req.AppId, permissionConfig.Roles) {
		resp.HasPermission = true
		markUserWithPermission(ctx, username, req.Permission, *req.AppId)
		common_utils.LogCtxInfo(ctx, "satisfy rbac model static roles")
		return resp, nil
	}

	if checkDepartmentWhiteList(ctx, permissionConfig.Departments, username) {
		resp.HasPermission = true
		markUserWithPermission(ctx, username, req.Permission, *req.AppId)
		common_utils.LogCtxInfo(ctx, "satisfy department whitelist")
		return resp, nil
	}

	return resp, nil
}

func checkIamPermission(ctx context.Context, appId int64, permission string, username string) bool {
	var IAMPermission string
	switch permission {
	case "mr":
		IAMPermission = "bits.space.view"
	default:
		return false
	}

	// 判断 IAM 是否有权限
	appInfo, err := db.BitsRead.GetAppInfoByIds(ctx, []int64{appId})
	if err == gorm.ErrRecordNotFound {
		return false
	}
	if err != nil {
		return false
	}
	authContext := make([]*authz.AuthzContext, 0)

	authContext = append(authContext, &authz.AuthzContext{
		Principal:   fmt.Sprintf("brn::iam:::user_account:%s", emails.TrimSuffix(username)),
		ResourceBrn: fmt.Sprintf("brn::bits:::space:%d", appInfo[0].SpaceId),
		Permission:  IAMPermission,
	})
	res, err := iam.QueryPermission(ctx, authContext)

	if len(res.GetResults()) != 0 && res.GetResults()[0].Result {
		return res.GetResults()[0].Result
	}
	return false
}

func checkDepartmentWhiteList(ctx context.Context, departments []string, username string) bool {
	if len(departments) == 0 {
		return false
	}
	result := getDepartmentWhiteListFromCache(ctx, departments, username)
	if result == "false" {
		return false
	}
	if result == "true" {
		return true
	}
	employee, err := people.GetEmployeesByEmail(ctx, username)
	if err != nil {
		common_utils.LogCtxInfo(ctx, "user = %v not found. err: %v", username, err)
		return false
	}

	contain := slice.StringContains(departments, employee.Department.Name)
	markDepartmentWhiteListFromCache(ctx, departments, username, contain)
	return contain
}

func getDepartmentWhiteListFromCache(ctx context.Context, departments []string, username string) string {
	key := fmt.Sprintf("%s%s", strings.Join(departments, ","), username)
	val, err := kv.GetMetaRedisClient().Get(key).Result()
	if err != nil {
		logs.CtxError(ctx, err.Error())
	}
	return val
}

func markDepartmentWhiteListFromCache(ctx context.Context, departments []string, username string, v bool) {
	flag := "false"
	if v {
		flag = "true"
	}
	key := fmt.Sprintf("%s%s", strings.Join(departments, ","), username)
	val, err := kv.GetMetaRedisClient().Set(key, flag, consts.CacheTimeoutDepartmentWhiteList).Result()
	logs.CtxInfo(ctx, "redis save username: %v with departments: %v. result: %v, err: %v", username, departments, val, err)
}

func userPermissionKey(username string, permissionName string, appID int64) string {
	return fmt.Sprintf("meta-permission-%v-%v-%d", username, permissionName, appID)
}

func checkUserPermissionFromCache(ctx context.Context, username string, permissionName string, appID int64) bool {
	key := userPermissionKey(username, permissionName, appID)
	val, err := kv.GetMetaRedisClient().Get(key).Result()
	if val == USER_HAS_PERMISSION_FLAG && err == nil {
		return true
	}
	return false
}

func markUserWithPermission(ctx context.Context, username string, permissionName string, appID int64) {
	key := userPermissionKey(username, permissionName, appID)
	val, err := kv.GetMetaRedisClient().Set(key, USER_HAS_PERMISSION_FLAG, consts.CacheTimeoutUserHasPermissionV2).Result()
	logs.CtxInfo(ctx, "redis save username: %v with permission: %v. result: %v, err: %v", username, permissionName, val, err)
}

func getKaniResourceKey(appId int64) string {
	if env.IsBoe() {
		return fmt.Sprintf("boe-app-%v", appId)
	}
	return fmt.Sprintf("app-%v", appId)
}

func kaniRequestPermissionURL(appId int64, action string) string {
	// 如果没有 action，比如不支持 kani 申请权限，就不让跳转了
	if action == "" {
		return ""
	}

	// 地址：https://{kani_host}/kani/v2/#/apply/workflow/?appId=718&resourceKey=app-20000009&action=read
	resourceKey := getKaniResourceKey(appId)
	return fmt.Sprintf("%v/#/apply/workflow/?appId=%v&resourceKey=%v&action=%v", consts.KaniFrontEnd, consts.OpenAppID, resourceKey, action)
}
