load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "team",
    srcs = ["team.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/meta/pkg/business/team",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/meta/kitex_gen/bytedance/bits/meta",
        "//infra/meta/pkg/adaptor",
        "//infra/meta/pkg/dal/db",
        "//infra/meta/pkg/lib",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_lang_gg//gslice",
    ],
)
