/**
 * @Date: 2023/1/29
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package configservice

import json "github.com/bytedance/sonic"

type ConySettings struct {
	CalendarWorkspaceId int64 `json:"calendar_workspace_id"`
}

func GetConySettings(conySettings string) *ConySettings {
	settings := new(ConySettings)
	_ = json.UnmarshalString(conySettings, settings)
	return settings
}
