package db

import (
	"code.byted.org/devinfra/hagrid/infra/cronjob/pkg/conf"

	db_conf "code.byted.org/gopkg/dbutil/conf"
	"code.byted.org/gopkg/dbutil/gormdb"
	"code.byted.org/gopkg/gorm"
)

var (
	Read  Repo
	Write Repo
)

func Init(cfg *conf.MysqlConfig) {
	Write = getDBRepo(&cfg.Master)
	Read = getDBRepo(&cfg.Slave)
}

func getDBRepo(cfg *conf.MysqlClientConfig) Repo {
	dbHandler := getDBHandler(cfg)
	db, err := dbHandler.GetConnection()
	if err != nil {
		panic(err)
	}
	db.LogMode(true)
	db.Callback().Create().Replace("gorm:update_time_stamp", updateTimeStampForCreateCallback)
	db.Callback().Update().Replace("gorm:update_time_stamp", updateTimeStampForUpdateCallback)

	repo := NewRepo(db)
	repo.SetDBClient(db)
	return repo
}

func getDBHandler(conf *conf.MysqlClientConfig) *gormdb.DBHandler {
	var option db_conf.DBOptional
	if conf.Psm != "" {
		option, _ = db_conf.GetDBOptionalByConsulName(conf.Psm)
	} else {
		option = db_conf.GetDefaultDBOptional()
		option.User = conf.Username
		option.Password = conf.Password
		option.DBHostname = conf.Host
		option.DBPort = conf.Port
	}
	option.DBName = conf.DB
	option.DBCharset = "utf8mb4"
	option.Timeout = "3.0s"
	option.ReadTimeout = "3.0s"
	option.WriteTimeout = "5.0s"

	return gormdb.NewDBHandlerWithOptional(&option)
}

func updateTimeStampForUpdateCallback(scope *gorm.Scope) {
	if _, ok := scope.Get("gorm:update_column"); !ok {
		_ = scope.SetColumn("Mtime", gorm.NowFunc())
	}
}

func updateTimeStampForCreateCallback(scope *gorm.Scope) {
	if !scope.HasError() {
		now := gorm.NowFunc()
		if createdAtField, ok := scope.FieldByName("Ctime"); ok {
			if createdAtField.IsBlank {
				_ = createdAtField.Set(now)
			}
		}

		if updatedAtField, ok := scope.FieldByName("MTime"); ok {
			if updatedAtField.IsBlank {
				_ = updatedAtField.Set(now)
			}
		}
	}
}
