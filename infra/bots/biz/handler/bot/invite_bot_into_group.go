package bot

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/logs"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
	"code.byted.org/devinfra/hagrid/infra/bots/biz/handler"
)

type inviteBotIntoGroupRequest struct {
	ChatID string `json:"chat_id"`
	AppID  string `json:"app_id"`
}

func InviteBotIntoGroup(ctx context.Context, c *app.RequestContext) handler.HttpResult {
	var req inviteBotIntoGroupRequest
	if binding.BindAndValidate(c, &req) != nil {
		return handler.NewHttpResultObj(http.StatusBadRequest, "参数错误", nil)
	}

	doudou, err := GetDouDou()
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return handler.NewHttpResultObj(http.StatusInternalServerError, err.Error(), nil)
	}

	err = doudou.InviteBotIntoGroup(ctx, req.ChatID, req.AppID)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return handler.NewHttpResultObj(http.StatusInternalServerError, err.Error(), nil)
	}
	return handler.NewHttpResultObj(http.StatusOK, "ok", nil)
}
