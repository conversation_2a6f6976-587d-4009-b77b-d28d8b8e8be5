package rpc

import (
	"time"

	"code.byted.org/devinfra/hagrid/infra/dependency_parse/kitex_gen/bytedance/bits/dev_task/devtaskservice"
	"code.byted.org/devinfra/hagrid/infra/dependency_parse/kitex_gen/bytedance/bits/devops_settings/devopssettingsservice"
	"code.byted.org/devinfra/hagrid/infra/dependency_parse/kitex_gen/bytedance/bits/git_server/gitservice"
	"code.byted.org/devinfra/hagrid/infra/dependency_parse/kitex_gen/bytedance/bits/meta/metaservice"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/kite/kitex/client"
)

var GitServerClient gitservice.Client
var MetaClient metaservice.Client
var DevopsSettingsClient devopssettingsservice.Client
var DevTaskClient devtaskservice.Client

func Init() {
	options := []client.Option{
		client.WithRPCTimeout(300 * time.Second),
		client.WithMiddleware(kitexmw.LogClientSideRequestResponse),
	}

	MetaClient = metaservice.MustNewClient("bytedance.bits.meta", options...)
	GitServerClient = gitservice.MustNewClient("bytedance.bits.git_server", options...)
	DevopsSettingsClient = devopssettingsservice.MustNewClient("bytedance.bits.devops_settings", options...)
	DevTaskClient = devtaskservice.MustNewClient("bytedance.bits.dev_task", options...)
}
