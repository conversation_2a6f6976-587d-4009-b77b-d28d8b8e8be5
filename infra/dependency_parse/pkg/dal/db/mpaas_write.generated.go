package db

import (
	"context"
	"errors"

	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

type _DBMpaasWriteInterfaceStruct struct {
	handler *gorm.DB
}

func NewDBMpaasWriteInterface(handler *gorm.DB) DBMpaasWriteInterface {
	return &_DBMpaasWriteInterfaceStruct{
		handler: handler,
	}
}

var _CacheKeyDBMpaasWriteInterfaceTemplate = struct {
}{}
var _DBMpaasWriteInterfaceTemplate = struct {
}{}
var GlobalErrDBMpaasWriteInterface = struct {
	CacheDemotionErr        error
	DBDemotionErr           error
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
}{
	errors.New("CacheDemotionError"),
	errors.New("DBDemotionError"),
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
}

func (interstruct *_DBMpaasWriteInterfaceStruct) CreatePatchShadowBranch(ctx context.Context, record *MpaasPodfilePatchShadowBranches) (int64, error) {
	if record == nil {
		return 0, GlobalErrDBMpaasWriteInterface.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Create(record)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "DBMpaasWriteInterface.CreatePatchShadowBranch occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
