package main

import (
	"context"
	"errors"
	"math/rand"

	"code.byted.org/bytefaas/faas-go/bytefaas"
	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/devinfra/hagrid/infra/message_sender/config"
	"code.byted.org/devinfra/hagrid/infra/message_sender/service/lark"
	"code.byted.org/devinfra/hagrid/infra/message_sender/service/redis"
	"code.byted.org/devinfra/hagrid/infra/message_sender/service/trace"
	_ "code.byted.org/devinfra/hagrid/libs/prelude"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gptr"
	"github.com/bytedance/sonic"
)

type Message struct {
	TargetId          string          `json:"target_id"`
	TargetIdType      string          `json:"target_id_type"` // 目前客户端为空
	Type              lark.TargetType `json:"type"`
	Content           string          `json:"content"`
	IsUrgent          bool            `json:"is_urgent"`
	MessageId         string          `json:"message_id"`
	MessageType       string          `json:"message_type"`          // 目前客户端为空
	Bot               string          `json:"bot"`                   // 目前客户端为空
	NeedSaveLarkMsgId bool            `json:"need_save_lark_msg_id"` // 目前客户端为空，收否保存 lark 返回的 open message id
}

// for default http case
func sendMessage(ctx context.Context, r *events.CloudEvent) (*events.EventResponse, error) {
	requestId := rand.Intn(int(^uint(0) >> 1))
	var body []byte
	switch r.Type() {
	case events.RocketMQEvent:
		resp, err := sendMessageFromRocketMq(ctx, r, requestId)
		if err != nil {
			body, _ = sonic.Marshal(map[string]any{"message": err})
		} else {
			// history reason, just return like this
			body, _ = sonic.Marshal(map[string]any{"code": 200, "message": "Send successfully", "message_id": resp.MessageID})
		}
	default:
		log.V2.Warn().With(ctx).Str("unknown event type").Emit()
	}
	return &events.EventResponse{
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body:       body,
		StatusCode: 200,
	}, nil
}

type SendMessageResp struct {
	MessageID string `json:"message_id"`
}

func sendMessageFromRocketMq(ctx context.Context, r *events.CloudEvent, requestId int) (*SendMessageResp, error) {
	dataBytes, err := r.DataBytes()
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to get dataBytes").Error(err).Emit()
		return nil, err
	}
	message := Message{}
	err = sonic.Unmarshal(dataBytes, &message)
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to unmarshal message").Error(err).Emit()
		return nil, err
	}
	if redis.IsDuplicated(ctx, conv.UnsafeBytesToString(dataBytes)) {
		log.V2.Error().With(ctx).Str("failed to send message, error: duplicated content").Emit()
		return nil, errors.New("failed to send message, error: duplicated content")
	}
	content, err := lark.JudgeAndAdaptMsgContent(message.MessageType, message.Content)
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to adapt message content").Error(err).Emit()
		return nil, err
	}
	// send msg
	ret, err := lark.SendMessage(ctx, message.TargetId, message.TargetIdType, content, message.Type, requestId, message.MessageId, message.MessageType, message.Bot)
	if err != nil {
		if message.MessageId != "" {
			trace.SendTrace(message.MessageId, message.TargetId, content, trace.SendResult_Failed, err.Error())
		}
		log.V2.Error().With(ctx).Str("failed to send message").Error(err).KVs("request id", requestId, "message id", message.MessageId).Emit()
		return nil, err
	}
	redis.SentFlag(ctx, conv.UnsafeBytesToString(dataBytes))
	// save lark msg id if it needs, now only save 10 minutes for default
	if message.NeedSaveLarkMsgId && message.MessageId != "" && ret != nil && ret.MessageId != nil {
		_ = redis.SaveLarkMsgId(ctx, message.MessageId, gptr.Indirect(ret.MessageId))
	}
	// 加急
	if ret != nil && ret.MessageId != nil && message.IsUrgent && message.Type == lark.TARGET_TYPE_USER {
		if err := lark.UrgentMessage(ctx, *ret.MessageId, message.TargetId, message.Bot); err != nil {
			log.V2.Error().With(ctx).Str("failed to urgent message").Error(err).KVs("request id", requestId, "message id", message.MessageId).Emit()
		} else {
			logs.V2.Info().With(ctx).Str("message urgent succeeded").KVs("request id", requestId, "message id", message.MessageId).Emit()
		}
	}
	trace.SendTrace(message.MessageId, message.TargetId, content, trace.SendResult_Success, "")
	return &SendMessageResp{MessageID: message.MessageId}, nil
}

func main() {
	InitializeLogger()

	// Make the handler available for Http Call by ByteFaaS Function
	config.Init()
	lark.Init()
	redis.Init()
	bytefaas.Start(sendMessage)
}

func InitializeLogger() {
	log.SetDefaultLogger(
		logs.SetPSM(env.PSM()),
		logs.SetCallDepth(2),
		logs.SetFullPath(),
		logs.SetKVPosition(logs.AfterMsg),
		logs.SetDisplayEnvInfo(true),
		logs.SetDisplayFuncName(true),
	)
}
