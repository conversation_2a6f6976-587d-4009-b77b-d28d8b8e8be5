package handlers

import (
	"context"
	json "github.com/bytedance/sonic"
	"code.byted.org/devinfra/hagrid/infra/track/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/track/pkg/handlers/core_task"

	"code.byted.org/gopkg/logs"
)

func MRCoreController(ctx context.Context, action string, data []byte) error {
	if consts.MrFunMap[action] != nil {
		return consts.MrFunMap[action](ctx, data)
	}
	if consts.ReviewFunMap[action] != nil {
		return consts.ReviewFunMap[action](ctx, data)
	}
	if consts.ComponentFunMap[action] != nil {
		return consts.ComponentFunMap[action](ctx, data)
	}
	return nil
}

func CoreTaskHandler(ctx context.Context, key, data []byte) error {
	logs.CtxInfo(ctx, "event key: %s", string(key))
	task := &core_task.CoreTask{}
	err := json.Unmarshal(data, task)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshal task json: %s,%s", err.Error(), string(data))
		return nil // 无论重试多少次都木大，所以直接return
	}
	switch task.TasksName {
	default:
		return core_task.LockTimeoutHandler(ctx, task)
	}
}

func TestCoreTask() {
	const data = `{
	"action": "is_end",
	"business_id": 4160296,
	"context": "{\"CanMergeTask_is_retry\":false,\"CanMergeTask_ready_for_merge\":false,\"CanMergeTask_ready_for_merge_time\":0,\"CanMergeTask_start_trace_id\":\"1634812879864085904\",\"CodeReviewTask_is_retry\":false,\"CodeReviewTask_start_trace_id\":\"1634812879326255950\",\"ConflictDetectNeedRetryTask___need_retry\":false,\"ConflictDetectNeedRetryTask_start_trace_id\":\"1634812927568102397\",\"ConflictDetectTask_is_retry\":false,\"ConflictDetectTask_start_trace_id\":\"1634812914645533327\",\"DependencyPipelineTask_start_trace_id\":\"1634812879165094099\",\"HostPipelineTask_start_trace_id\":\"1634813455675141336\",\"SecurityCheckTask_start_trace_id\":\"1634812879711642836\",\"ShadowBranchTask_start_trace_id\":\"1634813446155418412\",\"TTModuleSnapshotTask_is_retry\":false,\"TTModuleSnapshotTask_start_trace_id\":\"1634812879597828927\",\"TTModuleSnapshotTask_tt_task_start_time\":1634812887,\"byteflow_execution\":\"1451134448001630209\",\"lock\":{\"lock_time\":1634792946,\"lock_type\":\"atomic\",\"locked\":true},\"task_error_map\":{\"HostPipelineTask\":[{\"message\":\"113818 host pipeline failed\",\"name\":\"err_host_pipeline_failed\",\"title\":\"err_host_pipeline_failed\"}],\"TTModuleSnapshotTask\":[{\"message\":\"115338:3827 publish repo not end\",\"name\":\"err_publish_repo_not_end\",\"title\":\"err_publish_repo_not_end\"}]}}",
	"is_delayed_host_lock": false,
	"order_id": 146018738,
	"state_machine_id": 123,
	"tasks_name": "ConflictDetectNeedRetryTask",
	"trace_id": "b2d84038-3260-11ec-8f1b-0c42a1a6811e"
}`
	const key = `123`
	CoreTaskHandler(context.Background(), []byte(key), []byte(data))
}
