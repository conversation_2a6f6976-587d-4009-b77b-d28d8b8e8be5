package data

import (
	"context"
	"database/sql"
	"code.byted.org/devinfra/hagrid/infra/track/pkg/model"
	"code.byted.org/devinfra/hagrid/infra/track/service/database"
	"time"

	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bytedance_bits_optimus/rpc/bytedance_bits_optimus"
)

func UpdateMrLockTimeout(ctx context.Context, mrId int64, lockTime time.Time, callback func()) {
	defer callback()
	mrTimeout := model.NewOptimusMrTimeout()
	res := database.CommonData.Master.NewRequest(ctx).Where("mr_id = ?", mrId).Find(&mrTimeout)
	if res.Error != nil && res.Error != gorm.ErrRecordNotFound && res.Error != sql.ErrNoRows {
		logs.CtxError(ctx, "failed to get mr timeout:%s", res.Error.Error())
		return
	}
	if res.Error == gorm.ErrRecordNotFound || res.Error == sql.ErrNoRows {
		logs.CtxInfo(ctx, "mr timeout not exist, create...")
		mrInfo, err := bytedance_bits_optimus.GetMainMrInfo(ctx, mrId)
		if err != nil {
			logs.CtxError(ctx, "failed to get mr info:%s", err.Error())
			return
		}
		if mrInfo == nil || mrInfo.Info == nil {
			logs.CtxError(ctx, "failed to get mr info: mr not exist")
			return
		}
		mrTimeout.MrId = mrId
		mrTimeout.MrTitle = mrInfo.Info.MrTitle
		mrTimeout.Author = mrInfo.Info.AuthorName
		mrTimeout.TimeoutReason = model.TimeoutReason_Lock
		mrTimeout.StartTime = lockTime
		mrTimeout.Timeout = time.Now().Sub(lockTime)
		res = database.CommonData.Master.NewRequest(ctx).Create(mrTimeout)
		if res.Error != nil {
			logs.CtxError(ctx, "failed to create mr timeout:%s", res.Error.Error())
			return
		}
	} else {
		logs.CtxInfo(ctx, "update mr timeout:%d,mr_id:%d", mrTimeout.Id, mrId)
		mrTimeout.Timeout = time.Now().Sub(lockTime)
		res = database.CommonData.Master.NewRequest(ctx).Table(mrTimeout.TableName()).Where("mr_id = ?", mrId).Update(mrTimeout)
		if res.Error != nil {
			logs.CtxError(ctx, "failed to update mr timeout:%s", res.Error.Error())
			return
		}
	}
}
