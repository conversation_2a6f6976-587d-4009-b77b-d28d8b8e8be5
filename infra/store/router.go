package main

import (
	"code.byted.org/devinfra/hagrid/infra/store/biz/handler"
	"code.byted.org/devinfra/hagrid/infra/store/biz/handler/http/cache"
	"code.byted.org/devinfra/hagrid/infra/store/biz/handler/http/tos"

	"code.byted.org/gin/ginex"
)

// register register routes based on the IDL 'api.${HTTP Method}' annotation.
func register(r *ginex.Engine) {
	store := r.Group("/store")
	{
		store.POST("/ping", handler.Http<PERSON>uncHandler(handler.Ping))

		tosStore := store.Group("/tos")
		{
			tosStore.POST("/anonymous_file", handler.<PERSON>ttp<PERSON>uncHandler(tos.AnonymousFile))
			tosStore.POST("/anonymous_bytes", handler.Http<PERSON>uncHandler(tos.AnonymousBytes))
			tosStore.POST("/qrcode", handler.<PERSON>ttp<PERSON>un<PERSON><PERSON><PERSON><PERSON>(tos.QRCode))

		}

		cacheStore := store.Group("/cache")
		{
			cacheStore.POST("/redis_set", handler.<PERSON>tt<PERSON><PERSON>un<PERSON><PERSON>and<PERSON>(cache.RedisSet))
		}
	}
}
