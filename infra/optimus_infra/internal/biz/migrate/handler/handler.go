package handler

import (
	"context"

	"code.byted.org/gopkg/dbus"
)

type Handler interface {
	// 能否处理表
	CanHandleTable(table string) bool

	// 增量数据迁移
	HandleInsert(ctx context.Context, table string, columns map[string]dbus.MysqlIncrementColumn) error
	HandleDelete(ctx context.Context, table string, columns map[string]dbus.MysqlIncrementColumn) error
	HandleUpdate(ctx context.Context, table string, beforeColumns, afterColumns map[string]dbus.MysqlIncrementColumn) error

	// 存量数据迁移
	HandleStockDataMigration(ctx context.Context, table string, action string, start int64, count int64) error

	// mr创建编辑事件同步
	HandleMergeRequestSync(ctx context.Context, mrId int64) error
}

type BaseHandler struct {
}

func (h *BaseHandler) CanHandleTable(table string) bool {
	return false
}
func (h *BaseHandler) HandleInsert(ctx context.Context, table string, columns map[string]dbus.MysqlIncrementColumn) error {
	return nil
}
func (h *BaseHandler) HandleDelete(ctx context.Context, table string, columns map[string]dbus.MysqlIncrementColumn) error {
	return nil
}
func (h *BaseHandler) HandleUpdate(ctx context.Context, table string, beforeColumns, afterColumns map[string]dbus.MysqlIncrementColumn) error {
	return nil
}
func (h *BaseHandler) HandleMergeRequestSync(ctx context.Context, mrId int64) error {
	return nil
}
func (h *BaseHandler) HandleStockDataMigration(ctx context.Context, table string, action string, start int64, count int64) error {
	return nil
}
