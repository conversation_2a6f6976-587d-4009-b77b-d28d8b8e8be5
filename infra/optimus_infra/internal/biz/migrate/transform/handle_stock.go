package transform

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/devinfra/hagrid/infra/optimus_infra/internal/binlog"
	"code.byted.org/devinfra/hagrid/infra/optimus_infra/internal/data/devrepo"
	"code.byted.org/devinfra/hagrid/infra/optimus_infra/internal/data/oldrepo"
	"code.byted.org/devinfra/hagrid/infra/optimus_infra/kitex_gen/bits/optimus/infra"
	"code.byted.org/devinfra/hagrid/libs/common_lib/consts"
	"code.byted.org/devinfra/hagrid/libs/common_lib/model"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils_notification"
)

// 存量数据迁移
func (h *<PERSON>ler) HandleStockDataMigration(ctx context.Context, table string, action string, start int64, count int64) error {
	switch table {
	case binlog.Optimus_gitlab_mergerequest:
		return h.handleMergeRequestMigrate(ctx, start, count)
	}
	return nil
}

func (h *Handler) handleSaveWebhook(ctx context.Context, dev *devrepo.Dev, dependencies []*devrepo.Dependency, relations []*devrepo.Relation) {
	if dev == nil || len(dependencies) == 0 {
		return
	}
	// 记录哪些依赖被处理了
	kvs := make(map[string]interface{})
	for _, item := range dependencies {
		kvs[devrepo.DependencyPK(item.DepId, item.DepType)] = dev.Id
	}
	h.cache.MSet(ctx, kvs)
}

func (h *Handler) HandleMergeRequestSync(ctx context.Context, mrID int64) error {
	mr, err := h.repo.GetMergeRequest(ctx, mrID)
	if err != nil {
		return err
	}

	if mr.HostMrId != mr.Id { // 忽略非host mr
		return nil
	}

	return h.handleMergeRequestData(ctx, mr)
}

func (h *Handler) handleMergeRequestMigrate(ctx context.Context, start int64, count int64) error {
	logs.CtxInfo(ctx, "handleMergeRequestMigrate start: %d, count: %d", start, count)

	startTime := time.Now().Unix()
	lastNow := startTime
	var lastId, migrateCount int64
	err := h.repo.BatchGetMergeRequest(ctx, start, count, func(r *oldrepo.Repo, results []*oldrepo.OptimusGitlabMergerequest) error {
		for _, item := range results {
			lastId = item.Id
			migrateCount++

			pk := devrepo.DependencyPK(item.Id, int(infra.DependencyType_MR))
			if val, _ := h.cache.Get(ctx, pk); val != nil { // 已经处理过了, 忽略
				continue
			}
			if err := h.handleMergeRequestData(ctx, item); err != nil {
				logs.CtxError(ctx, "handleMergeRequestMigrate err: %v, mrId: %d", err, item.Id)
				return err
			}
		}

		// 每60s发送一次进度
		now := time.Now().Unix()
		if now-lastNow > 60 {
			h.sendMigrateProgressReport(ctx, start, count, lastId, migrateCount)
			lastNow = now
		}
		return nil
	})

	endTime := time.Now().Unix()
	duration := endTime - startTime
	h.sendMigrateResultReport(ctx, start, count, lastId, migrateCount, nil, duration)
	return err
}

// 发送任务进度报告
func (h *Handler) sendMigrateProgressReport(ctx context.Context, start int64, count int64, lastId int64, migrateCount int64) {
	template := "blue"
	contents := []interface{}{
		fmt.Sprintf("start id: %d, count: %d", start, count),
		fmt.Sprintf("current id: %d, current count: %d", lastId, migrateCount),
	}

	message := model.NotificationMessage{
		Targets: []model.NotificationTarget{
			{
				Type:   consts.NotificationTargetType_UserEmail,
				Target: "<EMAIL>",
			},
		},
		Title:    "Progress of migration of new model stock data",
		Template: template,
		Content:  contents,
	}
	utils_notification.SendNotificationMessage(ctx, message)
}

// 发送任务结果报告
func (h *Handler) sendMigrateResultReport(ctx context.Context, start int64, count int64, lastId int64, migrateCount int64, err error, duration int64) {
	template := "green"
	contents := []interface{}{
		fmt.Sprintf("start id: %d, count: %d", start, count),
		fmt.Sprintf("last id: %d, migrate count: %d", lastId, migrateCount),
		fmt.Sprintf("duration: %ds", duration),
	}

	if err != nil {
		template = "red"
		contents = append(contents, fmt.Sprintf("migrate %d err: %v", lastId, err))
	}

	message := model.NotificationMessage{
		Targets: []model.NotificationTarget{
			{
				Type:   consts.NotificationTargetType_UserEmail,
				Target: "<EMAIL>",
			},
		},
		Title:    "Results of migration of new model stock data",
		Template: template,
		Content:  contents,
	}
	utils_notification.SendNotificationMessage(ctx, message)
}
