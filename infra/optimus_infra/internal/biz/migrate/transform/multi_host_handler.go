package transform

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/optimus_infra/internal/data/oldrepo"
)

type multiHostHandler struct {
	repo  *oldrepo.Repo
	saver *devSaver
}

func newMultiHostHandler(repo *oldrepo.Repo, saver *devSaver) *multiHostHandler {
	return &multiHostHandler{repo: repo, saver: saver}
}

// 多宿主mr
func (h *multiHostHandler) handleInsert(ctx context.Context, optimusMr *oldrepo.OptimusMergerequest) error {
	if optimusMr == nil {
		return nil
	}
	relation := newMrRelation(optimusMr.Relation)
	if relation == nil {
		return nil
	}

	allMrIds := relation.allMrIds()
	if len(allMrIds) == 0 {
		return nil
	}
	mrs, _ := h.repo.GetMergeRequests(ctx, allMrIds)
	if len(allMrIds) != len(mrs) {
		return nil
	}

	mrDetail := newMultiHostMrDetail(relation, mrs)
	if mrDetail == nil {
		return nil
	}

	deps, _ := h.repo.GetDependenciesByQuery(ctx, mrDetail.allHostMrQuery())
	if len(deps) != len(mrDetail.Hosts) { // 应该相等
		return nil
	}
	vds, _ := h.repo.GetHostsVersionDependencies(ctx, hostIdForDeps(deps))
	mrDetail.setHostPods(deps, vds)
	groupIds := getMrProjectGroupIds(ctx, h.repo, mrs)

	dev := mrDetail.getDev(groupIds)
	return h.saver.save(ctx,
		dev,
		mrDetail.getDevDependencies(dev.Id, groupIds),
		mrDetail.getDevRelations(dev.Id),
		allMrIds,
	)
}

func (h *multiHostHandler) handleDelete(ctx context.Context, optimusMr *oldrepo.OptimusMergerequest) error {
	relation := newMrRelation(optimusMr.Relation)
	if relation == nil {
		return nil
	}

	return h.saver.delete(ctx, relation.hostMrId())
}

func hostIdForDeps(deps []*oldrepo.OptimusGitlabMergerequestdependency) []int64 {
	var hostIds []int64
	for _, item := range deps {
		if item.HostId > 0 {
			hostIds = append(hostIds, item.HostId)
		} else {
			hostIds = append(hostIds, item.Id)
		}
	}
	return hostIds
}
