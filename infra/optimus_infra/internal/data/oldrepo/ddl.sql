CREATE TABLE `optimus_gitlab_mergerequest` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time` datetime(6) DEFAULT NULL,
    `update_time` datetime(6) DEFAULT NULL,
    `title` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `author_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `work_in_progress` tinyint(1) NOT NULL,
    `state` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `mr_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `project_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `iid` bigint(20) NOT NULL,
    `mr_role` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'mr身份',
    `optimus_mr_id` bigint(20) unsigned DEFAULT NULL COMMENT 'optimus mr id',
    `host_mr_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'host mr id',
    `mr_change_status` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'mr 编辑状态',
    dev_id int null comment '研发任务id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `optimus_gitlab_versiondependency` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time` datetime(6) DEFAULT NULL,
    `host_id` bigint(20) unsigned NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `optimus_mergerequest` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `relation` json DEFAULT NULL COMMENT '关系',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='optimus mr';

CREATE TABLE `optimus_gitlab_mergerequestdependency` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time` datetime(6) DEFAULT NULL,
    `project_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `iid` bigint(20) NOT NULL,
    `host_id` bigint(20) unsigned DEFAULT NULL,
    `mr_role` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'mr身份',
    `optimus_mr_id` bigint(20) unsigned DEFAULT NULL COMMENT 'optimus mr id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1041405 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `optimus_config_project` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `project_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
    `main_group_config_id` bigint(20) unsigned DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_project_id` (`project_id`),
    KEY `idx_config_project_main_group_config_id_config_group_id` (`main_group_config_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;