package devrepo

import (
	"gorm.io/plugin/dbresolver"

	"gorm.io/gorm"
)

type Repo struct {
	db *gorm.DB
}

func NewRepo(db *gorm.DB) *Repo {
	return &Repo{
		db: db,
	}
}

// 默认是自动读写分离, 如果读方法想强制使用写库用这个
func (r *Repo) Writer() *Repo {
	return NewRepo(r.db.Clauses(dbresolver.Write))
}

// 使用事务
func (r *Repo) Transaction(fn func(Repo *Repo) error) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		return fn(NewRepo(tx))
	})
}
