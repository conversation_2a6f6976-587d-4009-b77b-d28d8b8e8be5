package cache

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/infra/optimus_infra/pkg/cache"
)

// 缓存根据dep查找devId的结果, 这个结果基本不变, 可以长时间保存
var depDevIdCache = cache.NewLocalSimpleCache(10000, time.Hour)

// 根据dep id + dep type查找 dev id
func GetDevIdForDep(ctx context.Context, depId int64, depType int, onMiss func() (int64, error)) (int64, error) {
	cacheKey := fmt.Sprintf("%d_%d", depId, depType)
	val, err := depDevIdCache.Get(ctx, cacheKey, func() (interface{}, error) {
		return onMiss()
	})
	if val != nil {
		devId, _ := val.(int64)
		return devId, nil
	}
	return 0, err
}
