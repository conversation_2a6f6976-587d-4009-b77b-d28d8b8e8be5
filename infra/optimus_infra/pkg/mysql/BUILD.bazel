load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mysql",
    srcs = ["mysql.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/optimus_infra/pkg/mysql",
    visibility = ["//visibility:public"],
    deps = [
        "@io_gorm_driver_mysql//:mysql",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_gorm//logger",
        "@org_byted_code_gopkg_bytedmysql//:bytedmysql",
        "@org_byted_code_gorm_bytedgorm//:bytedgorm",
    ],
)
