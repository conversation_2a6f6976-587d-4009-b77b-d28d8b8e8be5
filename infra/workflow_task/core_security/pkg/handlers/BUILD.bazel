load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "handlers",
    srcs = [
        "grey_strategy.go",
        "task_action_handler.go",
        "task_worker_handler.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/workflow_task/core_security/pkg/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/workflow_task/core_security/pkg/consts",
        "//infra/workflow_task/core_security/pkg/dal",
        "//infra/workflow_task/core_security/service/legacy_task",
        "//infra/workflow_task/core_security/service/metrics",
        "//infra/workflow_task/core_security/service/tcc",
        "//infra/workflow_task/core_security/utils",
        "//infra/workflow_task/core_task_common/task",
        "//infra/workflow_task/core_task_common/utils",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_eventbus_client_go//:client-go",
        "@org_byted_code_gopkg_facility//fjson",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_overpass_bits_devops_core_security//kitex_gen/core_task_common",
    ],
)

go_test(
    name = "handlers_test",
    srcs = ["grey_strategy_test.go"],
    embed = [":handlers"],
    tags = ["known-to-fail"],
    deps = [
        "//infra/workflow_task/core_security/pkg/config",
        "//infra/workflow_task/core_security/pkg/consts",
        "//infra/workflow_task/core_security/pkg/dal",
        "//infra/workflow_task/core_security/service/tcc",
    ],
)
