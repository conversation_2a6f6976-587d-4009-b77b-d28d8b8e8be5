package model

import (
	"time"
)

func (MRDependency) TableName() string {
	return "optimus_gitlab_mergerequestdependency"
}

func NewMRDependency() *MRDependency {
	return &MRDependency{}
}

type MRDependency struct {
	ID               int64     `gorm:"column:id;primary_key" json:"id"`
	CreateTime       time.Time `gorm:"column:create_time" json:"create_time"`
	UpdateTime       time.Time `gorm:"column:update_time" json:"update_time"`
	ProjectID        string    `gorm:"column:project_id" json:"project_id"`
	IID              int64     `gorm:"column:iid" json:"iid"`
	SourceBranch     string    `gorm:"column:source_branch" json:"source_branch"`
	TargetBranch     string    `gorm:"column:target_branch" json:"target_branch"`
	HostID           int64     `gorm:"column:host_id" json:"host_id"`
	IntegrationState string    `gorm:"column:integration_state" json:"integration_state"`
	IntegrationInfo  string    `gorm:"column:integration_info" json:"integration_info"`
	MRRole           string    `gorm:"column:mr_role" json:"mr_role"`             // mr身份
	OptimusMRID      int64     `gorm:"column:optimus_mr_id" json:"optimus_mr_id"` // optimus mr id
}
