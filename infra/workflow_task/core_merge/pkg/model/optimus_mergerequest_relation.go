package model

import (
	"fmt"

	"code.byted.org/gopkg/facility/fjson"
	json "github.com/bytedance/sonic"
)

func NewOptimusMRRelation(relation string) (*OptimusMRRelation, error) {
	if !fjson.ConfigCompatibleWithStandardLibrary.Valid([]byte(relation)) {
		return nil, fmt.Errorf("relation %v is not a valid JSON", relation)
	}
	var relationItem OptimusMRRelation
	err := json.Unmarshal([]byte(relation), &relationItem)
	if err != nil {
		return nil, err
	}
	return &relationItem, nil
}

type OptimusMRRelationHost struct {
	HostMRID        int64   `json:"host"`
	DependencyMRIDs []int64 `json:"dependencies"`
}

type OptimusMRRelation struct {
	Hosts                 []*OptimusMRRelationHost `json:"hosts"`
	PublicDependencyMRIDs []int64                  `json:"dependencies"`
}
