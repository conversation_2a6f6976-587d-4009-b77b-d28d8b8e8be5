load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "chat",
    srcs = [
        "adapter.go",
        "chat.go",
        "lark.go",
        "message_like.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/ai/pkg/domain/chat",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/ai/kitex_gen/bits/devops/push",
        "//infra/ai/kitex_gen/bytedance/bits/ai",
        "//infra/ai/pkg/dal/cache/agent",
        "//infra/ai/pkg/dal/database/entity",
        "//infra/ai/pkg/dal/database/repository",
        "//infra/ai/pkg/dal/tcc",
        "//infra/ai/pkg/domain/event",
        "//infra/ai/pkg/service/agent",
        "//infra/ai/pkg/service/agent/mcp",
        "//infra/ai/pkg/service/lark",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_larksuite_oapi_sdk_go_v3//event/dispatcher/callback",
        "@com_github_larksuite_oapi_sdk_go_v3//service/im/v1:im",
        "@com_github_pkg_errors//:errors",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
        "@org_byted_code_gopkg_logid//:logid",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "chat_test",
    srcs = ["adapter_test.go"],
    embed = [":chat"],
    deps = [
        "//infra/ai/kitex_gen/bytedance/bits/ai",
        "@org_byted_code_gopkg_logs_v2//:logs",
    ],
)
