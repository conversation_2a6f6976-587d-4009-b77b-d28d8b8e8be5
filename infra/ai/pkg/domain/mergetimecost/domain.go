package mergetimecost

import (
	"context"
	"code.byted.org/devinfra/hagrid/infra/ai/kitex_gen/bytedance/bits/ai"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/dal/cache"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/dal/database/entity"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/dal/database/repository"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/git"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/mergetimecost"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/mr"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/pipeline"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/review"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/timeline"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/user"
	"code.byted.org/devinfra/hagrid/infra/ai/pkg/service/workflow"
	"math"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type Domain struct {
	timeline             *repository.TimelineRepository
	mergeTimeCost        *repository.MergeTimeCostRepository
	engineTask           *repository.EngineTaskLogRepository
	workflow             *repository.WorkflowRepository
	project              *repository.ProjectRepository
	predictResult        *repository.PredictResultRepository
	gitService           *git.Commit
	mrService            *mr.Mr
	pipelineService      *pipeline.Pipeline
	reviewService        *review.Review
	userService          *user.User
	mergeTimeCostService *mergetimecost.MergeTimeCost
	timelineService      *timeline.Timeline
	workflowService      *workflow.EngineTask
	adaptor              *Adaptor
	rdb                  *cache.MergeTimeCost
}

func NewMergeTimeCostDomain(optimus *gorm.DB, commonData *gorm.DB, bitsWorkflow *gorm.DB, rdb *goredis.Client) *Domain {
	return &Domain{
		timeline:             repository.NewTimelineRepository(optimus),
		mergeTimeCost:        repository.NewMergeTimeCostRepository(commonData),
		engineTask:           repository.NewEngineTaskLogRepository(commonData),
		workflow:             repository.NewWorkflowRepository(bitsWorkflow),
		project:              repository.NewProjectRepository(optimus),
		predictResult:        repository.NewPredictResultRepository(commonData),
		gitService:           git.NewCommitService(optimus, rdb),
		mrService:            mr.NewMrService(optimus, bitsWorkflow, rdb),
		pipelineService:      pipeline.NewPipelineService(),
		reviewService:        review.NewReviewService(),
		userService:          user.NewUserService(),
		mergeTimeCostService: mergetimecost.NewMergeTimeCostService(commonData, rdb),
		timelineService:      timeline.NewTimelineService(),
		workflowService:      workflow.NewEngineTaskService(rdb, commonData, bitsWorkflow),
		adaptor:              &Adaptor{},
		rdb:                  cache.NewMergeTimeCost(rdb),
	}
}

func (d *Domain) getTargetBranchPrefix(ctx context.Context, mrs []*mr.GitlabMrDetail) gresult.R[string] {
	for _, mr := range mrs {
		if mr.TargetBranch == "" {
			continue
		}
		prefixArr := strings.Split(mr.TargetBranch, "/")
		return gresult.OK(prefixArr[0])
	}
	return gresult.OK("")
}

func (d *Domain) getMrCommitDiffInfo(ctx context.Context, projectId, iid, codebaseRepoId, codebaseChangeId int64, timestamp int64) gresult.R[*diffInfo] {
	answer := &diffInfo{}
	commits := d.gitService.GetCommits(ctx, projectId, iid)
	if commits.IsErr() {
		return gresult.Err[*diffInfo](commits.Err())
	}
	for _, commit := range commits.Must() {
		if commit.CommittedDate > timestamp || commit.AuthorName == "root" {
			continue
		}
		diff := d.gitService.GetCommitDiff(ctx, projectId, commit.GetShortId())
		if diff.IsErr() {
			return gresult.Err[*diffInfo](diff.Err())
		}
		answer.ChangeLineTotal += diff.Must().ChangeLineTotal
		answer.ChangeFileTotal += diff.Must().ChangeFileTotal
		answer.Files = append(answer.Files, diff.Must().Files...)
		answer.CommitTotal++
	}

	return gresult.OK(answer)
}

func (d *Domain) getCommitsInfo(ctx context.Context, mrs []*mr.GitlabMrDetail, timestamp int64) gresult.R[*diffInfo] {
	answer := &diffInfo{}
	fileCounter := 0
	for _, mr := range mrs {
		mrDiff := d.getMrCommitDiffInfo(ctx, mr.ProjectId, mr.Iid, mr.CodebaseRepoId, mr.CodebaseChangeId, timestamp)
		if mrDiff.IsErr() {
			logs.CtxWarn(ctx, "failed to get mr diff info, err: %s", mrDiff.Err())
			continue
		}
		answer.ChangeLineTotal += mrDiff.Must().ChangeLineTotal
		answer.ChangeFileTotal += mrDiff.Must().ChangeFileTotal
		answer.CommitTotal += mrDiff.Must().CommitTotal

		// 获取 comment 信息
		comments := d.gitService.GetComments(ctx, mr.CodebaseRepoId, mr.CodebaseChangeId)
		if comments.IsErr() {
			logs.CtxError(ctx, "get comments failed, err: %v", comments.Err())
			return gresult.Err[*diffInfo](comments.Err())
		}
		answer.ResoledTotal += comments.Must().CommentResolvedTotal
		answer.CommentTotal += comments.Must().CommentTotal
		// answer.files 去重
		fileMap := make(map[string]bool, 0)
		for _, file := range mrDiff.Must().Files {
			if _, ok := fileMap[file]; ok {
				continue
			}
			fileCounter++
			fileMap[file] = true
		}
	}
	answer.ChangeFileTotal = fileCounter
	if answer.ChangeLineTotal > 0 {
		answer.ThousandsCodeCommentTotal = answer.CommentTotal * 1000 / answer.ChangeLineTotal
	}
	return gresult.OK(answer)
}

func (d *Domain) getGitInfo(ctx context.Context, mrId int64, mrs []*mr.GitlabMrDetail, timelines []*entity.OptimusGitlabTimelineEvent, timestamp int64) gresult.R[*gitInfo] {
	answer := &gitInfo{}
	commits := d.getCommitsInfo(ctx, mrs, timestamp)
	if commits.IsErr() {
		return gresult.Err[*gitInfo](commits.Err())
	}
	c := commits.Must()
	answer.ChangeLineTotal = c.ChangeLineTotal
	answer.CommitTotal = c.CommitTotal
	answer.CommentTotal = c.CommentTotal
	answer.RepoTotal = len(mrs)
	answer.ChangeFileTotal = c.ChangeFileTotal
	answer.CommentResolvedTotal = c.ResoledTotal
	answer.ThousandsCodeCommentTotal = c.ThousandsCodeCommentTotal

	if res := d.getTargetBranchPrefix(ctx, mrs); res.IsOK() {
		answer.TargetBranchPrefix = res.Must()
	}
	if res := d.gitService.GetGitEventInTimeline(timelines, timestamp); res.IsOK() {
		answer.PushTimes = res.Must().PushTimes
		answer.MergeTargetTimes = res.Must().MergeTargetTimes
	}
	// 获取 mr 详情
	for _, mr := range mrs {
		if mrId == mr.Id {
			if answer.ChangeLineTotal == 0 {
				break
			}
			timeCost := int(mr.MergeTime - mr.CreateTime)
			// 每行代码平均耗时
			secondCode := float32(timeCost / answer.ChangeLineTotal)
			// 1000 行写多少分钟
			answer.ThousandsCodeTimeCost = int(1000*secondCode) / 60
		}
	}

	return gresult.OK(answer)
}

func (d *Domain) getUser(ctx context.Context, mrId, timestamp int64) gresult.R[*userInfo] {
	mr := d.mrService.GetMrInfo(ctx, mrId)
	if mr.IsErr() {
		return gresult.Err[*userInfo](mr.Err())
	}
	mrInfo := mr.Must()
	createTime := time.Unix(mrInfo.CreateTime, 0)
	// 计算用户使用了 bits 多少天了
	allUserCreatedMr := d.mrService.GetUserLastMrInfoByDay(ctx, timestamp, mrInfo.AuthorName, 360)
	if allUserCreatedMr.IsErr() {
		logs.CtxError(ctx, "ai_timeline_userlast90day_error: %s", allUserCreatedMr.Err().Error())
		return gresult.Err[*userInfo](allUserCreatedMr.Err())
	}
	firstCreateTimestamp := int64(math.MaxInt64)
	for _, mr := range allUserCreatedMr.Must() {
		if mr.CreateTime.Unix() < firstCreateTimestamp {
			firstCreateTimestamp = mr.CreateTime.Unix()
		}
	}
	days := int((timestamp - firstCreateTimestamp) / 60 / 60 / 24)

	usersBasicIno := d.userService.GetUserBasicInfo(ctx, []string{mrInfo.AuthorName})
	if usersBasicIno.IsErr() {
		logs.CtxError(ctx, "ai_timeline_userbasicinfo_error: %s", usersBasicIno.Err().Error())
		return gresult.Err[*userInfo](usersBasicIno.Err())
	}
	userBasicInfo := usersBasicIno.Must()[0]
	resp := &userInfo{
		HourTime:     createTime.Hour(),
		WeekTime:     int(createTime.Weekday()),
		MonthTime:    d.userService.MonthNameToInt(createTime.Month().String()),
		Username:     mrInfo.AuthorName,
		LeaderName:   userBasicInfo.LeaderName,
		WorkCity:     userBasicInfo.WorkCity,
		WorkCountry:  userBasicInfo.WorkCountry,
		WorkTimeZone: userBasicInfo.WorkTimeZone,
		Sequence:     userBasicInfo.Sequence,
		UseBitsTime:  days,
	}
	return gresult.OK(resp)
}

func (d *Domain) getCommon(ctx context.Context, mrId int64, timelines []*entity.OptimusGitlabTimelineEvent, timestamp int64) gresult.R[*commonInfo] {
	mr := d.mrService.GetMrInfo(ctx, mrId)
	if mr.IsErr() {
		return gresult.Err[*commonInfo](mr.Err())
	}
	mrInfo := mr.Must()
	resp := &commonInfo{
		SpaceName: mrInfo.GroupName,
		DevId:     mrId,
	}
	if timestamp < mrInfo.CreateTime {
		return gresult.Err[*commonInfo](bits_err.AI.TimestampFormatErr)
	}

	project := d.mrService.GetProjectTitleByMrId(ctx, mrId)
	if project.IsErr() {
		return gresult.Err[*commonInfo](project.Err())
	}
	for _, item := range timelines {
		if item.EventType == "merge_mr" && item.Operator == "ci_cony" {
			res := d.mrService.CheckMainRepoMergeTimeline(project.Must(), item.Data)
			if res.IsOK() && res.Must() {
				resp.Label = (item.CreatedAt.Unix() - timestamp) / 60 / 60
				break
			}
		}
	}

	n := time.Unix(timestamp, 0)
	resp.RealMergeTimeCost = int(mrInfo.MergeTime-mrInfo.CreateTime) / 60 / 60
	resp.CalculateTime = &n
	return gresult.OK(resp)
}

func (d *Domain) getAnalysis(ctx context.Context, mrId int64, records []*entity.OptimusGitlabTimelineEvent) gresult.R[*analyze] {
	// 获取 mrId 对应的 project title
	project := d.mrService.GetProjectTitleByMrId(ctx, mrId)
	if project.IsErr() {
		return gresult.Err[*analyze](project.Err())
	}
	// 获取 mr 的 qa 名字
	qasName := d.reviewService.GetQAReviewersName(ctx, mrId)
	if qasName.IsErr() {
		return gresult.Err[*analyze](qasName.Err())
	}
	var lastQAReviewTime, firstPipelineTime, lastPipelineTime, firstReviewTime, lastReviewTime, startCodeReviewTime, mergedTime, createTime int64
	pushTimesRecord := make([]int64, 0)

	if res := d.timelineService.GetLastQAReviewerFinishTime(qasName.Must(), records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		lastQAReviewTime = res.Must()
	}

	if res := d.timelineService.GetFirstReviewerFinishTime(records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		firstReviewTime = res.Must()
	}

	if res := d.timelineService.GetLastReviewerFinishTime(records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		lastReviewTime = res.Must()
	}

	if res := d.timelineService.GetFirstPipelineFinishTime(project.Must(), records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		firstPipelineTime = res.Must()
	}

	if res := d.timelineService.GetLastPipelineFinishTime(project.Must(), records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		lastPipelineTime = res.Must()
	}

	if res := d.timelineService.GetStartReviewTime(records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		startCodeReviewTime = res.Must()
	}

	if res := d.timelineService.GetMergeTime(project.Must(), records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		mergedTime = res.Must()
	}

	if res := d.timelineService.GetCreateTime(records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		createTime = res.Must()
	}

	if res := d.timelineService.GetPushTimeRecords(records); res.IsErr() {
		return gresult.Err[*analyze](res.Err())
	} else {
		pushTimesRecord = res.Must()
	}

	resp := &analyze{}

	if mergedTime != 0 {
		if lastPipelineTime != 0 {
			resp.MergedTimeMinusLastFinishPipeline = int(mergedTime-lastPipelineTime) / 60
		}

		if lastReviewTime != 0 {
			resp.MergedTimeMinusLastApprove = int(mergedTime-lastReviewTime) / 60
		}
		if lastQAReviewTime != 0 {
			resp.MergedTimeMinusLastQAApprove = int(mergedTime-lastQAReviewTime) / 60
		}
	}
	if firstPipelineTime != 0 && createTime != 0 {

		resp.FirstFinishPipelineTimeMinusCreateTime = int(firstPipelineTime-createTime) / 60
	}
	if lastPipelineTime != 0 && firstPipelineTime != 0 {
		resp.LastFinishPipelineTimeMinusFirstPipelineFinishTime = int(lastPipelineTime-firstPipelineTime) / 60
	}
	if lastPipelineTime != 0 && lastReviewTime != 0 {
		resp.LastFinishPipelineTimeMinusLastApprove = int(lastPipelineTime-lastReviewTime) / 60
	}
	if lastQAReviewTime != 0 && lastReviewTime != 0 {
		resp.LastQAApproveTimeMinusLastRDApprove = int(lastQAReviewTime-lastReviewTime) / 60
	}
	if lastReviewTime != 0 && firstReviewTime != 0 {
		resp.LastApproveTimeMinusFirstApprove = int(lastReviewTime-firstReviewTime) / 60
	}
	if startCodeReviewTime != 0 && createTime != 0 {
		resp.StartCodeReviewTimeMinusCreateTime = int(startCodeReviewTime-createTime) / 60
	}
	if firstReviewTime != 0 && startCodeReviewTime != 0 {
		resp.FirstApproveTimeMinusStartReview = int(firstReviewTime-startCodeReviewTime) / 60
	}

	for _, push := range pushTimesRecord {
		if push > firstReviewTime {
			resp.PushTimesAfterFirstApprove++
		}
	}
	return gresult.OK(resp)
}
func (d *Domain) getWorkflow(ctx context.Context, mrId int64, timestamp int64) gresult.R[*workflow.Info] {
	res := d.workflowService.GetWorkflow(ctx, mrId, timestamp)
	if res.IsErr() {
		logs.CtxWarn(ctx, "get workflow failed, err: %v", res.Err())
		return gresult.OK(&workflow.Info{})
	}
	return gresult.OK(res.Must())
}

func (d *Domain) getMergeTimeCostModelData(ctx context.Context, mrId, timestamp int64) gresult.R[*info] {
	// get author name
	singleMr := d.mrService.GetMrInfo(ctx, mrId)
	if singleMr.IsErr() {
		logs.CtxWarn(ctx, "get mr info failed, err: %v", singleMr.Err())
		return gresult.Err[*info](singleMr.Err())
	}
	// request mysql get all timeline of mr
	timelines := d.timeline.Get(ctx, mrId)
	if timelines.IsErr() {
		logs.CtxWarn(ctx, "get timelines failed, err: %v", timelines.Err())
		return gresult.Err[*info](timelines.Err())
	}
	relation := d.mrService.GetMrRelation(ctx, mrId)
	if relation.IsErr() {
		logs.CtxWarn(ctx, "get mr relation failed, err: %v", relation.Err())
		return gresult.Err[*info](relation.Err())
	}
	git := d.getGitInfo(ctx, mrId, relation.Must(), timelines.Must(), timestamp)
	if git.IsErr() {
		logs.CtxWarn(ctx, "get git info failed, err: %v", git.Err())
		return gresult.Err[*info](git.Err())
	}
	mr := d.mrService.GetMr(ctx, mrId, timestamp, timelines.Must())
	if mr.IsErr() {
		logs.CtxWarn(ctx, "get mr failed, err: %v", mr.Err())
		return gresult.Err[*info](mr.Err())
	}
	pipeline := d.pipelineService.GetPipeline(ctx, timestamp, timelines.Must())
	if pipeline.IsErr() {
		logs.CtxWarn(ctx, "get pipeline failed, err: %v", pipeline.Err())
		return gresult.Err[*info](pipeline.Err())
	}
	review := d.reviewService.GetReview(ctx, mrId, singleMr.Must().AuthorName, timelines.Must(), timestamp)
	if review.IsErr() {
		logs.CtxWarn(ctx, "get review failed, err: %v", review.Err())
		return gresult.Err[*info](review.Err())
	}
	user := d.getUser(ctx, mrId, timestamp)
	if user.IsErr() {
		logs.CtxWarn(ctx, "get user failed, err: %v", user.Err())
		return gresult.Err[*info](user.Err())
	}
	common := d.getCommon(ctx, mrId, timelines.Must(), timestamp)
	if common.IsErr() {
		logs.CtxWarn(ctx, "get common failed, err: %v", common.Err())
		return gresult.Err[*info](common.Err())
	}
	analysis := d.getAnalysis(ctx, mrId, timelines.Must())
	if analysis.IsErr() {
		logs.CtxWarn(ctx, "get analysis failed, err: %v", analysis.Err())
		return gresult.Err[*info](analysis.Err())
	}
	detail := &info{
		ID:       mrId,
		User:     user.Must(),
		Pipeline: pipeline.Must(),
		Review:   review.Must(),
		Git:      git.Must(),
		Mr:       mr.Must(),
		Common:   common.Must(),
		Analyze:  analysis.Must(),
	}

	// 弱相关
	workflow := d.getWorkflow(ctx, mrId, timestamp)
	if workflow.IsOK() {
		logs.CtxInfo(ctx, "get workflow success: %v, mr_id:%d", workflow.Must(), mrId)
		detail.Workflow = workflow.Must()
	}
	return gresult.OK(detail)
}

func (d *Domain) CalculateMrMergeTimeCostModel(ctx context.Context, req *ai.CalculateMrMergeTimeCostModelRequest) gresult.R[*ai.CalculateMrMergeTimeCostModelResponse] {
	resp := ai.NewCalculateMrMergeTimeCostModelResponse()
	mr := d.mrService.GetMrInfo(ctx, req.GetMrId())
	if mr.IsErr() {
		logs.CtxWarn(ctx, "get mr info failed, err: %v", mr.Err())
		return gresult.Err[*ai.CalculateMrMergeTimeCostModelResponse](mr.Err())
	}
	if mr.Must().AuthorName == "ci_cony" {
		logs.CtxWarn(ctx, "ci_cony mr, mrId: %d", req.GetMrId())
		return gresult.Err[*ai.CalculateMrMergeTimeCostModelResponse](bits_err.AI.IsRobot)
	}

	// 判断相同的数据是否已经存在
	span := mr.Must().MergeTime - req.Timestamp
	logs.CtxInfo(ctx, "%d,%d", mr.Must().MergeTime, req.Timestamp)
	label := span / 60 / 60
	if d.mergeTimeCost.GetByteDevIdAndLabelAndIsFinish(ctx, req.GetMrId(), label, 0).IsOK() {
		logs.CtxWarn(ctx, "record has exist failed, mrId: %d, label: %s", req.GetMrId(), label)
		return gresult.Err[*ai.CalculateMrMergeTimeCostModelResponse](bits_err.AI.RepeatedCreate)
	}

	data := d.getMergeTimeCostModelData(ctx, req.GetMrId(), req.GetTimestamp())
	if data.IsErr() {
		logs.CtxWarn(ctx, "get merge time cost model data err: %v", data.Err())
		return gresult.Err[*ai.CalculateMrMergeTimeCostModelResponse](data.Err())
	}

	if data.Must().Common.Label == 0 {
		res := d.workflowService.CreateEngineLog(ctx, req.GetMrId(), req.GetTimestamp())
		if res.IsErr() {
			logs.CtxError(ctx, "create engine log %v", res.Err())
		}
	}

	timeCost := d.mergeTimeCostService.CreateMergeTimeCostData(ctx, d.adaptor.mergeTimeCostEntityModelAdaptor(data.Must()), req.GetMrId(), req.GetTimestamp())

	if timeCost.IsErr() {
		logs.CtxWarn(ctx, "create merge time cost data err: %v", timeCost.Err())
		return gresult.Err[*ai.CalculateMrMergeTimeCostModelResponse](timeCost.Err())
	}
	resp.SetMergeInTime(timeCost.Must())

	return gresult.OK(resp)
}

func (d *Domain) GetMrMergeTimeCostModelResponse(ctx context.Context, req *ai.GetMrMergeTimeCostModelRequest) gresult.R[*ai.GetMrMergeTimeCostModelResponse] {
	resp := ai.NewGetMrMergeTimeCostModelResponse()

	records := d.mergeTimeCostService.GetMergeTimeCostData(ctx, req.GetMrId())
	if records.IsErr() {

	}

	data := gslice.Map(records.Must(), d.adaptor.mergeTimeCostRPCAdaptor)

	resp.SetInfo(data)
	return gresult.OK(resp)
}

func (d *Domain) UpdateMrMergePredictTime(ctx context.Context, req *ai.UpdateMrMergePredictTimeRequest) error {
	logs.CtxInfo(ctx, "update mr merge predict time, mrID:%d,event type: %s, timestamp: %d", req.MrId, req.EventType, req.Timestamp)
	mrMergeInfo := d.getMergeTimeCostModelData(ctx, req.MrId, req.Timestamp)
	mrInfo, err := mrMergeInfo.Get()
	if err != nil {
		return err
	}
	if mrInfo == nil {
		return nil
	}
	mrModel := d.adaptor.mergeTimeCostEntityModelAdaptor(mrInfo)
	predict := d.mergeTimeCostService.Predict(ctx, mrModel)
	predictValue, err := predict.Get()
	if err != nil {
		return err
	}
	return d.predictResult.Create(ctx, &entity.MrPredictResult{
		MrID:        req.MrId,
		EventName:   req.EventType,
		EventTime:   time.Unix(req.Timestamp, 0).Add(-8 * time.Hour),
		PredictTime: predictValue * 24,
		MrCostData:  datatypes.NewJSONType[entity.MergeTimeCostModel](*mrModel),
	})
}

func (d *Domain) GetMrPredictMergeTimeRealTime(ctx context.Context, mrID int64) (float64, error) {
	mrMergeInfo := d.getMergeTimeCostModelData(ctx, mrID, time.Now().Unix())
	mrInfo, err := mrMergeInfo.Get()
	if err != nil {
		return 0, err
	}
	if mrInfo == nil {
		return 0, nil
	}
	return d.mergeTimeCostService.Predict(ctx, d.adaptor.mergeTimeCostEntityModelAdaptor(mrInfo)).Get()
}

func (d *Domain) GetMrPredictMergeTimeFromDB(ctx context.Context, mrID int64) (float64, error) {
	predict, err := d.predictResult.GetLatestResult(ctx, mrID)
	if err != nil {
		logs.CtxError(ctx, "failed to get predict info,mrID:%d,err:%s", mrID, err.Error())
		return 0, err
	}
	if predict == nil { // 数据库里面没有，退化到实时
		return d.GetMrPredictMergeTimeRealTime(ctx, mrID)
	}
	return predict.PredictTime, nil
}
