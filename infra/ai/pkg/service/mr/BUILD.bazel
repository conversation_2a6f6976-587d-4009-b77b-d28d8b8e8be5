load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mr",
    srcs = [
        "model.go",
        "mr.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/ai/pkg/service/mr",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/ai/kitex_gen/bytedance/bits/optimus",
        "//infra/ai/pkg/dal/cache",
        "//infra/ai/pkg/dal/database/entity",
        "//infra/ai/pkg/dal/database/repository",
        "//infra/ai/service/elasticsearch",
        "//infra/ai/service/rpc",
        "//libs/bits_err",
        "@com_github_bytedance_sonic//:sonic",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_toutiao_elastic_v7//:elastic",
    ],
)
