package mr

import "time"

type GitlabMrDetail struct {
	Id               int64
	CreateTime       int64
	AuthorName       string
	TargetBranch     string
	CodebaseRepoId   int64
	CodebaseChangeId int64
	ProjectId        int64
	Iid              int64
	GroupName        string
	MergeTime        int64
	State            string
	MrType           string
}

type Info struct {
	State                   string `json:"state"`
	IsFinish                int    `json:"mr_is_finish"`
	WorkflowId              int    `json:"mr_workflow_id"`
	FeatureBindTotal        int    `json:"mr_feature_bind_total"`
	ForceMergeTimes         int    `json:"mr_force_merge_times"`
	TimeErrorMessageTotal   int    `json:"mr_time_error_message_total"`
	TicketGetFlag           int    `json:"mr_ticket_get_flag"`
	MrType                  string `json:"mr_type"`
	RetryWorkflowTimes      int    `json:"retry_workflow_times"`
	RecentCreatedMrTotal1D  int    `json:"mr_recent_created_mr_total_1_d"`
	RecentCreatedMrTotal3D  int    `json:"mr_recent_created_mr_total_3_d"`
	RecentCreatedMrTotal5D  int    `json:"mr_recent_created_mr_total_5_d"`
	RecentCreatedMrTotal10D int    `json:"mr_recent_created_mr_total_10_d"`
	RecentCreatedMrTotal20D int    `json:"mr_recent_created_mr_total_20_d"`
	RecentCreatedMrTotal50D int    `json:"mr_recent_created_mr_total_50_d"`
	RecentCreatedMrTotal90D int    `json:"mr_recent_created_mr_total_90_d"`
	RecentMergedMrTotal1D   int    `json:"mr_recent_merged_mr_total_1_d"`
	RecentMergedMrTotal3D   int    `json:"mr_recent_merged_mr_total_3_d"`
	RecentMergedMrTotal5D   int    `json:"mr_recent_merged_mr_total_5_d"`
	RecentMergedMrTotal10D  int    `json:"mr_recent_merged_mr_total_10_d"`
	RecentMergedMrTotal20D  int    `json:"mr_recent_merged_mr_total_20_d"`
	RecentMergedMrTotal50D  int    `json:"mr_recent_merged_mr_total_50_d"`
	RecentMergedMrTotal90D  int    `json:"mr_recent_merged_mr_total_90_d"`
	CreateAt                int64  `json:"create_at"`
}

type SearchInfo struct {
	Id         int64      `json:"id"`
	CreateTime *time.Time `json:"mr_create_time"`
	UpdateTime *time.Time `json:"mr_update_time"`
	State      string     `json:"mr_state"`
}

type timelineMergeData struct {
	ProjectName string `json:"project_name"`
}
