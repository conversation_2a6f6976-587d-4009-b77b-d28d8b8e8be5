package entity

import (
	"time"
)

type ContextType string

const CommitContextType ContextType = "commit"
const ChangeContextType ContextType = "change"

type DiffSummary struct {
	Id          int64       `json:"id" gorm:"column:id"` // id
	DevBasicId  int64       `json:"dev_basic_id" gorm:"column:dev_basic_id"`
	ChangeId    int64       `json:"change_id" gorm:"column:change_id"`
	ProjectId   int64       `json:"project_id" gorm:"column:project_id"`
	Content     string      `json:"content" gorm:"column:content"`
	CommitSha   string      `json:"commit_sha" gorm:"column:commit_sha"`
	ContextType ContextType `json:"context_type" gorm:"column:context_type"`
	Username    string      `json:"username" gorm:"username"`
	CreatedAt   *time.Time  `json:"created_at" gorm:"column:created_at"`
	UpdatedAt   *time.Time  `json:"updated_at" gorm:"column:updated_at"`
}

func (m *DiffSummary) TableName() string {
	return "ai_diff_summary"
}
