/**
 * @Date: 2023/4/4
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package mocktools

import (
	"context"
	"code.byted.org/devinfra/hagrid/infra/cachyper/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/infra/cachyper/kitex_gen/bytedance/bits/git_server_v2"
	"code.byted.org/devinfra/hagrid/infra/cachyper/pkg/backends/gitserver"

	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

type MockedGitServer struct{}

var _ gitserver.Api = &MockedGitServer{}

func (m MockedGitServer) GetMergeBase(ctx context.Context, username string, project int64, source, target string) gresult.R[string] {
	commit := "a0f82dc9998dbb83d620ed2c684293ba5adda430"
	return gresult.OK(commit)
}

func (m MockedGitServer) GetMergeBaseOnCodeChange(ctx context.Context, username string, ccid int64) gresult.R[*git_server_v2.Commit] {
	commit := &git_server_v2.Commit{
		Id:      "a0f82dc9998dbb83d620ed2c684293ba5adda431",
		ShortId: "a0f82dc9",
		Title:   "",
		Message: "",
	}
	return gresult.OK(commit)
}

func (m MockedGitServer) GetDiffFiles(ctx context.Context, username string, project, iid int64) gresult.R[[]*git_server.DiffFile] {
	resp := []*git_server.DiffFile{
		{
			Path:          "Makefile",
			FromPath:      "",
			ToPath:        "Makefile",
			LinesInserted: 1,
			LinesDeleted:  1,
			ChangeType:    "M",
			IsBinary:      false,
		},
		{
			Path:          "kitex_gen/bytedance/bits/meta/calendar.go",
			FromPath:      "",
			ToPath:        "kitex_gen/bytedance/bits/meta/calendar.go",
			LinesInserted: 313,
			LinesDeleted:  0,
			ChangeType:    "A",
			IsBinary:      false,
		},
		{
			Path:          "views/repo/cony_mr_search.go",
			FromPath:      "views/repo/cony_mr_search.go",
			ToPath:        "views/repo/cony_mr_search.go",
			LinesInserted: 11,
			LinesDeleted:  14,
			ChangeType:    "M",
			IsBinary:      false,
		},
	}
	return gresult.OK(resp)
}

func (m MockedGitServer) GetDiffFilesOnCodeChange(ctx context.Context, username string, ccid int64) gresult.R[[]*git_server_v2.DiffFile] {
	resp := []*git_server_v2.DiffFile{
		{
			Path:          "Makefile",
			FromPath:      "",
			ToPath:        "Makefile",
			LinesInserted: 1,
			LinesDeleted:  1,
			ChangeType:    "M",
			IsBinary:      false,
		},
		{
			Path:          "kitex_gen/bytedance/bits/meta/calendar.go",
			FromPath:      "",
			ToPath:        "kitex_gen/bytedance/bits/meta/calendar.go",
			LinesInserted: 313,
			LinesDeleted:  0,
			ChangeType:    "A",
			IsBinary:      false,
		},
		{
			Path:          "views/repo/cony_mr_search.go",
			FromPath:      "views/repo/cony_mr_search.go",
			ToPath:        "views/repo/cony_mr_search.go",
			LinesInserted: 11,
			LinesDeleted:  14,
			ChangeType:    "M",
			IsBinary:      false,
		},
	}
	return gresult.OK(resp)
}

func (m MockedGitServer) GetContent(ctx context.Context, project int64, revision string, filepath string) gresult.R[string] {
	switch filepath {
	case ".bits/config.yml":
		content := `
code_review:
  mode: strict
  maintainers:
    -   <EMAIL>   
    -   <EMAIL>   
  reviewers_required_amount: 2
  maintainers_required_approvals: 3
  pick_strategy: pick_first
`
		return gresult.OK(content)

	case "views/OWNERS.bits.yml":
		content := `
code_review_config:
  maintainers:
    -   <EMAIL>
    -  <EMAIL>
code_review_rule:
  files:
    - pattern: .*.java
      reviewers:
        -  <EMAIL>
        -   <EMAIL>
      required_approvals: 1
  target_branches:
    - pattern: release_.*
      reviewers:
        - <EMAIL>
        - <EMAIL>
      required_approvals: 1
`
		return gresult.OK(content)
	default:
		panic("implement me")
	}
}

func (m MockedGitServer) BatchGetFileContents(ctx context.Context, username string, project int64, revision string, paths []string, regex *string) gresult.R[[]*git_server.CodebaseFile] {
	var file []*git_server.CodebaseFile

	return gresult.OK(file)
}

func (m MockedGitServer) GetCodeChangeDetail(ctx context.Context, ccid int64) gresult.R[*git_server.CodeChangeDetail] {
	detail := &git_server.CodeChangeDetail{
		GitlabMr: &git_server.CodeChangeGitlabMergeRequest{
			Id:           18,
			CodeChangeID: 18,
			ProjectID:    466325,
			Iid:          84,
			Title:        "test3",
			Description:  "",
			State:        "opened",
			TargetBranch: "bits_test_dev",
			SourceBranch: "third_change",
			Author:       "liangjiayi.ljy",
			CreatedAt:    nil,
			UpdatedAt:    nil,
			LastCommitID: gptr.Of("77ab7400f36864c0c258726bba2111921ceca301"),
		},
	}
	return gresult.OK(detail)
}
