package code_impact

import (
	_ "embed"

	"testing"

	"code.byted.org/iesarch/atomic"
)

//go:embed test_input.json
var testInput string

func TestPlugin_Execute(t *testing.T) {

	tester := atomic.NewTester(
		&Plugin{},
		atomic.BuildCycleCtx(
			testInput,
			"prod",
			// add a trigger user field
			// more definition can be found on:
			//
			atomic.WithBuildCtxKV("trigger_user", "zhangyibo.114514"),
		),
	)
	res, err := tester.AutoCollect(
	// we will try to call your extra action
	)
	if err != nil {
		t.<PERSON>al(err)
	}
	t.Log(res)
}
