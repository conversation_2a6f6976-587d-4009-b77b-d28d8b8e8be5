package bits_gatekeeper

import (
	"context"
	"database/sql"
	"errors"

	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/model"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/service/database"
	"gorm.io/gorm"
)

func GetDevTaskGateKeeperPoints(ctx context.Context, devBasicId int64) ([]*model.GateKeeperCheckPoint, error) {
	point := make([]*model.GateKeeperCheckPoint, 0)
	res := database.BitsGatekeeper.Master.NewRequest(ctx).Where("biz_id = ? and biz_scene = 1", devBasicId).Find(&point)
	if res.RecordNotFound() || errors.Is(res.Error, gorm.ErrRecordNotFound) || errors.Is(res.Error, sql.ErrNoRows) {
		return nil, nil
	}
	if res.Error != nil {
		return nil, res.Error
	}
	return point, nil
}

func GetDevTaskGateKeeperPointById(ctx context.Context, id int64) (*model.GateKeeperCheckPoint, error) {
	point := &model.GateKeeperCheckPoint{}
	res := database.BitsGatekeeper.Master.NewRequest(ctx).Where("id = ?", id).Last(&point)
	if res.RecordNotFound() || errors.Is(res.Error, gorm.ErrRecordNotFound) || errors.Is(res.Error, sql.ErrNoRows) {
		return nil, nil
	}
	if res.Error != nil {
		return nil, res.Error
	}
	return point, nil
}
