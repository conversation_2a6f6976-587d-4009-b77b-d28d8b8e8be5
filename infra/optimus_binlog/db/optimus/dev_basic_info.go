package optimus

import (
	"context"
	"database/sql"
	"gorm.io/gorm"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/model"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/service/database"
)

func GetDevBasicInfoByDevBasicID(ctx context.Context, devBasicId int64) (*model.DevBasicInfo, error) {
	devBasic := &model.DevBasicInfo{}
	res := database.Optimus.Master.NewRequest(ctx).Where("id = ?", devBasicId).Find(devBasic)
	if res.RecordNotFound() || res.Error == gorm.ErrRecordNotFound || res.Error == sql.ErrNoRows {
		return nil, nil
	}
	if res.Error != nil {
		return nil, res.Error
	}
	return devBasic, nil
}

func GetDevBasicInfoByDevID(ctx context.Context, devID int64) (*model.DevBasicInfo, error) {
	devBasic := &model.DevBasicInfo{}
	res := database.Optimus.Master.NewRequest(ctx).Where("dev_id = ?", devID).Find(devBasic)
	if res.RecordNotFound() || res.Error == gorm.ErrRecordNotFound || res.Error == sql.ErrNoRows {
		return nil, nil
	}
	if res.Error != nil {
		return nil, res.Error
	}
	return devBasic, nil
}
