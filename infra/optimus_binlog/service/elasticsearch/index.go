package elasticsearch

import (
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/pkg/config"
	"code.byted.org/gopkg/logs"
	"code.byted.org/toutiao/elastic/v7"
)

type Common struct {
	clientV7     *elastic.Client
	connectionV7 *elastic.SearchService
}
type Logger struct{}

func (l *Logger) Printf(format string, v ...interface{}) {
	logs.Info(format, v)
}

func (c *Common) GetClient(conf *config.ElasticSearchBase) *elastic.Client {
	var err error
	logger := &Logger{}
	if len(conf.Psm) != 0 {
		c.clientV7, err = elastic.NewPSMClient(conf.Psm, conf.Cluster, 5*time.Second, elastic.SetErrorLog(logger), elastic.SetTraceLog(logger), elastic.SetSniff(false))
	} else {
		url := fmt.Sprintf("%s:%d", conf.Host, conf.Port)
		c.clientV7, err = elastic.NewClient(elastic.SetURL(url), elastic.SetErrorLog(logger), elastic.SetTraceLog(logger), elastic.SetSniff(false))

	}

	if err != nil {
		logs.Error("common_fatal: es init error", err)
		panic(err)
	} else {
		logs.Info("elasticsearch init success")
	}
	return c.clientV7
}

type Client interface {
	initSearchConnection(conf *config.ElasticSearchBase)
	GetSearchConnection() *elastic.SearchService
	GetCreateConnection() *elastic.IndexService
}

var MergeRequestSearchClient Client
var HotfixSearchClient Client

func Init() {
	MergeRequestSearchClient = &ESSSearch{}
	HotfixSearchClient = &ESSSearch{}
	HotfixSearchClient.initSearchConnection(config.App.ElasticSearch.HotfixSearch)
	MergeRequestSearchClient.initSearchConnection(config.App.ElasticSearch.MergeRequestSearch)
}
