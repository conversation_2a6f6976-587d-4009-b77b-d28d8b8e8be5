load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "feature_gate",
    srcs = [
        "index.go",
        "init.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/optimus_binlog/service/feature_gate",
    visibility = ["//visibility:public"],
    deps = [
        "@org_byted_code_devinfra_bytegate_sdk//bytegate_client",
        "@org_byted_code_devinfra_bytegate_sdk//models",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
