package binlog

import (
	"code.byted.org/gopkg/dbus"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gmap"
	"context"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/pkg/controller"
	"code.byted.org/devinfra/hagrid/infra/optimus_binlog/pkg/service/hotfix"
	"time"
)

type HotfixMysqlHandler struct{}

func (mh *HotfixMysqlHandler) Handle(ctx context.Context, tableName, dmlType string, beforeColumns, afterColumns map[string]dbus.MysqlIncrementColumn) error {
	ConcurrencyControl.P()
	go func() {
		defer ConcurrencyControl.V()
		sign := time.Now().UnixNano()
		err := controller.RunHotfix(ctx, &controller.MysqlMqData{
			TableName:     tableName,
			DmlType:       dmlType,
			BeforeColumns: beforeColumns,
			AfterColumns:  afterColumns,
			Sign:          sign,
		})
		if err != nil {
			logs.CtxError(ctx, "binlog_error: %s", err.Error())
		}
	}()
	return nil
}

func (mh *HotfixMysqlHandler) Tables() []string {
	return gmap.Keys(hotfix.HandlerMap)
}
