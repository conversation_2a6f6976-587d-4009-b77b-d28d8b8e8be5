package consts

const (
	MainGroupConfigURL      = "/api/v1/config/main"
	OptimusGitlabWebhookURL = "http://optimus.bytedance.net/api/v1/gitlab/webhook"
	BitsGitlabWebhookURL    = "https://bits.bytedance.net/webhook/gitlab"
)

// OptimusAppType project 和 group 的 app type 一致
type OptimusAppType int

const (
	OptimusAppUnknown       OptimusAppType = iota // 未知
	OptimusAppIOS                                 // iOS
	OptimusAppAndroid                             // Android
	OptimusAppFlutter                             // Flutter
	OptimusAppFrontend                            // 前端
	OptimusAppBackend                             // 后端
	OptimusAppFlutterApp                          // 纯flutter
	_                                             // skip
	OptimusAppMixed                               // mixed
	OptimusAppCpp                                 // C++
	OptimusAppCustomUpgrade                       // custom upgrade 自定义组件升级的仓库
	OptimusAppHarmony                             // Harmony
	OptimusAppEND
)

func (p OptimusAppType) IsValid() bool {
	return p > 0 && p < OptimusAppEND && p != 7
}

type AfterCheckType string

const (
	AfterCheckTypeDefault = "default" // 默认类型，执行主仓库pipeline流程
	AfterCheckTypeCustom  = "custom"  // 自定义类型，业务方定制
)
const (
	AwemeTemplateName  = "aweme_iOS"  // Aweme 初始化仓库代码模板
	TiktokTemplateName = "tiktok_iOS" // Tiktok 初始化仓库代码模板
)

const (
	TPPodTypeBusiness int = iota + 1
	TPPodTypeBasic
	TPPodTypeService
)

const (
	AutoPublishTypeOptimus                int = iota // optimus平台自动发版
	AutoPublishTypeBytebus                           // 组件平台自动发版
	AutoPublishTypeOptimusNoChangePodspec            // optimus平台自动发版，但是不改变podspec 版本号
)

const OptimusMaintainersGroupLarkID = "oc_7e54ee1c8d9917ed06433d3c3a191e4c"
