package app_info

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/operation_record"
	"code.byted.org/devinfra/hagrid/infra/config_service/service/tcc"
)

func (m *MetaAppUpdater) StoreMetaAppConfigOperation(ctx context.Context, userName *string) {
	if !tcc.VerifyEnable(ctx, "group_update_operation_store_enable") {
		return
	}
	operation := operation_record.GetValueDifferenceNew(ctx, m.OldConfigDB, m.CopyConfigDB, "", true)
	if len(operation) == 0 {
		return
	}
	name := "unknown"
	if userName != nil {
		name = *userName
	}
	operation.LogInfo(ctx, name, consts.GroupBasic.String())
	operation_record.StoreGroupOperations(ctx, name, consts.Update, consts.GroupBasic, m.<PERSON>, operation.ToInterfaceList())

}
