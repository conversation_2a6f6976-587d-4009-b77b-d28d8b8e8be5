package operation_record

import (
	"context"
	"database/sql"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"time"

	"code.byted.org/gopkg/logs"
	"gorm.io/gorm"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
)

// all record
func GetProjectsOperationRecord(ctx context.Context, page, pageSize int32, projectIDs []string) ([]*config_service.OptimusProjectOperationRecord, error) {
	records, err := db.CommonData.Slave.GetProjectsOperationRecord(ctx, projectIDs, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecord GetProjectsOperationRecord false error:%s", err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectsOperationRecordByDuration(ctx context.Context, page, pageSize int32, startTimeStamp, endTimeStamp int64, projectIDs []string) ([]*config_service.OptimusProjectOperationRecord, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	records, err := db.CommonData.Slave.GetProjectsOperationRecordByDuration(ctx, projectIDs, startTime, endTime, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecordByDuration GetProjectsOperationRecordByDuration false error:%s", err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectsOperationRecordByConfigType(ctx context.Context, page, pageSize int32, configType int64, projectIDs []string) ([]*config_service.OptimusProjectOperationRecord, error) {
	records, err := db.CommonData.Slave.GetProjectsOperationRecordByConfigType(ctx, projectIDs, configType, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecordByConfigType GetProjectsOperationRecordByConfigType false error:%s", err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectsOperationRecordByDurationAndConfigType(ctx context.Context, page, pageSize int32, startTimeStamp, endTimeStamp int64, configType int64, projectIDs []string) ([]*config_service.OptimusProjectOperationRecord, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	records, err := db.CommonData.Slave.GetProjectsOperationRecordByDuratioAndConfigType(ctx, projectIDs, startTime, endTime, configType, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecordByDurationAndConfigType GetProjectsOperationRecordByDuratioAndConfigType false error:%s", err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}

// all count
func GetProjectsOperationRecordCount(ctx context.Context, projectIDs []string) (int64, error) {
	count, err := db.CommonData.Slave.GetProjectsOperationRecordCount(ctx, projectIDs)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordCount GetProjectOperationRecordCount false error:%s", err.Error())
		return 0, err
	}
	return count, nil
}
func GetProjectsOperationRecordCountByDuration(ctx context.Context, startTimeStamp, endTimeStamp int64, projectIDs []string) (int64, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	count, err := db.CommonData.Slave.GetProjectsOperationRecordCountByDuration(ctx, projectIDs, startTime, endTime)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecordCountByDuration GetProjectsOperationRecordCountByDuration false error:%s", err.Error())
		return 0, err
	}
	return count, nil
}
func GetProjectsOperationRecordCountByConfigType(ctx context.Context, configType int64, projectIDs []string) (int64, error) {
	count, err := db.CommonData.Slave.GetProjectsOperationRecordCountByConfigType(ctx, projectIDs, configType)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecordCountByConfigType GetProjectsOperationRecordCountByConfigType false error:%s", err.Error())
		return 0, err
	}

	return count, nil
}
func GetProjectsOperationRecordCountByDurationAndConfigType(ctx context.Context, startTimeStamp, endTimeStamp int64, configType int64, projectIDs []string) (int64, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	count, err := db.CommonData.Slave.GetProjectsOperationRecordCountByDurationAndConfigType(ctx, projectIDs, startTime, endTime, configType)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectsOperationRecordCountByDurationAndConfigType GetProjectsOperationRecordCountByDurationAndConfigType false error:%s", err.Error())
		return 0, err
	}
	return count, nil
}

// project
func GetProjectOperationRecord(ctx context.Context, projectID string, page, pageSize int32) ([]*config_service.OptimusProjectOperationRecord, error) {
	records, err := db.CommonData.Slave.GetProjectOperationRecord(ctx, projectID, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecord GetProjectOperationRecord false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectOperationRecordCount(ctx context.Context, projectID string) (int64, error) {
	count, err := db.CommonData.Slave.GetProjectOperationRecordCount(ctx, projectID)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordCount GetProjectOperationRecordCount false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}

	return count, nil
}
func GetProjectOperationRecordByConfigType(ctx context.Context, projectID string, page, pageSize int32, configType int64) ([]*config_service.OptimusProjectOperationRecord, error) {
	records, err := db.CommonData.Slave.GetProjectOperationRecordByConfigType(ctx, projectID, configType, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordByConfigType GetProjectOperationRecordByConfigType false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectOperationRecordCountByConfigType(ctx context.Context, projectID string, configType int64) (int64, error) {
	count, err := db.CommonData.Slave.GetProjectOperationRecordCountByConfigType(ctx, projectID, configType)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordCountByConfigType GetProjectOperationRecordCountByConfigType false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}

	return count, nil
}
func GetProjectOperationRecordByDuration(ctx context.Context, projectID string, page, pageSize int32, startTimeStamp, endTimeStamp int64) ([]*config_service.OptimusProjectOperationRecord, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	records, err := db.CommonData.Slave.GetProjectOperationRecordByDuration(ctx, projectID, startTime, endTime, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecord GetProjectOperationRecordByDuration false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectOperationRecordCountByDuration(ctx context.Context, projectID string, startTimeStamp, endTimeStamp int64) (int64, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	count, err := db.CommonData.Slave.GetProjectOperationRecordCountByDuration(ctx, projectID, startTime, endTime)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordCount GetProjectOperationRecordCountByDuration false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}
	return count, nil
}
func GetProjectOperationRecordByDurationAndConfigType(ctx context.Context, projectID string, page, pageSize int32, startTimeStamp, endTimeStamp int64, configType int64) ([]*config_service.OptimusProjectOperationRecord, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	records, err := db.CommonData.Slave.GetProjectOperationRecordByDuratioAndConfigType(ctx, projectID, startTime, endTime, configType, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordByDurationAndConfigType GetProjectOperationRecordByDuratioAndConfigType false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusProjectOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusProjectConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetProjectOperationRecordCountByDurationAndConfigType(ctx context.Context, projectID string, startTimeStamp, endTimeStamp int64, configType int64) (int64, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	count, err := db.CommonData.Slave.GetProjectOperationRecordCountByDurationAndConfigType(ctx, projectID, startTime, endTime, configType)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetProjectOperationRecordCountByDurationAndConfigType GetProjectOperationRecordCountByDurationAndConfigType false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}
	return count, nil
}
func GetGroupOperationRecord(ctx context.Context, groupName string, page, pageSize int32) ([]*config_service.OptimusGroupOperationRecord, error) {
	records, err := db.CommonData.Slave.GetGroupOperationRecord(ctx, groupName, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecord GetProjectOperationRecord false groupName:%s error:%s", groupName, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusGroupOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusGroupConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetGroupOperationRecordCount(ctx context.Context, groupName string) (int64, error) {
	count, err := db.CommonData.Slave.GetGroupOperationRecordCount(ctx, groupName)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecordCount GetGroupOperationRecordCount false groupName:%s error:%s", groupName, err.Error())
		return 0, err
	}

	return count, nil
}
func GetGroupOperationRecordByConfigType(ctx context.Context, projectID string, page, pageSize int32, configType int64) ([]*config_service.OptimusGroupOperationRecord, error) {
	records, err := db.CommonData.Slave.GetGroupOperationRecordByConfigType(ctx, projectID, configType, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecordByConfigType GetGroupOperationRecordByConfigType false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusGroupOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusGroupConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetGroupOperationRecordCountByConfigType(ctx context.Context, projectID string, configType int64) (int64, error) {
	count, err := db.CommonData.Slave.GetGroupOperationRecordCountByConfigType(ctx, projectID, configType)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecordCountByConfigType GetGroupOperationRecordCountByConfigType false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}

	return count, nil
}
func GetGroupOperationRecordByDuration(ctx context.Context, projectID string, page, pageSize int32, startTimeStamp, endTimeStamp int64) ([]*config_service.OptimusGroupOperationRecord, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	records, err := db.CommonData.Slave.GetGroupOperationRecordByDuration(ctx, projectID, startTime, endTime, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecord GetGroupOperationRecordByDuration false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusGroupOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusGroupConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetGroupOperationRecordCountByDuration(ctx context.Context, projectID string, startTimeStamp, endTimeStamp int64) (int64, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	count, err := db.CommonData.Slave.GetGroupOperationRecordCountByDuration(ctx, projectID, startTime, endTime)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecordCount GetGroupOperationRecordCountByDuration false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}
	return count, nil
}
func GetGroupOperationRecordByDurationAndConfigType(ctx context.Context, projectID string, page, pageSize int32, startTimeStamp, endTimeStamp int64, configType int64) ([]*config_service.OptimusGroupOperationRecord, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	records, err := db.CommonData.Slave.GetGroupOperationRecordByDuratioAndConfigType(ctx, projectID, startTime, endTime, configType, pageSize, pageSize*(page-1))
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecordByDurationAndConfigType GetGroupOperationRecordByDuratioAndConfigType false projectID:%s error:%s", projectID, err.Error())
		return nil, err
	}
	answer := make([]*config_service.OptimusGroupOperationRecord, 0)
	for _, record := range records {
		item, err := AdaptOptimusGroupConfigToView(ctx, record)
		if err != nil {
			return nil, err
		}
		answer = append(answer, item)
	}
	return answer, nil
}
func GetGroupOperationRecordCountByDurationAndConfigType(ctx context.Context, projectID string, startTimeStamp, endTimeStamp int64, configType int64) (int64, error) {
	startTime := time.Unix(startTimeStamp, 0).Format(consts.TimeStoreLayout)
	endTime := time.Unix(endTimeStamp, 0).Format(consts.TimeStoreLayout)
	count, err := db.CommonData.Slave.GetGroupOperationRecordCountByDurationAndConfigType(ctx, projectID, startTime, endTime, configType)
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetGroupOperationRecordCountByDurationAndConfigType GetGroupOperationRecordCountByDurationAndConfigType false projectID:%s error:%s", projectID, err.Error())
		return 0, err
	}
	return count, nil
}
