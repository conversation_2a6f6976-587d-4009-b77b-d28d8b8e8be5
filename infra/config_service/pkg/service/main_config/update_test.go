package main_config

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
)

func Test_updateBasicConfigAutoGen(t *testing.T) {
	// param2.Maintainers != nil
	// param2 != nil
	// len(github.com/bytedance/sonic:Marshal()_ret-1) == 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			marshalRet1Mock := []byte{}
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var newConfigPtrValue db.OptimusConfigGroup
			newConfig := &newConfigPtrValue
			var configPtrValue config_service.OptimusServerBasicConfig
			config := &configPtrValue
			var configMaintainersPtrValue string
			config.Maintainers = &configMaintainersPtrValue

			// run target function and assert
			got1 := updateBasicConfig(newConfig, config)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// param2.Maintainers != nil
	// param2 != nil
	// len(github.com/bytedance/sonic:Marshal()_ret-1) != 0
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var marshalRet1MockItem0 byte
			marshalRet1Mock := []byte{marshalRet1MockItem0,}
			var marshalRet2Mock error
			mockey.Mock(sonic.Marshal, mockey.OptUnsafe).Return(marshalRet1Mock, marshalRet2Mock).Build()

			// prepare parameters
			var newConfigPtrValue db.OptimusConfigGroup
			newConfig := &newConfigPtrValue
			var configPtrValue config_service.OptimusServerBasicConfig
			config := &configPtrValue
			var configMaintainersPtrValue string
			config.Maintainers = &configMaintainersPtrValue

			// run target function and assert
			got1 := updateBasicConfig(newConfig, config)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func Test_updateGlobalConySettingsAutoGen(t *testing.T) {
	// param1["feature_switches"] == nil
	// param1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			oldConySettings := make(map[string]interface{})
			var oldConySettingsfeature_switchesItem interface{}
			oldConySettings["feature_switches"] = oldConySettingsfeature_switchesItem
			var configPtrValue config_service.OptimusServerGlobalConfig
			config := &configPtrValue
			convey.So(func() { updateGlobalConySettings(oldConySettings, config) }, convey.ShouldNotPanic)
		})
	})

	// param1["feature_switches"] != nil
	// param1["feature_switches"] == map[string]interface{}
	// param2 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			oldConySettings := make(map[string]interface{})
			var oldConySettingsfeature_switchesValue map[string]interface{}
			oldConySettings["feature_switches"] = oldConySettingsfeature_switchesValue
			var configPtrValue config_service.OptimusServerGlobalConfig
			config := &configPtrValue
			convey.So(func() { updateGlobalConySettings(oldConySettings, config) }, convey.ShouldPanic)
		})
	})

}

func Test_updateMRConfigAutoGen(t *testing.T) {
	// param3.ConySettings != nil
	// param3 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configConySettingsPtrValue string
			config.ConySettings = &configConySettingsPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.EnableTranslationCheck != nil
	// param3 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			// prepare parameters
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableTranslationCheckPtrValue bool
			config.EnableTranslationCheck = &configEnableTranslationCheckPtrValue
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.EnableSecurityCheck != nil
	// param3 != nil
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableSecurityCheckPtrValue bool
			config.EnableSecurityCheck = &configEnableSecurityCheckPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.MonitorLarkGroupID != nil
	// param3 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configMonitorLarkGroupIDPtrValue string
			config.MonitorLarkGroupID = &configMonitorLarkGroupIDPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.MrSubmit != nil
	// param3 != nil
	t.Run("case_4", func(t *testing.T) {
		mockey.PatchConvey("case_4", t, func() {
			// mock function returns or global values
			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configMrSubmitPtrValue string
			config.MrSubmit = &configMrSubmitPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.EnableCoverage != nil
	// param3 != nil
	t.Run("case_5", func(t *testing.T) {
		mockey.PatchConvey("case_5", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableCoveragePtrValue bool
			config.EnableCoverage = &configEnableCoveragePtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.RcPipelineEnable != nil
	// param3 != nil
	t.Run("case_6", func(t *testing.T) {
		mockey.PatchConvey("case_6", t, func() {
			// mock function returns or global values
			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configRcPipelineEnablePtrValue bool
			config.RcPipelineEnable = &configRcPipelineEnablePtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.RcChatID != nil
	// param3 != nil
	t.Run("case_7", func(t *testing.T) {
		mockey.PatchConvey("case_7", t, func() {
			// mock function returns or global values
			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configRcChatIDPtrValue string
			config.RcChatID = &configRcChatIDPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.PipelineBranchType != nil
	// param3 != nil
	t.Run("case_8", func(t *testing.T) {
		mockey.PatchConvey("case_8", t, func() {
			// mock function returns or global values
			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			// prepare parameters
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configPipelineBranchTypePtrValue string
			config.PipelineBranchType = &configPipelineBranchTypePtrValue
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.SelectHostWithComponents != nil
	// param3 != nil
	t.Run("case_9", func(t *testing.T) {
		mockey.PatchConvey("case_9", t, func() {
			// mock function returns or global values
			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			// prepare parameters
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configSelectHostWithComponentsPtrValue bool
			config.SelectHostWithComponents = &configSelectHostWithComponentsPtrValue
			var groupName string
			ctx := context.Background()

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.ShadowBranchMergeTarget != nil
	// param3 != nil
	t.Run("case_10", func(t *testing.T) {
		mockey.PatchConvey("case_10", t, func() {
			// mock function returns or global values
			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configShadowBranchMergeTargetPtrValue bool
			config.ShadowBranchMergeTarget = &configShadowBranchMergeTargetPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.ConySettings != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_11", func(t *testing.T) {
		mockey.PatchConvey("case_11", t, func() {
			// mock function returns or global values
			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			// prepare parameters
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configConySettingsPtrValue string
			config.ConySettings = &configConySettingsPtrValue
			var groupName string
			ctx := context.Background()

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.EnableTranslationCheck != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_12", func(t *testing.T) {
		mockey.PatchConvey("case_12", t, func() {
			// mock function returns or global values
			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableTranslationCheckPtrValue bool
			config.EnableTranslationCheck = &configEnableTranslationCheckPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.LarkGroupID != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_13", func(t *testing.T) {
		mockey.PatchConvey("case_13", t, func() {
			// mock function returns or global values
			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configLarkGroupIDPtrValue string
			config.LarkGroupID = &configLarkGroupIDPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.MrSubmit != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_14", func(t *testing.T) {
		mockey.PatchConvey("case_14", t, func() {
			// mock function returns or global values
			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configMrSubmitPtrValue string
			config.MrSubmit = &configMrSubmitPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.EnableRecommendRepos != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_15", func(t *testing.T) {
		mockey.PatchConvey("case_15", t, func() {
			// mock function returns or global values
			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableRecommendReposPtrValue bool
			config.EnableRecommendRepos = &configEnableRecommendReposPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.HostMrRequired != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_16", func(t *testing.T) {
		mockey.PatchConvey("case_16", t, func() {
			// mock function returns or global values
			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configHostMrRequiredPtrValue bool
			config.HostMrRequired = &configHostMrRequiredPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param3.MrTypeTipConfig != nil
	// param3 != nil
	// param2 != nil
	t.Run("case_17", func(t *testing.T) {
		mockey.PatchConvey("case_17", t, func() {
			// mock function returns or global values
			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configMrTypeTipConfigPtrValue string
			config.MrTypeTipConfig = &configMrTypeTipConfigPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.MrTypeSettings != nil
	// param3 != nil
	t.Run("case_18", func(t *testing.T) {
		mockey.PatchConvey("case_18", t, func() {
			// mock function returns or global values
			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			// prepare parameters
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configMrTypeSettingsPtrValue string
			config.MrTypeSettings = &configMrTypeSettingsPtrValue
			var groupName string
			ctx := context.Background()

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.PodChangePipelineEnable != nil
	// param3 != nil
	t.Run("case_19", func(t *testing.T) {
		mockey.PatchConvey("case_19", t, func() {
			// mock function returns or global values
			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			// prepare parameters
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configPodChangePipelineEnablePtrValue bool
			config.PodChangePipelineEnable = &configPodChangePipelineEnablePtrValue
			var groupName string
			ctx := context.Background()

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.PodChangePipelineConfigID != nil
	// param3 != nil
	t.Run("case_20", func(t *testing.T) {
		mockey.PatchConvey("case_20", t, func() {
			// mock function returns or global values
			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configPodChangePipelineConfigIDPtrValue int64
			config.PodChangePipelineConfigID = &configPodChangePipelineConfigIDPtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.EnableSDLCCheck != nil
	// param3 != nil
	t.Run("case_21", func(t *testing.T) {
		mockey.PatchConvey("case_21", t, func() {
			// mock function returns or global values
			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			// prepare parameters
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableSDLCCheckPtrValue bool
			config.EnableSDLCCheck = &configEnableSDLCCheckPtrValue
			var groupName string
			ctx := context.Background()

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.EnableQaReviewCheck != nil
	// param3 != nil
	t.Run("case_22", func(t *testing.T) {
		mockey.PatchConvey("case_22", t, func() {
			// mock function returns or global values
			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configEnableQaReviewCheckPtrValue bool
			config.EnableQaReviewCheck = &configEnableQaReviewCheckPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.RcPipelineType != nil
	// param3 != nil
	t.Run("case_23", func(t *testing.T) {
		mockey.PatchConvey("case_23", t, func() {
			// mock function returns or global values
			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configRcPipelineTypePtrValue int64
			config.RcPipelineType = &configRcPipelineTypePtrValue
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// param2 != nil
	// param3.ConflictSolveTools != nil
	// param3 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/main_config:VerifyConflictSolveTools()_ret-1 != nil
	t.Run("case_24", func(t *testing.T) {
		mockey.PatchConvey("case_24", t, func() {
			// mock function returns or global values
			var getBatchMergeTargetRet1Mock int8
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			verifyConflictSolveToolsRet1Mock := fmt.Errorf("error")
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			config.ConflictSolveTools = []string{}
			var groupName string

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// param2 != nil
	// param3.BatchMergeTarget != nil
	// param3 != nil
	// code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service.OptimusServerMRConfig:GetBatchMergeTarget()_ret-1 < 0
	t.Run("case_25", func(t *testing.T) {
		mockey.PatchConvey("case_25", t, func() {
			// mock function returns or global values
			var getRcPipelineTemplateIdRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineTemplateId, mockey.OptUnsafe).Return(getRcPipelineTemplateIdRet1Mock).Build()

			var getConflictSolveToolsRet1Mock []string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetConflictSolveTools, mockey.OptUnsafe).Return(getConflictSolveToolsRet1Mock).Build()

			getBatchMergeTargetRet1Mock := int8(-1)
			mockey.Mock((*config_service.OptimusServerMRConfig).GetBatchMergeTarget, mockey.OptUnsafe).Return(getBatchMergeTargetRet1Mock).Build()

			var getRcPipelineTypeRet1Mock int64
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineType, mockey.OptUnsafe).Return(getRcPipelineTypeRet1Mock).Build()

			var marshalStringRet1Mock string
			var marshalStringRet2Mock error
			mockey.Mock(sonic.MarshalString, mockey.OptUnsafe).Return(marshalStringRet1Mock, marshalStringRet2Mock).Build()

			var verifyRcPipelineReviewersRet1Mock error
			mockey.Mock(VerifyRcPipelineReviewers).Return(verifyRcPipelineReviewersRet1Mock).Build()

			var getEnableQaReviewCheckRet1Mock bool
			mockey.Mock((*config_service.OptimusServerMRConfig).GetEnableQaReviewCheck, mockey.OptUnsafe).Return(getEnableQaReviewCheckRet1Mock).Build()

			var getRcPipelineMrCollaboratorsRet1Mock string
			mockey.Mock((*config_service.OptimusServerMRConfig).GetRcPipelineMrCollaborators, mockey.OptUnsafe).Return(getRcPipelineMrCollaboratorsRet1Mock).Build()

			var verifyConflictSolveToolsRet1Mock error
			mockey.Mock(VerifyConflictSolveTools).Return(verifyConflictSolveToolsRet1Mock).Build()

			// prepare parameters
			var groupName string
			ctx := context.Background()
			var copyConfigGroupPtrValue db.OptimusConfigGroup
			copyConfigGroup := &copyConfigGroupPtrValue
			var configPtrValue config_service.OptimusServerMRConfig
			config := &configPtrValue
			var configBatchMergeTargetPtrValue int8
			config.BatchMergeTarget = &configBatchMergeTargetPtrValue

			// run target function and assert
			got1 := updateMRConfig(ctx, copyConfigGroup, config, groupName)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

