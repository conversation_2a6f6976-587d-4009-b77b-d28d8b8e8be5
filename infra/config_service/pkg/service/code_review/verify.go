package code_review

import (
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
	"context"
	"errors"
	"fmt"
	json "github.com/bytedance/sonic"
	"github.com/dlclark/regexp2"
)

func VerifyReviewMode(mode string) error {
	if mode != "gitlab" && mode != "bits" {
		return errors.New(fmt.Sprintf("VerifyReviewMode false unvalid mode: %s", mode))
	}
	return nil
}

func VerifyReviewRules(ctx context.Context, rule *db.OptimusConfigReviewRules) error {
	if rule == nil {
		logs.CtxError(ctx, "VerifyReviewRule false review rules null")
	}
	_, err := regexp2.Compile(rule.Rules, 0)
	if err != nil {
		logs.CtxError(ctx, "VerifyReviewRules false invalid rules")
		return err
	}
	if rule.Version == int8(consts.ReviewRuleVersionV2) {
		reviewersV2 := make([]*ReviewersV2, 0)
		if err := json.Unmarshal([]byte(rule.Reviewers), &reviewersV2); err != nil {
			logs.CtxError(ctx, "VerifyReviewRules Unmarshal reviewersV2 false reviewers:%s  error:%s", rule.Reviewers, err.Error())
			return err
		}
	}
	return nil
}

func VerifyCodeReviewAllConfig(config *config_service.ProjectCodeReviewConfig) error {
	if config == nil {
		return bits_err.COMMON.ErrInvalidInput
	}
	if config.ReviewerResetMode != nil {
		if !consts.ReviewerResetMode(config.GetReviewerResetMode()).IsValid() {
			return bits_err.COMMON.ErrInvalidInput.AddErrMsg("reviewer_reset_mode invalid")
		}
	}
	return nil
}
