load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "config_api",
    srcs = [
        "optimus.go",
        "qcss.go",
        "sdlc.go",
        "update_main_config.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/config_api",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/config_service/kitex_gen/bytedance/bits/config_service",
        "//infra/config_service/pkg/consts",
        "//infra/config_service/pkg/utils",
        "//infra/config_service/service/tcc",
        "//infra/config_service/utils",
        "@com_github_bytedance_sonic//:sonic",
        "@in_gopkg_resty_v1//:resty_v1",
        "@org_byted_code_bytedtrace_bytedtrace_client_go//utils",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "config_api_test",
    srcs = [
        "optimus_test.go",
        "qcss_test.go",
        "sdlc_test.go",
        "update_main_config_test.go",
    ],
    embed = [":config_api"],
    deps = [
        "//infra/config_service/kitex_gen/bytedance/bits/config_service",
        "//infra/config_service/pkg/utils",
        "//infra/config_service/service/tcc",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_go_resty_resty_v2//:resty",
        "@com_github_smartystreets_goconvey//convey",
        "@in_gopkg_resty_v1//:resty_v1",
        "@org_byted_code_bytedtrace_bytedtrace_client_go//utils",
        "@org_byted_code_gopkg_env//:env",
    ],
)
