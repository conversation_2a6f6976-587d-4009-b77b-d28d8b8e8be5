package config_api

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/utils"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
)

var (
	client          = utils.NewHTTPClient().SetTimeout(5 * time.Second)
	clientWithRetry = utils.NewHTTPClient().SetRetryCount(3).SetTimeout(5 * time.Second)
)

const (
	OfflineHost = "https://bytest-boe.bytedance.net"
	OnlineHost  = "https://bytest.bytedance.net"
)

func getQCSSHost() string {
	if env.IsProduct() || env.IsPPE() {
		return OnlineHost
	} else {
		return OfflineHost
	}
}

type ConfigIndex struct {
	SpaceId        int64  `json:"space_id"`
	TemplateId     int64  `json:"template_id"`
	TemplateFrom   string `json:"template_from"`
	TemplateStage  string `json:"template_stage"`
	GateKeeperType string `json:"gate_keeper_type"`
}

type GetConfigReq struct {
	RelationConfigIndex ConfigIndex `json:"relation_config_index"`
}

type GetConfigResp struct {
	Code               int             `json:"code"`
	Message            string          `json:"message"`
	RelationConfigInfo *RelationConfig `json:"relation_config_info"`
}

// GetQCSSConfig deal with not configured situation, return error ThirdPartyConfigNotFound
func GetQCSSConfig(ctx context.Context, req ConfigIndex) (*GetConfigResp, error) {
	u := getQCSSHost() + "/quality/api/v1/config/workflow/get"
	response := &GetConfigResp{}

	_, err := clientWithRetry.R().SetContext(ctx).SetBody(GetConfigReq{
		RelationConfigIndex: req,
	}).SetResult(response).Post(u)
	if err != nil {
		logs.CtxError(ctx, "get qcss config from quality failed error=%s", err.Error())
		return nil, err
	}

	if response.RelationConfigInfo == nil || (response.Code == -1 && response.Message == "record not found") {
		logs.CtxInfo(ctx, "get config from quality record not found")
		return nil, consts.ThirdPartyConfigNotFound
	}
	return response, nil
}

type CopyConfigReq struct {
	SpaceId         int64  `json:"space_id"`
	TemplateId      int64  `json:"template_id"`
	TemplateFrom    string `json:"template_from"`
	NewTemplateId   int64  `json:"new_template_id"`
	NewTemplateName string `json:"new_template_name"`
}

func CopyQCSSConfig(ctx context.Context, req CopyConfigReq) (err error) {
	resp := &UpsertConfigResp{}
	u := getQCSSHost() + "/quality/api/v1/config/workflow/copy"
	_, err = client.R().SetContext(ctx).SetBody(req).SetResult(resp).Post(u)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	return
}

func UpdateQCSSConfig(ctx context.Context, body UpsertConfigReq) (err error) {
	resp := &UpsertConfigResp{}
	u := getQCSSHost() + "/quality/api/v1/config/workflow/update"
	rsp, err := client.R().SetContext(ctx).SetBody(body).SetResult(resp).Post(u)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	if err, ok := rsp.Error().(error); err != nil && ok {
		logs.CtxError(ctx, err.Error())
		return err
	}
	return
}

func CreateQCSSConfig(ctx context.Context, body UpsertConfigReq) (err error) {
	resp := &UpsertConfigResp{}
	u := getQCSSHost() + "/quality/api/v1/config/workflow/create"
	rsp, err := client.R().SetContext(ctx).SetBody(body).SetResult(resp).Post(u)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	if err, ok := rsp.Error().(error); err != nil && ok {
		logs.CtxError(ctx, err.Error())
		return err
	}
	return nil
}

type UpsertConfigReq struct {
	RelationConfigIndex ConfigIndex    `json:"relation_config_index"`
	RelationConfigInfo  QCSSConfigInfo `json:"relation_config_info"`
}

type QCSSConfigInfo struct {
	Creator         string `json:"creator"`
	TemplateName    string `json:"template_name"`
	QCSSStage       string `json:"qcss_stage"`
	QCSSProcess     string `json:"qcss_process"`
	QualityPlatform string `json:"quality_platform"`
	ShepherdStage   int64  `json:"shepherd_stage"`
}

type UpsertConfigResp struct {
	ErrNo   int    `json:"err_no"`
	Message string `json:"message"`
	Data    int64  `json:"data"`
	Code    int    `json:"code"`
}

type RelationConfig struct {
	Creator         string                       `json:"creator"`
	QCSSStage       string                       `json:"qcss_stage"`
	QCSSProcess     string                       `json:"qcss_process"`
	CreatedAt       int64                        `json:"created_at"`
	UpdatedAt       int64                        `json:"updated_at"`
	QualityPlatform string                       `json:"quality_platform"`
	ShepherdStage   config_service.ShepherdStage `json:"shepherd_stage"`
}

// RiskDetail 单个风险项
type RiskDetail struct {
	Channel        string    `json:"channel"`
	Type           string    `json:"type"`
	TypeCn         string    `json:"type_cn"`
	TypeI18n       string    `json:"type_i18n"`
	TitleCn        string    `json:"title_cn"`
	TitleI18n      string    `json:"title_i18n"`
	ID             string    `json:"id"`
	Level          string    `json:"level"`
	SuggestionCn   string    `json:"suggestion_cn"`
	SuggestionI18n string    `json:"suggestion_i18n"`
	CreateTime     int64     `json:"create_time"`
	UpdateTime     int64     `json:"update_time"`
	ProcessStatus  string    `json:"process_status"`
	ServiceName    *string   `json:"service_name,omitempty"`
	ReadOnly       *bool     `json:"read_only,omitempty"`
	WorkItem       *WorkItem `json:"work_item,omitempty"`
}

// WorkItem 工作项
type WorkItem struct {
	ID         int64  `json:"id"`          // meego id
	ProjectKey string `json:"project_key"` // 项目键
	Title      string `json:"title"`       // 标题
	URL        string `json:"url"`         // 链接
	Type       string `json:"type"`        // 工作项类型
}

// GetMeegoRiskTaskDetailReq 通过需求查询风险的请求
type GetMeegoRiskTaskDetailReq struct {
	SpaceID   int64       `json:"space_id"`
	WorkItems []*WorkItem `json:"work_items,omitempty"`
}

// GetRiskDetailResp 返回风险详情
type GetRiskDetailResp struct {
	Code          int64         `json:"code"`
	Message       string        `json:"message"`
	SpaceID       int64         `json:"space_id"`
	TaskType      *string       `json:"task_type,omitempty"`
	Engine        string        `json:"engine"`
	CheckTargetID string        `json:"check_target_id"`
	RiskList      []*RiskDetail `json:"risk_list,omitempty"`
	Status        *string       `json:"status,omitempty"`
	RiskLevel     *string       `json:"risk_level,omitempty"`
	CreateTime    *int64        `json:"create_time,omitempty"`
	UpdateTime    *int64        `json:"update_time,omitempty"`
}

// GetMeegoRiskTaskDetail get meego risk task detail
func GetMeegoRiskTaskDetail(ctx context.Context, req *GetMeegoRiskTaskDetailReq) (*GetRiskDetailResp, error) {
	u := getQCSSHost() + "/quality/api/v1/risk/meego_risk/detail"
	response := &GetRiskDetailResp{}
	cli := clientWithRetry.R().SetContext(ctx)
	if env.IsPPE() {
		cli.SetHeaders(map[string]string{
			"x-tt-env":  env.Env(),
			"x-use-ppe": "1",
		})
	}
	_, err := cli.SetBody(req).SetResult(response).Post(u)
	if err != nil {
		logs.CtxError(ctx, "get meego task detail from quality failed error=%s", err.Error())
		return nil, err
	}
	return response, nil
}
