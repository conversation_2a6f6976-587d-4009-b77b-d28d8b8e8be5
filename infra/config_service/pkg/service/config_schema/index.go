package config_schema

import (
	"code.byted.org/gopkg/logs"
	"context"
	"database/sql"
	"github.com/bytedance/sonic"
	"gorm.io/gorm"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/consts"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/data"
	Common_config "code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/common_config"
)

func GetConfigSchemaByFormNameAndCategory(ctx context.Context, formName, category string) (string, error) {
	answer := &db.OptimusConfigSchema{}
	answer, err := data.GetConfigSchemaByFormNameAndCategory(ctx, formName, category, true)
	if err != nil {
		logs.CtxError(ctx, "GetConfigSchemaByFormNameAndCategory GetConfigSchemaByNameActived failed category=%s  error=%s", category, err.Error())
		return "", err
	}
	if answer == nil {
		return "", nil
	}
	return answer.JsonSchema, nil
}

/*
category is used to distinguish domain.
inuse categories now:
-rd_process_configs_dynamic: for the group domain
-repo_configs_dynamic: for the project domain
-default: for test only, no domain limit, won't sync
*/
func UpdateConfigSchemaByName(ctx context.Context, formName, category, schema, operator string) error {
	switch category {
	// sync new config key to completed keys record
	case "rd_process_configs_dynamic":
		configItems, _ := getConfigItemsBySchema(ctx, schema)
		if len(configItems) > 0 {
			cc := &Common_config.CommonConfig{
				Operator: consts.CommonConfigBot,
			}
			configItemsStr, err := sonic.Marshal(configItems)
			if err != nil {
				logs.CtxError(ctx, "json marshal failed error:%s items:%v", err.Error(), configItems)
				return consts.ErrUpdateConfigSchema
			}
			option := config_service.UpdateOption_UpdateOrCreate
			if err := cc.UpdateGroupCommonConfig(ctx, consts.CompletedGroupCommonConfigKeysGroupName, string(configItemsStr), &option); err != nil {
				return consts.ErrUpdateConfigSchema
			}
		}
	case "repo_configs_dynamic":
		configItems, _ := getConfigItemsBySchema(ctx, schema)
		if len(configItems) > 0 {
			cc := &Common_config.CommonConfig{
				Operator: consts.CommonConfigBot,
			}
			configItemsStr, err := sonic.Marshal(configItems)
			if err != nil {
				logs.CtxError(ctx, "json marshal failed error:%s items:%v", err.Error(), configItems)
				return consts.ErrUpdateConfigSchema
			}
			option := config_service.UpdateOption_UpdateOrCreate
			if err := cc.UpdateProjectCommonConfig(ctx, consts.CompletedProjectCommonConfigKeysProjectID, string(configItemsStr), &option); err != nil {
				return consts.ErrUpdateConfigSchema
			}
		}
	default:
		logs.CtxInfo(ctx, "test category:%s without sync", category)
	}
	return data.UpdateConfigSchema(ctx, formName, category, schema, operator)
}

func getConfigItemsBySchema(ctx context.Context, schema string) (map[string]interface{}, error) {
	answer := make(map[string]interface{}, 0)
	configSchema := make(map[string]interface{}, 0)
	if err := sonic.UnmarshalString(schema, &configSchema); err != nil {
		logs.CtxError(ctx, "UpdateConfigSchemaByName UnmarshalString failed error:%s", err.Error())
		return answer, consts.ErrUpdateConfigSchema
	}
	formItems, ok := configSchema["formItems"]
	if !ok {
		logs.CtxError(ctx, "schema no formItems")
		return answer, consts.ErrUpdateConfigSchema
	}
	items, ok := formItems.([]interface{})
	if !ok {
		logs.CtxError(ctx, "schema invalid formItems")
		return answer, consts.ErrUpdateConfigSchema
	}
	answer, err := scanFormItems(ctx, items)
	if err != nil {
		return answer, consts.ErrUpdateConfigSchema
	}
	return answer, nil
}
func scanFormItems(ctx context.Context, formItems []interface{}) (map[string]interface{}, error) {
	answer := make(map[string]interface{}, 0)
	for _, item := range formItems {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			logs.CtxError(ctx, "schema invalid item")
			return answer, consts.ErrUpdateConfigSchema
		}
		itemInfo, ok := itemMap["item"]
		if ok {
			if itemMap, ok := itemInfo.(map[string]interface{}); ok {
				if field, ok := itemMap["field"]; ok {
					answer[field.(string)] = ConfigItem{}
				}
			}
		} else {
			listChildren, ok := itemMap["listChildren"]
			if !ok {
				logs.CtxError(ctx, "schema invalid formItems")
				return answer, consts.ErrUpdateConfigSchema
			}
			listChildrenMap, ok := listChildren.([]interface{})
			if !ok {
				logs.CtxError(ctx, "schema invalid listChildren")
				return answer, consts.ErrUpdateConfigSchema
			}
			res, err := scanFormItems(ctx, listChildrenMap)
			if err != nil {
				return answer, err
			}
			// copy res to answer
			if len(res) > 0 {
				for k, v := range res {
					answer[k] = v
				}
			}
		}
	}
	return answer, nil
}
func GetConfigSchemaList(ctx context.Context, formName, category string, page, pageSize int64) ([]*config_service.ConfigSchema, error) {
	configDBList := make([]*db.OptimusConfigSchema, 0)
	answer := make([]*config_service.ConfigSchema, 0)
	var err error
	if len(category) == 0 && len(formName) == 0 {
		configDBList, err = db.Optimus.Slave.GetConfigSchemasActivedWithPage(ctx, pageSize, pageSize*(page-1))
	} else if len(category) == 0 {
		configDBList, err = db.Optimus.Slave.GetConfigSchemasByFormNameActivedWithPage(ctx, formName, pageSize, pageSize*(page-1))
	} else if len(formName) == 0 {
		configDBList, err = db.Optimus.Slave.GetConfigSchemasByCategoryActivedWithPage(ctx, category, pageSize, pageSize*(page-1))
	} else {
		configDBList, err = db.Optimus.Slave.GetConfigSchemasByFormNameAndCategoryActivedWithPage(ctx, formName, category, pageSize, pageSize*(page-1))
	}
	if err != nil && err != gorm.ErrRecordNotFound && err != sql.ErrNoRows {
		logs.CtxError(ctx, "GetConfigSchemaList failed formName:%s category:%s error:%s", err.Error())
		return nil, err
	}
	if len(configDBList) > 0 {
		answer = AdaptConfigSchemaDBListToIDL(configDBList)
	}
	return answer, nil
}
func GetConfigSchemasCount(ctx context.Context, formName, category string) (int64, error) {
	count := int64(0)
	var err error
	if len(category) == 0 && len(formName) == 0 {
		count, err = db.Optimus.Slave.GetConfigSchemasActivedWithPageCount(ctx)
	} else if len(category) == 0 {
		count, err = db.Optimus.Slave.GetConfigSchemasByFormNameActivedWithPageCount(ctx, formName)
	} else if len(formName) == 0 {
		count, err = db.Optimus.Slave.GetConfigSchemasByCategoryActivedWithPageCount(ctx, category)
	} else {
		count, err = db.Optimus.Slave.GetConfigSchemasByFormNameAndCategoryActivedWithPageCount(ctx, formName, category)
	}
	if err != nil {
		logs.CtxError(ctx, "GetConfigSchemasCount failed formName:%s category:%s error:%s", err.Error())
		return count, err
	}
	return count, nil
}

func GetConfigSchemaCategories(ctx context.Context) ([]string, error) {
	categories, err := db.Optimus.Slave.GetConfigSchemaCategories(ctx)
	if err != nil {
		logs.CtxError(ctx, "GetConfigSchemasCount failed formName:%s category:%s error:%s", err.Error())
		return nil, err
	}
	return categories, nil
}

func DeleteConfigSchema(ctx context.Context, formName, category string) error {
	if err := data.DeleteConfigSchema(ctx, formName, category); err != nil {
		return err
	}
	return nil
}
