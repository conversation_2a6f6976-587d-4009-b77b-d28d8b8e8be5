package workflowmanager

import (
	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/service/mq_producer"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/typing"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"context"
	"fmt"
	"github.com/bytedance/sonic"
	"github.com/r3labs/diff/v3"
	"golang.org/x/sync/errgroup"
	"strconv"
	"strings"
)

var jsonDiffer, _ = diff.NewDiffer(diff.TagName("json"))

// node 的开关其实是存在了 snapshot 里
func (manager *WorkflowConfigurationManager) UpdateOnesiteWorkflowNode(ctx context.Context, req *config_service.UpdateOnesiteWorkflowNodeRequest) (*base.EmptyResponse, error) {

	nodeConfigJson, err := TransferOnesiteNodeConfigRPC2DB(ctx, req.NodeConfig)
	if err != nil {
		return nil, err
	}

	err = manager.dynamicCreateRDTestTaskNode(ctx, req)
	if err != nil {
		return nil, err
	}

	oldDbNode, err := manager.workflowNodeRepository.FindById(ctx, req.Id, true)
	if db.MysqlNoRows(err) {
		err = bits_err.CONFIG.ErrBadRequest.AddErrMsg(err.Error())
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	if err != nil {
		err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
		logs.CtxError(ctx, err.Error())
		return nil, err
	}

	newDbNode := gptr.Clone(oldDbNode)

	eg, _ := errgroup.WithContext(ctx)
	//
	eg.Go(func() error {
		// update node
		updateNodeMap := map[string]interface{}{}
		if req.Name != oldDbNode.Name {
			updateNodeMap["name"] = req.Name
			newDbNode.Name = req.Name
		}
		if req.NameI18n != oldDbNode.NameI18n {
			updateNodeMap["name_i18n"] = req.NameI18n
			newDbNode.NameI18n = req.NameI18n
		}
		if nodeConfigJson != oldDbNode.NodeConfig {
			updateNodeMap["node_config"] = nodeConfigJson
			newDbNode.NodeConfig = nodeConfigJson
		}
		if len(updateNodeMap) == 0 {
			// 无需 update
			return nil
		}

		err = manager.workflowNodeRepository.UpdateBasicInfoWithUpdatesDictById(ctx, req.Id, updateNodeMap)
		if err != nil {
			err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
			logs.CtxError(ctx, err.Error())
			return err
		}
		return nil
	})

	//
	var oldNodeSnapshotInfo, newNodeSnapshotInfo typing.Node
	eg.Go(func() error {
		// 不用间隙锁了
		dbSnapshotPart, err := manager.workflowSnapshotPartRepository.FindLastByWorkflowId(ctx, oldDbNode.WorkflowId, true).Get()
		if err != nil {
			err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
			logs.CtxError(ctx, err.Error())
			return err
		}

		//
		fixedNameDag := dbSnapshotPart.GetDAG()
		fixedNameNode, ok := gslice.Find(fixedNameDag.FixedNameDag, func(node *typing.Node) bool {
			return node.FixedName == oldDbNode.FixedName
		}).Get()
		if !ok {
			gErr := bits_err.CONFIG.ErrDBError.AddErrMsg(fmt.Sprintf("%s not find in snapshot fixed name dag", oldDbNode.FixedName))
			logs.CtxError(ctx, gErr.Error())
			return gErr
		}

		//
		if fixedNameNode.Enabled == req.Enabled {
			return nil
		}

		//
		oldNodeSnapshotInfo.Enabled = fixedNameNode.Enabled
		fixedNameNode.Enabled = req.Enabled
		newNodeSnapshotInfo.Enabled = req.Enabled

		dbSnapshotPart.SetDAG(fixedNameDag)

		item := entity.WorkflowSnapshotPart{
			WorkflowId: oldDbNode.WorkflowId,
			Dag:        dbSnapshotPart.Dag,
		}
		_, dbErr := manager.workflowSnapshotPartRepository.Create(ctx, &item).Get()
		if dbErr != nil {
			return dbErr
		}
		return nil
	})
	err = eg.Wait()
	if err != nil {
		return nil, err
	}

	manager.AsyncUpdateRealtimePartTableForNode(ctx, oldDbNode.WorkflowId, 0, req.Username)
	AsyncSendEditWorkflowNode(ctx, req.SpaceId, oldDbNode.WorkflowId, req.Username, *oldDbNode, *newDbNode, oldNodeSnapshotInfo, newNodeSnapshotInfo)
	return &base.EmptyResponse{}, nil
}

func (manager *WorkflowConfigurationManager) dynamicCreateRDTestTaskNode(ctx context.Context, req *config_service.UpdateOnesiteWorkflowNodeRequest) error {

	// 动态创建 RDTestTaskNode
	if req.Id == 0 && req.NodeFixedName == config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask {

		snapshotPart, err := manager.workflowSnapshotPartRepository.FindLastByWorkflowId(ctx, req.WorkflowId, true).Get()
		if err != nil {
			err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
			logs.CtxError(ctx, err.Error())
			return err
		}

		node, err := manager.workflowNodeRepository.FindLastByWorkflowIdAndFixedName(ctx, req.WorkflowId, config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask, true).Get()
		if err != nil {
			if !db.MysqlNoRows(err) {
				err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
				logs.CtxError(ctx, err.Error())
				return err
			} else {
				ciServiceNodeConfig := &config_service.CIServiceNodeConfig{
					CanSkip: true,
				}
				item := &entity.WorkflowNode{
					NodeType:        int8(config_service.OnesiteWorkflowNodeType_CI_SERVICE_NODE),
					EditDisabled:    0,
					WorkflowId:      req.WorkflowId,
					Name:            req.Name,
					NameI18n:        req.NameI18n,
					NodeConfig:      jsons.Stringify(ciServiceNodeConfig),
					Creator:         "",
					Updater:         "",
					FixedName:       config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask,
					Description:     req.Description,
					DescriptionI18n: req.DescriptionI18n,
				}
				nodeId := int64(0)
				nodeId, err = manager.workflowNodeRepository.Create(ctx, item).Get()
				if err != nil {
					err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
					logs.CtxError(ctx, err.Error())
					return err
				}

				rdTaskExists := false
				rdTaskExistsInNext := false

				dag := snapshotPart.GetDAG()

				for _, v := range dag.FixedNameDag {
					if v.FixedName == config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask {
						rdTaskExists = true
					}
					if v.FixedName == config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageUserBeforeTask {
						if gslice.Contains(v.NextNodeFixedNames, config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask) {
							rdTaskExistsInNext = true
						}
					}
				}

				if !rdTaskExists {
					dag.FixedNameDag = append(dag.FixedNameDag, &typing.Node{
						Stage:          config_service.OnesiteWorkflowStageFixedName_DevDevelopStage,
						Enabled:        false,
						Hidden:         false,
						FixedName:      config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask,
						EditDisabled:   false,
						EnableDisabled: false,
						NextNodeFixedNames: []string{
							config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageUserAfterTask,
						},
					})
					snapshotPart.SetDAG(dag)
				}

				if !rdTaskExistsInNext {
					for _, v := range dag.FixedNameDag {
						if v.FixedName == config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageUserBeforeTask {
							v.NextNodeFixedNames = append(v.NextNodeFixedNames, config_service.OnesiteWorkflowNodeFixedName_DevDevelopStageRDTestTask)
							v.NextNodeFixedNames = gslice.Uniq(v.NextNodeFixedNames) // 去重复
						}
					}
					snapshotPart.SetDAG(dag)
				}

				err = manager.workflowSnapshotPartRepository.UpdateById(ctx, snapshotPart.Id, snapshotPart)
				if err != nil {
					err = bits_err.CONFIG.ErrDBError.AddErrMsg(err.Error())
					logs.CtxError(ctx, err.Error())
					return err
				}

				// 使用新建的 node id 更新 req 中的 id
				req.Id = nodeId
				return nil
			}
		} else {

			// 使用已经存在的 node id 更新 req 中的 id
			req.Id = node.Id
			return nil
		}
	}
	return nil
}

func (manager *WorkflowConfigurationManager) AsyncUpdateRealtimePartTableForNode(ctx context.Context, workflowId int64, nodeId int64, username string) {

	gCtx := utils.CopyKiteXContext(ctx)
	// version+1
	utils.SafeGo(ctx, func() {
		_ = manager.workflowRealtimePartRepository.UpdateUpdaterById(gCtx, workflowId, username)
	})
}

func AsyncSendEditWorkflowNode(ctx context.Context, spaceId int64, workflowId int64,
	operator string, oldValue, newValue entity.WorkflowNode, oldSnapshotNode, newSnapshotNode typing.Node) {

	//
	rpcOldValue, err := TransferOnesiteNodeDB2RPC(ctx, oldValue, oldSnapshotNode)
	if err != nil {
		return
	}
	rpcNewValue, err := TransferOnesiteNodeDB2RPC(ctx, newValue, newSnapshotNode)
	if err != nil {
		return
	}

	//
	changeLog, err := jsonDiffer.Diff(rpcOldValue, rpcNewValue)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	diffs := changelog2ColumnDiffs(ctx, changeLog)
	if len(diffs) == 0 {
		return
	}

	asyncSendInfoRecord(ctx, diffs, rpcOldValue, rpcNewValue, &events.InfoRecordEvent{
		ContentType:      events.InfoRecordEventContentType_JustParam,
		SpaceId:          uint64(spaceId),
		Parent:           events.InfoRecordEventParent_DevTaskWorkflowConfig,
		ParentPrimaryKey: strconv.FormatInt(workflowId, 10),
		EventType:        events.InfoRecordEventType_ConfigEditWorkflowNode,
		EventName:        events.InfoRecordEventName_ConfigEditWorkflowNode,
		Operator:         operator,
	})
}

type columnDiff struct {
	DiffColumn string `json:"diffColumn"`
	OldValue   string `json:"oldValue"`
	NewValue   string `json:"newValue"`
}

func changelog2ColumnDiffs(ctx context.Context, changeLog diff.Changelog) []columnDiff {
	//
	ret := make([]columnDiff, 0)

	diffColumnSet := set.New[string]()
	for _, change := range changeLog {

		oldColumnValueStr, jErr := sonic.MarshalString(change.From)
		if jErr != nil {
			logs.CtxInfo(ctx, "change log to column diffs: marshal old value error: (%v)", jErr)
			continue
		}

		newColumnValueStr, jErr := sonic.MarshalString(change.To)
		if jErr != nil {
			logs.CtxInfo(ctx, "change log to column diffs: marshal new value error: (%v)", jErr)
			continue
		}

		// 去除数组索引后的部分
		var truncatedPaths []string
		for _, path := range change.Path {
			_, numErr := strconv.ParseInt(path, 10, 64)
			if numErr == nil {
				oldColumnValueStr = ""
				newColumnValueStr = ""
				break
			}
			truncatedPaths = append(truncatedPaths, path)
		}

		diffColumn := strings.Join(truncatedPaths, ".")
		if strings.HasPrefix(diffColumn, "nodeConfig.ciPipelineNodeConfig.env") {
			diffColumn = "nodeConfig.ciPipelineNodeConfig.env"
			oldColumnValueStr = ""
			newColumnValueStr = ""
		}
		if diffColumnSet.Contains(diffColumn) {
			continue
		}
		diffColumnSet.Add(diffColumn)
		ret = append(ret, columnDiff{
			DiffColumn: diffColumn,
			OldValue:   oldColumnValueStr,
			NewValue:   newColumnValueStr,
		})
	}
	return ret
}

func asyncSendInfoRecord(ctx context.Context, paramValue, oldValue, newValue interface{}, event *events.InfoRecordEvent) {
	//
	if event == nil {
		return
	}
	//
	if paramValue != nil {
		event.ParamValue, _ = sonic.MarshalString(paramValue)
	}
	if oldValue != nil {
		event.OldValue, _ = sonic.MarshalString(oldValue)
	}
	if newValue != nil {
		event.NewValue, _ = sonic.MarshalString(newValue)
	}
	mq_producer.CoreEventProducer.SendInfoRecord(ctx, event, true)
}
