package duty

import (
	"context"
	"errors"
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/app/devjobs/dutyv5"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/backends/duty"
	pkgduty "code.byted.org/devinfra/hagrid/pkg/resource/duty"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs/v2"
	"github.com/samber/lo"
)

var dutyApi = duty.New()

func GetDutyList(ctx context.Context, req *config_service.GetDutyListRequest) (resp *config_service.GetDutyListResponse, err error) {
	dutyList, err := dutyApi.WithIdc(req.GetIdc()).GetDutyList(ctx, req.GetSearch())
	if err != nil {
		return
	}
	return &config_service.GetDutyListResponse{
		Dutys: lo.Flatten(lo.MapToSlice(dutyList, func(key string, value *pkgduty.GetDutyListResp) []*config_service.DutyItem {
			return lo.Map(value.Results, func(from pkgduty.DutyItem, _ int) *config_service.DutyItem {
				return &config_service.DutyItem{
					DutyName:        from.Name,
					DutyDisplayName: from.DisplayName,
					Idc:             key,
				}
			})
		})),
	}, nil
}

func GetDutyInfo(ctx context.Context, req *config_service.GetDutyInfoRequest) (resp *config_service.GetDutyInfoResponse, err error) {
	resp = &config_service.GetDutyInfoResponse{}
	dutyInfo, err := dutyApi.WithIdc(req.GetIdc()).GetDutyInfo(ctx, req.GetDutyName())
	if err != nil {
		return
	}
	resp.DutyInfo = &config_service.DutyInfo{
		Name:            dutyInfo.Name,
		DisplayName:     dutyInfo.DisplayName,
		CandidateGroups: adapterCandidateGroups(dutyInfo),
		Period:          dutyInfo.Period,
		Idc:             req.GetIdc(),
	}
	return
}

func GetForecastApprovers(ctx context.Context, req *config_service.GetForecastApproversRequest) (resp *config_service.GetForecastApproversResponse, err error) {
	resp = &config_service.GetForecastApproversResponse{}
	if len(req.GetDutyname()) == 0 {
		return nil, errors.New("required dutyName")
	}
	resp.Approvers, err = getDutyApprovers(ctx, req.GetIdc(), req.GetForecastTime(), &config_service.DutyGroup{
		DutyName: req.GetDutyname(),
		GroupId:  req.GetGroupId(),
	})
	return
}

func adapterCandidateGroups(info pkgduty.DutyInfo) []*config_service.CandidateGroup {
	groups := make([]*config_service.CandidateGroup, 0)

	for i, group := range info.CandidateGroups {
		groups = append(groups, &config_service.CandidateGroup{
			Candidates: lo.Map(group.Candidates, func(candidate *pkgduty.Candidate, _ int) *config_service.Candidate {
				return &config_service.Candidate{
					Primary: candidate.Primary,
					Backups: candidate.Backups,
					Id:      candidate.Id,
				}
			}),
			Id:        int64(i),                     // 使用index作为绑定id，因为如果用groupid的话在duty上每一次编辑都会产生新的groupid
			GroupName: fmt.Sprintf("值班组%d", i+1), // 延续duty的逻辑，数组排序为值班组号
			Period:    group.Time.Period,
		})
	}
	return groups
}

func getDutyApprovers(ctx context.Context, idc string, timestamp int64, dutyGroup *config_service.DutyGroup) (approvers []string, err error) {
	approvers, err = dutyv5.GetForecastApproversGuessIdc(ctx, dutyGroup.DutyName, time.Unix(timestamp, 0), idc)
	if err != nil {
		logs.V2.Warn().With(ctx).Str("get duty forecast failed").Error(err).Emit()
		return
	}
	return slicex.Distinct(approvers), err
}
