/**
 * @Date: 2022/9/15
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package customizetabhandler

import (
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
)

func VerifySaveCustomizeTabOrderRequest(req *config_service.SaveCustomizeTabOrderRequest) error {
	if req.Email == "" {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("email is empty")
	}
	if req.Category == "" {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("category is empty")
	}
	if len(req.Tags) == 0 {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("tabs is empty")
	}
	return nil
}

func VerifyQueryCustomizeTabOrderRequest(req *config_service.QueryCustomizeTabOrderRequest) error {
	if req.Email == "" {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("email is empty")
	}
	if req.Category == "" {
		return bits_err.COMMON.ErrInvalidInput.AddErrMsg("category is empty")
	}
	return nil
}
