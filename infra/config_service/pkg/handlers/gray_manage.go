package handlers

import (
	"context"
	"sync"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/configcenter"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/infra/config_service/utils"
	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

type GrayManagerSvc interface {
	IsGrayPointHitSpaces(ctx context.Context, req *config_service.IsGrayPointHitSpacesRequest) (resp *config_service.IsGrayPointHitSpacesResponse, err error)
	IsSpaceHitGrayPoints(ctx context.Context, req *config_service.IsSpaceHitGrayPointsRequest) (resp *config_service.IsSpaceHitGrayPointsResponse, err error)
	SetGrayPointSpaces(ctx context.Context, req *config_service.SetGrayPointSpacesRequest) (err error)
}

func NewGrayManagerSvc() GrayManagerSvc {
	return &GrayManager{
		grayManageDb: repository.NewDevGrayManage(),
	}
}

type GrayManager struct {
	grayManageDb repository.DevGrayManage
}

func (g GrayManager) IsGrayPointHitSpaces(ctx context.Context, req *config_service.IsGrayPointHitSpacesRequest) (resp *config_service.IsGrayPointHitSpacesResponse, err error) {
	resp = &config_service.IsGrayPointHitSpacesResponse{}
	configs := configcenter.GetGrayManagerConfigs(ctx)
	logs.CtxInfo(ctx, "[IsSpaceMatchFeatureKeyGray] configs:%s", utils.MustTransInterfaceToString(configs))
	hitConfig := configcenter.GrayManageConfig{}
	for _, config := range configs {
		if config.ProjectType == req.GetGrayPoint().GetProjectType().String() && config.ControlPlane == req.GetGrayPoint().ControlPlane.String() && config.FeatureKey == req.GetGrayPoint().FeatureKey {
			hitConfig = config
			break
		}
	}
	if hitConfig.IsFull {
		resp.SpaceResults = slicex.Map(req.GetSpaceIds(), func(spaceId int64) *config_service.SpaceResult_ {
			return &config_service.SpaceResult_{SpaceId: spaceId, IsHit: true}
		})
		return
	}
	spacesSet := set.NewInt64Set(hitConfig.Store.SpaceIds...)
	if hitConfig.Store.TccKey != "" {
		spaceInfo := configcenter.GetGraySpaces(ctx, hitConfig.Store.TccKey)
		spacesSet.AddAll(slicex.FlatMap(spaceInfo.SpaceIds, func(space configcenter.GraySpaceItem) []int64 {
			return space.List
		})...)
	}
	resp.SpaceResults = slicex.Map(req.GetSpaceIds(), func(spaceId int64) *config_service.SpaceResult_ {
		if spacesSet.Contains(spaceId) {
			return &config_service.SpaceResult_{SpaceId: spaceId, IsHit: true}
		}
		if hitConfig.Store.ByteGate != "" && configcenter.IfSpaceHitFeature(ctx, hitConfig.Store.ByteGate, spaceId) {
			return &config_service.SpaceResult_{SpaceId: spaceId, IsHit: true}
		}
		return &config_service.SpaceResult_{SpaceId: spaceId, IsHit: false}
	})
	return
}

func (g GrayManager) IsSpaceHitGrayPoints(ctx context.Context, req *config_service.IsSpaceHitGrayPointsRequest) (resp *config_service.IsSpaceHitGrayPointsResponse, err error) {

	resp = &config_service.IsSpaceHitGrayPointsResponse{}

	/**
	tcc: bytedance.bits.config_service
	key: feature_gray_key

	[
	    {
	        "project_type": "PROJECT_TYPE_WEB",
	        "control_plane": "CONTROL_PLANE_US_TTP",
	        "feature_key": "support_comp",
	        "feature_key_desc": "研发流程 usttp 控制面支持 web 项目",
	        "is_full": true,
	        "store": {
	            "byte_gate": "support_comp_web_usttp",
	            "tcc_key": "gray_release_for_web_us"
	        }
	    }
	]
	*/

	configs := configcenter.GetGrayManagerConfigs(ctx)
	logs.CtxInfo(ctx, "[IsSpaceMatchFeatureKeyGray] configs:%s", utils.MustTransInterfaceToString(configs))

	/**
	读 tcc
	key2Config 的格式 Demo

	{
		"PROJECT_TYPE_WEB.CONTROL_PLANE_US_TTP.support_comp": {
	        "project_type": "PROJECT_TYPE_WEB",
	        "control_plane": "CONTROL_PLANE_US_TTP",
	        "feature_key": "support_comp",
	        "feature_key_desc": "研发流程 usttp 控制面支持 web 项目",
	        "is_full": true,
	        "store": {
	            "byte_gate": "support_comp_web_usttp",
	            "tcc_key": "gray_release_for_web_us"
	        }
		}
	}
	*/

	key2Config := gslice.ToMap(configs, func(config configcenter.GrayManageConfig) (string, configcenter.GrayManageConfig) {
		pt, _ := dev.ProjectTypeFromString(config.ProjectType)
		cp, _ := dev.ControlPlaneFromString(config.ControlPlane)
		return GenerateGrayPoint(&config_service.GrayPoint{
			FeatureKey:   config.FeatureKey,
			ProjectType:  pt,
			ControlPlane: cp,
		}), config
	})

	/**
	读 db
	keys 的格式 Demo
		["PROJECT_TYPE_WEB.CONTROL_PLANE_US_TTP.support_comp"]
	根据 keys 搜索 bits 数据库的 dev_config_gray_manage 表
		SELECT * from dev_config_gray_manage where `gray_point` in ("PROJECT_TYPE_WEB.CONTROL_PLANE_US_TTP.support_comp")

	key2SpaceIds 的格式 Demo
	{
		"PROJECT_TYPE_WEB.CONTROL_PLANE_US_TTP.support_comp": [657608704514, 655882547714, 663076557058, 663065497602, 663076557314, 663074713858, 663080243970, 663065497858, 663466393858, 663466394114, 664705024514, 669852160002, 663062733570, 663074714114, 669786726402, 664705024770, 668563763458, 670013440002, 664905011202, 663106048514, 671137792002, 671142400002, 663058125058, 663074714370, 663062733826, 663079322114, 668772045058, 663106048770, 663060889858, 671028121602, 662908826626, 668351795714, 671028121858, 663065498626, 668351795970, 663067340802, 663079322626, 663066419458, 670061363202, 666590617858, 671276032002, 668351796226, 670061363458, 669786726658, 663062734082, 664661709058, 665932595202, 672573645058, 674832486402, 674865664002, 674588262402, 674842624002, 674857369602, 674795622402, 674859212802, 674831564802, 674859213058, 674875802626, 674960588802, 675617689602, 674855526402, 674795622658, 674859213570, 674842624258, 677957632002, 674830643458, 674866585602, 674866585858, 677957632258, 693971353602, 692670054914, 693752934402, 693722521602, 703191040002, 693741875202, 693720678402, 693734502402, 692670055170, 693822976002, 693734502658, 693792563202, 702226124802, 693741875458, 693796249858, 693771366402, 702226125058, 745236275202, 745216000002, 692448870658, 693734503170, 693846016258, 702226125314, 745236275458, 723433984002, 693771366658, 745216000258, 748008448002, 693734503426, 693777817602, 693825741570, 748397363202, 693752934658, 693741875714]
	}
	*/
	keys := slicex.Map(req.GrayPoint, func(from *config_service.GrayPoint) string {
		return GenerateGrayPoint(from)
	})
	dbConfigs, err := g.grayManageDb.GetSpaceIdsByGrayPoints(ctx, keys)
	if err != nil {
		return
	}

	key2SpaceIds := gslice.ToMap(dbConfigs, func(from entity.DevGrayManage) (string, []int64) {
		return from.GrayPoint, from.GetSpaceIds()
	})

	//
	eg, _ := errgroup.WithContext(ctx)
	mutex := sync.Mutex{}
	for _, grayPoint := range req.GrayPoint {
		tmp := grayPoint
		key := GenerateGrayPoint(grayPoint)

		// DB 匹配
		spaceIds, ok := key2SpaceIds[key]
		if ok && set.NewInt64Set(spaceIds...).Contains(req.GetSpaceId()) {
			resp.GrayPointResults = append(resp.GrayPointResults, &config_service.GrayPointResult_{
				GrayPoint: grayPoint,
				IsHit:     true,
			})
			logs.CtxInfo(ctx, "[IsSpaceMatchFeatureKeyGray] hit gray point:%s,space:%d,from db", utils.MustTransInterfaceToString(grayPoint), req.GetSpaceId())
			continue
		}

		// tcc 匹配
		config, ok := key2Config[key]
		if ok && (set.NewInt64Set(config.Store.SpaceIds...).Contains(req.GetSpaceId()) || config.IsFull) {
			resp.GrayPointResults = append(resp.GrayPointResults, &config_service.GrayPointResult_{
				GrayPoint: grayPoint,
				IsHit:     true,
			})
			logs.CtxInfo(ctx, "[IsSpaceMatchFeatureKeyGray] hit gray point:%s,space:%d,from main tcc", utils.MustTransInterfaceToString(grayPoint), req.GetSpaceId())
			continue
		}

		eg.Go(func() error {
			result := &config_service.GrayPointResult_{
				GrayPoint: tmp,
				IsHit:     false,
			}

			// tcc 匹配
			config, ok = key2Config[key]
			if ok && config.Store.TccKey != "" {
				/**
				tcc: bytedance.bits.config_service
				key: gray_release_for_web_us
				{
					"space_ids": [{
						"name": "Test",
						"list": [1, 473402982914, 386578125570, 35997699842, 473427866882, 93992140802, 473391923458, 473476710402, 504197325826, 39145911042, 4831039490, 189481064706, 427930317570, 351228314114, 3939262978, 330664652802, 616708096002, 604730060802, 581830144002, 607553843202, 588088729602, 586909081602, 576275661058, 330547609602, 421847756802, 35997706242, 350959210498, 97865638146, 4085174274, 470900839426, 427940455170, 163874410498, 4084796418, 427940455426, 4084934914, 635411968258, 4078082562, 3939152386, 4085444354, 473388236802, 201134696450, 4078080002, 161130809887, 164769281794, 112718137858, 284275001602, 146268983554, 3939372802, 163538022402, 163902987522, 112682194178, 4368384258, 130727118338, 161923458304, 161473461504, 93338727426, 163842161154, 189443277314]
					}]
				}
				*/

				tccKeyConfig := configcenter.GetGraySpaces(ctx, config.Store.TccKey)
				if set.NewInt64Set(slicex.FlatMap(tccKeyConfig.SpaceIds, func(from configcenter.GraySpaceItem) []int64 {
					return from.List
				})...).Contains(req.GetSpaceId()) {
					result.IsHit = true
					logs.CtxInfo(ctx, "[IsSpaceMatchFeatureKeyGray] hit gray point:%s,space:%d,from tcc:%s", utils.MustTransInterfaceToString(grayPoint), req.GetSpaceId(), config.Store.TccKey)
				}
			}
			if ok && config.Store.ByteGate != "" {
				/**
				ByteGate: BitsDevOps
				Key: support_comp_web_usttp
				Url: https://cloud.bytedance.net/bytegate/workspace/24/gate/detail/1669?x-resource-account=public&x-bc-vregion=China-North
				*/
				isHit := configcenter.IfSpaceHitFeature(ctx, config.Store.ByteGate, req.GetSpaceId())
				if isHit {
					result.IsHit = true
					logs.CtxInfo(ctx, "[IsSpaceMatchFeatureKeyGray] hit gray point:%s,space:%d,from byte gate:%s", utils.MustTransInterfaceToString(grayPoint), req.GetSpaceId(), config.Store.ByteGate)
				}
			}

			mutex.Lock()
			defer mutex.Unlock()
			resp.GrayPointResults = append(resp.GrayPointResults, result)
			return nil
		})
	}
	_ = eg.Wait()
	return
}

func (g GrayManager) SetGrayPointSpaces(ctx context.Context, req *config_service.SetGrayPointSpacesRequest) (err error) {
	for _, grayPoint := range req.GetGrayPoints() {
		err = g.grayManageDb.AddSpaceIdToGrayPoint(ctx, GenerateGrayPoint(grayPoint), req.GetSpaceId())
		if err != nil {
			logs.CtxWarn(ctx, "[SetGrayPointSpaces] set gray point:%s error:%s", utils.MustTransInterfaceToString(grayPoint), err.Error())
		}
	}
	return
}

func GenerateGrayPoint(grayPoint *config_service.GrayPoint) (str string) {
	return grayPoint.GetProjectType().String() + "." + grayPoint.GetControlPlane().String() + "." + grayPoint.GetFeatureKey()
}
