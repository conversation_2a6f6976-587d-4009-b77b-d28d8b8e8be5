/**
 * @Date: 2022/7/26
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repository

import (
	"context"
	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/dal/db"

	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type ProjectRelationApprovalRepository struct {
	gdb *gorm.DB
}

func NewProjectRelationApprovalRepository(gdb *gorm.DB) *ProjectRelationApprovalRepository {
	return &ProjectRelationApprovalRepository{
		gdb: gdb.Table(schema.Tabler(new(db.ProjectRelationApproval)).TableName()),
	}
}

func (repository *ProjectRelationApprovalRepository) Create(ctx context.Context, value *db.ProjectRelationApproval) gresult.R[int64] {
	err := repository.gdb.WithContext(ctx).Create(value).Error

	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(value.Id)
}

func (repository *ProjectRelationApprovalRepository) FindLastById(ctx context.Context, id int64) gresult.R[*db.ProjectRelationApproval] {
	value := new(db.ProjectRelationApproval)
	err := repository.gdb.
		WithContext(ctx).
		Where("`id` = ?", id).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*db.ProjectRelationApproval](err)
	}
	return gresult.OK(value)
}

func (repository *ProjectRelationApprovalRepository) FindLastByRepoNameAndStatus(ctx context.Context, repoName string, status db.ProjectRelationApprovalStatus) gresult.R[*db.ProjectRelationApproval] {
	value := new(db.ProjectRelationApproval)
	err := repository.gdb.
		WithContext(ctx).
		Where("`repo_name` = ? AND `status` = ?", repoName, status).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*db.ProjectRelationApproval](err)
	}
	return gresult.OK(value)
}

func (repository *ProjectRelationApprovalRepository) FindByAppIdAndStatusOrderByIdDesc(ctx context.Context, appId int64, status db.ProjectRelationApprovalStatus) gresult.R[[]*db.ProjectRelationApproval] {
	values := make([]*db.ProjectRelationApproval, 0, 8)
	err := repository.gdb.
		WithContext(ctx).
		Where("`app_id` = ? AND `status` = ?", appId, status).
		Find(&values).
		Order("`id` DESC").
		Error

	if err != nil {
		return gresult.Err[[]*db.ProjectRelationApproval](err)
	}
	return gresult.OK(values)
}

func (repository *ProjectRelationApprovalRepository) UpdateById(ctx context.Context, id int64, updated *db.ProjectRelationApproval) error {
	err := repository.gdb.
		WithContext(ctx).
		Where("`id` = ?", id).
		Updates(updated).
		Error

	if err != nil {
		return err
	}
	return nil
}

func (repository *ProjectRelationApprovalRepository) DeleteById(ctx context.Context, id int64) error {
	err := repository.gdb.
		WithContext(ctx).
		Delete(new(db.ProjectRelationApproval), id).
		Error

	if err != nil {
		return err
	}
	return nil
}
