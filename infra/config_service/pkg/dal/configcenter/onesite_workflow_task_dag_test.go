package configcenter

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/gopkg/tccclient"
)

func TestConfigCenter_GetOneSiteWorkflowTaskDAGAutoGen(t *testing.T) {
	// receiver.inner != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			getterMock := func(context.Context)(interface{}, error){
				var getterMockRet1 interface{}
				var getterMockRet2 error
				return getterMockRet1, getterMockRet2
			}
			mockey.Mock((*tccclient.ClientV2).NewGetter, mockey.OptUnsafe).Return(getterMock).Build()

			// prepare parameters
			ctx := context.Background()
			var receiverPtrValue ConfigCenter
			receiver := &receiverPtrValue
			var receiverInnerPtrValue tccclient.ClientV2
			receiver.inner = &receiverInnerPtrValue

			// run target function and assert
			got1 := receiver.GetOneSiteWorkflowTaskDAG(ctx)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).FixedNameDag), convey.ShouldEqual, 0)
			convey.So(len((*got1).Stages), convey.ShouldEqual, 0)
		})
	})

	// receiver.inner != nil
	// code.byted.org/gopkg/tccclient.ClientV2:NewGetter()_ret-1()_ret-2 != nil
	// code.byted.org/gopkg/logs/v2.Log:KV()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:Error()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:Str()_ret-1 != nil
	// code.byted.org/gopkg/logs/v2.Log:With()_ret-1 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			getterMock := func(context.Context)(interface{}, error){
				var getterMockRet1 interface{}
				getterMockRet2 := fmt.Errorf("error")
				return getterMockRet1, getterMockRet2
			}
			mockey.Mock((*tccclient.ClientV2).NewGetter, mockey.OptUnsafe).Return(getterMock).Build()

			// prepare parameters
			var receiverPtrValue ConfigCenter
			receiver := &receiverPtrValue
			var receiverInnerPtrValue tccclient.ClientV2
			receiver.inner = &receiverInnerPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.GetOneSiteWorkflowTaskDAG(ctx)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).FixedNameDag), convey.ShouldEqual, 0)
			convey.So(len((*got1).Stages), convey.ShouldEqual, 0)
		})
	})

}

