package db

import (
	"database/sql/driver"
	"errors"
	"time"

	"code.byted.org/devinfra/hagrid/infra/config_service/kitex_gen/bytedance/bits/config_service"
	json "github.com/bytedance/sonic"
)

func (DevConfigGatekeeper) TableName() string {
	return "dev_config_gatekeeper"
}

func NewDevConfigGatekeeper() *DevConfigGatekeeper {
	return &DevConfigGatekeeper{}
}

type DevConfigGatekeeper struct {
	Enable        bool             `gorm:"column:enable" json:"enable"`                 // 是否开启
	Id            int64            `gorm:"column:id" json:"id"`                         // id
	SpaceId       int64            `gorm:"column:space_id" json:"space_id"`             // 空间id
	WorkflowId    int64            `gorm:"column:workflow_id" json:"workflow_id"`       // workflow_id
	CreatedAt     time.Time        `gorm:"column:created_at" json:"created_at"`         // 创建时间
	UpdatedAt     time.Time        `gorm:"column:updated_at" json:"updated_at"`         // 更新时间
	WorkflowFrom  string           `gorm:"column:workflow_from" json:"workflow_from"`   // 模板类型
	WorkflowStage string           `gorm:"column:workflow_stage" json:"workflow_stage"` // 阶段名称
	EntryConfig   GateKeeperConfig `gorm:"column:entry_config" json:"entry_config"`     // 准入配置
	ExitConfig    GateKeeperConfig `gorm:"column:exit_config" json:"exit_config"`       // 准出配置
	ProcessConfig GateKeeperConfig `gorm:"column:process_config" json:"process_config"` // 过程中检查项配置
	Creator       string           `gorm:"column:creator" json:"creator"`               // 创建人
	Updater       string           `gorm:"column:updater" json:"updater"`               // 更新人
}

type GateKeeperConfig struct {
	ThirdPartyConfig  ThirdParty   `json:"third_party_config"`
	ThirdPartyConfigs []ThirdParty `json:"third_party_configs"`
}

type SkipApproverInfo struct {
	ApproverType string `json:"approver_type"`
	Username     string `json:"username"`
	RoleType     int64  `json:"role_type"`
}

type ThirdParty struct {
	Enable     bool                                   `json:"enable"`
	CheckType  string                                 `json:"check_type"`
	ConfigInfo *config_service.ThirdPartyConfigDetail `json:"config_info"`
	CheckLevel string                                 `json:"check_level"`
	Approvers  []*SkipApproverInfo                    `json:"approvers"`
	Skippable  bool                                   `json:"skippable"`
	Detail     *config_service.CheckConfigDetail      `json:"detail"`
}

func (s GateKeeperConfig) Value() (driver.Value, error) {
	return json.Marshal(s)
}

func (s *GateKeeperConfig) Scan(value interface{}) error {
	str, ok := value.([]byte)
	if !ok {
		return errors.New("failed to scan StringArray")
	}
	_ = json.Unmarshal(str, s)
	return nil
}
