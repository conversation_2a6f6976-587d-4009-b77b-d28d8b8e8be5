package db

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/infra/config_service/pkg/config"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	mysql2 "code.byted.org/devinfra/hagrid/libs/connections/mysql"

	"code.byted.org/gopkg/logs"
	"code.byted.org/gorm/bytedgorm"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/plugin/dbresolver"
)

var (
	Bits       BitsMysqlInterface
	Mpaas      MpaasMysqlInterface
	Optimus    OptimusMysqlInterface
	CommonData CommonDataMysqlInterface

	OptimusDB    *gorm.DB
	BitsDB       *gorm.DB
	CommonDataDB *gorm.DB
	MpaasDB      *gorm.DB
)

type BitsMysqlInterface struct {
	Master BitsRepoInterface
	Slave  BitsRepoInterface
}
type MpaasMysqlInterface struct {
	Master MpaasRepoInterface
	Slave  MpaasRepoInterface
}
type OptimusMysqlInterface struct {
	Master OptimusInterface
	Slave  OptimusInterface
}
type CommonDataMysqlInterface struct {
	Master CommonDataInterface
	Slave  CommonDataInterface
}

/*
MysqlConnInitByUserName() for host&port connection,
MysqlConnInitByPSM() for PSM&dbName connection
*/
func Init() {
	mysqlConfig := config.ServerConfig.Mysql

	OptimusDB = MysqlConnInit(mysqlConfig.Optimus.Master)
	Optimus.Master = NewOptimusInterface(OptimusDB.Clauses(dbresolver.Write))
	Optimus.Slave = NewOptimusInterface(OptimusDB.Clauses(dbresolver.Read))

	BitsDB = MysqlConnInitByBits(mysqlConfig.Bits.Master)
	Bits.Master = NewBitsRepoInterface(BitsDB.Clauses(dbresolver.Write))
	Bits.Slave = NewBitsRepoInterface(BitsDB.Clauses(dbresolver.Read))

	CommonDataDB = MysqlConnInit(mysqlConfig.CommonData.Master)
	CommonData.Master = NewCommonDataInterface(CommonDataDB.Clauses(dbresolver.Write))
	CommonData.Slave = NewCommonDataInterface(CommonDataDB.Clauses(dbresolver.Read))

	MpaasDB = MysqlConnInit(mysqlConfig.Mpaas.Master)
	Mpaas.Master = NewMpaasRepoInterface(MpaasDB.Clauses(dbresolver.Write))
	Mpaas.Slave = NewMpaasRepoInterface(MpaasDB.Clauses(dbresolver.Read))

}

func MysqlConnInit(opts config.CommonDbClient) *gorm.DB {
	if len(opts.PSM) > 0 && len(opts.DB) > 0 {
		return MysqlConnInitByPSM(opts)
	} else {
		return MysqlConnInitByUserName(opts)
	}
}

func MysqlConnInitByUserName(opts config.CommonDbClient) *gorm.DB {
	dsn := fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8&parseTime=True&loc=Local", opts.Username, opts.Password, fmt.Sprintf("%s:%d", opts.Host, opts.Port), opts.DB)
	r, err := gorm.Open(mysql.Open(dsn), bytedgorm.WithDefaults(),
		bytedgorm.WithSecurityScanSupport(),
		bytedgorm.Logger{LogLevel: logger.Info, IgnoreRecordNotFoundError: true}, bytedgorm.WithSingularTable(),
		bytedgorm.ConnPool{
			ConnMaxIdleTime: 3000 * time.Second,
			MaxIdleConns:    100,
			MaxOpenConns:    200,
		})
	if err != nil {
		logs.Info("mysql init error")
		panic(err)
	}
	return r
}

func MysqlConnInitByBits(conf config.CommonDbClient) *gorm.DB {
	logs.Infof("db init psm:%v,dbName:%v ", conf.PSM, conf.DB)
	return mysql2.MustConnect(
		mysql2.NewTCEEndpoint(strings.TrimRight(conf.PSM, "_write"), conf.DB),
		bytedgorm.WithSecurityScanSupport(),
		bytedgorm.Logger{LogLevel: logger.Info, IgnoreRecordNotFoundError: true}, bytedgorm.WithSingularTable(),
		bytedgorm.ConnPool{
			ConnMaxLifetime: 3000 * time.Second,
			ConnMaxIdleTime: 3000 * time.Second,
			MaxIdleConns:    1000,
			MaxOpenConns:    2000,
		},
	)
}

func MysqlConnInitByPSM(conf config.CommonDbClient) *gorm.DB {
	logs.Infof("db init psm:%v,dbName:%v ", conf.PSM, conf.DB)

	return mysql2.MustConnect(
		mysql2.NewTCEEndpoint(strings.TrimRight(conf.PSM, "_write"), conf.DB),
		bytedgorm.WithSecurityScanSupport(),
		bytedgorm.Logger{LogLevel: logger.Info, IgnoreRecordNotFoundError: true}, bytedgorm.WithSingularTable(),
		bytedgorm.ConnPool{
			ConnMaxLifetime: 3000 * time.Second,
			ConnMaxIdleTime: 3000 * time.Second,
			MaxIdleConns:    300,
			MaxOpenConns:    600,
		},
	)
}

func MysqlNoRows(err error) bool {
	return errors.Is(err, gorm.ErrRecordNotFound) || utils.MysqlNoRows(err)
}
