{"config": {"wide_screen_mode": true}, "header": {"title": {"tag": "plain_text", "i18n": {"en_us": "__releaseTicketNodeEn__ Pipeline Succeed", "ja_jp": "", "zh_cn": "__releaseTicketNode__流水线运行成功"}}, "template": "green"}, "i18n_elements": {"en_us": [{"tag": "div", "text": {"tag": "lark_md", "content": "**👉 Trigger Mode:** __pipelineTriggerMethodEn__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**🚦 Trigger By:** __pipelineTriggerUserEn__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**🚩‍ Release Ticket Name:** __releaseTicketName__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**🌏 Control Plane:** __controlPlanes__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**📃 Project Info:** __projectInfos__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**📝 Environment:** __envs__"}}, {"tag": "action", "actions": [{"tag": "button", "url": "__url__", "text": {"tag": "plain_text", "content": "Detail"}, "type": "primary"}]}], "zh_cn": [{"tag": "div", "text": {"tag": "lark_md", "content": "**👉 触发方式：** __pipelineTriggerMethod__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**🚦 触发人：** __pipelineTriggerUser__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**🚩‍ 发布单：** __releaseTicketName__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**🌏 控制面：** __controlPlanes__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**📃 项目信息：** __projectInfos__"}}, {"tag": "div", "text": {"tag": "lark_md", "content": "**📝 泳道环境：** __envs__"}}, {"tag": "action", "actions": [{"tag": "button", "url": "__url__", "text": {"tag": "plain_text", "content": "查看详情"}, "type": "primary"}]}]}}