load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = [
        "http.go",
        "logs.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/config_service/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/middleware/restymw",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_go_resty_resty_v2//:resty",
    ],
)

go_test(
    name = "utils_test",
    srcs = [
        "http_test.go",
        "logs_test.go",
    ],
    embed = [":utils"],
    deps = [
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_go_resty_resty_v2//:resty",
        "@com_github_smartystreets_goconvey//convey",
    ],
)
