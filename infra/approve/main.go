package main

import (
	"time"

	"code.byted.org/devinfra/hagrid/infra/approve/biz/dal"
	"code.byted.org/devinfra/hagrid/infra/approve/biz/service"
	"code.byted.org/devinfra/hagrid/infra/approve/kitex_gen/bytedance/bits/approve/approveservice"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	_ "code.byted.org/devinfra/hagrid/libs/prelude"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/cloudwego/kitex/server"
)

func main() {
	options := []server.Option{
		server.WithMiddleware(kitexmw.LogRequestResponse),
		server.WithMiddleware(kitexmw.BitsMetrics),
		server.WithReadWriteTimeout(5 * time.Minute),
	}
	svr := approveservice.NewServer(new(ApproveServiceImpl), options...)
	dal.Init()
	service.Init()
	err := svr.Run()
	if err != nil {
		log.V2.Fatal().Str("failed to run server").Error(err).Emit()
	}
}
