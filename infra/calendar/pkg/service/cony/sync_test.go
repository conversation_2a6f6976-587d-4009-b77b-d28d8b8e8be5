package cony

import (
	"context"
	"testing"
)

func TestSyncConyEvent(t *testing.T) {
	type args struct {
		ctx            context.Context
		syn            SyncData
		workspaceAlias string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				ctx: context.Background(),
				syn: SyncData{
					Type: "event",
					EventData: EventData{
						Action:  "create",
						Alias:   "cam",
						EventID: 9989,
					},
				},
				workspaceAlias: "cam",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncConyEvent(tt.args.ctx, tt.args.syn, tt.args.workspaceAlias)
		})
	}
}

func TestSyncUser(t *testing.T) {
	type args struct {
		ctx   context.Context
		email string
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "",
			args: args{
				ctx:   context.Background(),
				email: "testAAAAAAAA",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			SyncUser(tt.args.ctx, tt.args.email)
		})
	}
}
