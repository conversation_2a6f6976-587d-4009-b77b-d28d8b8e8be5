CREATE TABLE `optimus_user_gitlabuser`
(
    `create_time`         datetime(6)                                      DEFAULT NULL,
    `update_time`         datetime(6)                                      DEFAULT NULL,
    `userid`              bigint(20)                              NOT NULL,
    `username`            varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
    `nickname`            varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
    `email`               varchar(100) COLLATE utf8mb4_unicode_ci          DEFAULT NULL,
    `access_token`        varchar(500) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT 'gitlab access token',
    `is_refreshing_token` tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '是否正在刷新gitlab access token',
    `refresh_token`       varchar(500) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT 'gitlab refresh token',
    `token_expire_time`   bigint(20) unsigned                     NOT NULL DEFAULT '0' COMMENT 'gitlab access token 过期时间',
    PRIMARY KEY (`userid`),
    KEY `idx_optimus_user_gitlabuser_email` (`email`),
    KEY `idx_optimus_user_gitlabuser_username` (`username`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
