CREATE TABLE `optimus_user_optimususer`
(
    `create_time`    datetime(6)                             DEFAULT NULL,
    `update_time`    datetime(6)                             DEFAULT NULL,
    `id`             bigint(20) unsigned                     NOT NULL AUTO_INCREMENT,
    `username`       varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
    `gitlab_user_id` bigint(20)                              DEFAULT NULL,
    `lark_user_id`   varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `feishu_user_id` bigint(20) unsigned                     DEFAULT NULL COMMENT 'feishu user关联',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_gitlab_user_id` (`gitlab_user_id`),
    UNIQUE KEY `uniq_lark_user_id` (`lark_user_id`),
    UNIQUE KEY `feishu_user_id` (`feishu_user_id`),
    KEY `idx_username` (`username`(150))
) ENGINE = InnoDB
  AUTO_INCREMENT = 39304
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;
