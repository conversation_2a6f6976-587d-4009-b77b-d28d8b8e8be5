/**
 * @Date: 2022/9/3
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package branch

import (
	"context"
	"regexp"
	"strings"

	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/bytedancegitlab"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/baselib/gitexecute"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/service/project"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gresult"
)

// BranchesService
// 整个流程
// 用户push时,接受对应webhook,刷新缓存.
// 然后使用.
type BranchesService struct {
	gitlabApi      bytedancegitlab.Api
	projectService *project.ProjectService
}

func NewBranchesService(rdb *goredis.Client, gitlabApi bytedancegitlab.Api) *BranchesService {
	return &BranchesService{
		gitlabApi:      gitlabApi,
		projectService: project.NewProjectService(rdb, gitlabApi),
	}
}

func (service *BranchesService) FindAllBranchesByGitUrl(ctx context.Context, gitUrl string) gresult.R[[]string] {
	branches, err := service.GetAllBranches(ctx, gitUrl).Get()
	if err != nil {
		return gresult.Err[[]string](err)
	}
	return gresult.OK(branches)
}

func (service *BranchesService) FindAllBranchesByProjectId(ctx context.Context, projectId int64) gresult.R[[]string] {
	project, err := service.projectService.FindByProjectId(ctx, true, projectId)
	if err != nil {
		return gresult.Err[[]string](err)
	}
	return service.FindAllBranchesByGitUrl(ctx, project.SSHURLToRepo)
}

var parseBranchesRegex = regexp.MustCompile(`heads/(.*)$`)

func (service *BranchesService) GetAllBranches(ctx context.Context, gitUrl string) gresult.R[[]string] {
	stdout, stderr, code := gitexecute.RunLsRemote(ctx, gitUrl, service.gitlabApi.GetPrivateToken())
	if code > 0 && strings.Contains(stderr, "not found") {
		return gresult.Err[[]string](errors.New("not found"))
	}

	if stderr != "" {
		return gresult.Err[[]string](errors.New(stderr))
	}

	lines := strings.Split(stdout, "\n")
	branches := make([]string, 0, len(lines))
	for _, line := range lines {
		matches := parseBranchesRegex.FindStringSubmatch(line)
		if len(matches) > 1 {
			branches = append(branches, matches[1])
		}
	}
	return gresult.OK(branches)
}
