load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "project",
    srcs = ["service.go"],
    importpath = "code.byted.org/devinfra/hagrid/infra/git_server/pkg/service/project",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/git_server/pkg/backends/bytedancegitlab",
        "//infra/git_server/pkg/baselib/gitparse",
        "//infra/git_server/pkg/dal/redis/cache",
        "//libs/bits_err",
        "@com_github_xanzy_go_gitlab//:go-gitlab",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
    ],
)

go_test(
    name = "project_test",
    srcs = ["service_test.go"],
    embed = [":project"],
    deps = [
        "//infra/git_server/pkg/backends/bytedancegitlab",
        "//libs/connections/redis",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
    ],
)
