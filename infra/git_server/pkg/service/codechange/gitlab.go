package codechange

import (
	"context"

	"code.byted.org/devinfra/hagrid/infra/git_server/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/bytedancegitlab"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/codebaseapi"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/meta"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/dal/redis/cache"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/domain/codebase"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/internal/errors"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/service/authenticate"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"github.com/xanzy/go-gitlab"
	"gorm.io/gorm"
)

type GitlabMrService struct {
	gitlabClient                 bytedancegitlab.Api
	gitlabMergeRequestRepository *repository.GitlabMergeRequestRepository
	codeChangeRepository         *repository.CodeChangeRepository
	changeExplorer               *cache.ChangeExplorer
	DB                           *gorm.DB
	authenticateService          *authenticate.Service
	codebaseApiClient            codebaseapi.Api
	metaApi                      meta.Api
}

func NewGitlabMrService(rdb *goredis.Client, gitlabClient bytedancegitlab.Api, codebaseApiClient codebaseapi.Api, opdb *gorm.DB, metaApi meta.Api) *GitlabMrService {
	return &GitlabMrService{
		gitlabClient:                 gitlabClient,
		gitlabMergeRequestRepository: repository.NewGitlabMergeRequestRepository(opdb),
		codeChangeRepository:         repository.NewCodeChangeRepository(opdb),
		changeExplorer:               cache.NewChangeExplorer(rdb),
		DB:                           opdb,
		authenticateService:          authenticate.NewService(opdb, rdb, codebaseApiClient),
		codebaseApiClient:            codebaseApiClient,
		metaApi:                      metaApi,
	}
}

func (service *GitlabMrService) Create(ctx context.Context, codeChangeConfig *git_server.CodeChangeCreateConfig, extraConfig *git_server.ExtraCreateConfig, creationMode git_server.CodeChangeCreationMode, apiType *git_server.CreateCodeChangeApiType) gresult.R[*ChangeCreationDetail] {
	if apiType != nil && *apiType == git_server.CreateCodeChangeApiType_codebase {
		return service.CodebaseCreate(ctx, codeChangeConfig, extraConfig, creationMode, apiType)
	}

	if err := service.VerifyExtraCreateConfig(extraConfig, creationMode); err != nil {
		log.V2.Error().With(ctx).Str("verify create gitlab mr extra config failed").Error(err).KVs("config", extraConfig)
		return gresult.Err[*ChangeCreationDetail](err)
	}
	codeChangeEntity, err := TransCodeChangeToEntity(codeChangeConfig)
	if err != nil {
		log.V2.Error().With(ctx).Str("transform code change config to entity failed").Error(err).
			KVs("config", codeChangeConfig).Emit()
		return gresult.Err[*ChangeCreationDetail](err)
	}

	gitlabMrConfig := extraConfig.GitlabMrExtraCreateConfig
	mergeRequest := &gitlab.MergeRequest{}
	// track mode
	if creationMode == git_server.CodeChangeCreationMode_trackExisted {
		mergeRequest, err = service.gitlabClient.GetMergeRequest(ctx, gitlabMrConfig.GetProjectId(), gitlabMrConfig.GetIid()).Get()
		if err != nil {
			log.V2.Error().With(ctx).Str("failed to get merge request").Error(err).KV("mr config", gitlabMrConfig).Emit()
			return gresult.Err[*ChangeCreationDetail](errors.FromGitlabApiError(err))
		}
	} else {
		options := &gitlab.CreateMergeRequestOptions{
			Title:              &codeChangeConfig.Title,
			Description:        codeChangeConfig.Description,
			SourceBranch:       &codeChangeConfig.SourceBranch,
			TargetBranch:       &codeChangeConfig.TargetBranch,
			Labels:             gptr.Of[gitlab.LabelOptions](gitlabMrConfig.Labels),
			AssigneeIDs:        gptr.Of(gslice.Cast[int, int64](gitlabMrConfig.AssigneeIds)),
			RemoveSourceBranch: gitlabMrConfig.RemoveSourceBranch,
			Squash:             gitlabMrConfig.Squash,
			AllowCollaboration: gitlabMrConfig.AllowCollaboration,
		}
		// get user token
		token := service.authenticateService.GetGitlabAccessToken(ctx, codeChangeConfig.GetUsername()).ValueOrZero()
		// create mr
		mergeRequest, err = service.gitlabClient.CreateMergeRequest(ctx, token, gitlabMrConfig.GetProjectId(), options).Get()
		if err != nil {
			log.V2.Error().With(ctx).Str("failed to create merge request").Error(err).KV("options", options).Emit()
			return gresult.Err[*ChangeCreationDetail](errors.FromGitlabApiError(err))
		}
	}

	// fill platform config
	platFormConfig := &entity.PlatformConfig{
		GitlabProjectID: int64(mergeRequest.ProjectID),
		GitlabIid:       int64(mergeRequest.IID),
	}
	codeChangeEntity.PlatformChangeConfig = platFormConfig.ToString()
	// create code change and mr change
	mrEntity := &entity.CodeChangeGitlabMergeRequest{
		ProjectId:    int64(mergeRequest.ProjectID),
		Iid:          int64(mergeRequest.IID),
		Title:        mergeRequest.Title,
		Description:  mergeRequest.Description,
		State:        "opened",
		SourceBranch: codeChangeConfig.SourceBranch,
		TargetBranch: codeChangeConfig.TargetBranch,
		LastCommitId: mergeRequest.SHA,
		Author:       codeChangeConfig.Username,
	}
	// fill codebase repo id and change id
	codebaseChange, _ := service.codebaseApiClient.GetChangesByProjectID(ctx, int64(mergeRequest.ProjectID), int64(mergeRequest.IID)).Get()
	if codebaseChange != nil {
		mrEntity.CodebaseChangeId = codebaseChange.Id
		mrEntity.CodebaseRepoId = codebaseChange.Source.Repo.ID
		service.changeExplorer.Create(ctx, mrEntity.CodebaseRepoId, mrEntity.CodebaseChangeId) // 记录
		// 因为 gitlab mr 创建接口没有 draft 字段，所以这里主动更新 codebase change draft
		if codeChangeConfig.GetDraft() {
			token := service.metaApi.QueryCodebaseToken(ctx, mrEntity.Author).ValueOrZero()
			updateRes := service.codebaseApiClient.UpdateChange(ctx, mrEntity.CodebaseRepoId, mrEntity.CodebaseChangeId, &codebaseapi.UpdateChangeRequest{
				Draft: gptr.Of(true),
			}, token)
			if updateRes.IsOK() {
				mrEntity.Draft = 1
			}
		}
	}

	if err := service.DB.Transaction(func(tx *gorm.DB) error {
		_, err := repository.NewCodeChangeRepository(tx).Create(ctx, codeChangeEntity).Get()
		if err != nil {
			return err
		}
		mrEntity.CodeChangeId = codeChangeEntity.Id
		_, err = repository.NewGitlabMergeRequestRepository(tx).Create(ctx, mrEntity).Get()
		if err != nil {
			return err
		}
		return nil
	}); err != nil {
		log.V2.Error().With(ctx).Str("failed to create code change gitlab mr").Error(err).KVs("code change entity", codeChangeEntity, "mr entity", mrEntity).Emit()
		// close gitlab mr if save error
		_ = service.CloseGitlabMr(ctx, int64(mergeRequest.ProjectID), int64(mergeRequest.IID))
		return gresult.Err[*ChangeCreationDetail](err)
	}
	res := &ChangeCreationDetail{
		ChangeDetail: &ChangeDetail{
			GitlabMr: mrEntity,
		},
		CodeChange: codeChangeEntity,
	}
	return gresult.OK(res)
}

func (service *GitlabMrService) CodebaseCreate(ctx context.Context, codeChangeConfig *git_server.CodeChangeCreateConfig, extraConfig *git_server.ExtraCreateConfig, creationMode git_server.CodeChangeCreationMode, apiType *git_server.CreateCodeChangeApiType) gresult.R[*ChangeCreationDetail] {
	if err := service.VerifyExtraCreateConfig(extraConfig, creationMode); err != nil {
		log.V2.Error().With(ctx).Str("verify create gitlab mr extra config failed").Error(err).KVs("config", extraConfig)
		return gresult.Err[*ChangeCreationDetail](err)
	}
	codeChangeEntity, err := TransCodeChangeToEntity(codeChangeConfig)
	if err != nil {
		log.V2.Error().With(ctx).Str("transform code change config to entity failed").Error(err).
			KVs("config", codeChangeConfig).Emit()
		return gresult.Err[*ChangeCreationDetail](err)
	}

	gitlabMrConfig := extraConfig.GitlabMrExtraCreateConfig

	// track mode
	if creationMode == git_server.CodeChangeCreationMode_trackExisted {
		mergeRequest, err := service.gitlabClient.GetMergeRequest(ctx, gitlabMrConfig.GetProjectId(), gitlabMrConfig.GetIid()).Get()
		if err != nil {
			log.V2.Error().With(ctx).Str("failed to get merge request").Error(err).KV("mr config", gitlabMrConfig).Emit()
			return gresult.Err[*ChangeCreationDetail](errors.FromGitlabApiError(err))
		}
		// fill platform config
		platFormConfig := &entity.PlatformConfig{
			GitlabProjectID: int64(mergeRequest.ProjectID),
			GitlabIid:       int64(mergeRequest.IID),
		}
		codeChangeEntity.PlatformChangeConfig = platFormConfig.ToString()
		// create code change and mr change
		mrEntity := &entity.CodeChangeGitlabMergeRequest{
			ProjectId:    int64(mergeRequest.ProjectID),
			Iid:          int64(mergeRequest.IID),
			Title:        codeChangeConfig.Title,
			Description:  codeChangeConfig.GetDescription(),
			State:        "opened",
			SourceBranch: codeChangeConfig.SourceBranch,
			TargetBranch: codeChangeConfig.TargetBranch,
			LastCommitId: mergeRequest.SHA,
			Author:       codeChangeConfig.Username,
		}
		// fill codebase repo id and change id
		codebaseChange, _ := service.codebaseApiClient.GetChangesByProjectID(ctx, int64(mergeRequest.ProjectID), int64(mergeRequest.IID)).Get()
		if codebaseChange != nil {
			mrEntity.CodebaseChangeId = codebaseChange.Id
			mrEntity.CodebaseRepoId = codebaseChange.Source.Repo.ID
		}

		if err := service.DB.Transaction(func(tx *gorm.DB) error {
			_, err := repository.NewCodeChangeRepository(tx).Create(ctx, codeChangeEntity).Get()
			if err != nil {
				return err
			}
			mrEntity.CodeChangeId = codeChangeEntity.Id
			_, err = repository.NewGitlabMergeRequestRepository(tx).Create(ctx, mrEntity).Get()
			if err != nil {
				return err
			}
			return nil
		}); err != nil {
			log.V2.Error().With(ctx).Str("failed to create code change gitlab mr").Error(err).KVs("code change entity", codeChangeEntity, "mr entity", mrEntity).Emit()
			// close gitlab mr if save error
			_ = service.CloseGitlabMr(ctx, int64(mergeRequest.ProjectID), int64(mergeRequest.IID))
			return gresult.Err[*ChangeCreationDetail](err)
		}
		res := &ChangeCreationDetail{
			ChangeDetail: &ChangeDetail{
				GitlabMr: mrEntity,
			},
			CodeChange: codeChangeEntity,
		}
		return gresult.OK(res)
	} else {

		removeSourceBranch := false
		if gitlabMrConfig.RemoveSourceBranch != nil {
			removeSourceBranch = *gitlabMrConfig.RemoveSourceBranch
		}
		options := &codebaseapi.CreateChangeRequest{
			SourceBranch: &codeChangeConfig.SourceBranch,
			TargetBranch: &codeChangeConfig.TargetBranch,
			Title:        &codeChangeConfig.Title,
			Description:  codeChangeConfig.Description,
			ExternalAttrs: &codebaseapi.ExternalAttrs{
				removeSourceBranch,
				gitlabMrConfig.Labels,
			},
			Links: codebase.TransformToLinks(codeChangeConfig.Links),
			Draft: codeChangeConfig.Draft,
		}
		// get user token
		token := service.authenticateService.GetCodebaseAccessToken(ctx, codeChangeConfig.GetUsername()).ValueOrZero()
		// create mr
		// ctx context.Context, repoId int64, request *CreateChangeRequest, token string
		repo, err := service.codebaseApiClient.GetRepoByProjectID(ctx, gitlabMrConfig.GetProjectId(), "")
		if err != nil {
			return gresult.Err[*ChangeCreationDetail](err)
		}
		change, err := service.codebaseApiClient.CreateChange(ctx, repo.ID, options, token).Get()
		if err != nil {
			log.V2.Error().With(ctx).Str("failed to create merge request").Error(err).KV("options", options).Emit()
			return gresult.Err[*ChangeCreationDetail](errors.FromCodebaseApiError(err))
		}

		// fill platform config
		platFormConfig := &entity.PlatformConfig{
			GitlabProjectID: gitlabMrConfig.GetProjectId(),
			GitlabIid:       change.GetMrIid(),
		}
		codeChangeEntity.PlatformChangeConfig = platFormConfig.ToString()
		// create code change and mr change
		mrEntity := &entity.CodeChangeGitlabMergeRequest{
			ProjectId:    gitlabMrConfig.GetProjectId(),
			Iid:          change.GetMrIid(),
			Title:        codeChangeConfig.Title,
			Description:  codeChangeConfig.GetDescription(),
			State:        "opened",
			SourceBranch: codeChangeConfig.SourceBranch,
			TargetBranch: codeChangeConfig.TargetBranch,
			Author:       codeChangeConfig.Username,
		}

		if change.Source != nil {
			mrEntity.LastCommitId = change.Source.Sha
		}

		mrEntity.CodebaseRepoId = repo.ID
		mrEntity.CodebaseChangeId = change.Id

		if err := service.DB.Transaction(func(tx *gorm.DB) error {
			_, err := repository.NewCodeChangeRepository(tx).Create(ctx, codeChangeEntity).Get()
			if err != nil {
				return err
			}
			mrEntity.CodeChangeId = codeChangeEntity.Id
			_, err = repository.NewGitlabMergeRequestRepository(tx).Create(ctx, mrEntity).Get()
			if err != nil {
				return err
			}
			return nil
		}); err != nil {
			log.V2.Error().With(ctx).Str("failed to create code change gitlab mr").Error(err).KVs("code change entity", codeChangeEntity, "mr entity", mrEntity).Emit()
			// close gitlab mr if save error
			_ = service.CloseGitlabMr(ctx, gitlabMrConfig.ProjectId, change.GetMrIid())
			return gresult.Err[*ChangeCreationDetail](err)
		}
		res := &ChangeCreationDetail{
			ChangeDetail: &ChangeDetail{
				GitlabMr: mrEntity,
			},
			CodeChange: codeChangeEntity,
		}
		return gresult.OK(res)
	}
}

const (
	UpdateStrategyGitlab   = "gitlab"
	UpdateStrategyCodebase = "codebase"
)

func (service *GitlabMrService) UpdateStrategies(updateOptions *git_server.CodeChangeUpdateOptions) []string {

	strategies := make([]string, 0)
	if updateOptions.Draft != nil {
		strategies = append(strategies, UpdateStrategyCodebase)
	}
	if updateOptions.Title != nil ||
		updateOptions.Description != nil ||
		updateOptions.TargetBranch != nil ||
		updateOptions.SourceBranch != nil ||
		updateOptions.Status != nil ||
		updateOptions.PlatformChangeConfig != nil ||
		updateOptions.RemoveSourceBranch != nil ||
		updateOptions.Squash != nil {
		strategies = append(strategies, UpdateStrategyGitlab)
	}
	return strategies
}

func (service *GitlabMrService) Update(ctx context.Context, cid int64, updateOptions *git_server.CodeChangeUpdateOptions) error {
	if err := service.VerifyUpdateOptions(updateOptions); err != nil {
		log.V2.Error().With(ctx).Str("verify update options  failed").Error(err).KVs("options", updateOptions).Emit()
		return err
	}
	mrChange, err := service.gitlabMergeRequestRepository.FindLastByCodeChangeId(ctx, cid).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("find mr change failed").Error(err).KVs("cid", cid).Emit()
		return err
	}

	strategies := service.UpdateStrategies(updateOptions)
	if gslice.Contains(strategies, UpdateStrategyGitlab) {
		_, err = service.gitlabClient.UpdateMergeRequest(ctx, mrChange.ProjectId, mrChange.Iid, TransCodeChangeUpdateOptionsToMrUpdateOptions(updateOptions)).Get()
		if err != nil {
			log.V2.Error().With(ctx).Str("update gitlab mr failed").Error(err).KVs("options", updateOptions).Emit()
			return err
		}
	}
	if gslice.Contains(strategies, UpdateStrategyCodebase) {
		token := service.metaApi.QueryCodebaseToken(ctx, mrChange.Author).ValueOrZero()
		_, err = service.codebaseApiClient.UpdateChange(ctx, mrChange.CodebaseRepoId, mrChange.CodebaseChangeId, TransCodeChangeUpdateOptionsToUpdateChangeRequest(updateOptions), token).Get()
		if err != nil {
			log.V2.Error().With(ctx).Str("update codebase mr failed").Error(err).KVs("options", updateOptions).Emit()
			return err
		}
	}
	// update mr entity
	err = service.gitlabMergeRequestRepository.UpdateByCodeChangeIdSkipZero(ctx, cid, TransCodeChangeUpdateOptionsToUpdateMap(updateOptions))
	if err != nil {
		log.V2.Error().With(ctx).Str("update mr MySQL failed").Error(err).KVs("entity", updateOptions).Emit()
		return err
	}
	return nil
}

func (service *GitlabMrService) Close(ctx context.Context, cid int64) error {
	mrChange, err := service.gitlabMergeRequestRepository.FindLastByCodeChangeId(ctx, cid).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("find mr change failed").Error(err).KVs("cid", cid)
		return err
	}
	err = service.CloseGitlabMr(ctx, mrChange.ProjectId, mrChange.Iid)
	if err != nil {
		log.V2.Error().With(ctx).Str("close gitlab mr failed").Error(err).KVs("pid", mrChange.ProjectId, "iid", mrChange.Iid)
		return err
	}
	// update mr entity
	value := &entity.CodeChangeGitlabMergeRequest{
		State: "closed",
	}
	err = service.gitlabMergeRequestRepository.UpdateByCodeChangeId(ctx, cid, value)
	if err != nil {
		log.V2.Error().With(ctx).Str("update mr change in MySQL failed").Error(err).KVs("entity", value)
		return err
	}
	return nil
}

func (service *GitlabMrService) CloseGitlabMr(ctx context.Context, projectID, iid int64) error {
	state := "close"
	options := &bytedancegitlab.UpdateMergeRequestOptions{StateEvent: &state}
	if _, err := service.gitlabClient.UpdateMergeRequest(ctx, projectID, iid, options).Get(); err != nil {
		log.V2.Error().With(ctx).Str("failed to update merge request").Error(err).KVs("pid", projectID, "merge-request", iid, "options", options).Emit()
		return errors.FromGitlabApiError(err)
	}
	return nil
}

func (service *GitlabMrService) Merge(ctx context.Context, cid int64, mergeOptions *git_server.CodeChangeMergeOptions) error {
	if err := service.VerifyMergeOptions(mergeOptions); err != nil {
		return err
	}
	mrChange, err := service.gitlabMergeRequestRepository.FindLastByCodeChangeId(ctx, cid).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("find mr change failed").Error(err).KVs("cid", cid)
		return err
	}
	gitlabOptions := &bytedancegitlab.AcceptMergeRequestOptions{}
	if mergeOptions != nil && mergeOptions.GitlabMrMergeOptions != nil {
		gitlabOptions = &bytedancegitlab.AcceptMergeRequestOptions{
			MergeCommitMessage:        mergeOptions.GitlabMrMergeOptions.MergeCommitMessage,
			SquashCommitMessage:       mergeOptions.GitlabMrMergeOptions.SquashCommitMessage,
			Squash:                    mergeOptions.GitlabMrMergeOptions.Squash,
			ShouldRemoveSourceBranch:  mergeOptions.GitlabMrMergeOptions.ShouldRemoveSourceBranch,
			MergeWhenPipelineSucceeds: mergeOptions.GitlabMrMergeOptions.MergeWhenPipelineSucceeds,
			SHA:                       mergeOptions.GitlabMrMergeOptions.Sha,
			MergeMethod:               mergeOptions.GitlabMrMergeOptions.MergeMethod,
		}
		if mergeOptions.GitlabMrMergeOptions.Username != nil && len(*mergeOptions.GitlabMrMergeOptions.Username) > 0 {
			token := service.authenticateService.GetGitlabAccessToken(ctx, *mergeOptions.GitlabMrMergeOptions.Username).ValueOrZero()
			if len(token) > 0 {
				gitlabOptions.AccessToken = token
			}
		}
	}
	_, err = service.gitlabClient.AcceptMergeRequest(ctx, mrChange.ProjectId, mrChange.Iid, gitlabOptions).Get()
	if err != nil {
		return errors.FromGitlabApiError(err)
	}
	// 合入是异步的，而且接口并不会返回失败，不能直接修改 DB 记录的状态，靠 codebase webhook 去更新
	return nil
}

func (service *GitlabMrService) Get(ctx context.Context, cid int64) gresult.R[*ChangeDetail] {
	mrChange, err := service.gitlabMergeRequestRepository.FindLastByCodeChangeId(ctx, cid).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("find mr change failed").Error(err).KVs("cid", cid)
		return gresult.Err[*ChangeDetail](err)
	}
	if mrChange.CodebaseRepoId == 0 || mrChange.CodebaseChangeId == 0 {
		mrChange, err = service.fillCodebaseChangeFields(ctx, mrChange).Get()
		if err != nil {
			return gresult.Err[*ChangeDetail](err)
		}
	}
	return gresult.OK(&ChangeDetail{
		GitlabMr: mrChange,
	})
}

func (service *GitlabMrService) GetOpenedMrChangesByProjectIDAndBranch(ctx context.Context, projectID int64, sourceBranch string) gresult.R[[]*entity.CodeChangeGitlabMergeRequest] {
	value, err := service.gitlabMergeRequestRepository.FindByProjectIdAndSourceBranchAndState(ctx, projectID, sourceBranch, "opened").Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to get mr change from MySQL").Error(err).KVs("project id", projectID, "source_branch", sourceBranch).Emit()
		return gresult.Err[[]*entity.CodeChangeGitlabMergeRequest](err)
	}
	return gresult.OK(value)
}

func (service *GitlabMrService) GetGitlabMrChange(ctx context.Context, projectID int64, iid int64) gresult.R[*entity.CodeChangeGitlabMergeRequest] {
	value, err := service.gitlabMergeRequestRepository.FindLastByProjectIdAndIid(ctx, projectID, iid).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to get mr change from MySQL").Error(err).KVs("project id", projectID, "iid", iid).Emit()
		return gresult.Err[*entity.CodeChangeGitlabMergeRequest](err)
	}
	if value.CodebaseRepoId == 0 || value.CodebaseChangeId == 0 {
		value, err = service.fillCodebaseChangeFields(ctx, value).Get()
		if err != nil {
			return gresult.Err[*entity.CodeChangeGitlabMergeRequest](err)
		}
	}
	return gresult.OK(value)
}

func (service *GitlabMrService) GetCodebaseMrChange(ctx context.Context, repoId int64, changeId int64) gresult.R[*entity.CodeChangeGitlabMergeRequest] {
	value, err := service.gitlabMergeRequestRepository.FindLastByCodebaseRepoIdAndChangeId(ctx, repoId, changeId).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to get mr change from MySQL").Error(err).KVs("repoId", repoId, "changeId", changeId).Emit()
		return gresult.Err[*entity.CodeChangeGitlabMergeRequest](err)
	}
	return gresult.OK(value)
}

func (service *GitlabMrService) BatchGetGitlabMrChangeByCIDs(ctx context.Context, ids []int64) gresult.R[[]*entity.CodeChangeGitlabMergeRequest] {
	value, err := service.gitlabMergeRequestRepository.FindByCodeChangeIdIn(ctx, ids).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to get mr change from MySQL").Error(err).KVs("ids", ids).Emit()
		return gresult.Err[[]*entity.CodeChangeGitlabMergeRequest](err)
	}
	return gresult.OK(value)
}

func (service *GitlabMrService) BatchGetGitlabMrChangeByCodebaseChangeIDs(ctx context.Context, ids []int64) gresult.R[[]*entity.CodeChangeGitlabMergeRequest] {
	value, err := service.gitlabMergeRequestRepository.FindByCodebaseChangeIdIn(ctx, ids).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to get mr change from MySQL").Error(err).KVs("ids", ids).Emit()
		return gresult.Err[[]*entity.CodeChangeGitlabMergeRequest](err)
	}
	return gresult.OK(value)
}

func (service *GitlabMrService) FindCodeChangeIdsByRepoIdAndSourceBranch(ctx context.Context, projectId int64, sourceBranch string, lastId int64, size int64) gresult.R[[]int64] {
	value, err := service.gitlabMergeRequestRepository.FindCodeChangeIdsByProjectIdAndSourceBranch(ctx, projectId, sourceBranch, lastId, size).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to find code change ids from MySQL").Error(err).KVs("projectId", projectId, "sourceBranch", sourceBranch, "lastId", lastId).Emit()
		return gresult.Err[[]int64](err)
	}
	return gresult.OK(value)
}

func (service *GitlabMrService) fillCodebaseChangeFields(ctx context.Context, model *entity.CodeChangeGitlabMergeRequest) gresult.R[*entity.CodeChangeGitlabMergeRequest] {
	codebaseChange, err := service.codebaseApiClient.GetChangesByProjectID(ctx, model.ProjectId, model.Iid).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("find codebase change failed").Error(err).KVs("pid", model.ProjectId, "iid", model.Iid)
		return gresult.Err[*entity.CodeChangeGitlabMergeRequest](err)
	}
	model.CodebaseRepoId = codebaseChange.Source.Repo.ID
	model.CodebaseChangeId = codebaseChange.Id
	// update fields of entity
	go func() {
		value := &entity.CodeChangeGitlabMergeRequest{
			CodebaseChangeId: model.CodebaseChangeId,
			CodebaseRepoId:   model.CodebaseRepoId,
		}
		err = service.gitlabMergeRequestRepository.UpdateByCodeChangeId(ctx, model.CodeChangeId, value)
		if err != nil {
			log.V2.Error().With(ctx).Str("update mr MySQL failed").Error(err).KVs("entity", value)
			return
		}
	}()
	return gresult.OK(model)
}
