load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "ref",
    srcs = [
        "compare_refs_count.go",
        "domain.go",
        "mapper.go",
        "verify.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/infra/git_server/pkg/domain/ref",
    visibility = ["//visibility:public"],
    deps = [
        "//infra/git_server/kitex_gen/base",
        "//infra/git_server/kitex_gen/bytedance/bits/git_server",
        "//infra/git_server/pkg/backends/bytedancegitlab",
        "//infra/git_server/pkg/backends/codebaseapi",
        "//infra/git_server/pkg/backends/meta",
        "//infra/git_server/pkg/baselib/functools",
        "//infra/git_server/pkg/baselib/gitparse",
        "//infra/git_server/pkg/dal/mysql/repository",
        "//infra/git_server/pkg/domain/codebase",
        "//infra/git_server/pkg/internal/errors",
        "//infra/git_server/pkg/service/authenticate",
        "//infra/git_server/pkg/service/branch",
        "//infra/git_server/pkg/service/pipeline",
        "//infra/git_server/pkg/service/project",
        "//infra/git_server/pkg/service/repository",
        "//libs/bits_err",
        "@com_github_xanzy_go_gitlab//:go-gitlab",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "ref_test",
    srcs = [
        "compare_refs_count_test.go",
        "domain_test.go",
    ],
    embed = [":ref"],
    deps = [
        "//infra/git_server/kitex_gen/bytedance/bits/git_server",
        "//infra/git_server/mocktools",
        "//infra/git_server/pkg/backends/bytedancegitlab",
        "//infra/git_server/pkg/backends/codebaseapi",
        "//libs/connections/redis",
        "//libs/connections/sqlite",
        "@org_byted_code_bits_hephaestus//pkg/jsons",
    ],
)
