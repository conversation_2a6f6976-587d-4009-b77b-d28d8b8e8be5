/**
 * @Date: 2022/1/13
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package file

import (
	"context"
	"encoding/base64"

	"code.byted.org/devinfra/hagrid/infra/git_server/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/infra/git_server/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/bytedancegitlab"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/codebaseapi"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/internal/errors"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/service/contents"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/service/repository"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/service/repositoryfiles"
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gresult"
	"github.com/xanzy/go-gitlab"
)

type FileDomain struct {
	gitlabApi              bytedancegitlab.Api
	codebaseApi            codebaseapi.Api
	repositoryFilesService *repositoryfiles.RepositoryFilesService
	repositoryService      *repository.RepositoryService
	fileContentsService    *contents.FileContentsService
}

func NewFileDomain(rdb *goredis.Client, gitlabApi bytedancegitlab.Api, codebaseApi codebaseapi.Api) *FileDomain {
	return &FileDomain{
		gitlabApi:              gitlabApi,
		codebaseApi:            codebaseApi,
		repositoryFilesService: repositoryfiles.NewRepositoryFilesService(rdb, gitlabApi, codebaseApi),
		repositoryService:      repository.NewRepositoryService(rdb, codebaseApi),
		fileContentsService:    contents.NewFileContentsService(codebaseApi),
	}
}

func (domain *FileDomain) GetFileMetaData(ctx context.Context, req *git_server.GetFileMetaDataRequest) (*git_server.GetFileMetaDataResponse, error) {
	if err := VerifyGetFileMetaDataRequest(req); err != nil {
		return nil, err
	}

	file, err := domain.gitlabApi.GetFileMetaData(ctx, int64(req.GetProjectId()), req.GetFilePath(), req.GetRef())
	if err != nil {
		return nil, errors.FromGitlabApiError(err)
	}

	resp := git_server.NewGetFileMetaDataResponse()
	resp.SetFile(TransformGitlabFileToGitServerFile(file))
	return resp, nil
}

func (domain *FileDomain) GetFileContent(ctx context.Context, req *git_server.GetFileContentRequest) gresult.R[*git_server.GetFileContentResponse] {
	if err := VerifyGetFileContentRequest(req); err != nil {
		resp := &git_server.GetFileContentResponse{
			BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_INVALID_ARGUMENT)},
		}
		return gresult.OK(resp)
	}

	var (
		content []byte
		err     error
	)
	if req.IsSetProjectId() {
		content, err = domain.repositoryFilesService.GetRawFileFromGitlab(ctx, int64(req.GetProjectId()), req.Ref, req.FilePath).Get()
	} else if req.IsSetCodebaseId() {
		content, err = domain.repositoryFilesService.GetRawFileFromCodebase(ctx, int64(req.GetCodebaseId()), req.Ref, req.FilePath).Get()
	}

	if err != nil {
		resp := &git_server.GetFileContentResponse{
			BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_UNKNOWN)},
		}
		return gresult.OK(resp)
	}
	resp := &git_server.GetFileContentResponse{Body: content}
	return gresult.OK(resp)
}

func (domain *FileDomain) GetContent(ctx context.Context, req *git_server.GetContentRequest) gresult.R[*git_server.GetContentResponse] {
	repo, err := domain.repositoryService.FindByProjectId(ctx, req.ProjectId, "").Get()
	if err != nil {
		return gresult.Err[*git_server.GetContentResponse](err)
	}

	response, err := domain.codebaseApi.GetRawFile(ctx, repo.ID, req.Filename, req.Revision)
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to get content").Error(err).KVs("repo", repo.ID, "file", req.Filename, "revision", req.Revision).Emit()
		return gresult.Err[*git_server.GetContentResponse](errors.FromCodebaseApiError(err))
	}

	content, _ := base64.StdEncoding.DecodeString(response.Content)

	resp := &git_server.GetContentResponse{
		Name:     response.Name,
		Path:     response.Path,
		Sha:      response.Sha,
		Size:     int64(response.Size),
		Content:  conv.UnsafeBytesToString(content),
		Type:     string(response.Type),
		Encoding: string(response.Encoding),
	}
	return gresult.OK(resp)
}

func (domain *FileDomain) UpdateFileContent(ctx context.Context, req *git_server.UpdateFileContentRequest) (*git_server.UpdateFileContentResponse, error) {
	if err := VerifyUpdateFileContentRequest(req); err != nil {
		return nil, err
	}

	options := &gitlab.UpdateFileOptions{
		Branch:        &req.Branch,
		Encoding:      nil,
		AuthorEmail:   &req.AuthorEmail,
		AuthorName:    &req.AuthorName,
		Content:       &req.FileContent,
		CommitMessage: &req.CommitMessage,
		LastCommitID:  nil,
	}
	fileInfo, err := domain.gitlabApi.UpdateFile(ctx, req.GetProjectId(), req.GetFileName(), options)
	if err != nil {
		return nil, errors.FromGitlabApiError(err)
	}

	resp := git_server.NewUpdateFileContentResponse()
	resp.SetFilePath(fileInfo.FilePath)
	resp.SetBranch(fileInfo.Branch)
	resp.SetSuccess(true)
	return resp, nil
}

func (domain *FileDomain) GetFileContentByURL(ctx context.Context, req *git_server.GetFileContentByURLRequest) (*git_server.GetFileContentByURLResponse, error) {
	if err := VerifyGetFileContentByURLRequest(req); err != nil {
		return nil, err
	}

	body, err := domain.gitlabApi.GetFileByURL(ctx, req.GetContentUrl())
	if err != nil {
		return nil, errors.FromGitlabApiError(err)
	}

	resp := git_server.NewGetFileContentByURLResponse()
	resp.SetBody(body)
	return resp, nil
}

func (domain *FileDomain) BatchGetFileContents(ctx context.Context, req *git_server.BatchGetFileContentsRequest) gresult.R[*git_server.BatchGetFileContentsResponse] {
	repo, err := domain.repositoryService.FindByProjectId(ctx, req.Project, "").Get()
	if err != nil {
		return gresult.Err[*git_server.BatchGetFileContentsResponse](err)
	}

	var files []*codebaseapi.File
	if len(req.Paths) > 0 {
		files = domain.fileContentsService.BatchGetFileContentByPaths(ctx, repo.ID, req.Revision, req.Paths)
	} else if len(req.GetRegex()) > 0 {
		files = domain.fileContentsService.BatchGetFileContentByRegex(ctx, repo.ID, req.Revision, req.GetRegex())
	}

	resp := &git_server.BatchGetFileContentsResponse{
		Files:    TransformToFiles(files),
		BaseResp: &base.BaseResp{StatusMessage: "ok", StatusCode: int32(base.Code_OK)},
	}
	return gresult.OK(resp)
}
