package commit_queue

import (
	"code.byted.org/devinfra/hagrid/infra/git_server/kitex_gen/bytedance/bits/git_server_cq"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/commitqueueapi"
)

func TransformToGitServerChange(change *commitqueueapi.Change) *git_server_cq.Change {
	return &git_server_cq.Change{
		Id:        int64(change.ID),
		RepoId:    int64(change.RepoID),
		RepoName:  change.RepoName,
		Url:       change.URL,
		Author:    change.Author,
		Title:     change.Title,
		SourceRef: change.SourceRef,
		TargetRef: change.TargetRef,
		Sender:    change.Sender,
		InQueue:   change.InQueue,
		Priority:  int64(change.Priority),
	}
}
