package cache

import (
	"context"
	"testing"
	"time"

	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/backends/codebaseapi"
	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"github.com/stretchr/testify/assert"
)

func TestCommitCache(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustConnectAtLocally()
	cache := NewCommitCache(rdb)

	value := &codebaseapi.GitCommit{
		Id:      "123",
		Title:   "aaa",
		Message: "bbb",
	}
	repoId := int64(1)
	revision := "rrr"

	cache.Create(ctx, repoId, revision, value, 3*time.Second)
	defer func() { cache.DeleteByRepoIdAndRevision(ctx, repoId, revision) }()

	actual := cache.FindByRepoIdAndRevision(ctx, repoId, revision).Must()
	assert.Equal(t, "123", actual.Id)
	assert.Equal(t, "aaa", actual.Title)
	assert.Equal(t, "bbb", actual.Message)
}
