package cache

import (
	"context"
	"fmt"
	"time"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
)

// ChangeExplorer 通过 codebase repo id + codebase change id 判断是否有对应的开发任务
// 这个数据结构, 你不要使用
type ChangeExplorer struct {
	rdb *goredis.Client
}

func NewChangeExplorer(rdb *goredis.Client) *ChangeExplorer {
	return &ChangeExplorer{rdb: rdb}
}

const day = 24 * time.Hour

func (explorer *ChangeExplorer) Create(ctx context.Context, repoId, changeId int64) {
	key := fmt.Sprintf("change-explorer|repo:%d|change:%d", repoId, changeId)

	err := explorer.rdb.
		WithContext(ctx).
		SetNX(key, "1", 30*day).
		Err()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to set change").Error(err).KV("key", key).Emit()
	}
}
