/**
 * @Date: 2022/1/20
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repository

import (
	"context"
	"fmt"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/dal/mysql/entity"
	"strings"

	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type ProjectInfoRepository struct {
	db *gorm.DB
}

func NewProjectInfoRepository(db *gorm.DB) *ProjectInfoRepository {
	return &ProjectInfoRepository{
		db: db.Table(schema.Tabler(new(entity.ProjectInfo)).TableName()),
	}
}

func (repository *ProjectInfoRepository) Create(ctx context.Context, value *entity.ProjectInfo) (int64, error) {
	err := repository.db.
		WithContext(ctx).
		Create(value).
		Error

	if err != nil {
		return 0, err
	}
	return value.ID, err
}

func (repository *ProjectInfoRepository) FindLastByAppID(ctx context.Context, appID int64) (*entity.ProjectInfo, error) {
	value := new(entity.ProjectInfo)
	err := repository.db.
		WithContext(ctx).
		Where("`app_id` = ?", appID).
		Last(value).
		Error

	if err != nil {
		return nil, err
	}
	return value, nil
}

func (repository *ProjectInfoRepository) FindFirstByProjectId(ctx context.Context, projectId int64) gresult.R[*entity.ProjectInfo] {
	value := new(entity.ProjectInfo)
	err := repository.db.
		WithContext(ctx).
		Where("`project_id` = ?", projectId).
		First(value).
		Error

	if err != nil {
		return gresult.Err[*entity.ProjectInfo](err)
	}
	return gresult.OK(value)
}

func (repository *ProjectInfoRepository) FindLastByProjectId(ctx context.Context, projectId int64) gresult.R[*entity.ProjectInfo] {
	value := new(entity.ProjectInfo)
	err := repository.db.
		WithContext(ctx).
		Where("`project_id` = ?", projectId).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.ProjectInfo](err)
	}
	return gresult.OK(value)
}

func (repository *ProjectInfoRepository) FindByProjectIDAndRepoID(ctx context.Context, projectID *int64, repoID *int64) (entity.ProjectInfos, error) {
	values := make([]*entity.ProjectInfo, 0, 4)

	wheres := []string{}
	if projectID != nil {
		wheres = append(wheres, fmt.Sprintf("`project_id` = %d", *projectID))
	}
	if repoID != nil {
		wheres = append(wheres, fmt.Sprintf("`repo_id` = %d", *repoID))
	}

	err := repository.db.
		WithContext(ctx).
		Where(strings.Join(wheres, " AND ")).
		Find(&values).
		Error

	if err != nil {
		return nil, err
	}
	return values, nil
}

func (repository *ProjectInfoRepository) DeleteByID(ctx context.Context, id int64) error {
	err := repository.db.
		WithContext(ctx).
		Delete(entity.ProjectInfo{}, id).
		Error

	if err != nil {
		return err
	}
	return nil
}
