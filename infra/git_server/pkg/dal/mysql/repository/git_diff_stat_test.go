/**
 * @Date: 2022/6/29
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repository

import (
	"context"
	"code.byted.org/devinfra/hagrid/infra/git_server/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestDiffStatRepository(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	db := sqlite.MustInitialize()
	_ = db.AutoMigrate(new(entity.DiffStat))
	repository := NewDiffStatRepository(db)

	t.Run("FindLastByProjectIdAndMrIid", func(t *testing.T) {
		value := entity.NewDiffStat(123, 23, 10, 20)

		_ = repository.Create(ctx, value).Must()
		defer func() { _ = repository.DeleteByProjectIdAndMrIid(ctx, 123, 23) }()

		stat := repository.FindLastByProjectIdAndMrIid(ctx, 123, 23).Must()
		assert.Equal(t, uint(10), stat.Insertions)
		assert.Equal(t, uint(20), stat.Deletions)
	})
}
