/**
 * @Date: 2023/11/17
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package entity

import (
	"time"

	"gorm.io/gorm/schema"
)

type OptimusUser struct {
	Id           int64     `gorm:"column:id"`
	Username     string    `gorm:"column:username"`
	GitlabUserId int64     `gorm:"column:gitlab_user_id"`
	FeishuUserId int64     `gorm:"column:feishu_user_id"`
	CreateTime   time.Time `gorm:"column:create_time"`
	UpdateTime   time.Time `gorm:"column:update_time"`
}

func NewOptimusUser(username string, gitlabUserId int64, feishuUserId int64) *OptimusUser {
	now := time.Now()

	return &OptimusUser{
		Username:     username,
		GitlabUserId: gitlabUserId,
		FeishuUserId: feishuUserId,
		CreateTime:   now,
		UpdateTime:   now,
	}
}

var _ schema.Tabler = OptimusUser{}

func (OptimusUser) TableName() string {
	return "optimus_user_optimususer"
}
