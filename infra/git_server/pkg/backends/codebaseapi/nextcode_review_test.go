package codebaseapi

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/bits/hephaestus/pkg/jsons"
)

func TestReview(t *testing.T) {
	ctx := context.Background()
	client := NewClient(nil)

	t.Run("ListReviewers", func(t *testing.T) {
		t.Skip<PERSON>ow()

		req := &ListReviewersRequest{
			MergeRequestId:               "671062199113167",
			RepoId:                       "462106",
			WithMeetReviewRules:          nil,
			WithPendingEffectiveApprover: nil,
		}
		resp := client.ListReviewers(ctx, req)
		fmt.Println(jsons.Stringify(resp))
	})
}
