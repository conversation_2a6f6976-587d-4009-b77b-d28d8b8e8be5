/**
 * @Date: 2022/2/15
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package codebaseapi

import (
	"context"
	"net/http"

	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/lang/gg/gresult"
	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
)

type BatchGetWorkItemPoliciesRequest struct {
	CombinationIDSs []string `json:"combination_ids"`
	Email           string   `json:"email"`
}

type BatchGetWorkItemPoliciesResponse struct {
	Policies []*WorkItemPolicy `json:"policies"`
}

type WorkItemPolicy struct {
	WorkItemID            int64  `json:"work_item_id"`
	WorkItemCombinationID string `json:"work_item_combination_id"`
	EccNeeded             bool   `json:"ecc_needed"`
}

func (client *client) BatchGetWorkItemPolicies(ctx context.Context, request *BatchGetWorkItemPoliciesRequest, token string) gresult.R[[]*WorkItemPolicy] {
	u := "/unstable/compliance/batch_get_work_item_policies"

	response := new(BatchGetWorkItemPoliciesResponse)
	resp, err := WithJsonWebToken(client.httpclient.R(), token).
		SetContext(ctx).
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[[]*WorkItemPolicy](errors.WithMessage(err, "failed to send request to codebase"))
	}
	if resp.StatusCode() != http.StatusOK {
		return gresult.Err[[]*WorkItemPolicy](errors.New(gjson.Get(conv.UnsafeBytesToString(resp.Body()), "message").String()))
	}
	return gresult.OK(response.Policies)
}

type BatchGetChangePoliciesRequest struct {
	CombinationIds []*RepoAndChangeID `json:"combination_ids"`
}

type RepoAndChangeID struct {
	RepoID   int64 `json:"repo_id"`
	ChangeID int64 `json:"change_id"`
}

func NewRepoAndChangeID(repoID int64, changeID int64) *RepoAndChangeID {
	return &RepoAndChangeID{RepoID: repoID, ChangeID: changeID}
}

type BatchGetChangePoliciesResponse struct {
	Policies []*ChangePolicy `json:"policies"`
}

type ChangePolicy struct {
	RepoId    int64 `json:"repo_id"`
	ChangeId  int64 `json:"change_id"`
	EccNeeded bool  `json:"ecc_needed"`
}

func (client *client) BatchGetChangePolicies(ctx context.Context, request *BatchGetChangePoliciesRequest, token string) gresult.R[[]*ChangePolicy] {
	u := "/unstable/compliance/batch_get_change_policies"

	response := new(BatchGetChangePoliciesResponse)
	resp, err := WithJsonWebToken(client.httpclient.R(), token).
		SetContext(ctx).
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[[]*ChangePolicy](errors.WithMessage(err, "failed to send request to codebase"))
	}
	if resp.StatusCode() != http.StatusOK {
		return gresult.Err[[]*ChangePolicy](errors.New(gjson.Get(conv.UnsafeBytesToString(resp.Body()), "message").String()))
	}
	return gresult.OK(response.Policies)
}

type BatchGetRepoPoliciesRequest struct {
	RepoIDs []int64 `json:"repo_ids"`
}

type BatchGetRepoPoliciesResponse struct {
	Policies []*RepoPolicy `json:"policies"`
}

type RepoPolicy struct {
	RepoID    int64 `json:"repo_id"`
	EccNeeded bool  `json:"ecc_needed"`
}

func (client *client) BatchGetRepoPolicies(ctx context.Context, repoIDs []int64) gresult.R[[]*RepoPolicy] {
	u := "/unstable/compliance/batch_get_repo_policies"

	request := &BatchGetRepoPoliciesRequest{RepoIDs: repoIDs}
	response := new(BatchGetRepoPoliciesResponse)
	resp, err := client.httpclient.
		R().
		SetContext(ctx).
		SetBody(request).
		SetResult(response).
		Post(u)
	if err != nil {
		return gresult.Err[[]*RepoPolicy](errors.WithMessage(err, "failed to send request to codebase"))
	}
	if resp.StatusCode() != http.StatusOK {
		return gresult.Err[[]*RepoPolicy](errors.New(gjson.Get(conv.UnsafeBytesToString(resp.Body()), "message").String()))
	}
	return gresult.OK(response.Policies)
}
