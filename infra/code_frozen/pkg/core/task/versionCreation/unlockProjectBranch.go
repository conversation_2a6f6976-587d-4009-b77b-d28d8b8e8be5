package versionCreation

import (
	"context"
	"fmt"
	"code.byted.org/devinfra/hagrid/infra/code_frozen/pkg/core/atomic"
	"code.byted.org/devinfra/hagrid/infra/code_frozen/pkg/core/task/consts"
	"code.byted.org/devinfra/hagrid/infra/code_frozen/pkg/core/task/taskUtil"
)

type UnLockProjectBranchTask struct{}

func (u UnLockProjectBranchTask) TaskName() string {
	return consts.UnLockProjectBranchTask
}

func (u UnLockProjectBranchTask) Start(ctx context.Context, params, contextJson string) (end bool, contextInter, outputInter interface{}, err error) {
	// parse from params
	id, err := getBusinessIdFromStr(params)
	if err != nil {
		err = fmt.Errorf("parse params from contextStr err - %v", err)
		return false, nil, nil, err
	}

	// set status
	err = taskUtil.SetVersionCreationStage(ctx, id, u.TaskName())
	if err != nil {
		return false, nil, nil, err
	}

	// get range
	helper := taskUtil.NewRelationHelper(consts.BusinessVersionCreation, id)
	projects, err := helper.GetAll(ctx)
	if err != nil {
		return false, nil, nil, err
	}

	if len(projects) == 0 {
		return true, nil, nil, nil
	}

	result := atomic.NewBatch().UnLockProjectBranches(ctx, adapt2project(projects, u.TaskName()), commonRecorder)

	isEnd := len(result.Succeeded) == len(projects)
	if isEnd {
		_ = helper.FinishStage(ctx, u.TaskName())
	}

	return isEnd, nil, nil, nil
}

func (u UnLockProjectBranchTask) IsEnd(ctx context.Context, params, contextJson string) (end bool, contextInter, outputInter interface{}, err error) {
	// parse from params
	id, err := getBusinessIdFromStr(params)
	if err != nil {
		err = fmt.Errorf("parse params from contextStr err - %v", err)
		return false, nil, nil, err
	}

	// get range
	helper := taskUtil.NewRelationHelper(consts.BusinessVersionCreation, id)
	projects, err := helper.GetAll(ctx)
	if err != nil {
		return false, nil, nil, err
	}

	if len(projects) == 0 {
		return true, nil, nil, nil
	}

	result := atomic.NewBatch().UnLockProjectBranches(ctx, adapt2project(projects, u.TaskName()), commonRecorder)
	isEnd := len(result.Succeeded) == len(projects)
	if isEnd {
		_ = helper.FinishStage(ctx, u.TaskName())
	}

	return isEnd, nil, nil, nil
}

func (UnLockProjectBranchTask) Reset(ctx context.Context, params, contextJson string) (contextInter, outputInter interface{}, err error) {
	return
}

func (UnLockProjectBranchTask) Close(ctx context.Context, params, contextJson string) (contextInter, outputInter interface{}, err error) {
	return
}
