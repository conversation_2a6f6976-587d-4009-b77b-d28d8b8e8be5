package tt_release_plan

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"code.byted.org/clientQA/artifact-manager/framework/util"
	BytedanceBitsMeta "code.byted.org/clientQA/artifact-manager/kitex_gen/bytedance/bits/meta"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/pkg/errors"
)

func GetVersionReleaseDate(ctx context.Context, cloudAppID int64, stack BytedanceBitsMeta.TechnologyStack, version string) (*int64, error) {
	var (
		app string
		sys string
	)
	switch cloudAppID {
	case 1180:
		app = "TikTok"
	case 1233:
		app = "TikTok-M"
	default:
		return nil, nil
	}
	switch stack {
	case BytedanceBitsMeta.TechnologyStack_Android:
		sys = "Android"
	case BytedanceBitsMeta.TechnologyStack_iOS:
		sys = "iOS"
	default:
		return nil, nil
	}

	url := fmt.Sprintf("http://oar.bytedance.net/api/v2/release_plan?type=all&app=%v&sys=%v&version=%v", app, sys, version)
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}
	addLogIDHeader(ctx, req)

	res, err := client.Do(req)
	if err != nil {
		return nil, errors.Wrapf(err, "client do err")
	}
	defer res.Body.Close()
	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return nil, errors.Wrapf(err, "ReadAll err")
	}
	var resp response
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, errors.Wrapf(err, "Failed to request the TT plan，body: %v", string(body))
	}
	plans := resp.Data.ReleasePlan
	for i := range plans {
		if plans[i].ReleaseScale == "Full rollout" {
			parse, err := time.ParseInLocation("2006-01-02 15:04:05", plans[i].CreatedAt, util.DefaultZone)
			if err != nil {
				return nil, errors.Wrapf(err, "Failed to deserialize the time! time: %v", plans[i].CreatedAt)
			}
			unix := parse.Unix()
			return &unix, nil
		}
	}
	return nil, nil
}

func addLogIDHeader(ctx context.Context, req *http.Request) {
	if logsID, hasLogID := ctxvalues.LogID(ctx); hasLogID {
		req.Header.Set("X-TT-LOGID", logsID)
	}
}

type response struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    struct {
		ReleasePlan []struct {
			Id            string `json:"id"`
			App           string `json:"app"`
			ReleaseCounts string `json:"release_counts"`
			ReleaseSystem string `json:"release_system"`
			VersionCode   string `json:"version_code"`
			IsRelease     string `json:"is_release"`
			AddGrayScale  string `json:"add_gray_scale"`
			ReleaseScale  string `json:"release_scale"`
			GrayScaleId   string `json:"gray_scale_id"`
			CreatedAt     string `json:"created_at"`
			ReleaseTime   string `json:"release_time"`
			Author        string `json:"author"`
		} `json:"release_plan"`
	} `json:"data"`
}
