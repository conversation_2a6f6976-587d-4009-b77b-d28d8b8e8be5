package tt_release_plan

import (
	"context"
	"fmt"
	"testing"
	"time"

	"code.byted.org/clientQA/artifact-manager/framework/util"
	BytedanceBitsMeta "code.byted.org/clientQA/artifact-manager/kitex_gen/bytedance/bits/meta"
)

func TestGetVersionReleaseDate(t *testing.T) {
	date, err := GetVersionReleaseDate(context.Background(), 1180, BytedanceBitsMeta.TechnologyStack_Android, "24.2.0")
	fmt.Printf("%v\n", time.Unix(util.SafeInt64(date), 0))
	fmt.Printf("err: %v", util.ToJsonSimple(err))
}
