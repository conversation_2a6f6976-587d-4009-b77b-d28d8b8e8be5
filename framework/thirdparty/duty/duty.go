package duty

import (
	"code.byted.org/clientQA/artifact-manager/framework/util"
	"context"
	"fmt"
	"github.com/tidwall/gjson"
)

func GetDutyOncallers(dutyPlatformToken string) ([]string, error) {
	dutyPlatformUrl := fmt.Sprintf("https://data.bytedance.net/duty/oapi/v2/schedule/%v/", dutyPlatformToken)
	dutyRetBody, err := util.HttpRequest(context.Background(), "GET", dutyPlatformUrl, nil, nil)
	if err != nil {
		return nil, util.NewErr(err, "[GetDutyOncallers] err get duty users")
	}
	primary := gjson.Get(string(dutyRetBody), `data.oncall.primary`)
	backup := gjson.Get(string(dutyRetBody), `data.oncall.backup`)
	return []string{primary.String(), backup.String()}, nil
}
