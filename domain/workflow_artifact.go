package domain

import (
	"context"
)

type WorkflowArtifact struct {
	Id                  int64 `gorm:"-"`
	BitsAppID           int64 `gorm:"column:bits_app_id"`
	AtomExecutionID     int64 `gorm:"column:atom_execution_id" json:"atom_execution_id"`
	PipelineExecutionID int64 `gorm:"column:pipeline_execution_id" json:"pipeline_execution_id"`
	StageID             int64 `gorm:"column:stage_id" json:"stage_id"`
	WorkflowExecutionID int64 `gorm:"column:workflow_execution_id" json:"workflow_execution_id"`
	ArtifactID          int64 `gorm:"column:artifact_id" json:"artifact_id"`
}

type WorkflowArtifactRepository interface {
	ListArtifactsInWorkflowExec(ctx context.Context, exec int64) ([]*WorkflowArtifact, error)
}
