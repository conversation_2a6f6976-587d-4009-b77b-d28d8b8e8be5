package testflight

import "strings"

type NotificationGroup struct {
	Id           int64  `json:"id" gorm:"-"`
	BitsAppId    int64  `json:"bits_app_id" gorm:"bits_app_id"`
	LarkGroupIds string `json:"lark_group_ids" gorm:"lark_group_ids"`
}

func (group *NotificationGroup) GetGroupIds() []string {
	return strings.Split(group.LarkGroupIds, ",")
}

type NotificationRepository interface {
	FindByBitsAppId(bitsAppId int64) (*NotificationGroup, error)
	Create(group *NotificationGroup) error
	Update(group *NotificationGroup) error
}
