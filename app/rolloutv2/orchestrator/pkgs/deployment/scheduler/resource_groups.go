package scheduler

import (
	"sort"
	"strings"

	"code.byted.org/devinfra/pbgen/byted/devinfra/rolloutpb"
)

// group resources by their metadata.label.`key`. resource that does not have the `key`
// is considered a standalone unnamed group.
func groupResourcesByLabel(resources []*rolloutpb.Resource, key string) []*changeGroup {
	groups := make([]*changeGroup, 0)
	namedGroups := make(map[string]*changeGroup)

	for idx := range resources {
		resource := resources[idx]
		labels := resources[idx].GetMetadata().GetLabels()
		if len(labels) > 0 {
			if value, ok := labels[key]; ok {
				// the specified label key exists.
				group := namedGroups[value]
				if group == nil {
					// creat the group
					group = &changeGroup{labelValue: &value, resources: make([]*rolloutpb.Resource, 0)}
					namedGroups[value] = group
					groups = append(groups, group)
				}
				group.resources = append(group.resources, resource)

				continue
			}
		}
		// specified label key does not exist.
		group := &changeGroup{labelValue: nil, resources: []*rolloutpb.Resource{resource}}
		groups = append(groups, group)
	}

	return groups
}

type changeGroup struct {
	labelValue *string // value of the label key used to group, nil if value is not specified.
	resources  []*rolloutpb.Resource
}

// return resources name ordered lexicographically
func (g *changeGroup) ResourceNames() []string {
	names := make([]string, 0)
	for idx := range g.resources {
		names = append(names, g.resources[idx].Metadata.GetLogicalId())
	}
	sort.Slice(names, func(i, j int) bool { return strings.Compare(names[i], names[j]) < 0 })
	return names
}

func (g *changeGroup) ResourceNameSet() map[string]struct{} {
	nameSet := make(map[string]struct{})
	for idx := range g.resources {
		nameSet[g.resources[idx].Metadata.GetLogicalId()] = struct{}{}
	}
	return nameSet
}
