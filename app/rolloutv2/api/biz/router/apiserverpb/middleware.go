// Code generated by hertz generator.

package apiserverpb

import (
	"code.byted.org/devinfra/hagrid/app/rolloutv2/api/internal/middleware"
	"code.byted.org/middleware/hertz/pkg/app"
)

func rootMw() []app.HandlerFunc {
	// your code...
	return []app.HandlerFunc{
		middleware.Recovery(),
		middleware.CORS(),
		middleware.Security(),
	}
}

func _apiMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _v1Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _requestassetconditionqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitionsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitionsqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _nameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitiondeploystatusqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcelatestsucceedversionqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitionsnapshotrangequeryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitionstatusqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _snapshotsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitionsnapshotqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _partitionregisterMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _requestsMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyrequesteventqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _request_idMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _assetapplystatusqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _applyrequeststatusupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _assetapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _tasksMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deploymenttaskconditionqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _task_idMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deploymenttaskstatusupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _deploymenttaskcreateMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcesMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourcequeryMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _requestcancelMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _checkersMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _task_id0Mw() []app.HandlerFunc {
	// your code...
	return nil
}

func _checker_nameMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _predeploymentcheckerstatusupdateMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _resourcebatchqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}

func _resourceintentbatchapplyMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _synconlinespecMw() []app.HandlerFunc {
	// your code...
	return nil
}
func _resourceonlinesnapshotqueryMw() []app.HandlerFunc {
	// your code...
	return nil
}
