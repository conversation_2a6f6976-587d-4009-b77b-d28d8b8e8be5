// Code generated by hertztool.

package handler

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rolloutv2/api/biz/handler/bind"
	"code.byted.org/devinfra/hagrid/app/rolloutv2/api/internal/service"
	"code.byted.org/devinfra/hagrid/app/rolloutv2/api/pkg/http/respond"
	"code.byted.org/devinfra/pbgen/byted/devinfra/iac/rolloutv2/apiserverpb"
	"code.byted.org/middleware/hertz/pkg/app"
)

// RequestAssetConditionQuery .
// @router /api/v1/assets [GET]
func RequestAssetConditionQuery(ctx context.Context, c *app.RequestContext) {
	var req apiserverpb.RequestAssetConditionQueryRequest
	if err := respond.BindingAndValidate(c, &req); err != nil {
		return
	}
	var bindReq bind.RequestAssetConditionQueryRequest
	if err := respond.BindNoBody(c, &bindReq); err != nil {
		return
	}
	bindReq.Fill(&req)

	ins := service.Instance()
	resp, err := ins.RequestAssetConditionQuery(ctx, &req)
	ins.PostProcess(ctx, c, &req, resp, err)
	respond.ProtoBuf(ctx, c, &req, resp, err)
}
