// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/rolloutv2/api/internal/service (interfaces: Service)
//
// Generated by this command:
//
//	mockgen -package mock -destination service/mock/mock.go code.byted.org/devinfra/hagrid/app/rolloutv2/api/internal/service Service
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	apiserverpb "code.byted.org/devinfra/pbgen/byted/devinfra/iac/rolloutv2/apiserverpb"
	app "github.com/cloudwego/hertz/pkg/app"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// ApplyRequestEventQuery mocks base method.
func (m *MockService) ApplyRequestEventQuery(arg0 context.Context, arg1 *apiserverpb.ApplyRequestEventQueryRequest) (*apiserverpb.ApplyRequestEventQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyRequestEventQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.ApplyRequestEventQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyRequestEventQuery indicates an expected call of ApplyRequestEventQuery.
func (mr *MockServiceMockRecorder) ApplyRequestEventQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyRequestEventQuery", reflect.TypeOf((*MockService)(nil).ApplyRequestEventQuery), arg0, arg1)
}

// ApplyRequestStatusUpdate mocks base method.
func (m *MockService) ApplyRequestStatusUpdate(arg0 context.Context, arg1 *apiserverpb.ApplyRequestStatusUpdateRequest) (*apiserverpb.ApplyRequestStatusUpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ApplyRequestStatusUpdate", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.ApplyRequestStatusUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ApplyRequestStatusUpdate indicates an expected call of ApplyRequestStatusUpdate.
func (mr *MockServiceMockRecorder) ApplyRequestStatusUpdate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ApplyRequestStatusUpdate", reflect.TypeOf((*MockService)(nil).ApplyRequestStatusUpdate), arg0, arg1)
}

// AssetApply mocks base method.
func (m *MockService) AssetApply(arg0 context.Context, arg1 *apiserverpb.AssetApplyRequest) (*apiserverpb.AssetApplyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssetApply", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.AssetApplyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssetApply indicates an expected call of AssetApply.
func (mr *MockServiceMockRecorder) AssetApply(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssetApply", reflect.TypeOf((*MockService)(nil).AssetApply), arg0, arg1)
}

// AssetApplyStatusQuery mocks base method.
func (m *MockService) AssetApplyStatusQuery(arg0 context.Context, arg1 *apiserverpb.AssetApplyStatusQueryRequest) (*apiserverpb.AssetApplyStatusQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssetApplyStatusQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.AssetApplyStatusQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AssetApplyStatusQuery indicates an expected call of AssetApplyStatusQuery.
func (mr *MockServiceMockRecorder) AssetApplyStatusQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssetApplyStatusQuery", reflect.TypeOf((*MockService)(nil).AssetApplyStatusQuery), arg0, arg1)
}

// DeploymentTaskConditionQuery mocks base method.
func (m *MockService) DeploymentTaskConditionQuery(arg0 context.Context, arg1 *apiserverpb.DeploymentTaskConditionQueryRequest) (*apiserverpb.DeploymentTaskConditionQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeploymentTaskConditionQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.DeploymentTaskConditionQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeploymentTaskConditionQuery indicates an expected call of DeploymentTaskConditionQuery.
func (mr *MockServiceMockRecorder) DeploymentTaskConditionQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeploymentTaskConditionQuery", reflect.TypeOf((*MockService)(nil).DeploymentTaskConditionQuery), arg0, arg1)
}

// DeploymentTaskCreate mocks base method.
func (m *MockService) DeploymentTaskCreate(arg0 context.Context, arg1 *apiserverpb.DeploymentTaskCreateRequest) (*apiserverpb.DeploymentTaskCreateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeploymentTaskCreate", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.DeploymentTaskCreateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeploymentTaskCreate indicates an expected call of DeploymentTaskCreate.
func (mr *MockServiceMockRecorder) DeploymentTaskCreate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeploymentTaskCreate", reflect.TypeOf((*MockService)(nil).DeploymentTaskCreate), arg0, arg1)
}

// DeploymentTaskStatusUpdate mocks base method.
func (m *MockService) DeploymentTaskStatusUpdate(arg0 context.Context, arg1 *apiserverpb.DeploymentTaskStatusUpdateRequest) (*apiserverpb.DeploymentTaskStatusUpdateResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeploymentTaskStatusUpdate", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.DeploymentTaskStatusUpdateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeploymentTaskStatusUpdate indicates an expected call of DeploymentTaskStatusUpdate.
func (mr *MockServiceMockRecorder) DeploymentTaskStatusUpdate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeploymentTaskStatusUpdate", reflect.TypeOf((*MockService)(nil).DeploymentTaskStatusUpdate), arg0, arg1)
}

// PartitionDeployStatusQuery mocks base method.
func (m *MockService) PartitionDeployStatusQuery(arg0 context.Context, arg1 *apiserverpb.PartitionDeployStatusQueryRequest) (*apiserverpb.PartitionDeployStatusQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PartitionDeployStatusQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.PartitionDeployStatusQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PartitionDeployStatusQuery indicates an expected call of PartitionDeployStatusQuery.
func (mr *MockServiceMockRecorder) PartitionDeployStatusQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PartitionDeployStatusQuery", reflect.TypeOf((*MockService)(nil).PartitionDeployStatusQuery), arg0, arg1)
}

// PartitionRegister mocks base method.
func (m *MockService) PartitionRegister(arg0 context.Context, arg1 *apiserverpb.PartitionRegisterRequest) (*apiserverpb.PartitionRegisterResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PartitionRegister", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.PartitionRegisterResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PartitionRegister indicates an expected call of PartitionRegister.
func (mr *MockServiceMockRecorder) PartitionRegister(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PartitionRegister", reflect.TypeOf((*MockService)(nil).PartitionRegister), arg0, arg1)
}

// PartitionSnapshotQuery mocks base method.
func (m *MockService) PartitionSnapshotQuery(arg0 context.Context, arg1 *apiserverpb.PartitionSnapshotQueryRequest) (*apiserverpb.PartitionSnapshotQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PartitionSnapshotQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.PartitionSnapshotQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PartitionSnapshotQuery indicates an expected call of PartitionSnapshotQuery.
func (mr *MockServiceMockRecorder) PartitionSnapshotQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PartitionSnapshotQuery", reflect.TypeOf((*MockService)(nil).PartitionSnapshotQuery), arg0, arg1)
}

// PartitionStatusQuery mocks base method.
func (m *MockService) PartitionStatusQuery(arg0 context.Context, arg1 *apiserverpb.PartitionStatusQueryRequest) (*apiserverpb.PartitionStatusQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PartitionStatusQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.PartitionStatusQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PartitionStatusQuery indicates an expected call of PartitionStatusQuery.
func (mr *MockServiceMockRecorder) PartitionStatusQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PartitionStatusQuery", reflect.TypeOf((*MockService)(nil).PartitionStatusQuery), arg0, arg1)
}

// PartitionsQuery mocks base method.
func (m *MockService) PartitionsQuery(arg0 context.Context, arg1 *apiserverpb.PartitionsQueryRequest) (*apiserverpb.PartitionsQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PartitionsQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.PartitionsQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PartitionsQuery indicates an expected call of PartitionsQuery.
func (mr *MockServiceMockRecorder) PartitionsQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PartitionsQuery", reflect.TypeOf((*MockService)(nil).PartitionsQuery), arg0, arg1)
}

// PostProcess mocks base method.
func (m *MockService) PostProcess(arg0 context.Context, arg1 *app.RequestContext, arg2, arg3 any, arg4 error) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PostProcess", arg0, arg1, arg2, arg3, arg4)
}

// PostProcess indicates an expected call of PostProcess.
func (mr *MockServiceMockRecorder) PostProcess(arg0, arg1, arg2, arg3, arg4 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PostProcess", reflect.TypeOf((*MockService)(nil).PostProcess), arg0, arg1, arg2, arg3, arg4)
}

// RequestAssetConditionQuery mocks base method.
func (m *MockService) RequestAssetConditionQuery(arg0 context.Context, arg1 *apiserverpb.RequestAssetConditionQueryRequest) (*apiserverpb.RequestAssetConditionQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestAssetConditionQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.RequestAssetConditionQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestAssetConditionQuery indicates an expected call of RequestAssetConditionQuery.
func (mr *MockServiceMockRecorder) RequestAssetConditionQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestAssetConditionQuery", reflect.TypeOf((*MockService)(nil).RequestAssetConditionQuery), arg0, arg1)
}

// RequestCancel mocks base method.
func (m *MockService) RequestCancel(arg0 context.Context, arg1 *apiserverpb.RequestCancelRequest) (*apiserverpb.RequestCancelResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestCancel", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.RequestCancelResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestCancel indicates an expected call of RequestCancel.
func (mr *MockServiceMockRecorder) RequestCancel(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestCancel", reflect.TypeOf((*MockService)(nil).RequestCancel), arg0, arg1)
}

// ResourceLatestSucceedVersionQuery mocks base method.
func (m *MockService) ResourceLatestSucceedVersionQuery(arg0 context.Context, arg1 *apiserverpb.ResourceLatestSucceedVersionQueryRequest) (*apiserverpb.ResourceLatestSucceedVersionQueryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceLatestSucceedVersionQuery", arg0, arg1)
	ret0, _ := ret[0].(*apiserverpb.ResourceLatestSucceedVersionQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResourceLatestSucceedVersionQuery indicates an expected call of ResourceLatestSucceedVersionQuery.
func (mr *MockServiceMockRecorder) ResourceLatestSucceedVersionQuery(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceLatestSucceedVersionQuery", reflect.TypeOf((*MockService)(nil).ResourceLatestSucceedVersionQuery), arg0, arg1)
}
