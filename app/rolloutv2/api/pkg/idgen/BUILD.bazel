load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "idgen",
    srcs = [
        "client.go",
        "generate.go",
        "idgen.go",
        "mock.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutv2/api/pkg/idgen",
    visibility = ["//visibility:public"],
    deps = [
        "@org_byted_code_gopkg_idgenerator_v2//:idgenerator",
        "@org_uber_go_mock//mockgen/model",
    ],
)
