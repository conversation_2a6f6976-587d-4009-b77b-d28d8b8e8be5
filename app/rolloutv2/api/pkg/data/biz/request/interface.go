package request

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rolloutv2/api/pkg/data/model"
)

type (
	Dal interface {
		Reader
		Writer
	}

	Reader interface {
		// query a single request, reutrn nil if not found.
		QueryRequest(ctx context.Context, id int64) (*model.Request, error)
		// response may only contain partial instances. it is the caller's responsibility to
		// check the number of instances returned.
		QueryRequests(ctx context.Context, ids []int64) (map[int64]*model.Request, error)
		// query requests by partition name and offset.
		QueryRequestByPartitionOffset(ctx context.Context, partition string, offset, limit int64) ([]*model.Request, error)
		// Query request in range (start, end).
		QueryRequestInRange(ctx context.Context, partition string, start int64, end *int64) ([]*model.Request, error)
		// Find the left pointer snapshot(request) id of the deployment window, where all snapshots before it
		// (including itself) have finished deployment phase, no matter succeed or failed.
		//
		// return 0 if no snapshots have finished deployment phase.
		FindStartingSnapshotOfDeploymentWindow(ctx context.Context, partition string) (int64, error)
		// Find the left pointer request id of the generation window, where all requests before it
		// (including itself) have finished generation phase, no matter succeed or failed.
		//
		// return 0 if no requests have finished generation phase.
		FindStartingRequestOfGenerationWindow(ctx context.Context, partition string) (int64, error)

		// CAS update the flag set of request.
		// return error if there is no change.
		CASUpdateRequestFlagSet(ctx context.Context, requestID int64, oldFS, newFS model.FlagSet) error
	}

	Writer interface {
		InsertRequest(ctx context.Context, request *model.Request) error
		UpdateRequestStatus(ctx context.Context, request *model.Request) error
	}
)
