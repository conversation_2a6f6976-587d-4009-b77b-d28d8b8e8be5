load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library", "go_test")

go_library(
    name = "online_lib",
    srcs = [
        "call.go",
        "log.go",
        "run.go",
        "test_data_yamls.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutv2/integration/online",
    visibility = ["//visibility:private"],
    deps = [
        "//app/rolloutv2/api/pkg/sdk",
        "//app/rolloutv2/utils/parser",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/iac/rolloutv2/apiserverpb",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/rollout/resourcespb",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/rolloutpb",
        "@org_byted_code_lang_gg//gptr",
        "@org_golang_google_protobuf//encoding/protojson",
    ],
)

go_binary(
    name = "online",
    embed = [":online_lib"],
    visibility = ["//visibility:public"],
)

go_test(
    name = "online_test",
    srcs = ["manual_test.go"],
    embed = [":online_lib"],
    deps = [
        "//app/rolloutv2/utils/parser",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/iac/rolloutv2/apiserverpb",
        "@org_byted_code_devinfra_pbgen//byted/devinfra/rolloutpb",
    ],
)
