/**
 * @Date: 2022/6/1
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package entity

type PodWebhookTechType int8

const (
	PodWebhookTechTypeIOS     PodWebhookTechType = 1
	PodWebhookTechTypeAndroid PodWebhookTechType = 2
	PodWebhookTechTypeFlutter PodWebhookTechType = 3
)

type PodWebhookEventType int8

const (
	PodWebhookEventTypeUpgrade          PodWebhookEventType = 1 // 升级组件
	PodWebhookEventTypeCreate           PodWebhookEventType = 2 // 创建组件
	PodWebhookEventTypeCreateByTemplate PodWebhookEventType = 3 // 根据模板创建组件
)

type PodWebhook struct {
	Id         int64               `gorm:"column:id"`
	RepoId     int64               `gorm:"column:repo_id"`    // 表示某个特定的 repo
	EventType  PodWebhookEventType `gorm:"column:event_type"` // 1升级组件，2 创建组件, 3 根据模板创建组件
	WebhookUrl string              `gorm:"column:webhook_url"`

	//TechType   PodWebhookTechType  `gorm:"column:tech_type"`  // 1-iOS，2-Android，3-Flutter
	//AppId      int64               `gorm:"column:app_id"`     // 0 表示所有 App，1301 表示头条
}

func (PodWebhook) TableName() string {
	return "mpaas_pod_upgrade_webhook"
}

func NewPodWebhook(repoId int64, eventType PodWebhookEventType, webhookUrl string) *PodWebhook {
	return &PodWebhook{RepoId: repoId, EventType: eventType, WebhookUrl: webhookUrl}
}

func (w *PodWebhook) Url() string {
	return w.WebhookUrl
}
