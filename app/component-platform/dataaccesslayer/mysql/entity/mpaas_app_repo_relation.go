package entity

import "gorm.io/gorm/schema"

type AppRepoRelation struct {
	Id       int64  `gorm:"column:id"`
	AppId    int64  `gorm:"column:appId"`    // 关联 apps  表的 id
	RepoId   int64  `gorm:"column:repoId"`   // 关联 pod_main  表的 id
	TeamId   int64  `gorm:"column:teamId"`   // 组件归属的团队Id
	TeamName string `gorm:"column:teamName"` // 组件归属的团队名称
}

var _ schema.Tabler = &AppRepoRelation{}

func (AppRepoRelation) TableName() string {
	return "mpaas_app_repo_relation"
}

// ------------------------

type AppIdAndRepoId struct {
	AppId  int `gorm:"column:appId"`  // 关联 apps  表的 id
	RepoId int `gorm:"column:repoId"` // 关联 pod_main  表的 id
}

var _ schema.Tabler = &AppIdAndRepoId{}

func (AppIdAndRepoId) TableName() string {
	return "mpaas_app_repo_relation"
}
