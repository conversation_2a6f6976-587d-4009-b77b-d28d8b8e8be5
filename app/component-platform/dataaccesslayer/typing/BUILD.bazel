load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "typing",
    srcs = ["pageable.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/typing",
    visibility = ["//visibility:public"],
)

go_test(
    name = "typing_test",
    srcs = ["pageable_test.go"],
    embed = [":typing"],
    deps = ["@com_github_stretchr_testify//assert"],
)
