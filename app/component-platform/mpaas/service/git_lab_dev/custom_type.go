package git_lab_dev

import (
	"time"

	"github.com/xanzy/go-gitlab"
)

// MergeEventDev 抄了 gitlab.MergeEvent 的实现，但是做出如下 fix：
// 1. ForceRemoveSourceBranch 可能是 bool 也可能是 string，因此改成了 interface
type MergeEventDev struct {
	ObjectKind string       `json:"object_kind"`
	User       *gitlab.User `json:"user"`
	Project    struct {
		ID                int                    `json:"id"`
		Name              string                 `json:"name"`
		Description       string                 `json:"description"`
		AvatarURL         string                 `json:"avatar_url"`
		GitSSHURL         string                 `json:"git_ssh_url"`
		GitHTTPURL        string                 `json:"git_http_url"`
		Namespace         string                 `json:"namespace"`
		PathWithNamespace string                 `json:"path_with_namespace"`
		DefaultBranch     string                 `json:"default_branch"`
		Homepage          string                 `json:"homepage"`
		URL               string                 `json:"url"`
		SSHURL            string                 `json:"ssh_url"`
		HTTPURL           string                 `json:"http_url"`
		WebURL            string                 `json:"web_url"`
		Visibility        gitlab.VisibilityValue `json:"visibility"`
	} `json:"project"`
	ObjectAttributes struct {
		ID              int              `json:"id"`
		TargetBranch    string           `json:"target_branch"`
		SourceBranch    string           `json:"source_branch"`
		SourceProjectID int              `json:"source_project_id"`
		AuthorID        int              `json:"author_id"`
		AssigneeID      int              `json:"assignee_id"`
		Title           string           `json:"title"`
		CreatedAt       string           `json:"created_at"` // Should be *time.Time (see Gitlab issue #21468)
		UpdatedAt       string           `json:"updated_at"` // Should be *time.Time (see Gitlab issue #21468)
		StCommits       []*gitlab.Commit `json:"st_commits"`
		StDiffs         []*gitlab.Diff   `json:"st_diffs"`
		MilestoneID     int              `json:"milestone_id"`
		State           string           `json:"state"`
		MergeStatus     string           `json:"merge_status"`
		TargetProjectID int              `json:"target_project_id"`
		IID             int              `json:"iid"`
		Description     string           `json:"description"`
		Position        int              `json:"position"`
		LockedAt        string           `json:"locked_at"`
		UpdatedByID     int              `json:"updated_by_id"`
		MergeError      string           `json:"merge_error"`
		MergeParams     struct {
			ForceRemoveSourceBranch interface{} `json:"force_remove_source_branch"`
		} `json:"merge_params"`
		MergeWhenBuildSucceeds   bool               `json:"merge_when_build_succeeds"`
		MergeUserID              int                `json:"merge_user_id"`
		MergeCommitSHA           string             `json:"merge_commit_sha"`
		DeletedAt                string             `json:"deleted_at"`
		ApprovalsBeforeMerge     string             `json:"approvals_before_merge"`
		RebaseCommitSHA          string             `json:"rebase_commit_sha"`
		InProgressMergeCommitSHA string             `json:"in_progress_merge_commit_sha"`
		LockVersion              int                `json:"lock_version"`
		TimeEstimate             int                `json:"time_estimate"`
		Source                   *gitlab.Repository `json:"source"`
		Target                   *gitlab.Repository `json:"target"`
		LastCommit               struct {
			ID        string     `json:"id"`
			Message   string     `json:"message"`
			Timestamp *time.Time `json:"timestamp"`
			URL       string     `json:"url"`
			Author    struct {
				Name  string `json:"name"`
				Email string `json:"email"`
			} `json:"author"`
		} `json:"last_commit"`
		WorkInProgress bool   `json:"work_in_progress"`
		URL            string `json:"url"`
		Action         string `json:"action"`
		OldRev         string `json:"oldrev"`
		Assignee       struct {
			Name      string `json:"name"`
			Username  string `json:"username"`
			AvatarURL string `json:"avatar_url"`
		} `json:"assignee"`
	} `json:"object_attributes"`
	Repository *gitlab.Repository `json:"repository"`
	Assignee   struct {
		Name      string `json:"name"`
		Username  string `json:"username"`
		AvatarURL string `json:"avatar_url"`
	} `json:"assignee"`
	Labels  []gitlab.Label `json:"labels"`
	Changes struct {
		AssigneeID struct {
			Previous int `json:"previous"`
			Current  int `json:"current"`
		} `json:"assignee_id"`
		Description struct {
			Previous string `json:"previous"`
			Current  string `json:"current"`
		} `json:"description"`
		Labels struct {
			Previous []gitlab.Label `json:"previous"`
			Current  []gitlab.Label `json:"current"`
		} `json:"labels"`
	} `json:"changes"`
}
