load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "db",
    srcs = [
        "android_repo_group_upgrade.go",
        "app_component_history.go",
        "app_component_info.go",
        "archPodUpgradeHistory.go",
        "arch_app.go",
        "arch_app_new.go",
        "arch_pod_main.go",
        "arch_pod_ng_mdin.go",
        "arch_pod_upgrade_ng.go",
        "arch_user.go",
        "component_dependency.go",
        "cony_mr.go",
        "dev_tag.go",
        "init.go",
        "init_v2.go",
        "integration_area_list.go",
        "ios_arch_pod_optimus_mr.go",
        "mobile_dev_company_app.go",
        "mpaas_app_repo_relation.go",
        "mpaas_app_user_relation.go",
        "mpaas_concern.go",
        "mpaas_frequent_settings.go",
        "mpaas_integration_history.go",
        "mpaas_kv_store.go",
        "mpaas_pipeline_job.go",
        "mpaas_pod_upgrade_build.go",
        "mpaas_project_detail.go",
        "mpaas_subapp.go",
        "mpaas_tag_component_relation.go",
        "mpaas_tags_upgrade_history_relation.go",
        "mpaas_tool.go",
        "mpass_branch_hook.go",
        "repo_template.go",
        "seer.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/db",
    visibility = ["//visibility:public"],
    deps = [
        "//app/component-platform/dataaccesslayer/mysql",
        "//app/component-platform/mpaas/conf",
        "//app/component-platform/mpaas/consts",
        "//app/component-platform/mpaas/model",
        "//app/component-platform/mpaas/service/ti",
        "//app/component-platform/mpaas/utils",
        "//app/component-platform/mpaas/utils/language",
        "//app/component-platform/mpaas/utils/language/type_helper",
        "//libs/common_lib/utils",
        "//libs/notification-client/consts",
        "//libs/notification-client/lark",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_gin_gonic_gin//:gin",
        "@com_github_go_sql_driver_mysql//:mysql",
        "@com_github_makenowjust_heredoc//:heredoc",
        "@com_github_pkg_errors//:errors",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_gopkg_context//:context",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_gopkg_mysql_driver//:mysql-driver",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_security_go_polaris//sql",
    ],
)
