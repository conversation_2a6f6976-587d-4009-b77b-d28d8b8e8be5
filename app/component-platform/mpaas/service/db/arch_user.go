package db

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/model"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/utils"
	client_consts "code.byted.org/devinfra/hagrid/libs/notification-client/consts"
	client_lark "code.byted.org/devinfra/hagrid/libs/notification-client/lark"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
	"github.com/gin-gonic/gin"
)

func GetUserByUserName(ctx context.Context, name string) (*model.ArchUsers, error) {
	sql := "select * from ios_arch_users where name = ?"
	logs.Debug("sql:%v", sql)
	var user model.ArchUsers
	db := DbReadInstance.NewRequest(ctx).Raw(sql, name).Scan(&user)
	if db.Error != nil {
		if db.Error == gorm.ErrRecordNotFound {
			//需要插入 user信息
		}
		return nil, db.Error

	}
	logs.Debug("user%v", user)
	return &user, nil
}

func GetUsersByUserName(ctx context.Context, name []string) ([]*model.ArchUsers, error) {
	var users []*model.ArchUsers
	db := DbReadInstance.NewRequest(ctx).Where("name in (?)", name).Find(&users)
	if db.Error != nil {
		utils.LogCtxDebug(ctx, "sql error:%s", db.Error)
		return nil, db.Error
	}
	return users, nil
}

func GetAllUsers(ctx context.Context) ([]*model.ArchUsers, error) {
	sql := "select * from ios_arch_users"
	logs.Debug("sql:%v", sql)
	var users []*model.ArchUsers
	db := DbReadInstance.NewRequest(ctx).Raw(sql).Scan(&users)
	if db.Error != nil {
		logs.Debug("db.error:%v", db.Error)
		return nil, db.Error

	}

	return users, nil
}

// InsertArchUser 插入用户信息 TODO：支持拼音，替换 Node 服务
func InsertArchUser(ctx context.Context, name string) error {
	defer logs.Flush()

	larkUserInfoRes, err := client_lark.QueryUserInfoByName(ctx, client_consts.ByteBusBot, name)
	if err != nil {
		logs.CtxInfo(ctx, "InsertArchUser, client_lark.QueryUserInfoByNames, err = %v", err)
		return err
	}
	logs.CtxInfo(ctx, "InsertArchUser, larkUserInfoRes = %+v", larkUserInfoRes)

	var user model.ArchUsers

	user.Name = name
	//user.LarkId = larkUserInfoRes.UserId // LarkId 被废弃，现存老的 larkId 被 Lark 2020年 9.11日 废弃。
	user.FullName = larkUserInfoRes.Name
	user.Avator = larkUserInfoRes.Avatar
	user.CustomizedConfig = model.GetDefaultCustomizedConfigString()
	logs.CtxInfo(ctx, "InsertArchUser, user = %+v", user)
	dbInfo := DbWriteInstance.NewRequest(ctx).Save(&user)
	logs.CtxInfo(ctx, "InsertArchUser, dbInfo error = %v", dbInfo.Error)
	return nil
}

func GetUserCarePodList(ctx context.Context, user string) ([]int, error) {
	// 关注的repo
	archUser, err := GetUserByUserName(ctx, user)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("GetUserByUserName error: %v", err))
	}
	podList := archUser.CarePodList
	arrPod := strings.Split(podList, ",")
	ret := make([]int, 0)
	for _, pod := range arrPod {
		if pod == "" {
			continue
		}
		podId, err := strconv.ParseInt(pod, 10, 64)
		if err != nil {
			logs.CtxError(ctx, fmt.Sprintf("parseInt error: %v", err))
			continue
		}
		ret = append(ret, int(podId))
	}
	return ret, nil
}

func GetUserByNames(ctx context.Context, nameArr []string) ([]*model.ArchUsers, error) {

	var users []*model.ArchUsers
	if len(nameArr) <= 0 {
		return users, nil
	}

	str := ""
	for index, name := range nameArr {

		if index == 0 {
			str += "( "
		}
		str += fmt.Sprintf("\"%s\"", name)
		if index != len(nameArr)-1 {
			str += ","
		} else {
			str += " )"
		}
	}

	sql := fmt.Sprintf("select * from ios_arch_users where name in %s", str)
	logs.Debug("sql:%v", sql)
	db := DbReadInstance.NewRequest(ctx).Raw(sql).Scan(&users)
	if db.Error != nil {
		logs.Debug("db.error:%v", db.Error)
		return nil, db.Error

	}
	logs.Debug("user%v", users)
	return users, nil
}

func GetUserNameDict(ctx context.Context, names []string) (map[string]*model.ArchUsers, error) {

	persons := make([]*model.ArchUsers, 0)
	if len(names) <= 0 {
		arr, err := GetAllUsers(ctx)
		if err != nil {
			utils.LogCtxDebug(ctx, "error %v", err)
			return nil, err
		}
		persons = arr
	} else {
		arr, err := GetUserByNames(ctx, names)
		if err != nil {
			utils.LogCtxDebug(ctx, "error %v", err)
			return nil, err
		}
		persons = arr
	}

	personDict := make(map[string]*model.ArchUsers)
	for _, p := range persons {
		personDict[p.Name] = p
	}
	return personDict, nil
}

// SearchUsersById 根据用户 id 来搜索
func SearchUsersById(ctx *gin.Context, userIDs []int) ([]*model.ArchUsers, error) {
	var users []*model.ArchUsers
	if err := DbReadInstance.NewRequest(ctx).Model(&model.ArchUsers{}).Where("id in (?)", userIDs).Scan(&users).Error; err != nil {
		logs.Debug("search users by ids: %v, err:%v", userIDs, err)
		return nil, err

	}
	return users, nil
}
