package luban

import (
	"context"
	"errors"
	"fmt"

	"code.byted.org/gopkg/lang/v2/conv"
)

type CreateHarmonyRepoRequest struct {
	Repository      string `json:"repository"`
	Scope           string `json:"scope"`
	Name            string `json:"name"`
	HttpCodeAddress string `json:"http_code_address"`
}

func (client *client) CreateHarmonyRepo(ctx context.Context, jwt, scope, name, httpCodeAddress string) error {
	u := "/service/core"

	req := &Request[*CreateHarmonyRepoRequest]{
		Action: "ohpm",
		Method: "bindCodeURL",
		Data: &CreateHarmonyRepoRequest{
			Repository:      "ohpm-byted",
			Scope:           scope,
			Name:            name,
			HttpCodeAddress: httpCodeAddress,
		},
	}

	resp, err := client.httpclient.
		R().
		SetContext(ctx).
		SetHeader("x-jwt-token", jwt).
		SetBody(req).
		Post(u)
	if err != nil {
		return fmt.Errorf("failed to send request to luban: %w", err)
	}
	if resp.IsError() {
		return errors.New(conv.UnsafeBytesToString(resp.Body()))
	}
	return nil
}
