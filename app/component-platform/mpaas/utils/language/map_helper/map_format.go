package map_helper

import json "github.com/bytedance/sonic"

// StructToMap 结构体转 Map。支持 field tag
func StructToMap(what interface{}) map[string]interface{} {
	m := map[string]interface{}{}
	js, err := json.<PERSON>(what)
	if err != nil {
		return m
	}
	err = json.Unmarshal(js, &m)
	if err != nil {
		return map[string]interface{}{}
	}
	return m
}

// 结构体转 MapStr
func StructToMapStr(what interface{}) map[string]string {
	m := map[string]string{}
	js, err := json.<PERSON>(what)
	if err != nil {
		return m
	}
	err = json.Unmarshal(js, &m)
	if err != nil {
		return map[string]string{}
	}
	return m
}
