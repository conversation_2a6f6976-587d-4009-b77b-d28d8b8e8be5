package integration_biz

import (
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/model"
)

func formatNameToConcernModel(nameArr []string, podId int, podType model.PodIdType, concernType model.ConcernType) []*model.Mpaas_concern {

	modelArr := make([]*model.Mpaas_concern, 0)
	for _, str := range nameArr {
		concern := model.Mpaas_concern{
			PodId:       podId,
			PodType:     podType,
			ConcernType: concernType,
			ConcernName: str,
		}
		modelArr = append(modelArr, &concern)
	}
	return modelArr
}
