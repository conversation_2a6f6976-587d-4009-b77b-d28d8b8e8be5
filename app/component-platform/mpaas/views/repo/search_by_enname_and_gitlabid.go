package repo

import (
	"errors"

	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/caller"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/kitex_gen/bytedance/bits/meta"

	"code.byted.org/gin/ginex"

	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/consts"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/service/db"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/utils"
	"code.byted.org/devinfra/hagrid/app/component-platform/mpaas/views/mpaas"

	"github.com/gin-gonic/gin"
)

type SearchByEnnameAndGitlabidReq struct {
	EnName   string `form:"en_name" valid:"NotEmpty"`
	GitlabId int    `form:"gitlab_id" valid:"int-min=1"`
}

func SearchByEnnameAndGitlabid(ctx *gin.Context) {
	req := SearchByEnnameAndGitlabidReq{}
	rpcCtx := ginex.RpcContext(ctx)
	err := ctx.Bind(&req)
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_PARAM_ERROR, nil, err)
		return
	}

	err = utils.ParamsValidate(req)
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_PARAM_ERROR, nil, err)
		return
	}

	appId := 0
	metaResp, err := caller.MetaClient.QueryAppInfoByAppName(rpcCtx, &meta.QueryAppInfoByAppNameRequest{
		EnName: &req.EnName,
	})
	if metaResp == nil || len(metaResp.AppInfoList) == 0 || err != nil {
		app, err := db.GetAppByEnName(ctx, req.EnName)
		if err != nil {
			utils.RenderCommonResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, nil, errors.New("line of business not found"))
			return
		}
		appId = app.Id
	} else {
		appId = int(metaResp.AppInfoList[0].Id)
	}

	project, err := db.GetProjectDetailByGitlabId(ctx, req.GitlabId)
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, nil, err)
		return
	}

	repo, err := db.GetRelatedRepoWithProjectIdInApp(ctx, project.Id, appId)
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, nil, err)
		return
	}

	repoInfo, err := mpaas.GetRepoInfo(ctx, int64(repo.Id))
	if err != nil {
		utils.RenderCommonResponse(ctx, consts.ERROR_NO_INTERNAL_ERROR, nil, err)
		return
	}

	utils.RenderCommonResponse(ctx, consts.ERROR_NO_OK, repoInfo, nil)
	return
}
