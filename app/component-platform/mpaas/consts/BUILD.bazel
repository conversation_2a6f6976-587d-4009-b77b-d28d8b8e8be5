load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "consts",
    srcs = [
        "common.go",
        "community.go",
        "constdef.go",
        "db.go",
        "image.go",
        "integration.go",
        "jenkins.go",
        "larkrobot.go",
        "metrics_name.go",
        "redis.go",
        "repo.go",
        "tcc.go",
        "ti.go",
        "upgrade_history.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/component-platform/mpaas/consts",
    visibility = ["//visibility:public"],
    deps = [
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_lang_gg//choose",
    ],
)
