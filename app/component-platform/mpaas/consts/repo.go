package consts

type GroupRole int

const (
	GroupRole_Group   GroupRole = 1
	GroupRole_SubRepo GroupRole = 0
)

type REPO_COMMONSERVICE_STATUS int

const (
	REPO_COMMONSERVICE_STATUS_UNKNOWN  REPO_COMMONSERVICE_STATUS = -1 // 未知的
	REPO_COMMONSERVICE_STATUS_SUCCESS  REPO_COMMONSERVICE_STATUS = 1  // 成功
	REPO_COMMONSERVICE_STATUS_PENDING  REPO_COMMONSERVICE_STATUS = 2  // 排队
	REPO_COMMONSERVICE_STATUS_RUNNING  REPO_COMMONSERVICE_STATUS = 3  // 执行中
	REPO_COMMONSERVICE_STATUS_FAILED   REPO_COMMONSERVICE_STATUS = 4  // 失败
	REPO_COMMONSERVICE_STATUS_CANCELED REPO_COMMONSERVICE_STATUS = 5  // 取消
)

type REPO_COMMONSERVICE_TYPE int

const (
	REPO_COMMONSERVICE_TYPE_UPGRADE  REPO_COMMONSERVICE_TYPE = 1
	REPO_COMMONSERVICE_TYPE_PIPELINE REPO_COMMONSERVICE_TYPE = 2
)

type OpText string

const IOS_REPO_PUBLIC int = 888801         // iOS三方库
const ANDROID_REPO_PUBLIC int = 2020092679 // Android三方库
const IOS_REPO_PRIVATE int = 999901
const ANDROID_REPO_PRIVATE int = 999902
const FLUTTER_REPO_PRIVATE int = 999903

// 这块暂时不能改，前端会匹配这个中文，来显示按钮。
const OpTextUpgrade OpText = "升级组件"
const OpTextDeleteOp OpText = "删除组件"
const OpTextEdit OpText = "编辑组件"

const OpTextDetail OpText = "组件详情"
const OpTextStaticAnalyze OpText = "静态分析"
const OpTextAddSubRepo OpText = "添加子组件"
const OpTextUnFavor OpText = "取消订阅"
const OpTextFavor OpText = "订阅组件"
const OpTextApply OpText = "申请权限"
const OpTextReset OpText = "状态重置"

type ExistProject int

const (
	ProjectNotExist ExistProject = 0
	AppExist        ExistProject = 1
	CommpentExist   ExistProject = 2
)

var VALID_VERSION_RELEASE_TYPE_RELEASE = "formal"
var VALID_VERSION_RELEASE_TYPE_RC = "rc"
var VALID_VERSION_RELEASE_TYPE_ALPHA = "alpha"
var VALID_VERSION_UPGRADE_TYPE_BUGFIX = "bugfix"

var VALID_VERSION_UPGRADE_TYPE_MAJOR = "major"
var VALID_VERSION_UPGRADE_TYPE_MINOR = "minor"
var VALID_VERSION_UPGRADE_TYPE_PATCH = "patch"

var VALID_VERSION_UPGRADE_TYPE = []string{
	VALID_VERSION_UPGRADE_TYPE_MAJOR,
	VALID_VERSION_UPGRADE_TYPE_MINOR,
	VALID_VERSION_UPGRADE_TYPE_PATCH,
}

const (
	PUBLIC_REPO_PREFIX = "8888"
)

type GitType int

const (
	GIT_TYPE_GITLAB GitType = 0
	GIT_TYPE_GERRIT GitType = 1
	GIT_TYPE_GITHUB GitType = 2
	GIT_TYPE_EXTRA  GitType = 3
)

const TAG_VISIBLEIN_TYPE_ALL = "all"
const TAG_USED_FOR_TYPE_COMPONENTS = "components"

// 常用的 tag 对应的 Id
const (
	CONTAINER_TAG_ID = 48 // 容器化
	STANDARD_TAG_ID  = 43 // 公司标准库
	TEST_TAG_ID      = 49 // 测试仓库
)

type PodWebhookEventType int

const (
	POD_WEBHOOK_EVENT_TYPE_UPGRADE = 1
	POD_WEBHOOK_EVENT_TYPE_CREATE  = 2
)

const POD_EVENT_TYPE_CREATE = "repo_create"

const (
	ANDROID_COMPONENT_CHILD  = 0 // 子组件
	ANDROID_COMPONENT_PARENT = 1 // 组件组
	ANDROID_COMPONENT_COMMON = 3 // 普通组件
)

const (
	RepoTemplateTypeGitlab = "gitlab"
)

const (
	RepoRoleAdmin     int = 1
	RepoRoleDeveloper int = 2
	RepoRoleFollow    int = 3
)

// ElasticSearch 相关 Key
const (
	FlutterPlaygroundKey = "flutter_playground"
	AndroidRepoGroupKey  = "repo_group"
	AndroidSubRepoKey    = "sub_repo"
	BusinessRepoKey      = "business_repo"

	ElasticSearchUploadKey_CN = "6ec2d3f5-11eb-11eb-a059-00163e12859f"

	ElasticSearchPodSearchIndex = "toe_mpaas_pod_search_online" // toe es 索引
)
