load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "handlers",
    srcs = [
        "android_upgrade.go",
        "app_version_dependencies.go",
        "calculate_repo_version.go",
        "get_component.go",
        "get_component_histories_by_name_and_version.go",
        "get_concerned_components.go",
        "get_history.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/component-platform/component-legacy/pkg/handlers",
    visibility = ["//visibility:public"],
    deps = [
        "//app/component-platform/component-legacy/kitex_gen/bytedance/bits/component",
        "//app/component-platform/component-legacy/kitex_gen/bytedance/bits/meta",
        "//app/component-platform/component-legacy/pkg/adaptor",
        "//app/component-platform/component-legacy/pkg/business/dag_parse_v2",
        "//app/component-platform/component-legacy/pkg/business/dag_parser",
        "//app/component-platform/component-legacy/pkg/business/dag_upgrade_v2",
        "//app/component-platform/component-legacy/pkg/business/query",
        "//app/component-platform/component-legacy/pkg/business/report",
        "//app/component-platform/component-legacy/pkg/business/upgrade",
        "//app/component-platform/component-legacy/pkg/consts",
        "//app/component-platform/component-legacy/pkg/dal/db",
        "//app/component-platform/component-legacy/pkg/dal/graph",
        "//app/component-platform/component-legacy/pkg/dal/redis",
        "//app/component-platform/component-legacy/pkg/lib",
        "//app/component-platform/component-legacy/pkg/model",
        "//app/component-platform/component-legacy/pkg/rpc",
        "//app/component-platform/component-legacy/pkg/utils",
        "//libs/common_lib/consts",
        "//libs/common_lib/utils",
        "//libs/middleware/restymw",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_go_resty_resty_v2//:resty",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_facility//ternary",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
    ],
)
