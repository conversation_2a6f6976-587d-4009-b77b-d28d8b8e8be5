package model

type OptimusProjectConfig struct {
	IosPodBinaryType int                 `json:"binary_type"`
	RockBinaryConfig rockBinaryConfig    `json:"rock_binary_source"`
	GitURL           string              `json:"git_url"`           // git url
	AutoPublish      bool                `json:"auto_publish"`      // 是否自动发版
	AutoPublishType  int                 `json:"auto_publish_type"` // 1 => bytebus
	IsBizPod         bool                `json:"is_biz_pod"`        // 是否是业务仓库
	SkipPipline      bool                `json:"skip_pipline"`      // 是否可以跳过pipline
	Reviewers        []string            `json:"reviewers"`         // review负责人
	FileReviewerMap  map[string][]string `json:"file_reviewer_map"` // 目录负责人
}

type rockBinaryConfig struct {
	Debug   string `json:"debug"`
	Release string `json:"release"`
}
