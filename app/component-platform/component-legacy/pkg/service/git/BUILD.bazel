load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "git",
    srcs = ["commit.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/component-platform/component-legacy/pkg/service/git",
    visibility = ["//visibility:public"],
    deps = [
        "//app/component-platform/component-legacy/kitex_gen/bytedance/bits/git_server",
        "//app/component-platform/component-legacy/pkg/rpc",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
