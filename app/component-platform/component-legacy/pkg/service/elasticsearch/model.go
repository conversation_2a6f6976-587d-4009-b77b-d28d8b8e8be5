package elasticsearch

// TODO ES 相关代码只是迁移过来，初步跑通验证没有问题了，内部具体逻辑没有挨个函数具体去测试。在使用 ES 的时候务必一定要先测试下。

const (
	Index               = "settings_item"
	Tp_Component        = "component_info"
	Tp_ComponentUpgrade = "component_upgrade_info"
	Tp_Dev              = "dev_info"
	EsUrl               = "http://offline-misc-hl-es.byted.org"
	Article             = "bytebus_community_article"
)

type ComponentUpgradeDoc struct {
	// text
	ChangeLog string `json:"str_changelog"`
	// number
	Id     int64 `json:"id"`
	RepoId int64 `json:"repo_id"`
	Score  int   `json:"score"`
	// keyword
	BuildResult  string `json:"kw_build_result"`
	OpUser       string `json:"kw_op_user"`
	Branch       string `json:"kw_branch"`
	Version      string `json:"kw_version"`
	VersionAlias string `json:"kw_version_alias"`
	CommitId     string `json:"kw_commit_id"`
	VersionType  string `json:"kw_version_type"`
	RepoName     string `json:"kw_repo_name"`
}
