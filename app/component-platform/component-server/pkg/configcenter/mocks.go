// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package configcenter

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMockApi creates a new instance of <PERSON><PERSON><PERSON><PERSON>. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockApi(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockApi {
	mock := &MockApi{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockApi is an autogenerated mock type for the Api type
type MockApi struct {
	mock.Mock
}

type MockApi_Expecter struct {
	mock *mock.Mock
}

func (_m *MockApi) EXPECT() *MockApi_Expecter {
	return &MockApi_Expecter{mock: &_m.Mock}
}

// GetDisplayTTPRoWUsers provides a mock function for the type MockApi
func (_mock *<PERSON>ck<PERSON>pi) GetDisplayTTPRoWUsers(ctx context.Context) []string {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetDisplayTTPRoWUsers")
	}

	var r0 []string
	if returnFunc, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	return r0
}

// MockApi_GetDisplayTTPRoWUsers_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDisplayTTPRoWUsers'
type MockApi_GetDisplayTTPRoWUsers_Call struct {
	*mock.Call
}

// GetDisplayTTPRoWUsers is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetDisplayTTPRoWUsers(ctx interface{}) *MockApi_GetDisplayTTPRoWUsers_Call {
	return &MockApi_GetDisplayTTPRoWUsers_Call{Call: _e.mock.On("GetDisplayTTPRoWUsers", ctx)}
}

func (_c *MockApi_GetDisplayTTPRoWUsers_Call) Run(run func(ctx context.Context)) *MockApi_GetDisplayTTPRoWUsers_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetDisplayTTPRoWUsers_Call) Return(strings []string) *MockApi_GetDisplayTTPRoWUsers_Call {
	_c.Call.Return(strings)
	return _c
}

func (_c *MockApi_GetDisplayTTPRoWUsers_Call) RunAndReturn(run func(ctx context.Context) []string) *MockApi_GetDisplayTTPRoWUsers_Call {
	_c.Call.Return(run)
	return _c
}

// GetFirstTtpSecondCnReleaseRepos provides a mock function for the type MockApi
func (_mock *MockApi) GetFirstTtpSecondCnReleaseRepos(ctx context.Context) []int64 {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFirstTtpSecondCnReleaseRepos")
	}

	var r0 []int64
	if returnFunc, ok := ret.Get(0).(func(context.Context) []int64); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}
	return r0
}

// MockApi_GetFirstTtpSecondCnReleaseRepos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFirstTtpSecondCnReleaseRepos'
type MockApi_GetFirstTtpSecondCnReleaseRepos_Call struct {
	*mock.Call
}

// GetFirstTtpSecondCnReleaseRepos is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetFirstTtpSecondCnReleaseRepos(ctx interface{}) *MockApi_GetFirstTtpSecondCnReleaseRepos_Call {
	return &MockApi_GetFirstTtpSecondCnReleaseRepos_Call{Call: _e.mock.On("GetFirstTtpSecondCnReleaseRepos", ctx)}
}

func (_c *MockApi_GetFirstTtpSecondCnReleaseRepos_Call) Run(run func(ctx context.Context)) *MockApi_GetFirstTtpSecondCnReleaseRepos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetFirstTtpSecondCnReleaseRepos_Call) Return(int64s []int64) *MockApi_GetFirstTtpSecondCnReleaseRepos_Call {
	_c.Call.Return(int64s)
	return _c
}

func (_c *MockApi_GetFirstTtpSecondCnReleaseRepos_Call) RunAndReturn(run func(ctx context.Context) []int64) *MockApi_GetFirstTtpSecondCnReleaseRepos_Call {
	_c.Call.Return(run)
	return _c
}

// GetFuzzySearchVersionsRepos provides a mock function for the type MockApi
func (_mock *MockApi) GetFuzzySearchVersionsRepos(ctx context.Context) []int64 {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetFuzzySearchVersionsRepos")
	}

	var r0 []int64
	if returnFunc, ok := ret.Get(0).(func(context.Context) []int64); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}
	return r0
}

// MockApi_GetFuzzySearchVersionsRepos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetFuzzySearchVersionsRepos'
type MockApi_GetFuzzySearchVersionsRepos_Call struct {
	*mock.Call
}

// GetFuzzySearchVersionsRepos is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetFuzzySearchVersionsRepos(ctx interface{}) *MockApi_GetFuzzySearchVersionsRepos_Call {
	return &MockApi_GetFuzzySearchVersionsRepos_Call{Call: _e.mock.On("GetFuzzySearchVersionsRepos", ctx)}
}

func (_c *MockApi_GetFuzzySearchVersionsRepos_Call) Run(run func(ctx context.Context)) *MockApi_GetFuzzySearchVersionsRepos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetFuzzySearchVersionsRepos_Call) Return(int64s []int64) *MockApi_GetFuzzySearchVersionsRepos_Call {
	_c.Call.Return(int64s)
	return _c
}

func (_c *MockApi_GetFuzzySearchVersionsRepos_Call) RunAndReturn(run func(ctx context.Context) []int64) *MockApi_GetFuzzySearchVersionsRepos_Call {
	_c.Call.Return(run)
	return _c
}

// GetGithubToken provides a mock function for the type MockApi
func (_mock *MockApi) GetGithubToken(ctx context.Context) string {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGithubToken")
	}

	var r0 string
	if returnFunc, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = returnFunc(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}
	return r0
}

// MockApi_GetGithubToken_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGithubToken'
type MockApi_GetGithubToken_Call struct {
	*mock.Call
}

// GetGithubToken is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetGithubToken(ctx interface{}) *MockApi_GetGithubToken_Call {
	return &MockApi_GetGithubToken_Call{Call: _e.mock.On("GetGithubToken", ctx)}
}

func (_c *MockApi_GetGithubToken_Call) Run(run func(ctx context.Context)) *MockApi_GetGithubToken_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetGithubToken_Call) Return(s string) *MockApi_GetGithubToken_Call {
	_c.Call.Return(s)
	return _c
}

func (_c *MockApi_GetGithubToken_Call) RunAndReturn(run func(ctx context.Context) string) *MockApi_GetGithubToken_Call {
	_c.Call.Return(run)
	return _c
}

// GetGlobalWebhooks provides a mock function for the type MockApi
func (_mock *MockApi) GetGlobalWebhooks(ctx context.Context) []string {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetGlobalWebhooks")
	}

	var r0 []string
	if returnFunc, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	return r0
}

// MockApi_GetGlobalWebhooks_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetGlobalWebhooks'
type MockApi_GetGlobalWebhooks_Call struct {
	*mock.Call
}

// GetGlobalWebhooks is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetGlobalWebhooks(ctx interface{}) *MockApi_GetGlobalWebhooks_Call {
	return &MockApi_GetGlobalWebhooks_Call{Call: _e.mock.On("GetGlobalWebhooks", ctx)}
}

func (_c *MockApi_GetGlobalWebhooks_Call) Run(run func(ctx context.Context)) *MockApi_GetGlobalWebhooks_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetGlobalWebhooks_Call) Return(strings []string) *MockApi_GetGlobalWebhooks_Call {
	_c.Call.Return(strings)
	return _c
}

func (_c *MockApi_GetGlobalWebhooks_Call) RunAndReturn(run func(ctx context.Context) []string) *MockApi_GetGlobalWebhooks_Call {
	_c.Call.Return(run)
	return _c
}

// GetHarmonyGreyRepos provides a mock function for the type MockApi
func (_mock *MockApi) GetHarmonyGreyRepos(ctx context.Context) []*RepoWithConfiguration {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetHarmonyGreyRepos")
	}

	var r0 []*RepoWithConfiguration
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*RepoWithConfiguration); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*RepoWithConfiguration)
		}
	}
	return r0
}

// MockApi_GetHarmonyGreyRepos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetHarmonyGreyRepos'
type MockApi_GetHarmonyGreyRepos_Call struct {
	*mock.Call
}

// GetHarmonyGreyRepos is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetHarmonyGreyRepos(ctx interface{}) *MockApi_GetHarmonyGreyRepos_Call {
	return &MockApi_GetHarmonyGreyRepos_Call{Call: _e.mock.On("GetHarmonyGreyRepos", ctx)}
}

func (_c *MockApi_GetHarmonyGreyRepos_Call) Run(run func(ctx context.Context)) *MockApi_GetHarmonyGreyRepos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetHarmonyGreyRepos_Call) Return(repoWithConfigurations []*RepoWithConfiguration) *MockApi_GetHarmonyGreyRepos_Call {
	_c.Call.Return(repoWithConfigurations)
	return _c
}

func (_c *MockApi_GetHarmonyGreyRepos_Call) RunAndReturn(run func(ctx context.Context) []*RepoWithConfiguration) *MockApi_GetHarmonyGreyRepos_Call {
	_c.Call.Return(run)
	return _c
}

// GetIOSXcodeVersions provides a mock function for the type MockApi
func (_mock *MockApi) GetIOSXcodeVersions(ctx context.Context) []*XcodeVersion {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetIOSXcodeVersions")
	}

	var r0 []*XcodeVersion
	if returnFunc, ok := ret.Get(0).(func(context.Context) []*XcodeVersion); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]*XcodeVersion)
		}
	}
	return r0
}

// MockApi_GetIOSXcodeVersions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetIOSXcodeVersions'
type MockApi_GetIOSXcodeVersions_Call struct {
	*mock.Call
}

// GetIOSXcodeVersions is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetIOSXcodeVersions(ctx interface{}) *MockApi_GetIOSXcodeVersions_Call {
	return &MockApi_GetIOSXcodeVersions_Call{Call: _e.mock.On("GetIOSXcodeVersions", ctx)}
}

func (_c *MockApi_GetIOSXcodeVersions_Call) Run(run func(ctx context.Context)) *MockApi_GetIOSXcodeVersions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetIOSXcodeVersions_Call) Return(xcodeVersions []*XcodeVersion) *MockApi_GetIOSXcodeVersions_Call {
	_c.Call.Return(xcodeVersions)
	return _c
}

func (_c *MockApi_GetIOSXcodeVersions_Call) RunAndReturn(run func(ctx context.Context) []*XcodeVersion) *MockApi_GetIOSXcodeVersions_Call {
	_c.Call.Return(run)
	return _c
}

// GetSuperAdministrators provides a mock function for the type MockApi
func (_mock *MockApi) GetSuperAdministrators(ctx context.Context) []string {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetSuperAdministrators")
	}

	var r0 []string
	if returnFunc, ok := ret.Get(0).(func(context.Context) []string); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}
	return r0
}

// MockApi_GetSuperAdministrators_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSuperAdministrators'
type MockApi_GetSuperAdministrators_Call struct {
	*mock.Call
}

// GetSuperAdministrators is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetSuperAdministrators(ctx interface{}) *MockApi_GetSuperAdministrators_Call {
	return &MockApi_GetSuperAdministrators_Call{Call: _e.mock.On("GetSuperAdministrators", ctx)}
}

func (_c *MockApi_GetSuperAdministrators_Call) Run(run func(ctx context.Context)) *MockApi_GetSuperAdministrators_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetSuperAdministrators_Call) Return(strings []string) *MockApi_GetSuperAdministrators_Call {
	_c.Call.Return(strings)
	return _c
}

func (_c *MockApi_GetSuperAdministrators_Call) RunAndReturn(run func(ctx context.Context) []string) *MockApi_GetSuperAdministrators_Call {
	_c.Call.Return(run)
	return _c
}

// GetTtpPublicRepos provides a mock function for the type MockApi
func (_mock *MockApi) GetTtpPublicRepos(ctx context.Context) []int64 {
	ret := _mock.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for GetTtpPublicRepos")
	}

	var r0 []int64
	if returnFunc, ok := ret.Get(0).(func(context.Context) []int64); ok {
		r0 = returnFunc(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]int64)
		}
	}
	return r0
}

// MockApi_GetTtpPublicRepos_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTtpPublicRepos'
type MockApi_GetTtpPublicRepos_Call struct {
	*mock.Call
}

// GetTtpPublicRepos is a helper method to define mock.On call
//   - ctx
func (_e *MockApi_Expecter) GetTtpPublicRepos(ctx interface{}) *MockApi_GetTtpPublicRepos_Call {
	return &MockApi_GetTtpPublicRepos_Call{Call: _e.mock.On("GetTtpPublicRepos", ctx)}
}

func (_c *MockApi_GetTtpPublicRepos_Call) Run(run func(ctx context.Context)) *MockApi_GetTtpPublicRepos_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(context.Context))
	})
	return _c
}

func (_c *MockApi_GetTtpPublicRepos_Call) Return(int64s []int64) *MockApi_GetTtpPublicRepos_Call {
	_c.Call.Return(int64s)
	return _c
}

func (_c *MockApi_GetTtpPublicRepos_Call) RunAndReturn(run func(ctx context.Context) []int64) *MockApi_GetTtpPublicRepos_Call {
	_c.Call.Return(run)
	return _c
}
