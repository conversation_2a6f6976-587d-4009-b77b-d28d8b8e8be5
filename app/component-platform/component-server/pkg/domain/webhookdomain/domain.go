/**
 * @Date: 2022/8/2
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package webhookdomain

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/bits/component_platform/server"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/backends/optimusserver"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/internal/httpreader"
	"code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/mysql/repository"

	"code.byted.org/devinfra/hagrid/libs/repo_upgrades"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	json "github.com/bytedance/sonic"
	"gorm.io/gorm"
)

type WebhookDomain struct {
	podUpgradeHistoryRepository *repository.PodUpgradeHistoryRepository
	dagParseRepository          *repository.DagParseRepository
	optimusApi                  optimusserver.Api
	httpReader                  *httpreader.HttpReader
}

func NewWebhookDomain(iosArchDb *gorm.DB, bitsDb *gorm.DB, optimusApi optimusserver.Api) *WebhookDomain {
	return &WebhookDomain{
		podUpgradeHistoryRepository: repository.NewPodUpgradeHistoryRepository(iosArchDb),
		dagParseRepository:          repository.NewDagParseRepository(bitsDb),
		optimusApi:                  optimusApi,
		httpReader:                  httpreader.New(),
	}
}

func (domain *WebhookDomain) ComponentUpgradeWorkflowWebhook(ctx context.Context, req *server.ComponentUpgradeWorkflowWebhookRequest) gresult.R[*base.EmptyResponse] {
	job := req.GetData().GetJob()
	if job.GetStatus() != "failed" {
		return gresult.OK(base.NewEmptyResponse())
	}
	if !(job.ErrorCode < 0 || job.ErrorCode == 1) {
		return gresult.OK(base.NewEmptyResponse())
	}

	var (
		env           = job.GetEnv()
		toslink       = env[repo_upgrades.BytebusInfoUrl]
		allComponents = NewComponentsFromEnv(ctx, env["components"])
	)

	componentUpgradeParam, err := domain.GetComponentUpgradeParams(ctx, toslink).Get()
	if err != nil {
		return gresult.OK(base.NewEmptyResponse())
	}

	components := make([]*optimusserver.Component, 0, len(componentUpgradeParam.ComponentList))
	for _, comp := range componentUpgradeParam.ComponentList {
		history, _ := domain.podUpgradeHistoryRepository.FindLastByRepoIdAndCommitIdAndBuildFromLike(ctx, comp.RepoId, comp.CommitId, componentUpgradeParam.Type).Get()
		log.V2.Info().With(ctx).Str("find upgrade history").KVs("repo id", comp.RepoId, "commit", comp.CommitId, "type", componentUpgradeParam.Type, "history", history).Emit()
		if history != nil &&
			gslice.Contains([]entity.BuildResult{entity.BuildResultSuccess, entity.BuildResultFailed}, history.BuildResult) &&
			history.WorkflowJobId == job.GetId() {
			log.V2.Info().With(ctx).Str("ignore this component").KVs("history", history, "job id", job.Id).Emit()
			continue
		}
		if history != nil {
			updated := &entity.PodUpgradeHistory{FinishTime: time.Now().Format(entity.FinishTimeLayout), ReasonCode: entity.ReasonCodeDagUpgradeCloudBuildAbnormal, BuildResult: entity.BuildResultFailed}
			if err := domain.podUpgradeHistoryRepository.UpdateById(ctx, history.Id, updated); err != nil {
				log.V2.Error().With(ctx).Str("failed to update pod upgrade history").Error(err).KVs("id", history.Id, "updated", updated).Emit()
			}
		}

		repo := &optimusserver.Component{
			Id:          comp.RepoId,
			Status:      optimusserver.ComponentStatusFailed,
			Message:     job.ErrorMessage,
			ProjectId:   allComponents[comp.RepoId].ProjectId,
			MrIid:       allComponents[comp.RepoId].MrIid,
			CommitId:    allComponents[comp.RepoId].CommitId,
			CheckJobUrl: job.JobUrl,
			Upgrade:     true,
			ExtraInfo:   allComponents[comp.RepoId].ExtraInfo,
			Version:     "",
		}
		components = append(components, repo)
	}
	autoPublishRequest := optimusserver.NewAndroidAutoPublishCallbackRequest(components, optimusserver.ComponentUpgradeType(componentUpgradeParam.Type), componentUpgradeParam.UserName)
	if err := domain.optimusApi.AndroidAutoPublishCallback(ctx, autoPublishRequest); err != nil {
		log.V2.Warn().With(ctx).Str("failed to invoke optimus to publish component").Error(err).KV("request", autoPublishRequest).Emit()
		return gresult.OK(base.NewEmptyResponse())
	}
	return gresult.OK(base.NewEmptyResponse())
}

func (domain *WebhookDomain) GetComponentUpgradeParams(ctx context.Context, toslink string) gresult.R[*ComponentUpgradeParams] {
	body, err := domain.httpReader.ReadBody(ctx, toslink).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to read tos content").Error(err).KV("tos link", toslink).Emit()
		return gresult.Err[*ComponentUpgradeParams](err)
	}

	component := new(ComponentUpgradeParams)
	if err := json.Unmarshal(body, component); err != nil {
		log.V2.Warn().With(ctx).Str("failed to unmarshal bytebusInfo").Error(err).Emit()
		return gresult.Err[*ComponentUpgradeParams](err)
	}
	return gresult.OK(component)
}
