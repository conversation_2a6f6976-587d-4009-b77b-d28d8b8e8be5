/**
 * @Date: 2023/8/15
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package iamdomain

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/bits/component_platform/server"
	"code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/redis"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
)

func (domain *IamDomain) IamSync(ctx context.Context, req *server.IamSyncRequest) gresult.R[*base.EmptyResponse] {
	if req.Data == nil || req.Data.Resource == nil {
		resp := &base.EmptyResponse{
			BaseResp: &base.BaseResp{StatusMessage: "empty data", StatusCode: int32(base.Code_OK)},
		}
		return gresult.OK(resp)
	}

	if !strings.HasPrefix(req.Data.Resource.Brn, "brn::bits:::appComponent:") {
		resp := &base.EmptyResponse{
			BaseResp: &base.BaseResp{StatusMessage: fmt.Sprintf("not component resource (%s)", req.Data.Resource.Brn), StatusCode: int32(base.Code_OK)},
		}
		return gresult.OK(resp)
	}

	lockName := fmt.Sprintf("component:lock:%s", req.Data.Resource.Brn)
	guard, got := redis.AcquireUntil(ctx, domain.rdb, lockName, 3*time.Second, 10*time.Second).Get()
	if !got {
		resp := &base.EmptyResponse{
			BaseResp: &base.BaseResp{StatusMessage: "not got lock, maybe redis has problem", StatusCode: int32(base.Code_OK)},
		}
		return gresult.OK(resp)
	}
	defer redis.Release(ctx, domain.rdb, lockName, guard)

	resourceId := strings.TrimPrefix(req.Data.Resource.Brn, "brn::bits:::appComponent:")
	repoId, _ := strconv.ParseInt(resourceId, 10, 64)
	if repoId == 0 {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "can't parse resource id", StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}

	var err error
	switch req.Type {
	case "grant_role_binding": // 授权, 也就是新增人员
		err = domain.GrantRole(ctx, repoId, req.Data.RoleName, req.Data.Principal)
	case "revoke_role_binding": // 撤销, 也就是移除人员
		err = domain.RevokeRole(ctx, repoId, req.Data.RoleName, req.Data.Principal)
	}

	resp := &base.EmptyResponse{
		BaseResp: &base.BaseResp{StatusMessage: fmt.Sprintf("%v", err), StatusCode: int32(base.Code_OK)},
	}
	return gresult.OK(resp)
}

func (domain *IamDomain) GrantRole(ctx context.Context, repoId int64, role string, principal *server.IamPrincipal) error {
	repo, err := domain.podMainRepository.FindLastByIdDirectly(ctx, repoId).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to find repo").Error(err).KV("repo", repoId).Emit()
		return fmt.Errorf("can't find repo(%d): %w", repoId, err)
	}

	updated := &entity.PodMain{}
	switch role {
	case "brn::iam:::role:bits/bytedance/bits.appComponentOwner":
		updated.PodOwner = emails.WithSuffix(principal.UserName)
	case "brn::iam:::role:bits/bytedance/bits.appComponentAdmin": // 管理员
		admins := append(repo.GetAdministrators(), principal.UserName)
		updated.SetAdministrators(admins)
	case "brn::iam:::role:bits/bytedance/bits.appComponentDeveloper": // 开发人员
		developers := append(repo.GetDevelopers(), principal.UserName)
		updated.SetDevelopers(developers)
	}
	if err := domain.podMainRepository.UpdateById(ctx, repoId, updated); err != nil {
		log.V2.Info().With(ctx).Str("failed to update repo").Error(err).KV("updated", updated).Emit()
		return fmt.Errorf("failed to update repo: %w", err)
	}
	return nil
}

func (domain *IamDomain) RevokeRole(ctx context.Context, repoId int64, role string, principal *server.IamPrincipal) error {
	repo, err := domain.podMainRepository.FindLastByIdDirectly(ctx, repoId).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to find repo").Error(err).KV("repo", repoId).Emit()
		return fmt.Errorf("can't find repo(%d): %w", repoId, err)
	}

	updated := &entity.PodMain{}
	switch role {
	case "brn::iam:::role:bits/bytedance/bits.appComponentAdmin": // 管理员
		admins := gslice.Remove(repo.GetAdministrators(), principal.UserName)
		updated.SetAdministrators(admins)
	case "brn::iam:::role:bits/bytedance/bits.appComponentDeveloper": // 开发人员
		developers := gslice.Remove(repo.GetDevelopers(), principal.UserName)
		updated.SetDevelopers(developers)
	}
	if err := domain.podMainRepository.UpdateById(ctx, repoId, updated); err != nil {
		log.V2.Info().With(ctx).Str("failed to update repo").Error(err).KV("updated", updated).Emit()
		return fmt.Errorf("failed to update repo: %w", err)
	}
	return nil
}
