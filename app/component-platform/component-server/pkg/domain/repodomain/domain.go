/**
 * @Date: 2023/4/4
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repodomain

import (
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/backends/gitserver"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/backends/luban"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/backends/meta"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/configcenter"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/internal/httpreader"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/service/reposervice"
	"code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/mysql/repository"
	"code.byted.org/devinfra/hagrid/libs/sdk/jwt"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz/authzservice"
	"code.byted.org/kv/goredis"
	"gorm.io/gorm"
)

type RepoDomain struct {
	httpReader                            *httpreader.HttpReader
	rdb                                   *goredis.Client
	podMainRepository                     *repository.PodMainRepository
	podUpgradeHistoryRepository           *repository.PodUpgradeHistoryRepository
	devTagRepository                      *repository.DevTagRepository
	tagComponentRelationRepository        *repository.TagComponentRelationRepository
	communityForumComment                 *repository.CommunityForumCommentRepository
	componentDependentRepository          *repository.ComponentDependentRepository
	appComponentLatestRelationRepository  *repository.AppComponentLatestRelationRepository
	appComponentHistoryRelationRepository *repository.AppComponentHistoryRelationRepository
	communityForumCollectionRepository    *repository.CommunityForumCollectionRepository
	appBaseConfigRepository               *repository.AppBaseConfigRepository
	lubanApi                              luban.Api
	jwtApi                                jwt.Api
	metaApi                               meta.Api
	gitApi                                gitserver.Api
	configCenter                          configcenter.Api
	repoService                           *reposervice.RepoService
	authzClient                           authzservice.Client
}

func NewRepoDomain(rdb *goredis.Client, iosArchDb, bitsDb *gorm.DB, lubanApi luban.Api, jwtApi jwt.Api, metaApi meta.Api, gitApi gitserver.Api, configCenter configcenter.Api, authzClient authzservice.Client) *RepoDomain {
	return &RepoDomain{
		httpReader:                            httpreader.New(),
		rdb:                                   rdb,
		podMainRepository:                     repository.NewPodMainRepository(iosArchDb),
		podUpgradeHistoryRepository:           repository.NewPodUpgradeHistoryRepository(iosArchDb),
		devTagRepository:                      repository.NewDevTagRepository(iosArchDb),
		tagComponentRelationRepository:        repository.NewTagComponentRelationRepository(iosArchDb),
		communityForumComment:                 repository.NewCommunityForumCommentRepository(iosArchDb),
		componentDependentRepository:          repository.NewComponentDependentRepository(iosArchDb),
		appComponentLatestRelationRepository:  repository.NewAppComponentLatestRelationRepository(bitsDb),
		appComponentHistoryRelationRepository: repository.NewAppComponentHistoryRelationRepository(bitsDb),
		communityForumCollectionRepository:    repository.NewCommunityForumCollectionRepository(iosArchDb),
		appBaseConfigRepository:               repository.NewAppBaseConfigRepository(bitsDb),
		lubanApi:                              lubanApi,
		jwtApi:                                jwtApi,
		metaApi:                               metaApi,
		gitApi:                                gitApi,
		configCenter:                          configCenter,
		repoService:                           reposervice.NewRepoService(iosArchDb, configCenter),
		authzClient:                           authzClient,
	}
}
