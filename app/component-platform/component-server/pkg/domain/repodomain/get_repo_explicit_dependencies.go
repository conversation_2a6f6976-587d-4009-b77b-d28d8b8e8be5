/**
 * @Date: 2023/11/24
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repodomain

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/kitex_gen/bits/component_platform/server"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/functools"
	"code.byted.org/devinfra/hagrid/app/component-platform/component-server/pkg/internal/httpreader"
	"code.byted.org/devinfra/hagrid/app/component-platform/dataaccesslayer/mysql/entity"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	json "github.com/bytedance/sonic"
)

func (domain *RepoDomain) GetRepoExplicitDependencies(ctx context.Context, req *server.GetRepoExplicitDependenciesRequest) gresult.R[*server.GetRepoExplicitDependenciesResponse] {
	repo, err := domain.podMainRepository.FindLastById(ctx, req.RepoId).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to find repo").Error(err).KV("repo", req.RepoId).Emit()
		resp := &server.GetRepoExplicitDependenciesResponse{BaseResp: &base.BaseResp{StatusMessage: "repo not found"}}
		return gresult.OK(resp)
	}
	supportAppTypes := []entity.PodMainAppType{entity.PodMainAppTypeAndroid, entity.PodMainAppTypeIOS, entity.PodMainAppTypeFlutter, entity.PodMainAppTypeHarmony}
	if !gslice.Contains(supportAppTypes, repo.GetAppType()) {
		resp := &server.GetRepoExplicitDependenciesResponse{BaseResp: &base.BaseResp{StatusMessage: "for now only support ios, android and flutter"}}
		return gresult.OK(resp)
	}

	var (
		httpReader = httpreader.New()
		url        string
		appId      = repo.AppID
	)
	switch repo.GetAppType() {
	case entity.PodMainAppTypeIOS:
		url = fmt.Sprintf("https://voffline.byted.org/obj/iosbinary/%s/%s/%s/dependency.json", functools.Take(appId, len(appId)-2), repo.RepoName, req.Version)
	case entity.PodMainAppTypeAndroid:
		url = fmt.Sprintf("https://voffline.byted.org/obj/toutiao.ios.arch/depend_a/%s/%s/%s/dependency.json", repo.RepoGroupName, repo.RepoName, req.Version)
	case entity.PodMainAppTypeFlutter:
		url = fmt.Sprintf("https://voffline.byted.org/obj/toutiao.ios.arch/%s/flutter_plugin_dependency/%s/dependency.json", repo.RepoName, req.Version)
	case entity.PodMainAppTypeHarmony:
		url = fmt.Sprintf("https://voffline.byted.org/obj/toutiao.ios.arch/depend_a/%s/%s/%s/dependency.json", repo.RepoGroupName, repo.RepoName, req.Version) // TODO
	}

	body, err := httpReader.ReadBody(ctx, url).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("failed to read tos content").Error(err).KV("url", url).Emit()
		resp := &server.GetRepoExplicitDependenciesResponse{BaseResp: &base.BaseResp{StatusMessage: "dependencies file not found"}}
		return gresult.OK(resp)
	}

	dependencies := new(DependenciesBody)
	if err := json.Unmarshal(body, dependencies); err != nil {
		log.V2.Error().With(ctx).Str("failed to parse dependencies").Error(err).Emit()
		resp := &server.GetRepoExplicitDependenciesResponse{BaseResp: &base.BaseResp{StatusMessage: "repo not found", StatusCode: int32(base.Code_INTERNAL)}}
		return gresult.OK(resp)
	}

	resp := &server.GetRepoExplicitDependenciesResponse{
		RepoDependencies: TransformExplicitDependenciesToRepoDependencies(dependencies.ContainSelf),
		BaseResp:         &base.BaseResp{StatusMessage: "ok"},
	}
	return gresult.OK(resp)
}

type DependenciesBody struct {
	ContainSelf *ExplicitDependencies `json:"contain_self"`
}

type ExplicitDependencies struct {
	ExplicitComponent map[string]Dependency `json:"explicit_component"`
	ImplicitComponent map[string]Dependency `json:"implicit_component"`
}

type Dependency struct {
	Version      string   `json:"version"`
	Dependencies []string `json:"dependencies,omitempty"`
}
