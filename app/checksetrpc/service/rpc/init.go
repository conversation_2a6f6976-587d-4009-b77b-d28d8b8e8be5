package rpc

import (
	"code.byted.org/devinfra/hagrid/app/checksetrpc/kitex_gen/bits/gatekeeper/process/gatekeeperservice"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cdpb/cdrpc"
	cdrpcpb "code.byted.org/devinfra/hagrid/pbgen/rpc/bits/cdrpc"
	"code.byted.org/kite/kitex/byted/transmeta"
	"code.byted.org/kite/kitex/client"
)

var (
	CDClient         cdrpc.Client
	GateKeeperClient gatekeeperservice.Client
)

func Init() {
	CDClient = cdrpcpb.MustNewClient()
	GateKeeperClient = gatekeeperservice.MustNewClient("bits.gatekeeper.process", newRPCOptions()...)

	transmeta.SetReadBizStatusErr(true)
}

func newRPCOptions() []client.Option {
	opts := []client.Option{
		client.WithMiddleware(kitexmw.LogClientSideRequestResponse),
	}
	return opts
}
