load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["base_mock.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/deps/gatekeeper/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//app/checksetrpc/kitex_gen/bits/gatekeeper/process",
        "//app/checksetrpc/pkg/deps/gatekeeper",
        "@com_github_golang_mock//gomock",
    ],
)
