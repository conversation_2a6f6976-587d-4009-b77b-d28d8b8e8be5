package check_set

import (
	"context"

	"golang.org/x/sync/errgroup"

	cdutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/app/checksetrpc/kitex_gen/bits/gatekeeper/process"
	"code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/projectpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/checksetpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/lang/gg/gslice"
)

func (c *checkSetSvc) terminateCheckSet(ctx context.Context, checkSet *entity.DBReleaseTicketCheckSet) (err error) {
	err = c.gateKeeperSvc.TerminateCheckPoint(ctx, checkSet.ReleaseTicketID, checkSet.StageID,
		process.BizScene(checkSet.BizScene), true)
	if err != nil {
		return err
	}

	return nil
}

func (c *checkSetSvc) TerminateCheckSetByChangeItem(ctx context.Context, req *checksetpb.TerminateCheckSetByChangeItemReq) (resp *checksetpb.TerminateCheckSetByChangeItemResp, err error) {
	err = c.ValidateTerminateCheckSetByChangeItem(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "ValidateTerminateCheckSetByChangeItem failed, err: %s", err.Error())
		return nil, err
	}

	checkSet, err := c.checkSetDao.GetByChangeItem(ctx, req.GetReleaseTicketId(), req.GetStageId(), uint64(req.GetBizScene()),
		req.GetProject().GetProjectType().String(), req.GetProject().GetProjectUniqueId(), req.GetProject().GetControlPlane().String())
	if err != nil {
		logs.CtxError(ctx, "GetByChangeItem failed, err: %s", err.Error())
		return nil, err
	}

	err = c.terminateCheckSet(ctx, checkSet)
	if err != nil {
		logs.CtxError(ctx, "terminateCheckSet failed, err: %s", err.Error())
		return nil, err
	}

	return &checksetpb.TerminateCheckSetByChangeItemResp{}, nil
}

func (c *checkSetSvc) TerminateCheckSetsByReleaseTicket(ctx context.Context, req *checksetpb.TerminateCheckSetsByReleaseTicketReq) (resp *checksetpb.TerminateCheckSetsByReleaseTicketResp, err error) {
	err = c.ValidateTerminateCheckSetsByReleaseTicket(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "ValidateTerminateCheckSetsByReleaseTicket failed, err: %s", err.Error())
		return nil, err
	}

	checkSets, err := c.checkSetDao.GetByReleaseTicket(ctx, req.GetReleaseTicketId())
	if err != nil {
		logs.CtxError(ctx, "GetByReleaseTicket failed, err: %s", err.Error())
		return nil, err
	}

	g := errgroup.Group{}
	for _, checkSet := range checkSets {
		checkSet_ := checkSet
		g.Go(func() error {
			asyncCtx := cdutils.GetAsyncContext(ctx)
			defer utils.PanicGuard(asyncCtx)

			innerErr := c.terminateCheckSet(asyncCtx, checkSet_)
			if innerErr != nil {
				logs.CtxError(asyncCtx, "terminateCheckSet failed, err: %s", innerErr.Error())
				return innerErr
			}
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		logs.CtxError(ctx, "TerminateCheckSetsByReleaseTicket failed, err: %s", err.Error())
		return nil, err
	}

	return &checksetpb.TerminateCheckSetsByReleaseTicketResp{}, nil
}

func (c *checkSetSvc) TerminateCheckSetsByStage(ctx context.Context, req *checksetpb.TerminateCheckSetsByStageReq) (resp *checksetpb.TerminateCheckSetsByStageResp, err error) {
	err = c.ValidateTerminateCheckSetsByStage(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "ValidateTerminateCheckSetsByStage failed, err: %s", err.Error())
		return nil, err
	}

	checkSets, err := c.checkSetDao.GetByStage(ctx, req.GetStageId(), uint64(req.GetBizScene()))
	if err != nil {
		logs.CtxError(ctx, "GetByStage failed, err: %s", err.Error())
		return nil, err
	}

	g := errgroup.Group{}
	for _, checkSet := range checkSets {
		if req.GetProjects() != nil && len(req.GetProjects()) > 0 {
			needTerminate := gslice.Any(req.GetProjects(), func(proj *projectpb.ProjectBasicInfoItem) bool {
				return proj.GetProjectUniqueId() == checkSet.ProjectUniqueID &&
					proj.GetProjectType().String() == checkSet.ProjectType &&
					proj.GetControlPlane().String() == checkSet.ControlPlane &&
					req.GetStageId() == checkSet.StageID &&
					uint64(req.GetBizScene().Number()) == checkSet.BizScene
			})

			if !needTerminate {
				continue
			}
		}

		checkSet_ := checkSet
		g.Go(func() error {
			asyncCtx := cdutils.GetAsyncContext(ctx)
			defer utils.PanicGuard(asyncCtx)

			innerErr := c.terminateCheckSet(asyncCtx, checkSet_)
			if innerErr != nil {
				logs.CtxError(asyncCtx, "terminateCheckSet failed, err: %s", innerErr.Error())
				return innerErr
			}

			return nil
		})
	}

	if err = g.Wait(); err != nil {
		logs.CtxError(ctx, "TerminateCheckSetsByStage failed, err: %s", err.Error())
		return nil, err
	}
	return &checksetpb.TerminateCheckSetsByStageResp{}, nil
}
