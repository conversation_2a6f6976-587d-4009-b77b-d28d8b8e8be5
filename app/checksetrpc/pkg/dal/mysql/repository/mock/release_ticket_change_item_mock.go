// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/dal/mysql/repository (interfaces: ReleaseTicketChangeItemDao)

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockReleaseTicketChangeItemDao is a mock of ReleaseTicketChangeItemDao interface.
type MockReleaseTicketChangeItemDao struct {
	ctrl     *gomock.Controller
	recorder *MockReleaseTicketChangeItemDaoMockRecorder
}

// MockReleaseTicketChangeItemDaoMockRecorder is the mock recorder for MockReleaseTicketChangeItemDao.
type MockReleaseTicketChangeItemDaoMockRecorder struct {
	mock *MockReleaseTicketChangeItemDao
}

// NewMockReleaseTicketChangeItemDao creates a new mock instance.
func NewMockReleaseTicketChangeItemDao(ctrl *gomock.Controller) *MockReleaseTicketChangeItemDao {
	mock := &MockReleaseTicketChangeItemDao{ctrl: ctrl}
	mock.recorder = &MockReleaseTicketChangeItemDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReleaseTicketChangeItemDao) EXPECT() *MockReleaseTicketChangeItemDaoMockRecorder {
	return m.recorder
}

// GetByReleaseTicketID mocks base method.
func (m *MockReleaseTicketChangeItemDao) GetByReleaseTicketID(arg0 context.Context, arg1 uint64) ([]*entity.DBReleaseTicketChangeItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByReleaseTicketID", arg0, arg1)
	ret0, _ := ret[0].([]*entity.DBReleaseTicketChangeItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByReleaseTicketID indicates an expected call of GetByReleaseTicketID.
func (mr *MockReleaseTicketChangeItemDaoMockRecorder) GetByReleaseTicketID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByReleaseTicketID", reflect.TypeOf((*MockReleaseTicketChangeItemDao)(nil).GetByReleaseTicketID), arg0, arg1)
}

// GetByReleaseTicketIDAndControlPlane mocks base method.
func (m *MockReleaseTicketChangeItemDao) GetByReleaseTicketIDAndControlPlane(arg0 context.Context, arg1 uint64, arg2 string) ([]*entity.DBReleaseTicketChangeItem, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByReleaseTicketIDAndControlPlane", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.DBReleaseTicketChangeItem)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByReleaseTicketIDAndControlPlane indicates an expected call of GetByReleaseTicketIDAndControlPlane.
func (mr *MockReleaseTicketChangeItemDaoMockRecorder) GetByReleaseTicketIDAndControlPlane(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByReleaseTicketIDAndControlPlane", reflect.TypeOf((*MockReleaseTicketChangeItemDao)(nil).GetByReleaseTicketIDAndControlPlane), arg0, arg1, arg2)
}
