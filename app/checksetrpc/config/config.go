package config

import (
	_ "embed"
	"fmt"
	"os"

	"code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/deps/idgen"
	"github.com/cloudwego/kitex/pkg/utils"
	"gopkg.in/yaml.v3"

	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"

	"code.byted.org/devinfra/hagrid/app/checksetrpc/pkg/dal/mysql"
)

var Conf *Config

const (
	ConfFileFmt = "%s.yaml"
)

type Config struct {
	ConfName string        `yaml:"confName"`
	Mysql    *mysql.Config `yaml:"mysql"`
	IdGen    *idgen.Config `yaml:"idGenerator"`
}

type Callback struct {
	Pipeline string `yaml:"pipeline"`
}

func MustInitializeConfig() *Config {
	Conf = new(Config)
	confPath, err := getConfPath()
	logs.Infof("get conf path is: %s", confPath)
	if err != nil {
		logs.Errorf("get conf path err: %v", err)
		return Conf
	}
	confBuf, err := os.ReadFile(confPath)
	if err := yaml.Unmarshal(confBuf, &Conf); err != nil {
		logs.Errorf("get conf data err: %v", err)
		return Conf
	}
	logs.Infof("get confConf %v", Conf)
	return Conf
}

func getConfPath() (string, error) {
	var confFileName string
	if env.IsProduct() || env.IsPPE() {
		confFileName = fmt.Sprintf(ConfFileFmt, "prod")
	} else if env.IsBoe() {
		confFileName = fmt.Sprintf(ConfFileFmt, "boe")
	} else {
		confFileName = fmt.Sprintf(ConfFileFmt, "local")
	}
	confDir := utils.GetConfDir()
	confPath := fmt.Sprintf("%s/%s", confDir, confFileName)
	if confPath == "" {
		return confPath, fmt.Errorf("no conf path")
	}
	return confPath, nil
}
