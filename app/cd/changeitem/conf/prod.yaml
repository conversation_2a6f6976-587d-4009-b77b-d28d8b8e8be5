confName: prod
mysql:
  db: canal_delivery
  psm: toutiao.mysql.canal_delivery
  conn_max_idle_second_r: 3000
  max_idle_conns_r: 300
  conn_max_idle_second_w: 300
  max_idle_conns_w: 100
DevOpsRunningDB:
  psm: toutiao.mysql.devops_running
  db: devops_running
idGenerator:
  namespace: bits_hagrid

ResourceApi:
  host: "https://bytecycle-api.bytedance.net"
  domain: "canal_resource_api"

OnesiteApi:
  host: "https://bits.bytedance.net"
  domain: "onesite_api"

EnvSvcHost: "http://boe-platform.bytedance.net"

GoofyHost: "https://deploy.bytedance.net"
Goofy:
  RowHost: "https://deploy.bytedance.net"
  TtpHost: "https://deploy-ttp-us.bytedance.net"

rmq:
  BitsIntegrationEvent:
    Topic: bits_integration_event
    ClusterName: web_common2
    ConsumerGroup: bits_integration_multi
    WorkerNum: 5
    Orderly: true
  ReleaseTicketPlan:
    Topic: bits_release_ticket_plan_trigger
    ClusterName: web_common2
    ConsumerGroup: bits_release_ticket_plan
    WorkerNum: 5
    Orderly: false
  PipelineEvent:
    Topic: bytecycle_instance_status_event
    ClusterName: web_common
    ConsumerGroup: bits_cd_rpc
    WorkerNum: 5
    Orderly: false

callback:
  pipeline: "/api/v1/cd/callback/pipeline_callback"

DeliveryApp:
  host: "https://bytecycle-api.bytedance.net"
  domain: "canal_delivery_app"

MarchApp:
  host: "https://march-api.cn.goofy.app"
  domain: "canal_march_server"

redis:
  psm: toutiao.redis.canal_delivery

codebase:
  webHost: "https://code.byted.org"

eventbus:
  GitlabWebHook:
    Event: bits_gitlab_webhook
    Group: bits.cd.rpc

Notifiction:
  ChatId: oc_c2de2d406e7cccd95e82b768e9d88eb5
  ByteflowHost: "https://cloud.bytedance.net"

elasticsearch:
  release_ticket:
    index: release_ticket
    psm: search.apigateway.proxy
    cluster: bits
MigrationWorkflowConfigHost: "https://3zs9fc00.fn.bytedance.net"
