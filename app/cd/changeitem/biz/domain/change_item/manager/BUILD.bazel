load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "manager",
    srcs = ["projetc_support.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/cd/changeitem/biz/domain/change_item/manager",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cd/changeitem/biz/rpc",
        "//idls/byted/devinfra/appcenter:appcenter_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "@org_byted_code_lang_gg//gslice",
    ],
)

go_test(
    name = "manager_test",
    srcs = ["go_test.go"],
    embed = [":manager"],
    deps = [
        "//app/cd/changeitem/biz/rpc",
        "//idls/byted/devinfra/appcenter:appcenter_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_smartystreets_goconvey//convey",
    ],
)
