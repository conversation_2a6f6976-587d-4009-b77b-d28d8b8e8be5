load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "fg",
    srcs = ["fg.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/cd/changeitem/biz/dal/fg",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cd/changeitem/biz/utils",
        "//app/cd/changeitem/services",
        "@org_byted_code_devinfra_bytegate_sdk//models",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
