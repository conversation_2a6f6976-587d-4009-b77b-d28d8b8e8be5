#!/usr/bin/env bash

if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
  bash ${BUILD_PATH}/scripts/pre-build.scm.sh
fi

RUN_NAME="bits.devops.workbench"

mkdir -p output/bin output/conf
cp script/* output/
cp conf/* output/conf/
chmod +x output/bootstrap.sh

go build -v -o output/bin/${RUN_NAME}

if [[ $OLD_PWD != "" ]]; then
  cp -r output $OLD_PWD
  bash ${BUILD_PATH}/scripts/post-build.scm.sh
fi
