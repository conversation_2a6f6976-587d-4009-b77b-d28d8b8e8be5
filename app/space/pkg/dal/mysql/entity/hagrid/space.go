package hagrid

import (
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/pkg/brn"
)

type SpaceType string

const SpaceTypeClient SpaceType = "client"
const SpaceTypeBackendFrontEnd SpaceType = "backend_frontend"
const SpaceTypeGeneral SpaceType = "general"

type SpaceOrigin int8

const (
	SpaceOrigin_OldSync SpaceOrigin = 0
	SpaceOrigin_OneSite SpaceOrigin = 1
)

type Space struct {
	ID            uint64      `json:"id" gorm:"column:id"` // id
	WorkSpaceID   int64       `json:"workspace_id" gorm:"column:workspace_id;primary_key"`
	Name          string      `json:"name" gorm:"column:name"`                 // 标识符
	Type          SpaceType   `json:"type" gorm:"column:type"`                 // 类型
	BytetreeID    int64       `json:"bytetree_id" gorm:"column:bytetree_id"`   // 字节云服务树
	AvatarUrl     string      `json:"avatar_url" gorm:"column:avatar_url"`     // 头像
	Description   string      `json:"description" gorm:"column:description"`   // 描述
	Creator       string      `json:"creator" gorm:"column:creator"`           // 创建者
	CreateAt      time.Time   `json:"create_at" gorm:"column:create_at"`       // 创建时间
	UpdateAt      time.Time   `json:"update_at" gorm:"column:update_at"`       // 更新时间
	MigrateTo     uint64      `json:"migrate_to" gorm:"column:migrate_to"`     // 迁移到的新空间
	Origin        SpaceOrigin `json:"origin" gorm:"column:origin"`             // 空间来源
	Strategy      int         `json:"strategy"  gorm:"column:strategy"`        // 灰度策略
	CloudAppID    uint64      `json:"cloud_app_id" gorm:"column:cloud_app_id"` // 抖音1128这种星图的app_id
	Extra         string      `json:"extra" gorm:"column:extra;default:{}"`    // 暂时无用
	OldPlatformId uint64      `json:"old_platform_id" gorm:"old_platform_id"`  // 老平台ID（按照类型获取bizID且不会同bizID一样一直增长，本字段后期为0，仅用于前端搜索与展示）
	TenantKey     string      `json:"tenant_key" gorm:"tenant_key"`            // 租户标识 - 目前租户标识维护在字节云
	IsInitialized bool        `json:"is_initialized" gorm:"is_initialized"`    // 是否初始化
	// @sunyu.7379 目前质量空间未定如何与其他空间之间融合的方案
}

func (m *Space) TableName() string {
	return "spaces"
}

func (m *Space) GetBRN() brn.BRN {
	return brn.GenResourceBRN("space", strconv.FormatUint(m.ID, 10))
}

type ClientSpaceExtra struct {
	TechnologyStack string `json:"technology_stack"`
	Region          string `json:"region"`
	AppId           int    `json:"app_id"`
}
