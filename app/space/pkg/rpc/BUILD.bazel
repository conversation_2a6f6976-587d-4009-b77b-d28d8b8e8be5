load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "rpc",
    srcs = [
        "ab.go",
        "authz.go",
        "cd_rpc.go",
        "config_service.go",
        "init.go",
        "translation.go",
        "varstore.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/space/pkg/rpc",
    visibility = ["//visibility:public"],
    deps = [
        "//app/space/kitex_gen/bytedance/bits/config_service",
        "//app/space/kitex_gen/bytedance/bits/config_service/configservice",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto_xrpc_and_kitex_VarStoreService",
        "//idls/byted/devinfra/authz:authz_go_proto",
        "//idls/byted/devinfra/authz:authz_go_proto_kitexcli_AuthzService",
        "//idls/byted/devinfra/authz:authz_go_proto_xrpc_and_kitex_AuthzService",
        "//idls/byted/devinfra/cd:cd_go_proto_xrpc_and_kitex_CDRPC",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/translation:translation_go_proto",
        "//idls/byted/devinfra/translation:translation_go_proto_xrpc_and_kitex_TranslationService",
        "//libs/common_lib/utils",
        "//libs/middleware/kitexmw",
        "//pkg/middlewares/kitexmw",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_lang_gg//gmap",
        "@org_byted_code_overpass_bytedance_bits_config_service//rpc/bytedance_bits_config_service",
        "@org_byted_code_overpass_data_abtest_vm_framed//rpc/data_abtest_vm_framed",
    ],
)
