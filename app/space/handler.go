package main

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/space/pkg/backends/bytest"
	"code.byted.org/devinfra/hagrid/app/space/pkg/handler"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pkg/jirasdk"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/restymw"
	"code.byted.org/gopkg/env"
	"code.byted.org/lang/gg/gutil"
	"github.com/go-resty/resty/v2"
	"google.golang.org/protobuf/types/known/emptypb"
)

type SpaceRPCAPIImpl struct {
	jira *handler.JiraHandler
}

var _ rpcpb.SpaceRPCAPI = &SpaceRPCAPIImpl{}

/*
每一个接口都有redis msgpack 5分钟缓存
*/
func NewSpaceRPCAPI(conf *Config) rpcpb.SpaceRPCAPI {
	jiraApi := jirasdk.New(gutil.IfThen(env.IsBoe(), "https://jira-boe.byted.org", "https://tte-jira.byteintl.net"), func(client *resty.Client) {
		client.SetPreRequestHook(restymw.SetPreRequestHook).OnAfterResponse(restymw.OnAfterResponse)
	})
	bytestApi := bytest.New(conf.Bytest.Domain)

	return &SpaceRPCAPIImpl{
		jira: handler.NewJiraHandler(jiraApi, bytestApi),
	}
}

// GetBizIDBySpaceID implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetBizIDBySpaceID(ctx context.Context, req *rpcpb.GetBizIDBySpaceIDReq) (resp *rpcpb.GetBizIDBySpaceIDResp, err error) {
	bizID, err := handler.GetBizIDBySpaceID(ctx, req.SpaceId, req.PluginId)
	return &rpcpb.GetBizIDBySpaceIDResp{BizId: bizID}, err
}

func (s *SpaceRPCAPIImpl) GetSpaceName(ctx context.Context, req *rpcpb.GetSpaceNameReq) (res *rpcpb.GetSpaceNameResp, err error) {
	return handler.GetSpaceName(ctx, req)
}

// GetSpaceInfoByID implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetSpaceInfoByID(ctx context.Context, req *rpcpb.GetSpaceDetailQuery) (resp *rpcpb.SpaceDetail, err error) {
	return handler.GetSpaceDetail(ctx, req.SpaceId, req.Username)
}

// GetEnablePluginList implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetEnablePluginList(ctx context.Context, req *rpcpb.SpaceIdQuery) (resp *rpcpb.GetEnablePluginListResp, err error) {
	list, err := handler.GetEnablePluginList(ctx, req.SpaceId)
	return &rpcpb.GetEnablePluginListResp{Plugins: list}, err
}

// GetSpaceList implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetSpaceList(ctx context.Context, _ *emptypb.Empty) (resp *rpcpb.GetSpaceListResp, err error) {
	spaceList, err := handler.GetSpaceList(ctx)
	return &rpcpb.GetSpaceListResp{Spaces: spaceList}, err
}

// GetSpaceListWithTypeAndOffset implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetSpaceListWithTypeAndOffset(ctx context.Context, req *rpcpb.GetSpaceListWithTypeAndOffsetReq) (resp *rpcpb.GetSpaceListWithTypeAndOffsetResp, err error) {
	spaceList, total, err := handler.GetSpaceListWithTypeAndOffset(ctx, req.Type, req.Page, req.PageSize)
	return &rpcpb.GetSpaceListWithTypeAndOffsetResp{Spaces: spaceList, Total: total}, err
}

func (s *SpaceRPCAPIImpl) GetSpaceListWithOffset(ctx context.Context, req *rpcpb.GetSpaceListWithOffsetReq) (res *rpcpb.GetSpaceListWithTypeAndOffsetResp, err error) {
	spaceList, total, err := handler.GetSpaceListWithOffset(ctx, req.Page, req.PageSize)
	return &rpcpb.GetSpaceListWithTypeAndOffsetResp{Spaces: spaceList, Total: total}, err
}

func (s *SpaceRPCAPIImpl) GetSpaceListWithPermission(ctx context.Context, req *rpcpb.GetAccessibleSpaceListReq) (resp *rpcpb.GetSpaceListWithPermissionResp, err error) {
	f := handler.GetSpaceListWithPermission // 有权限+没权限
	if req.AccessibleOnly {                 // 从前有一个功能叫 我有权限的应用，返回所有有权限的
		f = handler.GetAccessibleSpace
	}
	spaceList, total, err := f(ctx, req.Username, req.Page, req.PageSize)
	return &rpcpb.GetSpaceListWithPermissionResp{Spaces: spaceList, Total: total}, err

}

// CreateSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) CreateSpace(ctx context.Context, req *rpcpb.CreateSpaceReq) (resp *rpcpb.SpaceBrief, err error) {
	return handler.CreateSpace(ctx, req)
}

// UpdateSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) UpdateSpace(ctx context.Context, req *rpcpb.UpdateSpaceReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.UpdateSpace(ctx, req)
}

// GetFavoriteSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetFavoriteSpace(ctx context.Context, req *rpcpb.GetFavoriteSpaceReq) (resp *rpcpb.GetFavoriteSpaceResp, err error) {
	spaceList, err := handler.GetFavoriteSpace(ctx, req.Username)
	return &rpcpb.GetFavoriteSpaceResp{Spaces: spaceList}, err
}

// AddToFavorite implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) AddToFavorite(ctx context.Context, req *rpcpb.AddToFavoriteReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.AddToFavorite(ctx, req.Username, req.SpaceId)
}

// RemoveFromFavorite implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) RemoveFromFavorite(ctx context.Context, req *rpcpb.RemoveFromFavoriteReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.RemoveFromFavorite(ctx, req.Username, req.SpaceId)
}

// TestPlugin implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) TestPlugin(ctx context.Context, req *rpcpb.TestPluginReq) (resp *rpcpb.TestPluginResp, err error) {
	message := "ok"
	res, err := handler.TestPlugin(ctx, req.PluginId)
	if err != nil {
		message = err.Error()
	}
	return &rpcpb.TestPluginResp{Message: message, Result: res}, nil
}

// InitSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) InitSpace(ctx context.Context, req *rpcpb.InitSpaceReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.InitializeSpace(ctx, req.SpaceId, req.PluginId, req.Data)
}

// GetSpaceIDByBizIDAndPluginID implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetSpaceIDByBizIDAndPluginID(ctx context.Context, req *rpcpb.GetSpaceInfoByBizIDAndPluginIDReq) (resp *rpcpb.SpaceBrief, err error) {
	return handler.GetSpaceIDByBizIDAndPluginID(ctx, req.PluginId, req.BizId)
}

// CreateOrUpdateSpaceByPlugin implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) CreateOrUpdateSpaceByPlugin(ctx context.Context, req *rpcpb.CreateOrUpdateSpaceByPluginReq) (resp *rpcpb.CreateOrUpdateSpaceByPluginResp, err error) {
	return handler.CreateOrUpdateSpaceByPlugin(ctx, req)
}

func (s *SpaceRPCAPIImpl) ListMeegoSpaces(ctx context.Context, req *rpcpb.ListMeegoSpacesRequest) (*rpcpb.ListMeegoSpacesResponse, error) {
	return handler.MeegoHandler.ListMeegoSpaces(ctx, req).Get()
}

func (s *SpaceRPCAPIImpl) ListJiraSpaces(ctx context.Context, req *rpcpb.ListJiraSpacesRequest) (*rpcpb.ListJiraSpacesResponse, error) {
	return s.jira.ListJiraSpaces(ctx, req).Get()
}

func (s *SpaceRPCAPIImpl) GetMeegoSpaces(ctx context.Context, req *rpcpb.GetMeegoSpacesRequest) (*rpcpb.GetMeegoSpacesResponse, error) {
	return handler.MeegoHandler.GetMeegoSpaces(ctx, req).Get()
}

func (s *SpaceRPCAPIImpl) GetJiraSpaces(ctx context.Context, req *rpcpb.GetJiraSpacesRequest) (*rpcpb.GetJiraSpacesResponse, error) {
	return s.jira.GetJiraSpaces(ctx, req).Get()
}

// BatchGetSpaceInfo implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) BatchGetSpaceInfo(ctx context.Context, req *rpcpb.BatchGetSpaceInfoReq) (resp *rpcpb.GetSpaceListResp, err error) {
	space, err := handler.BatchGetSpaceInfo(ctx, req.Username, req.SpaceIds)
	return &rpcpb.GetSpaceListResp{Spaces: space}, err
}

// GetRecentlySpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) GetRecentlySpace(ctx context.Context, req *rpcpb.GetRecentlySpaceReq) (resp *rpcpb.GetRecentlySpaceResp, err error) {
	ids, err := handler.GetRecentlyAccessSpaceRecord(ctx, req.Username, req.Type)
	return &rpcpb.GetRecentlySpaceResp{SpaceIds: ids}, err
}

// NoticeRecentlySpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) NoticeRecentlySpace(ctx context.Context, req *rpcpb.NoticeRecentlySpaceReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.AddRecentlyAccessSpaceRecord(ctx, req.Username, req.SpaceId)
}

// EnablePluginForSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) EnablePluginForSpace(ctx context.Context, req *rpcpb.EnablePluginForSpaceReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.EnablePluginForSpace(ctx, req)
}

// MergeSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) MergeSpace(ctx context.Context, req *rpcpb.MergeSpaceReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.MergeSpace(ctx, req)
}

// SearchSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) SearchSpace(ctx context.Context, req *rpcpb.SearchSpaceReq) (resp *rpcpb.SearchSpaceResp, err error) {
	spaceList, total, err := handler.SearchSpace(ctx, req)
	return &rpcpb.SearchSpaceResp{Spaces: spaceList, Total: total}, err
}

// DeleteSpace implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) DeleteSpace(ctx context.Context, req *rpcpb.DeleteSpaceReq) (resp *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.DeleteSpace(ctx, req.SpaceId, req.Username, req.DeleteByPlugin, req.PluginId)
}

// CheckSpaceUsable implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) CheckSpaceUsable(ctx context.Context, req *rpcpb.CheckSpaceUsableReq) (res *rpcpb.CheckSpaceUsableResp, err error) {
	return handler.CheckSpaceUsable(ctx, req)
}

// ListSpaceBizInfos implements the SpaceRPCAPIImpl interface.
func (s *SpaceRPCAPIImpl) ListSpaceBizInfos(ctx context.Context, req *rpcpb.ListSpaceBizInfosReq) (resp *rpcpb.ListSpaceBizInfosResp, err error) {
	return handler.ListSpaceBizInfos(ctx, req)
}

func (s *SpaceRPCAPIImpl) StrategyDataCallback(ctx context.Context, req *rpcpb.StrategyDataCallbackReq) (resp *emptypb.Empty, err error) {
	return handler.StrategyDataCallback(ctx, req)
}

func (s *SpaceRPCAPIImpl) ListSpaceStories(ctx context.Context, req *rpcpb.ListSpaceStoriesRequest) (res *rpcpb.ListSpaceStoriesResponse, err error) {
	return handler.ListSpaceStories(ctx, req)
}

func (s *SpaceRPCAPIImpl) GetAccessibleSpaceListWithType(ctx context.Context, req *rpcpb.GetAccessibleSpaceListWithTypeReq) (res *rpcpb.GetAccessibleSpaceListWithTypeResp, err error) {
	return handler.GetAccessibleSpaceListWithType(ctx, req)
}

func (s *SpaceRPCAPIImpl) GetNewSpaceList(ctx context.Context, req *rpcpb.GetNewSpaceListReq) (res *rpcpb.GetNewSpaceListResp, err error) {
	return handler.GetNewSpaceList(ctx, req.OldSpaceId, req.Type)
}

func (s *SpaceRPCAPIImpl) MarkAsInit(ctx context.Context, req *rpcpb.MarkAsInitReq) (res *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.MarkAsInit(ctx, req.SpaceId)
}

func (s *SpaceRPCAPIImpl) ProhibitSpace(ctx context.Context, req *rpcpb.ProhibitSpaceReq) (res *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.ProhibitSpace(ctx, req)
}

func (s *SpaceRPCAPIImpl) CustomCloseBanner(ctx context.Context, req *rpcpb.CustomCloseBannerReq) (res *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.CustomHideBanner(ctx, req.Username, req.SpaceId, req.BannerName)
}

func (s *SpaceRPCAPIImpl) ListSpacesByByteTreeId(ctx context.Context, req *rpcpb.ListSpacesByByteTreeIdReq) (*rpcpb.ListSpacesByByteTreeIdResp, error) {
	return handler.ListSpacesByByteTreeId(ctx, req).Get()
}

func (s *SpaceRPCAPIImpl) BanSpace(ctx context.Context, req *rpcpb.BanSpaceReq) (res *emptypb.Empty, err error) {
	return &emptypb.Empty{}, handler.BanSpace(ctx, req)
}
