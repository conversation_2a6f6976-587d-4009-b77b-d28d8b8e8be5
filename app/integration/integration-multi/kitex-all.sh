#! /usr/bin/env bash

kitex --disable-self-update -module code.byted.org/devinfra/hagrid -service bits.integration.multi -thrift no_fmt ../../../idls/app/integration_multi.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/app/rd-process/dev/dev.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/infra/git_server/service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/infra/config_service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/app/gatekeeper.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/infra/meta/service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/app/message_center.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/infra/track/track_service.thrift
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/infra/ai.thrift
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/infra/push_service.thrift
kitex --disable-self-update -module code.byted.org/devinfra/hagrid -thrift no_fmt ../../../idls/app/cd_changeitem/service.thrift
