package mq

import (
	mq "code.byted.org/devinfra/hagrid/libs/connections/rocketmq-producer"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/gopkg/env"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/producer"
)

var (
	p                         producer.Producer
	coreEventProducer         *events.ProducerWrapper
	NotificationEventProducer *events.NotificationProducer
)

func InitMQ() {
	coreEventProducer = events.NewProducer()
	options := []mq.Option{
		mq.SetFastFailOver(false),
	}
	if env.IsBoe() {
		// local debug panic please check https://bytedance.feishu.cn/wiki/wikcnjc18vw5KcUjWGbvSu62PYg
		p = mq.MustConnectProducer(env.PSM(), "rmq_sandbox_ipv6", options...)
	} else {
		p = mq.MustConnectProducer(env.PSM(), "web_common2", options...)
	}

	NotificationEventProducer = events.NewNotificationProducer()
}
