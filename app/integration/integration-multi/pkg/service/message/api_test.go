package message

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bytedance/bits/message_center"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/rpc"
)

func TestNewMessageSvcAutoGen(t *testing.T) {
	// Verify that the result of NewMessageSvc is not nil. 验证NewMessageSvc的结果不为空
	t.Run("testNewMessageSvc_NonNilResult", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {

			// run target function and assert
			got1 := NewMessageSvc()
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestMessageSvc_sendAutoGen(t *testing.T) {
	// Verify the normal operation of the send function in MessageSvc. 验证MessageSvc中send函数的正常运行
	t.Run("testMessageSvc_send", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var marshalRet1Mock []byte
			var marshalRet2Mock error
			mockey.Mock(json.Marshal).Return(marshalRet1Mock, marshalRet2Mock).Build()

			var messageCenterClientMockImpl messageserviceClientImplForTestAutoGen
			messageCenterClientMock := &messageCenterClientMockImpl
			mockey.MockValue(&rpc.MessageCenterClient).To(messageCenterClientMock)

			var sendOneSiteMessageResponseMockPtrValue message_center.SendOneSiteMessageResponse
			sendOneSiteMessageResponseMock := &sendOneSiteMessageResponseMockPtrValue
			var sendOneSiteMessageRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.MessageCenterClient, "SendOneSiteMessage"), mockey.OptUnsafe).Return(sendOneSiteMessageResponseMock, sendOneSiteMessageRet2Mock).Build()

			// prepare parameters
			var receiverPtrValue MessageSvc
			receiver := &receiverPtrValue
			ctx := context.Background()
			var dataPtrValue sendRequest
			data := &dataPtrValue

			// run target function and assert
			got1 := receiver.send(ctx, data)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

