package integration

import (
	"context"
	"fmt"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bits/integration/multi"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bits/track"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/entity"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gconv"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
)

// GetSameChangeItemAndBranchInPlatform
// @title 找到平台里用了相同 change item + 分支的所有集成区
func (i *IntegrationSrv) GetSameChangeItemAndBranchInPlatform(changeItems []*entity.ChangeItem) {

}

// GetNotConsistentMainScm
// @title 判断两个 change item 是否有交集
func (i *IntegrationSrv) GetNotConsistentMainScm(targetChangeItems, currentChangeItems []*entity.ChangeItem) []*multi.MainScmDifference {
	res := make([]*multi.MainScmDifference, 0)
	getUniqueKey := func(item *entity.ChangeItem) string {
		return fmt.Sprintf("%v_%v_%v", item.ProjectType, item.ProjectUniqueID, item.ControlPlane)
	}
	targetM := gslice.ToMap(targetChangeItems, func(t *entity.ChangeItem) (string, *entity.ChangeItem) {
		return getUniqueKey(t), t
	})
	currentM := gslice.ToMap(currentChangeItems, func(t *entity.ChangeItem) (string, *entity.ChangeItem) {
		return getUniqueKey(t), t
	})
	for key, currentChangeItem := range currentM {
		if currentChangeItem.Content == nil {
			continue
		}
		targetChangeItem, ok := targetM[key]
		if !ok {
			continue
		}

		// main scm 目前仅一个
		currentMain, ok := gslice.Find(currentChangeItem.GetScmList(), func(info *dev.SCMInfo) bool { return info.GetIsMain() }).Get()
		if !ok {
			continue
		}
		targetMain, ok := gslice.Find(targetChangeItem.GetScmList(), func(info *dev.SCMInfo) bool { return info.GetIsMain() }).Get()
		if !ok {
			continue
		}

		if currentMain.Id == targetMain.Id {
			continue
		}
		res = append(res, &multi.MainScmDifference{
			ProjectUniqueId: currentChangeItem.ProjectUniqueID,
			ProjectType:     dev.ProjectType(currentChangeItem.ProjectType),
			ProjectName:     currentChangeItem.ProjectName,
			ControlPlane:    dev.ControlPlane(currentChangeItem.ControlPlane),
			ExistedScmRepo:  targetMain.GetName(),
			CurrentScmRepo:  currentMain.GetName(),
		})
	}
	gslice.UniqBy(res, func(t *multi.MainScmDifference) string {
		return fmt.Sprintf("%v_%v_%v",
			t.ProjectType,
			t.ProjectUniqueId,
			t.ControlPlane)
	})
	return res
}

func (i *IntegrationSrv) GetAllChangeItemByDevTaskId(ctx context.Context, devTaskId, integrationId int64) gresult.R[[]*entity.ChangeItem] {
	res, err := i.ChangeItemComponent.FindByIntegrationId(ctx, integrationId).Get()
	if err != nil {
		logs.V2.Error().With(ctx).KV(track.BusinessType_Integration, integrationId).KV("error", bits_err.INTEGRATION_MULTI.ErrSetBranchUsed).Error(err).Emit()
		return gresult.Err[[]*entity.ChangeItem](err)
	}

	return gresult.OK(gslice.Filter(res, func(item *entity.ChangeItem) bool {
		return gslice.Contains(item.DevIds, jsons.Stringify(devTaskId))
	}))
}

// FilterChangeItemNotUse
// @title 过滤掉实际没有使用到 change item
// @description 代码撤销、项目撤销都会 revert 掉对应的代码，所以需要把实际没有使用的 change item 过滤掉
func (i *IntegrationSrv) FilterChangeItemNotUse(ctx context.Context, changeItems []*entity.ChangeItem) []*entity.ChangeItem {
	// 找到所有的 revert 开发任务 id
	reveredDevTaskList := make([]int64, 0)
	gslice.ForEach(changeItems, func(item *entity.ChangeItem) {
		revertInfo := item.RevertInfo
		reveredDevTaskList = append(reveredDevTaskList, gslice.Map(gmap.Keys(revertInfo), func(v string) int64 {
			return gconv.To[int64](v)
		})...)
	})

	// 找到对应未被 revert 完全覆盖的 changeItem
	return gslice.Filter(changeItems, func(item *entity.ChangeItem) bool {
		return !gslice.ContainsAll(reveredDevTaskList, item.GetDevIds()...)
	})
}
