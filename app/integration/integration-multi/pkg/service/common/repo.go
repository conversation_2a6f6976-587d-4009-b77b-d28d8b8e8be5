package common

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bits/integration/multi"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/dal/rds/entity"
	"code.byted.org/devinfra/hagrid/app/integration/integration-multi/pkg/model"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
)

func GetIntegrationArchiveBranchInfo(ctx context.Context, repoIds []int64, repoIdMap map[int64]*entity.ChangeItem, archiveBranch string) (integrationBranchMap, archiveBranchMap map[int64]*multi.BranchInfo, err error) {
	integrationBranchMap, err = getReposBranchInfo(ctx, slicex.Map(repoIds, func(repoId int64) *git_server.ProjectBranch {
		return &git_server.ProjectBranch{
			ProjectId: repoId,
			Branch:    repoIdMap[repoId].Branch,
		}
	}))
	if err != nil {
		return
	}

	archiveBranchMap, err = getReposBranchInfo(ctx, slicex.Map(repoIds, func(repoId int64) *git_server.ProjectBranch {
		return &git_server.ProjectBranch{
			ProjectId: repoId,
			Branch:    archiveBranch,
		}
	}))
	if err != nil {
		return
	}

	return
}

func getReposBranchInfo(ctx context.Context, projectBranchs []*git_server.ProjectBranch) (BranchInfo map[int64]*multi.BranchInfo, err error) {
	BranchInfo = make(map[int64]*multi.BranchInfo)
	projectBranchMap, err := gitServer.GetBranchInfos(ctx, projectBranchs)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	for repoId, item := range projectBranchMap {
		BranchInfo[repoId] = &multi.BranchInfo{
			Name:              item.Name,
			NewestCommitHash_: item.GetCommit().GetId(),
			NewestCommitMsg_:  item.GetCommit().GetMessage(),
		}
	}
	return
}

func ProtectBranchByConfig(ctx context.Context, releaseTicketId, codebaseRepoId int64, workBranch string) {
	rt, err := releaseTicketRpc.GetReleaseTicket(ctx, releaseTicketId)
	if err != nil {
		logs.CtxError(ctx, "[TryProtectBranch] get rt failed,err:%s", err.Error())
	}
	protectRule := &branching_model_configpb.ProtectRules{
		Enabled:      true,
		WhoCanPush:   branching_model_configpb.ProtectLevel_PROTECT_LEVEL_MAINTAINER_PERMISSIONS,
		WhoCanMerge:  branching_model_configpb.ProtectLevel_PROTECT_LEVEL_DEVELOPER_PERMISSIONS,
		WhoCanBypass: branching_model_configpb.ProtectLevel_PROTECT_LEVEL_DEVELOPER_PERMISSIONS,
	}
	if rt != nil && rt.GetWorkflowConfig() != nil && rt.GetWorkflowConfig().GetReleaseTicketConfig() != nil && rt.GetWorkflowConfig().GetReleaseTicketConfig().GetBranchingModelConfig() != nil &&
		rt.GetWorkflowConfig().GetReleaseTicketConfig().GetBranchingModelConfig().GetIntegrationBranch() != nil &&
		rt.GetWorkflowConfig().GetReleaseTicketConfig().GetBranchingModelConfig().GetIntegrationBranch().GetProtectRules() != nil {
		logs.CtxInfo(ctx, "[TryProtectBranch] get protect rule from config")
		protectRule = rt.GetWorkflowConfig().GetReleaseTicketConfig().GetBranchingModelConfig().GetIntegrationBranch().GetProtectRules()
	}
	if !protectRule.GetEnabled() {
		logs.CtxInfo(ctx, "[TryProtectBranch] protect rule not enabled")
		return
	}
	req := gitServer.GetProtectDefaultReq(codebaseRepoId, workBranch)
	req.PushAccess = model.MapProtectLevelV2(protectRule.WhoCanPush)
	req.MergeAccess = model.MapProtectLevelV2(protectRule.WhoCanMerge)
	req.BypassAccess = model.MapProtectLevelV2(protectRule.WhoCanBypass)

	err = gitServer.AddProtectBranches(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "[TryProtectBranch] add protect branch err:%s", err.Error())
	}
}
