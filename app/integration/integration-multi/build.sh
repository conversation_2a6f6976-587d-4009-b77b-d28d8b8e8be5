#!/usr/bin/env bash
RUN_NAME="bits.integration.multi"

mkdir -p output

if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
  bash ${BUILD_PATH}/scripts/pre-build.scm.sh
fi

mkdir -p output/bin output/conf
cp script/* output/
cp conf/* output/conf/
chmod +x output/bootstrap.sh

go build -v -o output/bin/${RUN_NAME}

if [[ $OLD_PWD != "" ]]; then
  cp -r output/* $OLD_PWD/output/
  bash ${BUILD_PATH}/scripts/post-build.scm.sh
fi
