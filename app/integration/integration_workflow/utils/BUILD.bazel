load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "http.go",
        "lark_bot.go",
        "lark_message.go",
        "meego.go",
        "optimus.go",
        "tools.go",
        "version.go",
        "webhook.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration_workflow/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration_workflow/config",
        "//app/integration/integration_workflow/dal",
        "//app/integration/integration_workflow/dal/model/cony_table",
        "//libs/common_lib/utils",
        "//libs/thirdparty-sdks/lark",
        "//libs/thirdparty-sdks/meego",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_go_resty_resty_v2//:resty",
        "@com_github_larksuite_oapi_sdk_go_v3//:oapi-sdk-go",
        "@com_github_larksuite_oapi_sdk_go_v3//service/im/v1:im",
        "@org_byted_code_gopkg_logid//:logid",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
