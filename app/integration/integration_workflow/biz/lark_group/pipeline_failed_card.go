package lark_group

import (
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/utils"
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"

	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"
	larkcontact "github.com/larksuite/oapi-sdk-go/v3/service/contact/v3"
)

type messageCardReq struct {
	Content string `json:"content"`
}

type messageCardResp struct {
	Code    int `json:"code"`
	Content struct {
		Card any `json:"card"`
	} `json:"content"`
}

func BuildPipelineFailedCard(ctx context.Context, pipelineName, groupName, version, mrTitle, author string, mrID, pipelineID int64) (string, error) {
	versionInfo, err := rpc.IntegrationClient.GetIntegrationAPI(ctx, &integration.GetIntegrationAPIRequest{Version: version, GroupName: &groupName})
	if err != nil {
		return "", err
	}
	mrURL := fmt.Sprintf("https://bits.bytedance.net/bytebus/devops/code/detail/%v", mrID)
	pipelineURl := fmt.Sprintf("https://bits.bytedance.net/bytebus/devops/code/pipeline?pipeline_id=%v", pipelineID)
	userInfo, err := utils.BitsBot.Contact.User.BatchGetId(ctx, larkcontact.NewBatchGetIdUserReqBuilder().UserIdType(larkcontact.UserIdTypeOpenId).Body(larkcontact.NewBatchGetIdUserReqBodyBuilder().Emails([]string{emails.WithSuffix(author)}).Build()).Build())
	if err != nil {
		return "", err
	}
	userID := ""
	if len(userInfo.Data.UserList) != 0 {
		if userInfo.Data.UserList[0].UserId != nil {
			userID = *userInfo.Data.UserList[0].UserId
		}
	}
	textMessage := fmt.Sprintf("#red 合入后打包失败\npipeline名称:[%v](%v) "+"\\n\nMR标题:[%v](%v) \\n\n版本信息: [%v](%v) \\n\n提交人: @%v \\n\n!b[前往pipeline](%v) !b[前往mr](%v) !b[前往版本](%v)", pipelineName, pipelineURl, mrTitle, mrURL, version, versionInfo.Info.DetailURL, userID, pipelineURl, mrURL, versionInfo.Info.DetailURL)
	logs.CtxInfo(ctx, "GenerateMessageCard textMessage:%v", textMessage)
	req := messageCardReq{
		Content: textMessage,
	}
	respBody := messageCardResp{}
	_, err = utils.HttpClient.R().SetContext(ctx).SetBody(req).SetResult(&respBody).Post("https://bot.apps.bytedance.net/jinshu/msg")
	if err != nil {
		logs.CtxError(ctx, "GenerateMessageCard utils.HttpClient.Post err:%v", err)
		return "", err
	}
	content, err := json.Marshal(respBody.Content.Card)
	if err != nil {
		logs.CtxError(ctx, "GenerateMessageCard json.Marshal err:%v", err)
		return "", err
	}
	logs.CtxInfo(ctx, "GenerateMessageCard content:%v", string(content))
	return string(content), nil
}
