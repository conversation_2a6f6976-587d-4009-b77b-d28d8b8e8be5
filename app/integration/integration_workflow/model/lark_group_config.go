package model

type Config struct {
	VersionsVersionChatGroup *VersionChatGroup `json:"versions_version_chat_group"`
}

type VersionChatGroup struct {
	OnFrozenRoles        []string `json:"on_frozen_roles"`
	VersionGroupMode     string   `json:"version_group_mode"`
	EnableOnBoardGroup   bool     `json:"enable_on_board_group"`
	OnFrozenUserEmails   []string `json:"on_frozen_user_emails"`
	OnFrozenBots         []string `json:"on_frozen_bots"`
	DeveloperGroupNumber *string  `json:"developer_group_number"`
	FixedGroupNumber     *string  `json:"fixed_group_number"`
	CreateGroupType      string   `json:"create_group_type"` // just for onboard group
	CreateGroupTime      string   `json:"create_group_time"` // "01:02"
	GroupName            *string  `json:"on_frozen_group_name"`
	OnBoardGroupUsers    []string `json:"on_board_group_users"`
	OnBoardBots          []string `json:"on_board_bots"`
}

const (
	CreateGroupOnFrozenStart = "beginning"
	CreateGroupCustom        = "time"
)
