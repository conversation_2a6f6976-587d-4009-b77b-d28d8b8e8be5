package pipeline

import (
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/biz/pipeline_business"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/dal"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/dal/model/bits_integration_table"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/kitex_gen/bytedance/bits/integration_workflow"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/model"
	"code.byted.org/devinfra/hagrid/app/integration/integration_workflow/tcc"
	eventbus "code.byted.org/eventbus/client-go"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bytedance_bits_config_service/rpc/bytedance_bits_config_service"
	"code.byted.org/overpass/bytedance_bits_meta/kitex_gen/bytedance/bits/meta"
	"code.byted.org/overpass/bytedance_bits_meta/rpc/bytedance_bits_meta"
	"code.byted.org/overpass/bytedance_bits_optimus/rpc/bytedance_bits_optimus"
	"code.byted.org/overpass/bytedance_bits_workflow/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/overpass/bytedance_bits_workflow/rpc/bytedance_bits_workflow"
	"context"
	"errors"
	"fmt"
	json "github.com/bytedance/sonic"
	"gorm.io/gorm"
	"regexp"
	"strconv"
	"time"
)

type MergedMR struct {
	Username  string `json:"username"`
	Timestamp int64  `json:"timestamp"`
	MrId      int    `json:"mr_id"`
	State     bool   `json:"state"`
	GroupName string `json:"group_name"`
}

func ManualTriggerPipeline(ctx context.Context, req *integration_workflow.TriggerPipelineRequest) error {
	mergedMR := MergedMR{
		MrId:      int(req.MrID),
		GroupName: req.GroupName,
	}
	forceTrigger := false
	if req.ForceBuild != nil {
		forceTrigger = *req.ForceBuild
	}
	return TriggerPipeline(ctx, mergedMR, forceTrigger, req.BuildCommit, req.BuildBranch)
}

func EventTriggerPipeline(ctx context.Context, event *eventbus.ConsumerEvent) error {
	mergedMR := MergedMR{}
	err := json.Unmarshal(event.Value, &mergedMR)
	if err != nil {
		return err
	}
	time.Sleep(time.Second * 10)
	return TriggerPipeline(ctx, mergedMR, false, nil, nil)
}

func TriggerPipeline(ctx context.Context, mergedMR MergedMR, forceTrigger bool, buildCommit *string, buildBranch *string) error {
	commonConfig, err := model.GetCommonConfig(ctx, mergedMR.GroupName)
	if err != nil {
		return err
	}
	if len(commonConfig.BuildConfig) == 0 {
		pipelineTriggerConfig := dal.BitsIntegrationDB.PipelineTriggerConfig
		configs, err := pipelineTriggerConfig.WithContext(ctx).Where(pipelineTriggerConfig.GroupName.Eq(mergedMR.GroupName)).Find()
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				return nil
			}
			logs.CtxError(ctx, "pipeline trigger error:%+v", err)
			return err
		}
		mrInfo, err := bytedance_bits_optimus.GetMainMrInfo(ctx, int64(mergedMR.MrId))
		if err != nil {
			logs.CtxError(ctx, "pipeline trigger error:%+v", err)
			return err
		}
		if mrInfo == nil {
			return nil
		}
		if mrInfo.Info.Url == nil || mrInfo.Info.MergedCommitID == nil {
			logs.CtxError(ctx, "pipeline trigger error: merge commit is nil")
			return nil
		}
		groupInfo, err := bytedance_bits_meta.RawCall.QueryAppInfoByAppName(ctx, &meta.QueryAppInfoByAppNameRequest{EnName: &mrInfo.Info.GroupName})
		if err != nil {
			logs.CtxError(ctx, "pipeline trigger error:%+v", err)
			return err
		}
		if len(groupInfo.AppInfoList) == 0 {
			return errors.New("AppID not found")
		}
		repoConfig, err := tcc.GetMainRepoConfig(ctx)
		if err != nil {
			logs.CtxError(ctx, "pipeline trigger error:%+v", err)
			return err
		}
		if repoIDs, ok := repoConfig[mergedMR.GroupName]; ok {
			if mrInfo.Info.CodebaseRepoID == nil {
				return nil
			}
			repoList := make(map[int64]int64)
			for _, repo := range repoIDs {
				repoList[repo] = repo
			}
			if _, ok := repoList[*mrInfo.Info.CodebaseRepoID]; !ok {
				return nil
			}
		}
		appID := groupInfo.AppInfoList[0].Id
		for _, config := range configs {
			if config.IsActive || forceTrigger {
				branches, err := pipeline_business.BuildBranch(mrInfo.Info.ProductVersion, []byte(config.TargetBranch))
				if err != nil {
					return err
				}
				for _, branch := range branches {
					if branch == mrInfo.Info.ToBranch {
						triggerCommit := *mrInfo.Info.MergedCommitID
						if buildCommit != nil {
							triggerCommit = *buildCommit
						}
						triggerBranch := mrInfo.Info.ToBranch
						if buildBranch != nil {
							triggerBranch = *buildBranch
						}
						urlRegexp := regexp.MustCompile("(https://)(.*)(/)(.*)(/)(.*)(/merge_requests/.*)")
						mrUrlMatch := urlRegexp.FindStringSubmatch(*mrInfo.Info.Url)
						gitlabUrl := ""
						if len(mrUrlMatch) == 8 {
							gitlabUrl = fmt.Sprintf("git@%v:%v/%v.git", mrUrlMatch[2], mrUrlMatch[4], mrUrlMatch[6])
						}
						logs.CtxInfo(ctx, "Trigger pipeline %+v , commit %+v", config.PipelineTemplate, triggerCommit)
						res, err := bytedance_bits_workflow.TriggerTemplatePipeline(ctx, &workflow.TriggerTemplatePipelineInfo{
							TemplateId:    int64(config.PipelineTemplate),
							TriggerType:   "template_custom",
							Operator:      "bits",
							ProjectId:     mrInfo.Info.ProjectID,
							ProjectURL:    gitlabUrl,
							ProjectBranch: triggerBranch,
							ProjectCommit: &triggerCommit,
							Env:           pipeline_business.BuildENV(mrInfo.Info, config.PipelineEnv),
							AppId:         appID,
						})
						if err != nil {
							return err
						}
						pipelineTriggerResult := dal.BitsIntegrationDB.PipelineResult
						result := bits_integration_table.PipelineResult{
							MrID:               uint64(mrInfo.Info.ID),
							CommitID:           *mrInfo.Info.MergedCommitID,
							BuildCommit:        triggerCommit,
							PipelineTemplateID: uint64(int64(config.PipelineTemplate)),
							PipelineID:         uint64(res.PipelineId),
							Status:             res.Pipeline.Status,
							PipelineName:       config.PipelineName,
							CreateTime:         time.Now(),
						}
						err = pipelineTriggerResult.WithContext(ctx).Create(&result)
						if err != nil {
							return err
						}
					}
				}
			}
		}
	} else {
		mrInfo, err := bytedance_bits_optimus.GetMainMrInfo(ctx, int64(mergedMR.MrId))
		if err != nil {
			logs.CtxError(ctx, "pipeline trigger error:%+v", err)
			return err
		}
		if mrInfo == nil {
			return nil
		}
		if mrInfo.Info.Url == nil || mrInfo.Info.MergedCommitID == nil {
			return nil
		}
		groupInfo, err := bytedance_bits_meta.RawCall.QueryAppInfoByAppName(ctx, &meta.QueryAppInfoByAppNameRequest{EnName: &mrInfo.Info.GroupName})
		if err != nil {
			logs.CtxError(ctx, "pipeline trigger error:%+v", err)
			return err
		}
		if len(groupInfo.AppInfoList) == 0 {
			return errors.New("AppID not found")
		}
		appID := groupInfo.AppInfoList[0].Id
		mainConfig, err := bytedance_bits_config_service.GetOptimusServerConfigMain(ctx, mergedMR.GroupName)
		if err != nil {
			return err
		}
		if mainConfig.ConfigMain == nil {
			return nil
		}
		if mainConfig.ConfigMain.ConfigGroup == nil {
			return nil
		}
		mainRepo, err := strconv.ParseInt(mainConfig.ConfigMain.ConfigGroup.RootProjectID, 10, 64)
		if err != nil {
			return err
		}
		if mrInfo.Info.ProjectID != mainRepo {
			return nil
		}
		for _, pipelineConfig := range commonConfig.BuildConfig {
			if !pipelineConfig.IsActive && !forceTrigger {
				continue
			}
			branches := pipeline_business.BuildTargetBranch(mrInfo.Info.ProductVersion, pipelineConfig.TargetBranch)
			for _, branch := range branches {
				if branch == mrInfo.Info.ToBranch {
					triggerCommit := *mrInfo.Info.MergedCommitID
					if buildCommit != nil {
						triggerCommit = *buildCommit
					}
					triggerBranch := mrInfo.Info.ToBranch
					if buildBranch != nil {
						triggerBranch = *buildBranch
					}
					urlRegexp := regexp.MustCompile("(https://)(.*)(/)(.*)(/)(.*)(/merge_requests/.*)")
					mrUrlMatch := urlRegexp.FindStringSubmatch(*mrInfo.Info.Url)
					gitlabUrl := ""
					if len(mrUrlMatch) == 8 {
						gitlabUrl = fmt.Sprintf("git@%v:%v/%v.git", mrUrlMatch[2], mrUrlMatch[4], mrUrlMatch[6])
					}
					logs.CtxInfo(ctx, "Trigger pipeline %+v , commit %+v", pipelineConfig.PipelineId, triggerCommit)
					triggerReq := workflow.TriggerTemplatePipelineInfo{
						TemplateId:    int64(pipelineConfig.PipelineId),
						TriggerType:   "template_custom",
						Operator:      "bits",
						ProjectId:     mrInfo.Info.ProjectID,
						ProjectURL:    gitlabUrl,
						ProjectBranch: triggerBranch,
						ProjectCommit: &triggerCommit,
						Env:           pipeline_business.BuildPipelineENV(mrInfo.Info, pipelineConfig.PipelineParams),
						AppId:         appID,
						CallbackUrls:  pipelineConfig.CallBackURL,
					}
					if mrInfo.Info.GroupName == "Aweme-Android" {
						triggerReq.ProjectURL = ""
					}
					res, err := bytedance_bits_workflow.TriggerTemplatePipeline(ctx, &triggerReq)
					if err != nil {
						logs.CtxError(ctx, "Trigger pipeline error:%+v", err)
						continue
					}
					pipelineTriggerResult := dal.BitsIntegrationDB.PipelineResult
					result := bits_integration_table.PipelineResult{
						MrID:               uint64(mrInfo.Info.ID),
						CommitID:           *mrInfo.Info.MergedCommitID,
						BuildCommit:        triggerCommit,
						PipelineTemplateID: uint64(int64(pipelineConfig.PipelineId)),
						PipelineID:         uint64(res.PipelineId),
						Status:             res.Pipeline.Status,
						PipelineName:       pipelineConfig.Name,
						CreateTime:         time.Now(),
					}
					err = pipelineTriggerResult.WithContext(ctx).Create(&result)
					if err != nil {
						logs.CtxError(ctx, "Save pipeline record error:%+v", err)
						continue
					}
				}
			}
		}
	}
	return nil
}
