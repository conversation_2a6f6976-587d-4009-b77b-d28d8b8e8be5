#!/usr/bin/env bash

mkdir -p output
if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
  bash ${BUILD_PATH}/scripts/pre-build.scm.sh
fi

RUN_NAME="bytedance.bits.integration"

mkdir -p output/bin output/conf
cp script/bootstrap.sh script/settings.py  output
chmod +x output/bootstrap.sh
cp conf/kitex.yml output/conf

go build -o output/bin/${RUN_NAME}

if [[ $OLD_PWD != "" ]]; then
  cp -r output/* $OLD_PWD/output/
fi
