package version

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/feature"
	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/consts"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/model"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/tcc"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/utils"
	CommonUtils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/mtctx"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/tenancies"
	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/facility/sorts"
	"code.byted.org/gopkg/logs"
	json "github.com/bytedance/sonic"
	"github.com/pkg/errors"
)

func init() {
	meegoStoryUrlReg = regexp.MustCompile(`https?:\/\/meego\.bytedance\.net\/([\da-z\.-]+)\/story\/detail\/(\d+)`)
}

// todo 用时间戳调用

func FetchVersionMetasV2(ctx context.Context, groupName string, version *string, reportConfig *model.VersionsReport, startTime, endTime *int64) (*BytedanceBitsIntegration.FetchVersionMetasResponse, error) {
	// 获取项目的 developBranch 和 releaseBranch

	// 1. fetch config main
	t := time.Now()
	queryParams := map[string]string{
		"group_name": groupName,
	}

	optimusConfigMainResp := OptimusConfigMain{}
	resp, err := CommonUtils.DoGetWithQueryParams(ctx, &optimusConfigMainResp, queryParams, consts.OptimusConfigMainURL, utils.OptimusHttpClient)
	if resp == nil || err != nil {
		CommonUtils.LogCtxWarn(ctx, "[FetchVersionMetas]get optimusConfigMainResp failed. resp: %v err:%v", string(resp.Body()), err)
		return nil, err
	}
	if groupName == "Tiktok-UltraLite" {
		optimusConfigMainResp.Data.ConySettings.DevelopBranch = "latest_but_unstable/dont_merge_unless_necessary"
		optimusConfigMainResp.Data.ConySettings.GreyBranch = "lite/alpha/v<sv>"
	}
	configMainElapsed := time.Since(t)

	// 2. fetch config project（要先以 project 为准）
	t = time.Now()
	optimusProjectConfigsResp := OptimusProjectConfigs{}
	queryParams = map[string]string{}
	gitlabProjectIDStr := optimusConfigMainResp.Data.RootProjectID
	if gitlabProjectIDStr == "" {
		return nil, errors.New("[FetchVersionMetas]empty gitlabProjectIDStr")
	}
	resp, err = CommonUtils.DoGetWithQueryParams(ctx, &optimusProjectConfigsResp, queryParams, consts.OptimusConfigProjectsURL+"/"+gitlabProjectIDStr, utils.OptimusHttpClient)
	if resp == nil || err != nil {
		CommonUtils.LogCtxWarn(ctx, "[FetchVersionMetas]get optimus project configs failed. resp: %v err:%v", string(resp.Body()), err)
		return nil, err
	}
	if groupName == "Tiktok-UltraLite" {
		optimusProjectConfigsResp.Data.ConySettings.DevelopBranch = "latest_but_unstable/dont_merge_unless_necessary"
		optimusProjectConfigsResp.Data.ConySettings.GreyBranch = "lite/alpha/v<sv>"
	}
	configProjectElapsed := time.Since(t)
	developBranch := optimusConfigMainResp.Data.ConySettings.DevelopBranch
	greyBranch := ""
	targetBranches := developBranch
	if version != nil {
		greyBranch, err = GetGreyBranch(*version, optimusConfigMainResp, optimusProjectConfigsResp)
		if err != nil {
			CommonUtils.LogCtxWarn(ctx, "[FetchVersionMetas]GetGreyBranch: err:%v", err)
			return nil, err
		}
		targetBranches = developBranch + "," + greyBranch
	}

	/**
	获取 app 下所有 targetBranch 为 developBranch 的 mr
	说明: 此处的 mr 为 bits mr，这里只会收集主仓，及多主仓的 mr
	*/
	t = time.Now()
	count := int64(100)
	lastID := int64(0)
	mrs := make([]int64, 0)
	var mrTypeQuery *string
	mrType := "feature"
	if reportConfig == nil {
		mrTypeQuery = &mrType
	} else {
		if !reportConfig.UseNewReport {
			mrTypeQuery = &mrType
		}
	}
	for {
		req := &optimus.GetMrListBySearchQuery{
			GroupName:      &groupName,
			ProductVersion: version,
			TargetBranch:   &targetBranches,
			LastID:         lastID,
			Limit:          &count,
			MrType:         mrTypeQuery,
		}
		if groupName == "Tiktok-UltraLite" {
			tmpGroupName := "TikTok_Android"
			req.GroupName = &tmpGroupName
			tags := "Lite_MR"
			req.Tags = &tags
		}

		if startTime != nil && endTime != nil {
			req.MergedTime = &optimus.TimeRangeFilter{
				Gte: *startTime,
				Lte: *endTime,
			}
		}
		mrRes, err := rpc.OptimusClient.GetMrListBySearch(ctx, req)
		if err != nil {
			CommonUtils.LogCtxWarn(ctx, "[FetchVersionMetas]GetMrListBySearch failed. err:%v", err)
			return nil, err
		}
		if len(mrRes.List) == 0 {
			break
		}
		sorts.Int64s(mrRes.List)
		mrs = append(mrRes.List, mrs...)
		if int64(len(mrRes.List)) < count {
			break
		}
		lastID = mrs[0]
	}
	getMrListBySearchElapsed := time.Since(t)

	// 从 optimus 获取 mr main info
	mrInfos := make([]*optimus.MrMainInfo, 0)
	t = time.Now()
	getMrInfoRoutinePool := utils.NewRoutinePool(ctx, 16)
	var lock sync.Mutex
	for _, mrID := range mrs {
		func(mrID int64) {
			getMrInfoRoutinePool.Submit(func() error {
				mrInfo, err := rpc.OptimusClient.GetMainMrInfo(ctx, &optimus.GetMrMainInfoQuery{
					MrID: mrID,
				})
				if err != nil {
					CommonUtils.LogCtxWarn(ctx, "[FetchVersionMetas]fetch mr info error: %v", mrInfo)
					return nil
				}
				if mrInfo != nil && mrInfo.Info != nil {
					if mrInfo.Info.MrState == optimus.MrState_merged.String() || mrInfo.Info.MrState == optimus.MrState_opened.String() {
						lock.Lock()
						mrInfos = append(mrInfos, mrInfo.Info)
						lock.Unlock()
					}
				}
				return nil
			})
		}(mrID)
	}
	getMrInfoRoutinePool.Shutdown()
	batchGetElapsed := time.Since(t)

	// 获取 mr 对应的 meego 信息
	t = time.Now()
	var dockings *string
	dockings = nil
	if reportConfig != nil {
		if reportConfig.UseNewReport {
			dockings = reportConfig.MeegoDockingIDs
		}
	}

	meegoStoryIDs := make([]model.MeegoIDMap, 0)
	meegoIssueIDs := make([]model.MeegoIDMap, 0)
	features := make([]*feature.FeatureBindRecordInfo, 0)
	getFeatureRoutinePool := utils.NewRoutinePool(ctx, 16)
	for _, info := range mrInfos {
		func(mrID int64) {
			getFeatureRoutinePool.Submit(func() error {
				featureRes, err := rpc.FeatureClient.GetFeatureBindRecord(ctx, &feature.GetFeatureBindRecordQuery{
					MrID: mrID,
				})
				if err != nil {
					CommonUtils.LogCtxWarn(ctx, "GetFeatureBindRecord: %v", err)
					return nil
				}
				if featureRes == nil || featureRes.List == nil {
					CommonUtils.LogCtxWarn(ctx, "GetFeatureBindRecord: featureRes nil")
					return nil
				}
				lock.Lock()
				for _, record := range featureRes.List {
					features = append(features, record)
					itemID, _ := strconv.ParseInt(record.TaskID, 10, 64)
					if record.TaskType == "bug" {
						meegoIssueIDs = append(meegoIssueIDs, model.MeegoIDMap{
							MeegoID:   int(itemID),
							SpaceName: record.PlatformProjectName,
						})
					} else if record.TaskType == "issue" {
						meegoStoryIDs = append(meegoStoryIDs, model.MeegoIDMap{
							MeegoID:   int(itemID),
							SpaceName: record.PlatformProjectName,
						})
					}
				}
				defer lock.Unlock()
				return nil
			})
		}(info.ID)
	}
	getFeatureRoutinePool.Shutdown()
	getFeatureElapsed := time.Since(t)

	t = time.Now()
	items := make([]model.WorkItem, 0)
	if len(meegoStoryIDs) > 0 {
		items, _ = FetchMeegoRequirements(ctx, meegoStoryIDs, "story")
	}
	if len(meegoIssueIDs) > 0 {
		issues, _ := FetchMeegoRequirements(ctx, meegoIssueIDs, "issue")
		items = append(items, issues...)
	}
	fetchMeegoElapsed := time.Since(t)

	t = time.Now()

	buildMetasRoutinePool := utils.NewRoutinePool(ctx, 16)
	versionMetas := make([]*BytedanceBitsIntegration.VersionMeta, 0)
	projectLineMap := make(map[string]string)
	for _, item := range items {
		projectLineMap[item.OwnedProject.SimpleName] = item.OwnedProject.ProjectKey
	}
	projectLines := make([]string, 0)
	for k := range projectLineMap {
		projectLines = append(projectLines, k)
	}
	businessLineMap := GetBusinessInfo(ctx, projectLines)
	// 构造 VersionMeta
	for _, mr := range mrInfos {
		func(mr *optimus.MrMainInfo) {
			buildMetasRoutinePool.Submit(func() error {
				versionInfo := ""
				if version != nil {
					versionInfo = *version
				}
				versionMeta := buildVersionMeta(ctx, versionInfo, mr, items, features, groupName, developBranch, greyBranch, dockings, businessLineMap)
				lock.Lock()
				versionMetas = append(versionMetas, versionMeta)
				lock.Unlock()
				return nil
			})
		}(mr)
	}
	buildMetasRoutinePool.Shutdown()

	buildVersionMetasElapsed := time.Since(t)

	logElapsed := buildLogElapsed()
	CommonUtils.LogCtxInfo(ctx, logElapsed, configMainElapsed, configProjectElapsed, getMrListBySearchElapsed, batchGetElapsed, getFeatureElapsed, fetchMeegoElapsed, buildVersionMetasElapsed)
	return &BytedanceBitsIntegration.FetchVersionMetasResponse{
		VersionMetas: versionMetas,
	}, err
}

func buildVersionMeta(ctx context.Context, version string, mr *optimus.MrMainInfo, items []model.WorkItem, features []*feature.FeatureBindRecordInfo, groupName string, developBranch string, greyBranch string, dockings *string, businessMap map[string]string) *BytedanceBitsIntegration.VersionMeta {
	tenancy, err := utils.GetTenancyByGroupName(ctx, groupName)
	if err != nil {
		logs.CtxError(ctx, "get tenancy failed,err:%v", err)
		return nil
	}
	if tenancy == "dcar" {
		if tcc.UseTenant(ctx) {
			ctx = mtctx.SetTenancy(ctx, tenancies.TenancyDcarlife)
		}
	}
	getMrReviewersResponse, err := rpc.OptimusClient.GetMrReviewers(ctx, &optimus.GetMrReviewersQuery{
		MrID: mr.ID,
	})
	reviewers := make([]*BytedanceBitsIntegration.Reviewer, 0)
	dockingMap := make(map[string]string)
	if dockings != nil {
		if len(*dockings) != 0 {
			dockingList := strings.Split(*dockings, ";")
			for _, docking := range dockingList {
				if len(docking) != 0 {
					dockingMap[docking] = docking
				}
			}
		}
	}
	if err == nil {
		reviewerUsernameSet := set.NewStringSet()
		for _, reviewer := range getMrReviewersResponse.List {
			if reviewerUsernameSet.Exist(reviewer.Username) {
				continue
			}
			reviewerUsernameSet.Add(reviewer.Username)
			mrIDInfo := &BytedanceBitsIntegration.MrIDInfo{}
			if reviewer.MrIDInfo != nil {
				mrIDInfo.MrID = reviewer.MrIDInfo.MrID
				mrIDInfo.ProjectID = reviewer.MrIDInfo.ProjectID
				mrIDInfo.Iid = reviewer.MrIDInfo.IID
			}
			reviewers = append(reviewers, &BytedanceBitsIntegration.Reviewer{
				Username:   reviewer.Username,
				Status:     reviewer.Status,
				ReviewRole: reviewer.ReviewRole,
				CreateTime: reviewer.CreateTime,
				FinishTime: reviewer.FinishTime,
				AvatarURL:  reviewer.AvatarURL,
				Removable:  reviewer.Removable,
				MrIDInfo:   mrIDInfo,
				ZhName:     reviewer.ZhName,
				ID:         reviewer.ID,
			})
		}
	}
	userInfoRes, err := rpc.OptimusClient.GetUserInfo(ctx, &optimus.GetUserInfoQuery{
		EnName: mr.AuthorName,
	})
	if err != nil {
		CommonUtils.LogCtxWarn(ctx, "[buildVersionMeta]GetUserInfo err: %v", err)
		return &BytedanceBitsIntegration.VersionMeta{}
	}
	commitURL := constructMrCommitUrl(mr.ID)

	mrItems := getMatchedStories(ctx, mr.ID, features, items)
	meegoInfos := parseMeegoInfos(ctx, mrItems, businessMap)
	dockingValues := make([]MeegoFieldValue, 0)
	for _, mrItem := range mrItems {
		for _, Fields := range mrItem.Fields {
			if _, ok := dockingMap[Fields.FieldKey]; ok {
				fieldValue := MeegoFieldValue{
					FieldName:  Fields.FieldName,
					FieldValue: Fields.FieldValue,
					FieldKey:   Fields.FieldKey,
				}
				dockingValues = append(dockingValues, fieldValue)
			}
		}
	}

	versionMeta := &BytedanceBitsIntegration.VersionMeta{
		Version:   version,
		MrID:      strconv.FormatInt(mr.ID, 10),
		GroupName: groupName,
		MrDetail: &BytedanceBitsIntegration.MrDetail{
			MrInfo: &BytedanceBitsIntegration.MrInfo{
				ID:             mr.ID,
				GroupName:      mr.GroupName,
				ProjectID:      mr.ProjectID,
				Iid:            mr.IID,
				ProjectName:    mr.ProjectName,
				FromBranch:     mr.FromBranch,
				ToBranch:       mr.ToBranch,
				MrTitle:        mr.MrTitle,
				MrType:         mr.MrType,
				CreateTime:     mr.CreateTime,
				MrState:        mr.MrState,
				AuthorName:     mr.AuthorName,
				ProductVersion: mr.ProductVersion,
				MergedTime:     mr.MergedTime,
				ZhName:         userInfoRes.Info.ZhName,
				MergeCommitSha: mr.LastCommitID,
				CommitURL:      commitURL,
			},
			Reviewers: reviewers,
		},
		MeegoInfos: meegoInfos,
		GroupInfo: &BytedanceBitsIntegration.GroupInfo{
			DevelopBranch: developBranch,
			ReleaseBranch: greyBranch,
			//Platform:      platform,
		},
		RelatedValues: nil,
	}
	if len(dockingValues) > 0 {
		relatedValues, err := json.Marshal(dockingValues)
		if err == nil {
			relatedValueString := string(relatedValues)
			versionMeta.RelatedValues = &relatedValueString
		}
	}
	return versionMeta
}

// https://meego.bytedance.net/cony-migration/story/detail/1104788
func ConstructRequirementURL(storyID string, projectName, requirementType string) string {
	if requirementType == "bug" {
		return "https://meego.bytedance.net/" + projectName + "/issue/detail/" + storyID

	} else {
		return "https://meego.bytedance.net/" + projectName + "/story/detail/" + storyID
	}
}

// https://bits.bytedance.net/bytebus/devops/code/detail/3286012
func constructMrCommitUrl(mrID int64) string {
	return CommonUtils.GetBitsHostWithScheme() + "/bytebus/devops/code/detail/" + strconv.FormatInt(mrID, 10)
}

func ConstructGetWorkItemDetailURL(projectName string, taskID int) string {
	storyURL := fmt.Sprintf("https://meego.bytedance.net/open-api/v1/project/%v/work_items/%v", projectName, taskID)
	return storyURL
}

func parseMeegoInfos(ctx context.Context, mrItems []model.WorkItem, businessLineMap map[string]string) []*BytedanceBitsIntegration.MeegoInfo {
	meegoInfos := make([]*BytedanceBitsIntegration.MeegoInfo, 0)
	for _, item := range mrItems {
		storyID := item.StoryID
		projectName := item.OwnedProject.SimpleName
		actualOnlineVersion := getFieldFromMeegoItem(item, "actual_online_version")
		qaEndDate := parseRoleEndData(ctx, item, "QA")
		business := ParseBusinessID(item, businessLineMap)
		status := parseFeatureStatus(ctx, item)
		stage := getFieldFromMeegoItem(item, "stage")
		roleOwners := parseRoleOwners(ctx, item)
		supportedApps := parseSupportedApps(ctx, item, "supported_apps")
		leopardID := item.StoryID
		owner := getMeegoOwner(ctx, item)
		ownerZhName := ""
		ownerEnName := ""
		if owner != nil {
			ownerZhName = owner.Name
			ownerEnName = owner.Username
		}
		requirementURL := ConstructRequirementURL(Interface2String(storyID), Interface2String(projectName), item.WorkItemTypeKey)

		meegoInfos = append(meegoInfos, &BytedanceBitsIntegration.MeegoInfo{
			Title:               item.Name,
			ActualOnlineVersion: Interface2String(actualOnlineVersion),
			QaEndDate:           Interface2String(qaEndDate),
			Business:            Interface2String(business),
			Status:              Interface2String(status),
			Stage:               Interface2String(stage),
			RoleOwners:          roleOwners,
			SupportedApps:       supportedApps,
			LeopardID:           Interface2String(leopardID),
			RequirementURL:      requirementURL,
			OwnerEnName:         Interface2String(ownerEnName),
			OwnerZhName:         Interface2String(ownerZhName),
			StoryID:             Interface2String(storyID),
		})
	}
	return meegoInfos
}

type GetMeegoItemResp struct {
	StatusCode int               `json:"statusCode"`
	Data       *GetMeegoItemData `json:"data"`
	ErrMsg     *string           `json:"errMsg"`
}

type MeegoFieldValue struct {
	FieldKey   string      `json:"field_key"`
	FieldValue interface{} `json:"field_value"`
	FieldName  string      `json:"field_name"`
}

type GetMeegoItemData struct {
	Value struct {
		FieldValues []MeegoFieldValue `json:"field_values"`
	} `json:"value"`
}

func getMatchedStories(ctx context.Context, mrID int64, features []*feature.FeatureBindRecordInfo, items []model.WorkItem) []model.WorkItem {
	storyIDs := make([]string, 0)
	mrItems := make([]model.WorkItem, 0)
	for _, feature := range features {
		if feature.MrID == mrID {
			storyIDs = append(storyIDs, feature.TaskID)
		}
	}

	for _, storyID := range storyIDs {
		for _, item := range items {
			if strconv.FormatInt(item.StoryID, 10) == storyID {
				mrItems = append(mrItems, item)
			}
		}
	}

	return mrItems
}
