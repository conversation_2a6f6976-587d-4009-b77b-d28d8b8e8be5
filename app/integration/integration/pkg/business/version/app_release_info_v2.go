package version

import (
	"context"
	"fmt"

	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	BytedanceBitsOptimus "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/rpc"
	"code.byted.org/gopkg/logs"
)

func AppReleaseInfoV2(ctx context.Context, groupName string, version string, appReleaseInfoVersion BytedanceBitsIntegration.AppReleaseInfoVersion) (*BytedanceBitsIntegration.FetchAppReleaseInfoResponse, error) {
	res, err := rpc.OptimusClient.GetAppReleaseVersionInfo(ctx, &BytedanceBitsOptimus.GetAppReleaseVersionInfoQuery{
		GroupName: groupName,
		Version:   version,
	})
	if err != nil {
		return nil, err
	}

	response, err := buildFetchAppReleaseInfoResponseV2(ctx, version, groupName, appReleaseInfoVersion, res)
	return response, err
}

func GetTicketMrRate(ctx context.Context, version, groupName, ticketName string) (string, bool) {
	var limit int64 = 1000
	var state = BytedanceBitsOptimus.MrState_opened.String()
	query := &BytedanceBitsOptimus.GetFrozenMrListRequest{
		GroupName: groupName,
		Tag:       &ticketName,
		Limit:     &limit,
		State:     &state,
		//Wip:       gptr.Of(int64(0)),
	}
	openedRes, err := rpc.OptimusClient.GetFrozenMrList(ctx, query)
	if err != nil {
		return "", false
	}
	state = "merged"
	query.State = &state
	mergedMrRes, err := rpc.OptimusClient.GetFrozenMrList(ctx, query)
	if err != nil {
		return "", false
	}
	mergedTotal := len(mergedMrRes.GetMrIds())
	openedTotal := len(openedRes.GetMrIds())
	return fmt.Sprintf("%v/%v", mergedTotal, openedTotal+mergedTotal), openedTotal == 0
}

func buildFetchAppReleaseInfoResponseV2(ctx context.Context, reqVersion string, groupName string, appReleaseInfoVersion BytedanceBitsIntegration.AppReleaseInfoVersion, res *BytedanceBitsOptimus.GetAppReleaseVersionInfoResponse) (*BytedanceBitsIntegration.FetchAppReleaseInfoResponse, error) {

	releaseId := res.ReleaseID
	status := res.Status.String()

	startIntegrationTime := res.StartIntegraionTime
	startVersionCloseTime := res.StartVersionCloseTime

	endVersionCloseTime := res.EndVersionCloseTime
	currentVersion := res.CurrentVersion
	ticketFinished := res.TicketFinished

	appReleaseInfo := buildDefaultAppReleaseInfoV2()
	appReleaseInfo.ReleaseID = releaseId
	appReleaseInfo.AppReleaseInfoVersion = appReleaseInfoVersion

	response := &BytedanceBitsIntegration.FetchAppReleaseInfoResponse{
		AppReleaseInfo: appReleaseInfo,
	}

	/****** 将来版本或临时增加的历史版本，直接返回默认模板 ******/
	if releaseId == 0 && currentVersion != reqVersion {
		return response, nil
	}

	appReleasePhaseInfos := make([]*BytedanceBitsIntegration.AppReleasePhaseInfo, 0)

	/****** 1.构建 "正在集成" 信息 ******/
	integratingStatus := BytedanceBitsIntegration.PhaseStatus_doing
	if releaseId != 0 {
		integratingStatus = BytedanceBitsIntegration.PhaseStatus_end
	}
	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseIntegrating,
		Status:      integratingStatus,
		BlockReason: "",
		Details:     nil,
		Extra: &BytedanceBitsIntegration.AppReleasePhaseExtra{
			StartTime: &startIntegrationTime,
		},
	})
	if releaseId == 0 {
		appReleaseInfo.AppReleasePhaseInfos = dataRenderingV2(appReleaseInfo.AppReleasePhaseInfos, appReleasePhaseInfos)
		return response, nil
	}

	/****** 2.构建 "准入管控阶段" 信息 ******/
	ticketingStatus := BytedanceBitsIntegration.PhaseStatus_doing
	if V2TaskSequence[status] > V2TaskSequence["WAITING_CLOSE_VERSION"] {
		ticketingStatus = BytedanceBitsIntegration.PhaseStatus_end
	}
	ticketingDetails := make([]*BytedanceBitsIntegration.AppReleasePhaseDetail, 0)
	V2PhaseDetailTicketingTicketFinishedStatus := BytedanceBitsIntegration.PhaseDetailStatus_doing
	if V2TaskSequence[status] > V2TaskSequence["WAITING_CLOSE_VERSION"] || (ticketFinished != nil && *ticketFinished) {
		V2PhaseDetailTicketingTicketFinishedStatus = BytedanceBitsIntegration.PhaseDetailStatus_end
	}
	ticketingDetails = append(ticketingDetails, &BytedanceBitsIntegration.AppReleasePhaseDetail{
		PhaseDetailKey: V2PhaseDetail_Ticketing_TicketFinished,
		Status:         V2PhaseDetailTicketingTicketFinishedStatus,
		Extra:          nil,
	})

	ticketMrMergeRate, completed := GetTicketMrRate(ctx, reqVersion, groupName, fmt.Sprintf("ticket:integration:%v", reqVersion))
	V2PhaseDetailTicketingMrMergeFinishedStatus := BytedanceBitsIntegration.PhaseDetailStatus_doing
	if V2TaskSequence[status] > V2TaskSequence["WAITING_CLOSE_VERSION"] || completed {
		V2PhaseDetailTicketingMrMergeFinishedStatus = BytedanceBitsIntegration.PhaseDetailStatus_end
	}
	ticketMrMergeRateJsonStr := ""
	if ticketMrMergeRate != "" {
		ticketMrMergeRateJsonStr = fmt.Sprintf(`{"rate": "%s"}`, ticketMrMergeRate)
	}
	ticketingDetails = append(ticketingDetails, &BytedanceBitsIntegration.AppReleasePhaseDetail{
		PhaseDetailKey: V2PhaseDetail_Ticketing_MrMergeFinished,
		Status:         V2PhaseDetailTicketingMrMergeFinishedStatus,
		Extra: &BytedanceBitsIntegration.AppReleasePhaseDetailExtra{
			ExtraJSONString: &ticketMrMergeRateJsonStr,
		},
	})

	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseTicketing,
		Status:      ticketingStatus,
		BlockReason: "",
		Details:     ticketingDetails,
		Extra: &BytedanceBitsIntegration.AppReleasePhaseExtra{
			StartTime: &startVersionCloseTime,
		},
	})

	if V2TaskSequence[status] <= V2TaskSequence["WAITING_CLOSE_VERSION"] {
		appReleaseInfo.AppReleasePhaseInfos = dataRenderingV2(appReleaseInfo.AppReleasePhaseInfos, appReleasePhaseInfos)
		return response, nil
	}

	for seq, phase := range V2TaskSequencePhase {
		if seq <= 1 {
			continue
		}
		phaseStatus := BytedanceBitsIntegration.PhaseStatus_doing
		if V2TaskSequence[status] > seq || (len(V2TaskSequencePhase)-1) == seq {
			phaseStatus = BytedanceBitsIntegration.PhaseStatus_end
		}
		appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
			PhaseKey: phase,
			Status:   phaseStatus,
		})
		if V2TaskSequence[status] == seq {
			if endVersionCloseTime != 0 {
				appReleasePhaseInfos[len(appReleasePhaseInfos)-1].Extra = &BytedanceBitsIntegration.AppReleasePhaseExtra{StartTime: &endVersionCloseTime}
			}
			appReleaseInfo.AppReleasePhaseInfos = dataRenderingV2(appReleaseInfo.AppReleasePhaseInfos, appReleasePhaseInfos)
			return response, nil
		}
	}
	logs.CtxError(ctx, "V2TaskSequencePhase: err length.")
	return response, nil
}

/**
渲染返回 JSON 模板
*/

func dataRenderingV2(resAppReleasePhaseInfos []*BytedanceBitsIntegration.AppReleasePhaseInfo,
	appReleasePhaseInfos []*BytedanceBitsIntegration.AppReleasePhaseInfo) []*BytedanceBitsIntegration.AppReleasePhaseInfo {
	// 渲染 AppReleasePhaseInfo 模板
	for i, info := range appReleasePhaseInfos {
		resAppReleasePhaseInfos[i] = info
	}
	return resAppReleasePhaseInfos
}

var V2TaskSequence = map[string]int{
	"START":                             0,
	"TICKETING":                         1,
	"WAITING_CLOSE_VERSION":             1,
	"CHECKING_DEVELOP_ATOMIC_LOCK":      2,
	"CREATING_GREY_BRANCH":              2,
	"CREATING_RC_TO_GREY_MR":            2,
	"UPDATING_FEATURE_STATUS_TO_MEEGO":  3,
	"MODIFYING_PROJECT_CONFIG":          4,
	"PRE_DELETING_DEVELOP_RELEASE_LOCK": 4,
	"DELETING_DEVELOP_RELEASE_LOCK":     4,
	"END":                               5,
}

var V2TaskSequencePhase = []string{
	V2PhaseIntegrating,
	V2PhaseTicketing,
	V2PhaseCreateGreyBranch,
	V2PhaseSendVersionCloseReport,
	V2PhaseUpdateProjectConfig,
	V2PhaseEnd,
}

// Phase
var V2PhaseIntegrating = "Integrating"
var V2PhaseTicketing = "Ticketing"
var V2PhaseCreateGreyBranch = "CreateGreyBranch"
var V2PhaseSendVersionCloseReport = "SendVersionCloseReport"
var V2PhaseUpdateProjectConfig = "UpdateProjectConfig"
var V2PhaseEnd = "End"

// Phase Detail
// Ticketing
var V2PhaseDetail_Ticketing_TicketFinished = "TicketFinished"

var V2PhaseDetail_Ticketing_MrMergeFinished = "MrMergeFinished"

func buildDefaultAppReleaseInfoV2() *BytedanceBitsIntegration.AppReleaseInfo {
	appReleasePhaseInfos := make([]*BytedanceBitsIntegration.AppReleasePhaseInfo, 0)

	// 1.Integrating
	integratingInfo := &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseIntegrating,
		Status:      BytedanceBitsIntegration.PhaseStatus_waiting,
		BlockReason: "",
		Details:     nil,
	}
	appReleasePhaseInfos = append(appReleasePhaseInfos, integratingInfo)

	// 2.Ticketing
	ticketingDetails := make([]*BytedanceBitsIntegration.AppReleasePhaseDetail, 0)
	ticketingDetails = append(ticketingDetails, buildDefaultDetailV2(V2PhaseDetail_Ticketing_TicketFinished, nil))
	ticketingDetails = append(ticketingDetails, buildDefaultDetailV2(V2PhaseDetail_Ticketing_MrMergeFinished, nil))
	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseTicketing,
		Status:      BytedanceBitsIntegration.PhaseStatus_waiting,
		BlockReason: "",
		Details:     ticketingDetails,
	})

	// 3.CreateGreyBranch
	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseCreateGreyBranch,
		Status:      BytedanceBitsIntegration.PhaseStatus_waiting,
		BlockReason: "",
		Details:     nil,
	})

	// 4.SendVersionCloseReport
	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseSendVersionCloseReport,
		Status:      BytedanceBitsIntegration.PhaseStatus_waiting,
		BlockReason: "",
		Details:     nil,
	})

	// 5.UpdateProjectConfig
	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseUpdateProjectConfig,
		Status:      BytedanceBitsIntegration.PhaseStatus_waiting,
		BlockReason: "",
		Details:     nil,
	})

	// 6.End
	appReleasePhaseInfos = append(appReleasePhaseInfos, &BytedanceBitsIntegration.AppReleasePhaseInfo{
		PhaseKey:    V2PhaseEnd,
		Status:      BytedanceBitsIntegration.PhaseStatus_waiting,
		BlockReason: "",
		Details:     nil,
	})

	return &BytedanceBitsIntegration.AppReleaseInfo{
		AppReleaseInfoVersion: BytedanceBitsIntegration.AppReleaseInfoVersion_v2,
		AppReleasePhaseInfos:  appReleasePhaseInfos,
		ReleaseID:             0,
	}
}

func buildDefaultDetailV2(phaseDetailKey string, extra *BytedanceBitsIntegration.AppReleasePhaseDetailExtra) *BytedanceBitsIntegration.AppReleasePhaseDetail {
	return &BytedanceBitsIntegration.AppReleasePhaseDetail{
		PhaseDetailKey: phaseDetailKey,
		Status:         BytedanceBitsIntegration.PhaseDetailStatus_waiting,
		Extra:          extra,
	}
}
