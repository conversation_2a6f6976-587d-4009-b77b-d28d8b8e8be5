package utils

import (
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/model"
	"context"
	"fmt"
	"reflect"
	"strings"
)

// 比较组件变化
func ComponentsDiff(arrayA, arrayB []*model.CommonComponent) []*model.ChangedComponent {

	// arrayA 是旧的数组 arrayB 是新的数组
	oldArr := make([]interface{}, 0)
	for _, component := range arrayA {
		oldArr = append(oldArr, component)
	}

	newArr := make([]interface{}, 0)
	for _, component := range arrayB {
		newArr = append(newArr, component)
	}

	diffDict := DiffArray(oldArr, newArr, isSameRepo, exactCompareAppStruct)
	changedArr := make([]*model.ChangedComponent, 0)
	addArr, ok := diffDict["add"]
	if ok {
		ret := handleAddOrDeleteItems(addArr, true)
		changedArr = append(changedArr, ret...)
	}
	deleteArr, ok := diffDict["delete"]
	if ok {
		ret := handleAddOrDeleteItems(deleteArr, false)
		changedArr = append(changedArr, ret...)
	}
	updateArr, ok := diffDict["update"]
	if ok {
		ret := handleUpdateItems(updateArr)
		changedArr = append(changedArr, ret...)
	}
	return changedArr
}

// 根据组件名判断是不是同一个组件
func isSameRepo(repoA, repoB interface{}) bool {

	itemA := repoA.(*model.CommonComponent)
	itemB := repoB.(*model.CommonComponent)
	return itemA.Name == itemB.Name && itemA.GroupName == itemB.GroupName
}

// 判断是不是同一个字符串
func isSameStr(elementA, elementB interface{}) bool {
	return elementA == elementB
}

// 处理添加或删除的组件
func handleAddOrDeleteItems(arr []interface{}, isAdd bool) []*model.ChangedComponent {

	changedArr := make([]*model.ChangedComponent, 0)
	for _, repo := range arr {
		repoInfo := repo.(*model.CommonComponent)

		com := &model.ChangedComponent{}
		com.CommonComponent = *repoInfo
		com.Type = model.ComponentRepoChangedType_Component

		var version model.ChangedVersion
		if isAdd {

			version.Add = repoInfo.Version
			version.Delete = ""
			com.Version = repoInfo.Version
			com.VersionChanged = &version
			com.ChangedType = model.ComponentChangedType_add
		} else {

			version.Delete = repoInfo.Version
			version.Add = ""
			com.Version = ""
			com.VersionChanged = &version
			com.ChangedType = model.ComponentChangedType_delete
		}
		changedArr = append(changedArr, com)
	}
	return changedArr
}

// 处理更新了的组件
func handleUpdateItems(arr []interface{}) []*model.ChangedComponent {

	changedArr := make([]*model.ChangedComponent, 0)
	for _, updateRepo := range arr {

		repoInfo := updateRepo.(*model.ChangedComponent)
		repoInfo.ChangedType = model.ComponentChangedType_update
		changedArr = append(changedArr, repoInfo)
	}
	return changedArr
}

// 精确匹配 AppStruct 的变化
func exactCompareAppStruct(elementA, elementB interface{}) interface{} {

	if elementA == elementB {
		return nil
	}

	itemA := elementA.(*model.CommonComponent)
	itemB := elementB.(*model.CommonComponent)

	changed := &model.ChangedComponent{}
	changed.CommonComponent = *itemB
	changed.Type = model.ComponentRepoChangedType_Component

	versionChanged := getVersionChanges(itemA, itemB, changed)
	targetChanged := getTargetChanges(itemA, itemB, changed)
	structChanged := getAppStructChanges(itemA, itemB, changed)
	targetsInfoChanged := getTargetsInfoChanges(itemA, itemB, changed)

	if versionChanged || targetChanged || structChanged || targetsInfoChanged {
		return changed
	}
	return nil
}

//获取 version 的变化
func getVersionChanges(itemA, itemB *model.CommonComponent, changed *model.ChangedComponent) bool {

	hasChanged := false
	if itemA.Version != itemB.Version {
		versionChange := model.ChangedVersion{}
		versionChange.Delete = itemA.Version
		versionChange.Add = itemB.Version
		changed.VersionChanged = &versionChange
		hasChanged = true
	}
	return hasChanged
}

// 格式化 targets 的变化信息
func handleAddOrDeleteTarget(targetsDiff map[string][]interface{}, key string) []string {

	changesArr := make([]string, 0)
	arr, ok := targetsDiff[key]
	if ok {
		for _, item := range arr {
			changesArr = append(changesArr, item.(string))
		}
	}
	return changesArr
}

// 获取 targets 的变化
func getTargetChanges(itemA, itemB *model.CommonComponent, changed *model.ChangedComponent) bool {

	hasChanged := false

	targetsA := make([]interface{}, 0)
	targetsB := make([]interface{}, 0)
	for _, v := range itemA.Targets {
		targetsA = append(targetsA, v)
	}
	for _, v := range itemB.Targets {
		targetsB = append(targetsB, v)
	}

	targetsDiff := DiffArray(targetsA, targetsB, isSameStr, nil)
	if targetsDiff != nil && len(targetsDiff) > 0 {
		var changedTargets model.ChangedTargets

		addArr := handleAddOrDeleteTarget(targetsDiff, "add")
		deleteArr := handleAddOrDeleteTarget(targetsDiff, "delete")
		changedTargets.Add = addArr
		changedTargets.Delete = deleteArr
		changed.TargetsChanged = &changedTargets
		hasChanged = true
	}
	return hasChanged
}

// 获取 app_struct 的变化
func getAppStructChanges(itemA, itemB *model.CommonComponent, changed *model.ChangedComponent) bool {

	hasChanged := false
	appStructA := make([]interface{}, 0)
	appStructB := make([]interface{}, 0)

	for k, v := range itemA.AppStruct {
		item := make(map[string]interface{})
		item[k] = v
		appStructA = append(appStructA, item)
	}
	for k, v := range itemB.AppStruct {
		item := make(map[string]interface{})
		item[k] = v
		appStructB = append(appStructB, item)
	}

	appStructDiff := DiffArray(appStructA, appStructB, isSameAppStructItem, nil)

	if appStructDiff != nil && len(appStructDiff) > 0 {

		var appStruct model.ChangedAppStruct
		appStruct.Add = addAppStructChangedInfo(appStructDiff, "add")
		appStruct.Delete = addAppStructChangedInfo(appStructDiff, "delete")
		changed.AppStructChanged = &appStruct
		hasChanged = true
	}
	return hasChanged
}

// 获取 targetsInfo 的变化
func getTargetsInfoChanges(itemA, itemB *model.CommonComponent, changed *model.ChangedComponent) bool {

	hasChanged := false
	targetsInfoChanges := make(map[string]model.ChangedVersion)

	for oldTarget, oldVersion := range itemA.TargetsInfo {

		newVersion, ok := itemB.TargetsInfo[oldTarget]
		if ok {
			if newVersion != oldVersion {
				var change model.ChangedVersion
				change.Add = newVersion
				change.Delete = oldVersion
				targetsInfoChanges[oldTarget] = change
			}
		} else {
			var change model.ChangedVersion
			change.Add = ""
			change.Delete = oldVersion
			targetsInfoChanges[oldTarget] = change
		}
	}

	for newTarget, newVersion := range itemB.TargetsInfo {
		_, ok := itemA.TargetsInfo[newTarget]
		if !ok {
			var change model.ChangedVersion
			change.Add = newVersion
			change.Delete = ""
			targetsInfoChanges[newTarget] = change
		}
	}

	changed.TargetsInfoChanged = targetsInfoChanges

	if len(targetsInfoChanges) > 0 {
		hasChanged = true
	}
	return hasChanged
}

// 判断是不是相同的 app_struct
func isSameAppStructItem(elementA, elementB interface{}) bool {

	itemA := elementA.(map[string]interface{})
	itemB := elementB.(map[string]interface{})

	keyA := ""
	keyB := ""

	for k := range itemA {
		keyA = k
	}
	for k := range itemB {
		keyB = k
	}

	if keyA != keyB {
		return false
	}

	subspecsA := itemA[keyA]
	subspecsB := itemB[keyB]

	arrA := make([]interface{}, 0)
	arrB := make([]interface{}, 0)

	s := reflect.ValueOf(subspecsA)
	for i := 0; i < s.Len(); i++ {
		a := fmt.Sprintf(s.Index(i).String())
		arrA = append(arrA, a)
	}

	s = reflect.ValueOf(subspecsB)
	for i := 0; i < s.Len(); i++ {
		arrB = append(arrB, s.Index(i).String())
	}
	ret := DiffArray(arrA, arrB, isSameStr, nil)

	if len(ret) > 0 {
		return false
	}
	return true
}

// 格式化 app_struct 的变化信息
func addAppStructChangedInfo(appStructValue interface{}, key string) []map[string][]string {

	dict := appStructValue.(map[string][]interface{})
	arr, ok := dict[key]
	changeArr := make([]map[string][]string, 0)
	if ok {
		for _, changed := range arr {
			changedDict := map[string][]string{}
			tmp := changed.(map[string]interface{})
			for k := range tmp {
				value := tmp[k].([]string)
				changedDict[k] = value
			}
			changeArr = append(changeArr, changedDict)
		}
	}
	return changeArr
}

// 返回的 add 和 delete 是新增和删除的元素，update 是具体有哪些变化，类型以传入的 exactComparator 的返回值为准
func DiffArray(arrayA, arrayB []interface{}, comparator func(interface{}, interface{}) bool, exactComparator func(interface{}, interface{}) interface{}) map[string][]interface{} {
	// arrayA 是旧的数组 arrayB 是新的数组

	diffDict := make(map[string][]interface{})

	removedElements := differenceWith(arrayA, arrayB, comparator)
	addedElements := differenceWith(arrayB, arrayA, comparator)

	if len(removedElements) > 0 {
		diffDict["delete"] = removedElements
	}
	if len(addedElements) > 0 {
		diffDict["add"] = addedElements
	}

	bothElements := intersectWith(arrayA, arrayB, comparator)

	updates := []interface{}{}
	for _, bothItem := range bothElements {

		elementA := intersectWith(arrayA, []interface{}{bothItem}, comparator)
		elementB := intersectWith(arrayB, []interface{}{bothItem}, comparator)
		if exactComparator != nil {
			exactSameRet := exactComparator(elementA[0], elementB[0])
			if exactSameRet != nil {
				updates = append(updates, exactSameRet)
			}
		}
	}
	if len(updates) > 0 {
		diffDict["update"] = updates
	}
	return diffDict
}

// 获取 arrayA 比 arrayB 多几个元素
func differenceWith(arrayA, arrayB []interface{}, comparator func(interface{}, interface{}) bool) []interface{} {

	diffArray := []interface{}{}
	for _, aItem := range arrayA {
		found := false
		for _, bItem := range arrayB {
			same := comparator(aItem, bItem)
			if same {
				found = true
				break
			}
		}
		if found == false {
			diffArray = append(diffArray, aItem)
		}
	}
	return diffArray
}

// 获取 arrayA 和 arrayB 共同的元素
func intersectWith(arrayA, arrayB []interface{}, comparator func(interface{}, interface{}) bool) []interface{} {

	// 先粗略的估算出相同的组件
	sameArray := []interface{}{}
	for _, aItem := range arrayA {
		for _, bItem := range arrayB {
			same := comparator(aItem, bItem)
			if same {
				sameArray = append(sameArray, aItem)
			}
		}
	}
	return sameArray
}

func ComponentFullName(ctx context.Context, repoName, repoGroupName string, appType meta.TechnologyStack) string {

	fullName := repoName
	if appType == meta.TechnologyStack_Android {
		if len(repoGroupName) > 0 {
			fullName = fmt.Sprintf("%v:%v", repoGroupName, repoName)
		} else {
			fullName = repoName
		}
	}
	return fullName
}

func SplitFullName(fullName string) (string, string) {

	sep := ":"
	repoName := ""
	repoGroupName := ""
	if strings.Contains(fullName, sep) {
		arr := strings.Split(fullName, sep)
		if len(arr) == 2 {
			repoGroupName = arr[0]
			repoName = arr[1]
		} else {
			repoName = fullName
		}
	} else {
		repoName = fullName
	}
	return repoName, repoGroupName
}
