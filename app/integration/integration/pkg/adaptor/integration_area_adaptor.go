package adaptor

import (
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/db"
	"context"
)

func AdaptDBAreaToRemoteArea(ctx context.Context, area db.IntegrationArea) *integration.IntegrationArea {
	return &integration.IntegrationArea{
		ID:              area.Id,
		Name:            area.Name,
		RepoID:          area.RepoId,
		RepoRef:         area.RepoRef,
		CurrentCommitID: area.CurrentCommitId,
		Status:          integration.IntegrationAreaStatus(area.Status),
		Creator:         area.Creator,
		CreateTime:      area.CreateTime.Unix(),
		ModifiedTime:    area.UpdateTime.Unix(),
		Description:     &area.Description,
	}
}
