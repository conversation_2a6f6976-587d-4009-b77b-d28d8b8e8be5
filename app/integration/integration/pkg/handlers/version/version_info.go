package version

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	businessVersion "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/business/version"
)

func FetchAppVersions(ctx context.Context, req *integration.FetchAppVersionsRequest) (r *integration.FetchAppVersionsResponse, err error) {
	//
	groupNames := req.GroupNames
	if len(groupNames) == 0 {
		return buildResAppVersions("empty groupNames"), nil
	}

	//
	appVersions := businessVersion.FetchAppVersions(ctx, groupNames)
	if len(appVersions) == 0 {
		return buildResAppVersions("empty appVersions"), nil
	}

	return &integration.FetchAppVersionsResponse{
		AppVersions: appVersions,
		BaseResp:    nil,
	}, nil
}

func buildResAppVersions(msg string) *integration.FetchAppVersionsResponse {
	return &integration.FetchAppVersionsResponse{
		AppVersions: nil,
		BaseResp: &base.BaseResp{
			StatusMessage: msg,
		},
	}
}
