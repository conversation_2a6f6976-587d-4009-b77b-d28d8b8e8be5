package version

import (
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/base"
	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	businessVersion "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/business/version"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/consts"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/db"
	"context"
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs"
)

func FetchReportSnapshots(ctx context.Context, req *BytedanceBitsIntegration.FetchReportSnapshotsRequest) (*BytedanceBitsIntegration.FetchReportSnapshotsResponse, error) {
	groupName := req.GroupName
	version := req.Version
	if groupName == "" || version == "" {
		return buildFetchReportSnapshotsResp("invalid params"), nil
	}
	createTimes, err := db.BitsIntegrationReadDB.GetVersionCloseReportsTimestamp(ctx, groupName, version)
	if err != nil {
		return buildFetchReportSnapshotsResp(fmt.Sprintf("[FetchReportSnapshots]db err:%+v", err)), nil
	}
	snapshots := make([]string, 0)
	for _, t := range createTimes {
		snapshots = append(snapshots, t.Format(consts.CommonTimeFormat))
	}
	return &BytedanceBitsIntegration.FetchReportSnapshotsResponse{
		Snapshots: snapshots,
	}, nil
}

func FetchReportSnapshotInfo(ctx context.Context, req *BytedanceBitsIntegration.FetchReportSnapshotsRequest) (*BytedanceBitsIntegration.FetchReportSnapshotsInfoResponse, error) {
	groupName := req.GroupName
	version := req.Version
	if groupName == "" || version == "" {
		return &BytedanceBitsIntegration.FetchReportSnapshotsInfoResponse{
			Info: nil,
			BaseResp: &base.BaseResp{
				StatusMessage: "invalid params",
				StatusCode:    406,
			},
		}, bits_err.COMMON.ErrInvalidInput
	}
	info, err := db.BitsIntegrationWriteDB.GetVersionCloseReportsByGroupNameAndVersion(ctx, groupName, version)
	if err != nil {
		return &BytedanceBitsIntegration.FetchReportSnapshotsInfoResponse{
			Info: nil,
			BaseResp: &base.BaseResp{
				StatusMessage: err.Error(),
				StatusCode:    500,
			},
		}, bits_err.COMMON.ErrRecordNotFound
	}
	snapshotInfo := make([]*BytedanceBitsIntegration.SnapshotInfo, 0)
	for _, info := range info {
		timestamp := info.CreateTime.Unix()
		snapshotInfo = append(snapshotInfo, &BytedanceBitsIntegration.SnapshotInfo{
			Snapshot:    info.CreateTime.Format(consts.CommonTimeFormat),
			TriggerType: info.TriggerType,
			Timestamp:   &timestamp,
		})
	}
	return &BytedanceBitsIntegration.FetchReportSnapshotsInfoResponse{Info: snapshotInfo}, nil
}

func FetchVersionCloseReports(ctx context.Context, req *BytedanceBitsIntegration.FetchReportRequest) (*BytedanceBitsIntegration.FetchReportResponse, error) {
	groupName := req.GroupName
	version := req.Version
	reqSnapshot := req.Snapshot
	if req.Timestamp != nil {
		reqSnapshot = time.Unix(*req.Timestamp, 0).Format(consts.CommonTimeFormat)
	}
	if groupName == "" || version == "" || reqSnapshot == "" {
		return buildFetchReportResp("invalid params"), nil
	}
	data, err := fetchVersionCloseReports(ctx, groupName, version, reqSnapshot)
	if err != nil {
		return buildFetchReportResp(fmt.Sprintf("[FetchVersionCloseReports]err:%+v", err)), nil
	}
	return &BytedanceBitsIntegration.FetchReportResponse{
		ResponseData: data,
	}, nil
}

func FetchVersionCloseExcel(ctx context.Context, req *BytedanceBitsIntegration.GetVersionCloseExcelReportRequest) (*BytedanceBitsIntegration.GetVersionCloseExcelReportResponse, error) {
	reqSnapshot := time.Unix(req.Timestamp, 0).Format(consts.CommonTimeFormat)
	url, err := fetchVersionCloseExcel(ctx, req.GroupName, req.Version, req.MrType, reqSnapshot)
	if err != nil {
		logs.CtxError(ctx, "fetchVersionCloseExcel err:%+v", err)
		return &BytedanceBitsIntegration.GetVersionCloseExcelReportResponse{
			ReportURL: "",
			BaseResp: &base.BaseResp{
				StatusMessage: err.Error(),
				StatusCode:    500,
			},
		}, err
	}
	return &BytedanceBitsIntegration.GetVersionCloseExcelReportResponse{
		ReportURL: url,
	}, nil
}

func fetchVersionCloseReports(ctx context.Context, groupName string, version string,
	reqSnapshot string) (*BytedanceBitsIntegration.ResponseData, error) {
	binaryReport, err := GetReportFromTos(ctx, groupName, version, reqSnapshot)
	if err != nil {
		return nil, err
	}
	responseData, err := UnmarshalReport(binaryReport)
	if err != nil {
		return nil, err
	}
	return responseData, nil
}

func fetchVersionCloseExcel(ctx context.Context, groupName string, version string, mrType []string, reqSnapshot string) (string, error) {
	binaryReport, err := GetReportFromTos(ctx, groupName, version, reqSnapshot)
	if err != nil {
		logs.CtxError(ctx, "[FetchVersionCloseExcel]GetReportFromTos err:%+v", err)
		return "", err
	}
	responseData, err := UnmarshalReport(binaryReport)
	if err != nil {
		logs.CtxError(ctx, "[FetchVersionCloseExcel]UnmarshalReport err:%+v", err)
		return "", err
	}
	if len(mrType) == 0 {
		mrType = append(mrType, "all")
	}
	excelKey, err := businessVersion.BuildReportExcel(ctx, groupName, version, mrType, responseData, reqSnapshot)
	if err != nil {
		logs.CtxError(ctx, "[FetchVersionCloseExcel]BuildReportExcel err:%+v", err)
		return "", err
	}
	if err != nil {
		logs.CtxError(ctx, "[FetchVersionCloseExcel]UpdateExcelKeyById err:%+v", err)
		return "", err
	}
	return fmt.Sprintf("https://tosv.byted.org/obj/toutiao.ios.arch/%v", excelKey), nil
}

func buildFetchReportResp(statusMessage string) *BytedanceBitsIntegration.FetchReportResponse {
	emptyData := make([]*BytedanceBitsIntegration.VersionMeta, 0)
	return &BytedanceBitsIntegration.FetchReportResponse{
		ResponseData: &BytedanceBitsIntegration.ResponseData{
			OnBoarding:  emptyData,
			PreBoarding: emptyData,
			NoBoarding:  emptyData,
		},
		BaseResp: &base.BaseResp{
			StatusMessage: statusMessage,
			//StatusCode:    statusCode,
			Extra: nil,
		},
	}
}

func buildFetchReportSnapshotsResp(statusMessage string) *BytedanceBitsIntegration.FetchReportSnapshotsResponse {
	return &BytedanceBitsIntegration.FetchReportSnapshotsResponse{
		Snapshots: []string{},
		BaseResp: &base.BaseResp{
			StatusMessage: statusMessage,
			//StatusCode:    statusCode,
			Extra: nil,
		},
	}
}
