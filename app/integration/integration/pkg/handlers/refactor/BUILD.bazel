load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "refactor",
    srcs = [
        "approve.go",
        "calendar_event_callback.go",
        "internal_service.go",
        "mergingQueue.go",
        "tracks_ref.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/refactor",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/base",
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/pkg/business/refactor",
        "//app/integration/integration/pkg/business/tracks_ref",
        "//app/integration/integration/pkg/consts",
        "//app/integration/integration/pkg/dal/db",
        "//app/integration/integration/pkg/model/calendar_callback_model",
        "//app/integration/integration/pkg/mq/rocket_mq/producer",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/utils",
        "//libs/events",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "refactor_test",
    srcs = ["calendar_event_callback_test.go"],
    embed = [":refactor"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/pkg/model/calendar_callback_model",
        "@com_github_bytedance_sonic//:sonic",
    ],
)
