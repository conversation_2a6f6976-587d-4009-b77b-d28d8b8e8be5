load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "build",
    srcs = [
        "artifact.go",
        "callback.go",
        "execute.go",
        "info_release.go",
        "list.go",
        "package_info.go",
        "update.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/regression/build",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/devops_settings",
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/kitex_gen/bytedance/bits/meta",
        "//app/integration/integration/kitex_gen/bytedance/bits/workflow",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/artifact",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/checkpipeline",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/gprelease",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/releaserule",
        "//app/integration/integration/pkg/adaptor",
        "//app/integration/integration/pkg/business/ci_pipeline",
        "//app/integration/integration/pkg/business/official",
        "//app/integration/integration/pkg/consts",
        "//app/integration/integration/pkg/dal/db",
        "//app/integration/integration/pkg/dal/redis",
        "//app/integration/integration/pkg/handlers/release/build",
        "//app/integration/integration/pkg/rpc",
        "//app/integration/integration/pkg/service/app",
        "//app/integration/integration/pkg/service/lark",
        "//app/integration/integration/pkg/service/mr_package",
        "//app/integration/integration/pkg/service/release",
        "//app/integration/integration/pkg/service/user",
        "//app/integration/integration/pkg/service/workflow",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/consts",
        "//libs/common_lib/utils",
        "//libs/compatibletenancy/emails",
        "@com_github_pkg_errors//:errors",
        "@com_github_spf13_cast//:cast",
        "@org_byted_code_clientqa_rls_util//errorutil",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_kv_redis_v6//:redis-v6",
    ],
)
