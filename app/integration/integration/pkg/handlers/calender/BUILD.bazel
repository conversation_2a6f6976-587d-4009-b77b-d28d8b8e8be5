load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "calender",
    srcs = [
        "adapt.go",
        "cache.go",
        "candidate.go",
        "create.go",
        "event_trigger.go",
        "list.go",
        "sync.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/calender",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/pkg/consts",
        "//app/integration/integration/pkg/dal/db",
        "//app/integration/integration/pkg/model",
        "//app/integration/integration/pkg/mq/rocket_mq/producer",
        "//app/integration/integration/pkg/rpc",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/utils",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_facility//slice",
        "@org_byted_code_gopkg_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_kite_kitc//discovery",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_kite_kitex//client/callopt",
    ],
)
