package official_build_handler

import (
	"context"
	"fmt"
	"time"

	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/lark"
	"code.byted.org/clientQA/rls_util/errorutil"
	redisV6 "code.byted.org/kv/redis-v6"
	"github.com/spf13/cast"
)

func Callback(ctx context.Context, req *BytedanceBitsIntegration.IntegrationBuildCallbackRequest) (r *BytedanceBitsIntegration.IntegrationBuildCallbackResponse, err error) {
	var funcName = "Callback"

	r = &BytedanceBitsIntegration.IntegrationBuildCallbackResponse{}
	officialBuildInfo, err := db.BitsReadDB.GetOfficialBuildByJobId(ctx, req.JobID)
	if err != nil {
		return r, errorutil.NewErr(err, "[%v] GetOfficialBuildByJobId err, req.JobID: %v",
			funcName, req.JobID)
	}
	integrationInfo, err := db.BitsReadDB.GetIntegrationById(ctx, officialBuildInfo.IntegrationId)
	if err != nil {
		return r, errorutil.NewErr(err, "[%v] GetIntegrationById err, IntegrationId: %v",
			funcName, officialBuildInfo.IntegrationId)
	}

	// 发送通知
	err = lark.SendOfficialBuildNotification(ctx, req.IsSuccess, integrationInfo, officialBuildInfo)
	if err != nil {
		return r, errorutil.NewErr(err, "[%v] SendOfficialBuildNotification err, isSuccess: %v, integrationInfo: %v, officialBuildInfo: %v",
			funcName, req.IsSuccess, integrationInfo, officialBuildInfo)
	}

	return
}

func RegressionCallback(ctx context.Context, req *BytedanceBitsIntegration.IntegrationRegressionCallbackRequest) (r *BytedanceBitsIntegration.IntegrationRegressionCallbackResponse, err error) {
	var funcName = "RegressionCallback"

	r = &BytedanceBitsIntegration.IntegrationRegressionCallbackResponse{}

	// 回归检查侧会有并发问题，可能会重复发送lark通知，需要加锁控制
	result := redis.Client.SetNX(redis.OfficialCallbackLock, 0, time.Second*10)
	if result.Val() {
		// 解锁
		defer redis.Client.Del(redis.OfficialCallbackLock)

		field := fmt.Sprintf("%v_%v_%v", req.IsSuccess, req.Operator, req.BitsLink)
		if !req.IsSuccess && req.CheckToolName != nil {
			field += "_" + *req.CheckToolName
		}
		// 判断是否已经发送过此条通知, 10秒内只发送一次通知
		lastSendTime, err := redis.Client.HGet(redis.OfficialRegressionCallback, field).Result()
		if err != nil && err != redisV6.Nil {
			return r, errorutil.NewErr(err, "[%v] HGet err, key: %v, field: %v",
				funcName, redis.OfficialRegressionCallback, field)
		} else if err == nil {
			if time.Now().Unix()-cast.ToInt64(lastSendTime) < 10 {
				return r, nil
			}
		}

		// 发送通知
		err = lark.SendOfficialRegressionNotification(ctx, req.IsSuccess, req)
		if err != nil {
			return r, errorutil.NewErr(err, "[%v] SendOfficialRegressionNotification err, isSuccess: %v, req: %v",
				funcName, req.IsSuccess, req)
		}

		// 将这条通知记录在Redis中
		err = redis.Client.HSet(redis.OfficialRegressionCallback, field, time.Now().Unix()).Err()
		if err != nil {
			return r, errorutil.NewErr(err, "[%v] HSet err, key: %v, field: %v",
				funcName, redis.OfficialRegressionCallback, field)
		}
	}
	return
}
