load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "setting",
    srcs = [
        "get.go",
        "upsert.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/gray/setting",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/pkg/dal/db",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/utils",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
