load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "LR",
    srcs = [
        "delete.go",
        "demand_list.go",
        "demand_notify.go",
        "demand_upsert.go",
        "issue.go",
        "rocket_demand_list.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/gray/LR",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/kitex_gen/bytedance/bits/meta",
        "//app/integration/integration/kitex_gen/bytedance/bits/rocket",
        "//app/integration/integration/kitex_gen/ep/rocket/project/pj",
        "//app/integration/integration/kitex_gen/ep/rocket/search",
        "//app/integration/integration/pkg/dal/db",
        "//app/integration/integration/pkg/dal/redis",
        "//app/integration/integration/pkg/handlers/notification_message",
        "//app/integration/integration/pkg/rpc",
        "//app/integration/integration/pkg/service/lark",
        "//app/integration/integration/pkg/service/rocket",
        "//app/integration/integration/pkg/service/user",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/consts",
        "//libs/common_lib/model",
        "//libs/common_lib/utils",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_clientqa_rls_util//errorutil",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_kv_redis_v6//:redis-v6",
    ],
)
