load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "calendar_wrapper",
    srcs = ["calendar_check.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/handlers/gray/calendar_wrapper",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/base",
        "//app/integration/integration/kitex_gen/bytedance/bits/calendar",
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/kitex_gen/bytedance/bits/optimus",
        "//app/integration/integration/pkg/dal/db",
        "//app/integration/integration/pkg/rpc",
        "//app/integration/integration/pkg/service/calendar_microservice",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/utils",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "calendar_wrapper_test",
    srcs = ["calendar_check_test.go"],
    embed = [":calendar_wrapper"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/calendar",
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/pkg/utils",
    ],
)
