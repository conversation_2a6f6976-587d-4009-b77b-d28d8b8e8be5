package period_job

import (
	BytedanceBitsIntegration "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/db"
	"code.byted.org/gopkg/logs"
	"context"
	"time"
)

// CreatePeriodJob implements the IntegrationServiceImpl interface.
func CreatePeriodJob(ctx context.Context, req *BytedanceBitsIntegration.CreatePeriodJobRequest) (*BytedanceBitsIntegration.PeriodJob, error) {
	job := db.PeriodJob{
		Frozen:            0,
		IntegrationAreaId: req.IntegrationAreaID,
		CreateTime:        time.Now(),
		UpdateTime:        time.Now(),
		Name:              req.Name,
		Description:       *req.Description,
		Cron:              req.Cron,
		Type:              req.Type,
		Data:              req.Data,
		Creator:           req.Creator,
	}
	jobId, err := db.BitsWriteDB.InsertPeriodJob(ctx, &job)
	if err != nil {
		logs.CtxError(ctx, "Insert an period job error %v", err)
		return nil, err
	}
	job.Id = jobId
	exportJob := &BytedanceBitsIntegration.PeriodJob{
		Name:          job.Name,
		Description:   &job.Description,
		IntegrationID: job.IntegrationAreaId,
		Type:          job.Type,
		Cron:          job.Cron,
		Creator:       job.Creator,
		Data:          job.Data,
		ID:            job.Id,
		Frozen:        int32(job.Frozen),
		CreateTime:    job.CreateTime.Unix(),
		UpdateTime:    job.UpdateTime.Unix(),
		NextTime:      job.NextTime,
	}
	return exportJob, nil
}
