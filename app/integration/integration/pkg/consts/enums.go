package consts

type IntegrationComponentType int

const (
	IntegrationComponentType_Base    IntegrationComponentType = 0
	IntegrationComponentType_Current IntegrationComponentType = 1
)

type IntegrationTimeType int64

const (
	BeginTime       IntegrationTimeType = 0
	GreyTime        IntegrationTimeType = 1
	PlanPublishTime IntegrationTimeType = 2
	PublishTime     IntegrationTimeType = 3
)

const (
	MrCurrentStateNotStart = "not_start"
	MrCurrentStateRunning  = "running"
	MrCurrentStatePending  = "pending"
	MrCurrentStateFinished = "finished"
)

const (
	MrRepoTypePublicDependency = "public_dependency"
	MrRepoTypeDependency       = "dependency"
	MrRepoTypeHost             = "host"
)

const (
	MrRepoPendingReasonMerging             = "merging"
	MrRepoPendingReasonBlockedByMain       = "blocked_by_main"
	MrRepoPendingReasonBlockedByDependency = "blocked_by_dependency"
	MrRepoPendingReasonExternLock          = "extern_lock"
	MrRepoPendingReasonVersionClose        = "version_close"
)
