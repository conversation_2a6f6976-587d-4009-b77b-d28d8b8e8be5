load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "release",
    srcs = ["release.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/release",
    visibility = ["//visibility:public"],
    deps = [
        "//app/integration/integration/kitex_gen/bytedance/bits/integration",
        "//app/integration/integration/kitex_gen/bytedance/bits/meta",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/appstorereview",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/artifact",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/bitsgray",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/checkpipeline",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/common",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/gprelease",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/misc",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/releaserule",
        "//app/integration/integration/kitex_gen/ep/artifact/manager/slardar",
        "//app/integration/integration/pkg/rpc",
        "//app/integration/integration/pkg/service/user",
        "//app/integration/integration/pkg/utils",
        "//libs/common_lib/consts",
        "//libs/common_lib/utils",
        "//libs/compatibletenancy/emails",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_golang_jwt_jwt//:jwt",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "release_test",
    srcs = ["release_test.go"],
    embed = [":release"],
    deps = [
        "//app/integration/integration/pkg/rpc",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
