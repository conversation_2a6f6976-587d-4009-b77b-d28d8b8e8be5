package integrate

import (
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"time"

	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/utils"

	"code.byted.org/gopkg/logs"
	redisV6 "code.byted.org/kv/redis-v6"
	common_utils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	devtask "code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/dev_task"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/integration"
	"code.byted.org/devinfra/hagrid/app/integration/integration/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/dal/db"
	"code.byted.org/devinfra/hagrid/app/integration/integration/pkg/service/user"
)

func GetGroupNameByAppID(ctx context.Context, appID int64) string {
	groupName := getGroupNameFromCache(ctx, appID)
	if groupName != "" {
		return groupName
	}
	app, err := rpc.MetaClient.QueryAppSimpleInfoByID(ctx, &meta.QueryAppSimpleInfoByIDRequest{
		AppID: appID,
	})
	if err != nil || app == nil || app.AppInfo == nil {
		logs.CtxError(ctx, "AdaptIntegrationToView error: %v", err)
		return ""
	}
	if b, err := json.Marshal(app.AppInfo.EnglishName); err == nil {
		if err = redis.Client.Set(getGroupNameCacheKey(GroupNameCache, appID), b, time.Hour*1).Err(); err != nil {
			common_utils.LogCtxInfo(ctx, "save cache fail, appID = %v", appID)
		}
	}
	return app.AppInfo.EnglishName
}

func getGroupNameFromCache(ctx context.Context, appID int64) string {
	res, err := redis.Client.Get(getGroupNameCacheKey(GroupNameCache, appID)).Result()
	common_utils.LogCtxInfo(ctx, "getGroupNameFromCache: %v, err: %v", res, err)
	if err != nil || err != redisV6.Nil {
		return ""
	}
	return res
}

type GroupNameCacheType string

const (
	GroupNameCache GroupNameCacheType = "group-name-cache-type"
)

func getGroupNameCacheKey(key GroupNameCacheType, appID int64) string {
	return fmt.Sprintf("%v-%v", key, appID)
}

func GetBmsByIntegration(ctx context.Context, dbItem *db.Integration) (version string, qaBMs, rdBMs, daBMs, crashBMs []string, customBmMap map[int64][]string, err error) {
	groupName := GetGroupNameByAppID(ctx, dbItem.UniqueId)
	if groupName == "" {
		err = fmt.Errorf("unexpected groupName")
		logs.CtxInfo(ctx, "not found group_name, appID = %v,groupName = %v", dbItem.UniqueId, dbItem.Name)
		return
	}
	bms := utils.GetBMInfo(ctx, groupName, dbItem.Version, dbItem.UniqueId)
	if bms == nil {
		return
	}
	version = bms.Version
	qaBMs = bms.QaBms
	rdBMs = bms.RdBms
	daBMs = bms.DaBms
	crashBMs = bms.CrashBms
	customBmMap = bms.CustomBms
	return
}

func AdaptIntegrationToView(ctx context.Context, dbItem *db.Integration) *integration.Integration {
	if dbItem == nil {
		return nil
	}

	qaBms := make([]string, 0)
	rdBms := make([]string, 0)
	crashBms := make([]string, 0)
	daBms := make([]string, 0)

	groupName := GetGroupNameByAppID(ctx, dbItem.UniqueId)
	if groupName == "" {
		logs.CtxInfo(ctx, "not found group_name, appID %v", dbItem.UniqueId)
		return nil
	}

	t := time.Now()
	bms := utils.GetBMInfo(ctx, groupName, dbItem.Version, dbItem.UniqueId)
	if bms != nil {
		rdBms = bms.RdBms
		qaBms = bms.QaBms
		crashBms = bms.CrashBms
		daBms = bms.DaBms
	}
	getBMInfoElapsed := time.Since(t)
	if getBMInfoElapsed.Milliseconds() > 100 {
		logs.CtxInfo(ctx, "getBMInfoElapsed %v", getBMInfoElapsed)
	}

	qa := make([]string, 0)
	_ = json.Unmarshal([]byte(dbItem.Qa), &qa)

	creatorUser, _ := user.QueryEmployee(ctx, dbItem.Creator)
	rdBmUsers, _ := user.GetUserInfos(ctx, rdBms)
	qaBmUsers, _ := user.GetUserInfos(ctx, qaBms)
	crashBmUsers, _ := user.GetUserInfos(ctx, crashBms)
	daBmUsers, _ := user.GetUserInfos(ctx, daBms)
	qaUsers, _ := user.GetUserInfos(ctx, qa)

	larkGroup := devtask.LarkGroup{}
	json.Unmarshal([]byte(dbItem.ExtInfo), &larkGroup)

	workflowStatus := integration.IntegrationWorkflowStatus(dbItem.WorkflowStatus)

	// totalCount, statusCount, _ := GetHistoryCountAndIntegratedCount(ctx, dbItem.Id) // 不查 dev_task 了，后面改成 optimus

	item := integration.Integration{
		ID:              dbItem.Id,
		UniqueID:        dbItem.UniqueId,
		UniqueType:      devtask.DevTaskUniqueType(dbItem.UniqueType),
		TechStack:       integration.TechnologyStack(dbItem.TechStack),
		IntegrationType: integration.IntegrationType(dbItem.IntegrationType),
		ReleaseType:     integration.ReleaseType(dbItem.ReleaseType),
		Name:            dbItem.Name,
		Description:     &dbItem.Description,
		Version:         dbItem.Version,
		BaseBranch:      dbItem.BaseBranch,
		MergeBranch:     dbItem.MergeBranch,
		GitlabProjectID: dbItem.GitlabProjectId,
		Status:          integration.IntegrationStatus(dbItem.Status),
		Creator:         &dbItem.Creator,
		Rds:             rdBms,
		Qas:             qaBms,
		CreateTime:      dbItem.Ctime.UnixNano() / int64(time.Millisecond),
		BeginTime:       dbItem.BeginTime,
		FreezeTime:      dbItem.FreezeTime,
		GreyTime:        dbItem.GreyTime,
		PlanPublishTime: dbItem.PlanPublishTime,
		PublishTime:     dbItem.PublishTime,
		CreatorUser:     user.EmployeeAdaptor(creatorUser),
		RdBmUsers:       user.EmployeesAdaptor(rdBmUsers),
		QaBmUsers:       user.EmployeesAdaptor(qaBmUsers),
		CrashBmUsers:    user.EmployeesAdaptor(crashBmUsers),
		QaUsers:         user.EmployeesAdaptor(qaUsers),
		DaBmUsers:       user.EmployeesAdaptor(daBmUsers),
		// TaskTotalCount:      totalCount,
		// TaskIntegratedCount: statusCount[devtask.DevTaskStatusIntegrated],
		IsFreeze:       dbItem.IsFreeze == 1,
		LarkGroup:      &larkGroup,
		GreyEndTime:    dbItem.GreyEndTime,
		WorkflowStatus: &workflowStatus,
		Labels:         common_utils.SpliteByComma(dbItem.Labels),
	}
	return &item
}

func AdaptIntegrationToSimpleView(ctx context.Context, dbItem *db.Integration) *integration.IntegrationSimpleInfo {
	if dbItem == nil {
		return nil
	}

	return &integration.IntegrationSimpleInfo{
		ID:             dbItem.Id,
		UniqueID:       dbItem.UniqueId,
		UniqueType:     devtask.DevTaskUniqueType(dbItem.UniqueType),
		Version:        dbItem.Version,
		Status:         integration.IntegrationStatus(dbItem.Status),
		WorkflowStatus: integration.IntegrationWorkflowStatusPtr(integration.IntegrationWorkflowStatus(dbItem.WorkflowStatus)),
		Labels:         common_utils.SpliteByComma(dbItem.Labels),
	}
}
