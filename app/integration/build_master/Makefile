idlpath = $(GOPATH)/src/code.byted.org/cpputil/service_rpc_idl
bits_idlpath = $(GOPATH)/src/code.byted.org/bits/bits-idl
selfpsm = "bytedance.bits.build_master"
selfmodule = "code.byted.org/bits/build_master"

rpc:
	kitex -disable-self-update -module $(selfmodule) -service $(selfpsm) $(bits_idlpath)/bits/build_master.thrift

# 生成db层代码
db:
	build_tools --target=orm --ddlpath=pkg/dal/db/ddl/cony.sql --package=db > pkg/dal/db/cony_tables.go && gofmt -w pkg/dal/db/cony_tables.go
	build_tools --target=orm_call --package=./pkg/dal/db

all: rpc db

test:export KMS_ZONE=boe
test:export TESTING_PREFIX=offline
test:export PROJECT_ROOT_DIRECTORY=$(current_directory)
test:
	@go test -failfast -coverprofile=coverage.out `go list ./pkg/... | grep -v conf`
ifeq ($(coverage), yes)
	@go tool cover -func=coverage.out
endif
