package bitscli

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/app/rolloutatoms/appcenter_atoms/adapter"
	"code.byted.org/devinfra/hagrid/app/rolloutatoms/rolloutv1atoms/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/input/biz/stub/bits"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
)

const PipelineNamePrefix = "rollout pipeline release"

type bitsClient struct {
	jwt      string
	username string
}

type BitsPipelineClient interface {
	CreateAndStartPipeline(ctx context.Context, pId string, cfg *app.ReleasePipelineConfig) (*BitsRunResponse, error)
	GetPipelineRuns(ctx context.Context, param *BitsRunSeqStatusParams) (*BitsRunSeqStatus, error)
}

type BitsRunResponse struct {
	PipelineID  string
	PipelineURL string
	RunSeq      int64
}

type BitsRunSeqStatusParams struct {
	PipelineID string
	RunSeq     int64
}

type BitsRunSeqStatus struct {
	RunStatus   dslpb.PipelineRunStatus
	PipelineURL string
}

func NewBitsCli(ctx context.Context, buildContext adapter.BuildContext) (BitsPipelineClient, error) {
	var ijErr error
	ctx, ijErr = ctxutil.InjectContext(ctx, buildContext)
	if ijErr != nil {
		return nil, errors.Wrap(ijErr, "failed to inject username and jwt into context")
	}

	username, err := ctxutil.GetUsername(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get username from the context")
	}
	jwt, err := ctxutil.GetJWTTokenByCtx(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get jwt from the context")
	}

	return &bitsClient{
		jwt:      jwt,
		username: username,
	}, nil
}

func (b *bitsClient) CreateAndStartPipeline(ctx context.Context, pId string, cfg *app.ReleasePipelineConfig) (*BitsRunResponse, error) {
	bitsCli := bits.NewApiServer()
	pipelineName := fmt.Sprintf("%s_%s", PipelineNamePrefix, pId)

	spaceID, pErr := strconv.ParseInt(cfg.WorkspaceId, 10, 64)
	if pErr != nil {
		return nil, fmt.Errorf("invalid workspace id: %s", cfg.WorkspaceId)
	}
	pipelineID, cErr := bitsCli.CreatePipelineFromTemplate(ctx,
		bits.CreatePipelineFromTemplateParams{
			TemplateID:   *cfg.PipelineTemplateId,
			SpaceID:      spaceID,
			PipelineName: pipelineName,
			TemplateVars: makeBitsVars(cfg.Vars),
		}, b.jwt, b.username)

	if cErr != nil {
		return nil, fmt.Errorf("error creating pipeline: %w", cErr)
	}

	runDetail, rErr := bitsCli.RunPipelineFullDetail(ctx, bits.RunPipelineParams{
		PipelineID:   pipelineID,
		TemplateVars: makeBitsVars(cfg.Vars),
	}, b.jwt, b.username)

	if rErr != nil {
		return nil, fmt.Errorf("error creating pipeline: %w", rErr)
	}

	return &BitsRunResponse{
		pipelineID,
		runDetail.PipelineRun.PipelineURL,
		runDetail.PipelineRun.RunSeq,
	}, nil
}

func (b *bitsClient) GetPipelineRuns(ctx context.Context, param *BitsRunSeqStatusParams) (*BitsRunSeqStatus, error) {
	bitsCli := bits.NewApiServer()

	pipelineID, pErr := strconv.ParseInt(param.PipelineID, 10, 64)
	if pErr != nil {
		return nil, fmt.Errorf("invalid pipelineID: %s", param.PipelineID)
	}
	runDetails, gErr := bitsCli.GetPipelineRuns(ctx, bits.GetPipelineRunsParams{
		PipelineID: pipelineID,
		PageNum:    1,
		PageSize:   10,
	}, b.jwt, b.username)

	if gErr != nil {
		return nil, fmt.Errorf("error getting pipeline runseq detail: %w", gErr)
	}

	for _, runDetail := range runDetails {
		if runDetail.RunSeq == param.RunSeq {
			return &BitsRunSeqStatus{
				RunStatus:   dslpb.PipelineRunStatus(runDetail.RunStatus),
				PipelineURL: runDetail.PipelineURL,
			}, nil
		}
	}

	return nil, fmt.Errorf("could not find pipeline runseq %d", param.RunSeq)
}

func makeBitsVars(releaseVars []*app.ReleasePipelineVar) []*bits.Var {
	var bitsVars []*bits.Var
	for _, rVar := range releaseVars {
		bitsVars = append(bitsVars, &bits.Var{
			Name: rVar.VarName,
			Value: bits.Value{
				// confirm if text is okay
				Text: rVar.VarValue,
			},
		})
	}

	return bitsVars
}
