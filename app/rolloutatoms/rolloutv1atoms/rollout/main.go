package main

import (
	"context"

	"code.byted.org/bytefaas/faas-go/bytefaas"
	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/devops/devops_go_faas/devops/v0"
)

type plugin struct{}

// 用于在编译阶段检查类是否实现了全部接口
var _ devops.PluginMethod = (*plugin)(nil)

func httpHandler(ctx context.Context, r *events.HTTPRequest) (eventReturn *events.EventResponse, rtnError error) {

	statusCode, retBody := devops.Dispatcher(ctx, r.Body, r.Path)

	return &events.EventResponse{
		StatusCode: statusCode,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: retBody,
	}, nil
}

func main() {
	// register implement
	devops.RegisterPlugin(new(plugin))
	// Make the handler available for Http Call by ByteFaaS Function
	bytefaas.Start(httpHandler)
}
