package bc_migration

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/bytedance/sonic"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/migrationtool/biz/constants"
	"code.byted.org/devinfra/hagrid/app/migrationtool/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/bc_migrationpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/templatepb"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
)

var (
	featureSpaceType = "continuous_delivery"
	trainSpaceType   = "release_train"
	templateErrFmt   = "%d: %v"

	CellMaxCharacterCount = 45000
)

func (w *BCMigrationServiceImpl) BCMigratePreCheck(ctx context.Context, req *bc_migrationpb.BCMigratePreCheckReq) (resp *bc_migrationpb.BCMigratePreCheckResp, err error) {
	g := errgroup.Group{}
	mu := sync.Mutex{}
	g.SetLimit(5)

	ctx = utils.WithJWT(ctx, req.Jwt)
	ctx = utils.WithUsername(ctx, req.Username)
	allTemplateIDs := make([]uint64, 0)
	workspaceLabelMap := make(map[uint64]string)
	workspaceUseNewVarMap := make(map[uint64]bool)
	spaceTemplateIDMap := make(map[uint64][]uint64)
	featureSpaceIDs := make([]uint64, 0)
	trainSpaceIDs := make([]uint64, 0)
	invalidSpaceIDs := make([]uint64, 0)
	invalidSpaceMap := make(map[uint64]string)
	spaceNonGoofyWebProjects := make(map[uint64]map[uint64]string)
	spaceConflictGoofyProjects := make(map[uint64]map[uint64][]uint64)
	spaceMultiGoofyProjects := make(map[uint64][]uint64)
	spaceSingleGoofyProjects := make(map[uint64][]uint64)
	spaceGoofyAPIErrMsg := make(map[uint64][]string)
	// 获取空间下的模板信息
	if len(req.GetSpaceIds()) != 0 {
		workspaceInfo, err := w.workspaceDao.BatchGetByIDs(ctx, req.GetSpaceIds())
		if err != nil {
			return nil, err
		}
		workspaceNewVarRecords, err := w.workspaceUpgradeVarDao.BatchGetByIDs(ctx, req.GetSpaceIds())
		if err != nil {
			return nil, erri.Error(err)
		}

		for _, record := range workspaceNewVarRecords {
			workspaceUseNewVarMap[uint64(record.WorkspaceID)] = true
		}

		for _, spaceInfo := range workspaceInfo {
			workspaceLabelMap[spaceInfo.ID] = spaceInfo.Label
			if spaceInfo.Type == featureSpaceType {
				featureSpaceIDs = append(featureSpaceIDs, spaceInfo.ID)
				continue
			} else if spaceInfo.Type == trainSpaceType {
				trainSpaceIDs = append(trainSpaceIDs, spaceInfo.ID)
				continue
			}
			invalidSpaceIDs = append(invalidSpaceIDs, spaceInfo.ID)
		}

		logs.CtxInfo(ctx, "featureSpaceIDs: %v, trainSpaceIDs: %v", featureSpaceIDs, trainSpaceIDs)

		// 获取不同的空间下的模板
		for _, sID := range trainSpaceIDs {
			spaceID := sID
			g.Go(func() error {
				tceMap, webMap, webFilterRes, innerErr := w.GetRecentlyTrainReleasePsmTemplate(ctx, &entity.DBBcFlowToBisMigrationRecord{BcSpaceId: int64(spaceID)})
				if innerErr != nil {
					return innerErr
				}
				list, innerErr := w.bytecycleCli.GetWorkflowSDKV1().GetWorkflowList(ctx, "", strconv.FormatInt(int64(spaceID), 10), 2, 1)
				if innerErr != nil {
					return innerErr
				}
				logs.CtxInfo(ctx, "tceMap for train_space_id=%d, is %v", spaceID, tceMap)
				templateIDs := gslice.Union(gslice.Map(gmap.Keys(tceMap), func(id int64) uint64 { return uint64(id) }),
					gslice.Map(gmap.Keys(webMap), func(id int64) uint64 { return uint64(id) }))
				nodeNameTemplateIDMap := make(map[string]int64)
				invalidMsg := ""
				for _, item := range list.WorkflowList {
					meta := &FeatureWorkflowMeta{}
					if innerErr = sonic.Unmarshal(item.RawMessage, meta); innerErr != nil {
						return erri.Error(innerErr)
					}
					for phase := range constants.TrainActivityPhase {
						if nodeContext, ok := meta.NodeContext[phase]; ok {
							nodeNameTemplateIDMap[constants.TrainActivityPhaseToNodeNameMap[phase]] = nodeContext.Template.Id
						} else {
							msg := fmt.Sprintf("get train workflow list fail, train workflow stage %s not exists, space_id=%d, ,rawMessage:%s \n", phase, spaceID, string(item.RawMessage))
							logs.CtxError(ctx, msg)
							invalidMsg = invalidMsg + msg
							continue
						}
					}
				}
				listTemplateIDs := gslice.Map(gmap.Values(nodeNameTemplateIDMap), func(id int64) uint64 { return uint64(id) })
				templateIDs = gslice.Union(templateIDs, listTemplateIDs)

				singleGoofy := make([]uint64, 0)
				multiGoofy := make([]uint64, 0)
				if len(webFilterRes.NormalWebProject) != 0 {
					for projID, goofyIDs := range webFilterRes.NormalWebProject {
						if len(goofyIDs) == 1 {
							singleGoofy = append(singleGoofy, projID)

						} else {
							multiGoofy = append(multiGoofy, projID)
						}
					}
				}

				mu.Lock()
				defer mu.Unlock()
				spaceTemplateIDMap[spaceID] = templateIDs
				spaceNonGoofyWebProjects[spaceID] = webFilterRes.NonGoofyProjects
				spaceMultiGoofyProjects[spaceID] = multiGoofy
				spaceSingleGoofyProjects[spaceID] = singleGoofy
				spaceConflictGoofyProjects[spaceID] = webFilterRes.ConflictProjects
				spaceGoofyAPIErrMsg[spaceID] = webFilterRes.APIErrMsg
				allTemplateIDs = gslice.Union(allTemplateIDs, templateIDs)
				if len(invalidMsg) != 0 {
					invalidSpaceMap[spaceID] = invalidMsg
				}
				return nil
			})
		}

		for _, sID := range featureSpaceIDs {
			spaceID := sID
			g.Go(func() error {
				tceMap, webMap, filterRes, innerErr := w.FindMostUsedTemplateInFeatureWorkflow(ctx, &entity.DBBcFlowToBisMigrationRecord{BcSpaceId: int64(spaceID)})
				if innerErr != nil {
					return innerErr
				}
				list, innerErr := w.bytecycleCli.GetWorkflowSDKV1().GetWorkflowList(ctx, "", strconv.FormatInt(int64(spaceID), 10), 2, 1)
				if innerErr != nil {
					logs.CtxError(ctx, "get workflow list failed, err:%s", err)
					return bits_err.BC_MIGRATION.ErrCallApiFail.SetMsg("get workflow list failed")
				}
				logs.CtxInfo(ctx, "tceMap for feature_space_id=%d, is %v", spaceID, tceMap)
				nodeNameTemplateIDMap := make(map[string]int64)
				//invalidMsg := ""
				for _, item := range list.WorkflowList {
					meta := &FeatureWorkflowMeta{}
					if innerErr = sonic.Unmarshal(item.RawMessage, meta); innerErr != nil {
						return erri.Error(innerErr)
					}
					for stage := range constants.FeatureStageMap {
						if nodeContext, ok := meta.NodeContext[stage]; ok {
							nodeNameTemplateIDMap[stage] = nodeContext.Template.Id
						} else {
							//msg := fmt.Sprintf("get feature workflow list fail, feature workflow stage %s not exists, space_id=%d, rawMessage:%s \n", stage, spaceID, string(item.RawMessage))
							//logs.CtxError(ctx, msg)
							//invalidMsg = invalidMsg + msg
							continue
						}
					}
				}
				templateIDs := gslice.Union(gslice.Map(gmap.Keys(tceMap), func(id int64) uint64 { return uint64(id) }),
					gslice.Map(gmap.Keys(webMap), func(id int64) uint64 { return uint64(id) }))
				listTemplateIDs := gslice.Map(gmap.Values(nodeNameTemplateIDMap), func(id int64) uint64 { return uint64(id) })
				templateIDs = gslice.Union(templateIDs, listTemplateIDs)

				singleGoofy := make([]uint64, 0)
				multiGoofy := make([]uint64, 0)
				if len(filterRes.NormalWebProject) != 0 {
					for projID, goofyIDs := range filterRes.NormalWebProject {
						if len(goofyIDs) == 1 {
							singleGoofy = append(singleGoofy, projID)
						} else {
							multiGoofy = append(multiGoofy, projID)
						}
					}
				}

				mu.Lock()
				defer mu.Unlock()
				spaceTemplateIDMap[spaceID] = templateIDs
				allTemplateIDs = gslice.Union(allTemplateIDs, templateIDs)
				spaceNonGoofyWebProjects[spaceID] = filterRes.NonGoofyProjects
				spaceMultiGoofyProjects[spaceID] = multiGoofy
				spaceSingleGoofyProjects[spaceID] = singleGoofy
				spaceConflictGoofyProjects[spaceID] = filterRes.ConflictProjects
				spaceGoofyAPIErrMsg[spaceID] = filterRes.APIErrMsg
				return nil
			})
		}

		if err = g.Wait(); err != nil {
			return nil, erri.Error(err)
		}
	}

	if len(req.GetTemplateIds()) != 0 {
		allTemplateIDs = gslice.Union(allTemplateIDs, req.GetTemplateIds())
	}

	logs.CtxInfo(ctx, "precheck template_ids=%v", allTemplateIDs)

	templateRes, err := w.oreoCli.MigratePipelineTemplatePreCheck(ctx, &templatepb.MigratePipelineTemplatePreCheckReq{
		TemplateIds: allTemplateIDs,
		Username:    req.GetUsername(),
		Jwt:         req.GetJwt(),
	})
	if err != nil {
		return nil, erri.Error(err)
	}

	resMap := make(map[uint64]*bc_migrationpb.TemplatePreCheckItem)
	spaceResults := make([]*bc_migrationpb.SpacePreCheckItem, 0)
	templateResults := make([]*bc_migrationpb.TemplatePreCheckItem, 0)
	for _, res := range templateRes.TemplateResults {
		resMap[res.TemplateId] = res
	}

	// 组装结果
	for _, tid := range req.GetTemplateIds() {
		res, ok := resMap[tid]
		if ok {
			templateResults = append(templateResults, res)
		}
	}
	for _, spaceID := range gslice.Concat(featureSpaceIDs, trainSpaceIDs) {
		spaceLabel := workspaceLabelMap[spaceID]
		useNewVar := workspaceUseNewVarMap[spaceID]
		spaceRes := &bc_migrationpb.SpacePreCheckItem{
			SpaceId:         spaceID,
			SpaceName:       spaceLabel,
			TemplateResults: make([]*bc_migrationpb.TemplatePreCheckItem, 0),
			UseNewVar:       useNewVar,
		}
		tids := spaceTemplateIDMap[spaceID]
		for _, tid := range tids {
			tRes, ok := resMap[tid]
			if ok {
				tRes.TemplateUrl = genTemplateURL(tid, spaceLabel)
				spaceRes.TemplateResults = append(spaceRes.TemplateResults, tRes)
			}
		}
		mids, ok := spaceMultiGoofyProjects[spaceID]
		if ok {
			spaceRes.MultipleGoofyDeployProjectIds = mids
		}
		pids, ok := spaceNonGoofyWebProjects[spaceID]
		if ok {
			spaceRes.NonGoofyDeployWebProject = pids
		}

		// spaceRes.TemplateResults 按照 id 从小到大排序
		gslice.SortBy(spaceRes.TemplateResults, func(a, b *bc_migrationpb.TemplatePreCheckItem) bool {
			return a.TemplateId < b.TemplateId
		})
		spaceResults = append(spaceResults, spaceRes)
	}

	resp = &bc_migrationpb.BCMigratePreCheckResp{
		SpaceResults:    spaceResults,
		TemplateResults: templateResults,
		InvalidSpaceIds: gmap.Keys(invalidSpaceMap),
	}

	AppendFeishu(ctx, &FeishuOutput{
		resp:                         resp,
		spaceAvailableWebProjectsMap: spaceSingleGoofyProjects,
		spaceConflictProjectsMap:     spaceConflictGoofyProjects,
		spaceGoofyAPIErrMsg:          spaceGoofyAPIErrMsg,
	}, invalidSpaceMap)

	return resp, nil
}

func genTemplateURL(templateID uint64, workspaceLabel string) string {
	prefix := choose.If(env.IsBoe(), "https://bytecycle-boe.bytedance.net", "https://bytecycle.bytedance.net")
	return fmt.Sprintf("%s/space/%s/module/pipeline/templates/%d/details",
		prefix, workspaceLabel, templateID)
}

func genWebProjectURL(projectID uint64) string {
	prefix := choose.If(env.IsBoe(), "https://bytecycle-boe.bytedance.net", "https://bytecycle.bytedance.net")
	return fmt.Sprintf("%s/home/<USER>/projects/%d",
		prefix, projectID)
}

type FeishuOutput struct {
	resp                         *bc_migrationpb.BCMigratePreCheckResp
	spaceAvailableWebProjectsMap map[uint64][]uint64
	spaceConflictProjectsMap     map[uint64]map[uint64][]uint64
	spaceGoofyAPIErrMsg          map[uint64][]string
}

// AppendFeishu 将结果回写到飞书文档
func AppendFeishu(ctx context.Context, output *FeishuOutput, invalidSpaceMap map[uint64]string) {
	// 获取 feishu token 用来操作飞书文档
	logs.CtxInfo(ctx, "start send feishu message")
	token, err := GetToken(ctx)
	if err != nil {
		logs.CtxError(ctx, "")
		return
	}
	logs.CtxInfo(ctx, "feishu: token got = %s!", token)

	targetRows := make([][]string, 0)

	resp := output.resp

	for _, item := range resp.GetSpaceResults() {
		row := make([]string, 0)
		row = append(row, strconv.FormatUint(item.GetSpaceId(), 10))
		row = append(row, item.GetSpaceName())

		useNewVar := choose.If(item.UseNewVar, "是", "否")
		row = append(row, useNewVar)
		row = append(row, invalidSpaceMap[item.GetSpaceId()])

		errMultipleGoofy := gslice.Map(item.MultipleGoofyDeployProjectIds, func(id uint64) string {
			return strconv.FormatUint(id, 10)
		})
		errNonGoofy := make([]string, 0)
		allErrProjectIDs := item.MultipleGoofyDeployProjectIds
		for projID, bindPlatform := range item.NonGoofyDeployWebProject {
			errNonGoofy = append(errNonGoofy, fmt.Sprintf("%d:%s", projID, bindPlatform))
			allErrProjectIDs = append(allErrProjectIDs, projID)
		}
		errConflictGoofy := make([]string, 0)
		for k, v := range output.spaceConflictProjectsMap[item.GetSpaceId()] {
			errConflictGoofy = append(errConflictGoofy, fmt.Sprintf("%d:%v", k, v))
			allErrProjectIDs = gslice.Union(allErrProjectIDs, v)
		}
		errWevProjURLs := gslice.Map(gslice.Uniq(allErrProjectIDs), func(id uint64) string {
			return genWebProjectURL(id)
		})

		spaceSingleGoofyProjects, ok := output.spaceAvailableWebProjectsMap[item.GetSpaceId()]
		if !ok {
			spaceSingleGoofyProjects = make([]uint64, 0)
		}
		spaceSingleGoofyProjectsStr := gslice.Map(spaceSingleGoofyProjects, func(id uint64) string {
			return strconv.FormatUint(id, 10)
		})

		errGoofyAPI, ok := output.spaceGoofyAPIErrMsg[item.GetSpaceId()]
		if !ok {
			errGoofyAPI = make([]string, 0)
		}

		errTemplateIDs := make([]string, 0)
		errTemplateURLs := make([]string, 0)
		notFoundAtoms := make([]string, 0)
		invalidAtoms := make([]string, 0)
		unsupportedSysVars := make([]string, 0)
		invalidVarNames := make([]string, 0)
		buildContextVars := make([]string, 0)
		contextParamsVars := make([]string, 0)
		dslErrors := make([]string, 0)
		skipOnFailVars := make([]string, 0)
		multipleTCE := make([]string, 0)
		multipleGoofy := make([]string, 0)
		rawResults := make([]string, 0)

		for _, checkItem := range item.GetTemplateResults() {
			errTemplateIDs = append(errTemplateIDs, strconv.FormatUint(checkItem.GetTemplateId(), 10))
			errTemplateURLs = append(errTemplateURLs, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetTemplateUrl()))
			if len(checkItem.GetNotFoundAtoms()) != 0 {
				notFoundAtoms = append(notFoundAtoms, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetNotFoundAtoms()))
			}
			if len(checkItem.GetInvalidAtoms()) != 0 {
				invalidAtoms = append(invalidAtoms, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetInvalidAtoms()))
			}
			if len(checkItem.GetUnsupportedSysVars()) != 0 {
				unsupportedSysVars = append(unsupportedSysVars, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetUnsupportedSysVars()))
			}
			if len(checkItem.GetInvalidVarNames()) != 0 {
				invalidVarNames = append(invalidVarNames, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetInvalidVarNames()))
			}
			if len(checkItem.GetVarsOnInvalidFields()) != 0 {
				skipOnFailVars = append(skipOnFailVars, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetVarsOnInvalidFields()))
			}
			if len(checkItem.GetBuildContextVars()) != 0 {
				buildContextVars = append(buildContextVars, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetBuildContextVars()))
			}
			if len(checkItem.GetContextParamsVars()) != 0 {
				contextParamsVars = append(contextParamsVars, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetContextParamsVars()))
			}
			if len(checkItem.GetInvalidDsl()) != 0 {
				dslErrors = append(dslErrors, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetInvalidDsl()))
			}
			if len(checkItem.GetMultipleTceDeployAtoms()) != 0 {
				multipleTCE = append(multipleTCE, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetMultipleTceDeployAtoms()))
			}
			if len(checkItem.GetMultipleGoofyDeployAtoms()) != 0 {
				multipleGoofy = append(multipleGoofy, fmt.Sprintf(templateErrFmt, checkItem.GetTemplateId(), checkItem.GetMultipleGoofyDeployAtoms()))
			}

			c := jsoniter.Config{
				EscapeHTML: false,
			}.Froze()
			text, err2 := c.MarshalToString(checkItem)
			if err2 != nil {
				continue
			}
			rawResults = append(rawResults, text)
		}

		loc, _ := time.LoadLocation("Asia/Shanghai")

		trimAndAppend := func(ss []string) {
			joined := strings.Join(ss, "\n")
			if len(joined) > CellMaxCharacterCount {
				joined = joined[:CellMaxCharacterCount]
			}
			row = append(row, joined)
		}
		trimAndAppend(errTemplateIDs)
		trimAndAppend(errTemplateURLs)
		trimAndAppend(spaceSingleGoofyProjectsStr)
		trimAndAppend(errMultipleGoofy)
		trimAndAppend(errNonGoofy)
		trimAndAppend(errConflictGoofy)
		trimAndAppend(errGoofyAPI)
		trimAndAppend(errWevProjURLs)
		trimAndAppend(notFoundAtoms)
		trimAndAppend(invalidAtoms)
		trimAndAppend(unsupportedSysVars)
		trimAndAppend(invalidVarNames)
		trimAndAppend(buildContextVars)
		trimAndAppend(contextParamsVars)
		trimAndAppend(skipOnFailVars)
		trimAndAppend(dslErrors)
		trimAndAppend(multipleTCE)
		trimAndAppend(multipleGoofy)
		//trimAndAppend(rawResults)
		row = append(row, time.Now().In(loc).Format("2006-01-02 15:04:05"))

		targetRows = append(targetRows, row)
	}
	if len(targetRows) == 0 {
		return
	}

	// 每次更新 100 行 targetRows ，sleep 500 ms 之后再更新下一 100 行
	for i := 0; i < len(targetRows); i += 100 {
		end := i + 100
		if end > len(targetRows) {
			end = len(targetRows)
		}
		values := make([][][]Elem, 0)
		for _, s := range targetRows[i:end] {
			row := make([][]Elem, 0)
			for _, sr := range s {
				row = append(row, []Elem{BuildElem(sr)})
			}
			values = append(values, row)
		}
		err = AppendFeishuRows(ctx, token, spreadsheetToken, spaceSheetID, values)
		if err != nil {
			logs.CtxError(ctx, erri.Error(err).Error())
		}
		time.Sleep(500 * time.Millisecond)
	}

	logs.CtxInfo(ctx, "feishu: message send!")

	return

}
