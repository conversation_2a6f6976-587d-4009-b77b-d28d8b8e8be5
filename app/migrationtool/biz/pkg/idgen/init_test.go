package idgen

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"
)

type InitTestSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
}

func (t *InitTestSuite) setupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
}

func (t *InitTestSuite) tearDownTest() {
	t.cancel()
}

func TestInit(t *testing.T) {
	MustInitialize(&Config{
		Namespace: "bits_hagrid",
	})
	suite.Run(t, new(InitTestSuite))
}

func (t *InitTestSuite) TestMustInitialize() {
	id, _ := Client.Get(context.TODO())
	t.IsType(uint64(0), id)
}

func (t *InitTestSuite) TestGetID() {
	id, _ := GetID(context.TODO())
	t.IsType(uint64(0), id)
}

func (t *InitTestSuite) TestBatchGetIDs() {
	ids, _ := GetBatchIds(context.TODO(), 2)
	t.<PERSON>(ids, 2)
}
