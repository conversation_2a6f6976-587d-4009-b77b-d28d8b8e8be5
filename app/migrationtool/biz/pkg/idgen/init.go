package idgen

import (
	"context"

	"code.byted.org/gopkg/idgenerator/v2"
	"code.byted.org/gopkg/logs"
)

var (
	Client idgenerator.NtIdGenerator
)

func MustInitialize(config *Config) {
	var err error
	Client, err = idgenerator.NewNtIdGeneratorBuilder().
		WithNamespace(config.Namespace).
		Build()
	if err != nil {
		logs.Error("id generator client init fail, err: %v", err)
		panic(err)
	}
}

func GetBatchIds(ctx context.Context, count int) ([]uint64, error) {
	ids, err := Client.MGet(ctx, count)
	if err != nil {
		return nil, err
	}

	res := make([]uint64, 0, count)

	iter := ids.NewIterator()
	for iter.Next() {
		res = append(res, iter.Id())
	}
	return res, nil
}

func GetID(ctx context.Context) (uint64, error) {
	return Client.Get(ctx)
}
