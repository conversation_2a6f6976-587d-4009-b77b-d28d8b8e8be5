package entity

import (
	"time"

	"gorm.io/datatypes"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/bc_migrationpb"
)

// DBBcFlowToBisMigrationRecord bc研发流程迁移记录表
type DBBcFlowToBisMigrationRecord struct {
	ID                      int64                            `json:"id" gorm:"id"`                                                   // 自增ID
	BcScene                 bc_migrationpb.BcScene           `json:"bc_scene" gorm:"bc_scene"`                                       // BC研发流程类型
	WorkspaceId             int64                            `json:"workspace_id" gorm:"workspace_id"`                               // 空间ID
	BcSpaceId               int64                            `json:"bc_space_id" gorm:"bc_space_id"`                                 // BC空间ID
	BcDevTemplateNodeId     int64                            `json:"bc_dev_template_node_id" gorm:"bc_dev_template_node_id"`         // 迁移的TCE项目节点ID
	BcReleaseTemplateNodeId int64                            `json:"bc_release_template_node_id" gorm:"bc_release_template_node_id"` // 迁移的TCE项目节点ID
	BcDevTemplate           string                           `json:"bc_dev_template" gorm:"bc_dev_template"`                         // 非发布阶段工单模板
	BcReleaseTemplate       string                           `json:"bc_release_template" gorm:"bc_release_template"`                 // 发布阶段工单模板
	PipelineId              int64                            `json:"pipeline_id" gorm:"pipeline_id"`                                 // 迁移流水线id
	BcFeatureWorkflowIDs    string                           `json:"bc_feature_workflow_ids" gorm:"feature_workflow_ids"`            // 需求流程id
	Status                  bc_migrationpb.BcMigrationStatus `json:"status" gorm:"status"`                                           // 迁移状态
	Creator                 string                           `json:"creator" gorm:"creator"`                                         // 创建人
	CreatedAt               time.Time                        `json:"created_at" gorm:"created_at"`                                   // 创建时间
	BcTrainWorkflowTypes    datatypes.JSON                   `json:"bc_train_workflow_types" gorm:"bc_train_workflow_types,type:json"`
}

// TableName 表名称
func (*DBBcFlowToBisMigrationRecord) TableName() string {
	return "hagrid_bc_flow_to_bis_migration_record"
}
