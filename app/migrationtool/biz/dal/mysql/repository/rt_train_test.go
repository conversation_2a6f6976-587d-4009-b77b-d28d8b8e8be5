package repository

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/migrationtool/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/migrationtool/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/migrationtool/testfactory"
)

func Test_rtTrainDao(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.RtTrain{})
	mysql.DefaultDB = db
	dao := NewTRTrainDao()

	list, total, err := dao.List(ctx, &entity.ListRtTrainParam{
		WorkspaceID: 1,
		Status:      "completed",
		CreateAt:    1,
		Page:        1,
		PageSize:    1,
		Count:       true,
	})
	assert.NoError(t, err)
	assert.Equal(t, 0, len(list))
	assert.Equal(t, int64(0), total)

}
