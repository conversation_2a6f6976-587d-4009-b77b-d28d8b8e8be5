load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "repository",
    srcs = [
        "bc_flow_to_bits_migration_record.go",
        "bc_flow_to_bits_migration_result.go",
        "devops_running_workspace.go",
        "devops_running_workspace_upgrade_var.go",
        "hagrid_bc_flow_to_bis_migration_train_workspace_info.go",
        "march_project_meta.go",
        "project.go",
        "project_attr.go",
        "project_template.go",
        "rt_create_train_cron_job_config.go",
        "rt_train.go",
        "rt_train_snapshot.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/migrationtool/biz/dal/mysql/repository",
    visibility = ["//visibility:public"],
    deps = [
        "//app/migrationtool/biz/dal/mysql",
        "//app/migrationtool/biz/dal/mysql/entity",
        "@org_byted_code_iesarch_cdaas_utils//erri",
    ],
)

go_test(
    name = "repository_test",
    srcs = [
        "bc_flow_to_bits_migration_record_test.go",
        "bc_flow_to_bits_migration_result_test.go",
        "march_project_meta_test.go",
        "project_template_test.go",
        "project_test.go",
        "rt_create_train_cron_job_config_test.go",
        "rt_train_snapshot_test.go",
        "rt_train_test.go",
    ],
    embed = [":repository"],
    deps = [
        "//app/migrationtool/biz/dal/mysql",
        "//app/migrationtool/biz/dal/mysql/entity",
        "//app/migrationtool/testfactory",
        "//idls/byted/devinfra/cd/bc_migration:bc_migration_go_proto",
        "@com_github_stretchr_testify//assert",
    ],
)
