// Code generated by MockGen. DO NOT EDIT.
// Source: hagrid_bc_flow_to_bis_migration_train_workspace_info.go

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/migrationtool/biz/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockMigrationTrainWorkspaceInfoDao is a mock of MigrationTrainWorkspaceInfoDao interface.
type MockMigrationTrainWorkspaceInfoDao struct {
	ctrl     *gomock.Controller
	recorder *MockMigrationTrainWorkspaceInfoDaoMockRecorder
}

// MockMigrationTrainWorkspaceInfoDaoMockRecorder is the mock recorder for MockMigrationTrainWorkspaceInfoDao.
type MockMigrationTrainWorkspaceInfoDaoMockRecorder struct {
	mock *MockMigrationTrainWorkspaceInfoDao
}

// NewMockMigrationTrainWorkspaceInfoDao creates a new mock instance.
func NewMockMigrationTrainWorkspaceInfoDao(ctrl *gomock.Controller) *MockMigrationTrainWorkspaceInfoDao {
	mock := &MockMigrationTrainWorkspaceInfoDao{ctrl: ctrl}
	mock.recorder = &MockMigrationTrainWorkspaceInfoDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMigrationTrainWorkspaceInfoDao) EXPECT() *MockMigrationTrainWorkspaceInfoDaoMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockMigrationTrainWorkspaceInfoDao) Get(ctx context.Context, workspaceID int64) (*entity.MigrationTrainWorkspaceInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, workspaceID)
	ret0, _ := ret[0].(*entity.MigrationTrainWorkspaceInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockMigrationTrainWorkspaceInfoDaoMockRecorder) Get(ctx, workspaceID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockMigrationTrainWorkspaceInfoDao)(nil).Get), ctx, workspaceID)
}
