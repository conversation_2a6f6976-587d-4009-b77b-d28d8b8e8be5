// Code generated by MockGen. DO NOT EDIT.
// Source: march_project_meta.go

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/migrationtool/biz/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockMarchProjectMetaDao is a mock of MarchProjectMetaDao interface.
type MockMarchProjectMetaDao struct {
	ctrl     *gomock.Controller
	recorder *MockMarchProjectMetaDaoMockRecorder
}

// MockMarchProjectMetaDaoMockRecorder is the mock recorder for MockMarchProjectMetaDao.
type MockMarchProjectMetaDaoMockRecorder struct {
	mock *MockMarchProjectMetaDao
}

// NewMockMarchProjectMetaDao creates a new mock instance.
func NewMockMarchProjectMetaDao(ctrl *gomock.Controller) *MockMarchProjectMetaDao {
	mock := &MockMarchProjectMetaDao{ctrl: ctrl}
	mock.recorder = &MockMarchProjectMetaDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMarchProjectMetaDao) EXPECT() *MockMarchProjectMetaDaoMockRecorder {
	return m.recorder
}

// BatchGetByProjectIDs mocks base method.
func (m *MockMarchProjectMetaDao) BatchGetByProjectIDs(ctx context.Context, projectIDs []uint64) ([]*entity.DBMarchProjectMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetByProjectIDs", ctx, projectIDs)
	ret0, _ := ret[0].([]*entity.DBMarchProjectMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetByProjectIDs indicates an expected call of BatchGetByProjectIDs.
func (mr *MockMarchProjectMetaDaoMockRecorder) BatchGetByProjectIDs(ctx, projectIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetByProjectIDs", reflect.TypeOf((*MockMarchProjectMetaDao)(nil).BatchGetByProjectIDs), ctx, projectIDs)
}

// GetByProjectID mocks base method.
func (m *MockMarchProjectMetaDao) GetByProjectID(ctx context.Context, projectID uint64) (*entity.DBMarchProjectMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByProjectID", ctx, projectID)
	ret0, _ := ret[0].(*entity.DBMarchProjectMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByProjectID indicates an expected call of GetByProjectID.
func (mr *MockMarchProjectMetaDaoMockRecorder) GetByProjectID(ctx, projectID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByProjectID", reflect.TypeOf((*MockMarchProjectMetaDao)(nil).GetByProjectID), ctx, projectID)
}
