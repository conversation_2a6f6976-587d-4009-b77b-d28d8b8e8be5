package main

import (
	"code.byted.org/devinfra/hagrid/app/migrationtool/config"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitex1701"
	bits_kitexmw "code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	_ "code.byted.org/devinfra/hagrid/libs/prelude"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/kitexmw"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cdpb/cdrpc"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/iesarch/paas_sdk/util/trace/regionmark"
	"code.byted.org/kite/kitex/server"
)

func main() {
	log.SetDefaultLogger(logs.SetPSM(env.PSM()), logs.SetCallDepth(2), logs.SetFullPath(), logs.SetKVPosition(logs.AfterMsg), logs.SetDisplayEnvInfo(true), logs.SetDisplayFuncName(true))

	Init(config.MustInitializeConfig())

	mws := kitexmw.NewServerSuite()
	// 先在cdrpc中试用MarkRequestRegionKitexMiddleware，验证过后再加入到kitexmw.NewServerSuite中
	mws = append(mws, server.WithMiddleware(regionmark.MarkRequestRegionKitexMiddleware))
	mws = append(mws, server.WithMiddleware(bits_kitexmw.WarpError))
	mws = append(mws, server.WithMiddleware(kitexmw.BitsErrTracerMW))
	mws = append(mws, server.WithRecvMiddleware(bits_kitexmw.LogServerSideRequestResponseRecv))
	mws = append(mws, server.WithMiddleware(kitex1701.ResponseAvoid1701Error))
	if err := cdrpc.NewServer(NewMigrationTool(), mws...).Run(); err != nil {
		log.V2.Fatal().Str("failed to run server").Error(err).Emit()
	}
}
