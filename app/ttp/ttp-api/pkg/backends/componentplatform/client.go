/**
 * @Date: 2023/8/4
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package componentplatform

import (
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/kitex_gen/bits/component_platform/server/historyservice"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/kitex_gen/bits/component_platform/server/reposervice"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/pkg/backends/options"
)

type client struct {
	repo    reposervice.Client
	history historyservice.Client
}

func New() Api {
	opts := options.NewRPCOptions()
	repo := reposervice.MustNewClient("bits.apps.component_server", opts...)
	history := historyservice.MustNewClient("bits.apps.component_server", opts...)

	return &client{
		repo:    repo,
		history: history,
	}
}
