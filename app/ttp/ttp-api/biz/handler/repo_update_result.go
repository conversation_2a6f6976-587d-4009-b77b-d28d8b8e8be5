// Code generated by hertztool.

package handler

import (
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/biz/model"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/kitex_gen/bits/complat_common"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/pkg/backends/mpaas"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/pkg/initializer"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-api/pkg/releases"
	"context"
	"net/http"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz_ext/v2/binding"
)

// RepoUpdateResult .
// @router /api/open/repo/upgrade/updateResult [POST]
func RepoUpdateResult(ctx context.Context, c *app.RequestContext) {
	request := new(model.RepoUpdateResultRequest)
	if err := binding.BindAndValidate(c, request); err != nil {
		log.V2.Info().With(ctx).Str("[RepoUpdateResult]").Str("failed to parse request").Error(err).Emit()
		resp := &model.RepoUpdateResultResponse{ErrNo: 0, ErrMsg: err.Error()}
		c.JSON(http.StatusOK, resp)
		return
	}

	if !request.FinalStatus { // false 表示没有成功
		log.V2.Info().With(ctx).Str("[RepoUpdateResult]").Str("this upgrade not success").KV("request", request).Emit()
		resp := &model.RepoUpdateResultResponse{ErrNo: 0, ErrMsg: "ok"}
		c.JSON(http.StatusOK, resp)
		return
	}

	history, err := initializer.ComplatApi.GetUpgradeHistoryById(ctx, request.HistoryId).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("[RepoUpdateResult]").Str("failed to find upgrade history").Error(err).KV("id", request.HistoryId).Emit()
		resp := &model.RepoUpdateResultResponse{ErrNo: 0, ErrMsg: err.Error()}
		c.JSON(http.StatusOK, resp)
		return
	}

	TryTriggerMpaasIosRepoUpgrade(ctx, history)
	releases.ReportRowRepoUpgradeSuccess(ctx, history.RepoId, history.Version)

	resp := &model.RepoUpdateResultResponse{
		ErrNo:  0,
		ErrMsg: "success",
	}
	c.JSON(http.StatusOK, resp)
	return
}

func TryTriggerMpaasIosRepoUpgrade(ctx context.Context, history *complat_common.History) {
	if !releases.ShouldReleaseAtCn(ctx, history.RepoId) {
		log.V2.Info().With(ctx).Str("[TryTriggerMpaasIosRepoUpgrade]").Str("this repo not ttp repo").KV("history", history).Emit()
		return
	}

	request := &mpaas.TriggerRepoUpgradeRequest{
		CommitId:    history.CommitId,
		Branch:      history.Branch,
		Version:     history.Version,
		RepoId:      history.RepoId,
		OperateUser: history.Username,
		SaveHistory: 0,
		From:        "ttp",
	}
	if err := initializer.MpaasApi.TriggerRepoUpgrade(ctx, request); err != nil {
		log.V2.Warn().With(ctx).Str("failed to trigger mpaas ios repo upgrade").Error(err).Emit()
	}
}
