/**
 * @Date: 2022/3/3
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package failuredomain

import (
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/kitex_gen/bits/devops/pigeon/request"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/backends/gitserver"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/globals"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/larkhelper"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/larkmessager"
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/service/letterservice"
	"context"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
)

type FailureDomain struct {
	letterService *letterservice.LetterService
	lark          larkmessager.Api
	gitApi        gitserver.Api
}

func NewFailureDomain(rdb *goredis.Client, db *gorm.DB, lark larkmessager.Api, gitApi gitserver.Api) *FailureDomain {
	return &FailureDomain{
		letterService: letterservice.NewLetterService(rdb, db, lark),
		lark:          lark,
		gitApi:        gitApi,
	}
}

func (domain *FailureDomain) SendSecurityCheckFailedMessage(ctx context.Context, req *request.SendSecurityCheckFailedMessageRequest) gresult.R[*base.EmptyResponse] {
	if globals.IsBot(req.Receiver) {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "bot, ignored", StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}

	if err := VerifySendSecurityCheckFailedMessageRequest(req); err != nil {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_INVALID_ARGUMENT)}}
		return gresult.OK(resp)
	}

	if has := domain.letterService.QueryByBizIDAndCategory(ctx, uint64(req.GetBizID()), entity.CategorySecurityCheck).IsOK(); has { // 消息已经发送过,所以就不再发送了
		log.V2.Info().With(ctx).Str("security check failed message has been sent").KVs("biz id", req.BizID, "category", entity.CategorySecurityCheck).Emit()
		resp := &base.EmptyResponse{
			BaseResp: &base.BaseResp{StatusMessage: "already sent", StatusCode: int32(base.Code_OK)},
		}
		return gresult.OK(resp)
	}

	card := larkhelper.NewSecurityCheckFailedCard(req.Receiver, req.BizID, req.Project, req.CommitId, req.URL)
	if err := domain.letterService.SendCardMessage(ctx, uint64(req.GetBizID()), entity.CategorySecurityCheck, req.GetReceiver(), card); err != nil {
		log.V2.Error().With(ctx).Str("security check failed message").Error(err).KVs("biz id", req.BizID, "receiver", req.GetReceiver()).Emit()
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}
	go func() { _ = domain.lark.SendCardMessageToChatGroup(ctx, "oc_346349ae76a2090d8d705185cac55cf0", card) }()

	resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "ok", StatusCode: int32(base.Code_OK)}}
	return gresult.OK(resp)
}

func (domain *FailureDomain) SendCodeSyncFailedMessage(ctx context.Context, req *request.SendCodeSyncFailedMessageRequest) gresult.R[*base.EmptyResponse] {
	if globals.IsBot(req.Receiver) {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "bot, ignored", StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}

	if err := VerifySendCodeSyncFailedMessageRequest(req); err != nil {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_INVALID_ARGUMENT)}}
		return gresult.OK(resp)
	}

	if has := domain.letterService.QueryByBizIDAndCategory(ctx, uint64(req.GetBizID()), entity.CategoryCodeSync).IsOK(); has { // 消息已经发送过,所以就不再发送了
		log.V2.Info().With(ctx).Str("code sync failed message has been sent").KVs("biz id", req.BizID, "category", entity.CategoryCodeSync).Emit()
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "already sent", StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}
	project, err := domain.gitApi.GetProjectById(ctx, req.GetProjectID()).Get()
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to query project when send code sync failed message").Error(err).KV("project id", req.GetProjectID()).Emit()
		return gresult.OK(base.NewEmptyResponse())
	}

	commit := larkhelper.NewCommit(project.GetNameWithNamespace(), project.GetWebUrl(), req.GetCommitID(), req.GetBranch())
	card := larkhelper.NewCodeSyncFailedCard(req.GetBizID(), req.GetURL(), commit)
	if err := domain.letterService.SendCardMessage(ctx, uint64(req.GetBizID()), entity.CategoryCodeSync, req.GetReceiver(), card); err != nil {
		log.V2.Error().With(ctx).Str("code sync failed message").Error(err).KVs("biz id", req.GetBizID(), "receiver", req.GetReceiver()).Emit()
		return gresult.OK(base.NewEmptyResponse())
	}

	return gresult.OK(base.NewEmptyResponse())
}

func (domain *FailureDomain) SendComponentUpgradeFailedMessage(ctx context.Context, req *request.SendComponentUpgradeFailedMessageRequest) gresult.R[*base.EmptyResponse] {
	if globals.IsBot(req.Receiver) {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "bot, ignored", StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}

	if err := VerifySendComponentUpgradeFailedMessageRequest(req); err != nil {
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_INVALID_ARGUMENT)}}
		return gresult.OK(resp)
	}

	if has := domain.letterService.QueryByBizIDAndCategory(ctx, uint64(req.GetBizID()), entity.CategoryComponentUpgrade).IsOK(); has { // 消息已经发送过,所以就不再发送了
		log.V2.Info().With(ctx).Str("component upgrade failed message has been sent").KVs("biz id", req.BizID, "category", entity.CategoryComponentUpgrade).Emit()
		resp := &base.EmptyResponse{BaseResp: &base.BaseResp{StatusMessage: "already sent", StatusCode: int32(base.Code_OK)}}
		return gresult.OK(resp)
	}

	component := larkhelper.NewComponent(req.GetComponentName(), req.GetComponentVersion(), req.GetComponentBranch(), req.GetComponentOperator(), req.GetComponentChange(), req.GetURL())
	reason := larkhelper.NewReason(req.GetError(), req.GetStatus())
	card := larkhelper.NewComponentUpgradeFailedCard(component, reason)
	if err := domain.letterService.SendCardMessage(ctx, uint64(req.GetBizID()), entity.CategoryComponentUpgrade, req.GetReceiver(), card); err != nil {
		log.V2.Error().With(ctx).Str("send component upgrade failed message").Error(err).KVs("biz id", req.GetBizID(), "receiver", req.GetReceiver()).Emit()
		return gresult.OK(base.NewEmptyResponse())
	}

	return gresult.OK(base.NewEmptyResponse())
}

func (domain *FailureDomain) SendTextToChatGroupMessage(ctx context.Context, req *request.SendTextToChatGroupMessageRequest) gresult.R[*base.EmptyResponse] {
	if err := domain.lark.SendStringMessageToChatGroup(ctx, req.Ocid, req.Text); err != nil {
		log.V2.Error().With(ctx).Str("send text to chat group message").Error(err).KVs("text", req.GetText(), "Ocid", req.GetOcid()).Emit()
		return gresult.OK(base.NewEmptyResponse())
	}
	return gresult.OK(base.NewEmptyResponse())
}
