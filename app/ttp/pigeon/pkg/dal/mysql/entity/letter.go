/**
 * @Date: 2022/3/3
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package entity

import "gorm.io/gorm/schema"

type Category string

const (
	CategorySecurityCheck    Category = "security_check"
	CategoryCodeSync         Category = "code_sync"
	CategoryComponentUpgrade Category = "component_upgrade"
)

type Letter struct {
	ID       uint64   `gorm:"column:id;primarykey"`
	BizID    uint64   `gorm:"column:biz_id"`
	Category Category `gorm:"column:category"`
	Receiver string   `gorm:"column:receiver"`
	//CreatedAt time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP"` // 数据库有这个字段,但是业务里面不使用,所以注释了.
}

var _ schema.Tabler = &Letter{}

func (Letter) TableName() string {
	return "pigeon_letter"
}

func NewLetter(bizID uint64, category Category, receiver string) *Letter {
	return &Letter{BizID: bizID, Category: category, Receiver: receiver}
}
