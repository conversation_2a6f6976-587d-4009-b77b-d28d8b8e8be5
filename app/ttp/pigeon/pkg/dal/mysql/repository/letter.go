/**
 * @Date: 2022/3/3
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repository

import (
	"code.byted.org/devinfra/hagrid/app/ttp/pigeon/pkg/dal/mysql/entity"
	"context"

	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
)

type LetterRepository struct {
	db *gorm.DB
}

func NewLetterRepository(db *gorm.DB) *LetterRepository {
	return &LetterRepository{db: db}
}

func (repository *LetterRepository) FindLastByBizIDAndCategory(ctx context.Context, bizID uint64, category entity.Category) gresult.R[*entity.Letter] {
	value := new(entity.Letter)
	err := repository.db.
		WithContext(ctx).
		Where("`biz_id` = ? AND `category` = ?", bizID, category).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.Letter](err)
	}
	return gresult.Ok(value)
}

func (repository *LetterRepository) Create(ctx context.Context, value *entity.Letter) error {
	err := repository.db.
		WithContext(ctx).
		Create(value).
		Error

	if err != nil {
		return err
	}
	return nil
}

func (repository *LetterRepository) DeleteByID(ctx context.Context, id uint64) error {
	err := repository.db.
		WithContext(ctx).
		Delete(entity.Letter{}, id).
		Error

	if err != nil {
		return err
	}
	return nil
}
