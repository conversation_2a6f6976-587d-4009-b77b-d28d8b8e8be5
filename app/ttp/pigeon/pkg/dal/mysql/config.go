/**
 * @Date: 2021/9/26
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package mysql

type Config struct {
	DB string

	Username string
	Password string
	Host     string
	Port     int64

	PSM string
}

func NewPSMConfig(psm string, db string) *Config {
	return &Config{
		DB:  db,
		PSM: psm,
	}
}

func NewConfig(host string, port int64, username string, password string, db string) *Config {
	return &Config{
		DB:       db,
		Username: username,
		Password: password,
		Host:     host,
		Port:     port,
	}
}
