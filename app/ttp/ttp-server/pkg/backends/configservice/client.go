/**
 * @Date: 2023/4/27
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package configservice

import (
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/kitex_gen/bytedance/bits/config_service/configservice"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/backends/options"
	"context"
	"errors"

	"code.byted.org/lang/gg/gresult"
)

type client struct {
	inner configservice.Client
}

func New() Api {
	opts := options.NewRPCOptions()
	inner := configservice.MustNewClient("bytedance.bits.config_service", opts...)

	return &client{
		inner: inner,
	}
}

func (client *client) GetAppDevopsSettings(ctx context.Context, appId int64) gresult.R[*config_service.DevopsAppBaseConfig] {
	request := &config_service.GetDevopsAppBaseConfigQuery{MetaAppID: appId}

	response, err := client.inner.GetDevopsAppBaseConfig(ctx, request)
	if err != nil {
		return gresult.Err[*config_service.DevopsAppBaseConfig](err)
	}
	if response.Config == nil {
		return gresult.Err[*config_service.DevopsAppBaseConfig](errors.New("app settings not found"))
	}
	return gresult.OK(response.Config)
}
