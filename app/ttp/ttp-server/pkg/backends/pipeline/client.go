/**
 * @Date: 2023/6/2
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package pipeline

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/kitex_gen/bytedance/bits/workflow/workflowservice"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/backends/options"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

type client struct {
	workflow workflowservice.Client
}

func New() Api {
	opts := options.NewRPCOptions()
	workflow := workflowservice.MustNewClient("bytedance.bits.workflow", opts...)

	return &client{
		workflow: workflow,
	}
}

func (client *client) GetJob(ctx context.Context, id int64) gresult.R[*workflow.Job] {
	request := &workflow.GetJobRequest{
		JobId: id,
		Base:  nil,
	}

	response, err := client.workflow.GetJob(ctx, request)
	if err != nil {
		return gresult.Err[*workflow.Job](err)
	}
	if response.Job == nil {
		return gresult.Err[*workflow.Job](errors.New("job not found"))
	}
	return gresult.OK(response.Job)
}

func (client *client) TriggerTemplateJob(ctx context.Context, username string, projectId int64, gitAddress string, params *workflow.TemplateConfigParams, env map[string]string, branch, commit string, webhooks []string) gresult.R[int64] {
	request := &workflow.TriggerTemplateJobRequest{
		Params:        params,
		Operator:      username,
		RepoId:        gptr.OfNotZero(strconv.FormatInt(projectId, 10)),
		RepoBranch:    &branch,
		RepoCommit:    &commit,
		RepoUrl:       gptr.OfNotZero(gitAddress),
		CallbackURLs:  webhooks,
		Sync:          nil,
		Env:           env,
		Configuration: nil,
		RepoMrIid:     nil,
		Name:          gptr.Of("DONT-DELETE"),
		DisplayName:   nil,
		PipelineId:    nil,
		DependsOn:     nil,
		JobSettings:   nil,
		Base:          nil,
	}

	response, err := client.workflow.TriggerTemplateJob(ctx, request)
	if err != nil {
		return gresult.Err[int64](err)
	}
	if response == nil || response.Job == nil {
		return gresult.Err[int64](errors.New("job not found"))
	}
	return gresult.OK(response.JobId)
}

func (client *client) TriggerTemplateJob2(ctx context.Context, request *workflow.TriggerTemplateJobRequest) gresult.R[int64] {
	request.Name = gptr.Of("DONT-DELETE")
	response, err := client.workflow.TriggerTemplateJob(ctx, request)
	if err != nil {
		return gresult.Err[int64](err)
	}
	if response == nil || response.Job == nil {
		return gresult.Err[int64](errors.New("job not found"))
	}
	return gresult.OK(response.JobId)
}

func (client *client) TriggerJob(ctx context.Context, req *workflow.TriggerJobRequest) gresult.R[int64] {
	if req.Info != nil {
		req.Info.Name = "DONT-DELETE"
	}
	response, err := client.workflow.TriggerJob(ctx, req)
	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(response.JobId)
}

func (client *client) GetTemplateConfigParams(ctx context.Context, templateConfigId int64) gresult.R[*workflow.TemplateConfigParams] {
	request := &workflow.GetTemplateConfigParamsRequest{
		TemplateConfigId: templateConfigId,
	}
	response, err := client.workflow.GetTemplateConfigParams(ctx, request)
	if err != nil {
		return gresult.Err[*workflow.TemplateConfigParams](err)
	}
	if response.Params == nil {
		return gresult.Err[*workflow.TemplateConfigParams](fmt.Errorf("template config params not found(%d)", templateConfigId))
	}
	return gresult.OK(response.Params)
}
