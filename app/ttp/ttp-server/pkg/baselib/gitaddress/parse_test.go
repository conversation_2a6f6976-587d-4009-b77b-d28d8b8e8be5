/**
 * @Date: 2021/12/3
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package gitaddress

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGroupAndName(t *testing.T) {
	gitAddress := "******************:bits/ttp.git"

	group, name, err := GroupAndName(gitAddress)

	assert.Nil(t, err)
	assert.Equal(t, "bits", group)
	assert.Equal(t, "ttp", name)
}
