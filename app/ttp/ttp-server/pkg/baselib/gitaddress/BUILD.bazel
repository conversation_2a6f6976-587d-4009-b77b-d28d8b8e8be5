load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "gitaddress",
    srcs = [
        "format.go",
        "parse.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/baselib/gitaddress",
    visibility = ["//visibility:public"],
    deps = ["@com_github_pkg_errors//:errors"],
)

go_test(
    name = "gitaddress_test",
    srcs = ["parse_test.go"],
    embed = [":gitaddress"],
    deps = ["@com_github_stretchr_testify//assert"],
)
