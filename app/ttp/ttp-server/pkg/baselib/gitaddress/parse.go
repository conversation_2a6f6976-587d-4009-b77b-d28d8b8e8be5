/**
 * @Date: 2021/12/3
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package gitaddress

import (
	"strings"

	"github.com/pkg/errors"
)

var ErrWrongGitURLFormat = errors.New("incorrect Git URl format, e.g.: ******************:<group>/<name>.git")

func GroupAndName(gitAddress string) (string, string, error) {
	s := strings.TrimPrefix(gitAddress, "******************:")
	s = strings.TrimSuffix(s, ".git")

	parts := strings.Split(s, "/")
	if len(parts) == 2 {
		return parts[0], parts[1], nil
	}
	return "", "", ErrWrongGitURLFormat
}
