/**
 * @Date: 2023/9/7
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package statemachine

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/backends"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/backends/codebase"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/backends/ussa"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/dal/mysql/repository"

	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/optional"
	"gorm.io/gorm"
)

type CodeSyncStateMachine struct {
	eventRepository         *repository.EventRepository
	projectCommitRepository *repository.ProjectCommitRepository
	backends                *backends.Backends
}

func NewCodeSyncStateMachine(db *gorm.DB, backends *backends.Backends) *CodeSyncStateMachine {
	return &CodeSyncStateMachine{
		eventRepository:         repository.NewEventRepository(db),
		projectCommitRepository: repository.NewProjectCommitRepository(db),
		backends:                backends,
	}
}

func (sm *CodeSyncStateMachine) TryPollCodebase(ctx context.Context, syncEvent optional.O[*entity.SyncEvent], commit *entity.ProjectCommit) {
	log.V2.Info().With(ctx).Str("[CodeSyncStateMachine.TryPollCodebase]").Str("start poll codebase").Obj(commit).Emit()

	switch commit.TicketState {
	case entity.TicketStatePending, entity.TicketStateRunning: // 没有结束，需要调用 Codebase 接口
		var (
			ticketId = commit.TicketId
			ticket   *codebase.SyncTicket
			err      error
		)
		if commit.Label == entity.SyncLabelStage {
			ticket, err = sm.backends.CodebaseStageApi.GetSyncTicket(ctx, ticketId).Get()
		} else {
			ticket, err = sm.backends.CodebaseApi.GetSyncTicket(ctx, ticketId).Get()
		}
		if err != nil {
			log.V2.Error().With(ctx).Str("[CodeSyncer]").Str("failed to fetch codebase sync ticket").Error(err).KV("ticket", ticketId).Emit()
			return
		}

		log.V2.Info().With(ctx).Str("[CodeSyncStateMachine.TryPollCodebase]").Str("fetch ticket").Obj(ticket).Emit()

		var (
			updated *entity.ProjectCommit = nil
			now                           = time.Now()
		)
		switch ticket.State {
		case codebase.TicketStateSucceed:
			syncEvent.IfOK(func(sync *entity.SyncEvent) { sm.SyncStatus(ctx, sync, "success") })
			updated = &entity.ProjectCommit{TicketState: entity.TicketStateSucceed, FinishedAt: now}
		case codebase.TicketStateFailed, codebase.TicketStateCanceled:
			syncEvent.IfOK(func(sync *entity.SyncEvent) { sm.SyncStatus(ctx, sync, "failure") })
			updated = &entity.ProjectCommit{TicketState: entity.TicketStateFailed, FinishedAt: now}
		}
		if updated != nil {
			if err := sm.projectCommitRepository.UpdateById(ctx, commit.Id, updated); err != nil {
				log.V2.Error().With(ctx).Str("[CodeSyncStateMachine]").Str("failed to update project commit").Error(err).KVs("id", commit.Id, "updated", updated).Emit()
			}
		}
	}

	if time.Since(commit.CreatedAt) > 1*time.Hour { // 一小时了都没有成功，直接判定失败
		updated := &entity.ProjectCommit{TicketState: entity.TicketStateFailed, FinishedAt: time.Now()}
		if err := sm.projectCommitRepository.UpdateById(ctx, commit.Id, updated); err != nil {
			log.V2.Error().With(ctx).Str("[CodeSyncStateMachine]").Str("failed to update project commit").Error(err).KVs("id", commit.Id, "updated", updated).Emit()
		}
	}
}

func (sm *CodeSyncStateMachine) SyncStatus(ctx context.Context, sync *entity.SyncEvent, status string) {
	var (
		taskName = "HandleCheckTask"
		now      = time.Now()
		cost     = int64(now.Sub(sync.CreatedAt).Seconds())
	)

	event, err := sm.eventRepository.FindLastById(ctx, sync.EventId).Get()
	if err != nil {
		log.V2.Warn().With(ctx).Str("[SyncStatus]").Str("failed to find event").Error(err).KV("event", sync.EventId).Emit()
		return
	}

	if !gslice.Contains(entity.RepoUpgradeEventTypes, event.Event) {
		log.V2.Info().With(ctx).Str("[SyncStatus]").Str("this event not repo upgrade so ignore").KV("event", event.Id).Emit()
		return
	}

	his, err := sm.backends.ComplatApi.GetUpgradeHistoryById(ctx, event.BizID).Get()
	if err != nil {
		log.V2.Warn().With(ctx).Str("[SyncStatus]").Str("failed to find repo version").Error(err).KV("id", event.BizID).Emit()
		return
	}
	repo, err := sm.backends.ComplatApi.GetRepoById(ctx, his.RepoId).Get()
	if err != nil {
		log.V2.Warn().With(ctx).Str("[SyncStatus]").Str("failed to find repo").Error(err).KV("id", his.RepoId).Emit()
		return
	}

	request := &ussa.SyncStatusRequest{
		TaskName:      taskName,
		Status:        status,
		Time:          cost,
		LogUrl:        event.GetURL(),
		Name:          his.Fullname,
		Version:       his.Version,
		RepoId:        his.RepoId,
		ComponentType: strings.ToLower(repo.AppType.String()),
		EventId:       sync.EventId,
		Env:           fmt.Sprintf("TTP_%s", event.Label.String()),
	}
	sm.backends.UssaApi.SyncStatus(ctx, request)
}
