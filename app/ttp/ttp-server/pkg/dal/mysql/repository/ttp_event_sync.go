/**
 * @Date: 2021/8/31
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package repository

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/pkg/dal/mysql/entity"
	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type SyncEventRepository struct {
	db *gorm.DB
}

func NewSyncEventRepository(db *gorm.DB) *SyncEventRepository {
	return &SyncEventRepository{
		db: db.Table(schema.Tabler(new(entity.SyncEvent)).TableName()),
	}
}

func (repository *SyncEventRepository) Create(ctx context.Context, value *entity.SyncEvent) gresult.R[int64] {
	err := repository.db.
		WithContext(ctx).
		Create(value).
		Error

	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(value.Id)
}

func (repository *SyncEventRepository) FindLastById(ctx context.Context, id int64) gresult.R[*entity.SyncEvent] {
	value := new(entity.SyncEvent)
	err := repository.db.
		WithContext(ctx).
		Where("`id` = ?", id).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.SyncEvent](err)
	}
	return gresult.OK(value)
}

func (repository *SyncEventRepository) FindByEventIdInOrderByIdDesc(ctx context.Context, eventIds []int64) gresult.R[[]*entity.SyncEvent] {
	values := make([]*entity.SyncEvent, 0, len(eventIds))
	err := repository.db.
		WithContext(ctx).
		Where("`event_id` IN ?", eventIds).
		Order("`id` DESC").
		Find(&values).
		Error

	if err != nil {
		return gresult.Err[[]*entity.SyncEvent](err)
	}
	return gresult.OK(values)
}

func (repository *SyncEventRepository) FindLastByEventId(ctx context.Context, eventId int64) gresult.R[*entity.SyncEvent] {
	value := new(entity.SyncEvent)
	err := repository.db.
		WithContext(ctx).
		Where("`event_id` = ?", eventId).
		Last(value).
		Error

	if err != nil {
		return gresult.Err[*entity.SyncEvent](err)
	}
	return gresult.OK(value)
}

func (repository *SyncEventRepository) UpdateById(ctx context.Context, id int64, updated *entity.SyncEvent) error {
	err := repository.db.
		WithContext(ctx).
		Where("`id` = ?", id).
		Updates(updated).
		Error

	if err != nil {
		return err
	}
	return nil
}

func (repository *SyncEventRepository) DeleteById(ctx context.Context, id int64) error {
	err := repository.db.
		WithContext(ctx).
		Delete(entity.SyncEvent{}, id).
		Error

	if err != nil {
		return err
	}
	return nil
}
