package bridgedomain

import (
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/ttp/ttp-server/kitex_gen/bits/devops/ttp"
	"context"

	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

func (domain *BridgeDomain) GetOverseaSpecifiedBuildLog(ctx context.Context, req *ttp.GetOverseaSpecifiedBuildLogRequest) gresult.R[*ttp.GetOverseaSpecifiedBuildLogResponse] {
	content, err := domain.backends.OverseaApi.GetSpecifiedBuildLog(ctx, req.JobId, req.<PERSON>ey(), int(req.GetStart()), req.GetReverse()).Get()
	if err != nil {
		resp := &ttp.GetOverseaSpecifiedBuildLogResponse{
			BaseResp: &base.BaseResp{StatusMessage: err.Error(), StatusCode: int32(base.Code_UNKNOWN)},
		}
		return gresult.OK(resp)
	}

	resp := &ttp.GetOverseaSpecifiedBuildLogResponse{
		Content:  gptr.Of(content),
		BaseResp: &base.BaseResp{StatusMessage: "ok", StatusCode: int32(base.Code_OK)},
	}
	return gresult.OK(resp)
}
