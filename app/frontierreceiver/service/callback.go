package service

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/frontierreceiver/config"
	"code.byted.org/devinfra/hagrid/app/frontierreceiver/domain/dgroup"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/frontierreceiverpb"
	sdk "code.byted.org/frontier/serverSDK/go"
	"code.byted.org/gopkg/logs"
	"google.golang.org/protobuf/encoding/protojson"
)

type BitsCallBack struct{}

// 以下接口按需实现。如在平台配置了接口能力，那么此接口将被调用
func (c *BitsCallBack) SendMessage(ctx context.Context, req *sdk.SendMessageRequest) (r *sdk.SendMessageResponse, err error) {
	resp := &sdk.SendMessageResponse{
		Service:  config.Conf.Frontier.ServiceID,
		Method:   1,
		Payload:  []byte{},
		BaseResp: sdk.NewBaseResp(),
	}
	payload := &frontierreceiverpb.PullMessageReq{}
	err = protojson.Unmarshal(req.Payload, payload)
	if err != nil {
		resp.BaseResp.StatusCode = 1
		return resp, err
	}
	res, err := sdk.GroupControl(ctx, dgroup.GetAddToGroupRequest(payload))
	logs.CtxInfo(ctx, "add to group response: %v", res)
	if err != nil {
		logs.CtxError(ctx, "add to group fail, %v", err)
		resp.BaseResp.StatusCode = 1
	}
	return resp, err
}

func (c *BitsCallBack) Auth(ctx context.Context, req *sdk.AuthRequest) (r *sdk.AuthResponse, err error) {
	logs.CtxWarn(ctx, "auth not implement")
	return nil, nil
}
func (c *BitsCallBack) SendEvent(ctx context.Context, req *sdk.SendEventRequest) (r *sdk.SendEventResponse, err error) {
	logs.CtxWarn(ctx, "SendEvent not implement")
	return nil, nil
}
func (c *BitsCallBack) PullMessages(ctx context.Context, req *sdk.PullMessagesRequest) (r *sdk.PullMessagesResponse, err error) {
	logs.CtxWarn(ctx, "PullMessages not implement")
	return nil, nil
}
func (c *BitsCallBack) ACKMessage(ctx context.Context, req *sdk.ACKMessageRequest) (r *sdk.ACKMessageResponse, err error) {
	logs.CtxWarn(ctx, "ACKMessage not implement")
	return nil, nil
}
func (c *BitsCallBack) Upstream(ctx context.Context, req *sdk.SendMessageRequest) (err error) {
	logs.CtxWarn(ctx, "Upstream not implement")
	return nil
}
