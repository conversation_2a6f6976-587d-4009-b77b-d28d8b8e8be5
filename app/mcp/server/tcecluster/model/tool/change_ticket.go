package tool

type ListChangeTicketRequest struct {
	Psm string `json:"psm" validate:"required" jsonschema:"required,description=psm"`
}

type ListChangeTicketResponse struct {
	ChangeTickets []*ChangeTicketBriefInfo `json:"change_tickets" jsonschema:"description=a change ticket list contains the brief information of change tickets"`
}

type ChangeTicketBriefInfo struct {
	TicketId string `json:"ticket_id" jsonschema:"required,description=change ticket id"`
	Status   string `json:"status" jsonschema:"required,description=ticket overall status,enum=UNSPECIFIED,enum=PENDING,enum=RUNNING,enum=SUCCESS,enum=FAILED,enum=CANCELLED,enum=ROLLBACK_IN_PROGRESS,enum=ROLLBACK_SUCCESS"`
}

type GetChangeTicketDetailRequest struct {
	TicketId string `json:"ticket_id" validate:"required" jsonschema:"required,description=change ticket id"`
}

type GetChangeTicketDetailResponse struct {
	ChangeTicket *ChangeTicketDetail `json:"change_ticket" jsonschema:"required,description=change ticket detail"`
}

type ChangeTicketDetail struct {
	TicketId                 string `json:"ticket_id" jsonschema:"required,description=change ticket id"`
	Status                   string `json:"status" jsonschema:"required,description=ticket overall status,enum=UNSPECIFIED,enum=PENDING,enum=RUNNING,enum=SUCCESS,enum=FAILED,enum=CANCELLED,enum=ROLLBACK_IN_PROGRESS,enum=ROLLBACK_SUCCESS"`
	CurrentStage             string `json:"current_stage" jsonschema:"required,description=current stage of the workflow,enum=PRE_VALIDATE,enum=CHANGE_EXECUTE"`
	CurrentStageStatus       string `json:"current_stage_status" jsonschema:"required,description=current stage status"`
	CurrentStageErrorMessage string `json:"current_stage_error_message,omitempty" jsonschema:"description=current stage error message"`
	//PreValidateStage         *PreValidateStage   `json:"pre_validate_stage,omitempty" jsonschema:"description=detail about PRE_VALIDATE stage"`
	//ChangeExecuteStage       *ChangeExecuteStage `json:"change_execute_stage,omitempty" jsonschema:"description=detail about CHANGE_EXECUTE stage"`
}

//type PreValidateStage struct {
//	Status       string `json:"status" jsonschema:"required,description=ticket overall status,enum=UNSPECIFIED,enum=RUNNING,enum=SUCCESS,enum=FAILED,enum=CANCELLED,enum=ROLLBACK_IN_PROGRESS,enum=ROLLBACK_SUCCESS"`
//	ErrorMessage string `json:"error_message,omitempty" jsonschema:"description=error message"`
//}
//
//type ChangeExecuteStage struct {
//	Status       string `json:"status" jsonschema:"required,description=ticket overall status,enum=UNSPECIFIED,enum=RUNNING,enum=SUCCESS,enum=FAILED,enum=CANCELLED,enum=ROLLBACK_IN_PROGRESS,enum=ROLLBACK_SUCCESS"`
//	ErrorMessage string `json:"error_message,omitempty" jsonschema:"description=error message"`
//}

type CancelChangeTicketRequest struct {
	TicketId string `json:"ticket_id" validate:"required" jsonschema:"required,description=change ticket id"`
}

type CancelChangeTicketResponse struct{}
