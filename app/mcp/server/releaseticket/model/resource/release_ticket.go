package resource

import (
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bits/integration/multi"

	"code.byted.org/devinfra/hagrid/app/mcp/pkg/timeutil"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
)

type GetReleaseTicketRequest struct {
	ReleaseTicketId string `json:"releaseTicketId" query:"releaseTicketId"  binding:"required" validate:"required" jsonschema:"required,description=release ticket id"`
}

type ListReleaseTicketRequest struct {
	WorkspaceId string `json:"workspaceId" query:"workspaceId"  binding:"required" validate:"required" jsonschema:"required,description=workspace id"`
}

type ReleaseTicket struct {
	// 发布单信息
	ReleaseTicketId            uint64   `json:"releaseTicketId" jsonschema:"description=release ticket id"`
	Name                       string   `json:"name" jsonschema:"description=release ticket name"`
	Status                     string   `json:"status" jsonschema:"enum=RELEASE_TICKET_STATUS_UNSPECIFIED,enum=RELEASE_TICKET_STATUS_CREATED,enum=RELEASE_TICKET_STATUS_BEFORE_INTEGRATION,enum=RELEASE_TICKET_STATUS_INTEGRATING,enum=RELEASE_TICKET_STATUS_BEFORE_RELEASE,enum=RELEASE_TICKET_STATUS_RELEASING,enum=RELEASE_TICKET_STATUS_RELEASED,enum=RELEASE_TICKET_STATUS_CANCELED" jsonschema_description:"release ticket status， RELEASE_TICKET_STATUS_UNSPECIFIED 未指定状态；RELEASE_TICKET_STATUS_CREATED 已创建；RELEASE_TICKET_STATUS_BEFORE_INTEGRATION 集成前；RELEASE_TICKET_STATUS_INTEGRATING 集成中；RELEASE_TICKET_STATUS_BEFORE_RELEASE 发布前；RELEASE_TICKET_STATUS_RELEASING 发布中；RELEASE_TICKET_STATUS_RELEASED 已发布；RELEASE_TICKET_STATUS_CANCELED 已取消；"`
	WorkspaceId                uint64   `json:"workspaceId" jsonschema:"description=workspace id"`
	WorkflowId                 uint64   `json:"workflowId" jsonschema:"description=the id of a workflow template which the release ticket is created from"`
	WorkflowName               string   `json:"workflowName" jsonschema:"description=workflow name"`
	ControlPlanes              []string `json:"controlPlanes" jsonschema:"enum=CONTROL_PLANE_CN,enum=CONTROL_PLANE_I18N,enum=CONTROL_PLANE_TTP,enum=CONTROL_PLANE_EU_TTP,enum=CONTROL_PLANE_US_TTP,description=control planes"`
	Description                string   `json:"description" jsonschema:"description=release ticket description"`
	BoeEnvName                 string   `json:"boeEnvName" jsonschema:"description=boe environment name"`
	PpeEnvName                 string   `json:"ppeEnvName" jsonschema:"description=ppe environment name"`
	Creator                    string   `json:"creator" jsonschema:"description=creator of the release ticket"`
	ReleaseApprovers           []string `json:"releaseApprovers" jsonschema:"description=release approvers"`
	TestApprovers              []string `json:"testApprovers" jsonschema:"description=test approvers"`
	RollbackStatus             string   `json:"rollbackStatus" jsonschema:"enum=ROLLBACK_STATUS_FAILED,enum=ROLLBACK_STATUS_SUCCESS,enum=ROLLBACK_STATUS_CANCELLED,enum=ROLLBACK_STATUS_ROLLBACKING" jsonschema_description:"rollback status, ROLLBACK_STATUS_UNSPECIFIED 未指定状态；ROLLBACK_STATUS_FAILED 回滚失败；ROLLBACK_STATUS_SUCCESS 回滚成功；ROLLBACK_STATUS_CANCELLED 回滚已取消；ROLLBACK_STATUS_ROLLBACKING 回滚中"`
	RollbackReason             string   `json:"rollbackReason" jsonschema:"description=rollback reason"`
	LarkGroupIds               []string `json:"larkGroupIds" jsonschema:"description=lark group ids"`
	NumOfAssociatedDevTaskType int      `json:"numOfAssociatedDevTaskType" jsonschema:"description=number of associated dev task type"`
	ExceptionReason            int      `json:"exceptionReason" jsonschema:"description=exception reason"`
	EndReleaseTime             string   `json:"endReleaseTime" jsonschema:"description=end release time"`
	IsIncrementalDelivery      bool     `json:"isIncrementalDelivery" jsonschema:"description=是否为分批次发布"`
	TeamFlowId                 uint64   `json:"teamFlowId" jsonschema:"description=team development process id"`
	TeamFlowType               string   `json:"teamFlowType" jsonschema:"enum=TEAM_FLOW_TYPE_UNSPECIFIED,enum=TEAM_FLOW_TYPE_TRAIN,enum=TEAM_FLOW_TYPE_FEATURE,enum=TEAM_FLOW_TYPE_HOTFIX,enum=TEAM_FLOW_TYPE_TBD,enum=TEAM_FLOW_TYPE_INCREMENTAL_DELIVERY" jsonschema_description:"team development process type, TEAM_FLOW_TYPE_UNSPECIFIED 未知类型；TEAM_FLOW_TYPE_TRAIN 火车模式；TEAM_FLOW_TYPE_FEATURE 需求模式；TEAM_FLOW_TYPE_HOTFIX 热修复模式；TEAM_FLOW_TYPE_INCREMENTAL_DELIVERY 分批次交付模式；TEAM_FLOW_TYPE_TBD 主干开发模式"`
	TeamFlowName               string   `json:"teamFlowName" jsonschema:"description=team development process name"`
	CreatedAt                  string   `json:"createdAt" jsonschema:"description=created at"`

	// 计划时间
	ScheduledStartIntegrationTime string `json:"scheduledStartIntegrationTime" jsonschema:"description=scheduled start integration time"`
	ScheduledEndIntegrationTime   string `json:"scheduledEndIntegrationTime" jsonschema:"description=scheduled end integration time"`
	ScheduledReleaseTime          string `json:"scheduledReleaseTime" jsonschema:"description=scheduled release time"`

	// 发布单关联的集成区信息
	IntegrationId        uint64 `json:"integrationId" jsonschema:"description=the id of integration zone that the release ticket is associated with"`
	IntegrationStatus    string `json:"integrationStatus" jsonschema:"enum=INTEGRATION_STATUS_UNSPECIFIED,enum=INTEGRATION_STATUS_WAITING,enum=INTEGRATION_STATUS_INTEGRATING,enum=INTEGRATION_STATUS_COMPLETED,enum=INTEGRATION_STATUS_CANCEL,enum=INTEGRATION_STATUS_TERMINAL" jsonschema_description:"integration status, INTEGRATION_STATUS_UNSPECIFIED 未知状态；INTEGRATION_STATUS_WAITING 等待集成中；INTEGRATION_STATUS_INTEGRATING 集成中；INTEGRATION_STATUS_COMPLETED 已完成；INTEGRATION_STATUS_CANCEL 已取消；INTEGRATION_STATUS_TERMINAL 终止状态"`
	TotalDevTaskNum      int64  `json:"totalDevTaskNum" jsonschema_description:"total dev task num, including closed/finished/unfinished dev task"`
	FinishedDevTaskNum   int64  `json:"finishedDevTaskNum" jsonschema:"description=finished dev task num"`
	UnfinishedDevTaskNum int64  `json:"unfinishedDevTaskNum" jsonschema:"description=unfinished dev task num"`
	ClosedDevTaskNum     int64  `json:"closedDevTaskNum" jsonschema:"description=closed dev task num"`

	// 发布单stage信息
	StageInfo *StageInfo `json:"stageInfo" jsonschema:"description=stage info of the release ticket, include current stage and all stages"`

	// 发布单回滚信息
	RollbackProgress []*RollbackProgressOfOneControlPlane `json:"rollbackProgress" jsonschema:"description=rollback progress of every control plane"`

	// 发布单归档信息
	//ArchiveDetail *ArchiveDetail `json:"archiveDetail" jsonschema:"description=archive detail"`

	// 仓库清单
	Repos []*Repo `json:"repos" jsonschema:"description=repos that should be released"`

	// 项目清单
	Projects []*Project `json:"projects" jsonschema:"description=projects that should be released"`
}

func (r *ReleaseTicket) FromReleaseTicketDetail(releaseTicketPB *release_ticketpb.ReleaseTicketDetail) *ReleaseTicket {
	if releaseTicketPB == nil {
		return r
	}

	rt := &ReleaseTicket{
		ReleaseTicketId:               releaseTicketPB.ReleaseTicketId,
		CreatedAt:                     releaseTicketPB.CreatedAt,
		Name:                          releaseTicketPB.Name,
		Status:                        releaseTicketPB.Status.String(),
		WorkspaceId:                   releaseTicketPB.WorkspaceId,
		WorkflowId:                    releaseTicketPB.WorkflowId,
		WorkflowName:                  releaseTicketPB.WorkflowName,
		Description:                   releaseTicketPB.Description,
		BoeEnvName:                    releaseTicketPB.BoeEnvName,
		PpeEnvName:                    releaseTicketPB.PpeEnvName,
		Creator:                       releaseTicketPB.Creator,
		ReleaseApprovers:              releaseTicketPB.ReleaseApprovers,
		IntegrationId:                 releaseTicketPB.IntegrationId,
		RollbackStatus:                releaseTicketPB.RollbackStatus.String(),
		RollbackReason:                releaseTicketPB.RollbackReason,
		TestApprovers:                 releaseTicketPB.TestApprovers,
		ScheduledStartIntegrationTime: timeutil.TimestampToString(releaseTicketPB.StartIntegrationAt),
		ScheduledEndIntegrationTime:   timeutil.TimestampToString(releaseTicketPB.EndIntergrationAt),
		ScheduledReleaseTime:          timeutil.TimestampToString(releaseTicketPB.ReleaseAt),
		LarkGroupIds:                  releaseTicketPB.LarkGroupIds,
		NumOfAssociatedDevTaskType:    int(releaseTicketPB.NumOfAssociatedDevTaskType),
		ExceptionReason:               int(releaseTicketPB.ExceptionReason),
		EndReleaseTime:                releaseTicketPB.EndReleaseTime,
		IsIncrementalDelivery:         releaseTicketPB.IsIncrementalDelivery,
		TeamFlowId:                    releaseTicketPB.TeamFlowId,
		TeamFlowType:                  releaseTicketPB.TeamFlowType.String(),
		TeamFlowName:                  releaseTicketPB.TeamFlowName,
	}

	rt.ControlPlanes = slicex.Map(releaseTicketPB.ControlPlanes, func(c sharedpb.ControlPlane) string {
		return c.String()
	})

	return rt
}

func (r *ReleaseTicket) WithIntegrationInfo(integrationInfo *multi.GetIntegrationInfoResp) *ReleaseTicket {
	if integrationInfo == nil {
		return r
	}

	r.IntegrationStatus = integrationInfo.Status.String()
	r.TotalDevTaskNum = integrationInfo.TotalDevTasks
	r.FinishedDevTaskNum = integrationInfo.FinishCount
	r.UnfinishedDevTaskNum = integrationInfo.TotalCount - integrationInfo.FinishCount
	r.ClosedDevTaskNum = integrationInfo.TotalDevTasks - integrationInfo.TotalCount

	// 后续按需添加集成区其它信息
	return r
}

func (r *ReleaseTicket) FromReleaseTicketListItem(releaseTicketPB *release_ticketpb.ReleaseTicketListItem) *ReleaseTicket {
	if releaseTicketPB == nil {
		return r
	}
	rt := &ReleaseTicket{
		ReleaseTicketId:  releaseTicketPB.ReleaseTicketId,
		CreatedAt:        releaseTicketPB.CreatedAt,
		Name:             releaseTicketPB.Name,
		Status:           releaseTicketPB.Status.String(),
		WorkspaceId:      releaseTicketPB.WorkspaceId,
		WorkflowId:       releaseTicketPB.WorkflowId,
		Description:      releaseTicketPB.Description,
		BoeEnvName:       releaseTicketPB.BoeEnvName,
		PpeEnvName:       releaseTicketPB.PpeEnvName,
		Creator:          releaseTicketPB.Creator,
		ReleaseApprovers: releaseTicketPB.ReleaseApprovers,
		IntegrationId:    releaseTicketPB.IntegrationId,
		RollbackStatus:   releaseTicketPB.RollbackStatus.String(),
		TestApprovers:    releaseTicketPB.TestApprovers,
		EndReleaseTime:   releaseTicketPB.EndReleaseTime,
		TeamFlowId:       releaseTicketPB.TeamFlowId,
		TeamFlowType:     releaseTicketPB.TeamFlowType.String(),
		TeamFlowName:     releaseTicketPB.TeamFlowName,
	}
	return rt
}
