load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "test",
    srcs = ["register.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/mcp/server/test",
    visibility = ["//visibility:public"],
    deps = [
        "//app/mcp/middleware",
        "@org_byted_code_devinfra_mcp//:mcp",
        "@org_byted_code_devinfra_mcp//transport/http",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_middleware_hertz//pkg/app/server",
    ],
)
