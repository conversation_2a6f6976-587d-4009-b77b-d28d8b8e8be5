package entity

import "time"

const (
	BitsStateTypeReleaseTicket = "release_ticket"
)

type BitsState struct {
	Id            uint64    `gorm:"column:id" json:"state_id"`                   // id
	Type          string    `gorm:"column:type" json:"type"`                     // type
	ResourceId    string    `gorm:"column:resource_id" json:"resource_id"`       // resource_id
	ResourceState string    `gorm:"column:resource_state" json:"resource_state"` // resource_state
	NextEventId   uint64    `gorm:"column:next_event_id" json:"next_event_id"`   // 该状态下，下一个事件的id
	CreatedAt     time.Time `gorm:"column:created_at" json:"created_at"`         // 创建时间
}

func (e *BitsState) TableName() string {
	return "bits_state"
}
