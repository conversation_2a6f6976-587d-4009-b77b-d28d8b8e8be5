load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "metrics",
    srcs = [
        "client.go",
        "query_error_serials.go",
        "token.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/slaanalyzeapi/pkg/metrics",
    visibility = ["//visibility:public"],
    deps = [
        "//pkg/net/httpclient",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
