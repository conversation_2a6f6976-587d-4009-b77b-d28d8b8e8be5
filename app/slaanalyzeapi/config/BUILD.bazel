load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "config",
    srcs = ["config.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/slaanalyzeapi/config",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_cloudwego_kitex//pkg/utils",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "config_test",
    srcs = ["config_test.go"],
    embed = [":config"],
    deps = [
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_cloudwego_kitex//pkg/utils",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//suite",
        "@org_byted_code_gopkg_env//:env",
    ],
)
