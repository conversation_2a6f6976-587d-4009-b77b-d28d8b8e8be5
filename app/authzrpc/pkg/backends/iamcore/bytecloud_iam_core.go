package iamcore

import (
	"os"
	"time"

	"code.byted.org/bytecloud/intelligen/kitex_gen/bytecloud/iam/core/coreservice"
	"code.byted.org/bytecycle/go-middlewares/infra/initializer"
	"code.byted.org/devinfra/hagrid/app/authzrpc/pkg/authzmw"
	"code.byted.org/kite/kitex/byted"
	"code.byted.org/kite/kitex/client"
)

const PSM = "bytecloud.iam.core"

// Module MUST be initialized before calls to Client.
var Module = initializer.MustRegisterPkg(mustInit)

// Client returns the singleton client of bytecloud.iam.core.
// NOTICE: nil will be returned if <PERSON><PERSON><PERSON> is NOT initialized.
func Client() coreservice.Client {
	return stub
}

// MustNewClient creates a Kitex Client from opts, with some ByteCycle
// conventions applied.
//
// NOTICE: Kitex client creation comes with BIG costs. You should NOT call this
// construction in MOST cases. Use the Client instead.
func MustNewClient(opts ...client.Option) coreservice.Client {
	cfg := byted.NewClientConfig()
	cfg.DestService = PSM
	cfg.ReadBizStatusErr = true
	//cfg.Transport = byted.GRPC
	var options = []client.Option{
		client.WithMiddleware(authzmw.LogClientTraffic()),
	}
	// 本地调试时，使用本地端口
	if _, ok := os.LookupEnv("APP_LOCAL_IAM_CORE"); ok {
		options = append(options, client.WithHostPorts("localhost:8889"),
			client.WithRPCTimeout(time.Minute*10))
	}
	//options = append(options, kitexmd.ClientSuiteOfPSM(PSM)...)
	options = append(options, opts...)
	cli := coreservice.MustNewClientWithBytedConfig(PSM, cfg, options...)
	return cli
}

func WithClientMock(t interface{}, mock coreservice.Client) {
	Module.MarkInitializedForTesting()
	stub = mock

	if wc, ok := t.(interface {
		Cleanup(func())
	}); ok {
		wc.Cleanup(func() { stub = realClient })
	}
}

var (
	stub       coreservice.Client // the client stub, exported via Client()
	realClient coreservice.Client // the client created by Kitex framework
)

// mustInit initialized the kitex client of PSM.
// MUST be protected by Module.
func mustInit() {
	realClient = MustNewClient()
	stub = realClient
}
