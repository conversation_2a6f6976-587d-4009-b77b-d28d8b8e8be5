package trigger_rpc

import (
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/middleware/ci_auth"
	"context"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/triggerpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/triggerpb/triggerservice"
	"github.com/cloudwego/hertz/pkg/app"

	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/pipeline_rpc"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/auth"
)

type TriggerRpc interface {
	DeleteTriggers(ctx context.Context, c *app.RequestContext, req *platformpb.DeleteTriggersRequest) (*platformpb.DeleteTriggersResponse, error)
	// ci cron trigger
	SaveCICronTriggers(ctx context.Context, c *app.RequestContext, req *platformpb.SaveCICronTriggersRequest) (*platformpb.SaveCICronTriggersResponse, error)
	GetCICronTriggers(ctx context.Context, c *app.RequestContext, req *platformpb.GetCICronTriggersRequest) (*platformpb.GetCICronTriggersResponse, error)
	CreateCICronTrigger(ctx context.Context, c *app.RequestContext, req *platformpb.CreateCICronTriggerRequest) (*platformpb.CreateCICronTriggerResponse, error)
	UpdateCICronTrigger(ctx context.Context, c *app.RequestContext, req *platformpb.UpdateCICronTriggerRequest) (*platformpb.UpdateCICronTriggerResponse, error)
}

type triggerRpcImpl struct {
	triggerClient triggerservice.Client
}

func NewTriggerRpc() TriggerRpc {
	triggerClient := GetClient()

	return &triggerRpcImpl{
		triggerClient: triggerClient,
	}
}
func (t triggerRpcImpl) SaveCICronTriggers(ctx context.Context, c *app.RequestContext, req *platformpb.SaveCICronTriggersRequest) (*platformpb.SaveCICronTriggersResponse, error) {
	user, ok := auth.GetUser(c)
	if !ok {
		return nil, pipeline_rpc.ErrUserAuthorization
	}
	res, err := t.triggerClient.SaveCICronTriggers(ctx, &triggerpb.SaveCICronTriggersRequest{
		GitRepo:      req.GitRepo,
		GitBranch:    req.GitBranch,
		YamlFilename: req.YamlFilename,
		Triggers:     req.Triggers,
		OperatedBy:   user.GetID(),
	})
	if err != nil {
		return nil, err
	}
	return &platformpb.SaveCICronTriggersResponse{
		Triggers: res.Triggers,
	}, nil
}

func (t triggerRpcImpl) GetCICronTriggers(ctx context.Context, c *app.RequestContext, req *platformpb.GetCICronTriggersRequest) (*platformpb.GetCICronTriggersResponse, error) {
	res, err := t.triggerClient.GetCICronTriggers(ctx, &triggerpb.GetCICronTriggersRequest{
		GitRepo:      req.GitRepo,
		GitBranch:    req.GitBranch,
		YamlFilename: req.YamlFilename,
		PageSize:     req.PageSize,
		PageNum:      req.PageNum,
	})
	if err != nil {
		return nil, err
	}
	return &platformpb.GetCICronTriggersResponse{
		Triggers:   res.Triggers,
		CiTriggers: res.CiTriggers,
		Pagination: res.Pagination,
	}, nil
}

func (t triggerRpcImpl) DeleteTriggers(ctx context.Context, c *app.RequestContext, req *platformpb.DeleteTriggersRequest) (*platformpb.DeleteTriggersResponse, error) {
	user, ok := auth.GetUser(c)
	if !ok {
		return nil, pipeline_rpc.ErrUserAuthorization
	}
	_, err := t.triggerClient.DeleteTriggers(ctx, &triggerpb.DeleteTriggersRequest{
		TriggerIds: req.TriggerIds,
		OperatedBy: user.GetID(),
	})
	if err != nil {
		return nil, err
	}
	return &platformpb.DeleteTriggersResponse{}, nil
}

func (t triggerRpcImpl) CreateCICronTrigger(ctx context.Context, c *app.RequestContext, req *platformpb.CreateCICronTriggerRequest) (*platformpb.CreateCICronTriggerResponse, error) {
	user, ok := auth.GetUser(c)
	if !ok {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	if req.CiTrigger.Trigger.TriggeredBy != user.GetID() {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	if err := ci_auth.CheckCodebaseAuth(ctx, user.GetJwt(), req.GitRepo); err != nil {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	if req.CiTrigger.Trigger.TriggeredBy != user.GetID() {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	res, err := t.triggerClient.CreateCICronTrigger(ctx, &triggerpb.CreateCICronTriggerRequest{
		GitRepo:    req.GitRepo,
		CiTrigger:  req.CiTrigger,
		OperatedBy: user.GetID(),
	})
	if err != nil {
		return nil, err
	}
	return &platformpb.CreateCICronTriggerResponse{
		CiTrigger: res.CiTrigger,
	}, nil
}

func (t triggerRpcImpl) UpdateCICronTrigger(ctx context.Context, c *app.RequestContext, req *platformpb.UpdateCICronTriggerRequest) (*platformpb.UpdateCICronTriggerResponse, error) {
	user, ok := auth.GetUser(c)
	if !ok {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	if req.CiTrigger.Trigger.TriggeredBy != user.GetID() {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	if err := ci_auth.CheckCodebaseAuth(ctx, user.GetJwt(), req.CiTrigger.GitRepo); err != nil {
		return nil, pipeline_rpc.ErrUserAuthorization
	}

	res, err := t.triggerClient.UpdateCICronTrigger(ctx, &triggerpb.UpdateCICronTriggerRequest{
		CiTrigger:  req.CiTrigger,
		OperatedBy: user.GetID(),
	})
	if err != nil {
		return nil, err
	}
	return &platformpb.UpdateCICronTriggerResponse{
		CiTrigger: res.CiTrigger,
	}, nil
}
