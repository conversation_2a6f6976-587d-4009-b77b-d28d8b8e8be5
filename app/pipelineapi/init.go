package main

import (
	"context"
	"net/http"
	"os"
	"time"

	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/service/permission"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/atom_rpc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/frontier_rpc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/git_rpc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/git_server"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/pipeline_rpc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/pmarshaller"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/template_rpc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/pkg/trigger_rpc"

	"code.byted.org/bytecycle/go-middlewares/xhertz/route"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/metainfo"
	"code.byted.org/middleware/hertz/pkg/app"
	"code.byted.org/middleware/hertz/pkg/app/server"

	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/localcache"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/pkms"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/rmq/consumer"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/rmq/producer"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/dal/tos"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/external/meego"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/middleware"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/openapi"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/service/codebasesvc"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/biz/service/engine"
	"code.byted.org/devinfra/hagrid/app/pipelineapi/config"
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/limiter"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/auth"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/i18n"
)

func Init(r *server.Hertz) {
	InitDependency(config.MustInitializeConfig())

	RegisterGlobalMiddleware(r)

	InitCustomPath(r)
}

func RegisterGlobalMiddleware(r *server.Hertz) {
	r.Use(
		middleware.Limiter,
		authMW(),
		middleware.CheckPermission,
		setCtxSwimLaneAndStressMW,
		setCtxUsernameAndJWTMW,
		setAcceptLanguageMW,
		middleware.GetOpenApiUserInfo(), // open api parse username
	)
}

func InitCustomPath(r *server.Hertz) {
	// service alive probe init
	r.GET("/api/v1/pipelines/ping", func(c context.Context, ctx *app.RequestContext) {
		ctx.String(http.StatusOK, "pong")
	})

	r.GET("/api/v1/p/ping", func(c context.Context, ctx *app.RequestContext) {
		ctx.String(http.StatusOK, "pong")
	})

	// BOE 「bits-pipeline-boe」app used to receive event in boe
	r.POST("/api/v1/p/check_git_event", CheckGitEvent)
	// PROD 「Pipeline」 app used to receive event in online
	r.POST("/api/v1/p/internal/check_git_event", CheckGitEvent)
	r.GET("/api/v1/p/internal/job_runs/:runId/artifacts/:artifactName", GetJobArtifact)
	r.GET("/api/v1/p/internal/job_runs/:runId/step_logs/:stepUId", GetJobStepLogInternal)

	openapiV1Group := r.Group("api/v1")
	openapi.RegisterOpenApiV1(openapiV1Group)

	openapiV2Group := r.Group("api/v2")
	openapi.RegisterOpenApiV2(openapiV2Group)
}

func InitDependency(config *config.Config) {
	mysql.MustInitialize(config.Mysql)
	redis.MustInitialize(config.Redis)
	codebasesvc.MustInitialize(config.Codebase)
	tos.MustInitialize(config.Tos)
	engine.MustInitialize(config.Engine)
	tcc.MustInitialize(config.Tcc)
	limiter.MustInit()
	producer.MustInitialize(config.Rmq)
	consumer.MustInitialize(config.Rmq)
	// es.MustInitialize(config.ES)
	pkms.MustInit()
	bits_err.Init()
	localcache.MustInitialize()
	if os.Getenv("LOCAL_MOD") != "true" {
		meego.MustInitialize()
	}
	trigger_rpc.GetClient()
	atom_rpc.GetClient()
	git_rpc.GetClient()
	git_server.GetClient()
	pipeline_rpc.GetClient()
	permission.GetPermissionService()
	frontier_rpc.GetFrontierRpc()
	template_rpc.NewTemplateRpc()
	template_rpc.GetClient()
	pmarshaller.NewPipelineMarshaller(atom_rpc.GetClient())
}

func authMW() app.HandlerFunc {
	authConfig := auth.DefaultConfig()
	// TODO: delete later
	authConfig.NeedCheckIss = false
	authConfig.SkipCfg = auth.SkipCfg{
		Prefix: []string{
			"POST/api/v1/p/webhook_callback",
			"POST/api/v1/p/check_git_event",
			"POST/api/v1/p/internal",
			"GET/api/v1/p/internal",
		},
	}
	return auth.New(authConfig)
}

// set swimlane and stress to ctx
var setCtxSwimLaneAndStressMW = func(ctx context.Context, c *app.RequestContext) {
	swimlane := string(c.GetHeader(constvar.HttpHeader_XTTEnv))
	if swimlane != "" {
		ctx = context.WithValue(ctx, constvar.ContextKey_KEnv, swimlane)
	}
	stressTag := string(c.GetHeader(constvar.HttpHeader_XTTStress))
	if stressTag != "" {
		ctx = context.WithValue(ctx, constvar.ContextKey_KStress, stressTag)
	}
	c.Next(ctx)
}

// add username and jwt to context
var setCtxUsernameAndJWTMW = func(ctx context.Context, c *app.RequestContext) {
	if user, ok := auth.GetUser(c); ok {
		ctx = metainfo.WithPersistentValue(ctx, constvar.ContextKey_USERNAME, user.GetID())
		ctx = metainfo.WithPersistentValue(ctx, constvar.ContextKey_JwtToken, user.GetJwt())
		ctx = metainfo.WithValue(ctx, constvar.ContextKey_USERNAME, user.GetID())
		ctx = metainfo.WithValue(ctx, constvar.ContextKey_JwtToken, user.GetJwt())
		ctx = context.WithValue(ctx, constvar.ContextKey_USERNAME, user.GetID())
		ctx = context.WithValue(ctx, constvar.ContextKey_JwtToken, user.GetJwt())
	} else {
		username := string(c.GetHeader(constvar.ContextKey_USERNAME))
		if env.IsBoe() && username != "" {
			ctx = metainfo.WithPersistentValue(ctx, constvar.ContextKey_USERNAME, username)
			ctx = metainfo.WithValue(ctx, constvar.ContextKey_USERNAME, username)
			ctx = context.WithValue(ctx, constvar.ContextKey_USERNAME, username)
		}
		jwtToken := string(c.GetHeader(constvar.HttpHeader_XJwtToken))
		if env.IsBoe() && jwtToken != "" {
			ctx = metainfo.WithPersistentValue(ctx, constvar.ContextKey_JwtToken, jwtToken)
			ctx = metainfo.WithValue(ctx, constvar.ContextKey_JwtToken, jwtToken)
			ctx = context.WithValue(ctx, constvar.ContextKey_JwtToken, jwtToken)
		}
	}
	c.Next(ctx)
}

// add accept language
var setAcceptLanguageMW = func(ctx context.Context, c *app.RequestContext) {
	lang := string(c.GetHeader(constvar.HttpHeader_AcceptLanguage))
	if lang != "" {
		ctx = metainfo.WithPersistentValue(ctx,
			constvar.ContextKey_AcceptLanguage, string(c.GetHeader(constvar.HttpHeader_AcceptLanguage)))
	}
	c.Next(ctx)
}

func I18n() route.Option {
	const appModule = "bits.pipeline.api"
	serviceStorage := i18n.NewServiceStorage(
		appModule,                         // 业务模块，需唯一且不建议之后有变动
		i18n.WithCacheTTL(time.Second*10), // ServiceStorage实现中使用了一层缓存，CacheTTL用于指定文案在该缓存中的过期时间
		// i18n.WithCache(Your Implement), // 该选项可以替换缓存实现
		i18n.WithForceLatest(),
	)

	mw := i18n.NewMiddleware(
		serviceStorage,                      // 使用文案存储服务作为中间件的data access layer, 可以被其他实现替换
		i18n.WithUploadText(true),           // 中间件是否负责上传request中的文案，默认false
		i18n.WithFallbackByTextInResp(true), // true：key不存在或者查询存储失败时，会使用Resp里的Texts里的内容作为fallback
		i18n.WithEmptyReplace(false),        // true：没有标注时 i18nString.value中的目标语言为空，也替换
		i18n.WithBatchGet(true),
	)

	return route.WithInterceptor(
		mw.GetInterceptor(),
	)
}
