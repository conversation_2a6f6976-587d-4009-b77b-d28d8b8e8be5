package entity

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type PipelineCallbackPatrol struct {
	ID           uint64               `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	InstanceID   uint64               `json:"instance_id" gorm:"column:instance_id"`
	InstanceType CallbackInstanceType `json:"instance_type" gorm:"column:instance_type"`
	CallbackBody datatypes.JSON       `json:"callback_body" gorm:"column:callback_body"`
	FailType     CallbackFailType     `json:"fail_type" gorm:"column:fail_type"`
	FailReason   string               `json:"fail_reason" gorm:"column:fail_reason"`
	LogID        string               `json:"log_id" gorm:"column:log_id"`
	CreatedAt    time.Time            `json:"created_at" gorm:"column:created_at"`
	UpdatedAt    time.Time            `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt    gorm.DeletedAt       `json:"deleted_at" gorm:"deleted_at"`
}

func (m *PipelineCallbackPatrol) TableName() string {
	return "pipeline_callback_patrol"
}

type CallbackInstanceType string

const (
	CallbackInstanceTypePipeline CallbackInstanceType = "pipeline"
	CallbackInstanceTypeStage    CallbackInstanceType = "stage"
	CallbackInstanceTypeJob      CallbackInstanceType = "job"
	CallbackInstanceTypeStep     CallbackInstanceType = "step"
)

type CallbackFailType string

const (
	CallbackFailTypeNotDefined              CallbackFailType = "not_defined"
	CallbackFailTypeRecordNotFound          CallbackFailType = "record_not_found"
	CallbackFailTypeUpStreamFailedToRespond CallbackFailType = "upstream_failed_to_respond"
)
