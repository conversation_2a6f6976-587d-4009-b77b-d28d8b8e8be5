package entity

import (
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
)

type PipelineRunTrigger struct {
	ID             uint64      `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	PipelineID     uint64      `json:"pipeline_id" gorm:"column:pipeline_id"`
	TriggerType    TriggerType `json:"trigger_type" gorm:"column:trigger_type"`
	RunID          uint64      `json:"run_id" gorm:"column:run_id"`
	RepoID         int64       `json:"repo_id" gorm:"column:repo_id"`
	RepoName       string      `json:"repo_name" gorm:"column:repo_name"`
	Branch         string      `json:"branch" gorm:"column:branch"`
	YamlFilename   string      `json:"yaml_filename" gorm:"column:yaml_filename"`
	MrID           int64       `json:"mr_id" gorm:"column:mr_id"`
	MrUrl          string      `json:"mr_url" gorm:"column:mr_url"`
	MrTitle        string      `json:"mr_title" gorm:"column:mr_title"`
	MrSourceBranch string      `json:"mr_source_branch" gorm:"column:mr_source_branch"`
	MrTargetBranch string      `json:"mr_target_branch" gorm:"column:mr_target_branch"`
	CheckSuiteID   int64       `json:"check_suite_id" gorm:"column:check_suite_id"`
	CommitSha      string      `json:"commit_sha" gorm:"column:commit_sha"`
	CommitMessage  []byte      `json:"commit_message" gorm:"column:commit_message"`
	TriggerContext []byte      `json:"trigger_context" gorm:"column:trigger_context"`
	CreatedBy      string      `json:"created_by" gorm:"column:created_by"`
	CreatedAt      time.Time   `json:"created_at" gorm:"column:created_at;autoCreateTime"`
}

func (m *PipelineRunTrigger) TableName() string {
	return "pipeline_run_trigger"
}

type TriggerType int8

const (
	TriggerTypeUnknown    TriggerType = iota // unknown
	TriggerTypeManual                        // Manual
	TriggerTypeSchedule                      // Cron
	TriggerTypeApi                           // api default
	TriggerTypeRollbacked                    // api rollbacked
	TriggerTypeGitMrPushed
	TriggerTypeGitMrOpened
	TriggerTypeGitMrMerged
	TriggerTypeGitMrClosed
	TriggerTypeGitMrReopened
	TriggerTypeGitMrUpdated
	TriggerTypeGitPushed
	TriggerTypeGitDeleted
)

func (t TriggerType) ToPB() platformpb.TriggerType {
	switch t {
	case TriggerTypeManual:
		return platformpb.TriggerType_TRIGGER_TYPE_MANUAL
	case TriggerTypeSchedule:
		return platformpb.TriggerType_TRIGGER_TYPE_SCHEDULE
	case TriggerTypeApi:
		return platformpb.TriggerType_TRIGGER_TYPE_API
	case TriggerTypeRollbacked:
		return platformpb.TriggerType_TRIGGER_TYPE_ROLLBACKED
	case TriggerTypeGitMrPushed:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_PUSHED
	case TriggerTypeGitMrOpened:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_OPENED
	case TriggerTypeGitMrMerged:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_MERGED
	case TriggerTypeGitMrClosed:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_CLOSED
	case TriggerTypeGitMrReopened:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_REOPENED
	case TriggerTypeGitMrUpdated:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_UPDATED
	case TriggerTypeGitPushed:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_PUSHED
	case TriggerTypeGitDeleted:
		return platformpb.TriggerType_TRIGGER_TYPE_GIT_DELETED
	default:
		return platformpb.TriggerType_TRIGGER_TYPE_UNSPECIFIED
	}
}

func (t TriggerType) ToMrStatus() platformpb.MrStatus {
	switch t {
	case TriggerTypeGitMrOpened,
		TriggerTypeGitMrPushed,
		TriggerTypeGitMrReopened,
		TriggerTypeGitMrUpdated:
		return platformpb.MrStatus_MR_STATUS_OPEN
	case TriggerTypeGitMrMerged:
		return platformpb.MrStatus_MR_STATUS_MERGED
	case TriggerTypeGitMrClosed:
		return platformpb.MrStatus_MR_STATUS_CLOSED
	default:
		return platformpb.MrStatus_MR_STATUS_UNSPECIFIED
	}
}

func (t TriggerType) String() string {
	switch t {
	case TriggerTypeManual:
		return "manual"
	case TriggerTypeSchedule:
		return "schedule"
	case TriggerTypeApi:
		return "api"
	case TriggerTypeRollbacked:
		return "rollbacked"
	case TriggerTypeGitMrPushed:
		return "git_mr_pushed"
	case TriggerTypeGitMrOpened:
		return "git_mr_opened"
	case TriggerTypeGitMrMerged:
		return "git_mr_merged"
	case TriggerTypeGitMrClosed:
		return "git_mr_closed"
	case TriggerTypeGitMrReopened:
		return "git_mr_reopened"
	case TriggerTypeGitMrUpdated:
		return "git_mr_updated"
	case TriggerTypeGitPushed:
		return "git_pushed"
	case TriggerTypeGitDeleted:
		return "git_deleted"
	}
	return "unknown"
}

func (t TriggerType) BcString() string {
	switch t {
	case TriggerTypeManual:
		return "handle trigger"
	case TriggerTypeApi:
		return "api trigger"
	case TriggerTypeSchedule:
		return "triggered by cron event"
	case TriggerTypeRollbacked:
		return "rollback trigger"
	default:
		if IsGitTriggerType(t) {
			return "triggered by git event"
		}
		return t.String()
	}
}

func TriggerTypeFromString(s string) (TriggerType, error) {
	switch s {
	case "manual":
		return TriggerTypeManual, nil
	case "schedule":
		return TriggerTypeSchedule, nil
	case "api":
		return TriggerTypeApi, nil
	case "rollbacked":
		return TriggerTypeRollbacked, nil
	case "git_mr_pushed":
		return TriggerTypeGitMrPushed, nil
	case "git_mr_opened":
		return TriggerTypeGitMrOpened, nil
	case "git_mr_merged":
		return TriggerTypeGitMrMerged, nil
	case "git_mr_closed":
		return TriggerTypeGitMrClosed, nil
	case "git_mr_reopened":
		return TriggerTypeGitMrClosed, nil
	case "git_mr_updated":
		return TriggerTypeGitMrUpdated, nil
	case "git_pushed":
		return TriggerTypeGitPushed, nil
	case "git_deleted":
		return TriggerTypeGitDeleted, nil
	}
	return TriggerTypeUnknown, fmt.Errorf("unknown trigger type %s", s)
}

func TriggerTypeFromPB(s platformpb.TriggerType) TriggerType {
	switch s {
	case platformpb.TriggerType_TRIGGER_TYPE_MANUAL:
		return TriggerTypeManual
	case platformpb.TriggerType_TRIGGER_TYPE_SCHEDULE:
		return TriggerTypeSchedule
	case platformpb.TriggerType_TRIGGER_TYPE_API:
		return TriggerTypeApi
	case platformpb.TriggerType_TRIGGER_TYPE_ROLLBACKED:
		return TriggerTypeRollbacked
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_OPENED:
		return TriggerTypeGitMrOpened
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_MERGED:
		return TriggerTypeGitMrMerged
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_CLOSED:
		return TriggerTypeGitMrClosed
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_REOPENED:
		return TriggerTypeGitMrReopened
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_UPDATED:
		return TriggerTypeGitMrUpdated
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_MR_PUSHED:
		return TriggerTypeGitMrPushed
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_PUSHED:
		return TriggerTypeGitPushed
	case platformpb.TriggerType_TRIGGER_TYPE_GIT_DELETED:
		return TriggerTypeGitDeleted
	}
	return TriggerTypeManual // 默认手动
}

var gitTriggerTypeMap = map[TriggerType]bool{
	TriggerTypeGitMrPushed:   true,
	TriggerTypeGitMrUpdated:  true,
	TriggerTypeGitMrMerged:   true,
	TriggerTypeGitMrReopened: true,
	TriggerTypeGitMrOpened:   true,
	TriggerTypeGitMrClosed:   true,
	TriggerTypeGitPushed:     true,
	TriggerTypeGitDeleted:    true,
}

func IsGitTriggerType(triggerType TriggerType) bool {
	if _, ok := gitTriggerTypeMap[triggerType]; ok {
		return true
	}
	return false
}

var mrTriggerTypeMap = map[TriggerType]bool{
	TriggerTypeGitMrPushed:   true,
	TriggerTypeGitMrUpdated:  true,
	TriggerTypeGitMrMerged:   true,
	TriggerTypeGitMrReopened: true,
	TriggerTypeGitMrOpened:   true,
	TriggerTypeGitMrClosed:   true,
}

func IsMrTriggerType(triggerType TriggerType) bool {
	if _, ok := mrTriggerTypeMap[triggerType]; ok {
		return true
	}
	return false
}
