load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "notify",
    srcs = ["service.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/pipelineeventrpc/biz/service/notify",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pipelineeventrpc/biz/provider/lark",
        "//app/pipelineeventrpc/biz/service/card",
        "//idls/byted/devinfra/pipeline/dsl:dsl_go_proto",
        "//internal/pipeline/pevent",
        "//internal/pipeline/utils",
        "@com_github_hashicorp_go_multierror//:go-multierror",
        "@com_github_larksuite_oapi_sdk_go_v3//:oapi-sdk-go",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//gslice",
    ],
)
