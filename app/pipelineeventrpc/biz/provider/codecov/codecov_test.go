package codecov

import (
	"context"
	"testing"
)

func TestMain(m *testing.M) {
	MustInitialize()
	m.Run()
}

func TestGetChangeCoverage(t *testing.T) {
	t.Run("test get change coverage", func(t *testing.T) {
		coverage, err := GetChangeCoverage(context.Background(), 597472439184995, "tce/zzmtest1", "b088f2c68a296723b4b44904e197cb93ead1841f")
		if err != nil {
			t.<PERSON><PERSON>(err)
		}
		t.Logf("%+v", coverage)
	})
}

func TestGetCommitCoverage(t *testing.T) {
	t.Run("test get commit coverage", func(t *testing.T) {
		coverage, err := GetCommitCoverage(context.Background(), "codebase/yangtze", "b0b41f81cb9c1be2ff9c1b0bf778b82fcc32377c")
		if err != nil {
			t.<PERSON>(err)
		}
		t.Logf("%+v", coverage)
	})
}
