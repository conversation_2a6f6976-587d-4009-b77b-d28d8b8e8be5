package pipeline

import (
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb/pipelineservice"
	"code.byted.org/devinfra/hagrid/pbgen/rpc/bits/pipelinerpc"
	"github.com/cloudwego/kitex/client"
)

var cli pipelineservice.Client

func GetClient() pipelineservice.Client {
	if cli == nil {
		opts := []client.Option{
			client.WithMiddleware(kitexmw.LogRequestResponse),
		}
		cli = pipelineservice.MustNewClient(pipelinerpc.PSM, opts...)
	}
	return cli
}
