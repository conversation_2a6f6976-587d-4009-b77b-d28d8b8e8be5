package pipeline

import (
	"code.byted.org/gopkg/logs"
	"context"
	"github.com/bytedance/sonic"

	"code.byted.org/devinfra/hagrid/internal/pipeline/pevent"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
)

var (
	pipelineStatusMap = map[dslpb.PipelineRunStatus]string{
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_UNSPECIFIED: pevent.EVENT_STATUS_UNKNOWN,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_WAITING:     pevent.EVENT_STATUS_WAITING,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_BLOCKING:    pevent.EVENT_STATUS_BLOCKING,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING:     pevent.EVENT_STATUS_RUNNING,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_SUCCEEDED:   pevent.EVENT_STATUS_SUCCEEDED,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_FAILED:      pevent.EVENT_STATUS_FAILED,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_CANCELLED:   pevent.EVENT_STATUS_CANCELLED,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_CANCELLING:  pevent.EVENT_STATUS_CANCELLING,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_ROLLBACKING: pevent.EVENT_STATUS_ROLLBACKING,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_ROLLBACKED:  pevent.EVENT_STATUS_ROLLBACKED,
		dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_QUEUING:     pevent.EVENT_STATUS_START,
	}
)

func GetPipelineRunStatus(ctx context.Context, pipelineRunID uint64) (string, error) {
	resp, err := GetClient().GetPipelineRun(ctx, &servicepb.GetPipelineRunRequest{
		RunId: pipelineRunID,
	})
	if err != nil {
		return pevent.EVENT_STATUS_UNKNOWN, err
	}
	return pipelineStatusMap[resp.GetPipelineRun().GetRunStatus()], nil
}

type RunParams struct {
	Repo struct {
		Id          int    `json:"id"`
		Name        string `json:"name"`
		ExternalUrl string `json:"external_url"`
	} `json:"repo"`
	GitMrUrl    string `json:"git_mr_url"`
	GitMrTitle  string `json:"git_mr_title"`
	GitMrDetail struct {
		Status string `json:"status"`
	} `json:"git_mr_detail"`
	SourceBranch  string `json:"source_branch"`
	TargetBranch  string `json:"target_branch"`
	GitTagName    string `json:"git_tag_name"`
	CommitMessage string `json:"commit_message"`
	Branch        string `json:"branch"`
}

func ConvertRunParams(ctx context.Context, runParamsMap map[string]any) (r RunParams, err error) {
	runParamsBytes, err := sonic.Marshal(runParamsMap)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	_ = sonic.Unmarshal(runParamsBytes, &r)
	return
}
