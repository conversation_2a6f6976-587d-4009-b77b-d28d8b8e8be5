// Code generated by hertztool.

package handler

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/app/spaceapi/pkg/bytest"
	"code.byted.org/devinfra/hagrid/app/spaceapi/pkg/cache"
	"code.byted.org/devinfra/hagrid/app/spaceapi/pkg/rpc"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/apipb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/gutil"
	"github.com/bytedance/sonic"
	"golang.org/x/sync/errgroup"
)

// GetSpaceList .
// @router /api/v1/space/list [GET]

type clientExtra struct {
	TechnologyStack string `json:"technologyStack"`
	Region          string `json:"region"`
	AppId           int64  `json:"appId"`
	GitUrl          string `json:"gitUrl"`
	DevScene        string `json:"devScene"`
	GroupName       string `json:"groupName"`
}

func convertRPCSpaceToHTTP(s *rpcpb.SpaceBrief) *apipb.SpaceBrief {
	return &apipb.SpaceBrief{
		Id:             strconv.FormatUint(s.Id, 10),
		Name:           s.Name,
		Type:           parseSpaceType(s.Type),
		BytetreeId:     gutil.IfThen(s.MigrateTo == 0, "", strconv.FormatInt(s.BytetreeId, 10)),
		Avatar:         s.Avatar,
		Description:    s.Description,
		HasPermission:  s.HasPermission,
		Identification: s.Identification,
		MigrateId:      gutil.IfThen(s.MigrateTo == 0, "", strconv.FormatUint(s.MigrateTo, 10)),
		TenantKey:      s.TenantKey,
		Extra:          s.Extra,
		/*
			20240823 by @liangbo.albus
			组件包搜索空间组件，需要批量查询空间信息，调用这个接口。
			发现这个接口一直都没有返回 oldPlatformId 字段，提供这个字段给他们作为 appid
			前端需求同学 @songjianying @huleye
		*/
		OldPlatformId: s.OldPlatformId,
	}
}

func sortSpaces(spaces []*apipb.SpaceDetail) {
	sort.Slice(spaces, func(i, j int) bool {
		if spaces[i].HasPermission && !spaces[j].HasPermission {
			return true
		} else if !spaces[i].HasPermission && spaces[j].HasPermission {
			return false
		} else if spaces[i].HasPermission == spaces[j].HasPermission {
			return spaces[i].Id > spaces[j].Id
		}
		return false
	})
}

func FillSpaces(ctx context.Context, spaces []*apipb.SpaceDetail, withClientExtra, withGeneralExtra, withAdmin bool) (err error) {
	if len(spaces) == 0 {
		return
	}
	// 20240708 这段代码中原来使用的是 pool,cCtx := errgroup.WithContext(ctx)
	// 但是在使用时发现，只要有一个go rountine 返回err，会导致 ctx 被取消，
	// 其他所有并发调用都会停止
	// 所以改成了 pool := errgroup.Group{}
	pool := errgroup.Group{}

	spaceMap := gslice.ToMap(spaces, func(t *apipb.SpaceDetail) (string, *apipb.SpaceDetail) {
		return t.Id, t
	})
	if withClientExtra {
		pool.Go(func() error {
			return FillSpacesClientExtra(ctx, spaceMap)
		})
	}
	if withGeneralExtra {
		pool.Go(func() error {
			return FillSpacesGeneralExtra(ctx, spaceMap)
		})
	}
	if withAdmin {
		pool.Go(func() error {
			return FillSpacesWithAdmin(ctx, spaceMap)
		})
	}
	err = pool.Wait()
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return
	}
	return
}

func FillSpacesClientExtra(ctx context.Context, spaceMap map[string]*apipb.SpaceDetail) (err error) {
	clientSpaceMap := gmap.FilterValues(spaceMap, func(detail *apipb.SpaceDetail) bool {
		return detail.Type == rpcpb.SpaceType_CLIENT
	})
	if len(clientSpaceMap) == 0 {
		return
	}
	clientSpaces := gmap.Values(clientSpaceMap)
	bizIds := gslice.Map(clientSpaces, func(f *apipb.SpaceDetail) uint64 {
		result, _ := strconv.ParseUint(f.Id, 10, 64)
		return result
	})
	bizPluginInfos, err := rpc.ListSpaceBizInfos(ctx, bizIds, rpcpb.PluginType_PLUGIN_TYPE_CLIENT)
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return
	}
	bizPluginInfoMap := gslice.ToMap(bizPluginInfos, func(t *rpcpb.Plugin) (string, *rpcpb.Plugin) {
		return strconv.FormatUint(t.SpaceId, 10), t
	})
	appInfoMap, err := rpc.GetAppSimpleInfoMap(ctx, gslice.Map(bizPluginInfos, func(f *rpcpb.Plugin) int64 {
		return f.BizId
	}))
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return
	}

	for _, clientSpace := range clientSpaces {
		clientSpaceMap[clientSpace.Id].Extra = "{}"

		plugin, ok := bizPluginInfoMap[clientSpace.Id]
		if !ok {
			log.V2.Warn().With(ctx).Str(clientSpace.Id).Str("not find in clientSpace bizPluginInfoMap").Emit()
			continue
		}
		appInfo, ok := appInfoMap[plugin.BizId]
		if !ok {
			log.V2.Warn().With(ctx).Int64(plugin.BizId).Str("not find in clientSpace appInfoMap").Emit()
			continue
		}
		clientSpaceMap[clientSpace.Id].Extra, _ = sonic.MarshalString(clientExtra{
			TechnologyStack: appInfo.TechnologyStack.String(),
			Region:          appInfo.Region,
			AppId:           appInfo.AppCloudId,
			GitUrl:          appInfo.GitUrl,
			DevScene:        "client",
			GroupName:       appInfo.EnglishName,
		})
	}
	return
}

func FillSpacesGeneralExtra(ctx context.Context, spaceMap map[string]*apipb.SpaceDetail) (err error) {
	generalSpaceMap := gmap.FilterValues(spaceMap, func(detail *apipb.SpaceDetail) bool {
		return detail.Type == rpcpb.SpaceType_GNERNAL
	})
	if len(generalSpaceMap) == 0 {
		return
	}
	generalSpaces := gmap.Values(generalSpaceMap)
	spaceIds := gslice.Map(generalSpaces, func(f *apipb.SpaceDetail) uint64 {
		result, _ := strconv.ParseUint(f.Id, 10, 64)
		return result
	})
	extraMap, err := bytest.GetSpaceExtraMap(ctx, spaceIds)
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return
	}

	for _, generalSpace := range generalSpaces {
		generalSpaceMap[generalSpace.Id].Extra = "{}"

		extra, ok := extraMap[generalSpace.Id]
		if !ok {
			log.V2.Warn().With(ctx).Str(generalSpace.Id).Str("not find in generalSpace extraMap").Emit()
			continue
		}
		generalSpaceMap[generalSpace.Id].Extra = extra
	}
	return
}

func FillSpacesWithAdmin(ctx context.Context, spaceMap map[string]*apipb.SpaceDetail) (err error) {
	binding, err := rpc.QueryRoleBinding(ctx, &authz.QueryRoleBindingRequest{
		ResourceBrns: gslice.Map(gmap.Values(spaceMap), func(f *apipb.SpaceDetail) string {
			return fmt.Sprintf("brn::bits:::space:%s", f.Id)
		}),
		RolesFilters: []string{"bits.serviceSpaceAdmin", "bits.appSpaceAdmin", "bits.generalSpaceAdmin", "bits.serviceSpaceOwner", "bits.appSpaceOwner", "bits.generalSpaceOwner"},
		PageNum:      1,
		PageSize:     math.MaxInt32,
	})
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return
	}
	for _, bind := range binding.Bindings {
		sid := strings.TrimPrefix(bind.ResourceBrn, "brn::bits:::space:")
		if _, ok := spaceMap[sid]; !ok {
			log.V2.Error().With(ctx).Str(sid).Str("not find in spaceResultMap").Emit()
			continue
		}
		spaceMap[sid].Admins = append(spaceMap[sid].Admins, gslice.Map(bind.Principals, func(f string) string {
			return strings.TrimPrefix(f, "brn::iam:::user_account:")
		})...)
	}
	for _, space := range spaceMap {
		space.Admins = set.New(space.Admins...).ToSlice()
	}
	return
}

func GetEnabledPluginListWithCache(ctx context.Context, spaceID uint64) ([]*rpcpb.GetEnablePluginListResp_EnabledPlugin, error) {
	if pluginList, found := cache.GetList[rpcpb.GetEnablePluginListResp_EnabledPlugin](ctx, fmt.Sprintf("get_enable_plugin_info_%d", spaceID)); found {
		return pluginList, nil
	}
	plugins, err := rpc.GetEnablePluginList(ctx, spaceID)
	if err != nil {
		logs.CtxError(ctx, "failed to get enable plugin list for %d", spaceID)
		return nil, err
	}
	_ = cache.Set(ctx, fmt.Sprintf("get_enable_plugin_info_%d", spaceID), plugins, time.Hour)
	return plugins, nil
}

func FillFavorite(spaces []*apipb.SpaceBrief) []*apipb.SpaceBrief {
	for i := 0; i < len(spaces); i++ {
		spaces[i].IsFavorite = true
	}
	return spaces
}
