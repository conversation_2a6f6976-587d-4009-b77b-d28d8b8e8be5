/**
 * @Date: 2022/12/21
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package handler

import (
	"context"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/auth"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/apipb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/pkg/errors"
)

func (s SpaceApiServiceImpl) ListMeegoSpace(ctx context.Context, c *app.RequestContext, req *apipb.ListMeegoSpacesRequest) (*apipb.ListMeegoSpacesResponse, error) {
	user, _ := auth.GetUser(c)
	username := user.GetID()
	email := emails.WithSuffix(username)

	resp, err := s.spaceClient.ListMeegoSpaces(ctx, &rpcpb.ListMeegoSpacesRequest{Email: email})
	if err != nil {
		return nil, errors.Unwrap(err)
	}

	return &apipb.ListMeegoSpacesResponse{Spaces: TransformMeegoSpaces(resp.Spaces)}, nil
}

func TransformMeegoSpaces(inputs []*rpcpb.MeegoSpace) []*apipb.MeegoSpace {
	return gslice.Map(inputs, TransformMeegoSpace)
}

func TransformMeegoSpace(input *rpcpb.MeegoSpace) *apipb.MeegoSpace {
	return &apipb.MeegoSpace{
		Id:         input.Id,
		Name:       input.Name,
		SimpleName: input.SimpleName,
	}
}

func (s SpaceApiServiceImpl) GetMeegoSpaces(ctx context.Context, c *app.RequestContext, req *apipb.GetMeegoSpacesRequest) (*apipb.GetMeegoSpacesResponse, error) {
	user, _ := auth.GetUser(c)
	username := user.GetID()
	email := emails.WithSuffix(username)

	request := &rpcpb.GetMeegoSpacesRequest{
		Ids:   req.Ids,
		Email: email,
	}

	resp, err := s.spaceClient.GetMeegoSpaces(ctx, request)
	if err != nil {
		return nil, errors.Unwrap(err)
	}

	return &apipb.GetMeegoSpacesResponse{Spaces: TransformMeegoSpaces(resp.Spaces)}, nil
}
