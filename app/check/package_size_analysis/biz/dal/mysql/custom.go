package mysql

import (
	"code.byted.org/gopkg/logs"
	"context"
	"database/sql"
	"errors"
	"gorm.io/gorm"
)

func GetCheckWithDefault(ctx context.Context, appId int32) (check PackageSizeCheck, err error) {
	check, err = WriteRepoOperator.GetCheck(ctx, appId)
	if MysqlNoRows(err) {
		err = nil
		check = PackageSizeCheck{
			Able:        0,
			Id:          0,
			AppId:       int(appId),
			Value:       0,
			Approver:    "",
			PackagePath: "",
		}
	}
	return
}

func FilterPipelines(ctx context.Context, pipelineIds []int64) (newPipelineIds []int64, err error) {
	newPipelineIds = make([]int64, 0)
	for _, id := range pipelineIds {
		_, err = WriteRepoOperator.ListPackageSizeInfoByPipelineId(ctx, id)
		if MysqlNoRows(err) {
			err = nil
			continue
		} else if err != nil {
			logs.CtxError(ctx, err.Error())
			return
		}
		newPipelineIds = append(newPipelineIds, id)
	}
	return
}

func MysqlNoRows(err error) bool {
	if err == nil {
		return false
	}
	if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, sql.ErrNoRows) {
		return true
	}
	return false
}
