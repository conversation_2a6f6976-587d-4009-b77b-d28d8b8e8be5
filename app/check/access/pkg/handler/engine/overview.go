package engine

import (
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"strconv"

	"code.byted.org/devinfra/hagrid/app/check/access/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/check/access/pkg/common"
	"code.byted.org/devinfra/hagrid/app/check/access/pkg/service/rpc"

	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	"code.byted.org/gopkg/logs"
)

func getCodeCheckOverview(ctx context.Context, appID, hostID int64) (overView Overview, err error) {
	// oneSite api for get mr scan execution info
	const getExecutionInfoAPI = "https://code.byted.org/analysis/api/v3/unstable/get_rapp_mr_execution_info"

	hosts, err := rpc.GetMrsByHostId(ctx, hostID)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	var codeCheckResp CodeCheckResp
	for _, host := range hosts {
		param := map[string]int64{"app_id": appID, "host_id": host.HostId}
		resp, err := utils.DoRequest(ctx, param, getExecutionInfoAPI)
		if err != nil {
			logs.CtxError(ctx, err.Error())
			err = nil
			continue
		}
		var tmpResp CodeCheckResp
		err = json.Unmarshal(resp.Body(), &tmpResp)
		if err != nil {
			logs.CtxError(ctx, err.Error())
			err = nil
			continue
		}
		if codeCheckResp.Data.StartTime == 0 || codeCheckResp.Data.StartTime > tmpResp.Data.StartTime {
			codeCheckResp.Data.StartTime = tmpResp.Data.StartTime
		}
		if codeCheckResp.Data.EndTime < tmpResp.Data.EndTime {
			codeCheckResp.Data.EndTime = tmpResp.Data.EndTime
		}
		codeCheckResp.Data.Overview.Total += tmpResp.Data.Overview.Total
		codeCheckResp.Data.Overview.Intercept += tmpResp.Data.Overview.Intercept
		codeCheckResp.Data.Overview.Prompt += tmpResp.Data.Overview.Prompt
		codeCheckResp.Data.Overview.CheckedIssues.P0 += tmpResp.Data.Overview.CheckedIssues.P0
		codeCheckResp.Data.Overview.CheckedIssues.P1 += tmpResp.Data.Overview.CheckedIssues.P1
		codeCheckResp.Data.Overview.CheckedIssues.P2 += tmpResp.Data.Overview.CheckedIssues.P2
		codeCheckResp.Data.Overview.CheckedIssues.P3 += tmpResp.Data.Overview.CheckedIssues.P3
	}

	overView.startTime = int64(codeCheckResp.Data.StartTime)
	overView.endTime = int64(codeCheckResp.Data.EndTime)
	//detail := fmt.Sprintf("Total Problems %d\n", codeCheckResp.Data.Overview.Total)
	//detail += getCodeCheckIssueDetail(codeCheckResp.Data.Overview.CheckedIssues.P0, codeCheckResp.Data.Overview.IssueThreshold.P0, "Fatal Problems")
	//detail += getCodeCheckIssueDetail(codeCheckResp.Data.Overview.CheckedIssues.P1, codeCheckResp.Data.Overview.IssueThreshold.P1, "Serious Problems")
	//detail += getCodeCheckIssueDetail(codeCheckResp.Data.Overview.CheckedIssues.P2, codeCheckResp.Data.Overview.IssueThreshold.P2, "Normal Problems")
	//detail += getCodeCheckIssueDetail(codeCheckResp.Data.Overview.CheckedIssues.P3, codeCheckResp.Data.Overview.IssueThreshold.P3, "Notice Problems")
	overView.detail = codeCheckResp.Data.Overview.Message
	return
}

func getCodeCheckIssueDetail(value, threshold int, issueName string) (str string) {
	if value == 0 {
		return
	}
	str = fmt.Sprintf("\n%s %d items", issueName, value)
	if threshold == 0 {
		str += ",not set a threshold"
		return
	}
	if value >= threshold {
		str += fmt.Sprintf(">=threshold %d items,exceeds the threshold", threshold)
	} else {
		str += fmt.Sprintf("<threshold %d items,not exceeds the threshold", threshold)
	}
	return
}

func getSDLCOverview(ctx context.Context, hostID int64) (sdlcInfo SDLCInfo, err error) {
	mrInfo, err := rpc.OptimusClient.GetMainMrInfo(ctx, &optimus.GetMrMainInfoQuery{
		MrID: hostID,
	})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	param := map[string]string{
		"project_id": strconv.FormatInt(mrInfo.Info.GetProjectID(), 10),
		"mr_iid":     strconv.FormatInt(mrInfo.Info.GetIID(), 10),
	}
	header := map[string]string{
		"X-Shepherd-token": common.SDLCCNToken,
	}
	url := common.SDLCCNHost + "/open-api/v2/scan_task/brief"
	var sdlcResp SDLCResp
	_, err = utils.DoGetWithHeadersQueryParams(ctx, &sdlcResp, header, param, url)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	sdlcInfo.startTime = sdlcResp.Data.StartTime
	sdlcInfo.endTime = sdlcResp.Data.EndTime
	sdlcInfo.IsShow = sdlcResp.Data.IsShow
	sdlcInfo.Status = sdlcResp.Data.Status
	detail := ""
	for _, story := range sdlcResp.Data.Brief {
		for _, item := range story.Detail {
			if item.Status == "" {
				detail += fmt.Sprintf("%s of %s failed", item.Title.En, story.StoryName)
			}
		}
	}
	sdlcInfo.detail = detail
	return
}

func getUnitTestOverview(ctx context.Context, hostID int64) (overView Overview, err error) {
	url := fmt.Sprintf("https://baymax.bytedance.net/api/unit_test/mr_report/%d", hostID)
	resp, err := utils.DoGet(ctx, url, nil)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}

	var unitTestResp UnitTestResp
	err = json.Unmarshal(resp, &unitTestResp)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	overView.startTime = unitTestResp.Data.TaskInfo.CreatedAt
	overView.endTime = unitTestResp.Data.TaskInfo.FinishedAt
	overView.detail = unitTestResp.Data.Brief
	if overView.detail == "" {
		overView.detail = fmt.Sprintf("This unit test executes a total of %d cases, %d succeeds, %d fails, and %d are skipped;\nCoverage:%.2f%%;\nIncremental coverage:%.2f%%",
			unitTestResp.Data.TestSummary.TotalCase, unitTestResp.Data.TestSummary.PassCase, unitTestResp.Data.TestSummary.FailCase,
			unitTestResp.Data.TestSummary.IgnoreCase, unitTestResp.Data.TestSummary.FullCoverage, unitTestResp.Data.TestSummary.DiffCoverage)
	}
	return
}

func getZKOverview(ctx context.Context, hostID int64) (overView Overview, err error) {
	url := fmt.Sprintf("https://zhongkui.bytedance.net/api/v1/open/mrs/%d/survey", hostID)
	resp, err := utils.DoGet(ctx, url, nil)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	zkResp := make([]ZkResp, 0)
	err = json.Unmarshal(resp, &zkResp)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	var startTime, endTime int64
	var detail string
	for _, item := range zkResp {
		if startTime == 0 || startTime > item.StartTime {
			startTime = item.StartTime
		}
		if endTime < item.EndTime {
			endTime = item.EndTime
		}
		if len(item.Reason) != 0 {
			detail += item.Reason + "\n"
		}
	}
	overView.startTime = startTime
	overView.endTime = endTime
	overView.detail = detail
	return
}
