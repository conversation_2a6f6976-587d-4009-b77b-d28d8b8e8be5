package mysql

type RuleCommonInfoWithDetailId struct {
	RuleDetailId int64 `json:"rule_detail_id" gorm:"rule_detail_id"`
	SecurityGlobalRuleCommon
}

type SecuritySingleEngineRule struct {
	Id      int64 `json:"id"`
	Deleted int8  `json:"deleted"`
	SecurityGlobalRuleCommon
	SecurityGlobalRuleDetail

	// app规则 特有字段
	SyncTime int64 `json:"sync_time"`
}

type SecurityRule struct {
	Id int64 `json:"id"`
	SecurityGlobalRuleCommon
	Details []SecurityGlobalRuleDetail

	// app规则 特有字段
	SyncTime int64 `json:"sync_time"`
}

type SecurityTaskAndCheck struct {
	Id        int64 `json:"id"`
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
	SecurityTask
	SecurityCheck
}

type IssueStatusCount struct {
	C      int64  `json:"c"`
	Status string `json:"status"`
}

type MRInfo struct {
	ProjectId int64  `json:"project_id"`
	MrIId     int64  `json:"mr_iid"`
	UserEmail string `json:"user_email"`
	MrUrl     string `json:"mr_url"`
	MrTitle   string `json:"mr_title"`
	AppId     int64  `json:"app_id"`
	GroupName string `json:"group_name"`
}

type TaskIdAndRuleDetailId struct {
	TaskId       int64 `json:"task_id"`
	RuleDetailId int64 `json:"rule_detail_id"`
}

type CheckIdAndRuleId struct {
	CheckId int64 `json:"check_id"`
	RuleId  int64 `json:"rule_id"`
}

type JobInTask struct {
	Proxy   string `json:"proxy,omitempty"` // job触发时的代理任务名，没有就是自己
	JobName string `json:"job_name"`
	CheckId int64  `json:"check_id,omitempty"`
	// 下面是为了oncall、计算方便的冗余字段（check表里面没有pending状态的status）
	Status string `json:"status,omitempty"` // 这个check导致的rule_id，方便对task在审批方面的计算。task->check->rule_detail_ids->rule_ids
}

type SecurityIssueStatus struct {
	AggregateId int64
	Id          int64
	CheckId     int64
	Status      string
	IssueId     string
	RuleId      int64
	Extra       string
	RawIssueId  string
	Code        string
}

type IssueInfoInRuleApproveInfo struct {
	Id      int64
	IssueId string
}

type AggregateIssueWithReason struct {
	IssueId    string
	Status     string
	ReasonInfo string
}

type CompareIssue struct {
	FileName   string `json:"file_name"`
	LineNumber int64  `json:"line_number"`
	Signature  string `json:"signature"`
	Demangled  string `json:"demangled"`
	FilePath   string `json:"file_path"`
	ModuleName string `json:"module_name"`
	MachoName  string `json:"macho_name"`
	Address    string `json:"address"`
}
