package openapi

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/check/security/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/jobs"
	BytedanceBitsSecurity "code.byted.org/devinfra/hagrid/app/check/security/kitex_gen/bytedance/bits/security"

	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs"
)

func BPEAGetCheckIdByHostId(ctx context.Context, req *BytedanceBitsSecurity.BPEAGetCheckIdByHostIdRequest) (resp *BytedanceBitsSecurity.BPEAGetCheckIdByHostIdResponse, err error) {
	resp = &BytedanceBitsSecurity.BPEAGetCheckIdByHostIdResponse{}
	relation, err := mysql.ReadRepoOperator.ListLatestBPEACheckIdsByHostId(ctx, req.HostId)
	if utils.MysqlNoRows(err) {
		err = nil
		return
	} else if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	if relation.JobName == jobs.AndroidBPEACheck {
		resp.AndroidBpeaCheck = relation.CheckId
	} else if relation.JobName == jobs.IOSBPEACheckV2 ||
		relation.JobName == jobs.IOSDataFlowIDCheck ||
		relation.JobName == jobs.IOSDFIDDiffCheck ||
		relation.JobName == jobs.IOSCookieAPIDiffCheck {
		resp.IosBpeaCheck = relation.CheckId
	}
	return
}
