package atomtrigger

import (
	"code.byted.org/devinfra/hagrid/app/check/security/biz/common"
	"context"
	"errors"
	"fmt"
	json "github.com/bytedance/sonic"
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/libs/bits_err"

	"code.byted.org/devinfra/hagrid/libs/common_lib/model"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bytedance_bits_workflow/kitex_gen/bytedance/bits/workflow"
	OverpassBytedanceBitsWorkFlow "code.byted.org/overpass/bytedance_bits_workflow/rpc/bytedance_bits_workflow"
)

// TriggerParam 触发ussa工具扫描需要的参数
type TriggerParam struct {
	AppCloudId            string   `json:"app_cloud_id"`  // 第一个包
	AppCloudId2           string   `json:"app_cloud_id2"` // 第二个包
	AndroidDiffInfo       string   `json:"diff_info"`     // android对比包
	AndroidDiffInfo2      string   `json:"diff_info2"`    // android对比包 - 第二个包
	ShadowBranch          string   `json:"shadow_branch"`
	ShadowBranchCommitId  string   `json:"shadow_branch_commit_id"`
	Extra                 string   `json:"extra"`
	ThirdSdk              string   `json:"third_sdk"`           // 三方SDK检测
	IpaBinaryUrl          string   `json:"binary_url"`          // ipa检测
	DsymUrl               string   `json:"dsym_url"`            // ipa检测
	LinkMapUrl            string   `json:"linkmap_url"`         // ipa检测
	BasePackages          string   `json:"base_target"`         // ios对比包
	SourcePackages        string   `json:"test_target"`         // ios对比包
	CustomDependencies    string   `json:"custom_dependencies"` // MR 自定义更新的依赖组件
	FeatBuildJobID        string   `json:"feat_build_job_id"`
	BaseBuildJobID        string   `json:"base_build_job_id"`
	SecurityLint          string   `json:"security_lint"`            // 安全检测左移， Lint 检测
	TargetBranch          string   `json:"target_branch"`            // MR 合入目标分支
	AndroidMultiDiffInfos []string `json:"android_multi_diff_infos"` // 安卓多包扫描参数汇总 - 在一个job内触发、聚合扫描结果
	AfterCheck            bool     `json:"after_check"`              // 是否为MR合入后检测
}
type AndroidDiffInfo struct {
	PkgIndex   string            `json:"pkg_index,omitempty"` // 该字段仅用于多包聚合扫描
	AppCloudId string            `json:"app_cloud_id,omitempty"`
	Base       androidPackageUrl `json:"base"`
	Source     androidPackageUrl `json:"review"`
}
type androidPackageUrl struct {
	BinaryUrl     string `json:"binary_url"`
	MappingUrl    string `json:"mapping_url"`
	CakeKnifeUrl  string `json:"cakeknife_url"`
	ResMappingUrl string `json:"resource_mapping_url"`
}
type iOSPackageUrl struct {
	BinaryUrl   string `json:"binary_url"`
	DsymUrl     string `json:"dsym_url"`
	LinkMapUrl  string `json:"linkmap_url"`
	RockfileUrl string `json:"rockfile_url"`
}
type ThirdSDKParam struct {
	WorkflowName string            `json:"workflow_name"` // hardcode: android_sdk_check_workflow / ios_mr_sdk_check_workflow
	EnvDict      map[string]string `json:"context"`       // 所有环境变量
}

// GetJobTriggerParam 获取触发扫描工具所需的全部参数 - 应取尽取
func GetJobTriggerParam(ctx context.Context, infoEnv map[string]string) (string, *bits_err.BitsErr) {
	// 获取分支信息
	appCloudId := infoEnv["APP_CLOUD_ID"]
	appType := infoEnv["WORKFLOW_APP_TYPE"]
	appType = strings.ToLower(appType)
	shadowBranch := infoEnv["WORKFLOW_REPO_BRANCH"]
	shadowBranchCommitId := infoEnv["WORKFLOW_REPO_COMMIT"]
	targetBranch := infoEnv["WORKFLOW_REPO_TARGET_BRANCH"]

	// 三方SDK参数目前取全部环境变量 - 后续可跟工具方沟通，是否有具体的参数
	var workflowName string
	if appType == "android" {
		workflowName = "android_sdk_check_workflow"
	} else if appType == "ios" {
		workflowName = "ios_mr_sdk_check_workflow"
	}
	thirdSDKParam := ThirdSDKParam{
		WorkflowName: workflowName,
		EnvDict:      infoEnv,
	}
	thirdSDKParamStr, _ := json.MarshalString(thirdSDKParam)

	triggerParam := &TriggerParam{
		AppCloudId:           appCloudId,
		ShadowBranch:         shadowBranch,
		ShadowBranchCommitId: shadowBranchCommitId,
		Extra:                "",
		ThirdSdk:             thirdSDKParamStr,
		TargetBranch:         targetBranch,
	}

	// 从依赖的上游jobs获取产物信息
	var artifactTriggerParams *TriggerParam
	var err error
	artifactBuildBinds, ok := infoEnv["multi_build_jobs_bind"]
	if ok && artifactBuildBinds != "" { // 多个构建任务对应的多应用包扫描
		artifactTriggerParams, err = getArtifactInfosFromMultipleBuilds(ctx, infoEnv)
	} else { // 单构建任务对应的单应用包扫描
		artifactTriggerParams, err = getArtifactInfosFromSingleBuild(ctx, infoEnv)
	}
	if artifactTriggerParams != nil {
		// android
		triggerParam.AppCloudId2 = artifactTriggerParams.AppCloudId2
		triggerParam.AndroidDiffInfo = artifactTriggerParams.AndroidDiffInfo
		triggerParam.AndroidDiffInfo2 = artifactTriggerParams.AndroidDiffInfo2
		triggerParam.AndroidMultiDiffInfos = artifactTriggerParams.AndroidMultiDiffInfos
		// ios
		triggerParam.BasePackages = artifactTriggerParams.BasePackages
		triggerParam.SourcePackages = artifactTriggerParams.SourcePackages
		triggerParam.IpaBinaryUrl = artifactTriggerParams.IpaBinaryUrl
		triggerParam.DsymUrl = artifactTriggerParams.DsymUrl
		triggerParam.LinkMapUrl = artifactTriggerParams.LinkMapUrl
		triggerParam.CustomDependencies = artifactTriggerParams.CustomDependencies
		triggerParam.FeatBuildJobID = artifactTriggerParams.FeatBuildJobID
		triggerParam.BaseBuildJobID = artifactTriggerParams.BaseBuildJobID
	}

	// 是否进行 SecurityLint 扫描
	runSecurityLint := false
	if str, ok := infoEnv["run_security_lint"]; ok {
		runSecurityLint, err = strconv.ParseBool(str)
		if err != nil {
			logs.CtxError(ctx, "[GetJobTriggerParam] %s", err.Error())
			return "", bits_err.SECURITYCHECK.ErrTransferData.AddError(err)
		}
	}
	if runSecurityLint {
		securityLintParams, err := getSecurityLintTriggerParams(ctx, infoEnv)
		if err != nil {
			logs.CtxWarn(ctx, "[GetJobTriggerParam] trigger security lint failed: %s", err.Error())
		}
		triggerParam.SecurityLint = securityLintParams
	}

	// 参数汇总，所有job的扫描参数都在这里了
	triggerParamStr, mErr := json.Marshal(triggerParam)
	if mErr != nil {
		logs.CtxError(ctx, "[GetJobTriggerParam] Marshal failed: %s", mErr.Error())
		return "", bits_err.SECURITYCHECK.ErrTransferData.AddError(mErr)
	}
	return string(triggerParamStr), nil
}

type Input struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type BuildParam struct {
	ID     int64   `json:"id"`
	Inputs []Input `json:"inputs"`
}

type Params struct {
	ID          int64        `json:"id"`
	BuildParams []BuildParam `json:"build_params"`
}

func getSecurityLintTriggerParams(ctx context.Context, infoEnv map[string]string) (string, error) {
	pipelineIdStr := infoEnv["WORKFLOW_PIPELINE_ID"]
	pipelineId, err := strconv.ParseInt(pipelineIdStr, 10, 64)
	if err != nil {
		logs.CtxError(ctx, "[GetJobTriggerParam] Convert Pipeline failed: %s", err.Error())
		return "", err
	}
	logs.CtxInfo(ctx, "[GetJobTriggerParam] pipelineId: %s", pipelineIdStr)
	pipelineResp, err := OverpassBytedanceBitsWorkFlow.GetPipeline(ctx, pipelineId)
	if err != nil {
		logs.CtxError(ctx, "[GetJobTriggerParam] Get Pipeline failed: %s", err.Error())
		return "", err
	}
	notInterestedKeys := []string{"CUSTOM_CI_DEV_ID", "CUSTOM_CI_HOST_MR_ID", "CUSTOM_CI_MR_ID"}
	for _, job := range pipelineResp.Pipeline.Jobs {
		if job.ServiceName != nil {
			serviceName := *job.ServiceName
			if serviceName == "code_check" {
				if job.Params != nil {
					var codeCheckJobParams model.CodeCheckJobParams
					err := json.UnmarshalString(*job.Params, &codeCheckJobParams)
					if err != nil {
						logs.CtxError(ctx, "[GetJobTriggerParam] Unmarshal Job Params failed: %s", err.Error())
						return "", err
					}
					var infos []map[string]interface{}
					for _, codeCheckInfo := range codeCheckJobParams.Infos {
						info := map[string]interface{}{
							"mode":                  codeCheckInfo.Mode,
							"git_address":           codeCheckInfo.GitAddress,
							"commit_ids":            codeCheckInfo.CommitIds,
							"target_commit":         codeCheckInfo.TargetCommit,
							"source_commit":         codeCheckInfo.SourceCommit,
							"target_branch":         codeCheckInfo.TargetBranch,
							"source_branch":         codeCheckInfo.SourceBranch,
							"name":                  codeCheckInfo.Name,
							"android_special_param": codeCheckInfo.AndroidSpecialParam,
						}
						infos = append(infos, info)
					}
					commonInfo := map[string]interface{}{
						"app_id":          codeCheckJobParams.CommonInfo.AppId,
						"trigger_type":    "custom",
						"scene_type":      "online",
						"operator":        "liujun.neal",
						"build_config_id": 0,
						"pre_hook":        "",
						"post_hook":       "",
						"create_bug":      false,
						"web_hook_url":    "",
						"extra_params":    codeCheckJobParams.CommonInfo.ExtraParams,
						"daily_build_id":  0,
					}
					jobResult := job.GetJobResult_()
					r, _ := regexp.Compile("jobId=(\\d+)")
					for _, jobIdStr := range r.FindAllString(jobResult, -1) {
						jobIdStr = strings.Replace(jobIdStr, "jobId=", "", -1)
						jobId, err := strconv.ParseInt(jobIdStr, 10, 64)
						if err != nil {
							return "", err
						}
						resp, gErr := OverpassBytedanceBitsWorkFlow.GetJob(ctx, jobId)
						if gErr != nil {
							logs.CtxError(ctx, "[GetJobTriggerParam] GetJob failed: %s", gErr.Error())
							return "", gErr
						}
						if resp == nil || resp.Job == nil {
							logs.CtxError(ctx, "[GetJobTriggerParam] GetJob failed: not find job %v", jobId)
							return "", fmt.Errorf("not find job %v", jobId)
						}
						logs.CtxInfo(ctx, "[GetJobTriggerParam] GetJob resp: %+v", resp)
						job := resp.Job
						if job.Params != nil {
							var cloudBuildParams Params
							err := json.UnmarshalString(job.GetParams(), &cloudBuildParams)
							if err != nil {
								logs.CtxError(ctx, "[GetJobTriggerParam] Unmarshal Job Params failed: %s", err.Error())
								return "", err
							}
							for _, buildParams := range cloudBuildParams.BuildParams {
								for _, input := range buildParams.Inputs {
									if input.Name == "prehook" {
										prehook := input.Value
										if len(prehook) > 0 {
											commonInfo["pre_hook"] = prehook
										}
									}
								}
							}
						}
					}
					envs := map[string]string{}
					for key, value := range pipelineResp.Pipeline.Env {
						if !slices.ContainsString(notInterestedKeys, key) {
							envs[key] = value
						}
					}
					envs["rule_app_id"] = "2020094187"
					triggerParams := map[string]interface{}{
						"pipeline_info": map[string]interface{}{
							"env": envs,
						},
						"job_params": map[string]interface{}{
							"common_info": commonInfo,
							"infos":       infos,
						},
					}
					lintParams := map[string]interface{}{
						"pipeline_id":    pipelineIdStr,
						"job_id":         infoEnv["WORKFLOW_JOB_ID"],
						"trigger_params": triggerParams,
					}
					return json.MarshalString(lintParams)
				}
			}
		}
	}
	logs.CtxError(ctx, "[GetJobTriggerParam] No Job named code_check")
	return "", errors.New("[GetJobTriggerParam] No Job Named code_check")
}

// 获取单构建任务的产物 - 单应用包扫描
func getArtifactInfosFromSingleBuild(ctx context.Context, infoEnv map[string]string) (*TriggerParam, error) {
	// 上游打包jobs
	baseBuildJobName := infoEnv["BASE_BUILD_JOB_NAME"]
	sourceBuildJobName := infoEnv["SOURCE_BUILD_JOB_NAME"]
	// 依赖的 job ids
	jobStr, ok := infoEnv["WORKFLOW_DEPEND_JOB_IDS"]
	if !ok || jobStr == "" {
		return nil, nil
	}
	var dependJobIds []int64
	err := json.UnmarshalString(jobStr, &dependJobIds)
	if err != nil {
		logs.CtxError(ctx, "[getArtifactInfosFromSingleBuild] %s", err.Error())
		return nil, err
	}
	// job更新可能存在主从延迟，预计是1.5s左右，保险起见查询job信息时 sleep 3s
	time.Sleep(3 * time.Second)

	// 逐个取出包信息
	triggerParam := &TriggerParam{}
	appType := strings.ToLower(infoEnv["WORKFLOW_APP_TYPE"])
	var androidBaseDiffInfos []androidPackageUrl
	var androidSourceDiffInfos []androidPackageUrl
	for _, jogId := range dependJobIds {
		resp, gErr := OverpassBytedanceBitsWorkFlow.GetJob(ctx, jogId)
		if gErr != nil {
			logs.CtxError(ctx, "[getArtifactInfosFromSingleBuild] GetJob failed: %s", gErr.Error())
			return nil, gErr
		}
		if resp == nil || resp.Job == nil {
			return nil, errors.New(fmt.Sprintf("not find job %v", jogId))
		}
		job := resp.Job

		switch appType {
		case "android":
			switch job.Name {
			case baseBuildJobName: // base包
				triggerParam.BaseBuildJobID = strconv.Itoa(int(job.GetJobId()))
				androidBaseDiffInfos, err = getAndroidArtifacts(ctx, infoEnv, job.GetArtifacts())
				if err != nil {
					logs.CtxError(ctx, "[getArtifactInfosFromSingleBuild] getAndroidArtifacts for base_build: %v", err.Error())
					return nil, err
				}
			case sourceBuildJobName: // source包
				triggerParam.FeatBuildJobID = strconv.Itoa(int(job.GetJobId()))
				androidSourceDiffInfos, err = getAndroidArtifacts(ctx, infoEnv, job.GetArtifacts())
				if err != nil {
					logs.CtxError(ctx, "[getArtifactInfosFromSingleBuild] getAndroidArtifacts for source_build: %v", err.Error())
					return nil, err
				}
			}
		case "ios":
			switch job.Name {
			case baseBuildJobName: // base包
				basePackageStr, _ := json.MarshalString(getIOSArtifacts(job.GetArtifacts()))
				triggerParam.BasePackages = basePackageStr
				triggerParam.BaseBuildJobID = strconv.Itoa(int(job.GetJobId()))
			case sourceBuildJobName: // source包
				packageUrl := getIOSArtifacts(job.GetArtifacts())
				sourcePackageStr, _ := json.MarshalString(packageUrl)
				triggerParam.SourcePackages = sourcePackageStr
				triggerParam.IpaBinaryUrl = packageUrl.BinaryUrl
				triggerParam.DsymUrl = packageUrl.DsymUrl
				triggerParam.LinkMapUrl = packageUrl.LinkMapUrl
				triggerParam.CustomDependencies = infoEnv["CUSTOM_CI_MR_DEPENDENCIES"]
				triggerParam.FeatBuildJobID = strconv.Itoa(int(job.GetJobId()))
			}
		}
	}

	// 安卓端需要聚合数据 - 兼容历史数据
	if appType == "android" {
		androidMultiDiffInfos := make([]string, 0)
		for index := range androidSourceDiffInfos {
			androidDiffInfo := AndroidDiffInfo{
				PkgIndex:   strconv.Itoa(index),
				AppCloudId: infoEnv["APP_CLOUD_ID"],
				Source:     androidSourceDiffInfos[index],
			}
			if index < len(androidBaseDiffInfos) {
				androidDiffInfo.Base = androidBaseDiffInfos[index]
			}
			androidDiffInfoStr, _ := json.MarshalString(androidDiffInfo)
			androidMultiDiffInfos = append(androidMultiDiffInfos, androidDiffInfoStr)
		}
		triggerParam.AndroidMultiDiffInfos = androidMultiDiffInfos
		if len(androidMultiDiffInfos) > 0 {
			triggerParam.AndroidDiffInfo = androidMultiDiffInfos[0]
		}
	}

	return triggerParam, nil
}

// 获取多构建任务的产物 - 同仓多应用包扫描，目前仅支持安卓端
func getArtifactInfosFromMultipleBuilds(ctx context.Context, infoEnv map[string]string) (*TriggerParam, error) {
	// 上游打包jobs
	artifactBuildBindStr, ok := infoEnv["multi_build_jobs_bind"]
	if !ok || artifactBuildBindStr == "" {
		return nil, nil
	}
	type bindInfo struct {
		AppId              string `json:"app_cloud_id"`
		BaseBuildJobName   string `json:"base_build_job_name"`
		SourceBuildJobName string `json:"source_build_job_name"`
	}
	var artifactBuildBinds []bindInfo
	err := json.UnmarshalString(artifactBuildBindStr, &artifactBuildBinds)
	if err != nil {
		logs.CtxError(ctx, "[getMultipleArtifactInfos] fjson.UnmarshalString failed: %v", err)
		return nil, err
	}

	// 依赖的 job ids
	jobStr, ok := infoEnv["WORKFLOW_DEPEND_JOB_IDS"]
	if !ok || jobStr == "" {
		return nil, nil
	}
	var dependJobIds []int64
	err = json.UnmarshalString(jobStr, &dependJobIds)
	if err != nil {
		logs.CtxError(ctx, "[getMultipleArtifactInfos] %s", err.Error())
		return nil, err
	}
	// job更新可能存在主从延迟，预计是1.5s左右，保险起见查询job信息时 sleep 3s
	time.Sleep(3 * time.Second)

	// 逐个取出包信息
	triggerParam := &TriggerParam{}
	appType := strings.ToLower(infoEnv["WORKFLOW_APP_TYPE"])
	for bindIndex, buildBind := range artifactBuildBinds {
		var androidBaseDiffInfos []androidPackageUrl
		var androidSourceDiffInfos []androidPackageUrl
		for _, jogId := range dependJobIds {
			resp, gErr := OverpassBytedanceBitsWorkFlow.GetJob(ctx, jogId)
			if gErr != nil {
				logs.CtxError(ctx, "[getMultipleArtifactInfos] GetJob failed: %s", gErr.Error())
				return nil, gErr
			}
			if resp == nil || resp.Job == nil {
				return nil, errors.New(fmt.Sprintf("not find job %v", jogId))
			}
			job := resp.Job

			switch appType {
			case "android":
				switch job.Name {
				case buildBind.BaseBuildJobName: // base包
					triggerParam.BaseBuildJobID = strconv.Itoa(int(job.GetJobId()))
					androidBaseDiffInfos, err = getAndroidArtifacts(ctx, infoEnv, job.GetArtifacts())
					if err != nil {
						logs.CtxError(ctx, "[getArtifactInfosFromSingleBuild] getAndroidArtifacts for base_build: %v", err.Error())
						return nil, err
					}
				case buildBind.SourceBuildJobName: // source包
					triggerParam.FeatBuildJobID = strconv.Itoa(int(job.GetJobId()))
					androidSourceDiffInfos, err = getAndroidArtifacts(ctx, infoEnv, job.GetArtifacts())
					if err != nil {
						logs.CtxError(ctx, "[getArtifactInfosFromSingleBuild] getAndroidArtifacts for source_build: %v", err.Error())
						return nil, err
					}
				}
			case "ios":
				switch job.Name {
				case buildBind.BaseBuildJobName: // base包
					basePackageStr, _ := json.MarshalString(getIOSArtifacts(job.GetArtifacts()))
					triggerParam.BasePackages = basePackageStr
					triggerParam.BaseBuildJobID = strconv.Itoa(int(job.GetJobId()))
				case buildBind.SourceBuildJobName: // source包
					packageUrl := getIOSArtifacts(job.GetArtifacts())
					sourcePackageStr, _ := json.MarshalString(packageUrl)
					triggerParam.SourcePackages = sourcePackageStr
					triggerParam.IpaBinaryUrl = packageUrl.BinaryUrl
					triggerParam.DsymUrl = packageUrl.DsymUrl
					triggerParam.LinkMapUrl = packageUrl.LinkMapUrl
					triggerParam.CustomDependencies = infoEnv["CUSTOM_CI_MR_DEPENDENCIES"]
					triggerParam.FeatBuildJobID = strconv.Itoa(int(job.GetJobId()))
				}
			}
		}
		// 安卓端需要聚合数据 - 兼容历史数据
		if appType == "android" {
			androidMultiDiffInfos := make([]string, 0)
			for index := range androidSourceDiffInfos {
				androidDiffInfo := AndroidDiffInfo{
					PkgIndex:   strconv.Itoa(index + len(triggerParam.AndroidMultiDiffInfos)),
					AppCloudId: buildBind.AppId,
					Source:     androidSourceDiffInfos[index],
				}
				if index < len(androidBaseDiffInfos) {
					androidDiffInfo.Base = androidBaseDiffInfos[index]
				}
				androidDiffInfoStr, _ := json.MarshalString(androidDiffInfo)
				androidMultiDiffInfos = append(androidMultiDiffInfos, androidDiffInfoStr)
			}
			triggerParam.AndroidMultiDiffInfos = append(triggerParam.AndroidMultiDiffInfos, androidMultiDiffInfos...)
			if len(androidMultiDiffInfos) > 0 {
				// 默认第一个、第二个包绑定分别为主端包和lite包，其他包绑定就不在单包扫描job里处理了，建议使用上述的多包聚合扫描
				switch bindIndex {
				case 0:
					triggerParam.AppCloudId = buildBind.AppId
					triggerParam.AndroidDiffInfo = androidMultiDiffInfos[0]
				case 1:
					triggerParam.AppCloudId2 = buildBind.AppId
					triggerParam.AndroidDiffInfo2 = androidMultiDiffInfos[0]
				}
			}
		}
	}

	return triggerParam, nil
}

func getIOSArtifacts(artifacts []*workflow.JobArtifacts) iOSPackageUrl {
	packageUrl := iOSPackageUrl{}
	for _, artifact := range artifacts {
		switch artifact.Type {
		case "ipa", "size_ipa":
			packageUrl.BinaryUrl = artifact.Url
		case "dsyms", "dsym":
			packageUrl.DsymUrl = artifact.Url
		case "linkmaps_zip", "LinkMapZip":
			packageUrl.LinkMapUrl = artifact.Url
		case "rockfile_lock":
			packageUrl.RockfileUrl = artifact.Url
		}
	}
	return packageUrl
}

func getAndroidArtifacts(ctx context.Context, infoEnv map[string]string, artifacts []*workflow.JobArtifacts) ([]androidPackageUrl, error) {
	var (
		artifactInfos []*ArtifactInfo
		err           error
	)

	// 获取产物列表
	if bindStr, ok := infoEnv["multi_build_artifacts_bind"]; ok { // 多apks
		artifactInfos, err = getAndroidArtifactUrlsForMultiApks(ctx, artifacts, bindStr)
		if err != nil {
			logs.CtxError(ctx, "[getAndroidArtifacts] getAndroidArtifactUrlsForMultiApks: %v", err.Error())
			return nil, err
		}
	} else { // 单apk
		artifactInfo := getAndroidArtifactUrlsForSingleApk(ctx, infoEnv, artifacts)
		artifactInfos = []*ArtifactInfo{artifactInfo}
	}

	packages := make([]androidPackageUrl, len(artifactInfos))
	for index, artifactInfo := range artifactInfos {
		binaryUrl := artifactInfo.Aab
		if binaryUrl == "" {
			binaryUrl = artifactInfo.Apk
		}
		mappingUrl := artifactInfo.MappingWithLineNumber
		if mappingUrl == "" {
			mappingUrl = artifactInfo.Mapping
		}
		packages[index] = androidPackageUrl{
			BinaryUrl:     binaryUrl,
			MappingUrl:    mappingUrl,
			CakeKnifeUrl:  artifactInfo.CakeKnife,
			ResMappingUrl: artifactInfo.ResMapping,
		}
	}

	return packages, nil
}

type ArtifactInfo struct {
	Aab                   string
	Apk                   string
	Mapping               string
	MappingWithLineNumber string
	ResMapping            string
	CakeKnife             string
	Dependencies          string
}

// 获取android端同构建任务的单包产物
func getAndroidArtifactUrlsForSingleApk(ctx context.Context, infoEnv map[string]string, artifacts []*workflow.JobArtifacts) *ArtifactInfo {
	artifactInfo := &ArtifactInfo{}
	for _, artifact := range artifacts {
		if artifact.Name == "jenkins_build_result.json" {
			resp, err := utils.DoGet(ctx, artifact.Url, nil)
			if err != nil {
				logs.CtxError(ctx, "[getArtifactUrls] utils.DoGet failed, url: %v, err: %v", artifact.Url, err)
			} else {
				param := make(map[string]interface{})
				err = json.Unmarshal(resp, &param)
				if err != nil {
					logs.CtxError(ctx, "[getArtifactUrls] json..Unmarshal failed, resp: %+v, err: %v", resp, err)
				} else {
					artifactInfo = getArtifactByParseMap(param, artifactInfo)
				}
			}
		}

		// 按照打包类型和打包名称匹配 - 标准是只使用打包类型判断，至于打包名称最初由鲜时光提出来的，但其实这个是否普适待判定
		subName, ok := infoEnv[fmt.Sprintf("%s_artifact_name_match", artifact.Type)]
		switch artifact.Type {
		case "aab":
			if ok && subName != common.EmptyString { // 只要配置了，就必须匹配上打包名称，否则就取不到
				if strings.Contains(artifact.Name, subName) {
					artifactInfo.Aab = artifact.Url
				}
			} else {
				artifactInfo.Aab = artifact.Url
			}
		case "apk", "package_url":
			if ok && subName != common.EmptyString { // 只要配置了，就必须匹配上打包名称，否则就取不到
				if strings.Contains(artifact.Name, subName) {
					artifactInfo.Apk = artifact.Url
				}
			} else {
				artifactInfo.Apk = artifact.Url
			}
		case "mapping", "mapping_url":
			if ok && subName != common.EmptyString { // 只要配置了，就必须匹配上打包名称，否则就取不到
				if strings.Contains(artifact.Name, subName) {
					artifactInfo.Mapping = artifact.Url
				}
			} else {
				artifactInfo.Mapping = artifact.Url
			}
		case "mapping_with_line_number":
			if ok && subName != common.EmptyString { // 只要配置了，就必须匹配上打包名称，否则就取不到
				if strings.Contains(artifact.Name, subName) {
					artifactInfo.MappingWithLineNumber = artifact.Url
				}
			} else {
				artifactInfo.MappingWithLineNumber = artifact.Url
			}
		case "resmapping", "resguard_mapping_url", "res_mapping":
			if ok && subName != common.EmptyString { // 只要配置了，就必须匹配上打包名称，否则就取不到
				if strings.Contains(artifact.Name, subName) {
					artifactInfo.ResMapping = artifact.Url
				}
			} else {
				artifactInfo.ResMapping = artifact.Url
			}
		case "cakeknife_txt", "cake_knife":
			if ok && subName != common.EmptyString { // 只要配置了，就必须匹配上打包名称，否则就取不到
				if strings.Contains(artifact.Name, subName) {
					artifactInfo.CakeKnife = artifact.Url
				}
			} else {
				artifactInfo.CakeKnife = artifact.Url
			}
		}
	}
	return artifactInfo
}

// 获取android端同构建任务的多包产物，例如宿主包/插件包
func getAndroidArtifactUrlsForMultiApks(ctx context.Context, artifacts []*workflow.JobArtifacts, bindStr string) ([]*ArtifactInfo, error) {
	// 解析bind
	var artifactBinds []map[string]string
	err := json.UnmarshalString(bindStr, &artifactBinds)
	if err != nil {
		logs.CtxError(ctx, "[getAndroidArtifactUrlsForMultiApks] fjson.UnmarshalString failed: %v", err)
		return nil, err
	}

	// 依次找出所有包以及附属产物
	artifactInfos := make([]*ArtifactInfo, len(artifactBinds))
	for index, bind := range artifactBinds {
		artifactInfo := &ArtifactInfo{}
		for _, artifact := range artifacts {
			// 按照打包类型和打包名称匹配
			if subName, ok := bind[artifact.Type]; !ok || !strings.Contains(artifact.Name, subName) {
				continue
			}
			switch artifact.Type {
			case "aab":
				artifactInfo.Aab = artifact.Url
			case "apk", "package_url":
				artifactInfo.Apk = artifact.Url
			case "mapping", "mapping_url":
				artifactInfo.Mapping = artifact.Url
			case "mapping_with_line_number":
				artifactInfo.MappingWithLineNumber = artifact.Url
			case "resmapping", "resguard_mapping_url", "res_mapping":
				artifactInfo.ResMapping = artifact.Url
			case "cakeknife_txt", "cake_knife":
				artifactInfo.CakeKnife = artifact.Url
			}
		}
		artifactInfos[index] = artifactInfo
	}

	return artifactInfos, nil
}

// 从jenkins json文件获取产物信息 - 递归解析map
func getArtifactByParseMap(aMap map[string]interface{}, artifactInfo *ArtifactInfo) *ArtifactInfo {
	for key, val := range aMap {
		switch val.(type) {
		case string:
			url := val.(string)
			// 匹配到产物标识，或者匹配到产物后缀
			var find bool
			artifactInfo, find = matchUrlKey(key, url, artifactInfo)
			if !find {
				artifactInfo = matchUrlSuffix(url, artifactInfo)
			}
		case map[string]interface{}:
			artifactInfo = getArtifactByParseMap(val.(map[string]interface{}), artifactInfo)
		case []interface{}:
			artifactInfo = getArtifactByParseArray(val.([]interface{}), artifactInfo)
		default:
			continue
		}
	}
	return artifactInfo
}

// 从jenkins json文件获取产物信息 - 递归解析array
func getArtifactByParseArray(anArray []interface{}, artifactInfo *ArtifactInfo) *ArtifactInfo {
	for _, val := range anArray {
		switch val.(type) {
		case string:
			url := val.(string)
			// 只能匹配到后缀
			artifactInfo = matchUrlSuffix(url, artifactInfo)
		case map[string]interface{}:
			return getArtifactByParseMap(val.(map[string]interface{}), artifactInfo)
		case []interface{}:
			return getArtifactByParseArray(val.([]interface{}), artifactInfo)
		default:
			continue
		}
	}
	return artifactInfo
}

// 记录<key,url>是否为所需产物
func matchUrlKey(key string, url string, artifactInfo *ArtifactInfo) (*ArtifactInfo, bool) {
	find := false
	if key == "raw-aab" || key == "aab" {
		artifactInfo.Aab = url
		find = true
	}
	if key == "package_url" {
		artifactInfo.Apk = url
		find = true
	}
	if key == "mapping_url" {
		artifactInfo.Mapping = url
		find = true
	}
	if key == "mapping_dsln_url" {
		artifactInfo.MappingWithLineNumber = url
		find = true
	}
	if key == "cakeKnife_txt" {
		artifactInfo.CakeKnife = url
		find = true
	}
	if key == "resource_mapping_txt" {
		artifactInfo.ResMapping = url
		find = true
	}
	if key == "dependencies_url" {
		artifactInfo.Dependencies = url
		find = true
	}
	return artifactInfo, find
}

// url是否为所需产物链接
func matchUrlSuffix(url string, artifactInfo *ArtifactInfo) *ArtifactInfo {
	if strings.HasSuffix(url, ".aab") {
		if len(artifactInfo.Aab) == 0 {
			artifactInfo.Aab = url
		}
	}
	if strings.HasSuffix(url, ".apk") {
		if len(artifactInfo.Apk) == 0 {
			artifactInfo.Apk = url
		}
	}
	if strings.HasSuffix(url, "/mapping.txt") {
		if len(artifactInfo.Mapping) == 0 {
			artifactInfo.Mapping = url
		}
	}
	if strings.HasSuffix(url, "/MappingWithLineNumber.txt") {
		if len(artifactInfo.MappingWithLineNumber) == 0 {
			artifactInfo.MappingWithLineNumber = url
		}
	}
	if strings.HasSuffix(url, "/cakeKnife.txt") {
		if len(artifactInfo.CakeKnife) == 0 {
			artifactInfo.CakeKnife = url
		}
	}
	if strings.HasSuffix(url, "/resource_mapping.txt") {
		if len(artifactInfo.ResMapping) == 0 {
			artifactInfo.ResMapping = url
		}
	}
	if strings.HasSuffix(url, "/dependencies_including_abb.json") || strings.HasSuffix(url, "/dependencies.json") {
		if len(artifactInfo.Dependencies) == 0 {
			artifactInfo.Dependencies = url
		}
	}
	return artifactInfo
}
