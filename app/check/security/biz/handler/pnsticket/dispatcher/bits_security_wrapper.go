package dispatcher

import (
	"code.byted.org/devinfra/hagrid/app/check/security/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/handler/pnsticket/fsm"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/handler/pnsticket/model"
	sdlcMetrics "code.byted.org/devinfra/hagrid/app/check/security/biz/sdlc_metrics"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/service/tcc"
	BytedanceBitsSecurity "code.byted.org/devinfra/hagrid/app/check/security/kitex_gen/bytedance/bits/security"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs"
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"reflect"
	"strings"
	"time"
)

type BitsPlatformWrapper struct{}

func (*BitsPlatformWrapper) Type() model.TicketItemPlatformType {
	return model.TicketItemPlatformBits
}

func (*BitsPlatformWrapper) ListCiTicketItemByTicket(ctx context.Context, ticket mysql.SecurityCiTicket, withDetail bool) ([]mysql.SecurityCiTicketItem, error) {
	return mysql.WriteRepoOperator.ListCiTicketItemByTicketId(ctx, ticket.Id)
}

func (*BitsPlatformWrapper) ListCiTicketItemByCheckIds(ctx context.Context, appID int64, checkIds []int64, jobName string, withDetail bool) (items []mysql.SecurityCiTicketItem, err error) {
	items, err = mysql.ReadRepoOperator.ListCiTicketItemByCheckIds(ctx, checkIds)
	if utils.MysqlNoRows(err) {
		return items, nil
	}
	if err != nil {
		logs.CtxError(ctx, "[GetTicketItemByCheckIdsMultiWay] ListCiTicketItemByCheckIds failed: %v, check_ids: %+v", err.Error(), checkIds)
		return
	}
	return items, nil
}

func (*BitsPlatformWrapper) ListCiTicketItemStatusByTicket(ctx context.Context, writeCon mysql.WriteCRUDRepo, ticket mysql.SecurityCiTicket, withDetail bool) ([]string, error) {
	return writeCon.ListCiTicketItemStatusByTicketId(ctx, ticket.Id)
}

func (*BitsPlatformWrapper) CreateCiTicketItemsFromReq(ctx context.Context, tx mysql.WriteCRUDRepo, req *BytedanceBitsSecurity.CiTicketCreateReq,
	ticket *mysql.SecurityCiTicket, check *mysql.SecurityCheck) error {

	// get new/update items in the ticket
	newItems, updateItems, err := compareItemsForTicket(ctx, tx, ticket.Id, req.Data)
	if err != nil {
		logs.CtxError(ctx, "[CreateCiTicket] compareItemsForTicket failed: %v", err.Error())
		return err
	}

	// update status of exist items
	statusMap := map[string][]int64{}
	forecastVersion := ""
	pureJobName := strings.Split(req.JobName, " ")[0] // 因为存在相同job下的多个checks，check和ticket粒度的jobName是带了索引的，但是有些地方需要job关联的配置，故去掉索引
	jobUsingForecastVersion := GetJobUsingForecastVersion(ctx)
	if jobUsingForecastVersion[pureJobName] {
		forecastVersion, err = tx.GetForecastVersion(ctx, ticket.HostId)
		if err != nil {
			err = fmt.Errorf("transaction rollback due to err when finding forecast version of host %v, %v", ticket.HostId, err)
			logs.CtxError(ctx, "[CreateCiTicket] %v", err.Error())
			return err
		}
	}
	for idx := 0; idx < len(updateItems); idx++ {
		// here filter items with label, skip creating filtered item
		if redis.IsApprovedItem(ctx, pureJobName, updateItems[idx].ItemLabel, forecastVersion) {
			continue
		}
		statusMap[updateItems[idx].Status] = append(statusMap[updateItems[idx].Status], updateItems[idx].Id)
		sdlcMetrics.MetricsItem(check, &updateItems[idx], sdlcMetrics.EVENT_ITEM_REPORT, "")
	}
	for status, itemIds := range statusMap {
		_, err = tx.BatchUpdateCiTicketItemStatus(ctx, status, time.Now().Unix(), itemIds)
		if err != nil {
			logs.CtxError(ctx, "[CreateCiTicket] transaction rollback when update batch(status: %v, ids: %v) of items: %v", status, itemIds, err)
			return err
		}
	}

	// insert new items
	filteredNewItems := []mysql.SecurityCiTicketItem{}
	for idx := 0; idx < len(newItems); idx++ {
		if redis.IsApprovedItem(ctx, pureJobName, newItems[idx].ItemLabel, forecastVersion) {
			continue
		}
		filteredNewItems = append(filteredNewItems, newItems[idx])
		sdlcMetrics.MetricsItem(check, &newItems[idx], sdlcMetrics.EVENT_ITEM_REPORT, "")
	}
	if len(filteredNewItems) > 0 {
		_, err = tx.BatchInsertCiTicketItem(ctx, filteredNewItems)
		logs.CtxInfo(ctx, "[CreateCiTicket] new items: %v", filteredNewItems)
		if err != nil {
			logs.CtxError(ctx, "[CreateCiTicket] transaction rollback when batch inserting items: %v, items are %v", err, filteredNewItems)
			return err
		}
	}
	return nil
}

func GetJobUsingForecastVersion(ctx context.Context) map[string]bool {
	jobUsingForecastVersion := map[string]bool{}
	usingList, err := tcc.GetStringSliceByTCCKey(ctx, "pns_filter_forecast_version")
	if err != nil {
		logs.CtxError(ctx, "[getJobUsingForecastVersion] failed to get tcc pns_filter_forecast_version, %v", err)
		return nil
	}
	logs.Info("[getJobUsingForecastVersion] tcc pns_filter_forecast_Version: %v", usingList)
	for _, x := range usingList {
		jobUsingForecastVersion[x] = true
	}
	return jobUsingForecastVersion
}

func compareItemsForTicket(ctx context.Context, tx mysql.WriteCRUDRepo, ticketId int64, reqItems []*BytedanceBitsSecurity.CiTicketCreateReqItem) (newItems, updateItems []mysql.SecurityCiTicketItem, err error) {
	// existed items of this ticket in db
	dbItems, dErr := tx.ListCiTicketItemByTicketId(ctx, ticketId)
	if dErr != nil && !utils.MysqlNoRows(dErr) {
		err = fmt.Errorf("transaction rollback when list existing newItems of ticket_id %v: %v", ticketId, dErr)
		logs.CtxError(ctx, "[compareItemsForTicket] %v", err.Error())
		return
	}

	// find new and update items for this req
	remainItemM := map[int64]bool{} // req和db的交集部分
	for _, data := range reqItems {
		found := false
		for _, dbItem := range dbItems {
			// 交集：判断是否更新，即由修复状态重新进入报备审批
			if compareJsonStr(data.UniqFields, dbItem.UniqFields) {
				found = true
				remainItemM[dbItem.Id] = true
				// 处理mutual的非终态item
				if fsm.GetItemFSMInstance().ReachEnd(dbItem.Status) {
					break
				}
				toStat, e1 := fsm.GetItemFSMInstance().Exec(dbItem.Status, model.ItemFSMActionReappear)
				if e1 != nil {
					err = fmt.Errorf("transaction rollback due to wrong fsm exec for mutual item: %v", e1)
					logs.Error("[compareItemsForTicket] %v", err.Error())
					return
				}
				// example, repaired(db) => running(req)
				if toStat != dbItem.Status { // 省略对自环情况的处理
					dbItem.Status = toStat
					updateItems = append(updateItems, dbItem)
				}
				break
			}
		}
		// req差集：属于纯新增问题
		if !found {
			newItems = append(newItems, mysql.SecurityCiTicketItem{
				TicketId:          ticketId,
				CreateTime:        time.Now().Unix(),
				UpdateTime:        time.Now().Unix(),
				ItemLabel:         data.ItemLabel,
				Status:            model.ItemFSMStateRunning,
				Approver:          "",
				Category:          data.Category,
				CategoryFields:    data.CategoryFields,
				CategoryDevsreKey: data.CategoryDevsreKey,
				UniqFields:        data.UniqFields,
				InfoFields:        data.InfoFields,
				InfoDevsreKey:     data.InfoDevsreKey,
				FormFields:        data.FormFields,
				FormDevsreKey:     data.FormDevsreKey,
				AuditFields:       data.AuditFields,
				AuditDevsreKey:    data.AuditDevsreKey,
				Remark:            "",
				IssueId:           data.IssueId,
			})
		}
	}

	// db差集：进入修复状态
	for _, item := range dbItems {
		if !remainItemM[item.Id] {
			if fsm.GetItemFSMInstance().ReachEnd(item.Status) {
				continue
			}
			toState, e := fsm.GetItemFSMInstance().Exec(item.Status, model.ItemFSMActionRepair)
			if e != nil || toState == "" {
				err = fmt.Errorf("transaction rollback due to wrong fsm exec: %v", e)
				logs.Error("[compareItemsForTicket] %v", err.Error())
				return
			}
			item.Status = toState
			updateItems = append(updateItems, item)
		}
	}

	return
}

func compareJsonStr(s1, s2 string) bool {
	var v1, v2 interface{}
	err := json.Unmarshal([]byte(s1), &v1)
	if err != nil {
		return false
	}
	err = json.Unmarshal([]byte(s2), &v2)
	if err != nil {
		return false
	}
	if reflect.DeepEqual(v1, v2) {
		return true
	}
	return false
}
