package metric

import (
	"code.byted.org/devinfra/hagrid/app/check/security/biz/service/tcc"
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/app/check/security/biz/common"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/dal/mysql"
	eventbusService "code.byted.org/devinfra/hagrid/app/check/security/biz/service/eventbus"
	"code.byted.org/devinfra/hagrid/app/check/security/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/check/security/kitex_gen/bits/devops/metrics_data_collect"
	"code.byted.org/devinfra/hagrid/app/check/security/kitex_gen/bytedance/bits/feature"
	BytedanceBitsSecurity "code.byted.org/devinfra/hagrid/app/check/security/kitex_gen/bytedance/bits/security"

	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	eventbus "code.byted.org/eventbus/client-go"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
)

type ApproveMetric struct {
	EventName       string   `json:"event_name"`
	AppId           int64    `json:"appid"`
	JobType         string   `json:"job_type"`
	CheckId         int64    `json:"check_id"`
	MrId            int64    `json:"mr_id"`
	Result          string   `json:"result"`         //命中的结果
	Module          string   `json:"module"`         //模块
	IssueHash       string   `json:"issue_hash"`     //issue_id
	IsMissReport    bool     `json:"is_miss_report"` //是否误报？
	File            string   `json:"file"`           //命中的文件
	LineNumber      int64    `json:"line_number"`    //命中的行号
	Status          string   `json:"status"`
	MeegoIds        []string `json:"meego_ids"` //所有的meegoId
	RdReason        string   `json:"rd_reason"`
	RdOwner         string   `json:"rd_owner"`
	OperationOwner  string   `json:"operation_owner"`
	OperationReason string   `json:"operation_reason"`
	RuleId          int64    `json:"rule_id"`   //命中的规则
	OrderUrl        string   `json:"order_url"` //报告页地址
	InstId          string   `json:"instId"`    //审批流Id
	CreateTime      int64    `json:"create_time"`
	EndTime         int64    `json:"end_time"`
	Duration        int64    `json:"duration"`
	ProductVersion  string   `json:"product_version"`
}

func ForApprove(ctx context.Context, req *BytedanceBitsSecurity.ApproveV2ApproveRequest) {
	var approveInfo ApproveMetric
	approveInfo.MrId = req.HostId
	if req.Reason == common.NeedApprovePick {
		approveInfo.Status = common.IssueOffline
	}
	approveInfo.InstId = req.ApproveInstanceId
	mrInfo, err := rpc.GetMainMrInfoByMrId(ctx, req.HostId)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	} else if mrInfo == nil || mrInfo.Info == nil {
		return
	}
	approveInfo.ProductVersion = mrInfo.Info.ProductVersion
	featureBindRecord, err := rpc.FeatureClient.GetFeatureBindRecord(ctx, &feature.GetFeatureBindRecordQuery{
		MrID: req.HostId,
	})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	for _, record := range featureBindRecord.List {
		approveInfo.MeegoIds = append(approveInfo.MeegoIds, record.TaskID)
	}

	approveReason, err := tcc.GetApproveReasonConfig(ctx)
	if err != nil {
		logs.CtxError(ctx, "[ForApprove] tcc.GetApproveReasonConfig failed: %v", err)
		return
	}

	issues, err := mysql.ReadRepoOperator.ListIssueApproveInfosByHostId(ctx, req.HostId)
	for _, issue := range issues {
		//审批信息
		issueApprove := make([]*BytedanceBitsSecurity.IssueApproveInfoReasonElement, 0)
		err = json.Unmarshal([]byte(issue.ReasonInfo), &issueApprove)
		if err != nil {
			logs.CtxError(ctx, err.Error())
			return
		}
		approveInfo.EventName = strconv.FormatInt(issue.AggregateIssueId, 10)
		if len(issueApprove) >= 2 {
			approveInfo.CreateTime = issueApprove[0].Timestamp
			approveInfo.EndTime = issueApprove[1].Timestamp
			approveInfo.Duration = issueApprove[1].Timestamp - issueApprove[0].Timestamp
			approveInfo.RdReason = issueApprove[0].Reason
			approveInfo.RdOwner = issueApprove[0].UserName
			approveInfo.OperationOwner = issueApprove[1].UserName
			approveInfo.OperationReason = issueApprove[1].Reason
			approveInfo.IsMissReport = approveInfo.RdReason == approveReason["miss_report"] && approveInfo.OperationReason == approveReason["true_matter_operation"]
		}

		//问题信息
		issueInfo, err := mysql.ReadRepoOperator.GetAggregateIssueById(ctx, issue.AggregateIssueId)
		if err != nil {
			logs.CtxError(ctx, err.Error())
			return
		}
		approveInfo.IssueHash = issueInfo.IssueId
		approveInfo.RuleId = issueInfo.RuleId
		approveInfo.LineNumber = issueInfo.CodeStartLine
		approveInfo.File = issueInfo.FilePath
		approveInfo.Module = issueInfo.ClassName
		approveInfo.AppId = issueInfo.AppId
		approveInfo.JobType = issueInfo.JobName
		approveInfo.Result = issueInfo.MatchResult
		approveInfo.OrderUrl = common.GetOneSiteSecurityCheckReportUrl(ctx, issueInfo.AppId, issueInfo.HostId)

		securityIssues, err := mysql.ReadRepoOperator.ListIssueByIssueIdsAndHostIds(ctx, []int64{issueInfo.HostId}, []string{issueInfo.IssueId})
		if len(securityIssues) == 1 {
			approveInfo.CheckId = securityIssues[0].CheckId
		}
		logs.CtxInfo(ctx, "approveInfo", approveInfo)
		value, _ := json.Marshal(approveInfo)
		msg := eventbus.NewProducerEventBuilder().
			WithKey([]byte(approveInfo.EventName)).
			WithValue(value).
			Build()

		if err := eventbusService.ProducerSecurityApprove.Send(ctx, msg); err != nil {
			logs.Warnf("issue approve event sync error: %#v", err)
			return
		}
	}
}

type JobDone struct {
	EventName  string `json:"event_name"`
	AppId      int64  `json:"appid"`
	Status     string `json:"status"`
	JobType    string `json:"job_type"`
	CheckId    int64  `json:"check_id"`
	CreateTime int64  `json:"create_time"`
	EndTime    int64  `json:"end_time"`
	Duration   int64  `json:"duration"`

	Url       string `json:"url"` //回调返回的检测url
	Message   string `json:"message"`
	MrIid     int64  `json:"mr_iid"`
	ProjectId int64  `json:"project_id"`
	CommitId  string `json:"commit_id"`
	HostId    int64  `json:"host_id"`
}

func ForJobDone(ctx context.Context, check mysql.SecurityCheck, hostId int64) {
	// 老系统打点
	metricPayload := map[string]string{
		"mr_id":      strconv.Itoa(int(check.MrId)),
		"duration":   strconv.Itoa(int(check.ReportTime - check.StartTime)),
		"bits_appid": strconv.Itoa(int(check.AppId)),
		"job_name":   check.JobName,
		"check_id":   strconv.Itoa(int(check.Id)),
	}
	if check.Status == common.CheckStatusFailure {
		metricPayload["status"] = "0"
		metricPayload["error_message"] = check.Message
		common.AddMetrices("job_status", metricPayload)
	} else if check.Status == common.CheckStatusSuccess || check.Status == common.CheckStatusUnqualified {
		// 成功是待审批的时候如何为检测成功
		metricPayload["status"] = "1"
		common.AddMetrices("job_status", metricPayload)
	}

	jobDone := JobDone{
		EventName:  strconv.FormatInt(check.Id, 10),
		AppId:      check.AppId,
		Status:     check.Status,
		JobType:    check.JobName,
		CheckId:    check.Id,
		CreateTime: check.StartTime,
		EndTime:    check.ReportTime,
		Duration:   check.ReportTime - check.StartTime,
		Url:        check.Url,
		Message:    check.Message,
		MrIid:      check.MrIid,
		ProjectId:  check.ProjectId,
		CommitId:   check.CommitId,
		HostId:     hostId,
	}
	logs.CtxInfo(ctx, "jobDone", jobDone)
	value, _ := json.Marshal(jobDone)
	msg := eventbus.NewProducerEventBuilder().
		WithKey([]byte(jobDone.EventName)).
		WithValue(value).
		Build()

	if err := eventbusService.ProducerSecurityJob.Send(ctx, msg); err != nil {
		logs.Warnf("job done event sync error: %#v", err)
		return
	}
}

type IssueMetric struct {
	EventName   string `json:"event_name"`
	IssueHash   string `json:"issue_hash"` //issue_id
	HostId      int64  `json:"host_id"`
	AppId       int64  `json:"app_id"`
	IssueStatus string `json:"issue_status"`
	JobType     string `json:"job_type"`
	Time        int64  `json:"time"`
}

func IssueRepaired(ctx context.Context, hostId int64) {
	const mapKeyFmt = "%d_%s_%s"
	simpleAggregateIssues, err := mysql.WriteRepoOperator.ListAggregateIssuesByHostIdImmediately(ctx, hostId)
	if utils.MysqlNoRows(err) {
		err = nil
		return
	} else if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	var simpleAggregateIssueMap = map[string]mysql.SecurityAggregateIssue{}
	for _, simpleAggregateIssue := range simpleAggregateIssues {
		simpleAggregateIssueMap[fmt.Sprintf(mapKeyFmt, hostId, simpleAggregateIssue.JobName, simpleAggregateIssue.IssueId)] = simpleAggregateIssue
	}
	for key, value := range simpleAggregateIssueMap {
		issueMetric := IssueMetric{
			EventName:   key,
			IssueHash:   value.IssueId,
			HostId:      hostId,
			AppId:       value.AppId,
			IssueStatus: value.Status,
			JobType:     value.JobName,
			Time:        time.Now().Unix(),
		}
		logs.CtxInfo(ctx, "issueMetric", issueMetric)
		eventValue, _ := json.Marshal(issueMetric)
		msg := eventbus.NewProducerEventBuilder().
			WithKey([]byte(issueMetric.EventName)).
			WithValue(eventValue).
			Build()

		if err = eventbusService.ProducerSecurityIssueRepaired.Send(ctx, msg); err != nil {
			logs.Warnf("issue repaired event sync error: %#v", err)
			return
		}
	}

}

type Check struct {
	Id           int64  `json:"id"` //唯一标识
	HostId       int64  `json:"host_id"`
	ProjectId    int64  `json:"project_id"`
	MrIid        int64  `json:"mr_iid"`
	ProjectName  string `json:"project_name"`
	GitAddress   string `json:"git_address"`
	CommitId     string `json:"commit_id"`
	BaseCommitId string `json:"base_commit_id"`
	Branch       string `json:"branch"`
	BaseBranch   string `json:"base_branch"`
	GroupName    string `json:"group_name"`
	UserEmail    string `json:"user_email"`
	Url          string `json:"url"`
	Message      string `json:"message"`
	JobName      string `json:"job_name"`
	Status       string `json:"status"`
	CacheKey     string `json:"cache_key"`
	Param        string `json:"param"`
	StartTime    int64  `json:"start_time"`
	TimeoutSpan  int64  `json:"timeout_span"`
	ReportTime   int64  `json:"report_time"`
}

func CheckChange(ctx context.Context, check mysql.SecurityCheck, hostId int64, eventType metrics_data_collect.EventType) {
	metricData := Check{}
	str, err := json.Marshal(check)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	err = json.Unmarshal(str, &metricData)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	metricData.HostId = hostId
	uploadEvent(ctx, check.AppId, eventType, utils.ToJson(metricData))
}

func uploadEvent(ctx context.Context, appId int64, eventType metrics_data_collect.EventType, eventParams string) {
	if !env.IsProduct() {
		return
	}

	m := &metrics_data_collect.Metrics{
		CommonParam: &metrics_data_collect.CommonParam{
			AppId: appId,
		},
		Events: []*metrics_data_collect.Event{
			{
				Type:      eventType,
				EventTime: time.Now().Unix(),
				Params:    eventParams,
			},
		},
	}

	_, err := rpc.BitsMetricsDataClient.Sink(ctx, &metrics_data_collect.SinkRequset{
		Metrics: m,
	})
	if err != nil {
		logs.CtxError(ctx, "sink event %+v, err: %v", m, err)
	}
}
