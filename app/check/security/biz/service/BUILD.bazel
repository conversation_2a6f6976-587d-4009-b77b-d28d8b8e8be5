load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/check/security/biz/service",
    visibility = ["//visibility:public"],
    deps = [
        "//app/check/security/biz/service/eventbus",
        "//app/check/security/biz/service/eventbus/handler",
        "//app/check/security/biz/service/metric",
        "//app/check/security/biz/service/optimus",
        "//app/check/security/biz/service/rocket_mq",
        "//app/check/security/biz/service/rpc",
        "//app/check/security/biz/service/tcc",
        "//libs/common_lib/utils",
    ],
)
