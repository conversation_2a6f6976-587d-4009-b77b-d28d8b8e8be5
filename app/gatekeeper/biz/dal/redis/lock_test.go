package redis

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/utils"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bits/gatekeeper/process"
	"code.byted.org/kv/goredis"
	redis "code.byted.org/kv/redis-v6"
)

func TestNewCheckRetryLockAutoGen(t *testing.T) {
	// Verify the function behavior under default conditions.
	t.Run("testNewCheckRetryLock_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var uniqueIDRet1Mock uint64
			var uniqueIDRet2Mock error
			mockey.Mock(utils.UniqueID).Return(uniqueIDRet1Mock, uniqueIDRet2Mock).Build()

			// prepare parameters
			var checkID uint64

			// run target function and assert
			got1 := NewCheckRetryLock(checkID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestNewCheckPointExecuteLockAutoGen(t *testing.T) {
	// Verify the function under default conditions.
	t.Run("testNewCheckPointExecuteLock_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var uniqueIDRet1Mock uint64
			var uniqueIDRet2Mock error
			mockey.Mock(utils.UniqueID).Return(uniqueIDRet1Mock, uniqueIDRet2Mock).Build()

			// prepare parameters
			var phase string
			var bizID int64
			var bizScene process.BizScene

			// run target function and assert
			got1 := NewCheckPointExecuteLock(bizID, bizScene, phase)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestNewCheckPointForceCheckLock(t *testing.T) {
	// Verify the function under default conditions.
	t.Run("TestNewCheckPointForceCheckLock", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var uniqueIDRet1Mock uint64
			var uniqueIDRet2Mock error
			mockey.Mock(utils.UniqueID).Return(uniqueIDRet1Mock, uniqueIDRet2Mock).Build()

			// prepare parameters
			var checkpointID uint64

			// run target function and assert
			got1 := NewCheckpointForceCheckLock(checkpointID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestMockLock_LockAutoGen(t *testing.T) {
	// Verify the Lock function returns nil as expected.
	t.Run("testMockLock_LockSuccess", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := MockLock{}
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.Lock(ctx)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestLockInfo_LockAutoGen(t *testing.T) {
	// Verify the behavior when the lock is already acquired.
	t.Run("testLockInfo_Lock_AlreadyLocked", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var resultRet1Mock int64
			var resultRet2Mock error
			mockey.Mock((*redis.IntCmd).Result, mockey.OptUnsafe).Return(resultRet1Mock, resultRet2Mock).Build()

			var clientMockPtrValueClient redis.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.MockValue(&client).To(clientMock)

			// prepare parameters
			var receiverPtrValue LockInfo
			receiver := &receiverPtrValue
			receiver.lockFlag = true
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.Lock(ctx)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the behavior under default conditions.
	t.Run("testLockInfo_Lock_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var resultRet1Mock int64
			var resultRet2Mock error
			mockey.Mock((*redis.IntCmd).Result, mockey.OptUnsafe).Return(resultRet1Mock, resultRet2Mock).Build()

			var clientMockPtrValueClient redis.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.MockValue(&client).To(clientMock)

			// prepare parameters
			var receiverPtrValue LockInfo
			receiver := &receiverPtrValue
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.Lock(ctx)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestMockLock_IsLockAutoGen(t *testing.T) {
	// Verify the default behavior of IsLock function.
	t.Run("testMockLock_IsLock", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := MockLock{}
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.IsLock(ctx)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func TestMockLock_UnLockAutoGen(t *testing.T) {
	// Verify the UnLock function under default conditions.
	t.Run("testMockLock_UnLock", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			receiver := MockLock{}
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.UnLock(ctx)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestLockInfo_UnLockAutoGen(t *testing.T) {
	// Verify the behavior when the lock is already unlocked.
	t.Run("testLockInfo_UnLock_AlreadyUnlocked", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var clientMockPtrValueClient redis.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.MockValue(&client).To(clientMock)

			// prepare parameters
			var receiverPtrValue LockInfo
			receiver := &receiverPtrValue
			receiver.lockFlag = false
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.UnLock(ctx)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// Verify the scenario when the lock is locked and an error occurs during unlocking.
	t.Run("testLockInfo_UnLock_LockedAndError", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var clientMockPtrValueClient redis.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.MockValue(&client).To(clientMock)

			// prepare parameters
			var receiverPtrValue LockInfo
			receiver := &receiverPtrValue
			receiver.lockFlag = true
			ctx := context.Background()
			convey.So(func() { _ = receiver.UnLock(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestLockInfo_IsLockAutoGen(t *testing.T) {
	// Verify the IsLock function when lockFlag is true.
	t.Run("testLockInfo_IsLock_lockFlagTrue", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var resultRet1Mock string
			var resultRet2Mock error
			mockey.Mock((*redis.StringCmd).Result, mockey.OptUnsafe).Return(resultRet1Mock, resultRet2Mock).Build()

			var clientMockPtrValueClient redis.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.MockValue(&client).To(clientMock)

			// prepare parameters
			var receiverPtrValue LockInfo
			receiver := &receiverPtrValue
			receiver.lockFlag = true
			ctx := context.Background()

			// run target function and assert
			got1 := receiver.IsLock(ctx)
			convey.So(got1, convey.ShouldEqual, true)
		})
	})

	// Verify the IsLock function when lockFlag is false and expect a panic.
	t.Run("testLockInfo_IsLock_lockFlagFalse", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var resultRet1Mock string
			var resultRet2Mock error
			mockey.Mock((*redis.StringCmd).Result, mockey.OptUnsafe).Return(resultRet1Mock, resultRet2Mock).Build()

			var clientMockPtrValueClient redis.Client
			clientMockPtrValue := goredis.Client{Client: &clientMockPtrValueClient}
			clientMock := &clientMockPtrValue
			mockey.MockValue(&client).To(clientMock)

			// prepare parameters
			var receiverPtrValue LockInfo
			receiver := &receiverPtrValue
			receiver.lockFlag = false
			ctx := context.Background()
			convey.So(func() { _ = receiver.IsLock(ctx) }, convey.ShouldPanic)
		})
	})

}

func TestNewCheckPointUpdateLockAutoGen(t *testing.T) {
	// Verify the function under default conditions.
	t.Run("testNewCheckPointUpdateLock_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var uniqueIDRet1Mock uint64
			var uniqueIDRet2Mock error
			mockey.Mock(utils.UniqueID).Return(uniqueIDRet1Mock, uniqueIDRet2Mock).Build()

			// prepare parameters
			var checkPointID uint64

			// run target function and assert
			got1 := NewCheckPointUpdateLock(checkPointID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}
