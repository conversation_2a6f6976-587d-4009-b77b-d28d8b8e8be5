package thirdparty

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bits/integration/multi"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bytedance/bits/dev"
)

type IntegrationRpcSvc interface {
	CheckIntegrationStatus(ctx context.Context, devID int64) (result *multi.AccessCheckNormalResp, err error)
	CheckDevTaskSubDepConflict(ctx context.Context, devTask *dev.DevTask) (result *multi.AccessCheckSCMConflictResp, err error)
	UpdateIntegrationSubDependency(ctx context.Context, devTaskID, integrationID int64, scmInfos []*multi.ModifySCMItem) error
	GetIntegrationDevTaskIDList(ctx context.Context, integrationID int64) (devTaskIDs []int64, err error)
	GetIntegrationBasicInfo(ctx context.Context, integrationID int64) (resp *multi.GetIntegrationBasicInfoResp, err error)
	RunGateKeeperCheck(ctx context.Context, integrationID int64, checkItemType multi.CheckItemType) (passFlag bool, detail *multi.GateKeeperCheckDetail, err error)
	GetIntegrationByDevTask(ctx context.Context, devTaskID int64) (detail *multi.Integration, err error)
	CheckIntegrationCanMerge(ctx context.Context, req *multi.IntegrationCanMergeCheckReq) (result *multi.IntegrationCanMergeCheckResp, err error)
}

type IntegrationServerRpc struct {
}

func NewIntegrationServerRPC() IntegrationRpcSvc {
	return &IntegrationServerRpc{}
}

func (i *IntegrationServerRpc) CheckIntegrationStatus(ctx context.Context, devID int64) (result *multi.AccessCheckNormalResp, err error) {
	result, err = integrationClient.AccessCheckNormal(ctx, &multi.AccessCheckNormalReq{DevTaskID: devID})
	if err != nil {
		return
	}
	if result == nil {
		err = emptyResponseError
		return
	}
	return
}

func (i *IntegrationServerRpc) CheckDevTaskSubDepConflict(ctx context.Context, devTask *dev.DevTask) (result *multi.AccessCheckSCMConflictResp, err error) {
	result, err = integrationClient.AccessCheckSCMConflict(ctx, &multi.AccessCheckSCMConflictReq{DevTask: devTask})
	if result == nil && err == nil {
		err = emptyResponseError
	}
	return
}

func (i *IntegrationServerRpc) UpdateIntegrationSubDependency(ctx context.Context, devTaskID, integrationID int64, scmInfos []*multi.ModifySCMItem) error {
	_, err := integrationClient.ModifySCMContent(ctx, &multi.ModifySCMContentReq{
		DevId:         devTaskID,
		IntegrationId: integrationID,
		Items:         scmInfos,
	})
	return err
}

func (i *IntegrationServerRpc) GetIntegrationDevTaskIDList(ctx context.Context, integrationID int64) (devTaskIDs []int64, err error) {
	resp, err := integrationClient.GetDevTaskIdsByIntegrationId(ctx, &multi.GetDevTaskIdsByIntegrationIdReq{IntegrationId: integrationID})
	if err != nil {
		return
	}
	if resp == nil {
		err = emptyResponseError
		return
	}
	devTaskIDs = resp.DevIds
	return
}

func (i *IntegrationServerRpc) GetIntegrationBasicInfo(ctx context.Context, integrationID int64) (resp *multi.GetIntegrationBasicInfoResp, err error) {
	resp, err = integrationClient.GetIntegrationBasicInfo(ctx, &multi.GetIntegrationBasicInfoReq{IntegrationId: integrationID})
	if err != nil {
		return
	}
	if resp == nil {
		err = emptyResponseError
	}
	return
}

func (i *IntegrationServerRpc) RunGateKeeperCheck(ctx context.Context, integrationID int64, checkItemType multi.CheckItemType) (passFlag bool, detail *multi.GateKeeperCheckDetail, err error) {
	resp, err := integrationClient.RunGateKeeperCheck(ctx, &multi.RunGateKeeperCheckReq{IntegrationId: integrationID, CheckItemType: checkItemType})
	if err != nil {
		return
	}
	if resp == nil {
		err = emptyResponseError
	}
	return resp.GetResult_(), resp.GetDetail(), nil
}

func (i *IntegrationServerRpc) GetIntegrationByDevTask(ctx context.Context, devTaskID int64) (detail *multi.Integration, err error) {
	resp, err := integrationClient.GetIntegrationByDevTask(ctx, &multi.GetIntegrationByDevTaskReq{DevTaskId: devTaskID})
	if err != nil {
		return
	}
	if resp == nil || resp.IntegrationInfo == nil {
		err = emptyResponseError
	}
	return resp.IntegrationInfo, err
}

func (i *IntegrationServerRpc) CheckIntegrationCanMerge(ctx context.Context, req *multi.IntegrationCanMergeCheckReq) (result *multi.IntegrationCanMergeCheckResp, err error) {
	result, err = integrationClient.IntegrationCanMergeCheck(ctx, req)
	if err != nil {
		return
	}
	if result == nil {
		err = emptyResponseError
		return
	}
	return
}
