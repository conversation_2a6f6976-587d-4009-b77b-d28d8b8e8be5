package orm

import (
	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/utils"
	gk "code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bits/gatekeeper/process"
	json "github.com/bytedance/sonic"
	"gorm.io/gorm"
)

type TriggerSource string

const (
	TriggerSourceGateKeeper TriggerSource = "gatekeeper"
	TriggerSourcePipeline   TriggerSource = "pipeline"
)

type GateKeeperCheck struct {
	gorm.Model
	CheckID            uint64 `gorm:"uniqueIndex"`
	Name               string
	NameI18N           string `gorm:"column:name_i18n"`
	Message            string
	MessageI18N        string `gorm:"column:message_i18n"`
	CheckPointID       uint64 `gorm:"idx_check_point,priority:1"`
	ExecutionID        uint64 `gorm:"idx_check_point,priority:2"`
	CheckItemType      gk.CheckItemType
	CheckStatus        int
	StartTime          int64
	EndTime            int64
	TriggerSource      TriggerSource
	ConfigSnapshot     string
	ResultData         string
	ResultDataLink     string
	RetryCheckID       uint64
	HideDisplay        bool
	ResultDataChecksum string
	ApproveInfo        string
	ErrCode            int
}

func (g *GateKeeperCheck) TableName() string {
	return "gatekeeper_check"
}

func (g *GateKeeperCheck) BeforeCreate(tx *gorm.DB) (err error) {
	if g.CheckID != 0 {
		return nil
	}
	g.CheckID, err = utils.UniqueID()
	return err
}

func (g *GateKeeperCheck) GetConfig() (config *GateKeeperCheckItem, err error) {
	config = new(GateKeeperCheckItem)
	err = json.Unmarshal([]byte(g.ConfigSnapshot), &config)
	return
}

func (g *GateKeeperCheck) GetApprove() *gk.ApproveInfo {
	config := new(gk.ApproveInfo)
	_ = json.Unmarshal([]byte(g.ApproveInfo), &config)
	return config
}
