package builtin

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/dal/fg"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/model"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/model/protocol"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/thirdparty"
	gk "code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bits/gatekeeper/process"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/mono_managerpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	json "github.com/bytedance/sonic"
)

// 代码可合入性检测
type CodeCanMergeCheck struct {
	config *model.CheckItemConfig

	gitSvc           thirdparty.GitServerRpcSvc
	devTaskSvc       thirdparty.DevTaskRpcSvc
	configSvc        thirdparty.ConfigRPCSvc
	integrationSvc   thirdparty.IntegrationRpcSvc
	releaseTicketSvc thirdparty.ReleaseTicketSvc
	nextCodeSvc      thirdparty.NextCodeRpcSvc
	fgSvc            fg.FeatureGateService
}

func NewCodeCanMergeCheckDriver(ctx context.Context, config *model.CheckItemConfig) *CodeCanMergeCheck {
	return &CodeCanMergeCheck{
		config:           config,
		gitSvc:           thirdparty.NewGitServerRPC(),
		devTaskSvc:       thirdparty.NewDevTaskRpc(),
		configSvc:        thirdparty.NewConfigRPC(),
		integrationSvc:   thirdparty.NewIntegrationServerRPC(),
		releaseTicketSvc: thirdparty.NewReleaseTicketRpc(),
		nextCodeSvc:      thirdparty.NewNextCodeRpc(),
		fgSvc:            fg.NewFeatureGateSvc(),
	}
}

func isAllowReasonForCodeMerge(reason string) bool {
	// review and check is guaranteed in other builtin checks
	return isUnmergeableReasonAlreadyMerge(reason) || isUnmergeableReasonCodebaseCIFail(reason) || isUnmergeableReasonCodeReviewFail(reason) || isUnmergeableReasonMergeQueue(reason)
}

func (c *CodeCanMergeCheck) executeCodeMergeCheck(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	log.V2.Info().With(ctx).Str("start code merge check").KVs("check_id", req.CheckID).Emit()
	resp = &protocol.ExecutionResp{
		CheckID: req.CheckID,
		Status:  gk.CheckStatus_CheckStatusRunning,
	}
	result := gk.CodeConflictCheckResult_{
		ConflictDevChanges: []*gk.DevChangeItem{},
	}
	defer func() {
		if bits_err.IsBitsErr(err) {
			resp.Code = int(bits_err.ToBizError(err).Code())
		}
		if resp.ResultData == nil {
			data, _ := json.Marshal(result)
			resp.ResultData = data
		}
	}()
	// get changes need check
	changes := make([]*ChangeInfo, 0)
	if req.ProcessContext != nil && req.ProcessContext.CheckDimension == gk.CheckDimension_CheckDimensionChangeItem.String() {
		changeItem, err := c.devTaskSvc.GetChangeItemByID(ctx, int64(req.ProcessContext.CheckSetID))
		if err != nil {
			return resp, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetChangeItem
		}
		changes = adaptChangeItemResponseToCodeChange(changeItem)
	} else {
		changeInfo, err := c.devTaskSvc.GetDevTaskChangeInfo(ctx, int64(req.ProcessContext.BizID))
		if err != nil {
			log.V2.Error().With(ctx).Str("get dev change info failed").Error(err).Emit()
			return resp, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDevChangeInfo
		}
		changes = gslice.Map(changeInfo.Changes, adaptChangeInfoToCodeChange)
	}
	// return success when no changes
	if len(changes) == 0 {
		log.V2.Info().With(ctx).Str("dev task no changes, just return success").KVs("change_item_id", req.ProcessContext.CheckSetID).Emit()
		return protocol.NewNoChangeSuccessResp(req.CheckID), nil
	}
	// do check with changes
	changesCheckResult := new(changesCheckResult)
	if c.fgSvc.CodeMergeCheckV2Enabled(ctx, req.SpaceID) {
		log.V2.Info().With(ctx).Str("start code merge check V2").Emit()
		changesCheckResult, err = c.checkCodeChangesCanMergeV2(ctx, changes, int64(req.ProcessContext.BizID), req.SpaceID, req.ProcessContext.Stage, req.Operator)
		if err != nil {
			return resp, err
		}
	} else {
		changesCheckResult, err = c.checkCodeChangesCanMerge(ctx, changes, int64(req.ProcessContext.BizID), req.SpaceID, req.ProcessContext.Stage, req.Operator)
		if err != nil {
			return resp, err
		}
	}
	result.ConflictDevChanges = changesCheckResult.changes

	data, err := json.Marshal(result)
	if err != nil {
		log.V2.Error().With(ctx).Str("marshal result failed").Error(err).Emit()
		return
	}
	resp.ResultData = data
	if changesCheckResult.pass {
		resp.Status = gk.CheckStatus_CheckStatusSuccess
	} else {
		resp.Status = gk.CheckStatus_CheckStatusFailed
		resp.Code = int(bits_err.GATEKEEPERCHECKCODEMERGE.NotAllChangesReadyToMerge.Code())
	}
	return
}

type changesCheckResult struct {
	changes []*gk.DevChangeItem
	pass    bool
}

func (c *CodeCanMergeCheck) checkCodeChangesCanMerge(ctx context.Context, changes []*ChangeInfo, devBasicID int64, spaceID int64, stage, operator string) (*changesCheckResult, error) {
	changesCheckResult := &changesCheckResult{
		changes: make([]*gk.DevChangeItem, 0),
		pass:    true,
	}
	basicInfoResp, err := c.devTaskSvc.GetDevTaskBasicInfo(ctx, devBasicID)
	if err != nil {
		log.V2.Error().With(ctx).Str("get dev task basic info failed").Error(err).Emit()
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDevTaskBasicInfo
	}
	devTaskType := basicInfoResp.BasicInfo.GetType()
	// get dev task errors
	devTaskErrors, err := c.devTaskSvc.GetDevTaskErrors(ctx, devBasicID)
	if err != nil {
		log.V2.Error().With(ctx).Str("get dev task errors failed").Error(err).Emit()
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDevTaskErrorsFailed
	}
	// get code merge check config detail
	config, _ := c.configSvc.GetGatekeeperCheckConfig(ctx, &config_service.GetGateKeeperConfigRequest{
		WorkspaceId:   spaceID,
		WorkflowId:    basicInfoResp.GetBasicInfo().GetDevTaskTemplateId(),
		WorkflowFrom:  config_service.WorkflowFrom_devTask,
		WorkflowStage: stage,
	}, config_service.CheckType_codeMerge, false)
	var codeMergeConfigDetail = new(config_service.CodeMergeConfig)
	if config != nil && config.ConfigDetail != nil && config.ConfigDetail.CodeMergeConfig != nil {
		codeMergeConfigDetail = config.ConfigDetail.GetCodeMergeConfig()
	}

	for _, change := range changes {
		if change.Manifest == nil || change.Manifest.CodeElement == nil {
			continue
		}
		// merge or close change not block
		if change.Manifest.CodeElement.Status == dev.CodeChangeStatus_merged || change.Manifest.CodeElement.Status == dev.CodeChangeStatus_closed {
			continue
		}
		devChangeItem := &gk.DevChangeItem{
			DevTaskID:      devBasicID,
			DevChangeID:    change.Id,
			DevChangeName:  change.Title,
			CodebaseURL:    change.Manifest.CodeElement.Url,
			RepoID:         change.Manifest.CodeElement.RepoId,
			RepoPath:       change.Manifest.CodeElement.RepoPath,
			ContributionID: change.ContributionID,
			SourceBranch:   change.Manifest.CodeElement.SourceBranch,
			TargetBranch:   change.Manifest.CodeElement.TargetBranch,
		}
		// check dev task errors
		if _, ok := gslice.Find(devTaskErrors, func(taskErr *dev.DevTaskError) bool {
			if taskErr.BizType == dev.DevTaskErrorBizType_change_mr && taskErr.BizKey == strconv.FormatInt(change.Id, 10) {
				return true
			}
			return false
		}).Get(); ok {
			changesCheckResult.pass = false
			devChangeItem.FailReason = unmergeableReasonFailReasonNoMR
			devChangeItem.CodebaseURL = ""
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		if change.Manifest.CodeElement.TargetBranch == "" || change.Manifest.CodeElement.CodeChangeId == 0 {
			changesCheckResult.pass = false
			devChangeItem.FailReason = unmergeableReasonFailReasonNoMR
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		if change.IsDraft {
			changesCheckResult.pass = false
			devChangeItem.FailReason = unmergeableReasonFailIsDraft
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		// get code change meta info
		codeChangeMeta, err := c.gitSvc.GetCodebaseChangeMetaByProjectID(ctx, change.Manifest.CodeElement.RepoId, change.Manifest.CodeElement.Iid, gptr.Of(operator))
		if err != nil {
			log.V2.Error().With(ctx).Str("get code change meta failed").Error(err).Emit()
			return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetCodeChangeMeta
		}
		// change submittable check
		submittableResult, err := c.changeSubmittableCheck(ctx, codeChangeMeta)
		if err != nil {
			log.V2.Error().With(ctx).Str("change submittable check failed").Error(err).Emit()
			return changesCheckResult, err
		}
		if !submittableResult.Pass {
			// these kinds of dev tasks should ignore branch fall back https://meego.feishu.cn/bitsdevops/story/detail/16558996?super_app=1&open_type=open_in_page&target_width=700&min_width=550&is_nearest_order=1
			if (devTaskType == dev.DevTaskType_archive || devTaskType == dev.DevTaskType_merge) && submittableResult.FailReason == unmergeableReasonRebaseNeeded {
				log.V2.Info().With(ctx).Str("follow-up checks will be ignored").Emit()
				continue
			}
			changesCheckResult.pass = false
			devChangeItem = c.dealDevChangeItemByFailReason(ctx, devChangeItem, codeChangeMeta, codeMergeConfigDetail, submittableResult.FailReason, operator)
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		// integration&archive branch fall behind check
		if codeMergeConfigDetail.IntegrationBranchFallBehindArchiveBranchEnable && devTaskType != dev.DevTaskType_revert && devTaskType != dev.DevTaskType_merge && devTaskType != dev.DevTaskType_archive {
			// 如果打开了这个检测项目，说明有关联的发布单，获取发布单id，然后调用发布单接口判断是否落后
			devTaskRelatedInfo, err := c.devTaskSvc.GetDevTaskRelatedInfo(ctx, devBasicID)
			if err != nil {
				log.V2.Error().With(ctx).Str("get dev task related info failed").Error(err).Emit()
				return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetIntegrationInfo
			}
			if devTaskRelatedInfo.GetReleaseTicket() == nil {
				// 没有获取到发布单信息，可能是 TBD 流程，走老逻辑，自己获取检测结果
				log.V2.Info().With(ctx).Str("no release ticket, use old logic").Emit()
				integration, err := c.integrationSvc.GetIntegrationByDevTask(ctx, devBasicID)
				if err != nil {
					log.V2.Error().With(ctx).Str("get integration by dev task failed").Error(err).Emit()
					return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetIntegrationInfo
				}
				if integration.ArchiveBranch == "BRANCH_NAMING_TYPE_GIT_DEFAULT" {
					integration.ArchiveBranch, err = c.gitSvc.GetRepoDefaultBranch(ctx, int32(codeChangeMeta.ProjectID))
					if err != nil {
						log.V2.Error().With(ctx).Str("get repo default branch failed").Error(err).Emit()
						return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetRepoDefaultBranch
					}
				}
				if integration.ReleaseBranch == "BRANCH_NAMING_TYPE_GIT_DEFAULT" {
					integration.ReleaseBranch, err = c.gitSvc.GetRepoDefaultBranch(ctx, int32(codeChangeMeta.ProjectID))
					if err != nil {
						log.V2.Error().With(ctx).Str("get repo default branch failed").Error(err).Emit()
						return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetRepoDefaultBranch
					}
				}
				if len(integration.ArchiveBranch) > 0 && len(integration.ReleaseBranch) > 0 && integration.ArchiveBranch != integration.ReleaseBranch && change.Manifest.CodeElement.TargetBranch != integration.ArchiveBranch {
					divertCommits, err := c.gitSvc.GetDivertCommitCount(ctx, codeChangeMeta.ProjectID, integration.ReleaseBranch, integration.ArchiveBranch, "", operator)
					if err != nil {
						log.V2.Error().With(ctx).Str("get divert commit count failed").Error(err).Emit()
						changesCheckResult.pass = false
						devChangeItem.FailReason = unmergeableReasonIntegrationBranchFallBehindArchiveBranch
						changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
					}
					if len(divertCommits) > 0 && divertCommits[0].BehindCount > 0 {
						changesCheckResult.pass = false
						devChangeItem.FailReason = unmergeableReasonIntegrationBranchFallBehindArchiveBranch
						changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
						continue
					}
				}
				continue
			}
			releaseID := devTaskRelatedInfo.GetReleaseTicket().GetId()
			isBackward, err := c.releaseTicketSvc.CheckBranchBackWardV2(ctx, uint64(releaseID), uint64(codeChangeMeta.ProjectID))
			if err != nil {
				log.V2.Error().With(ctx).Str("check branch backward failed").Error(err).Emit()
				return changesCheckResult, err
			}
			if isBackward {
				changesCheckResult.pass = false
				devChangeItem.FailReason = unmergeableReasonIntegrationBranchFallBehindArchiveBranch
				changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
				continue
			}
		}
		// development&integration branch fall behind check
		if codeMergeConfigDetail.BranchBackwardDetectionEnable {
			behindCheckResp, err := c.doDevelopmentBranchFallBehindCheck(ctx, codeChangeMeta, codeMergeConfigDetail, change, spaceID, operator)
			if err != nil {
				return nil, err
			}
			log.V2.Info().With(ctx).Str("get development branch behind check result").Obj(behindCheckResp).Emit()
			if behindCheckResp.ReachFallBehindLimit {
				changesCheckResult.pass = false
				devChangeItem.FailReason = unmergeableReasonRebaseNeeded
				devChangeItem.BackwardInfo = &gk.BranchBackwardInfo{
					CurrentBackwardCount: behindCheckResp.BehindCommitsCount,
					MaxBackwardCount:     codeMergeConfigDetail.BranchBackwardLimit,
				}
				changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			}
		}
	}
	return changesCheckResult, nil
}

type checkResult struct {
	Pass       bool
	FailReason string
}

func (c *CodeCanMergeCheck) changeSubmittableCheck(ctx context.Context, codeChangeMeta *thirdparty.CodeChangeMeta) (*checkResult, error) {
	submittable, err := c.nextCodeSvc.GetMergeRequestMergeability(ctx, codeChangeMeta.RepoID, codeChangeMeta.ChangeID)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetSubmittableInfo
	}
	log.V2.Info().With(ctx).KVs("codebase_change_id", codeChangeMeta.ChangeID, "submittable", submittable).Emit()
	result := &checkResult{
		Pass: true,
	}
	if !submittable.Mergeable && !isAllowReasonForCodeMerge(submittable.UnmergeableReason) {
		result.Pass = false
		result.FailReason = submittable.UnmergeableReason
	}
	return result, nil
}

func (c *CodeCanMergeCheck) dealDevChangeItemByFailReason(ctx context.Context, devChangeItem *gk.DevChangeItem, codeChangeMeta *thirdparty.CodeChangeMeta, codeMergeConfig *config_service.CodeMergeConfig, reason, operator string) *gk.DevChangeItem {
	switch reason {
	case unmergeableReasonRebaseNeeded:
		if codeMergeConfig.BranchBackwardDetectionEnable {
			divertCommits, err := c.gitSvc.GetDivertCommitCount(ctx, codeChangeMeta.ProjectID, codeChangeMeta.SourceBranch, codeChangeMeta.TargetBranch, codeChangeMeta.SourceSha, operator)
			if err != nil {
				log.V2.Error().With(ctx).Str("get divert commit count failed").Error(err).Emit()
			}
			if divertCommits != nil {
				devChangeItem.BackwardInfo = &gk.BranchBackwardInfo{
					CurrentBackwardCount: divertCommits[0].BehindCount,
					MaxBackwardCount:     codeMergeConfig.BranchBackwardLimit,
				}
			}
		}
	case unmergeableReasonBranchProtected:
		protectionRule, _ := c.gitSvc.GetBranchEffectedProtectionRule(ctx, codeChangeMeta.RepoID, codeChangeMeta.TargetBranch)
		if protectionRule != nil && protectionRule.ProtectionRule != nil {
			if gslice.Find(protectionRule.ProtectionRule.MergeAccess, func(item *git_server.MergeAccessItem) bool {
				return item.UserType == protectBranchMergeAccessUserType
			}).IsOK() {
				log.V2.Info().With(ctx).Str("user merge access role configured and merge by bot identity").Emit()
			}
			devChangeItem.ResolutionURL = gptr.Of(model.FormatProtectedBranchEditURL(codeChangeMeta.RepoPath, protectionRule.ProtectionRule.GetId()))
		}
	}
	devChangeItem.FailReason = reason
	devChangeItem.SourceBranch = codeChangeMeta.SourceBranch
	devChangeItem.TargetBranch = codeChangeMeta.TargetBranch
	return devChangeItem
}

type BranchFallBehindInfo struct {
	ReachFallBehindLimit bool
	BehindCommitsCount   int64
}

func (c *CodeCanMergeCheck) doDevelopmentBranchFallBehindCheck(ctx context.Context, codeChangeMeta *thirdparty.CodeChangeMeta, codeMergeConfig *config_service.CodeMergeConfig, changeInfo *ChangeInfo, spaceID int64, operator string) (*BranchFallBehindInfo, error) {
	resp := &BranchFallBehindInfo{
		ReachFallBehindLimit: false,
		BehindCommitsCount:   0,
	}
	divertCommits, err := c.gitSvc.GetDivertCommitCount(ctx, codeChangeMeta.ProjectID, codeChangeMeta.SourceBranch, codeChangeMeta.TargetBranch, codeChangeMeta.SourceSha, operator)
	if err != nil {
		log.V2.Error().With(ctx).Str("get divert commit count failed").Error(err).Emit()
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDivertCommit
	}
	if len(divertCommits) == 0 || divertCommits[0].BehindCount <= codeMergeConfig.BranchBackwardLimit {
		return resp, nil
	}
	resp.ReachFallBehindLimit = true
	resp.BehindCommitsCount = divertCommits[0].BehindCount
	// check if sub dependency
	if changeInfo.IsSubDependency {
		log.V2.Info().With(ctx).Str("change is sub dependency, just return").Emit()
		return resp, nil
	}
	if !c.fgSvc.MonoDevelopmentBranchFallBehindOptimizeEnabled(ctx, spaceID) {
		return resp, nil
	}
	// check if mono
	log.V2.Info().With(ctx).Str("start mono check").Emit()
	monoQueryProjects := gslice.Map(changeInfo.Projects, func(project *Project) *mono_managerpb.QueryProject {
		return &mono_managerpb.QueryProject{
			ProjectType:     sharedpb.ProjectType(int(project.Type)),
			ProjectUniqueId: project.UniqueID,
		}
	})
	projectPathInfos, err := c.releaseTicketSvc.GetMonoProjectsPathInfo(ctx, monoQueryProjects, true)
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return nil, err
	}
	monoProjectPathInfos := make([]*mono_managerpb.ProjectPathInfo, 0, len(projectPathInfos))
	for _, pathInfo := range projectPathInfos {
		if gslice.Find(changeInfo.Projects, func(project *Project) bool {
			return project.UniqueID == pathInfo.ProjectUniqueId && project.Type == int64(pathInfo.ProjectType)
		}).IsOK() && pathInfo.SubPath != "" {
			monoProjectPathInfos = append(monoProjectPathInfos, pathInfo)
		}
	}
	if len(monoProjectPathInfos) != len(changeInfo.Projects) {
		log.V2.Info().With(ctx).Str("not all mono projects, just return fall behind info").Emit()
		return resp, nil
	}
	// all mono projects, do mono diff match check
	log.V2.Info().With(ctx).Str("all projects are mono projects, do mono fall behind check").Obj(monoProjectPathInfos).Emit()
	inMonoProjects, err := c.checkIfFallBehindDiffInMonoProjects(ctx, codeChangeMeta, projectPathInfos)
	if err != nil {
		return nil, err
	}
	// fall behind diff not in current mono projects, turn to be no fall behind
	if !inMonoProjects {
		resp.ReachFallBehindLimit = false
		resp.BehindCommitsCount = 0
	}
	return resp, nil
}

func (c *CodeCanMergeCheck) checkIfFallBehindDiffInMonoProjects(ctx context.Context, codeChangeMeta *thirdparty.CodeChangeMeta, projectPathInfos []*mono_managerpb.ProjectPathInfo) (bool, error) {
	files, err := c.gitSvc.GetRepoDiffByCommit(ctx, codeChangeMeta.RepoID, codeChangeMeta.TargetSha, codeChangeMeta.SourceSha)
	if err != nil {
		log.V2.Error().With(ctx).Str("get repo diff failed").Error(err).Emit()
		return false, err
	}
	for _, pathInfo := range projectPathInfos {
		for _, file := range files {
			if strings.HasPrefix(file.FromPath, pathInfo.SubPath) || strings.HasPrefix(file.ToPath, pathInfo.SubPath) || strings.HasPrefix(file.Path, pathInfo.SubPath) {
				log.V2.Info().With(ctx).Str("match mono sub path").KVs("diff_file", file, "path_info", pathInfo).Emit()
				return true, nil
			}
			for _, dependencyPath := range pathInfo.DependencyPaths {
				if strings.HasPrefix(file.FromPath, dependencyPath.Path) || strings.HasPrefix(file.ToPath, dependencyPath.Path) || strings.HasPrefix(file.Path, dependencyPath.Path) {
					log.V2.Info().With(ctx).Str("match mono dep path").KVs("diff_file", file, "dep_path_info", dependencyPath, "path_info", pathInfo).Emit()
					return true, nil
				}
			}
		}
	}
	return false, nil
}

func (c *CodeCanMergeCheck) checkCodeChangesCanMergeV2(ctx context.Context, changes []*ChangeInfo, devBasicID int64, spaceID int64, stage, operator string) (*changesCheckResult, error) {
	changesCheckResult := &changesCheckResult{
		changes: make([]*gk.DevChangeItem, 0),
		pass:    true,
	}
	basicInfoResp, err := c.devTaskSvc.GetDevTaskBasicInfo(ctx, devBasicID)
	if err != nil {
		log.V2.Error().With(ctx).Str("get dev task basic info failed").Error(err).Emit()
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDevTaskBasicInfo
	}
	devTaskType := basicInfoResp.BasicInfo.GetType()
	// get dev task errors
	devTaskErrors, err := c.devTaskSvc.GetDevTaskErrors(ctx, devBasicID)
	if err != nil {
		log.V2.Error().With(ctx).Str("get dev task errors failed").Error(err).Emit()
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDevTaskErrorsFailed
	}
	// get code merge check config detail
	config, _ := c.configSvc.GetGatekeeperCheckConfig(ctx, &config_service.GetGateKeeperConfigRequest{
		WorkspaceId:   spaceID,
		WorkflowId:    basicInfoResp.GetBasicInfo().GetDevTaskTemplateId(),
		WorkflowFrom:  config_service.WorkflowFrom_devTask,
		WorkflowStage: stage,
	}, config_service.CheckType_codeMerge, false)
	var codeMergeConfigDetail = new(config_service.CodeMergeConfig)
	if config != nil && config.ConfigDetail != nil && config.ConfigDetail.CodeMergeConfig != nil {
		codeMergeConfigDetail = config.ConfigDetail.GetCodeMergeConfig()
	}

	for _, change := range changes {
		if change.Manifest == nil || change.Manifest.CodeElement == nil {
			continue
		}
		// merge or close change not block
		if change.Manifest.CodeElement.Status == dev.CodeChangeStatus_merged || change.Manifest.CodeElement.Status == dev.CodeChangeStatus_closed {
			continue
		}
		devChangeItem := &gk.DevChangeItem{
			DevTaskID:      devBasicID,
			DevChangeID:    change.Id,
			DevChangeName:  change.Title,
			CodebaseURL:    change.Manifest.CodeElement.Url,
			RepoID:         change.Manifest.CodeElement.RepoId,
			RepoPath:       change.Manifest.CodeElement.RepoPath,
			ContributionID: change.ContributionID,
			SourceBranch:   change.Manifest.CodeElement.SourceBranch,
			TargetBranch:   change.Manifest.CodeElement.TargetBranch,
		}
		// check dev task errors
		if _, ok := gslice.Find(devTaskErrors, func(taskErr *dev.DevTaskError) bool {
			if taskErr.BizType == dev.DevTaskErrorBizType_change_mr && taskErr.BizKey == strconv.FormatInt(change.Id, 10) {
				return true
			}
			return false
		}).Get(); ok {
			changesCheckResult.pass = false
			devChangeItem.FailReason = unmergeableReasonFailReasonNoMR
			devChangeItem.CodebaseURL = ""
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		if change.Manifest.CodeElement.TargetBranch == "" || change.Manifest.CodeElement.CodeChangeId == 0 {
			changesCheckResult.pass = false
			devChangeItem.FailReason = unmergeableReasonFailReasonNoMR
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		if change.IsDraft {
			changesCheckResult.pass = false
			devChangeItem.FailReason = unmergeableReasonFailIsDraft
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		// get code change meta info
		codeChangeMeta := &CodeChangeMeta{
			ChangeID:     change.Manifest.CodeElement.CodebaseChangeId,
			RepoID:       change.Manifest.CodeElement.CodebaseRepoId,
			ProjectID:    change.Manifest.CodeElement.RepoId,
			MrIID:        change.Manifest.CodeElement.Iid,
			SourceBranch: change.Manifest.CodeElement.SourceBranch,
			TargetBranch: change.Manifest.CodeElement.TargetBranch,
			RepoPath:     change.Manifest.CodeElement.GetRepoPath(),
		}
		// change submittable check
		submittableResult, err := c.changeSubmittableCheckV2(ctx, codeChangeMeta)
		if err != nil {
			log.V2.Error().With(ctx).Str("change submittable check failed").Error(err).Emit()
			return changesCheckResult, err
		}
		if !submittableResult.Pass {
			// these kinds of dev tasks should ignore branch fall back https://meego.feishu.cn/bitsdevops/story/detail/16558996?super_app=1&open_type=open_in_page&target_width=700&min_width=550&is_nearest_order=1
			if (devTaskType == dev.DevTaskType_archive || devTaskType == dev.DevTaskType_merge) && submittableResult.FailReason == unmergeableReasonRebaseNeeded {
				log.V2.Info().With(ctx).Str("follow-up checks will be ignored").Emit()
				continue
			}
			changesCheckResult.pass = false
			devChangeItem = c.dealDevChangeItemByFailReasonV2(ctx, devChangeItem, codeChangeMeta, codeMergeConfigDetail, submittableResult.FailReason, operator)
			changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			continue
		}
		// integration&archive branch fall behind check
		if codeMergeConfigDetail.IntegrationBranchFallBehindArchiveBranchEnable && devTaskType != dev.DevTaskType_revert && devTaskType != dev.DevTaskType_merge && devTaskType != dev.DevTaskType_archive {
			// 如果打开了这个检测项目，说明有关联的发布单，获取发布单id，然后调用发布单接口判断是否落后
			devTaskRelatedInfo, err := c.devTaskSvc.GetDevTaskRelatedInfo(ctx, devBasicID)
			if err != nil {
				log.V2.Error().With(ctx).Str("get dev task related info failed").Error(err).Emit()
				return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetIntegrationInfo
			}
			if devTaskRelatedInfo.GetReleaseTicket() == nil {
				// 没有获取到发布单信息，可能是 TBD 流程，走老逻辑，自己获取检测结果
				log.V2.Info().With(ctx).Str("no release ticket, use old logic").Emit()
				integration, err := c.integrationSvc.GetIntegrationByDevTask(ctx, devBasicID)
				if err != nil {
					log.V2.Error().With(ctx).Str("get integration by dev task failed").Error(err).Emit()
					return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetIntegrationInfo
				}
				if integration.ArchiveBranch == "BRANCH_NAMING_TYPE_GIT_DEFAULT" {
					integration.ArchiveBranch, err = c.nextCodeSvc.GetRepoDefaultBranch(ctx, codeChangeMeta.RepoID)
					if err != nil {
						log.V2.Error().With(ctx).Str("get repo default branch failed").Error(err).Emit()
						return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetRepoDefaultBranch
					}
				}
				if integration.ReleaseBranch == "BRANCH_NAMING_TYPE_GIT_DEFAULT" {
					integration.ReleaseBranch, err = c.nextCodeSvc.GetRepoDefaultBranch(ctx, codeChangeMeta.RepoID)
					if err != nil {
						log.V2.Error().With(ctx).Str("get repo default branch failed").Error(err).Emit()
						return changesCheckResult, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetRepoDefaultBranch
					}
				}
				if len(integration.ArchiveBranch) > 0 && len(integration.ReleaseBranch) > 0 && integration.ArchiveBranch != integration.ReleaseBranch && change.Manifest.CodeElement.TargetBranch != integration.ArchiveBranch {
					divertCommit, err := c.nextCodeSvc.CountDivergingCommit(ctx, codeChangeMeta.RepoID, integration.ReleaseBranch, integration.ArchiveBranch, gptr.Of(operator))
					if err != nil {
						log.V2.Error().With(ctx).Str("get divert commit count failed").Error(err).Emit()
						changesCheckResult.pass = false
						devChangeItem.FailReason = unmergeableReasonIntegrationBranchFallBehindArchiveBranch
						changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
					}
					if divertCommit != nil && divertCommit.GetBehindCommitCount() > 0 {
						changesCheckResult.pass = false
						devChangeItem.FailReason = unmergeableReasonIntegrationBranchFallBehindArchiveBranch
						changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
						continue
					}
				}
				continue
			}
			releaseID := devTaskRelatedInfo.GetReleaseTicket().GetId()
			isBackward, err := c.releaseTicketSvc.CheckBranchBackWardV2(ctx, uint64(releaseID), uint64(codeChangeMeta.ProjectID))
			if err != nil {
				log.V2.Error().With(ctx).Str("check branch backward failed").Error(err).Emit()
				return changesCheckResult, err
			}
			if isBackward {
				changesCheckResult.pass = false
				devChangeItem.FailReason = unmergeableReasonIntegrationBranchFallBehindArchiveBranch
				changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
				continue
			}
		}
		// development&integration branch fall behind check
		if codeMergeConfigDetail.BranchBackwardDetectionEnable {
			behindCheckResp, err := c.doDevelopmentBranchFallBehindCheckV2(ctx, codeChangeMeta, codeMergeConfigDetail, change, spaceID, operator)
			if err != nil {
				return nil, err
			}
			log.V2.Info().With(ctx).Str("get development branch behind check result").Obj(behindCheckResp).Emit()
			if behindCheckResp.ReachFallBehindLimit {
				changesCheckResult.pass = false
				devChangeItem.FailReason = unmergeableReasonRebaseNeeded
				devChangeItem.BackwardInfo = &gk.BranchBackwardInfo{
					CurrentBackwardCount: behindCheckResp.BehindCommitsCount,
					MaxBackwardCount:     codeMergeConfigDetail.BranchBackwardLimit,
				}
				changesCheckResult.changes = append(changesCheckResult.changes, devChangeItem)
			}
		}
	}
	return changesCheckResult, nil
}

func (c *CodeCanMergeCheck) changeSubmittableCheckV2(ctx context.Context, codeChangeMeta *CodeChangeMeta) (*checkResult, error) {
	submittable, err := c.nextCodeSvc.GetMergeRequestMergeability(ctx, codeChangeMeta.RepoID, codeChangeMeta.ChangeID)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetSubmittableInfo
	}
	log.V2.Info().With(ctx).KVs("codebase_change_id", codeChangeMeta.ChangeID, "submittable", submittable).Emit()
	result := &checkResult{
		Pass: true,
	}
	if !submittable.Mergeable && !isAllowReasonForCodeMerge(submittable.UnmergeableReason) {
		result.Pass = false
		result.FailReason = submittable.UnmergeableReason
	}
	return result, nil
}

func (c *CodeCanMergeCheck) dealDevChangeItemByFailReasonV2(ctx context.Context, devChangeItem *gk.DevChangeItem, codeChangeMeta *CodeChangeMeta, codeMergeConfig *config_service.CodeMergeConfig, reason, operator string) *gk.DevChangeItem {
	devChangeItem.FailReason = reason
	devChangeItem.SourceBranch = codeChangeMeta.SourceBranch
	devChangeItem.TargetBranch = codeChangeMeta.TargetBranch
	switch reason {
	case unmergeableReasonRebaseNeeded:
		if codeMergeConfig.BranchBackwardDetectionEnable {
			divergeCommit, err := c.nextCodeSvc.CountDivergingCommit(ctx, codeChangeMeta.RepoID, codeChangeMeta.SourceBranch, codeChangeMeta.TargetBranch, gptr.Of(operator))
			if err != nil {
				log.V2.Error().With(ctx).Str("get divert commit count failed").Error(err).Emit()
				return devChangeItem
			}
			devChangeItem.BackwardInfo = &gk.BranchBackwardInfo{
				CurrentBackwardCount: int64(divergeCommit.GetBehindCommitCount()),
				MaxBackwardCount:     codeMergeConfig.BranchBackwardLimit,
			}
		}
	case unmergeableReasonBranchProtected:
		protectionRule, err := c.nextCodeSvc.GetBranchMatchedProtectedBranch(ctx, codeChangeMeta.RepoID, codeChangeMeta.TargetBranch)
		if err != nil {
			return devChangeItem
		}
		if protectionRule.Protected && protectionRule.ProtectedBranch != nil {
			if gslice.Find(protectionRule.ProtectedBranch.MergeAccess, func(item *git_server.AccessInfo) bool {
				return item.GetUserType() == git_server.UserType_personal
			}).IsOK() {
				log.V2.Info().With(ctx).Str("user merge access role configured and merge by bot identity").Emit()
			}
			devChangeItem.ResolutionURL = gptr.Of(model.FormatProtectedBranchEditURL(codeChangeMeta.RepoPath, protectionRule.ProtectedBranch.GetId()))
		}
	}
	return devChangeItem
}

func (c *CodeCanMergeCheck) doDevelopmentBranchFallBehindCheckV2(ctx context.Context, codeChangeMeta *CodeChangeMeta, codeMergeConfig *config_service.CodeMergeConfig, changeInfo *ChangeInfo, spaceID int64, operator string) (*BranchFallBehindInfo, error) {
	resp := &BranchFallBehindInfo{
		ReachFallBehindLimit: false,
		BehindCommitsCount:   0,
	}
	divertCommit, err := c.nextCodeSvc.CountDivergingCommit(ctx, codeChangeMeta.RepoID, codeChangeMeta.SourceBranch, codeChangeMeta.TargetBranch, gptr.Of(operator))
	if err != nil {
		log.V2.Error().With(ctx).Str("get divert commit count failed").Error(err).Emit()
		return nil, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetDivertCommit
	}
	if int64(divertCommit.GetBehindCommitCount()) <= codeMergeConfig.BranchBackwardLimit {
		return resp, nil
	}
	resp.ReachFallBehindLimit = true
	resp.BehindCommitsCount = int64(divertCommit.GetBehindCommitCount())
	// check if sub dependency
	if changeInfo.IsSubDependency {
		log.V2.Info().With(ctx).Str("change is sub dependency, just return").Emit()
		return resp, nil
	}
	if !c.fgSvc.MonoDevelopmentBranchFallBehindOptimizeEnabled(ctx, spaceID) {
		return resp, nil
	}
	// check if mono
	log.V2.Info().With(ctx).Str("start mono check").Emit()
	monoQueryProjects := gslice.Map(changeInfo.Projects, func(project *Project) *mono_managerpb.QueryProject {
		return &mono_managerpb.QueryProject{
			ProjectType:     sharedpb.ProjectType(int(project.Type)),
			ProjectUniqueId: project.UniqueID,
		}
	})
	projectPathInfos, err := c.releaseTicketSvc.GetMonoProjectsPathInfo(ctx, monoQueryProjects, true)
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return nil, err
	}
	monoProjectPathInfos := make([]*mono_managerpb.ProjectPathInfo, 0, len(projectPathInfos))
	for _, pathInfo := range projectPathInfos {
		if gslice.Find(changeInfo.Projects, func(project *Project) bool {
			return project.UniqueID == pathInfo.ProjectUniqueId && project.Type == int64(pathInfo.ProjectType)
		}).IsOK() && pathInfo.SubPath != "" {
			monoProjectPathInfos = append(monoProjectPathInfos, pathInfo)
		}
	}
	if len(monoProjectPathInfos) != len(changeInfo.Projects) {
		log.V2.Info().With(ctx).Str("not all mono projects, just return fall behind info").Emit()
		return resp, nil
	}
	// all mono projects, do mono diff match check
	log.V2.Info().With(ctx).Str("all projects are mono projects, do mono fall behind check").Obj(monoProjectPathInfos).Emit()
	inMonoProjects, err := c.checkIfFallBehindDiffInMonoProjectsV2(ctx, codeChangeMeta, projectPathInfos)
	if err != nil {
		return nil, err
	}
	// fall behind diff not in current mono projects, turn to be no fall behind
	if !inMonoProjects {
		resp.ReachFallBehindLimit = false
		resp.BehindCommitsCount = 0
	}
	return resp, nil
}

func (c *CodeCanMergeCheck) checkIfFallBehindDiffInMonoProjectsV2(ctx context.Context, codeChangeMeta *CodeChangeMeta, projectPathInfos []*mono_managerpb.ProjectPathInfo) (bool, error) {
	sourceBranch, err := c.nextCodeSvc.GetBranchV2(ctx, codeChangeMeta.RepoID, codeChangeMeta.SourceBranch)
	if err != nil {
		log.V2.Error().With(ctx).Str("get source branch failed").Error(err).Emit()
		return false, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetMRBranch
	}
	targetBranch, err := c.nextCodeSvc.GetBranchV2(ctx, codeChangeMeta.RepoID, codeChangeMeta.TargetBranch)
	if err != nil {
		log.V2.Error().With(ctx).Str("get target branch failed").Error(err).Emit()
		return false, bits_err.GATEKEEPERCHECKCODEMERGE.ErrGetMRBranch
	}
	if sourceBranch.Commit == nil || targetBranch.Commit == nil {
		log.V2.Error().With(ctx).Str("get source or target last commit failed").Emit()
		return false, errors.New("get source or target last commit failed")
	}
	files, err := c.gitSvc.GetRepoDiffByCommit(ctx, codeChangeMeta.RepoID, targetBranch.Commit.GetId(), sourceBranch.Commit.GetId())
	if err != nil {
		log.V2.Error().With(ctx).Str("get repo diff failed").Error(err).Emit()
		return false, err
	}
	for _, pathInfo := range projectPathInfos {
		for _, file := range files {
			if strings.HasPrefix(file.FromPath, pathInfo.SubPath) || strings.HasPrefix(file.ToPath, pathInfo.SubPath) || strings.HasPrefix(file.Path, pathInfo.SubPath) {
				log.V2.Info().With(ctx).Str("match mono sub path").KVs("diff_file", file, "path_info", pathInfo).Emit()
				return true, nil
			}
			for _, dependencyPath := range pathInfo.DependencyPaths {
				if strings.HasPrefix(file.FromPath, dependencyPath.Path) || strings.HasPrefix(file.ToPath, dependencyPath.Path) || strings.HasPrefix(file.Path, dependencyPath.Path) {
					log.V2.Info().With(ctx).Str("match mono dep path").KVs("diff_file", file, "dep_path_info", dependencyPath, "path_info", pathInfo).Emit()
					return true, nil
				}
			}
		}
	}
	return false, nil
}

func (c *CodeCanMergeCheck) Execute(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	resp, err := c.executeCodeMergeCheck(ctx, req)
	if err != nil {
		log.V2.Warn().With(ctx).Str("trigger failed, return running for next ping").KVs("check_id", req.CheckID).Emit()
		return &protocol.ExecutionResp{
			CheckID: req.CheckID,
			Status:  gk.CheckStatus_CheckStatusRunning,
		}, nil
	}
	return resp, nil
}

func (c *CodeCanMergeCheck) Ping(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	return c.executeCodeMergeCheck(ctx, req)
}

func (c *CodeCanMergeCheck) ForceCheck(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	return c.executeCodeMergeCheck(ctx, req)
}

func (c *CodeCanMergeCheck) Skip(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	return new(protocol.ExecutionResp), fmt.Errorf("not implement")
}

func (c *CodeCanMergeCheck) Remove(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	return new(protocol.ExecutionResp), nil
}

func (c *CodeCanMergeCheck) Operate(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	return new(protocol.ExecutionResp), nil
}

func (c *CodeCanMergeCheck) Terminate(ctx context.Context, req *protocol.ExecutionReq) (resp *protocol.ExecutionResp, err error) {
	return new(protocol.ExecutionResp), nil
}
