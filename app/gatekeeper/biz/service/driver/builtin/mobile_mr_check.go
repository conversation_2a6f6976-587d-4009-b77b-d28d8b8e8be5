package builtin

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/model/protocol"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/biz/thirdparty"
	gk "code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bits/gatekeeper/process"
	"code.byted.org/devinfra/hagrid/app/gatekeeper/kitex_gen/bytedance/bits/gatekeeper_mobile"
	"code.byted.org/gopkg/logs/v2/log"
)

type MobileMrCheck struct {
	checkType           gk.CheckItemType
	mobileGatekeeperSvc thirdparty.MobileGatekeeperSvc
}

func NewMobileMrCheck(checkType gk.CheckItemType) *MobileMrCheck {
	return &MobileMrCheck{
		checkType:           checkType,
		mobileGatekeeperSvc: thirdparty.NewMobileGatekeeperRpc(),
	}
}

func (m *MobileMrCheck) Execute(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	resp := &protocol.ExecutionResp{
		CheckID: req.CheckID,
		Status:  gk.CheckStatus_CheckStatusRunning,
	}
	checkResp, err := m.mobileGatekeeperSvc.ExecuteCheck(ctx, &gatekeeper_mobile.GatekeeperMobileCheckCommonRequest{
		BizID:     int64(req.ProcessContext.BizID),
		BizScene:  gk.BizScene_BizSceneMobileMRProcess,
		BizStage:  req.ProcessContext.Stage,
		CheckID:   int64(req.CheckID),
		CheckType: m.checkType,
		SpaceID:   req.SpaceID,
		Operator:  req.Operator,
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("execute mobile mr check failed").Error(err).Emit()
		return nil, err
	}
	resp.Status = checkResp.Status
	resp.Message = checkResp.Message
	resp.MessageI18N = checkResp.MessageI18N
	return resp, nil
}

func (m *MobileMrCheck) Ping(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	resp := &protocol.ExecutionResp{
		CheckID: req.CheckID,
		Status:  gk.CheckStatus_CheckStatusRunning,
	}
	checkResp, err := m.mobileGatekeeperSvc.QueryCheck(ctx, &gatekeeper_mobile.GatekeeperMobileCheckCommonRequest{
		BizID:     int64(req.ProcessContext.BizID),
		BizScene:  gk.BizScene_BizSceneMobileMRProcess,
		BizStage:  req.ProcessContext.Stage,
		CheckID:   int64(req.CheckID),
		CheckType: m.checkType,
		SpaceID:   req.SpaceID,
		Operator:  req.Operator,
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("execute mobile mr check failed").Error(err).Emit()
		return nil, err
	}
	resp.Message = checkResp.Message
	resp.MessageI18N = checkResp.MessageI18N
	resp.Status = checkResp.Status
	return resp, nil
}

func (m *MobileMrCheck) Skip(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	resp := &protocol.ExecutionResp{
		CheckID: req.CheckID,
		Status:  gk.CheckStatus_CheckStatusRunning,
	}
	checkResp, err := m.mobileGatekeeperSvc.SkipCheck(ctx, &gatekeeper_mobile.GatekeeperMobileCheckCommonRequest{
		BizID:     int64(req.ProcessContext.BizID),
		BizScene:  gk.BizScene_BizSceneMobileMRProcess,
		BizStage:  req.ProcessContext.Stage,
		CheckID:   int64(req.CheckID),
		CheckType: m.checkType,
		SpaceID:   req.SpaceID,
		Operator:  req.Operator,
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("execute mobile mr check failed").Error(err).Emit()
		return nil, err
	}
	resp.Message = checkResp.Message
	resp.MessageI18N = checkResp.MessageI18N
	resp.Status = checkResp.Status
	return resp, nil
}

func (m *MobileMrCheck) Remove(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	return &protocol.ExecutionResp{}, nil
}

func (m *MobileMrCheck) Operate(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	return &protocol.ExecutionResp{}, nil
}

func (m *MobileMrCheck) ForceCheck(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	return &protocol.ExecutionResp{}, nil
}

func (m *MobileMrCheck) Terminate(ctx context.Context, req *protocol.ExecutionReq) (*protocol.ExecutionResp, error) {
	resp := &protocol.ExecutionResp{
		CheckID: req.CheckID,
		Status:  gk.CheckStatus_CheckStatusRunning,
	}
	checkResp, err := m.mobileGatekeeperSvc.TerminateCheck(ctx, &gatekeeper_mobile.GatekeeperMobileCheckCommonRequest{
		BizID:     int64(req.ProcessContext.BizID),
		BizScene:  gk.BizScene_BizSceneMobileMRProcess,
		BizStage:  req.ProcessContext.Stage,
		CheckID:   int64(req.CheckID),
		CheckType: m.checkType,
		SpaceID:   req.SpaceID,
		Operator:  req.Operator,
	})
	if err != nil {
		log.V2.Error().With(ctx).Str("execute mobile mr check failed").Error(err).Emit()
		return nil, err
	}
	resp.Status = checkResp.Status
	resp.Message = checkResp.Message
	resp.MessageI18N = checkResp.MessageI18N
	return resp, nil
}
