package biz_handler

import (
	"context"
	"strings"
	"testing"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestSearch(t *testing.T) {
	ctx := context.Background()
	mockey.PatchConvey("case 1", t, func() {
		//    d2 - d3
		//  /        \
		// d1  - g -  d4
		parser := newDeployPipelineParser(ctx, pplStage1)
		pre := parser.searchJob(ctx, false, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		suc := parser.searchJob(ctx, true, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		parallel := parser.searchJob(ctx, true, []string{pre}, []string{suc}, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		convey.So(pre, convey.ShouldEqual, "d1")
		convey.So(suc, convey.ShouldEqual, "d4")
		convey.So(parallel, convey.ShouldEqual, "d2")
	})
	mockey.PatchConvey("case 2", t, func() {
		//    d2 - d3
		//  /
		// d1  - g
		parser := newDeployPipelineParser(ctx, pplStage2)
		pre := parser.searchJob(ctx, false, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		suc := parser.searchJob(ctx, true, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		parallel := parser.searchJob(ctx, true, []string{pre}, []string{}, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		convey.So(pre, convey.ShouldEqual, "d1")
		convey.So(suc, convey.ShouldEqual, "")
		convey.So(parallel, convey.ShouldEqual, "d2")
	})
	mockey.PatchConvey("case 3", t, func() {
		//    d2 - d3
		//  /        \
		// a  - g -  d4
		parser := newDeployPipelineParser(ctx, pplStage3)
		pre := parser.searchJob(ctx, false, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		suc := parser.searchJob(ctx, true, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		parallel := parser.searchJob(ctx, true, []string{}, []string{suc}, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		convey.So(pre, convey.ShouldEqual, "")
		convey.So(suc, convey.ShouldEqual, "d4")
		convey.So(parallel, convey.ShouldEqual, "d2")
	})
	mockey.PatchConvey("case 4", t, func() {
		//        d2
		//    /        \
		//  /            \
		// d1  - g -  d3 - a
		parser := newDeployPipelineParser(ctx, pplStage4)
		pre := parser.searchJob(ctx, false, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		suc := parser.searchJob(ctx, true, []string{"g"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		parallel := parser.searchJob(ctx, true, []string{pre}, []string{suc}, func(job string) bool {
			return strings.HasPrefix(job, "d")
		})
		convey.So(pre, convey.ShouldEqual, "d1")
		convey.So(suc, convey.ShouldEqual, "d3")
		convey.So(parallel, convey.ShouldEqual, "")
	})
}

func TestSearchMultiStages(t *testing.T) {
	ctx := context.Background()
	mockey.PatchConvey("case 1", t, func() {
		//   stage1           stage2             stage3
		// - x1 - a1 - - - g1 - - - - - - -   - g2 - - - -
		//        	   |                  |	  |           |
		//             - - x2 - b1 - b2 - - - - x3 - c1 - - - x4
		stageCase1 := &pipelinepb.Stages{
			Stages: []*dslpb.Stage{
				{
					Jobs: []*dslpb.Job{
						{
							Id:        "x1",
							DependsOn: []string{},
						},
						{
							Id:        "a1",
							DependsOn: []string{"x1"},
						},
					},
				},
				{
					Jobs: []*dslpb.Job{
						{
							Id:        "g1",
							DependsOn: []string{"a1"},
						},
						{
							Id:        "x2",
							DependsOn: []string{"a1"},
						},
						{
							Id:        "b1",
							DependsOn: []string{"x2"},
						},
						{
							Id:        "b2",
							DependsOn: []string{"b1"},
						},
					},
				},
				{
					Jobs: []*dslpb.Job{
						{
							Id:        "g2",
							DependsOn: []string{"g1", "b2"},
						},
						{
							Id:        "x3",
							DependsOn: []string{"g1", "b2"},
						},
						{
							Id:        "c1",
							DependsOn: []string{"x3"},
						},
					},
				},
				{
					Jobs: []*dslpb.Job{
						{
							Id:        "x4",
							DependsOn: []string{"g2", "c1"},
						},
					},
				},
			},
		}
		parser := newDeployPipelineParser(ctx, stageCase1)
		pre := parser.searchJob(ctx, false, []string{"g2"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "x")
		})
		suc := parser.searchJob(ctx, true, []string{"g2"}, nil, func(job string) bool {
			return strings.HasPrefix(job, "x")
		})
		parallel := parser.searchJob(ctx, true, []string{pre}, []string{suc}, func(job string) bool {
			return strings.HasPrefix(job, "x")
		})
		convey.So(pre, convey.ShouldEqual, "x2")
		convey.So(suc, convey.ShouldEqual, "x4")
		convey.So(parallel, convey.ShouldEqual, "x3")
	})
}

var pplStage1 = &pipelinepb.Stages{
	Stages: []*dslpb.Stage{
		{
			Jobs: []*dslpb.Job{
				{
					Id:        "d1",
					DependsOn: []string{},
				},
				{
					Id:        "d2",
					DependsOn: []string{"d1"},
				},
				{
					Id:        "d3",
					DependsOn: []string{"d2"},
				},
				{
					Id:        "g",
					DependsOn: []string{"d1"},
				},
				{
					Id:        "d4",
					DependsOn: []string{"g", "d3"},
				},
			},
		},
	},
}

var pplStage2 = &pipelinepb.Stages{
	Stages: []*dslpb.Stage{
		{
			Jobs: []*dslpb.Job{
				{
					Id:        "d1",
					DependsOn: []string{},
				},
				{
					Id:        "d2",
					DependsOn: []string{"d1"},
				},
				{
					Id:        "d3",
					DependsOn: []string{"d2"},
				},
				{
					Id:        "g",
					DependsOn: []string{"d1"},
				},
			},
		},
	},
}

var pplStage3 = &pipelinepb.Stages{
	Stages: []*dslpb.Stage{
		{
			Jobs: []*dslpb.Job{
				{
					Id:        "a",
					DependsOn: []string{},
				},
				{
					Id:        "d2",
					DependsOn: []string{"a"},
				},
				{
					Id:        "d3",
					DependsOn: []string{"d2"},
				},
				{
					Id:        "g",
					DependsOn: []string{"a"},
				},
				{
					Id:        "d4",
					DependsOn: []string{"g", "d3"},
				},
			},
		},
	},
}

var pplStage4 = &pipelinepb.Stages{
	Stages: []*dslpb.Stage{
		{
			Jobs: []*dslpb.Job{
				{
					Id:        "d1",
					DependsOn: []string{},
				},
				{
					Id:        "d2",
					DependsOn: []string{"d1"},
				},
				{
					Id:        "g",
					DependsOn: []string{"d1"},
				},
				{
					Id:        "d3",
					DependsOn: []string{"g"},
				},
				{
					Id:        "a",
					DependsOn: []string{"d3", "d2"},
				},
			},
		},
	},
}
