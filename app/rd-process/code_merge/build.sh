#!/usr/bin/env bash
RUN_NAME="bits.dev.code_merge"

if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
  bash ${BUILD_PATH}/scripts/pre-build.scm.sh
fi

mkdir -p output/bin output/conf
cp script/* output/
cp conf/* output/conf/
chmod +x output/bootstrap.sh

if [ "$IS_SYSTEM_TEST_ENV" != "1" ]; then
    go build -o output/bin/${RUN_NAME}
else
    go test -c -covermode=set -o output/bin/${RUN_NAME} -coverpkg=./...
fi

if [[ $OLD_PWD != "" ]]; then
  cp -r output $OLD_PWD
fi

bash -c "$(curl -fsSL https://tosv.byted.org/obj/uitesting/tos_upload_blame.sh)" || echo "failed"
