package rpc

import (
	"context"
	"fmt"
	json "github.com/bytedance/sonic"

	"code.byted.org/devinfra/hagrid/app/rd-process/code_review_model/biz/self_common"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bits_devops_metrics_data_collect/kitex_gen/bits/devops/metrics_data_collect"
	"code.byted.org/overpass/bits_devops_metrics_data_collect/rpc/bits_devops_metrics_data_collect"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
)

const (
	EventType_CR_SERVER_STABILITY_RESP = 102500
)

type CodeReviewServiceStabilityEvent struct {
	GroupName string `json:"group_name"`
	EventType int64  `json:"event_type"`
	EventTime int64  `json:"event_time"` // 事件发生时间，会给予此分区
	EventId   string `json:"event_id"`   // log_id

	PSM        string `json:"psm"`
	Method     string `json:"method"`
	CostTimeMs int64  `json:"cost_time_ms"`
	HostMrId   int64  `json:"host_mr_id"`

	Error string `json:"error"`
}

func SinkServiceStabilityEvent(ctx context.Context, serviceStabilityEvent CodeReviewServiceStabilityEvent, err error) {
	if err != nil && err.Error() != "implement me" {
		serviceStabilityEvent.Error = err.Error()
	}
	serviceStabilityEvent.EventTime = self_common.Now().Unix()
	serviceStabilityEvent.EventId = ctxvalues.LogIDDefault(ctx)
	serviceStabilityEvent.PSM = env.PSM()
	eventBytes, err := json.Marshal(serviceStabilityEvent)
	if err != nil {
		logs.CtxError(ctx, err.Error())
	} else {
		SinkEvent(ctx, string(eventBytes))
	}
}

func SinkEvent(ctx context.Context, eventJson string) {
	utils.SafeGo(ctx, func() {
		gCtx := utils.CopyKiteXContext(ctx)

		data := &metrics_data_collect.MetricsV2{
			Event: eventJson,
		}
		//产品线：目前有CI,CD,AnywhereDoor,BuildCloud四个产品线，会将不同产品线数据打到不同的队列中，如果上报的数据不属于这四个产品线请联系@xiaozan进行添加
		req := metrics_data_collect.NewSinkV2Requset()
		req.SetMetrics(data)
		req.SetProduct(metrics_data_collect.ProductPtr(metrics_data_collect.Product_CI))
		_, err := bits_devops_metrics_data_collect.RawCall.SinkV2(gCtx, req)
		if err != nil {
			logs.CtxError(gCtx, fmt.Sprintf("SinkV2 error:%s eventJson: %s", err, eventJson))
			return
		}
	})
}

/*
@杨承志 希望是rpc方式相比较消息队列，所以就需要起一个协程
*/
var metricProductId = int64(metrics_data_collect.Topic_CodeReview)

func MetricEventsSink(events []*metrics_data_collect.Event) {
	ctx := context.Background() // 中断log_id，避免平时查日志看到
	if len(events) != 0 {
		utils.SafeGo(ctx, func() {
			_, _ = bits_devops_metrics_data_collect.Sink(ctx, &metrics_data_collect.Metrics{
				CommonParam: &metrics_data_collect.CommonParam{ProductId: &metricProductId},
				Events:      events,
			})
		})
	}
}

func MetricEventSink(t metrics_data_collect.EventType, param map[string]interface{}) {
	if utils.IsMac() {
		return
	}
	MetricEventsSink([]*metrics_data_collect.Event{
		{
			Type:      t,
			EventTime: self_common.Now().Unix(),
			Params:    utils.ToJson(param),
		},
	})
}
