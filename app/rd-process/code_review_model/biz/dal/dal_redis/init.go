package dal_redis

import (
	"context"
	"errors"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/kv/goredis"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
)

var Bits *goredis.Client

func Init() {
	Bits = utils.RedisInitByPSM("toutiao.redis.bits_cache")
}

func WaitUntilGetNxLock(ctx context.Context, client *goredis.Client, key string, value interface{}, expirationTime time.Duration) (err error) {
	if client == nil {
		err = errors.New("client == nil")
		logs.CtxError(ctx, err.Error())
		return
	}

	const (
		LogTime   = time.Second * 2
		SleepTime = time.Microsecond * 100
	)
	var runCount = 0
	var redisErrCount = 0
	for {
		runCount += 1
		getLock, redisErr := client.SetNX(key, value, expirationTime).Result()
		if redisErr != nil {
			logs.CtxError(ctx, redisErr.Error())
			redisErrCount += 1
		}
		if redisErrCount >= 2 {
			return redisErr
		}
		if getLock {
			return nil
		}
		if runCount%int(LogTime/SleepTime) == 0 {
			logs.CtxInfo(ctx, "wait for tree write lock %d", runCount/int(LogTime/SleepTime))
		}
		time.Sleep(SleepTime)
	}
}
