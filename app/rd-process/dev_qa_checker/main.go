package main

import (
	"context"
	"regexp"
	"strconv"

	"code.byted.org/bytefaas/eventbus"
	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/bytefaas/faas-go/functioncontext"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/mtctx"
	"code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/meego"
	eb "code.byted.org/eventbus/client-go"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bytedance_bits_dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/overpass/bytedance_bits_dev/rpc/bytedance_bits_dev"
	json "github.com/bytedance/sonic"
)

type devTaskEvent struct {
	DevBasicId *int    `json:"dev_basic_id"`
	Type       *string `json:"type"`
	SpaceId    *int64  `json:"space_id"`
	GroupName  *string `json:"group_name"`
	Username   *string `json:"username"`
	Timestamp  *int    `json:"timestamp"`
	Name       *string `json:"name"`
	Data       *string `json:"data"`
	Action     *string `json:"action"`
}

type roleStruct struct {
	Role   string   `json:"role"`
	Owners []string `json:"owners"`
}

var spaceList = []int64{97864716290, 39151417858, 88994312194, 4084890370}

// "https://meego.feishu.cn/meego/bitsdevops/story/detail/15215258"
var workItemRegexp = regexp.MustCompile(`(https://meego.feishu.cn/)(meego/)?(.*)/(.*)(/detail/)(.*)`)

func GetWorkItemQAs(ctx context.Context, devBasicID int64) ([]string, error) {
	qaList := make([]string, 0)
	log.V2.Info().With(ctx).KV("devBasicID", devBasicID).Emit()
	devBasicInfo, err := bytedance_bits_dev.GetDevTaskBasicInfo(ctx, devBasicID)
	if err != nil {
		log.V2.Error().With(ctx).Str("failed to get dev basic info").Error(err).KV("dev basic id", devBasicID).Emit()
		return nil, err
	}
	creator := devBasicInfo.BasicInfo.Creator
	ctx = mtctx.WithTenancy(ctx, creator)
	workItems, err := bytedance_bits_dev.GetDevTaskRelatedInfo(ctx, devBasicID)
	if err != nil {
		return nil, err
	}
	log.V2.Info().With(ctx).KV("work_item", workItems).Emit()
	client := meego.NewClient()
	qaEmailList := make([]string, 0)
	for _, item := range workItems.WorkItems {
		subStrings := workItemRegexp.FindStringSubmatch(item.Url)
		if len(subStrings) != 7 {
			continue
		}
		space := subStrings[3]
		itemType := subStrings[4]
		itemID := subStrings[6]
		id, _ := strconv.Atoi(itemID)
		itemInfo, err := client.QueryWorkItemByIdByEmail(ctx, emails.WithSuffix(creator), space, itemType, int64(id)).Get()
		if err != nil {
			return qaList, err
		}
		for _, field := range itemInfo.Fields {
			if field.FieldKey == "role_owners" {
				str, _ := json.Marshal(field.FieldValue)
				roles := make([]*roleStruct, 0)
				err := json.Unmarshal(str, &roles)
				if err != nil {
					return qaEmailList, err
				}
				for _, role := range roles {
					if role.Role == "QA" {
						qaList = append(qaList, role.Owners...)
					}
				}
			}
		}
	}

	if len(qaList) == 0 {
		return qaEmailList, nil
	}
	userInfo, err := client.QueryUsers(ctx, &meego.QueryUsersRequest{
		UserKeys: qaList,
	})
	if err != nil {
		return qaEmailList, err
	}
	for _, user := range userInfo {
		qaEmailList = append(qaEmailList, user.Email)
	}
	return qaEmailList, nil
}

func EBHandler(ctx context.Context, event *eb.ConsumerEvent) error {
	// single event processing
	//return nil
	if string(event.GetKey()) == "dev_task" {
		body := &devTaskEvent{}
		err := json.Unmarshal(event.GetValue(), body)
		if err != nil {
			logs.CtxError(ctx, "unmarshal event value error: %v", err)
			return err
		}
		if body.SpaceId != nil && body.Type != nil && body.DevBasicId != nil && body.Action != nil {
			if gslice.Contains(spaceList, *body.SpaceId) && *body.Type == "standard" && *body.Action == "create" {
				qaEmailList, err := GetWorkItemQAs(ctx, int64(*body.DevBasicId))
				if err != nil {
					logs.CtxError(ctx, "get work item qa list error: %v", err)
					return err
				}
				if len(qaEmailList) == 0 {
					return nil
				}
				basic, err := bytedance_bits_dev.GetDevTaskBasicInfo(ctx, int64(*body.DevBasicId))
				if err != nil {
					logs.CtxError(ctx, "get dev task basic info error: %v", err)
					return err
				}
				roles := basic.Members
				for _, email := range qaEmailList {
					roles = append(roles, &dev.DevTaskMember{Role: dev.DevTaskMemberRole_TESTER, Email: email})
				}
				_, err = bytedance_bits_dev.RawCall.UpdateDevTask(ctx, &dev.UpdateDevTaskRequest{
					DevBasicId: int64(*body.DevBasicId),
					Members:    roles,
				})
				if err != nil {
					logs.CtxError(ctx, "update dev task error: %v", err)
					return err
				}
			}
		}
	}
	return nil
}

func EBBatchHandler(events eventbus.Events) {
	// batch events processing
	// will call this function when consumer is set "batch consume"
	logs.Info("receive batch events, len: %v", len(events))
	for _, e := range events {
		logs.CtxInfo(e.Context, "receive event, id: %v, event: %v", e.ID(), e)
		// handle one event
		// ...
		// if consumed fail, please mark this event error as follow, otherwise do not do anything
		// e.MarkError(errors.New("sample"))
	}
}

func CommonHandler(ctx context.Context, ce interface{}) (response *events.EventResponse, err error) {
	// non eventbus handler, can be used to handle other triggers such as http, timer or other mq
	switch r := ce.(type) {
	case *events.HTTPRequest:
		// http trigger
		logs.CtxInfo(ctx, "request ID: %v. function ID: %v, headers: %v \n", functioncontext.GetRequestID(ctx), functioncontext.FunctionID, r.Headers)
		body, _ := json.Marshal(map[string]string{"message": "Hello world!"})
		return &events.EventResponse{
			StatusCode: 0,
			Headers: map[string]string{
				"Content-Type": "application/json",
			},
			Body: body,
		}, nil

	default:
		// other case
		logs.CtxInfo(ctx, "orther cloud event")
	}
	return &events.EventResponse{Body: []byte("Hello world")}, nil
}

func main() {
	InitializeLogger()
	// no need to change
	// Start input params
	// 1. eventbus single event handler, cannot be nil
	// 2. eventbus batch event handler, cannot be nil
	// 3. option other handlers
	eventbus.Start(EBHandler, EBBatchHandler, eventbus.WithCommonHandler(CommonHandler))
}

func InitializeLogger() {
	log.SetDefaultLogger(
		logs.SetPSM(env.PSM()),
		logs.SetCallDepth(2),
		logs.SetFullPath(),
		logs.SetKVPosition(logs.AfterMsg),
		logs.SetDisplayEnvInfo(true),
		logs.SetDisplayFuncName(true),
	)
}
