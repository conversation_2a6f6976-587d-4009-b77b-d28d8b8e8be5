/**
 * @Date: 2022/9/4
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package checksuiterouter

import "code.byted.org/middleware/hertz/pkg/app"

func VerifyHandleCheckSuiteEventHeader(c *app.RequestContext) error {
	event := c.Request.Header.Get(CodebaseHeaderXCodebaseEvent.String())
	if event != CodebaseCheckEventChangeCheckSuite.String() {
		return ErrIgnored
	}
	return nil
}
