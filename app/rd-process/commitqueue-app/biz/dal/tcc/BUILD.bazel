load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "tcc",
    srcs = [
        "api.go",
        "client.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/commitqueue-app/biz/dal/tcc",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/connections/tcc",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_lang_gg//gresult",
    ],
)
