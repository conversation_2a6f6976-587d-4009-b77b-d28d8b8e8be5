package model

import (
	json "github.com/bytedance/sonic"
	"testing"

	. "github.com/bytedance/mockey"
)

func TestGetCheckPointInfoResponseDecoder(t *testing.T) {

	PatchConvey("Test ", t, func() {
		jsonString := `{
    "groupInfos": [
        {
            "title": "通用检测",
            "titleI18N": "GeneralDetection",
            "hostCheckInfos": [
                {
                    "checkID": 694337247695088,
                    "checkPointID": 571592501466322,
                    "status": "failed",
                    "message": "",
                    "name": "审批门禁管控",
                    "nameI18N": "Approval&Access",
                    "messageI18N": "",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/show_detail",
                    "oncall": "",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/retry_check",
                    "author": "liy.liyao",
                    "checkType": "CheckItemTypeTikTokAndroidApprovalAndAccess",
                    "isShow": true
                }
            ]
        },
        {
            "title": "M/T业务卡口",
            "titleI18N": "M/T BusinessCheck",
            "hostCheckInfos": [
                {
                    "name": "构建检测",
                    "nameI18N": "BuildCheck",
                    "checkType": "MT_BuildCheck",
                    "status": "failed",
                    "message": "",
                    "messageI18N": "",
                    "skippable": false,
                    "isShow": true
                },
                {
                    "name": "Lite检测",
                    "nameI18N": "LiteCheck",
                    "checkType": "MT_LiteCheck",
                    "status": "failed",
                    "message": "",
                    "messageI18N": "",
                    "skippable": false,
                    "isShow": true
                }
            ]
        }
    ]
}`
		res := GetCheckPointInfoResponse{}

		err := json.UnmarshalString(jsonString, &res)
		if err != nil {
			t.Error(err)
		}

	})

	PatchConvey("Test json2", t, func() {
		jsonString := `{
    "groupInfos": [
        {
            "title": "通用检测",
            "titleI18N": "GeneralDetection",
            "hostCheckInfos": [
                {
                    "checkID": 146392634977223,
                    "checkPointID": 701891977789677,
                    "status": "running",
                    "message": "",
                    "name": "CodeReview",
                    "nameI18N": "CodeReview",
                    "messageI18N": "",
                    "skippable": false,
                    "detail": "",
                    "oncall": "",
                    "retry": "",
                    "author": "",
                    "checkType": "CheckItemTypeMobileMrCodeReview",
                    "isShow": true
                },
                {
                    "checkID": 146392635042759,
                    "checkPointID": 701891977789677,
                    "status": "failed",
                    "message": "",
                    "name": "审批门禁管控",
                    "nameI18N": "Approval&Access",
                    "messageI18N": "",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/show_detail",
                    "oncall": "",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/retry_check",
                    "author": "liy.liyao",
                    "checkType": "CheckItemTypeTikTokAndroidApprovalAndAccess",
                    "isShow": true,
                    "renderType": "normal"
                },
                {
                    "name": "质量门禁管控",
                    "nameI18N": "QualityGateAccess",
                    "checkType": "CheckItemTypeTikTokQualityGateKeeper",
                    "status": "running",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "MF",
                    "skippable": false,
                    "oncall": "",
                    "isShow": true
                }
            ]
        },
        {
            "title": "Musically&TikTok",
            "titleI18N": "Musically&TikTok",
            "hostCheckInfos": [
                {
                    "name": "build:CICheck",
                    "nameI18N": "build:CICheck",
                    "checkType": "MT_BuildCheck",
                    "status": "success",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_BuildCheck",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_BuildCheck",
                    "oncall": "",
                    "isShow": false
                },
                {
                    "name": "build:CICheckBase",
                    "nameI18N": "build:CICheckBase",
                    "checkType": "MT_BuildCheck_Base",
                    "status": "success",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_BuildCheck_Base",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_BuildCheck_Base",
                    "oncall": "",
                    "isShow": false
                },
                {
                    "name": "check:FrameworkRules",
                    "nameI18N": "check:FrameworkRules",
                    "checkType": "MT_Framework_Rules",
                    "status": "failed",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_Framework_Rules",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_Framework_Rules",
                    "oncall": "",
                    "isShow": true
                },
                {
                    "name": "check:AutotestShoots",
                    "nameI18N": "check:AutotestShoots",
                    "checkType": "MT_AutoTest",
                    "status": "success",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_AutoTest",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_AutoTest",
                    "oncall": "",
                    "isShow": false
                },
                {
                    "name": "check:ZhongkuiAfter",
                    "nameI18N": "check:ZhongkuiAfter",
                    "checkType": "MT_ZhongKuiAfter",
                    "status": "failed",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_ZhongKuiAfter",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_ZhongKuiAfter",
                    "oncall": "",
                    "isShow": true
                }
            ]
        }
    ]
}`
		res := GetCheckPointInfoResponse{}

		err := json.UnmarshalString(jsonString, &res)
		if err != nil {
			t.Error(err)
		}
	})

	PatchConvey("test json3", t, func() {
		jsonString := `{
    "groupInfos": [
        {
            "title": "通用检测",
            "titleI18N": "GeneralDetection",
            "hostCheckInfos": [
                {
                    "checkID": 146392634977223,
                    "checkPointID": 701891977789677,
                    "status": "running",
                    "message": "",
                    "name": "CodeReview",
                    "nameI18N": "CodeReview",
                    "messageI18N": "",
                    "skippable": false,
                    "detail": "",
                    "oncall": "",
                    "retry": "",
                    "author": "",
                    "checkType": "CheckItemTypeMobileMrCodeReview",
                    "isShow": true,
                    "renderType": 0
                },
                {
                    "checkID": 146392635042759,
                    "checkPointID": 701891977789677,
                    "status": "failed",
                    "message": "",
                    "name": "审批门禁管控",
                    "nameI18N": "Approval&Access",
                    "messageI18N": "",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/show_detail",
                    "oncall": "",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/retry_check",
                    "author": "liy.liyao",
                    "checkType": "CheckItemTypeTikTokAndroidApprovalAndAccess",
                    "isShow": true,
                    "renderType": "normal"
                },
                {
                    "checkID": 146392634911687,
                    "checkPointID": 701891977789677,
                    "status": "running",
                    "message": "",
                    "name": "质量门禁管控",
                    "nameI18N": "QualityGateAccess",
                    "messageI18N": "",
                    "skippable": false,
                    "detail": "{\"remoteName\":\"qcss\",\"module\":\"./QcssReportMF\",\"scmPrefix\":\"onesite/resource/onesite_bytest_qcss\",\"sandbox\":true,\"localEntryPort\":3099,\"oncallType\":\"https://oncall.bytedance.net/chats/user/index?tenantId=7989&region=cn&step=self-solve&type=37453\"}",
                    "oncall": "",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/qualitygate/retry_check",
                    "author": "liy.liyao",
                    "checkType": "CheckItemTypeTikTokQualityGateKeeper",
                    "isShow": true,
                    "renderType": "MF"
                }
            ]
        },
        {
            "title": "Musically&TikTok",
            "titleI18N": "Musically&TikTok",
            "hostCheckInfos": [
                {
                    "name": "build:CICheck",
                    "nameI18N": "build:CICheck",
                    "checkType": "MT_BuildCheck",
                    "status": "success",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_BuildCheck",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_BuildCheck",
                    "oncall": "",
                    "isShow": false
                },
                {
                    "name": "build:CICheckBase",
                    "nameI18N": "build:CICheckBase",
                    "checkType": "MT_BuildCheck_Base",
                    "status": "success",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_BuildCheck_Base",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_BuildCheck_Base",
                    "oncall": "",
                    "isShow": false
                },
                {
                    "name": "check:FrameworkRules",
                    "nameI18N": "check:FrameworkRules",
                    "checkType": "MT_Framework_Rules",
                    "status": "failed",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_Framework_Rules",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_Framework_Rules",
                    "oncall": "",
                    "isShow": true
                },
                {
                    "name": "check:AutotestShoots",
                    "nameI18N": "check:AutotestShoots",
                    "checkType": "MT_AutoTest",
                    "status": "success",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_AutoTest",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_AutoTest",
                    "oncall": "",
                    "isShow": false
                },
                {
                    "name": "check:ZhongkuiAfter",
                    "nameI18N": "check:ZhongkuiAfter",
                    "checkType": "MT_ZhongKuiAfter",
                    "status": "failed",
                    "message": "",
                    "messageI18N": "",
                    "renderType": "normal",
                    "skippable": false,
                    "detail": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/show_detail?mr_id=7288224&check_type=MT_ZhongKuiAfter",
                    "retry": "https://vgh8ajrs.fn.bytedance.net/api/approval/checkpoint/retry_check?mr_id=7288224&check_type=MT_ZhongKuiAfter",
                    "oncall": "",
                    "isShow": true
                }
            ]
        }
    ]
}`
		res := GetCheckPointInfoResponse{}
		_ = json.UnmarshalString(jsonString, &res)

	})
}
