load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "card",
    srcs = ["card_notify.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/aosp/biz/service/card",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/common_lib/model",
        "//libs/notification-client/lark",
        "@com_github_larksuite_botframework_go//SDK/message",
        "@com_github_larksuite_botframework_go//SDK/protocol",
    ],
)
