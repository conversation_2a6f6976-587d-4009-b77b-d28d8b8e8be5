package views

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/observers"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/permission"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/pyoptimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/version"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/callback/pkg"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/util"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/util/tools"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/gopkg/logs"
	"context"
	"fmt"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/lark-kit/bot"
	"strings"
)

type NewBranchHandler struct {
	pkg.BaseHandler
}

const (
	patch  = 1
	hotfix = 2
	branch = 3
)

func (n *NewBranchHandler) match(ackCtx *pkg.Context) int {
	lowerText := ackCtx.LowerText
	questionType := tools.GetQuestionType(lowerText)
	// patch 分支创建
	if questionType == tools.QuestionTypeNone && (tools.IsContainsAll(lowerText, []string{"create", "patch"}) || tools.IsContainsAll(lowerText, []string{"创建", "patch"})) {
		return patch
	}

	// hotfix分支创建
	if questionType == tools.QuestionTypeNone && tools.IsContainsAll(lowerText, []string{"create", "hotfix"}) {
		return hotfix
	}

	// 批量创建新的分支
	if questionType == tools.QuestionTypeNone && tools.IsContainsAll(lowerText, []string{"create", "branch", "from", "to"}) {
		return branch
	}
	return 0
}

func (n *NewBranchHandler) Do(ctx context.Context, ackCtx *pkg.Context) pkg.ChainStatus {
	switch n.match(ackCtx) {
	case patch:
		CreatePatchBranchForGroup(ctx, ackCtx)
	case hotfix:
		CreateBranchForGroup(ctx, ackCtx, hotfix)
	case branch:
		CreateBranchForGroup(ctx, ackCtx, branch)
	default:
		return pkg.ChainStatusContinue
	}
	return pkg.ChainStatusStopped
}

func CreatePatchBranchForGroup(ctx context.Context, ackCtx *pkg.Context) {
	if len(ackCtx.GroupName) == 0 {
		_, _ = ackCtx.GetBot(ctx).Must().SendTextToChat(ctx, ackCtx.OpenChatId, "Please enter content just like: (Aweme-iOS) create patch 8.1.0 #Aweme# #AWEMain#")
		return
	}

	text := ackCtx.Text
	source, _ := tools.ExtractVersionOrBranch(&text)

	var projects []string
	for {
		project := tools.ExtractFirstBusinessLikeText(&text)
		if len(project) == 0 {
			break
		}
		projects = append(projects, project)
	}

	if len(projects) == 0 {
		_, _ = ackCtx.GetBot(ctx).Must().SendTextToChat(ctx, ackCtx.OpenChatId, "Please enter projects which need to create patch branch, for example: (Aweme-iOS) create patch 8.1.0 #Aweme# #AWEMain#")
		return
	}

	req := DoPatchCreationRequest{
		GroupName: ackCtx.GroupName,
		Projects:  projects,
		Source:    source,
	}

	btnConfirm := pkg.NewButtonBuilder(ackCtx, observers.BtnNewPatchBranchConfirm).SetValidUsers(ackCtx.Username).SetReq(req).Build()
	btnCancel := pkg.NewButtonBuilder(ackCtx, observers.BtnCommonCancel).SetValidUsers(ackCtx.Username).Build()
	ackCtx.Response = pkg.NewResponse(category, "new.branch.patch.confirm").
		SetParam("projects", utils.JoinByComma(projects)).
		SetParam("version", source).
		SetBtnVal("confirm_val", btnConfirm).
		SetBtnVal("cancel_val", btnCancel)
	return
}

func DoPatchCreation(ctx context.Context, parser *bot.ValueParser) {
	var req DoPatchCreationRequest
	err := pkg.GetCustomReqFromParser(parser, &req)
	if err != nil {
		logs.CtxError(ctx, "get DoPatchCreationRequest err, %v", err)
		return
	}

	if req.GroupName != "Aweme-iOS" {
		pkg.NewSimpleResponse(fmt.Sprintf("not support for %s", req.GroupName)).SendWithParser(ctx, parser)
		return
	}

	if !tools.IsVersion(req.Source) {
		pkg.NewResponse(category, "new.branch.patch.hint").Reply().SendWithParser(ctx, parser)
		return
	}

	pkg.NewResponse(category, "version.merge.wait").SendWithParser(ctx, parser)

	conyGroup, err := mysql.ConyRepoOperator.GetConyGroupByName(ctx, req.GroupName)
	if err != nil {
		return
	}
	gid := conyGroup.GroupId
	projects, err := mysql.ConyRepoOperator.ListAllProjectByGroupID(ctx, gid)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}

	var filteredProjects []mysql.ConyProject
	for _, item := range projects {
		if tools.InArray(item.ProjectName, req.Projects) {
			filteredProjects = append(filteredProjects, item)
		}
	}

	if len(filteredProjects) == 0 {
		pkg.NewResponse(category, "new.branch.patch.project.missing").Reply().SendWithParser(ctx, parser)
	}

	pid := int64(1142)
	body, err := rpc.GetFileContent(ctx, pid, fmt.Sprintf("douyin/release/%s", req.Source), AWESeerfilePath)
	if err != nil {
		content := fmt.Sprintf("Get Podfile.seer failed from douyin/release/%s ，please check Tag whether exist", req.Source)
		pkg.NewSimpleResponse(content).SendWithParser(ctx, parser)
		return
	}
	fileContent := string(body)

	var versions = make(map[string]string)
	lines := strings.Split(fileContent, "\n")
	for _, line := range lines {
		matches := tools.PregMatch(line, "-\\s*([A-Za-z0-9+]+)\\s*\\(([0-9a-zA-Z.-]+)")
		if len(matches) > 0 {
			versions[matches[1]] = matches[2]
		}
	}

	var warnings []string
	patchNewBranch := fmt.Sprintf("douyin/patch/%s", req.Source)
	releaseBranch := fmt.Sprintf("douyin/release/%s", req.Source)
	patchBaseBranch := fmt.Sprintf("douyin/patch-base/%s", req.Source)

	_, err = rpc.BitsGitServer.CreateBranch(ctx, &git_server.CreateBranchRequest{
		ProjectID: 1142,
		Options: &git_server.CreateBranchOptions{
			Branch: &patchNewBranch,
			Ref:    &releaseBranch,
		},
		Base: nil,
	})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		warnings = append(warnings, fmt.Sprintf("create branch %s fail：%s", patchNewBranch, err.Error()))
	}

	_, err = rpc.BitsGitServer.CreateBranch(ctx, &git_server.CreateBranchRequest{
		ProjectID: 1142,
		Options: &git_server.CreateBranchOptions{
			Branch: &patchBaseBranch,
			Ref:    &releaseBranch,
		},
		Base: nil,
	})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		warnings = append(warnings, fmt.Sprintf("create branch %s fail：%s", patchBaseBranch, err.Error()))
	}

	for _, project := range filteredProjects {
		if _, ok := versions[project.ProjectName]; !ok {
			warnings = append(warnings, fmt.Sprintf("%s is not found in Podfile.seer", project.ProjectName))
			continue
		}

		_, err = rpc.BitsGitServer.CreateBranch(ctx, &git_server.CreateBranchRequest{
			ProjectID: int32(project.ProjectGitlabID),
			Options: &git_server.CreateBranchOptions{
				Branch: &patchNewBranch,
				Ref:    &releaseBranch,
			},
			Base: nil,
		})
		if err != nil {
			logs.CtxError(ctx, err.Error())
			warnings = append(warnings, fmt.Sprintf("create branch %s fail：%s", patchNewBranch, err.Error()))
		}

		_, err = rpc.BitsGitServer.CreateBranch(ctx, &git_server.CreateBranchRequest{
			ProjectID: int32(project.ProjectGitlabID),
			Options: &git_server.CreateBranchOptions{
				Branch: &patchBaseBranch,
				Ref:    &releaseBranch,
			},
			Base: nil,
		})
		if err != nil {
			logs.CtxError(ctx, err.Error())
			warnings = append(warnings, fmt.Sprintf("create branch %s fail：%s", patchBaseBranch, err.Error()))
		}
	}

	content := fmt.Sprintf("patch for %s success", req.Source)
	if len(warnings) > 0 {
		content += "\n\nNotice：\n" + strings.Join(warnings, "\n")
	}
	pkg.NewSimpleResponse(content).SendWithParser(ctx, parser)
}

func DoBranchCreation(ctx context.Context, parser *bot.ValueParser) {
	var req DoBranchCreationRequest
	err := pkg.GetCustomReqFromParser(parser, &req)
	if err != nil {
		logs.CtxError(ctx, "get DoBranchCreationRequest error, %v", err)
		return
	}

	resp, err := pyoptimus.Client.CreateBranchForGroup(ctx, &pyoptimus.CreateBranchForGroupRequest{
		GroupName: req.GroupName,
		From:      req.Source,
		To:        req.Target,
	})
	if err != nil {
		logs.CtxError(ctx, err.Error())
		pkg.NewSimpleResponse(util.GetBitsErrMsg(err.Error())).SendWithParser(ctx, parser)
		return
	}

	pkg.NewResponse(category, "new.branch.success").
		SetParam("branch", req.Target).
		SetParam("projects", strings.Join(resp.UnsuccessProjects, ",")).
		SendWithParser(ctx, parser)
}

func CreateBranchForGroup(ctx context.Context, ackCtx *pkg.Context, label int) {
	text := ackCtx.Text
	source, escapeSource := tools.ExtractVersionOrBranch(&text)
	target, escapeTarget := tools.ExtractVersionOrBranch(&text)
	if len(source) == 0 || len(target) == 0 {
		if label == hotfix {
			ackCtx.Response = pkg.NewResponse(category, "branch.hint.hotfix").Reply()
		} else {
			ackCtx.Response = pkg.NewResponse(category, "branch.hint.branch").Reply()
		}
		return
	}

	if len(ackCtx.GroupName) == 0 {
		ackCtx.Response = pkg.NewResponse(category, "branch.hint.bind").Reply()
		return
	}

	if !escapeSource {
		source = version.ConvertToRealBranch(ctx, source, ackCtx.GroupName)
	}
	if !escapeTarget {
		target = version.ConvertToRealBranch(ctx, target, ackCtx.GroupName)
	}

	if !permission.CanNewBranch(ctx, ackCtx.GroupName, target, ackCtx.Username) {
		ackCtx.Response = pkg.NewResponse(category, "branch.hint.permission").Reply()
		return
	}

	req := DoBranchCreationRequest{
		GroupName: ackCtx.GroupName,
		Source:    source,
		Target:    target,
		Label:     label,
	}
	btnConfirm := pkg.NewButtonBuilder(ackCtx, observers.BtnNewBranchConfirm).SetValidUsers(ackCtx.Username).SetReq(req).Build()
	btnCancel := pkg.NewButtonBuilder(ackCtx, observers.BtnCommonCancel).SetValidUsers(ackCtx.Username).Build()
	if label == hotfix {
		ackCtx.Response = pkg.NewResponse(category, "new.branch.hotfix.confirm").
			SetParam("group", ackCtx.GroupName).
			SetParam("source", source).
			SetParam("target", target).
			SetBtnVal("confirm_val", btnConfirm).
			SetBtnVal("cancel_val", btnCancel)
	} else {
		ackCtx.Response = pkg.NewResponse(category, "new.branch.confirm").
			SetParam("group", ackCtx.GroupName).
			SetParam("source", source).
			SetParam("target", target).
			SetBtnVal("confirm_val", btnConfirm).
			SetBtnVal("cancel_val", btnCancel)
	}
}

type DoPatchCreationRequest struct {
	GroupName string   `json:"group_name"`
	Projects  []string `json:"projects"`
	Source    string   `json:"source"`
}

type DoBranchCreationRequest struct {
	GroupName string `json:"group_name"`
	Source    string `json:"source"`
	Target    string `json:"target"`
	Label     int    `json:"label"`
}
