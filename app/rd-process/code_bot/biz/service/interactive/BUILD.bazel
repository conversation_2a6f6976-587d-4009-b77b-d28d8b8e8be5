load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "interactive",
    srcs = [
        "handler.go",
        "init.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/interactive",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/code_bot/biz/constdef",
        "//app/rd-process/code_bot/biz/pkg/bots",
        "//app/rd-process/code_bot/biz/pkg/notification",
        "//app/rd-process/code_bot/biz/pkg/observers",
        "//app/rd-process/code_bot/biz/pkg/tea",
        "//app/rd-process/code_bot/biz/service/interactive/public/command",
        "//app/rd-process/code_bot/biz/service/interactive/public/views",
        "//app/rd-process/code_bot/biz/util/tools",
        "//app/rd-process/code_bot/kitex_gen/bytedance/bits/code_bot",
        "//libs/compatibletenancy/emails",
        "//libs/lark-kit/bot",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
