load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "views",
    srcs = [
        "batch_version_merge.go",
        "bm_operation.go",
        "build_number.go",
        "chat_binding.go",
        "cony_auth.go",
        "git_operation.go",
        "group_notice.go",
        "handler.go",
        "new_branch_for_group.go",
        "new_fix_version.go",
        "notice_merge_code.go",
        "process_with_context.go",
        "query_bits_mr.go",
        "query_bm.go",
        "query_diff.go",
        "query_version.go",
        "repeat.go",
        "set_verison_code.go",
        "static_question.go",
        "trigger_cloudbuild_task.go",
        "util.go",
        "util_message.go",
        "version_merge.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/service/interactive/public/views",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/code_bot/biz/constdef",
        "//app/rd-process/code_bot/biz/dal/mysql",
        "//app/rd-process/code_bot/biz/dal/redis",
        "//app/rd-process/code_bot/biz/dal/tcc",
        "//app/rd-process/code_bot/biz/model",
        "//app/rd-process/code_bot/biz/model/mergemodel",
        "//app/rd-process/code_bot/biz/model/mqmodel",
        "//app/rd-process/code_bot/biz/pkg/bots",
        "//app/rd-process/code_bot/biz/pkg/messageBuilder",
        "//app/rd-process/code_bot/biz/pkg/metrics",
        "//app/rd-process/code_bot/biz/pkg/notification",
        "//app/rd-process/code_bot/biz/pkg/permission",
        "//app/rd-process/code_bot/biz/pkg/pyoptimus",
        "//app/rd-process/code_bot/biz/pkg/tea",
        "//app/rd-process/code_bot/biz/pkg/version",
        "//app/rd-process/code_bot/biz/rpc",
        "//app/rd-process/code_bot/biz/service/branchsync",
        "//app/rd-process/code_bot/biz/service/cron_task",
        "//app/rd-process/code_bot/biz/service/interactive/copywriting",
        "//app/rd-process/code_bot/biz/service/interactive/public/command",
        "//app/rd-process/code_bot/biz/util/tools",
        "//app/rd-process/code_bot/kitex_gen/bytedance/bits/code_bot",
        "//app/rd-process/code_bot/kitex_gen/bytedance/bits/code_frozen",
        "//app/rd-process/code_bot/kitex_gen/bytedance/bits/git_server",
        "//libs/common_lib/consts",
        "//libs/common_lib/utils",
        "//libs/compatibletenancy/emails",
        "//libs/lark-kit/bot",
        "//libs/lark-kit/model",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_facility//slice",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gconv",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//optional",
        "@org_byted_code_overpass_bytedance_bits_config_service//rpc/bytedance_bits_config_service",
        "@org_byted_code_overpass_bytedance_bits_meta//rpc/bytedance_bits_meta",
        "@org_byted_code_overpass_bytedance_bits_optimus//kitex_gen/bytedance/bits/optimus",
        "@org_byted_code_overpass_bytedance_bits_workflow//kitex_gen/bytedance/bits/workflow",
    ],
)

go_test(
    name = "views_test",
    srcs = [
        "batch_version_merge_test.go",
        "build_number_test.go",
        "util_message_test.go",
    ],
    embed = [":views"],
    deps = [
        "//app/rd-process/code_bot/biz/config",
        "//app/rd-process/code_bot/biz/dal/mysql",
        "//app/rd-process/code_bot/biz/dal/tcc",
        "//app/rd-process/code_bot/biz/util/tools",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_smartystreets_goconvey//convey",
        "@org_byted_code_overpass_bytedance_bits_config_service//kitex_gen/bytedance/bits/config_service",
        "@org_byted_code_overpass_bytedance_bits_config_service//rpc/bytedance_bits_config_service",
    ],
)
