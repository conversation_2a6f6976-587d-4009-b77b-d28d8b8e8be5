package messagetemplate

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/dal/mysql"
	"context"
)

type client struct {
	Category string
	BizKey   string
}

func (c *client) Insert(ctx context.Context, m MessageTemplate) error {
	record := toMysqlRecord(c.<PERSON>, c.Category, m)
	err := mysql.QualityWriteDB.Context(ctx).Save(record).Error
	return err
}

func (c *client) Edit(ctx context.Context, m MessageTemplate) error {
	where := &mysql.CodeBotMessageTemplate{
		Category: c.Category,
		BizKey:   c.<PERSON>,
	}

	var existRecord mysql.CodeBotMessageTemplate
	err := mysql.QualityWriteDB.Context(ctx).Where(where).First(&existRecord).Error
	if err != nil {
		return err
	}

	record := toMysqlRecord(c.<PERSON><PERSON>, c.Category, m)
	if len(record.Content) > 0 {
		existRecord.Content = record.Content
	}
	if len(record.MsgType) > 0 {
		existRecord.MsgType = record.MsgType
	}
	if len(record.Parameters) > 0 {
		existRecord.Parameters = record.Parameters
	}
	err = mysql.QualityWriteDB.Context(ctx).Save(&existRecord).Error
	return err
}
