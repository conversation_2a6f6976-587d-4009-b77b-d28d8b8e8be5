load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "common.go",
        "interactive.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/model",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/code_bot/kitex_gen/bytedance/bits/code_bot",
        "@org_byted_code_overpass_bytedance_bits_optimus//kitex_gen/bytedance/bits/optimus",
    ],
)
