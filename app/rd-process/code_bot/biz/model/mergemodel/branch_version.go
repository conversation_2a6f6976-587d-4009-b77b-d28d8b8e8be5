package mergemodel

import (
	"strconv"
	"strings"
)

type Branch []string

func ChangeToNumber(branch string) int {
	components := strings.Split(branch, ".")
	if len(components) < 2 {
		return 0
	}
	a, _ := strconv.Atoi(components[0])
	b, _ := strconv.Atoi(components[1])
	c := 0
	if len(components) == 3 {
		c, _ = strconv.Atoi(components[2])
	}

	return a*10000 + b*100 + c
}

func (branch Branch) Len() int      { return len(branch) }
func (branch Branch) Swap(i, j int) { branch[i], branch[j] = branch[j], branch[i] }
func (branch Branch) Less(i, j int) bool {
	return ChangeToNumber(branch[i]) < ChangeToNumber(branch[j])
}

func Less(ver1, ver2 string) bool {
	return ChangeToNumber(ver1) < ChangeToNumber(ver2)
}
