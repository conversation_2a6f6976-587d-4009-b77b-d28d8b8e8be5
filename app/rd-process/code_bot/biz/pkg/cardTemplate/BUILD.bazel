load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cardTemplate",
    srcs = ["bus.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/code_bot/biz/pkg/cardTemplate",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/code_bot/biz/dal/tcc",
        "//libs/common_lib/utils",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
