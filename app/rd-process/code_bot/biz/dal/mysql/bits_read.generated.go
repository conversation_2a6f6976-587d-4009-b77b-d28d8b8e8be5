// Code generated by COMMENTS_BUILD_TOOLS 2.0.73. DO NOT EDIT.
package mysql

import (
	"context"
	"database/sql"
	"errors"

	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

var GlobalErrBitsQualityReader = struct {
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
	NothingExecute          error
}{
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
	errors.New("NothingExecuteErr"),
}

func NewBitsQualityReader(handler *gorm.DB) BitsQualityReader {
	return &_BitsQualityReaderStruct{
		handler: handler,
	}
}

type _BitsQualityReaderStruct struct {
	handler *gorm.DB
}

func (interstruct *_BitsQualityReaderStruct) GetBusByGroupAndVersion(ctx context.Context, groupName string, version string) (CodeBotBus, error) {
	_result, _retErr := func() (CodeBotBus, error) {
		_sqlText := "select * from code_bot_bus where group_name=? and version_code=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, groupName, version)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CodeBotBus{}, _sdb.Error
		}
		var _ret CodeBotBus
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CodeBotBus{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetChatBindingByChatID(ctx context.Context, chatID string) (CodeBotChatBinding, error) {
	_result, _retErr := func() (CodeBotChatBinding, error) {
		_sqlText := "select * from code_bot_chat_binding where chat_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, chatID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CodeBotChatBinding{}, _sdb.Error
		}
		var _ret CodeBotChatBinding
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CodeBotChatBinding{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetMergeDetailByID(ctx context.Context, detailID int) (CodeBotMergeDetail, error) {
	_result, _retErr := func() (CodeBotMergeDetail, error) {
		_sqlText := "select * from code_bot_merge_detail where id =?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, detailID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CodeBotMergeDetail{}, _sdb.Error
		}
		var _ret CodeBotMergeDetail
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CodeBotMergeDetail{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetMergeDetailIDByMrID(ctx context.Context, mrID int) (int, error) {
	_result, _retErr := func() (int, error) {
		_sqlText := "select id from code_bot_merge_detail where mr_id =?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, mrID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetMergeProfileByID(ctx context.Context, ID int) (CodeBotMergeProfile, error) {
	_result, _retErr := func() (CodeBotMergeProfile, error) {
		_sqlText := "select * from code_bot_merge_profile where id =?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, ID)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CodeBotMergeProfile{}, _sdb.Error
		}
		var _ret CodeBotMergeProfile
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CodeBotMergeProfile{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetMergeRecordByGroupAndBranch(ctx context.Context, source string, target string, group string) (CodeBotMergeDetail, error) {
	_result, _retErr := func() (CodeBotMergeDetail, error) {
		_sqlText := "select * from code_bot_merge_detail where source = ? and target = ? and group_name = ? order by id desc limit 1"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, source, target, group)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CodeBotMergeDetail{}, _sdb.Error
		}
		var _ret CodeBotMergeDetail
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CodeBotMergeDetail{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetObserveFileById(ctx context.Context, id int) (CodeBotToolObserveFile, error) {
	_result, _retErr := func() (CodeBotToolObserveFile, error) {
		_sqlText := "select * from code_bot_tool_observe_file where id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return CodeBotToolObserveFile{}, _sdb.Error
		}
		var _ret CodeBotToolObserveFile
		_sdb = _sdb.Scan(&_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return CodeBotToolObserveFile{}, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetObserveFileID(ctx context.Context, projectID int64, filePath string, branch string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "select id from code_bot_tool_observe_file where project_id =? and file_path=? and branch_name = ?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectID, filePath, branch)
		if _sdb.Error != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		_row := _sdb.Row()
		if _row == nil {
			return 0, gorm.ErrRecordNotFound
		}
		var _ret int64
		_err := _row.Scan(&_ret)
		if _err != nil {
			if _err != gorm.ErrRecordNotFound && _err != sql.ErrNoRows {
				logs.CtxError(ctx, "Scan: %s, occur error: %s", _sqlText, _err.Error())
			}
			return 0, _err
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetObserveRelationByFidAndChatId(ctx context.Context, fid []int, chatId string) ([]CodeBotToolObserveRelation, error) {
	_result, _retErr := func() ([]CodeBotToolObserveRelation, error) {
		_sqlText := "select * from code_bot_tool_observe_relation where file_id in (?) and chat_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, fid, chatId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []CodeBotToolObserveRelation
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret CodeBotToolObserveRelation
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetObserveRelationByFidAndEmail(ctx context.Context, fid []int, email string) ([]CodeBotToolObserveRelation, error) {
	_result, _retErr := func() ([]CodeBotToolObserveRelation, error) {
		_sqlText := "select * from code_bot_tool_observe_relation where file_id in (?) and email=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, fid, email)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []CodeBotToolObserveRelation
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret CodeBotToolObserveRelation
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) GetObserversOfFile(ctx context.Context, fileId int64) ([]CodeBotToolObserveRelation, error) {
	_result, _retErr := func() ([]CodeBotToolObserveRelation, error) {
		_sqlText := "select * from code_bot_tool_observe_relation where file_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, fileId)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []CodeBotToolObserveRelation
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret CodeBotToolObserveRelation
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsQualityReaderStruct) ListAllObserveFileOfProject(ctx context.Context, projectID int) ([]CodeBotToolObserveFile, error) {
	_result, _retErr := func() ([]CodeBotToolObserveFile, error) {
		_sqlText := "select * from code_bot_tool_observe_file where project_id=?"
		_db := interstruct.handler.Context(ctx)
		_sdb := _db.Raw(_sqlText, projectID)
		if _sdb.Error != nil {
			_rows, _ := _sdb.Rows()
			if _rows != nil {
				_ = _rows.Close()
			}
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		_rows, _err := _sdb.Rows()
		defer func() {
			if _rows != nil {
				_ = _rows.Close()
			}
		}()
		if _err == gorm.ErrRecordNotFound || _err == sql.ErrNoRows {
			return nil, gorm.ErrRecordNotFound
		}
		if _err != nil {
			logs.CtxError(ctx, "execute: %s, occur error: %s", _sqlText, _err.Error())
			return nil, _err
		}
		if _rows == nil {
			return nil, gorm.ErrRecordNotFound
		}
		if _rows.Err() != nil {
			return nil, _rows.Err()
		}
		var _rets []CodeBotToolObserveFile
		var errRet error
		var hasError = false
		for _rows.Next() {
			var _ret CodeBotToolObserveFile
			_err = _db.ScanRows(_rows, &_ret)
			if _err != nil {
				hasError = true
				errRet = _err
			}
			_rets = append(_rets, _ret)
		}
		if hasError {
			return _rets, errRet
		}
		if len(_rets) <= 0 {
			return _rets, gorm.ErrRecordNotFound
		}
		return _rets, nil
	}()
	return _result, _retErr
}
