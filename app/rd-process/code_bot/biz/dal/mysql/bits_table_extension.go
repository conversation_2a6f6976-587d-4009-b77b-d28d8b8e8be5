package mysql

import (
	"context"
	json "github.com/bytedance/sonic"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"

	"code.byted.org/gopkg/logs"
)

func (s CodeBotMergeDetail) DisableResolveConflict(ctx context.Context) bool {
	if len(s.Setting) == 0 {
		return false
	}

	var setting MergeDetailSettings
	err := json.UnmarshalString(s.Setting, &setting)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return false
	}
	return setting.DisableResolveConflict
}

func (s CodeBotMergeDetail) DisableMergeTarget(ctx context.Context) bool {
	if len(s.Setting) == 0 {
		return false
	}

	var setting MergeDetailSettings
	err := json.UnmarshalString(s.Setting, &setting)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return false
	}
	return setting.DisableMergeTarget
}

func (sy CodeBotSyncTrace) Insert(ctx context.Context) error {
	return QualityWriteDB.Context(ctx).Create(&sy).Error
}

func (sy CodeBotSyncTrace) Update(ctx context.Context) error {
	return QualityWriteDB.Context(ctx).Model(&CodeBotSyncTrace{}).Where(&CodeBotSyncTrace{
		DevId:     sy.DevId,
		GroupName: sy.GroupName,
	}).Updates(&CodeBotSyncTrace{
		SyncType:  sy.SyncType,
		SyncState: sy.SyncState,
		SyncDevId: sy.SyncDevId,
		SyncMrId:  sy.SyncMrId,
		MergedAt:  sy.MergedAt,
		SyncAt:    sy.SyncAt,
		SyncTo:    sy.SyncTo,
	}).Error
}

func (sy CodeBotSyncTrace) Delete(ctx context.Context) error {
	return QualityWriteDB.Context(ctx).Where(&CodeBotSyncTrace{
		SyncState: 0,
		DevId:     sy.DevId,
		GroupName: sy.GroupName,
	}).Delete(&sy).Error
}

func (t *BranchSyncTask) SetStatus(ctx context.Context, status string) {
	if t.Id <= 0 {
		logs.CtxInfo(ctx, "task is not inserted")
		return
	}
	t.Status = status
	err := QualityWriteDB.Context(ctx).Save(t).Error
	if err != nil {
		logs.CtxError(ctx, "save task failed, %v, %s", err, utils.ToJson(t))
	}
}

func (t *BranchSyncTask) SetErrMsg(ctx context.Context, errMsg string) {
	if t.Id <= 0 {
		logs.CtxInfo(ctx, "task is not inserted")
		return
	}
	t.ErrMsg = errMsg
	err := QualityWriteDB.Context(ctx).Save(t).Error
	if err != nil {
		logs.CtxError(ctx, "save task failed, %v, %s", err, utils.ToJson(t))
	}
}
