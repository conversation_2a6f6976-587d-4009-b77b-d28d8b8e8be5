load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "handler",
    srcs = [
        "ping.go",
        "task.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/hotfix/hotfix_task/biz/handler",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/hotfix/hotfix_task/biz/data",
        "//app/rd-process/hotfix/hotfix_task/biz/model",
        "//app/rd-process/hotfix/hotfix_task/biz/service/hotfix",
        "//app/rd-process/hotfix/hotfix_task/biz/utils",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_middleware_hertz//pkg/app",
        "@org_byted_code_middleware_hertz//pkg/common/utils",
        "@org_byted_code_middleware_hertz_ext_v2//binding",
        "@org_byted_code_overpass_bytedance_bits_optimus//rpc/bytedance_bits_optimus",
        "@org_byted_code_overpass_bytedance_bits_workflow//kitex_gen/bytedance/bits/workflow",
    ],
)
