package model

import "time"

type HotfixAndroidBuild struct {
	ID             int64     `json:"id" gorm:"column:id"`                             // id
	VersionID      int64     `json:"version_id" gorm:"column:version_id"`             // version
	MainRepoCommit string    `json:"main_repo_commit" gorm:"column:main_repo_commit"` // 主仓commit id
	Extra          string    `json:"extra" gorm:"column:extra"`                       // extra信息
	CreateTime     time.Time `json:"create_time" gorm:"column:create_time"`           // 创建时间
	UpdateTime     time.Time `json:"update_time" gorm:"column:update_time"`           // 更新时间
}

func (m *HotfixAndroidBuild) TableName() string {
	return "hotfix_android_build"
}
