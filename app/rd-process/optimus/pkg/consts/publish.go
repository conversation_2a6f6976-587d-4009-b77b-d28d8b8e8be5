package consts

const PUBLISHING = "version_publishing"
const PUBLISH_VERSION_SUCCEEDED = "version_published"
const PUBLISH_VERSION_FAILED = "version_publish_failed"

const PUBLISHTYPENONE = "none"     // 未知类型，默认
const PUBLISHTYPEFIXED = "fixed"   // 固定版本
const PUBLISHTYPEAUTO = "auto"     // 自增版本
const PUBLISHTYPESEM = "sem"       // 头条自动发版
const PUBLISHTYPECUSTOM = "custom" // 即用户自定义版本号，通过接口请求用户的服务，获取组件升级的版本号 例如 creativex 仓库
