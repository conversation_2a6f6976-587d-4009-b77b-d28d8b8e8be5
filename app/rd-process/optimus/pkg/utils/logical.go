package utils

import (
	"regexp"
	"strings"
)

func If(condition bool, trueVal, falseVal interface{}) interface{} {
	if condition {
		return trueVal
	}
	return falseVal
}

func TrimSuffix(email string) string {
	components := strings.Split(email, "@")
	username := components[0]
	return username
}

func GetUserNames(emails []string) []string {
	usernames := make([]string, 0)
	for _, email := range emails {
		username := TrimSuffix(email)
		usernames = append(usernames, username)
	}
	return usernames
}

var TaskOauth2TokenRegexp = regexp.MustCompile("oauth2:(.*?)@")

func DesensitizeMsg(msg string) string {
	if len(msg) == 0 {
		return msg
	}
	return TaskOauth2TokenRegexp.ReplaceAllString(msg, "oauth2:****@")
}
