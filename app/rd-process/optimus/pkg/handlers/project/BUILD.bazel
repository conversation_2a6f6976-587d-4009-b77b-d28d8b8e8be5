load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "project",
    srcs = [
        "branch.go",
        "info.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/handlers/project",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/optimus",
        "//app/rd-process/optimus/pkg/data",
        "//app/rd-process/optimus/pkg/model",
        "//app/rd-process/optimus/pkg/service/project",
        "//app/rd-process/optimus/pkg/utils",
        "//app/rd-process/optimus/service/tcc",
        "//libs/bits_err",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_facility//set",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_toutiao_elastic_v7//:elastic",
    ],
)
