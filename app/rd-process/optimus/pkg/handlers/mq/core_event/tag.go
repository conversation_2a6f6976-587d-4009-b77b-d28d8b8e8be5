package core_event

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/mr"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/utils"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gresult"
	"context"
)

// isTagExist
// @title check if tag exists
// @description Check if the tag has been bound on the same mr
func isTagExist(ctx context.Context, mrId int64, tagId int64) gresult.R[bool] {
	tagBind := data.GetBinding(ctx, tagId, mrId)
	if tagBind.IsErr() && !utils.IsRecordNotFound(tagBind.Err()) {
		return gresult.Err[bool](tagBind.Err())
	}
	if tagBind.IsOK() {
		return gresult.OK(true)
	}
	return gresult.OK(false)
}

// CreateTagToMrWithDouDouCreated
// @title add a builtin tag that is named DouDou on mr,
// @description when the mr is created by DouDou bot, through subscribe the merge_request_core_event mq
func CreateTagToMrWithDouDouCreated(ctx context.Context, body *CreateMrModel) {
	// only trigger bind logic once when host mr is created
	if !body.BitsHostMr {
		return
	}

	flag := false
	if body.SystemTags != nil && len(*body.SystemTags) != 0 {
		for _, item := range *body.SystemTags {
			if item == "DouDou" {
				flag = true
			}
		}
	}

	// if tags not contain the DouDou
	if !flag {
		return
	}

	// avoiding duplicate bindings
	if isTagExist(ctx, body.MrID, model.OptimusTagsDouDouID).Value() {
		return
	}
	m := &mr.MergeRequest{}
	err := m.FillUniqueID(ctx, nil, nil, &body.MrID)

	if err != nil {
		logs.CtxError(ctx, err.Error())
	}
	res := m.BindGitlabMRTag(ctx, model.OptimusTagsDouDouName, "be created by DouDou bot", nil)

	if res.IsErr() {
		logs.CtxError(ctx, "create DouDou system tag error: error=%s", res.Err().Error())
	}
}

// CreateTagToMrWithOpenAPICreated
// @title add a builtin tag that is named Open API on mr,
// @description when the mr is created by open API, through subscribe the merge_request_core_event mq
func CreateTagToMrWithOpenAPICreated(ctx context.Context, body *CreateMrModel) {
	if !body.BitsHostMr {
		return
	}
	flag := false
	if body.SystemTags != nil && len(*body.SystemTags) != 0 {
		for _, item := range *body.SystemTags {
			if item == "openapi" {
				flag = true
			}
		}
	}
	if !flag {
		return
	}

	if isTagExist(ctx, body.MrID, model.OptimusTagsOpenAPIID).Value() {
		return
	}
	m := &mr.MergeRequest{}
	err := m.FillUniqueID(ctx, nil, nil, &body.MrID)

	if err != nil {
		logs.CtxError(ctx, err.Error())
	}
	res := m.BindGitlabMRTag(ctx, model.OptimusTagsOpenAPIName, "be created by Open API", nil)
	if res.IsErr() {
		logs.CtxError(ctx, "create open api system tag error: error=%s", res.Err().Error())
	}
}

// MrBindTask
// @title add a builtin tag that is named Meego on mr,
// @description when the mr is created with meego task, through subscribe the merge_request_core_event mq
func MrBindTask(ctx context.Context, body *MrBindTaskModel) {
	tagId := 0
	switch body.Platform {
	case "slardar":
		tagId = model.OptimusTagsSlardarID
	case "meego":
		tagId = model.OptimusTagsMeegoID
	case "rocket":
		tagId = model.OptimusTagsRocketID
	}
	if isTagExist(ctx, body.MrID, int64(tagId)).Value() {
		return
	}
	res := data.BindTag(ctx, &model.OptimusTags{
		ID: tagId,
	}, body.MrID, body.GroupName, "bind meego task")

	if res.IsErr() {
		logs.CtxError(ctx, "create mr_bind system tag error: error=%s", res.Err().Error())
	}
}

// ForceMerge
// @title add a builtin tag that is named Force Merge on mr,
// @description when the mr use force-merge to make mr merge duress, through subscribe the merge_request_core_event mq
func ForceMerge(ctx context.Context, body *MrForceMergeModel) {
	if isTagExist(ctx, body.MrID, model.OptimusTagsForceMergeID).Value() {
		return
	}
	m := &mr.MergeRequest{}
	err := m.FillUniqueID(ctx, nil, nil, &body.MrID)

	if err != nil {
		logs.CtxError(ctx, err.Error())
	}
	res := m.BindGitlabMRTag(ctx, model.OptimusTagsForceMergeEnName, "use force merge function", nil)
	if res.IsErr() {
		logs.CtxError(ctx, "create force merge system tag error: error=%s", res.Err().Error())
	}
}

// SuperMr
// @title add a builtin tag that is named Super MR on mr
// @description when the ordinary mr upgraded to super mr will set this tag, in addition, when the mr is changed from super to ordinary this tag will be removed
// through subscribe the merge_request_core_event mq
func SuperMr(ctx context.Context, body *SuperMrModel) {
	existFlag := isTagExist(ctx, body.MrID, model.OptimusTagsSuperMrID).Value()
	m := &mr.MergeRequest{}
	err := m.FillUniqueID(ctx, nil, nil, &body.MrID)

	if err != nil {
		logs.CtxError(ctx, err.Error())
	}
	// changed from ordinary to super
	if body.State && !existFlag {
		res := m.BindGitlabMRTag(ctx, model.OptimusTagsSuperMrEnName, "use super mr function", nil)
		if res.IsErr() {
			logs.CtxError(ctx, "create super mr system tag error: error=%s", res.Err().Error())
		}
	}
	// changed from super to ordinary
	if !body.State && existFlag {
		res := m.RemoveBindGitlabMRTag(ctx, model.OptimusTagsSuperMrEnName, nil)
		if res.IsErr() {
			logs.CtxError(ctx, "create super mr system tag error: error=%s", res.Err().Error())
		}
	}
}

// MrMode
// @title add a builtin tag that is named Common MR or Mutil-host MR on mr
// @description this tag is represent type of mr
func MrMode(ctx context.Context, body *CreateMrModel) {
	if !body.BitsHostMr {
		return
	}

	var tagId int
	if body.MrMode == "single_master" {
		tagId = model.OptimusTagsSingleMasterMrID
	}
	if body.MrMode == "multiple_master" {
		tagId = model.OptimusTagsMultipleMasterMrID
	}

	if tagId == 0 {
		return
	}

	if isTagExist(ctx, body.MrID, int64(tagId)).Value() {
		return
	}
	res := data.BindTag(ctx, &model.OptimusTags{
		ID: tagId,
	}, body.MrID, body.GroupName, "represent mr type")

	if res.IsErr() {
		logs.CtxError(ctx, "create mr_mode system tag error: error=%s", res.Err().Error())
	}
}
