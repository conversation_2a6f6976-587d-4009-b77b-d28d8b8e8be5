package data

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/database"
	"code.byted.org/lang/gg/gresult"
	"context"
)

func GetTimelineList(ctx context.Context, mrID int64) ([]*model.OptimusGitlabTimelineEvent, error) {
	timelines := make([]*model.OptimusGitlabTimelineEvent, 0)

	res := database.Optimus.Slave.NewRequest(ctx).Where("mr_id = ?", mrID).Order("id DESC").Limit(1000).Find(&timelines) // TODO 后期支持分页, 数据在多张表暂时无法实现

	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return timelines, nil
}

//支持分页，有最大限制1000,超过则分页无效
func GetTimelineListWithPage(ctx context.Context, mrID int64, pageSize, pageNum *int64) ([]*model.OptimusGitlabTimelineEvent, error) {
	timelines := make([]*model.OptimusGitlabTimelineEvent, 0)
	var limit int64 = 1000
	var offset int64 = 0
	if pageSize != nil && *pageSize > 0 && pageNum != nil && *pageNum > 0 {
		size := *pageSize
		num := *pageNum
		if size < 1000 {
			limit = size
		}
		offset = (num - 1) * limit
		res := database.Optimus.Slave.NewRequest(ctx).Where("mr_id = ?", mrID).Order("id DESC").Limit(limit).Offset(offset).Find(&timelines)
		if res.Error != nil && !res.RecordNotFound() {
			return nil, res.Error
		} else if res.RecordNotFound() {
			return nil, nil
		}
		return timelines, nil
	}
	res := database.Optimus.Slave.NewRequest(ctx).Where("mr_id = ?", mrID).Order("id DESC").Limit(limit).Find(&timelines)
	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}

	return timelines, nil
}

//支持分页，有最大限制1000,超过则分页无效
func GetDevTimelineListWithPage(ctx context.Context, devID int64, pageSize, pageNum *int64) ([]*model.OptimusGitlabTimelineEvent, error) {
	timelines := make([]*model.OptimusGitlabTimelineEvent, 0)
	var limit int64 = 1000
	var offset int64 = 0
	if pageSize != nil && *pageSize > 0 && pageNum != nil && *pageNum > 0 {
		size := *pageSize
		num := *pageNum
		if size < 1000 {
			limit = size
		}
		offset = (num - 1) * limit
		res := database.Optimus.Slave.NewRequest(ctx).Where("dev_id = ?", devID).Order("id DESC").Limit(limit).Offset(offset).Find(&timelines)
		if res.Error != nil && !res.RecordNotFound() {
			return nil, res.Error
		} else if res.RecordNotFound() {
			return nil, nil
		}
		return timelines, nil
	}
	res := database.Optimus.Slave.NewRequest(ctx).Where("dev_id = ?", devID).Order("id DESC").Limit(limit).Find(&timelines)
	if res.Error != nil && !res.RecordNotFound() {
		return nil, res.Error
	} else if res.RecordNotFound() {
		return nil, nil
	}
	return timelines, nil
}
func GetTimelineDataCount(ctx context.Context, mrID int64) (int64, error) {
	var total int64 = 0
	res := database.Optimus.Slave.NewRequest(ctx).Model(&model.OptimusGitlabTimelineEvent{}).Where("mr_id = ?", mrID).Count(&total)
	if res.Error != nil && !res.RecordNotFound() {
		return 0, res.Error
	} else if res.RecordNotFound() {
		return 0, nil
	}
	return total, nil
}
func AddTimelineEvent(ctx context.Context, event *model.OptimusGitlabTimelineEvent) (*model.OptimusGitlabTimelineEvent, error) {
	err := database.Optimus.Master.NewRequest(ctx).Table(event.TableName()).Create(event).Error
	if err != nil {
		return nil, err
	}
	return event, err
}

func CreateDevTimelineEvent(ctx context.Context, event *model.DevTimelineEvent) gresult.R[*model.DevTimelineEvent] {
	err := database.Optimus.Master.NewRequest(ctx).Table(event.TableName()).Create(event).Error
	if err != nil {
		return gresult.Err[*model.DevTimelineEvent](err)
	}
	return gresult.OK(event)
}

func GetDevTimelineEventsWithLastID(ctx context.Context, devID int64, limit int, lastID int64) gresult.R[[]*model.DevTimelineEvent] {
	timelines := make([]*model.DevTimelineEvent, 0)
	res := database.Optimus.Slave.NewRequest(ctx).Where("dev_id = ? and id < ?", devID, lastID).Order("id DESC").Limit(limit).Find(&timelines)
	if res.Error != nil {
		return gresult.Err[[]*model.DevTimelineEvent](res.Error)
	}
	return gresult.OK(timelines)
}

func GetDevTimelineEventsInEventTypeWithLastID(ctx context.Context, devID int64, limit int, lastID int64, typeList []string) gresult.R[[]*model.DevTimelineEvent] {
	timelines := make([]*model.DevTimelineEvent, 0)
	if len(typeList) == 0 {
		return gresult.OK(timelines)
	}
	res := database.Optimus.Slave.NewRequest(ctx).Where("dev_id = ? AND event_type IN (?) AND id < ?", devID, typeList, lastID).Order("id DESC").Limit(limit).Find(&timelines)
	if res.Error != nil {
		return gresult.Err[[]*model.DevTimelineEvent](res.Error)
	}
	return gresult.OK(timelines)
}
