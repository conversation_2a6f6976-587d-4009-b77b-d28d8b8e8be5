package data

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/database"
	"context"
	"database/sql"
	"gorm.io/gorm"
)

func GetDevFeatureRecordByDevId(ctx context.Context, devId int64) ([]*model.DevFeatureRecord, error) {
	var list []*model.DevFeatureRecord
	res := database.Optimus.Slave.NewRequest(ctx).Where("dev_id = ?", devId).Find(&list)
	if res.RecordNotFound() || res.Error == gorm.ErrRecordNotFound || res.Error == sql.ErrNoRows {
		return nil, nil
	}
	if res.Error != nil {
		return nil, res.Error
	}
	return list, nil
}
