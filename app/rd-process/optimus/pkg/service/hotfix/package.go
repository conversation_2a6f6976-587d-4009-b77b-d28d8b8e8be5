package hotfix

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/hotfix"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	release "code.byted.org/overpass/bits_release_hotfix/kitex_gen/bits/release/hotfix"
	"code.byted.org/overpass/bits_release_hotfix/rpc/bits_release_hotfix"
	"context"
	"errors"
)

func GetPackageArtifacts(ctx context.Context, packageID int64) ([]*hotfix.HotfixArtifact, error) {
	pkg, err := data.GetHotfixPackageByID(ctx, packageID)
	if err != nil {
		logs.CtxError(ctx, "failed to get package by id:%d, err: %s", packageID, err.Error())
		return nil, err
	}
	if pkg == nil {
		return nil, errors.New("package not found")
	}
	artifact, err := bits_release_hotfix.QueryDevOpsHotfixArtifact(ctx, pkg.PipelineID)
	if err != nil {
		logs.CtxError(ctx, "failed to query artifact, err: %s", err.Error())
		return nil, err
	}
	return gslice.Map(gslice.Filter(artifact.Artifacts, func(a *release.HotfixArtifact) bool {
		return a.ArtifactType != nil && *a.ArtifactType == release.ArtifactType_SaveU
	}), func(a *release.HotfixArtifact) *hotfix.HotfixArtifact {
		artifactType := hotfix.ArtifactType(*a.ArtifactType)
		ha := &hotfix.HotfixArtifact{
			Url:             a.Url,
			Md5:             a.Md5,
			Size:            a.Size,
			Id:              a.Id,
			SaveuPkgVersion: a.SaveuPkgVersion,
			ArtifactType:    &artifactType,
			PublishID:       a.PublishId,
		}
		if a.Status != nil {
			artifactStatus := hotfix.SaveuHotfixPkgStatus(*a.Status)
			ha.Status = &artifactStatus
		}
		return ha
	}), nil
}
