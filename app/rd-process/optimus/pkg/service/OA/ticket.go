package OA

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"sync"

	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/tcc"

	"code.byted.org/gopkg/logs"
	"code.byted.org/overpass/bytedance_bits_approve/kitex_gen/bytedance/bits/approve"
	"code.byted.org/overpass/bytedance_bits_approve/rpc/bytedance_bits_approve"
)

type oaApproverType string

const (
	OAApproverType_Tech = "tech"
	OAApproverType_Biz  = "biz"
)

func (t oaApproverType) String() string {
	return string(t)
}

const hotfixVariableTemplate = `{"hotfix_tech_approvers":"%s","hotfix_biz_approvers":"%s"}`

func CreateOATicket(ctx context.Context, title, author string, bizApprovers, techApprovers []string, hotfixID int64) (string, error) {
	initiatorID, err := username2ID(ctx, author)
	if err != nil {
		return "", err
	}
	bizApproverIDs := make([]string, len(bizApprovers))
	wg := sync.WaitGroup{}
	for i, approver := range bizApprovers {
		wg.Add(1)
		approver := approver
		i := i
		go func() {
			defer wg.Done()
			id, err := username2ID(ctx, approver)
			if err != nil {
				logs.CtxError(ctx, "failed convert username to id: %s", err)
			}
			bizApproverIDs[i] = strconv.FormatInt(id, 10)
		}()
	}
	techApproverIDs := make([]string, len(techApprovers))
	for i, approver := range techApprovers {
		wg.Add(1)
		approver := approver
		i := i
		go func() {
			defer wg.Done()
			id, err := username2ID(ctx, approver)
			if err != nil {
				logs.CtxError(ctx, "failed convert username to id: %s", err)
			}
			techApproverIDs[i] = strconv.FormatInt(id, 10)
		}()
	}
	wg.Wait()
	hotfix, err := data.GetHotfixTaskByID(ctx, hotfixID, true)
	if err != nil {
		logs.CtxError(ctx, "failed to get hotfix task: %s", err.Error())
		return "", err
	}
	if hotfix == nil {
		logs.CtxError(ctx, "hotfix task not found")
		return "", errors.New("hotfix task not found")
	}
	var processDefinitionKey string
	var ticketTitle string
	if hotfix.HotfixType == model.HotfixType_iOS || hotfix.HotfixType == model.HotfixType_android || hotfix.HotfixType == model.HotfixType_IOSSDK {
		processDefinitionKey = "bits_hotfix_approve"
		ticketTitle = "hotfix package form  " + title
	} else if hotfix.HotfixType == model.HotfixType_flutter {
		processDefinitionKey = "bits_hotfix_approve_flutter"
		ticketTitle = "patch package form  " + title
	}
	hotfixVariable := fmt.Sprintf(hotfixVariableTemplate, strings.Join(techApproverIDs, ","), strings.Join(bizApproverIDs, ","))
	res, err := bytedance_bits_approve.RawCall.OACreateProcessInstance(ctx, &approve.OACreateProcessInstanceRequest{
		InitiatorId:           strconv.FormatInt(initiatorID, 10),
		ProcessDefinitionKey:  processDefinitionKey,
		BusinessKey:           strconv.FormatInt(hotfixID, 10),
		TransientVariableJson: &hotfixVariable,
		Form:                  "",
		Title:                 ticketTitle,
	})
	if err != nil {
		logs.CtxError(ctx, "failed to create oa ticket:%s", err.Error())
		return "", err
	}
	return res.ProcessInstanceId, nil
}

func checkCanApprove(ctx context.Context, ticketID string) bool {
	task, err := data.GetHotfixTaskByTicketID(ctx, ticketID)
	if err != nil {
		logs.CtxError(ctx, "failed to get hotfix task: %s", err.Error())
		return false
	}
	if task.ApprovalStatus == "PENDING" {
		return true
	}
	return false
}

func Approve(ctx context.Context, ticketID, user, comment string) (bool, error) {
	if !checkCanApprove(ctx, ticketID) {
		return false, fmt.Errorf("task is not in pending status")
	}
	nodeMap, err := tcc.GetOANodeMap(ctx)
	if err != nil {
		return false, err
	}
	userID, err := username2ID(ctx, user)
	if err != nil {
		return false, err
	}
	approvals, err := data.GetApprovalByName(ctx, ticketID, user)
	if err != nil {
		return false, err
	}
	if len(approvals) == 0 {
		return false, nil
	}
	done := false
	for _, approval := range approvals {
		if !done {
			nodeId := nodeMap[approval.Role]
			res, err := bytedance_bits_approve.RawCall.OARunTaskProcess(ctx, &approve.OARunTaskProcessRequest{
				ProcessInstanceId: &ticketID,
				CommandId:         approve.OACommand_general.String(),
				AssigneeId:        strconv.FormatInt(userID, 10),
				NodeId:            &nodeId,
			})
			if err != nil {
				logs.CtxError(ctx, "faield to approval:%s", err.Error())
				// 没有待处理的节点，不报错
				if strings.Contains(err.Error(), "getTodoNode == nil") {
					continue
				}
				return false, err
			}
			if res.ProcessStatus == approve.OAProcessInstanceStatus_COMPLETE.String() {
				// 流程终止，没必要继续下去了
				done = true
			}
		}
		err = data.SetApprovalStatus(ctx, approval, model.ApprovalStatus_approved, comment)
		if err != nil {
			logs.CtxError(ctx, "failed to set approval status:%s", err.Error())
			return false, err
		}
	}
	return done, nil
}

func Reject(ctx context.Context, ticketID, user, comment string) (bool, error) {
	if !checkCanApprove(ctx, ticketID) {
		return false, fmt.Errorf("task is not in pending status")
	}
	nodeMap, err := tcc.GetOANodeMap(ctx)
	if err != nil {
		return false, err
	}
	userID, err := username2ID(ctx, user)
	if err != nil {
		return false, err
	}
	approvals, err := data.GetApprovalByName(ctx, ticketID, user)
	if err != nil {
		return false, err
	}
	if len(approvals) == 0 {
		return false, nil
	}
	done := false
	for _, approval := range approvals {
		if !done {
			nodeId := nodeMap[approval.Role]
			res, err := bytedance_bits_approve.RawCall.OARunTaskProcess(ctx, &approve.OARunTaskProcessRequest{
				ProcessInstanceId: &ticketID,
				CommandId:         approve.OACommand_rejectAndTerminate.String(),
				AssigneeId:        strconv.FormatInt(userID, 10),
				NodeId:            &nodeId,
			})
			if err != nil {
				logs.CtxError(ctx, "failed to reject:%s", err.Error())
				return false, err
			}
			if res.ProcessStatus == approve.OAProcessInstanceStatus_TERMINATION.String() {
				done = true
			}
		}
		err = data.SetApprovalStatus(ctx, approval, model.ApprovalStatus_rejected, comment)
		if err != nil {
			logs.CtxError(ctx, "failed to set approval status:%s", err.Error())
			return false, err
		}
	}
	return done, nil
}

func username2ID(ctx context.Context, username string) (int64, error) {
	info, err := rpc.MetaClient.GetUserEmployeeId(ctx, &meta.GetUserEmployeeIdRequest{Username: username})
	if err != nil {
		logs.CtxError(ctx, "failed to get employee info", err.Error())
		return 0, err
	}
	return info.EmployeeId, nil
}

func GetOAStatus(ctx context.Context, ticketID string) (*approve.GetApproveInfoResponseData, error) {
	info, err := bytedance_bits_approve.RawCall.GetApproveInfo(ctx, &approve.GetApproveInfoRequest{ProcessInstanceId: ticketID})
	if err != nil {
		logs.CtxError(ctx, "failed to get oa status:%s", err.Error())
		return nil, err
	}
	return info.Data, nil
}

func TerminalOATicket(ctx context.Context, ticketID string) error {
	_, err := bytedance_bits_approve.OACancelProcess(ctx, ticketID)
	if err != nil {
		logs.CtxError(ctx, "failed to cancel oa ticket:%s", err.Error())
		return err
	}
	return nil
}
