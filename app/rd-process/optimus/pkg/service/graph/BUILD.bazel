load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "graph",
    srcs = [
        "android.go",
        "block_error.go",
        "cache.go",
        "ci_check.go",
        "code_review.go",
        "common.go",
        "config.go",
        "custom.go",
        "dep_merge.go",
        "flutter_deps.go",
        "flutter_host.go",
        "gerrit_dev.go",
        "graph.go",
        "ios.go",
        "lock.go",
        "meego_check.go",
        "pipeline.go",
        "popover.go",
        "push.go",
        "stage.go",
        "train.go",
    ],
    cgo = True,
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/graph",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/optimus/kitex_gen/bits/devops/push",
        "//app/rd-process/optimus/kitex_gen/bits/devops/trains",
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/aosp",
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/component",
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/git_server",
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/graph",
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/optimus",
        "//app/rd-process/optimus/kitex_gen/bytedance/bits/workflow",
        "//app/rd-process/optimus/pkg/consts",
        "//app/rd-process/optimus/pkg/data",
        "//app/rd-process/optimus/pkg/model",
        "//app/rd-process/optimus/pkg/service",
        "//app/rd-process/optimus/pkg/service/block_error",
        "//app/rd-process/optimus/pkg/service/codebase",
        "//app/rd-process/optimus/pkg/service/gitlab",
        "//app/rd-process/optimus/pkg/service/group",
        "//app/rd-process/optimus/pkg/service/merge_request",
        "//app/rd-process/optimus/pkg/service/mr",
        "//app/rd-process/optimus/pkg/service/node",
        "//app/rd-process/optimus/pkg/utils",
        "//app/rd-process/optimus/rpc",
        "//app/rd-process/optimus/service/cache",
        "//app/rd-process/optimus/service/tcc",
        "//idls/protobuf/graph_node",
        "//libs/bits_err",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//collection/set",
        "@org_byted_code_overpass_bits_optimus_core//rpc/bits_optimus_core",
        "@org_byted_code_overpass_bits_optimus_infra//kitex_gen/bits/optimus/infra",
        "@org_byted_code_overpass_bits_optimus_infra//rpc/bits_optimus_infra",
        "@org_byted_code_overpass_bytedance_bits_code_review//rpc/bytedance_bits_code_review",
        "@org_byted_code_overpass_bytedance_bits_qa_review//kitex_gen/bytedance/bits/qa_review",
        "@org_byted_code_overpass_bytedance_bits_qa_review//rpc/bytedance_bits_qa_review",
        "@org_byted_code_overpass_common//option/calloption",
    ],
)
