package graph

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/graph"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/merge_request"
	"code.byted.org/devinfra/hagrid/idls/protobuf/graph_node"
	"code.byted.org/gopkg/logs"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/overpass/bits_optimus_infra/kitex_gen/bits/optimus/infra"
	"code.byted.org/overpass/bits_optimus_infra/rpc/bits_optimus_infra"
	json "github.com/bytedance/sonic"
)

func getShadowBranchNode(ctx context.Context, mrId int64) (*graph.NodeBrief, error) {
	const stageName = "shadow branch"
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	taskName, err := getShadowBranchTaskName(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if len(taskName) == 0 {
		return nil, nil
	}
	machineTask, err := data.GetMachineTaskByProjectIdAndIIdAndTaskName(ctx, mrInfo.ProjectID, mrInfo.IID, taskName)
	if err != nil {
		return nil, err
	}
	if machineTask == nil {
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_shadow_branch),
			Position:    nil,
			Status:      graph.Status_not_start,
			Name:        stageName,
			Type:        graph.WorkFlowType_shadow_branch,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	var status graph.Status
	switch machineTask.Status {
	case "succeeded":
		status = graph.Status_success
	case "running":
		status = graph.Status_running
	case "pending":
		status = graph.Status_not_start
	case "failed":
		status = graph.Status_failed
	default:
		status = graph.Status_running
	}

	var errorCount int64
	if status == graph.Status_failed {
		errorCount = 1
	}

	return &graph.NodeBrief{
		Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_shadow_branch),
		Position:    nil,
		Status:      status,
		Name:        stageName,
		Type:        graph.WorkFlowType_shadow_branch,
		StartTime:   machineTask.CreateTime.Unix(),
		EndTime:     machineTask.UpdateTime.Unix(),
		Collapsable: false,
		ErrorCount:  &errorCount,
	}, nil
}

const (
	integrationType_dependency_table = 0
	integrationType_submodule        = 1
	integrationType_skip             = 2
	integrationType_pipeline         = 3
)

func getShadowBranchTaskName(ctx context.Context, mrId int64) (string, error) {
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return "", err
	}
	if mrInfo == nil {
		return "", nil
	}
	projectConfig, err := data.GetConfigProjectInfoByProjectIDRaw(ctx, mrInfo.ProjectID, true)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return "", err
	}
	groupConfig, err := data.GetConfigGroupInfoByID(ctx, projectConfig.MainGroupConfigID, true)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return "", err
	}
	if groupConfig.PipelineBranchType != "shadow_branch" { // 没开shadowbranch
		return "", nil
	}

	if projectConfig.IntegrationType == integrationType_dependency_table {
		switch projectConfig.AppType {
		case model.AppType_ios:
			return "tasks.integration.IntegrationBranchCreatorForIOSTask.IntegrationBranchCreatorForIOSTask", nil
		case model.AppType_android:
			return "tasks.integration.IntegrationBranchCreatorForAndroidTask.IntegrationBranchCreatorForAndroidTask", nil
		case model.AppType_harmony:
			return "tasks.integration.IntegrationBranchCreatorForHarmonyTask.IntegrationBranchCreatorForHarmonyTask", nil
		case model.AppType_flutter:
			return "tasks.integration.IntegrationBranchCreatorForFlutterTask.IntegrationBranchCreatorForFlutterTask", nil
		default:
			return "", nil
		}
	} else if projectConfig.IntegrationType == integrationType_submodule {
		return "tasks.integration.IntegrationBranchCreatorForSubmoduleTask.IntegrationBranchCreatorForSubmoduleTask", nil
	}
	return "", nil
}

func getRepoMergeNode(ctx context.Context, mrId int64, role mrRole) (*graph.NodeBrief, error) {
	const stageName = "repo merge"
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	if mrInfo.State == consts.OPTIMUS_CLOSED {
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_merge),
			Position:    nil,
			Status:      graph.Status_failed,
			Name:        stageName,
			Type:        graph.WorkFlowType_repo_merge,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	} else if mrInfo.State == consts.OPTIMUS_MERGED {
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_merge),
			Position:    nil,
			Status:      graph.Status_success,
			Name:        stageName,
			Type:        graph.WorkFlowType_repo_merge,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	isEnableReleaseBeforeMerge, err := enableReleaseBeforeMerge(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if isEnableReleaseBeforeMerge {
		hostMrId, err := bits_optimus_infra.GetFirstHost(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR, Role: infra.DependencyRole_Dependency})
		if err != nil {
			return nil, err
		}
		if hostMrId == nil || hostMrId.Dep == nil {
			return nil, errors.New("get host dev failed")
		}
		hostMrInfo, err := data.GetMergeRequestInfoByMrID(ctx, hostMrId.Dep.Id, true)
		if err != nil {
			return nil, err
		}
		if hostMrInfo == nil {
			return nil, fmt.Errorf("can't find host mr id:%d", hostMrId.Dep.Id)
		}
		isEnableAfterCheck := false
		if role != MrRole_PubDep && role != MrRole_PubDep_Host {
			isEnableAfterCheck, err = enableAfterCheck(ctx, hostMrInfo.ProjectID)
			if err != nil {
				return nil, err
			}
		}
		if !isEnableAfterCheck {
			publishVersion, err := data.GetPublishVersionByProjectIDRawAndIID(ctx, mrInfo.ProjectID, mrInfo.IID, false)
			if err != nil {
				return nil, err
			}
			if publishVersion == nil || publishVersion.RepoStatus != "succeeded" {
				return &graph.NodeBrief{
					Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_merge),
					Position:    nil,
					Status:      graph.Status_not_start,
					Name:        stageName,
					Type:        graph.WorkFlowType_repo_merge,
					StartTime:   0,
					EndTime:     0,
					Collapsable: false,
					ErrorCount:  nil,
				}, nil
			}
		} else {
			pipelineStatus, err := merge_request.GetMergeTargetAndAfterCheckPipelineContextValue(ctx, mrId)
			if err != nil {
				return nil, err
			}
			if pipelineStatus != "success" {
				return &graph.NodeBrief{
					Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_merge),
					Position:    nil,
					Status:      graph.Status_not_start,
					Name:        stageName,
					Type:        graph.WorkFlowType_repo_merge,
					StartTime:   0,
					EndTime:     0,
					Collapsable: false,
					ErrorCount:  nil,
				}, nil
			}
		}

	}
	var status graph.Status
	if mrInfo.State == "merged" {
		status = graph.Status_success
	} else if mrInfo.State == "closed" {
		status = graph.Status_failed
	} else if canUpdateStatus(ctx, mrId) {
		status = graph.Status_not_start
	} else {
		status = graph.Status_running
	}
	return &graph.NodeBrief{
		Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_merge),
		Position:    nil,
		Status:      status,
		Name:        stageName,
		Type:        graph.WorkFlowType_repo_merge,
		StartTime:   0,
		EndTime:     0,
		Collapsable: false,
		ErrorCount:  nil,
	}, nil
}

func getRepoAfterReleaseNode(ctx context.Context, mrId int64, projectConfig *model.OptimusConfigProject) (*graph.NodeBrief, error) {
	const stageName = "repo release"
	isReleaseBeforeMerge, err := enableReleaseBeforeMerge(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if isReleaseBeforeMerge {
		return nil, nil
	}
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	pubVersion, err := data.GetPublishVersionByProjectIDRawAndIID(ctx, mrInfo.ProjectID, mrInfo.IID, false)
	if err != nil {
		return nil, err
	}
	if pubVersion == nil {
		logs.CtxError(ctx, "publish version not found,projectId:%s,iid:%d", mrInfo.ProjectID, mrInfo.IID)
		return nil, nil
	}
	componentVersions, err := data.GetComponentVersionInfoByPublishVersionID(ctx, pubVersion.ID)
	if err != nil {
		return nil, err
	}
	if len(componentVersions) == 0 {
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_after_release),
			Position:    nil,
			Status:      graph.Status_not_start,
			Name:        stageName,
			Type:        graph.WorkFlowType_repo_after_release,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	if componentVersions[0].ComponentType == "snapshot" && (projectConfig.AppType == model.AppType_android || projectConfig.AppType == model.AppType_harmony) { // 考虑ios binary
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_after_release),
			Position:    nil,
			Status:      graph.Status_not_start,
			Name:        stageName,
			Type:        graph.WorkFlowType_repo_after_release,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	var status graph.Status
	if mrInfo.State == "merged" {
		switch pubVersion.RepoStatus {
		case "pending":
			status = graph.Status_waiting
		case "running":
			status = graph.Status_running
		case "failed":
			status = graph.Status_failed
		case "succeeded":
			status = graph.Status_success
		default:
			status = graph.Status_not_start
		}
	} else {
		status = graph.Status_not_start
	}
	var startTime int64 = 0
	var endTime int64 = 0
	releaseInfo := releaseInfoStruct{}
	err = json.Unmarshal([]byte(pubVersion.ReleaseInfo), &releaseInfo)
	if err != nil || releaseInfo.StartTime*releaseInfo.EndTime == 0 {
		logs.CtxWarn(ctx, "failed get time from release info:", pubVersion.ReleaseInfo)
		host, err := bits_optimus_infra.GetHostForDep(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR})
		if err != nil {
			return nil, err
		}
		if host.Host != nil {
			startTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_start", host.Host.Id)
			if err != nil {
				return nil, err
			}
			endTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_is_end", host.Host.Id)
			if err != nil {
				return nil, err
			}
		}
	} else {
		startTime = releaseInfo.StartTime
		endTime = releaseInfo.EndTime
	}
	if startTime*endTime == 0 { // 时间兜底
		host, err := bits_optimus_infra.GetHostForDep(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR})
		if err != nil {
			return nil, err
		}
		if host.Host != nil {
			startTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_start", host.Host.Id)
			if err != nil {
				return nil, err
			}
			endTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_is_end", host.Host.Id)
			if err != nil {
				return nil, err
			}
		}
	}
	return &graph.NodeBrief{
		Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_repo_after_release),
		Position:    nil,
		Status:      status,
		Name:        stageName,
		Type:        graph.WorkFlowType_repo_after_release,
		StartTime:   startTime,
		EndTime:     endTime,
		Collapsable: false,
		ErrorCount:  nil,
	}, nil
}

func getHostAfterReleaseNode(ctx context.Context, mrId int64, projectConfig *model.OptimusConfigProject) (*graph.NodeBrief, error) {
	const stageName = "host release"
	isReleaseBeforeMerge, err := enableReleaseBeforeMerge(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if isReleaseBeforeMerge {
		return nil, nil
	}
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	pubVersion, err := data.GetPublishVersionByProjectIDRawAndIID(ctx, mrInfo.ProjectID, mrInfo.IID, false)
	if err != nil {
		return nil, err
	}
	if pubVersion == nil {
		logs.CtxError(ctx, "publish version not found,projectId:%s,iid:%d", mrInfo.ProjectID, mrInfo.IID)
		return nil, nil
	}
	componentVersions, err := data.GetComponentVersionInfoByPublishVersionID(ctx, pubVersion.ID)
	if err != nil {
		return nil, err
	}
	if len(componentVersions) == 0 {
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_after_release),
			Position:    nil,
			Status:      graph.Status_not_start,
			Name:        stageName,
			Type:        graph.WorkFlowType_host_after_release,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	if componentVersions[0].ComponentType == "snapshot" && (projectConfig.AppType == model.AppType_android || projectConfig.AppType == model.AppType_harmony) { // 考虑ios binary
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_after_release),
			Position:    nil,
			Status:      graph.Status_not_start,
			Name:        stageName,
			Type:        graph.WorkFlowType_host_after_release,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	var status graph.Status
	if mrInfo.State == "merged" {
		switch pubVersion.RepoStatus {
		case "pending":
			status = graph.Status_waiting
		case "running":
			status = graph.Status_running
		case "failed":
			status = graph.Status_failed
		case "succeeded":
			status = graph.Status_success
		default:
			status = graph.Status_not_start
		}
	} else {
		status = graph.Status_not_start
	}
	var startTime int64 = 0
	var endTime int64 = 0
	releaseInfo := releaseInfoStruct{}
	err = json.Unmarshal([]byte(pubVersion.ReleaseInfo), &releaseInfo)
	if err != nil || releaseInfo.StartTime*releaseInfo.EndTime == 0 {
		logs.CtxWarn(ctx, "failed get time from release info:", pubVersion.ReleaseInfo)
		host, err := bits_optimus_infra.GetHostForDep(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR})
		if err != nil {
			return nil, err
		}
		if host.Host != nil {
			startTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_start", host.Host.Id)
			if err != nil {
				return nil, err
			}
			endTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_is_end", host.Host.Id)
			if err != nil {
				return nil, err
			}
		}
	} else {
		startTime = releaseInfo.StartTime
		endTime = releaseInfo.EndTime
	}
	if startTime*endTime == 0 { // 时间兜底
		host, err := bits_optimus_infra.GetHostForDep(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR})
		if err != nil {
			return nil, err
		}
		if host.Host != nil {
			startTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_start", host.Host.Id)
			if err != nil {
				return nil, err
			}
			endTime, err = merge_request.GetTaskTimeContextValue(ctx, "DependencyPublishVersionTask_is_end", host.Host.Id)
			if err != nil {
				return nil, err
			}
		}
	}
	return &graph.NodeBrief{
		Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_after_release),
		Position:    nil,
		Status:      status,
		Name:        stageName,
		Type:        graph.WorkFlowType_host_after_release,
		StartTime:   startTime,
		EndTime:     endTime,
		Collapsable: false,
		ErrorCount:  nil,
	}, nil
}

func getHostIntegrationNode(ctx context.Context, mrId int64) (*graph.NodeBrief, error) {
	const stageName = "host integration"
	// 已经是主仓了
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	projectConfig, err := data.GetConfigProjectInfoByProjectIDRaw(ctx, mrInfo.ProjectID, true)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	hostModel, err := data.GetMergeRequestDependencyByProjectIDAndIIDRaw(ctx, mrInfo.ProjectID, mrInfo.IID, false)
	if err != nil {
		return nil, err
	}
	if hostModel == nil {
		return nil, nil
	}
	var isPipelineIntegrationGray bool
	extra := make(map[string]interface{})
	err = json.Unmarshal([]byte(mrInfo.Extra), &extra)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshall extra:%s,%s", err.Error(), mrInfo.Extra)
		isPipelineIntegrationGray = false
	} else {
		if v, ok := extra["mr_integration_type"]; ok && v.(string) == "pipeline" {
			isPipelineIntegrationGray = true
		}
	}
	var status graph.Status
	startTime := mrInfo.IntegrationStartTime
	endTime := mrInfo.IntegrationEndTime
	switch hostModel.IntegrationState {
	case "pending":
		status = graph.Status_waiting
	case "running":
		status = graph.Status_running
	case "success":
		status = graph.Status_success
	case "failed":
		status = graph.Status_failed
	}
	if hostModel.IntegrationState == "pending" {
		status = graph.Status_not_start
	}
	if isPipelineIntegrationGray || projectConfig.IntegrationType == model.IntegrationType_pipeline { // 走pipeline
		pipeline, err := data.GetPipelineInfoByID(ctx, mrInfo.IntegrationPipelineID)
		if err != nil {
			return nil, err
		}
		if pipeline == nil {
			status = graph.Status_not_start
		} else {
			switch pipeline.Status {
			case "pending":
				status = graph.Status_waiting
			case "running":
				status = graph.Status_running
			case "success":
				status = graph.Status_success
			case "failed":
				status = graph.Status_failed
			case "canceled":
				status = graph.Status_skipped
			case "last_failed":
				status = graph.Status_failed
			default:
				status = graph.Status_not_start
			}
			startTime = pipeline.CreateTime
			endTime = pipeline.UpdateTime
		}
	}
	startTimeUnix := int64(0)
	endTimeUnix := int64(0)
	if startTime != nil {
		startTimeUnix = startTime.Unix()
	}
	if endTime != nil {
		endTimeUnix = endTime.Unix()
	}
	var errCount *int64 = nil
	if status == graph.Status_failed {
		tmp := int64(1)
		errCount = &tmp
	}
	return &graph.NodeBrief{
		Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_integration),
		Position:    nil,
		Status:      status,
		Name:        stageName,
		Type:        graph.WorkFlowType_host_integration,
		StartTime:   startTimeUnix,
		EndTime:     endTimeUnix,
		Collapsable: false,
		ErrorCount:  errCount,
	}, nil
}

func getHostMergeNode(ctx context.Context, mrId int64) (*graph.NodeBrief, error) {
	const stageName = "host merge"
	// 已经是主仓了
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	if mrInfo.State == "closed" {
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_merge),
			Position:    nil,
			Status:      graph.Status_failed,
			Name:        stageName,
			Type:        graph.WorkFlowType_host_merge,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	}
	isEnableAfterCheck, err := enableAfterCheck(ctx, mrInfo.ProjectID)
	if err != nil {
		return nil, err
	}
	isEnableReleaseBeforeMerge, err := enableReleaseBeforeMerge(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if isEnableAfterCheck && isEnableReleaseBeforeMerge {
		pipelineStatus, err := merge_request.GetMergeTargetAndAfterCheckPipelineContextValue(ctx, mrId)
		if err != nil {
			return nil, err
		}

		if pipelineStatus != "success" {
			return &graph.NodeBrief{
				Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_merge),
				Position:    nil,
				Status:      graph.Status_not_start,
				Name:        stageName,
				Type:        graph.WorkFlowType_host_merge,
				StartTime:   0,
				EndTime:     0,
				Collapsable: false,
				ErrorCount:  nil,
			}, nil
		}
	}
	extra := make(map[string]interface{})
	err = json.Unmarshal([]byte(mrInfo.Extra), &extra)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshall extra:%s,%s", err.Error(), mrInfo.Extra)
	} else {
		if v, ok := extra["rc_merge_mr"]; ok {
			if b, ok := v.(bool); ok && b {
				mergeBranchToRcStatus, err := merge_request.GetRCHostMergeContextValue(ctx, "merge_branch_to_rc_status", mrId)
				if err != nil {
					return nil, err
				}
				if mergeBranchToRcStatus == "running" {
					return &graph.NodeBrief{
						Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_merge),
						Position:    nil,
						Status:      graph.Status_running,
						Name:        stageName,
						Type:        graph.WorkFlowType_host_merge,
						StartTime:   0,
						EndTime:     0,
						Collapsable: false,
						ErrorCount:  nil,
					}, nil
				}
				if mergeBranchToRcStatus == "failed" {
					return &graph.NodeBrief{
						Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_merge),
						Position:    nil,
						Status:      graph.Status_failed,
						Name:        stageName,
						Type:        graph.WorkFlowType_host_merge,
						StartTime:   0,
						EndTime:     0,
						Collapsable: false,
						ErrorCount:  nil,
					}, nil
				}
			}
		}
	}
	var status graph.Status
	if mrInfo.State == "merged" {
		status = graph.Status_success
	} else if mrInfo.State == "closed" {
		status = graph.Status_failed
	} else if canUpdateStatus(ctx, mrId) {
		status = graph.Status_not_start
	} else {
		if isMultiMr(ctx, mrId) {
			hostModel, err := data.GetMergeRequestDependencyByProjectIDAndIIDRaw(ctx, mrInfo.ProjectID, mrInfo.IID, true)
			if err != nil {
				return nil, err
			}
			if hostModel != nil {
				if hostModel.IntegrationState != "succeeded" {
					status = graph.Status_not_start
				} else {
					status = graph.Status_running
				}
			} else {
				logs.CtxError(ctx, "host model not found,projectId:%s , iid:%d", mrInfo.ProjectID, mrInfo.IID)
			}
		} else {
			status = graph.Status_running
		}
	}
	return &graph.NodeBrief{
		Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_host_merge),
		Position:    nil,
		Status:      status,
		Name:        stageName,
		Type:        graph.WorkFlowType_host_merge,
		StartTime:   0,
		EndTime:     0,
		Collapsable: false,
		ErrorCount:  nil,
	}, nil
}

var getShadowBranchPopOver = getFlutterShadowBranchPopOver

func getRepoMergePopOver(ctx context.Context, meta *graph_node.NodeMeta) (graph.PopOverType, *graph.GeneralPopOver, error) {
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, meta.BusinessId, true)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	isEnableReleaseBeforeMerge, err := enableReleaseBeforeMerge(ctx, meta.BusinessId)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	var status graph.Status
	if isEnableReleaseBeforeMerge {
		hostMrId, err := bits_optimus_infra.GetFirstHost(ctx, &infra.Dependency{Id: meta.BusinessId, Type: infra.DependencyType_MR, Role: infra.DependencyRole_Dependency})
		if err != nil {
			return graph.PopOverType_none, nil, err
		}
		if hostMrId == nil || hostMrId.Dep == nil {
			return graph.PopOverType_none, nil, errors.New("not found")
		}
		hostMrInfo, err := data.GetMergeRequestInfoByMrID(ctx, hostMrId.Dep.Id, true)
		if err != nil {
			return graph.PopOverType_none, nil, err
		}
		isEnableAfterCheck, err := enableAfterCheck(ctx, hostMrInfo.ProjectID)
		if err != nil {
			return graph.PopOverType_none, nil, err
		}
		if !isEnableAfterCheck {
			publishVersion, err := data.GetPublishVersionByProjectIDRawAndIID(ctx, mrInfo.ProjectID, mrInfo.IID, false)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			if publishVersion == nil || publishVersion.RepoStatus != "succeeded" {
				status = graph.Status_not_start
				goto ret
			}
		} else {
			pipelineStatus, err := merge_request.GetMergeTargetAndAfterCheckPipelineContextValue(ctx, meta.BusinessId)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			if pipelineStatus != "success" {
				status = graph.Status_not_start
				goto ret
			}
		}

	}
	if mrInfo.State == "merged" {
		status = graph.Status_success
	} else if mrInfo.State == "closed" {
		status = graph.Status_failed
	} else if canUpdateStatus(ctx, meta.BusinessId) {
		status = graph.Status_not_start
	} else {
		status = graph.Status_running
	}
ret:
	if mrInfo.State == "closed" {
		status = graph.Status_failed
	}
	return graph.PopOverType_general, &graph.GeneralPopOver{
		Name:         "Merge",
		Status:       status,
		Type:         graph.DepType_Repo,
		SourceBranch: mrInfo.SourceBranch,
		TargetBranch: mrInfo.TargetBranch,
		LogUrl:       "",
		GitUrl:       mrInfo.Url,
		RetryUrl:     "",
		RetryBody:    nil,
		ErrorMsg:     nil,
		ID:           &mrInfo.ID,
	}, nil
}

func getHostMergePopOver(ctx context.Context, meta *graph_node.NodeMeta) (graph.PopOverType, *graph.GeneralPopOver, error) {
	mrId := meta.BusinessId
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	isEnableAfterCheck, err := enableAfterCheck(ctx, mrInfo.ProjectID)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	isEnableReleaseBeforeMerge, err := enableReleaseBeforeMerge(ctx, mrId)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	var status graph.Status
	extra := make(map[string]interface{})
	if isEnableAfterCheck && isEnableReleaseBeforeMerge {
		pipelineStatus, err := merge_request.GetMergeTargetAndAfterCheckPipelineContextValue(ctx, mrId)
		if err != nil {
			return graph.PopOverType_none, nil, err
		}
		if pipelineStatus != "success" {
			status = graph.Status_not_start
			goto ret
		}
	}
	err = json.Unmarshal([]byte(mrInfo.Extra), &extra)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshall extra:%s,%s", err.Error(), mrInfo.Extra)
	} else {
		if v, ok := extra["rc_merge_mr"]; ok {
			if b, ok := v.(bool); ok && b {
				mergeBranchToRcStatus, err := merge_request.GetRCHostMergeContextValue(ctx, "merge_branch_to_rc_status", mrId)
				if err != nil {
					return graph.PopOverType_none, nil, err
				}
				if mergeBranchToRcStatus == "running" {
					status = graph.Status_running
					goto ret
				}
				if mergeBranchToRcStatus == "failed" {
					status = graph.Status_failed
					goto ret
				}
			}
		}
	}
	if mrInfo.State == "merged" {
		status = graph.Status_success
	} else if mrInfo.State == "closed" {
		status = graph.Status_failed
	} else if canUpdateStatus(ctx, mrId) {
		status = graph.Status_not_start
	} else {
		if isMultiMr(ctx, mrId) {
			hostModel, err := data.GetMergeRequestDependencyByProjectIDAndIIDRaw(ctx, mrInfo.ProjectID, mrInfo.IID, true)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			if hostModel.IntegrationState != "succeeded" {
				status = graph.Status_not_start
			} else {
				status = graph.Status_running
			}
		} else {
			status = graph.Status_running
		}
	}
ret:
	if mrInfo.State == "closed" {
		status = graph.Status_failed
	}
	return graph.PopOverType_general, &graph.GeneralPopOver{
		Name:         "Merge",
		Status:       status,
		Type:         graph.DepType_Repo,
		SourceBranch: mrInfo.SourceBranch,
		TargetBranch: mrInfo.TargetBranch,
		LogUrl:       "",
		GitUrl:       mrInfo.Url,
		RetryUrl:     "",
		RetryBody:    nil,
		ErrorMsg:     nil,
		ID:           &mrInfo.ID,
	}, nil
}

func getHostIntegrationPopOver(ctx context.Context, meta *graph_node.NodeMeta) (graph.PopOverType, *graph.IntegrationPopOver, error) {
	// 我自己就是主仓
	hostMrInfo, err := data.GetMergeRequestInfoByMrID(ctx, meta.BusinessId, true)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	hostMrDependency, err := data.GetMergeRequestDependencyByProjectIDAndIIDRaw(ctx, hostMrInfo.ProjectID, hostMrInfo.IID, true)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	var status graph.Status
	switch hostMrDependency.IntegrationState {
	case "pending":
		status = graph.Status_waiting
	case "running":
		status = graph.Status_running
	case "failed":
		status = graph.Status_failed
	case "succeeded":
		status = graph.Status_success
	default:
		status = graph.Status_not_start
	}
	if status == graph.Status_waiting {
		status = graph.Status_not_start // 我也不懂为啥，老代码里面有这个逻辑，不敢动
	}
	var isPipelineIntegrationGray bool
	extra := make(map[string]interface{})
	err = json.Unmarshal([]byte(hostMrInfo.Extra), &extra)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshall extra:%s,%s", err.Error(), hostMrInfo.Extra)
		isPipelineIntegrationGray = false
	} else {
		if v, ok := extra["mr_integration_type"]; ok && v.(string) == "pipeline" {
			isPipelineIntegrationGray = true
		}
	}
	projectConfig, err := data.GetConfigProjectInfoByProjectIDRaw(ctx, hostMrInfo.ProjectID, true)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	if isPipelineIntegrationGray || projectConfig.IntegrationType == integrationType_pipeline {
		pipeline, err := data.GetPipelineInfoByID(ctx, hostMrInfo.IntegrationPipelineID)
		if err != nil {
			return graph.PopOverType_none, nil, err
		}
		res := graph.IntegrationPopOver{
			Label:             "Integration",
			Status:            status,
			ProgressNodes:     make([]*graph.PipelinePopOver, 0),
			StartTime:         0,
			EstimatedDuration: 0,
			RetryUrl:          "",
			RetryBody:         nil,
			Message:           "",
			Url:               "",
			ReleaseVersions:   nil,
		}
		if pipeline != nil {
			appid, _ := merge_request.GetMrAppId(ctx, meta.BusinessId)
			pipelineNode := graph.PipelinePopOver{
				Status:            convertPipelineStatusToIDL(pipeline.Status),
				RetryUrl:          "",
				StartTime:         pipeline.CreateTime.Unix(),
				EstimatedDuration: data.GetPipelineEstimatedTime(ctx, pipeline),
				Message:           "",
				Id:                &pipeline.ID,
				RetryBody:         nil,
				PipelineType:      graph.PipelineType(pipeline.PipelineType),
				Url:               data.GetPipelineUrl(ctx, pipeline, appid),
				Label:             "Pipeline",
				Type:              "pipeline",
				ProgressNodes:     nil,
			}
			sortedJobs, stages, err := data.GetLastJobByPipeline(ctx, pipeline)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			wg := sync.WaitGroup{}
			m := sync.Mutex{}
			jobNodes := make([]*graph.PipelineProgressNode, 0)
			for _, j := range sortedJobs {
				wg.Add(1)
				j := j
				go func() {
					defer wg.Done()
					jobNode, err := getPipelineJobNode(ctx, pipeline, j, false)
					if err != nil {
						return
					}
					if jobNode != nil {
						m.Lock()
						jobNodes = append(jobNodes, jobNode)
						m.Unlock()
					}
				}()
			}
			wg.Wait()
			pipelineNode.ProgressNodes = sortJobs(jobNodes, stages)
			if pipeline.Status != "not_started" {
				pipelineNode.RetryUrl = "rerun"
				str, _ := json.Marshal(map[string]interface{}{
					"project_id": hostMrInfo.ProjectID,
					"mr_iid":     hostMrInfo.IID,
				})
				pipelineNode.RetryBody = &graph.RetryBodyStruct{
					Data: string(str),
					Type: "pipeline",
				}
			}
			res.Status = convertPipelineStatusToIDL(pipeline.Status)
			r, err := getIntegrationVersionList(ctx, meta.BusinessId)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			res.ReleaseVersions = r
			res.ProgressNodes = append(res.ProgressNodes, &pipelineNode)
		}
		return graph.PopOverType_integration, &res, nil
	} else {
		var integrationTaskName string
		if projectConfig.IntegrationType == integrationType_dependency_table {
			switch projectConfig.AppType {
			case model.AppType_ios:
				integrationTaskName = "tasks.integration.IntegrationForIOSTask.IntegrationForIOSTask"
			case model.AppType_android:
				integrationTaskName = "tasks.integration.IntegrationForAndroidTask.IntegrationForAndroidTask"
			case model.AppType_harmony:
				integrationTaskName = "tasks.integration.IntegrationForHarmonyTask.IntegrationForHarmonyTask"
			case model.AppType_flutter:
				integrationTaskName = "tasks.integration.IntegrationForFlutterTask.IntegrationForFlutterTask"
			case model.AppType_cpp_app:
				integrationTaskName = "tasks.integration.IntegrationForCPPAppTask.IntegrationForCPPAppTask"
			}
		} else if projectConfig.IntegrationType == integrationType_submodule {
			integrationTaskName = "tasks.integration.IntegrationForSubmoduleTask.IntegrationForSubmoduleTask"
		}
		var url string
		if integrationTaskName != "" {
			task, err := data.GetMachineTaskByProjectIdAndIIdAndTaskName(ctx, hostMrInfo.ProjectID, hostMrInfo.IID, integrationTaskName)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			if task != nil {
				url = fmt.Sprintf("https://bits.bytedance.net/space/legacy/build/logs?legacyId=%s", task.TaskID)
			}
		}
		res := graph.IntegrationPopOver{
			Status:            status,
			Label:             "Integration",
			StartTime:         data.GetMergeRequestDependencyKeyTimePoint(hostMrDependency, "start_time"),
			EstimatedDuration: data.GetMergeRequestDependencyEstimatedDuration(ctx, hostMrDependency),
			RetryUrl:          "",
			RetryBody:         nil,
			Message:           "",
			Url:               url,
			ReleaseVersions:   nil,
			ProgressNodes:     nil,
		}
		if status != graph.Status_not_start {
			res.RetryUrl = "rerun"
			d := map[string]interface{}{
				"project_id": hostMrInfo.ProjectID,
				"mr_iid":     hostMrInfo.IID,
			}
			str, _ := json.Marshal(d)
			res.RetryBody = &graph.RetryBodyStruct{
				Data: string(str),
				Type: "integration",
			}
			r, err := getIntegrationVersionList(ctx, meta.BusinessId)
			if err != nil {
				return graph.PopOverType_none, nil, err
			}
			res.ReleaseVersions = r
		}
		integrationInfo := data.GetMergeRequestDependencyIntegrationInfoMap(ctx, hostMrDependency)
		if m, ok := integrationInfo["message"]; ok {
			res.Message = m.(string)
		}
		return graph.PopOverType_integration, &res, nil
	}
}

func getIntegrationVersionList(ctx context.Context, mrId int64) ([]*graph.ReleaseVersion, error) {
	deps, err := bits_optimus_infra.GetDepsForHost(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR, Role: infra.DependencyRole_Host})
	if err != nil {
		return nil, err
	}
	release := make([]*graph.ReleaseVersion, 0)
	hostMrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	m := sync.Mutex{}
	wg := sync.WaitGroup{}
	for _, dep := range deps.Dependencies {
		wg.Add(1)
		dep := dep
		go func() {
			defer wg.Done()
			if dep.Type == infra.DependencyType_MR {
				mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, dep.Id, true)
				if err != nil {
					return
				}
				if mrInfo == nil {
					log.V2.Warn().With(ctx).Str("found empty mr").KV("id", dep.Id).Error(err).Emit()
					return
				}
				pubVer, err := data.GetPublishVersionByProjectIDRawAndIID(ctx, mrInfo.ProjectID, mrInfo.IID, false)
				if err != nil {
					return
				}
				if pubVer == nil {
					return
				}
				projectConfig, err := data.GetConfigProjectInfoByProjectIDRaw(ctx, mrInfo.ProjectID, true)
				if err != nil {
					return
				}
				if hostMrInfo.MrRole != "public_dependency" && projectConfig.AppType != model.AppType_flutter && projectConfig.FlutterModule != 1 {
					componentInfo, err := getComponentInfo(ctx, pubVer)
					if err != nil {
						return
					}
					m.Lock()
					release = append(release, &graph.ReleaseVersion{
						ProjectName:   projectConfig.Name,
						Version:       pubVer.VersionFinal,
						VersionOrigin: pubVer.VersionOrigin,
						Component:     componentInfo,
						Role:          graph.MrRole_Deps,
					})
					m.Unlock()
				}
			} else { // pod
				versionDependency, err := data.GetVersionDependencyByID(ctx, dep.Id, true)
				if err != nil {
					return
				}
				if versionDependency == nil {
					logs.CtxWarn(ctx, "pod not exist:%d", dep.Id)
					return
				}
				type podInfoStruct struct {
					Builds        []interface{} `json:"builds"`
					RepoGroupName string        `json:"repoGroupName"`
					Id            int64         `json:"id"`
					CommitId      string        `json:"commitId"`
					Version       string        `json:"version"`
					RepoName      string        `json:"repoName"`
					RepoId        int64         `json:"repoId"`
				}
				var techStack int16
				podInfo := podInfoStruct{}
				err = json.Unmarshal([]byte(versionDependency.PodInfo), &podInfo)
				if err == nil {
					projectConfig, err := data.GetConfigProjectInfoByProjectID(ctx, podInfo.RepoId, true)
					if err == nil && projectConfig != nil {
						techStack = projectConfig.AppType
					} else {
						logs.CtxError(ctx, "projectConfig not found,vd podinfo:%s", versionDependency.PodInfo)
					}
				}

				m.Lock()
				release = append(release, &graph.ReleaseVersion{
					ProjectName:   versionDependency.Name,
					Version:       versionDependency.Version,
					VersionOrigin: versionDependency.VersionOrigin,
					Role:          graph.MrRole_PodChange,
					Component: []*graph.ComponentInfo{{
						Version:       versionDependency.Version,
						Name:          versionDependency.Name,
						VersionOrigin: versionDependency.Version,
						TechStack:     techStack,
						Branch:        "",
						Status:        "",
						Type:          "",
					}},
				})
				m.Unlock()
			}
		}()
	}
	dev, err := bits_optimus_infra.GetDevForDep(ctx, &infra.Dependency{Id: mrId, Type: infra.DependencyType_MR, Role: infra.DependencyRole_Host}, false)
	if err != nil {
		return nil, err
	}
	pubDeps, err := bits_optimus_infra.GetDevPublicDependencies(ctx, dev.Dev.Id)
	if err != nil {
		return nil, err
	}
	for _, dep := range pubDeps.PublicDependencies {
		wg.Add(1)
		dep := dep
		go func() {
			defer wg.Done()
			mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, dep.Id, true)
			if err != nil {
				return
			}
			pubVer, err := data.GetPublishVersionByProjectIDRawAndIID(ctx, mrInfo.ProjectID, mrInfo.IID, false)
			if err != nil {
				return
			}
			if pubVer == nil {
				return
			}
			projectConfig, err := data.GetConfigProjectInfoByProjectIDRaw(ctx, mrInfo.ProjectID, true)
			if err != nil {
				return
			}
			if hostMrInfo.MrRole != "public_dependency" && projectConfig.AppType != model.AppType_flutter && projectConfig.FlutterModule != 1 {
				componentInfo, err := getComponentInfo(ctx, pubVer)
				if err != nil {
					return
				}
				m.Lock()
				release = append(release, &graph.ReleaseVersion{
					ProjectName:   projectConfig.Name,
					Version:       pubVer.VersionFinal,
					VersionOrigin: pubVer.VersionOrigin,
					Component:     componentInfo,
					Role:          graph.MrRole_PubDeps,
				})
				m.Unlock()
			}
		}()
	}
	wg.Wait()
	return release, nil
}
