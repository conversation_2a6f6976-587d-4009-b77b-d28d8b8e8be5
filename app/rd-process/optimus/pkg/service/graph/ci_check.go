package graph

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/graph"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/gitlab"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/service/merge_request"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/pkg/utils"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/service/tcc"
	"code.byted.org/devinfra/hagrid/idls/protobuf/graph_node"
	"context"
	"fmt"
	json "github.com/bytedance/sonic"
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/lang/gg/choose"

	"code.byted.org/gopkg/logs"
)

type internalNode struct {
	Status    graph.Status
	Name      string
	StartTime *time.Time
	EndTime   *time.Time
}

func getSingleCiCheckNode(ctx context.Context, mrId int64) (*graph.NodeBrief, error) {
	nodeList := make([]*internalNode, 0)
	pipelineData, err := getMrPipelineNode(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if pipelineData != nil {
		nodeList = append(nodeList, pipelineData)
	}
	conflictCheckData, err := getSingleConflictCheckNode(ctx, mrId)
	if err != nil {
		return nil, err
	}
	nodeList = append(nodeList, conflictCheckData)
	if enable, err := enableSecurityCheck(ctx, mrId); enable || err != nil {
		if err != nil {
			return nil, err
		}
		securityCheckData, err := getSingleSecurityCheckNode(ctx, mrId)
		if err != nil {
			return nil, err
		}
		nodeList = append(nodeList, securityCheckData.internalNode)
	}

	projectConfig, err := data.GetConfigProjectInfoByMrId(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	if projectConfig == nil {
		return nil, bits_err.OPTIMUS.ErrProjectNotExist
	}
	groupConfig, err := data.GetConfigGroupInfoByID(ctx, projectConfig.MainGroupConfigID, true)
	if err != nil {
		return nil, err
	}
	if groupConfig == nil {
		return nil, bits_err.OPTIMUS.ErrGroupNotExist
	}
	startTime := calculateStartTime(nodeList).Unix()
	endTime := calculateEndTime(nodeList).Unix()
	if displayConfig, _ := tcc.GetGraphDisplayConfig(ctx); displayConfig != nil {
		if utils.Contains(displayConfig.GroupsHideCiCheckDuration, groupConfig.Name) {
			startTime = 0
			endTime = 0
		}
	}
	return &graph.NodeBrief{
		Id:         graph_node.GenerateNodeId(graph_node.Type_Repo, mrId, graph_node.Stage_ci_check),
		Position:   nil,
		Status:     calculateStatusInternalNode(nodeList),
		Name:       gitlab.GetGitlabProject(projectConfig.GitRepoAddr),
		Type:       graph.WorkFlowType_ci_check,
		StartTime:  startTime,
		EndTime:    endTime,
		ErrorCount: countError(nodeList),
	}, nil
}

const PipelineNodeName = "pipeline"

func getMrPipelineNode(ctx context.Context, mrId int64) (*internalNode, error) {
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	if mrInfo.LatestInternalID == -1 {
		if mrInfo.PipelinePass == 1 { // 没开启pipeline
			return nil, nil
		}
		return &internalNode{
			Status: graph.Status_waiting,
			Name:   PipelineNodeName,
		}, nil
	}
	skip, err := isSkipPipelineForMr(ctx, mrId)
	if err != nil {
		return nil, err
	}
	if skip {
		return &internalNode{
			Status: graph.Status_success,
			Name:   PipelineNodeName,
		}, nil
	}
	pipelineInfo, err := data.GetPipelineInfoByID(ctx, mrInfo.LatestInternalID)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipelineInfo:%s", err.Error())
		return nil, err
	}
	if pipelineInfo == nil {
		return &internalNode{
			Status: graph.Status_waiting,
			Name:   PipelineNodeName,
		}, nil
	}
	var status graph.Status
	switch pipelineInfo.Status {
	case "not_started":
		status = graph.Status_not_start
	case "pending", "running":
		status = graph.Status_running
	case "success":
		status = graph.Status_success
	case "failed":
		status = graph.Status_failed
	case "canceled":
		status = graph.Status_skipped
	case "skipped":
		status = graph.Status_skipped
	default:
		status = graph.Status_unknown
	}
	return &internalNode{
		Status:    status,
		Name:      PipelineNodeName,
		StartTime: pipelineInfo.GetStartAt(),
		EndTime:   pipelineInfo.GetFinishAt(),
	}, nil
}

const ConflictCheckNodeName = "conflict_check"

func getSingleConflictCheckNode(ctx context.Context, mrId int64) (*internalNode, error) {
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	var status graph.Status
	switch mrInfo.Conflicted {
	case model.CONFLICTED_UNCHECKED:
		status = graph.Status_not_start
	case model.CONFLICTED_CHECKING:
		status = graph.Status_running
	case model.CONFLICTED_CONFLICTED:
		status = graph.Status_failed
	case model.CONFLICTED_NOT_CONFLICTED:
		status = graph.Status_success
	case model.CONFLICTED_CHECK_FAILED:
		status = graph.Status_failed
	}
	if mrInfo.State == "merged" {
		status = graph.Status_success
	}
	var startTime *time.Time
	var endTime *time.Time
	config, _ := tcc.GetConflictDetectTaskConfig(ctx)
	if config != nil && config.UseCloudBuild {
		groupConfig, _ := data.GetConfigGroupInfoByProjectIDRaw(ctx, mrInfo.ProjectID, true)
		if groupConfig == nil {
			logs.CtxError(ctx, "group config not found")
			return nil, bits_err.OPTIMUS.ErrGroupNotExist
		}

		if utils.Contains(config.UseCloudBuildGroups, "all") || utils.Contains(config.UseCloudBuildGroups, groupConfig.Name) {
			for _, fc := range config.FastCheck {
				if fc.GroupName == groupConfig.Name {
					if status == graph.Status_success {
						logs.CtxInfo(ctx, "fast check")
						return &internalNode{
							Status:    status,
							Name:      ConflictCheckNodeName,
							StartTime: startTime,
							EndTime:   endTime,
						}, nil
					}
				}
			}
		}

		// cloud build task
		if utils.Contains(config.UseCloudBuildGroups, "all") || utils.Contains(config.UseCloudBuildGroups, groupConfig.Name) {
			task, err := data.GetCloudBuildMachineTaskByProjectIdAndIidAndTaskNameLatest(ctx, mrInfo.ProjectID, mrInfo.IID, consts.CloudBuildMachineTaskNameConflictDetectTask, true)
			if err != nil {
				logs.CtxError(ctx, "get cloud build machine task failed error=%s", err.Error())
				return nil, err
			}
			if task != nil {
				startTime = &task.CreateTime
				endTime = &task.UpdateTime
				status = transTaskStatusToGraphStatus(task.Status, task.Result)
				return &internalNode{
					Status:    status,
					Name:      ConflictCheckNodeName,
					StartTime: startTime,
					EndTime:   endTime,
				}, nil
			}
		}
	}
	// celery task
	lastTask, err := data.GetMachineTaskByProjectIdAndIIdAndTaskName(ctx, mrInfo.ProjectID, mrInfo.IID, "tasks.repo.ConflictDetectTask.ConflictDetectTask")
	if err != nil {
		return nil, err
	}
	if lastTask != nil {
		startTime = &lastTask.CreateTime
		endTime = &lastTask.UpdateTime
		status = transTaskStatusToGraphStatus(lastTask.Status, lastTask.Result)
	}

	return &internalNode{
		Status:    status,
		Name:      ConflictCheckNodeName,
		StartTime: startTime,
		EndTime:   endTime,
	}, nil
}
func transTaskStatusToGraphStatus(taskStatus string, resultStr string) graph.Status {
	status := graph.Status_not_start
	switch taskStatus {
	case "pending":
		status = graph.Status_not_start
	case "running":
		status = graph.Status_running
	case "failed":
		status = graph.Status_failed
	case "succeeded":
		result := make(map[string]interface{})
		err := json.Unmarshal([]byte(resultStr), &result)
		if err == nil {
			if v, ok := result["conflicted"]; ok {
				if vResult, ok := v.(bool); ok && vResult == true {
					status = graph.Status_failed
				} else {
					status = graph.Status_success
				}
			}
		}
	case "canceled":
		status = graph.Status_skipped
	}
	return status
}

type checkNode struct {
	*internalNode
	Msg string
}

const SecurityCheckNodeName = "security_check"

func getSingleSecurityCheckNode(ctx context.Context, mrId int64) (*checkNode, error) {
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	// 时间不准
	if mrInfo.SecurityPass && mrInfo.LastSecurityTaskID == 0 {
		return &checkNode{
			internalNode: &internalNode{
				Status: graph.Status_success,
				Name:   SecurityCheckNodeName,
			},
		}, nil
	}
	securityTask, err := data.GetThirdPartyCheckTaskById(ctx, mrInfo.LastSecurityTaskID)
	if err != nil {
		return nil, err
	}
	if securityTask == nil {
		return &checkNode{
			internalNode: &internalNode{
				Status: graph.Status_not_start,
				Name:   SecurityCheckNodeName,
			},
		}, nil
	}
	var status graph.Status
	var msg string
	switch securityTask.Status {
	case "failure":
		status = graph.Status_failed
		msg = "This task has exited with exceptions. Please retry."
	case "unqualified":
		status = graph.Status_warning
		msg = "This task is waiting for approval."
	case "success", "succeeded":
		status = graph.Status_success
	case "running":
		status = graph.Status_running
	case "pending":
		status = graph.Status_waiting
	default:
		status = graph.Status_unknown
	}
	var startTime, endTime *time.Time
	if securityTask.StartedAt != "" {
		s, err := strconv.ParseInt(securityTask.StartedAt, 10, 64)
		if err == nil {
			t := time.Unix(s, 0)
			startTime = &t
		}
	}
	if securityTask.EndedAt != "" {
		e, err := strconv.ParseInt(securityTask.EndedAt, 10, 64)
		if err == nil {
			t := time.Unix(e, 0)
			endTime = &t
		}
	}
	return &checkNode{&internalNode{
		Status:    status,
		Name:      SecurityCheckNodeName,
		StartTime: startTime,
		EndTime:   endTime,
	}, msg}, nil
}

type conflictResult struct {
	Exception          interface{} `json:"exception" gorm:"column:exception"`
	Conflicted         bool        `json:"conflicted" gorm:"column:conflicted"`
	TargetBranchCommit string      `json:"target_branch_commit" gorm:"column:target_branch_commit"`
	ConflictFiles      []struct {
		ConflictFile         string `json:"conflict_file" gorm:"column:conflict_file"`
		ConflictType         string `json:"conflict_type" gorm:"column:conflict_type"`
		ConflictFileMimeType string `json:"conflict_file_mime_type" gorm:"column:conflict_file_mime_type"`
		ConflictFileUrl      string `json:"conflict_file_url" gorm:"column:conflict_file_url"`
		ConflictBlameFileUrl string `json:"conflict_blame_file_url" gorm:"column:conflict_blame_file_url"`
	} `json:"conflict_files" gorm:"column:conflict_files"`
	SourceBranchCommit string `json:"source_branch_commit" gorm:"column:source_branch_commit"`
	Succeed            bool   `json:"succeed" gorm:"column:succeed"`
	Message            string `json:"message" gorm:"column:message"`
}

func getConflictCheckPopOver(ctx context.Context, mrId int64) (*graph.GeneralPopOver, error) {
	var taskID int64
	res := &graph.GeneralPopOver{
		Name:         "Conflict Check",
		Status:       0,
		Type:         graph.DepType_Repo,
		SourceBranch: "",
		TargetBranch: "",
		LogUrl:       "",
		GitUrl:       "",
		RetryUrl:     "",
		RetryBody:    nil,
		ErrorMsg:     make([]*graph.ErrorMsg, 0),
		ID:           &taskID,
	}
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	res.SourceBranch = mrInfo.SourceBranch
	res.TargetBranch = mrInfo.TargetBranch
	res.GitUrl = mrInfo.Url
	res.RetryUrl = fmt.Sprintf("projects/%s/merge_requests/%d", mrInfo.ProjectID, mrInfo.IID)
	res.RetryBody = &graph.RetryBodyStruct{
		Data: `{"action":"CONFLICT_DETECT"}`,
		Type: "conflict_check",
	}
	var status graph.Status
	switch mrInfo.Conflicted {
	case model.CONFLICTED_UNCHECKED:
		status = graph.Status_not_start
	case model.CONFLICTED_CHECKING:
		status = graph.Status_running
	case model.CONFLICTED_CONFLICTED:
		status = graph.Status_failed
	case model.CONFLICTED_NOT_CONFLICTED:
		status = graph.Status_success
	case model.CONFLICTED_CHECK_FAILED:
		status = graph.Status_failed
	}
	if mrInfo.State == "merged" {
		status = graph.Status_success
	}
	res.Status = status
	config, _ := tcc.GetConflictDetectTaskConfig(ctx)
	if config != nil && config.UseCloudBuild {
		groupConfig, _ := data.GetConfigGroupInfoByProjectIDRaw(ctx, mrInfo.ProjectID, true)
		if groupConfig == nil {
			logs.CtxError(ctx, "group config not found")
			return res, nil
		}

		if utils.Contains(config.UseCloudBuildGroups, "all") || utils.Contains(config.UseCloudBuildGroups, groupConfig.Name) {
			for _, fc := range config.FastCheck {
				if fc.GroupName == groupConfig.Name {
					if status == graph.Status_success {
						logs.CtxInfo(ctx, "fast check")
						return res, nil
					}
				}
			}
		}

		// cloud build task
		if utils.Contains(config.UseCloudBuildGroups, "all") || utils.Contains(config.UseCloudBuildGroups, groupConfig.Name) {
			appid, _ := merge_request.GetMrAppId(ctx, mrId)
			taskInfo, err := getConflictDetectCloudBuildTaskGraphInfo(ctx, mrInfo.ProjectID, mrInfo.IID, appid)
			if err != nil {
				logs.CtxError(ctx, "conflict check cloud build task error:%s", err.Error())
				return res, nil
			}
			logs.CtxInfo(ctx, "get cloud build task info:%v", *taskInfo)
			res.Status = taskInfo.Status
			res.LogUrl = taskInfo.LogURL
			res.ID = &taskInfo.PipelineID
			if taskInfo.ErrMsg != nil {
				res.ErrorMsg = append(res.ErrorMsg, taskInfo.ErrMsg)
			}
			return res, nil
		}
	}
	// if it's celery task
	taskInfo, err := getConflictDetectCeleryTaskGraphInfo(ctx, mrInfo.ProjectID, mrInfo.IID)
	if err != nil {
		logs.CtxError(ctx, "conflict check celery task error:%s", err.Error())
		return res, nil
	}
	logs.CtxInfo(ctx, "get cloud build task info:%v", *taskInfo)
	res.Status = taskInfo.Status
	res.LogUrl = taskInfo.LogURL
	if taskInfo.ErrMsg != nil {
		res.ErrorMsg = append(res.ErrorMsg, taskInfo.ErrMsg)
	}
	return res, nil
}

func getSecurityCheckPopOver(ctx context.Context, mrId int64) (*graph.GeneralPopOver, error) {
	res := graph.GeneralPopOver{
		Name:         "Security Check",
		Status:       0,
		Type:         graph.DepType_Repo,
		SourceBranch: "",
		TargetBranch: "",
		LogUrl:       "",
		GitUrl:       "",
		RetryUrl:     "",
		RetryBody:    nil,
		ErrorMsg:     nil,
	}
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	res.SourceBranch = mrInfo.SourceBranch
	res.TargetBranch = mrInfo.TargetBranch
	res.GitUrl = mrInfo.Url
	if mrInfo.SecurityPass && mrInfo.LastSecurityTaskID == 0 {
		res.Status = graph.Status_success
		return &res, err
	}
	var status graph.Status
	var msg string
	securityTask, err := data.GetThirdPartyCheckTaskById(ctx, mrInfo.LastSecurityTaskID)
	if err != nil {
		return nil, err
	}
	if securityTask == nil {
		res.Status = graph.Status_waiting
	} else {
		switch securityTask.Status {
		case "failure":
			status = graph.Status_failed
			msg = "This task has exited with exceptions. Please retry."
		case "unqualified":
			status = graph.Status_warning
			msg = "This task is waiting for approval."
		case "success", "succeeded":
			status = graph.Status_success
		case "running":
			status = graph.Status_running
		case "pending":
			status = graph.Status_waiting
		default:
			status = graph.Status_unknown
		}
		appID, _ := merge_request.GetMrAppId(ctx, mrId)
		res.LogUrl = choose.If(appID > 0, fmt.Sprintf(securityTask.DetailUrl+"&appId=%d", appID), securityTask.DetailUrl)
		if securityTask.Message != "" {
			msg = securityTask.Message
		}
		if msg != "" {
			res.ErrorMsg = []*graph.ErrorMsg{{msg, "", graph.MessageType_plainText}}
		}
		res.Status = status
		res.RetryUrl = fmt.Sprintf("third_party_check/tasks/%d/retry", securityTask.ID)
	}
	return &res, nil
}

const TranslationCheckNodeName = "translate_check"

func getSingleTranslationCheckNode(ctx context.Context, mrId int64) (*checkNode, error) {
	mrInfo, err := data.GetMergeRequestInfoByMrID(ctx, mrId, true)
	if err != nil {
		return nil, err
	}
	translationTask, err := data.GetMrCodebaseTaskById(ctx, mrInfo.LatestTranslationTaskID)
	if err != nil {
		return nil, err
	}
	if translationTask == nil {
		return &checkNode{&internalNode{
			Status: graph.Status_not_start,
			Name:   TranslationCheckNodeName,
		}, ""}, nil
	}
	var status graph.Status
	var msg string
	switch translationTask.CheckStatus {
	case "failure":
		status = graph.Status_failed
		msg = "This task has exited with exceptions. Please retry."
	case "error":
		status = graph.Status_failed
		msg = "Find Chinese words. Please check the results."
	case "pending":
		status = graph.Status_waiting
	case "success":
		status = graph.Status_success
	default:
		status = graph.Status_unknown
	}
	return &checkNode{&internalNode{
		Status:    status,
		Name:      TranslationCheckNodeName,
		StartTime: &translationTask.CreateTime,
		EndTime:   &translationTask.UpdateTime,
	}, msg}, nil
}

func getCiCheckPodNode(ctx context.Context, podId int64) (*graph.NodeBrief, error) {
	versionDependency, err := data.GetVersionDependencyByID(ctx, podId, true)
	if err != nil {
		return nil, err
	}
	if versionDependency == nil {
		logs.CtxWarn(ctx, "pod not found:%d", podId)
		return nil, nil
	}
	stageName := versionDependency.Name
	if versionDependency.PipelinePass == 1 {
		logs.CtxInfo(ctx, "pipeline pass is %d, skip...", versionDependency.PipelinePass)
		return &graph.NodeBrief{
			Id:          graph_node.GenerateNodeId(graph_node.Type_Pod, podId, graph_node.Stage_ci_check),
			Position:    nil,
			Status:      graph.Status_success,
			Name:        stageName,
			Type:        graph.WorkFlowType_ci_check,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
			ErrorCount:  nil,
		}, nil
	} else {
		if versionDependency.LatestInternalId == -1 {
			return &graph.NodeBrief{
				Id:          graph_node.GenerateNodeId(graph_node.Type_Pod, podId, graph_node.Stage_ci_check),
				Position:    nil,
				Status:      graph.Status_not_start,
				Name:        stageName,
				Type:        graph.WorkFlowType_ci_check,
				StartTime:   0,
				EndTime:     0,
				Collapsable: false,
				ErrorCount:  nil,
			}, nil
		} else {
			pipeline, err := data.GetPipelineInfoByID(ctx, versionDependency.LatestInternalId)
			if err != nil {
				return nil, err
			}
			if pipeline == nil {
				return &graph.NodeBrief{
					Id:          graph_node.GenerateNodeId(graph_node.Type_Pod, podId, graph_node.Stage_ci_check),
					Position:    nil,
					Status:      graph.Status_not_start,
					Name:        stageName,
					Type:        graph.WorkFlowType_ci_check,
					StartTime:   0,
					EndTime:     0,
					Collapsable: false,
					ErrorCount:  nil,
				}, nil
			}
			var status graph.Status
			switch pipeline.Status {
			case "not_started":
				status = graph.Status_not_start
			case "pending", "running":
				status = graph.Status_running
			case "success":
				status = graph.Status_success
			case "failed":
				status = graph.Status_failed
			case "canceled":
				status = graph.Status_skipped
			case "skipped":
				status = graph.Status_skipped
			default:
				status = graph.Status_unknown
			}
			return &graph.NodeBrief{
				Id:          graph_node.GenerateNodeId(graph_node.Type_Pod, podId, graph_node.Stage_ci_check),
				Position:    nil,
				Status:      status,
				Name:        stageName,
				Type:        graph.WorkFlowType_ci_check,
				StartTime:   pipeline.CreateTime.Unix(),
				EndTime:     pipeline.UpdateTime.Unix(),
				Collapsable: false,
				ErrorCount:  countErrorPodChange(status),
			}, nil
		}
	}
}

func countErrorPodChange(status graph.Status) *int64 {
	if status == graph.Status_failed {
		tmp := int64(1)
		return &tmp
	}
	return nil
}

func getCiCheckPopOver(ctx context.Context, meta *graph_node.NodeMeta) (graph.PopOverType, *graph.CiCheckPopOverData, error) {
	if meta.Type == graph_node.Type_Repo {
		return getCiCheckRepoPopOver(ctx, meta)
	} else {
		return getCiCheckPodChangePopOver(ctx, meta)
	}
}

func getCiCheckRepoPopOver(ctx context.Context, meta *graph_node.NodeMeta) (graph.PopOverType, *graph.CiCheckPopOverData, error) {
	pipeline, err := getRepoCiCheckPipelinePopOver(ctx, meta.BusinessId)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	conflictCheck, err := getConflictCheckPopOver(ctx, meta.BusinessId)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	var securityCheck *graph.GeneralPopOver
	if enable, err := enableSecurityCheck(ctx, meta.BusinessId); enable || err != nil {
		securityCheck, err = getSecurityCheckPopOver(ctx, meta.BusinessId)
		if err != nil {
			return graph.PopOverType_none, nil, err
		}
	}
	return graph.PopOverType_ci_check, &graph.CiCheckPopOverData{
		ConflictCheck: conflictCheck,
		Pipeline:      pipeline,
		SecurityCheck: securityCheck,
	}, nil
}

func getCiCheckPodChangePopOver(ctx context.Context, meta *graph_node.NodeMeta) (graph.PopOverType, *graph.CiCheckPopOverData, error) {
	pipeline, err := getPodChangeCiCheckPipelinePopOver(ctx, meta.BusinessId)
	if err != nil {
		return graph.PopOverType_none, nil, err
	}
	return graph.PopOverType_ci_check, &graph.CiCheckPopOverData{
		ConflictCheck: nil,
		Pipeline:      pipeline,
		SecurityCheck: nil,
	}, nil
}

func getMergeTrainCiCheckPipelineNode(ctx context.Context, mrID int64) (*internalNode, error) {
	res, err := data.GetCommitQueuePipelineByBitsMRIDAndOtherStageLatest(ctx, mrID, "pre_submit")
	if err != nil {
		return nil, err
	}
	status := graph.Status_not_start
	if res == nil || res.PipelineId == 0 {
		return &internalNode{
			Status: status,
			Name:   MergeTrainPipelineName,
		}, nil
	}
	return &internalNode{
		Status: transMergeTrainPipelineStatusToGraphStatus(res.Status),
		Name:   MergeTrainPipelineName,
	}, nil
}

type CloudBuildTaskGraphInfo struct {
	Status     graph.Status
	LogURL     string
	ErrMsg     *graph.ErrorMsg
	PipelineID int64
}

func getConflictDetectCeleryTaskGraphInfo(ctx context.Context, projectID string, iid int64) (*CloudBuildTaskGraphInfo, error) {
	answer := &CloudBuildTaskGraphInfo{
		Status: graph.Status_not_start,
		LogURL: "",
	}
	lastTask, err := data.GetMachineTaskByProjectIdAndIIdAndTaskName(ctx, projectID, iid, "tasks.repo.ConflictDetectTask.ConflictDetectTask")
	if err != nil {
		return nil, err
	}
	if lastTask == nil {
		return nil, bits_err.COMMON.ErrRecordNotFound.AddErrMsg("task isn't existed")
	}
	answer.LogURL = fmt.Sprintf("https://bits.bytedance.net/space/legacy/build/logs?legacyId=%s", lastTask.TaskID)
	answer.Status = transTaskStatusToGraphStatus(lastTask.Status, lastTask.Result)
	result := conflictResult{}
	err = json.Unmarshal([]byte(lastTask.Result), &result)
	if err == nil && result.Conflicted {
		msg := result.Message
		for i, file := range result.ConflictFiles {
			if i != 0 {
				msg += "\n"
			}
			msg += fmt.Sprintf(`- [%s](%s)`, file.ConflictFile, file.ConflictBlameFileUrl)
		}
		answer.ErrMsg = &graph.ErrorMsg{Msg: msg, Link: "", Type: graph.MessageType_markdown}
	}
	return answer, nil
}
func getConflictDetectCloudBuildTaskGraphInfo(ctx context.Context, projectID string, iid int64, appID int64) (*CloudBuildTaskGraphInfo, error) {
	answer := &CloudBuildTaskGraphInfo{
		Status: graph.Status_not_start,
		LogURL: "",
	}
	task, err := data.GetCloudBuildMachineTaskByProjectIdAndIidAndTaskNameLatest(ctx, projectID, iid, consts.CloudBuildMachineTaskNameConflictDetectTask, true)
	if err != nil {
		logs.CtxError(ctx, "get cloud build machine task failed error=%s", err.Error())
		return nil, err
	}
	if task == nil {
		return nil, bits_err.COMMON.ErrRecordNotFound.AddErrMsg("cloud build task isn't existed")
	}
	logs.CtxInfo(ctx, "get cloud build task db info:%v", *task)
	answer.LogURL = choose.If(appID > 0, fmt.Sprintf("https://bits.bytedance.net/bytebus/devops/code/pipeline?pipeline_id=%s&tab=dag&appId=%d", task.TaskId, appID), fmt.Sprintf("https://bits.bytedance.net/bytebus/devops/code/pipeline?pipeline_id=%s&tab=dag", task.TaskId))
	answer.Status = transTaskStatusToGraphStatus(task.Status, task.Result)
	pplID, err := strconv.ParseInt(task.TaskId, 10, 64)
	if err != nil {
		logs.CtxError(ctx, "parse task id failed error = %s task id = %s", err.Error(), task.TaskId)
	}
	answer.PipelineID = pplID
	result := conflictResult{}
	err = json.Unmarshal([]byte(task.Result), &result)
	if err == nil && result.Conflicted {
		msg := result.Message
		for i, file := range result.ConflictFiles {
			if i != 0 {
				msg += "\n"
			}
			msg += fmt.Sprintf(`- [%s](%s)`, file.ConflictFile, file.ConflictBlameFileUrl)
		}
		answer.ErrMsg = &graph.ErrorMsg{Msg: msg, Link: "", Type: graph.MessageType_markdown}
	}
	return answer, nil
}
