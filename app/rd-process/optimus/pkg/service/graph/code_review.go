package graph

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/graph"
	"context"
	"code.byted.org/devinfra/hagrid/idls/protobuf/graph_node"
	"time"

	"code.byted.org/overpass/bytedance_bits_code_review/rpc/bytedance_bits_code_review"
	"code.byted.org/overpass/bytedance_bits_qa_review/kitex_gen/bytedance/bits/qa_review"
	"code.byted.org/overpass/bytedance_bits_qa_review/rpc/bytedance_bits_qa_review"
	"code.byted.org/overpass/common/option/calloption"
)

func GetCodeReviewNode(ctx context.Context, hostMr int64) (map[int64]*graph.NodeBrief, error) {
	var cr = true
	var qa = true
	var name = "Code Review"

	// CR 状态
	reviewStatus, err := bytedance_bits_code_review.CheckStatus(ctx, hostMr, nil, calloption.WithRPCTimeout(5*time.Second))
	if err != nil {
		return nil, err
	}
	cr = reviewStatus.GetStatus()

	// QA 状态
	enableResp, err := bytedance_bits_qa_review.CheckEnable(ctx, hostMr, calloption.WithRPCTimeout(5*time.Second))
	if err != nil {
		return nil, err
	}
	if enableResp.GetEnable() {
		qaResp, e := bytedance_bits_qa_review.RawCall.GetStatus(ctx, &qa_review.GetStatusRequest{
			DevId: 0,
			MrId:  &hostMr,
		}, calloption.WithRPCTimeout(5*time.Second))
		if e != nil {
			return nil, e
		}
		qa = qaResp.GetApproved()
		name = "manual review"
	}

	// 总状态
	var status graph.Status
	if cr && qa {
		status = graph.Status_success
	} else {
		status = graph.Status_warning
	}
	return map[int64]*graph.NodeBrief{
		hostMr: {
			Id:          graph_node.GenerateNodeId(graph_node.Type_Repo, hostMr, graph_node.Stage_code_review),
			Position:    nil,
			Status:      status,
			Name:        name,
			Type:        graph.WorkFlowType_code_review,
			StartTime:   0,
			EndTime:     0,
			Collapsable: false,
		},
	}, nil
}
