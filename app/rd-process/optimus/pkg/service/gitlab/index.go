package gitlab

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/rpc"
	"context"
	"regexp"
	"strings"

	"code.byted.org/gopkg/logs"
)

func DeleteProjectBranch(ctx context.Context) error {
	return nil
}
func InsertProjectBranch(ctx context.Context) error {
	return nil
}

// Get group from git url

var groupReg = regexp.MustCompile("(.+)/")

func getSegFromGitURL(reg *regexp.Regexp, url string) string {
	if !strings.HasSuffix(url, ".git") {
		url += ".git"
	}
	url = strings.TrimPrefix(url, "https://")
	url = strings.TrimPrefix(url, "ssh://")
	url = strings.TrimPrefix(url, "git://")
	res := reg.FindStringSubmatch(url)
	if len(res) > 1 {
		return res[1]
	}
	return ""
}
func GetGitlabGroup(gitURL string) string {
	return getSegFromGitURL(groupReg, gitURL)
}

var projectReg = regexp.MustCompile("^.*/(.+).git")

func GetGitlabProject(gitURL string) string {
	return getSegFromGitURL(projectReg, gitURL)
}

func CreateMR(ctx context.Context, projectID int64, source, target, author, title string) (*git_server.MergeRequest, error) {
	removeSourceBranch := false
	change, err := rpc.GitClient.CreateMergeRequest(ctx, &git_server.CreateMergeRequestRequest{
		ProjectId:          projectID,
		SourceBranch:       source,
		TargetBranch:       target,
		Title:              title,
		RemoveSourceBranch: &removeSourceBranch,
	})
	if err != nil {
		logs.CtxError(ctx, "failed to create change:%s", err.Error())
		// 看看是否存在
		status := git_server.ChangeState_open
		exist, err2 := rpc.GitClient.SearchChange(ctx, &git_server.SearchChangeRequest{
			Username: &author,
			Options: &git_server.SearchChangeOptions{
				SourceRepoId: &projectID,
				SourceBranch: &source,
				TargetBranch: &target,
				Status:       &status,
			},
		})
		if err2 != nil {
			return nil, err2
		}
		if len(exist.Changes) > 0 && exist.Changes[0].Source.Ref == source { // 存在直接沿用
			change.MergeRequest = &git_server.MergeRequest{
				Id:  int32(exist.Changes[0].Id),
				Iid: int32(exist.Changes[0].Iid),
				Author: &git_server.BasicUser{
					Username: exist.Changes[0].Author.Username,
				},
			}
		} else {
			logs.CtxError(ctx, "failed to create change:%s", err.Error())
			return nil, err
		}
	}
	return change.MergeRequest, nil
}
