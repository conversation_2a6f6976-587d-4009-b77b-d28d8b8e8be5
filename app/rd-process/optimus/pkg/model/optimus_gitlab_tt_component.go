package model

import (
	"context"
	"strconv"
	"time"

	"code.byted.org/gopkg/logs"
)

const (
	TtComponentStatusFailed  = "failed"
	TtComponentStatusRunning = "running"
	TtComponentStatusPending = "pending"
	TtComponentStatusSucceed = "succeeded"
)

const OptimusGitlabTtComponentModelTableName = "optimus_gitlab_tt_component_model"

type OptimusGitlabTtComponent struct {
	ID            int64      `gorm:"column:id" json:"id"`
	CreateTime    *time.Time `gorm:"column:create_time" json:"create_time"`
	UpdateTime    *time.Time `gorm:"column:update_time" json:"update_time"`
	ProjectID     string     `gorm:"column:project_id" json:"project_id"`
	ComponentID   string     `gorm:"column:component_id" json:"component_id"`
	RepoName      string     `gorm:"column:repo_name" json:"repo_name"`
	RepoGroupName string     `gorm:"column:repo_group_name" json:"repo_group_name"`
	ModuleName    string     `gorm:"column:module_name" json:"module_name"`
	FlavorType    int64      `gorm:"column:flavor_type" json:"flavor_type"`
	Target        string     `gorm:"column:target" json:"target"`
	TechType      int16      `gorm:"column:repo_tech_type" json:"repo_tech_type"`
}

func (c OptimusGitlabTtComponent) TableName() string {
	return OptimusGitlabTtComponentModelTableName
}

func (c *OptimusGitlabTtComponent) GetComponentIDNumber(ctx context.Context) int64 {
	if c == nil {
		return 0
	}
	res, err := strconv.ParseInt(c.ComponentID, 10, 64)
	if err != nil {
		logs.CtxError(ctx, "OptimusGitlabTtComponent component_id parse error: project_id=%s, err=%s", c.ComponentID, err.Error())
	}
	return res
}
