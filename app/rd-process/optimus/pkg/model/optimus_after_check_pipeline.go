package model

import "time"

type OptimusAfterCheckPipeline struct {
	ID         int64     `json:"id" gorm:"column:id"`                   // 自增ID
	MrIID      int64     `json:"mr_iid" gorm:"column:mr_iid"`           // mr_iid
	ProjectID  string    `json:"project_id" gorm:"column:project_id"`   // project_id
	Status     string    `json:"status" gorm:"column:status"`           // status
	CreateTime time.Time `json:"create_time" gorm:"column:create_time"` // 创建时间
	UpdateTime time.Time `json:"update_time" gorm:"column:update_time"` // 更新时间
	URL        string    `json:"url" gorm:"column:url"`                 // 任务地址
}

func (m *OptimusAfterCheckPipeline) TableName() string {
	return "optimus_after_check_pipeline"
}
