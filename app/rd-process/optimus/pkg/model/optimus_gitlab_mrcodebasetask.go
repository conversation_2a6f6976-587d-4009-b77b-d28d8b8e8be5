package model

import "time"

type OptimusGitlabMrcodebasetask struct {
	CreateTime     time.Time `json:"create_time" gorm:"column:create_time"`
	UpdateTime     time.Time `json:"update_time" gorm:"column:update_time"`
	ID             uint64    `json:"id" gorm:"column:id"`
	MrID           uint64    `json:"mr_id" gorm:"column:mr_id"`
	CommitID       string    `json:"commit_id" gorm:"column:commit_id"`
	TaskType       string    `json:"task_type" gorm:"column:task_type"`
	Status         string    `json:"status" gorm:"column:status"`
	Message        string    `json:"message" gorm:"column:message"`
	RepoID         uint64    `json:"repo_id" gorm:"column:repo_id"`
	AnalyzerID     int64     `json:"analyzer_id" gorm:"column:analyzer_id"`
	AnalyzerName   string    `json:"analyzer_name" gorm:"column:analyzer_name"`
	AnalyzerTaskID int64     `json:"analyzer_task_id" gorm:"column:analyzer_task_id"`
	StartedAt      string    `json:"started_at" gorm:"column:started_at"`
	EndedAt        string    `json:"ended_at" gorm:"column:ended_at"`
	CheckStatus    string    `json:"check_status" gorm:"column:check_status"` // check_status
	DetailUrl      string    `json:"detail_url" gorm:"column:detail_url"`     // detail_url
}

func (m *OptimusGitlabMrcodebasetask) TableName() string {
	return "optimus_gitlab_mrcodebasetask"
}
