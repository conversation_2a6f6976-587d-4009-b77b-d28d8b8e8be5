package git

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/optimus/rpc"
	"code.byted.org/gopkg/logs"
	"context"
	"strings"
)

func CreateBranch(ctx context.Context, projectID int64, branchName, ref string) error {
	_, err := rpc.GitClient.CreateBranch(ctx, &git_server.CreateBranchRequest{ProjectId: int32(projectID), Options: &git_server.CreateBranchOptions{Branch: &branchName, Ref: &ref}})
	if err != nil {
		logs.CtxError(ctx, "failed to create branch:%s", err.Error())
		if !strings.Contains(err.Error(), "Branch already exists") {
			return err
		}
		branch, err2 := rpc.GitClient.GetBranch(ctx, &git_server.GetBranchRequest{ProjectId: int32(projectID), Branch: branchName})
		if err2 != nil {
			logs.CtxError(ctx, "failed to get branch:%s", err2.Error())
			return err
		}
		if branch.Branch != nil {
			logs.CtxWarn(ctx, "branch %s existed", branchName)
			return nil
		}
	}
	return nil
}
