package configservice

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/config_service/configservice"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/backends/opts"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/optional"
)

type client struct {
	inner configservice.Client
}

func New() Api {
	options := opts.NewRPCOptions(false)

	return &client{
		inner: configservice.MustNewClient("bytedance.bits.config_service", options...),
	}
}

var ErrWorkflowNotFound = errors.New("workflow config not found")

func (client *client) GetOneSiteWorkflow(ctx context.Context, workflowId, workflowSnapshotId int64) gresult.R[*config_service.OnesiteWorkflow] {
	req := &config_service.GetOnesiteWorkflowRequest{
		WorkflowId:         choose.If(workflowSnapshotId > 0, 0, workflowId), // 也就是说, 如果 workflow snapshot id 有的话, 就不使用 workflow id
		WorkflowSnapshotId: workflowSnapshotId,
		HandleHidden:       true, // 隐藏未开启 task
		OnlyEnabledNode:    true,
	}

	resp, err := client.inner.GetOnesiteWorkflow(ctx, req)
	if err != nil {
		return gresult.Err[*config_service.OnesiteWorkflow](err)
	}
	if resp == nil ||
		resp.Workflow == nil ||
		resp.Workflow.WorkflowConfig == nil ||
		resp.Workflow.WorkflowConfig.DevTaskWorkflowConfig == nil {
		return gresult.Err[*config_service.OnesiteWorkflow](ErrWorkflowNotFound)
	}
	return gresult.OK(resp.Workflow)
}

// GetOnesiteWorkflowCITemplateIdV2 为什么是 V2, 因为 config service 的原生接口返回的是一个二维数组形式, 我自己修改了一下返回,展开成一维数组,这样子业务场景使用代码会更简洁一些
func (client *client) GetOnesiteWorkflowCITemplateIdV2(
	ctx context.Context,
	username string,
	workflowSnapshotId int64,
	taskName string,
	projects []*config_service.OnesiteProject,
) (mainTemplates []*config_service.MainPipelineTemplate, projectTemplates []*config_service.ProjectPipelineTemplate, err error) {
	req := &config_service.GetOnesiteWorkflowCITemplateIdRequest{
		WorkflowSnapshotId: workflowSnapshotId,
		NodeFixedName:      taskName,
		Projects:           projects,
		Username:           username,
		PipelineType:       config_service.PipelineType_NEW,
	}
	resp, err := client.inner.GetOnesiteWorkflowCITemplateId(ctx, req)
	if err != nil {
		return nil, nil, err
	}
	if resp == nil {
		return nil, nil, errors.New("empty response")
	}

	mainPipelineTemplates := gslice.Map(resp.TemplateConfigs, func(v *config_service.CIPipelineNodeConfigTemplateConfig) *config_service.MainPipelineTemplate {
		return &config_service.MainPipelineTemplate{
			TemplateId:   v.TemplateId,
			UpdatedAt:    v.UpdateAt,
			ControlPlane: v.Cp,
		}
	})
	projectPipelineTemplates := gslice.FlatMap(resp.Projects, func(v *config_service.OnesiteProject) []*config_service.ProjectPipelineTemplate {
		return gslice.Map(v.ProjectDetail, func(p *config_service.OnesiteProjectDetail) *config_service.ProjectPipelineTemplate {
			return &config_service.ProjectPipelineTemplate{
				TemplateId:        p.TemplateId,
				UpdatedAt:         p.UpdateAt,
				ControlPlane:      p.Cp,
				ProjectType:       dev.ProjectType(v.ProjectType),
				ProjectUniqueId:   v.ProjectUniqueId,
				ProjectName:       v.ProjectName,
				ProjectTTPWebName: v.ProjectTTPWebName,
			}
		})
	})

	return mainPipelineTemplates, projectPipelineTemplates, nil
}

// GetOnesiteWorkflowCINodeInfo 获取开发任务模板中某一个 Task 的配置, 用户控制流水线运行的配置
func (client *client) GetOnesiteWorkflowCINodeInfo(ctx context.Context, workflowSnapshotId int64, nodeFixedName string) gresult.R[optional.O[*config_service.OnesiteWorkflowNodeConfig]] {
	req := &config_service.GetOnesiteWorkflowCINodeInfoRequest{
		WorkflowSnapshotId: workflowSnapshotId,
		NodeFixedName:      nodeFixedName,
	}
	resp, err := client.inner.GetOnesiteWorkflowCINodeInfo(ctx, req)
	if err != nil {
		return gresult.Err[optional.O[*config_service.OnesiteWorkflowNodeConfig]](err)
	}
	if resp == nil {
		return gresult.Err[optional.O[*config_service.OnesiteWorkflowNodeConfig]](errors.New("empty response"))
	}
	if !resp.Enabled {
		return gresult.OK(optional.Nil[*config_service.OnesiteWorkflowNodeConfig]())
	}
	return gresult.OK(optional.OK(resp.NodeConfig))
}

func (client *client) GetPipelineConfigs(ctx context.Context, workflowSnapshotId int64, nodeFixedName string) optional.O[*config_service.CIPipelineNodeConfig] {
	req := &config_service.GetOnesiteWorkflowCINodeInfoRequest{
		WorkflowSnapshotId: workflowSnapshotId,
		NodeFixedName:      nodeFixedName,
	}
	resp, err := client.inner.GetOnesiteWorkflowCINodeInfo(ctx, req)
	if err != nil {
		return optional.Nil[*config_service.CIPipelineNodeConfig]()
	}
	if resp == nil {
		return optional.Nil[*config_service.CIPipelineNodeConfig]()
	}
	if !resp.Enabled {
		return optional.Nil[*config_service.CIPipelineNodeConfig]()
	}
	if resp.NodeConfig == nil || resp.NodeConfig.CiPipelineNodeConfig == nil {
		return optional.Nil[*config_service.CIPipelineNodeConfig]()
	}
	return optional.OK(resp.NodeConfig.CiPipelineNodeConfig)
}
