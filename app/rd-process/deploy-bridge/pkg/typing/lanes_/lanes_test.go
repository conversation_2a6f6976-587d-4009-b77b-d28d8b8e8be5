package lanes_

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/dev"
)

func TestLanes(t *testing.T) {
	t.Run("DisplayName", func(t *testing.T) {
		var (
			overwritePrefix = "boe_"
			laneType        = "boe_env_"
			laneId          = "liutao_use"
		)
		assert.Equal(t, "boe_liutao_use", DisplayName(overwritePrefix, laneType, laneId))

		overwritePrefix = ""
		laneType = "boe_env_"
		laneId = "liutao_use"
		assert.Equal(t, "boe_env_liutao_use", DisplayName(overwritePrefix, laneType, laneId))
	})

	t.Run("DisplayNameFromTCE", func(t *testing.T) {
		tce := &dev.TCEProjectConfig{
			LaneType:        "a",
			LaneId:          "b",
			OverwritePrefix: "c",
		}

		assert.Equal(t, "cb", DisplayNameFromTCE(tce))
	})

	t.Run("DisplayNameFromFAAS", func(t *testing.T) {
		faas := &dev.FaaSProjectConfig{
			LaneType:        "a",
			LaneId:          "b",
			OverwritePrefix: "c",
		}

		assert.Equal(t, "cb", DisplayNameFromFAAS(faas))
	})

	t.Run("fullname", func(t *testing.T) {
		var (
			overwritePrefix = "boe_"
			laneType        = "boe_env_"
			laneId          = "liutao_use"
		)

		name := fullname(overwritePrefix, laneType, laneId)
		assert.Equal(t, "boe_:boe_env_:liutao_use", name)

		overwritePrefix = ""
		laneType = "boe_env_"
		laneId = "liutao_use"
		name = fullname(overwritePrefix, laneType, laneId)
		assert.Equal(t, ":boe_env_:liutao_use", name)
	})

	t.Run("FullNameFromLane", func(t *testing.T) {
		lane := &dev.NodeConfigEnvLane{
			LaneType:        "boe_env_",
			LaneId:          "aaa",
			OverwritePrefix: "boe_",
		}
		assert.Equal(t, "boe_:boe_env_:aaa", FullNameFromLane(lane))

		var lzero *dev.NodeConfigEnvLane
		assert.Equal(t, "", FullNameFromLane(lzero))
	})

	t.Run("FullNameFromTCE", func(t *testing.T) {
		var tcezero *dev.TCEProjectConfig
		assert.Equal(t, "", FullNameFromTCE(tcezero))

		tce := &dev.TCEProjectConfig{
			LaneType:        "boe_env_",
			LaneId:          "aaa",
			OverwritePrefix: "boe_",
		}
		assert.Equal(t, "boe_:boe_env_:aaa", FullNameFromTCE(tce))
	})

	t.Run("FullNameFromWEB", func(t *testing.T) {
		var webzero *dev.WebProjectConfig
		assert.Equal(t, "", FullNameFromWEB(webzero))

		web := &dev.WebProjectConfig{
			LaneType:        "boe_env_",
			LaneId:          "aaa",
			OverwritePrefix: "boe_",
		}
		assert.Equal(t, "boe_:boe_env_:aaa", FullNameFromWEB(web))
	})

	t.Run("FullNameFromGeckoChannel", func(t *testing.T) {
		var channelzero *dev.ChannelItem
		assert.Equal(t, "", FullNameFromGeckoChannel(channelzero))

		channel := &dev.ChannelItem{
			LaneType:        "boe_env_",
			LaneId:          "aaa",
			OverwritePrefix: "boe_",
		}
		assert.Equal(t, "boe_:boe_env_:aaa", FullNameFromGeckoChannel(channel))
	})

	t.Run("FullNameFromFAAS", func(t *testing.T) {
		var faaszero *dev.FaaSProjectConfig
		assert.Equal(t, "", FullNameFromFAAS(faaszero))

		faas := &dev.FaaSProjectConfig{
			LaneType:        "boe_env_",
			LaneId:          "aaa",
			OverwritePrefix: "boe_",
		}
		assert.Equal(t, "boe_:boe_env_:aaa", FullNameFromFAAS(faas))
	})

	t.Run("IsPPELane", func(t *testing.T) {
		assert.True(t, IsPPELane("ppe_aaa"))
	})

	t.Run("IsBOELane", func(t *testing.T) {
		assert.True(t, IsBOELane("boe_aaa"))
	})

	t.Run("IsPPELaneFeature", func(t *testing.T) {
		assert.True(t, IsPPELaneIsolationMode("ppe_h_aaa"))
	})

	t.Run("", func(t *testing.T) {
		assert.True(t, IsPPEEnvIsolationMode("ppe_aaa"))
	})
}
