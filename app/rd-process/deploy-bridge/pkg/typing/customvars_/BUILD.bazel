load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "customvars_",
    srcs = ["merge.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/typing/customvars_",
    visibility = ["//visibility:public"],
    deps = ["@org_byted_code_lang_gg//gmap"],
)

go_test(
    name = "customvars__test",
    srcs = ["merge_test.go"],
    embed = [":customvars_"],
    deps = ["@com_github_stretchr_testify//assert"],
)
