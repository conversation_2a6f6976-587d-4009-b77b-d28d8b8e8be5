package projects_

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bits/devops/deploy"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/lang/gg/gslice"
)

type Project struct {
	UniqueId     string
	Type         dev.ProjectType
	ControlPlane dev.ControlPlane
}

func NewProject(uniqueId string, typ dev.ProjectType, controlPlane dev.ControlPlane) *Project {
	return &Project{
		UniqueId:     uniqueId,
		Type:         typ,
		ControlPlane: controlPlane,
	}
}

func FromDeployProjectInfo(p *dev.DeployProjectInfo, controlPlane dev.ControlPlane) *Project {
	return NewProject(p.ProjectUniqueId, p.Type, controlPlane)
}

func FromDevChangeDeployConfig(i *dev.DevChangeDeployConfig) []*Project {
	p := i.ProjectInfo
	return gslice.Map(
		i.BuildConfigs,
		func(v *dev.DevChangeBuildConfig) *Project {
			return NewProject(p.ProjectUniqueId, p.Type, v.ControlPanel)
		},
	)
}

func FromDevChangeDeployConfigs(xs []*dev.DevChangeDeployConfig) []*Project {
	return gslice.FlatMap(
		xs,
		func(x *dev.DevChangeDeployConfig) []*Project { return FromDevChangeDeployConfig(x) },
	)
}

func FromChangeItemContents(xs []*dev.ChangeItemContent) []*Project {
	return gslice.Map(xs, FromChangeItemContent)
}

func FromChangeItemContent(v *dev.ChangeItemContent) *Project {
	return NewProject(v.DeployTarget.ProjectUniqueId, v.DeployTarget.ProjectType, v.ControlPanel)
}

func FromTaskPipelines(xs []*entity.TaskPipeline) []*Project {
	return gslice.Map(xs, FromTaskPipeline)
}

func FromTaskPipeline(v *entity.TaskPipeline) *Project {
	return NewProject(v.ProjectUniqueId, dev.ProjectType(v.ProjectType), dev.ControlPlane(v.ControlPlane))
}

func FromProject(v *deploy.Project) *Project {
	return NewProject(v.ProjectUniqueId, v.ProjectType, v.ControlPlane)
}

func FromControlPlaneProjects(xs []*dev.ControlPlaneProject) []*Project {
	return gslice.Map(xs, FromControlPlaneProject)
}

func FromControlPlaneProject(v *dev.ControlPlaneProject) *Project {
	return NewProject(v.ProjectUniqueId, v.ProjectType, v.ControlPlane)
}

func FromControlPlaneProjectProgress(x *dev.ControlPlaneProjectProgress) *Project {
	return FromControlPlaneProject(x.Project)
}

func FromControlPlaneProjectProgresses(xs []*dev.ControlPlaneProjectProgress) []*Project {
	return gslice.Map(xs, FromControlPlaneProjectProgress)
}

func FromTaskPipelineOperation(x *entity.TaskPipelineOperation) *Project {
	return NewProject(x.ProjectUniqueId, dev.ProjectType(x.ProjectType), dev.ControlPlane(x.ControlPlane))
}

func FromTaskPipelinesAndRuns(pipelines []*entity.TaskPipeline, runs []*platformpb.SimplePipelineRun) []*Project {
	projects := make([]*Project, 0, len(pipelines))
	for _, p := range pipelines {
		gslice.
			Find(runs, func(run *platformpb.SimplePipelineRun) bool { return run.PipelineId == uint64(p.PipelineId) }).
			IfOK(func(run *platformpb.SimplePipelineRun) { projects = append(projects, FromTaskPipeline(p)) })
	}
	return projects
}

func FromEnvProjectConfig(v *dev.EnvProjectConfig) *Project {
	return NewProject(v.ProjectUniqueId, v.ProjectType, v.ControlPlane)
}

func (p *Project) Equals(o *Project) bool {
	return p.UniqueId == o.UniqueId &&
		p.Type == o.Type &&
		p.ControlPlane == o.ControlPlane
}

func Contains(projects []*Project, other *Project) bool {
	return gslice.Any(
		projects,
		func(v *Project) bool { return v.Equals(other) },
	)
}

func NotContains(projects []*Project, other *Project) bool {
	return !Contains(projects, other)
}

func Equals(left *entity.TaskPipeline, right *dev.DeployProjectInfo, controlPlane dev.ControlPlane) bool {
	a := FromTaskPipeline(left)
	b := FromDeployProjectInfo(right, controlPlane)
	return a.Equals(b)
}
