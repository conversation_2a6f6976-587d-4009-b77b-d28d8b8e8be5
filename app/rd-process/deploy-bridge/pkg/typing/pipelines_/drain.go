package pipelines_

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql/entity"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/gslice"
)

// DrainMainPipelines 去除没有项目流水线的控制面, 并且去除重复控制面的流水线
func DrainMainPipelines(pipelines []*entity.TaskPipeline) []*entity.TaskPipeline {
	gslice.SortBy(pipelines, func(prev, next *entity.TaskPipeline) bool { return prev.MainPipelineId > next.MainPipelineId })
	pipelines = gslice.UniqBy(pipelines, (*entity.TaskPipeline).UniqueIdentifier)

	var (
		mainPipelines     = gslice.Filter(pipelines, func(p *entity.TaskPipeline) bool { return p.IsMainPipeline() })
		projectPipelines  = gslice.Filter(pipelines, func(p *entity.TaskPipeline) bool { return p.IsProjectPipeline() })
		taskControlPlanes = set.New[string]()
		result            = make([]*entity.TaskPipeline, 0, len(pipelines))
	)
	for _, mainPipeline := range mainPipelines {
		ppls := gslice.Filter(projectPipelines, func(p *entity.TaskPipeline) bool {
			return p.MainPipelineId == mainPipeline.PipelineId && p.TaskName == mainPipeline.TaskName
		})
		if mainPipeline.ControlPlane == entity.ControlPlaneUnknown {
			result = append(result, mainPipeline) // 这种情况只展示主流水线
			continue
		}
		if len(ppls) == 0 {
			continue // 如果这个主流水线没有子流水线了,那么不加上这个主流水线
		}
		if taskControlPlanes.Contains(fmt.Sprintf("%v-%v", mainPipeline.TaskName, mainPipeline.ControlPlane)) {
			continue
		}
		result = append(result, mainPipeline)
		result = append(result, ppls...)
		taskControlPlanes.Add(fmt.Sprintf("%v-%v", mainPipeline.TaskName, mainPipeline.ControlPlane))
	}
	return result
}

func DrainPipelines(pipelines []*entity.TaskPipeline) []*entity.TaskPipeline {
	gslice.SortBy(pipelines, func(prev, next *entity.TaskPipeline) bool { return prev.MainPipelineId > next.MainPipelineId })
	pipelines = gslice.UniqBy(pipelines, (*entity.TaskPipeline).UniqueIdentifier)
	return pipelines
}
