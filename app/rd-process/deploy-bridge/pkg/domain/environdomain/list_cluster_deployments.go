package environdomain

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bits/devops/deploy"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/mapper"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/service/tceservice"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/typing/lanes_"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	tce "code.byted.org/tce/tce_golang_sdk"
)

func (domain *EnvironDomain) ListClusterDeployments(ctx context.Context, req *deploy.ListClusterDeploymentsRequest) gresult.R[*deploy.ListClusterDeploymentsResponse] {
	var (
		uniqueType    = TransformUniqueType(req.UniqueType)
		lane          = lanes_.NewLane(req.Lane.Id, req.Lane.T, req.Lane.Prefix)
		controlPlanes = gslice.Map(req.ControlPlanes, mapper.TransformDevControlPlaneToControlPlane)
	)

	deployments := domain.clusterDeploymentRepository.FindByUniqueIdAndUniqueTypeAndLaneAndControlPlaneIn(ctx, req.UniqueId, uniqueType, lane.EnvName(), controlPlanes).ValueOrZero()

	unfinishedDeployments := gslice.Filter(deployments, func(d *entity.ClusterDeployment) bool { return d.Status != entity.ClusterDeploymentStatusFinished })
	unfinishedDeployments2 := gslice.Map(unfinishedDeployments, func(v *entity.ClusterDeployment) tceservice.DeploymentIdAndLaneAndControlPlane { return v })

	tceDeployments := domain.tceservice.FindDeployments(ctx, unfinishedDeployments2, req.GetUsername())

	go domain.refreshClusterDeploymentsStatus(ctx, unfinishedDeployments, tceDeployments)
	resp := &deploy.ListClusterDeploymentsResponse{
		Deployments: TransformClusterDeployments(ctx, deployments, tceDeployments),
	}
	return gresult.OK(resp)
}

func (domain *EnvironDomain) refreshClusterDeploymentsStatus(ctx context.Context, deployments []*entity.ClusterDeployment, tceDeployments []*tce.BaseDeployment) {
	for _, tceDeployment := range tceDeployments {
		if tceDeployment.Status == "finished" {
			gslice.Find(deployments, func(v *entity.ClusterDeployment) bool { return v.DeploymentId == tceDeployment.Id }).
				IfOK(func(v *entity.ClusterDeployment) {
					_ = domain.clusterDeploymentRepository.UpdateById(ctx, v.Id, &entity.ClusterDeployment{Status: entity.ClusterDeploymentStatusFinished})
				})
		}
	}
}

func TransformClusterDeployments(ctx context.Context, inputs []*entity.ClusterDeployment, tceDeployments []*tce.BaseDeployment) []*deploy.ClusterDeployment {
	return gslice.Map(inputs, func(input *entity.ClusterDeployment) *deploy.ClusterDeployment {
		return TransformClusterDeployment(ctx, input, tceDeployments)
	})
}

func TransformClusterDeployment(ctx context.Context, input *entity.ClusterDeployment, tceDeployments []*tce.BaseDeployment) *deploy.ClusterDeployment {
	var status = input.Status

	// TODO 这里可能是一个 BUG
	gslice.
		Find(tceDeployments, func(d *tce.BaseDeployment) bool { return d.Id == input.DeploymentId }).
		IfOK(func(d *tce.BaseDeployment) { status = entity.ClusterDeploymentStatus(d.Status) })

	meta := input.GetTCEMeta(ctx)

	return &deploy.ClusterDeployment{
		DeploymentId:    int64(input.DeploymentId),
		Link:            input.GetLink(ctx),
		Status:          TransformClusterDeploymentStatus(status),
		CreatedAt:       input.CreatedAt.Unix(),
		Lane:            meta.Lane,
		TceCluster:      meta.Cluster,
		Operator:        input.Operator,
		ProjectUniqueId: input.ProjectUniqueId,
		ProjectType:     dev.ProjectType(input.ProjectType),
	}
}

func TransformClusterDeploymentStatus(input entity.ClusterDeploymentStatus) deploy.ClusterDeploymentStatus {
	switch input {
	case entity.ClusterDeploymentStatusRunning:
		return deploy.ClusterDeploymentStatus_Running
	case entity.ClusterDeploymentStatusFinished:
		return deploy.ClusterDeploymentStatus_Finished
	case entity.ClusterDeploymentStatusFailed:
		return deploy.ClusterDeploymentStatus_Failed

	default:
		return deploy.ClusterDeploymentStatus_Running
	}
}
