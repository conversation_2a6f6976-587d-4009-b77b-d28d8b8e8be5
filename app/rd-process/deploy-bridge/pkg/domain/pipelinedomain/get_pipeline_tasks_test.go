package pipelinedomain

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bits/devops/deploy"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bits/workflow/enums"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bits/workflow/execution_engine"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/backends/configservice"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/backends/pipeline"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/backends/workflow"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/optional"
	"github.com/stretchr/testify/assert"
)

func TestGetPipelineTasks(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustConnectAtLocally()
	db := mysql.NewForTest()
	_ = db.Optimus.AutoMigrate(new(entity.TaskPipeline))
	taskPipelineRepository := repository.NewTaskPipelineRepository(db.Optimus)
	configserviceApi := configservice.NewMockApi(t)
	pipelineApi := pipeline.NewMockApi(t)
	workflowApi := workflow.NewMockApi(t)
	producer := events.NewMockedProducer()
	domain := NewPipelineDomain(rdb, db, nil, nil, configserviceApi, nil, pipelineApi, nil, workflowApi, nil, nil, nil, nil, nil, producer, nil)

	t.Run("当策略是全部流水线通过时", func(t *testing.T) {
		pipeline1 := &entity.TaskPipeline{
			DevBasicId:   747631,
			ControlPlane: entity.ControlPlaneCN,
			PipelineType: entity.PipelineTypeMainPipeline,
			PipelineId:   163209989120,
			TaskName:     "DevDevelopStageSelfTestTask",
		}
		pipelineId1 := taskPipelineRepository.Create(ctx, pipeline1).Must()
		defer func() { _ = taskPipelineRepository.DeleteById(ctx, pipelineId1) }()

		pipeline2 := &entity.TaskPipeline{
			DevBasicId:   747631,
			ControlPlane: entity.ControlPlaneI18N,
			PipelineType: entity.PipelineTypeMainPipeline,
			PipelineId:   163209969920,
			TaskName:     "DevDevelopStageSelfTestTask",
		}
		pipelineId2 := taskPipelineRepository.Create(ctx, pipeline2).Must()
		defer func() { _ = taskPipelineRepository.DeleteById(ctx, pipelineId2) }()

		pipeline3 := &entity.TaskPipeline{
			DevBasicId:     747631,
			ControlPlane:   entity.ControlPlaneCN,
			PipelineType:   entity.PipelineTypeProjectPipeline,
			PipelineId:     163209989121,
			TaskName:       "DevDevelopStageSelfTestTask",
			MainPipelineId: 163209989120,
		}
		pipelineId3 := taskPipelineRepository.Create(ctx, pipeline3).Must()
		defer func() { _ = taskPipelineRepository.DeleteById(ctx, pipelineId3) }()

		call1 := configserviceApi.On("GetOnesiteWorkflowCINodeInfo", ctx, int64(11870), "DevDevelopStageSelfTestTask").
			Return(gresult.OK(optional.OK(&config_service.OnesiteWorkflowNodeConfig{CiPipelineNodeConfig: &config_service.CIPipelineNodeConfig{PassStrategy: config_service.CIPipelineNodeConfigPassStrategy_ALL_CONTROL_PLANE_PASS}})))
		defer func() { call1.Unset() }()

		call2 := pipelineApi.
			On("BatchGetLatestPipelineRuns", ctx, []int64{163209989120}).
			Return(gresult.OK([]*platformpb.SimplePipelineRun{
				{PipelineId: 163209989120, RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING},
			}))
		defer func() { call2.Unset() }()

		call3 := workflowApi.
			On("GetExecutionAndTasks", ctx, int64(747631), enums.UniqueType_DEV_TASK_V1, []string{"DevDevelopStageSelfTestTask"}).
			Return(&execution_engine.Execution{}, []*execution_engine.ExecutionTask{{State: enums.ExecutionTaskState_RUNNING, Name: "DevDevelopStageSelfTestTask"}}, error(nil))
		defer func() { call3.Unset() }()

		call4 := configserviceApi.
			On("GetPipelineConfigs", ctx, int64(11870), "DevDevelopStageSelfTestTask").
			Return(optional.OK(&config_service.CIPipelineNodeConfig{PassStrategy: config_service.CIPipelineNodeConfigPassStrategy_ALL_CONTROL_PLANE_PASS}))
		defer func() { call4.Unset() }()

		req := &deploy.GetPipelineTasksRequest{DevBasicId: 747631, WorkflowSnapshotId: 11870}
		resp := domain.GetPipelineTasks(ctx, req).Must()
		assert.Equal(t, 1, len(resp.Tasks))
		assert.Equal(t, "DevDevelopStageSelfTestTask", resp.Tasks["DevDevelopStageSelfTestTask"].TaskName)
		assert.Equal(t, dev.DevWorkflowTaskStatus_running, resp.Tasks["DevDevelopStageSelfTestTask"].Status)
	})
}

func TestTransformToDevWorkflowTaskStatus(t *testing.T) {
	t.Run("当流程引擎的当前阶段与任务是同一个阶段时, 按照流水线的状态聚合出Task的状态", func(t *testing.T) {
		t.Run("当策略是一个流水线通过时,一个流水线成功,得到成功", func(t *testing.T) {
			var (
				task           = optional.Nil[*execution_engine.ExecutionTask]()
				runs           = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_FAILED}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_SUCCEEDED}}
				execution      = &execution_engine.Execution{CurrentStage: nil}
				pipelineConfig = optional.OK(&config_service.CIPipelineNodeConfig{PassStrategy: config_service.CIPipelineNodeConfigPassStrategy_AT_LEAST_ONE_CONTROL_PLANE_PASS})
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, pipelineConfig, optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_succeeded, actual)
		})

		t.Run("当策略是全部流水线通过时,全部流水线成功,得到成功", func(t *testing.T) {
			var (
				task           = optional.Nil[*execution_engine.ExecutionTask]()
				runs           = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_SUCCEEDED}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_SUCCEEDED}}
				execution      = &execution_engine.Execution{CurrentStage: nil}
				pipelineConfig = optional.OK(&config_service.CIPipelineNodeConfig{PassStrategy: config_service.CIPipelineNodeConfigPassStrategy_ALL_CONTROL_PLANE_PASS})
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, pipelineConfig, optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_succeeded, actual)
		})

		t.Run("当任何流水线都没有运行时,返回 pending", func(t *testing.T) {
			task := &execution_engine.ExecutionTask{State: enums.ExecutionTaskState_RUNNING, Stage: "DevDevelopStage"}
			execution := &execution_engine.Execution{CurrentStage: gptr.Of("DevDevelopStage")}

			actual := TransformToDevWorkflowTaskStatus(1, execution, optional.OK(task), nil, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_pending, actual)
		})

		t.Run("当策略是一个流水线通过时,所有流水线都失败,得到失败", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_FAILED}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_FAILED}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_failed, actual)
		})

		t.Run("流水线一个失败,得到失败", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_FAILED}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(1, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_failed, actual)
		})

		t.Run("所有流水线是被取消了,返回取消", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_CANCELLED}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_CANCELLED}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_canceled, actual)
		})

		t.Run("一个流水线是被取消了一个是运行中,返回运行中", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_CANCELLED}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_running, actual)
		})

		t.Run("一个流水线是被取消,返回取消", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_CANCELLED}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_canceled, actual)
		})

		t.Run("当流水线待交互时,返回 held", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_WAITING}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_WAITING}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_held, actual)
		})

		t.Run("当流水线没有结束时,返回运行中", func(t *testing.T) {
			var (
				task      = optional.Nil[*execution_engine.ExecutionTask]()
				runs      = []*platformpb.SimplePipelineRun{{RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING}, {RunStatus: dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING}}
				execution = &execution_engine.Execution{CurrentStage: nil}
			)

			actual := TransformToDevWorkflowTaskStatus(2, execution, task, runs, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

			assert.Equal(t, dev.DevWorkflowTaskStatus_running, actual)
		})
	})

	t.Run("当流程引擎的当前阶段超过任务所在阶段时,使用引擎上的Task的成功或者跳过状态", func(t *testing.T) {
		task := &execution_engine.ExecutionTask{State: enums.ExecutionTaskState_SUCCEED, Stage: "DevGatekeeperStage"}
		execution := &execution_engine.Execution{CurrentStage: gptr.Of("DevCodeMergeStage")}
		actual := TransformToDevWorkflowTaskStatus(2, execution, optional.OK(task), nil, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())
		assert.Equal(t, dev.DevWorkflowTaskStatus_succeeded, actual)

		task = &execution_engine.ExecutionTask{State: enums.ExecutionTaskState_SKIPPED, Stage: "DevGatekeeperStage"}
		execution = &execution_engine.Execution{CurrentStage: gptr.Of("DevCodeMergeStage")}
		actual = TransformToDevWorkflowTaskStatus(2, execution, optional.OK(task), nil, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())
		assert.Equal(t, dev.DevWorkflowTaskStatus_skipped, actual)
	})

	t.Run("当流程引擎的当前阶段落后任务所在阶段时,返回pending状态", func(t *testing.T) {
		task := &execution_engine.ExecutionTask{State: enums.ExecutionTaskState_SUCCEED, Stage: "DevGatekeeperStage"}
		execution := &execution_engine.Execution{CurrentStage: gptr.Of("DevDevelopStage")}

		actual := TransformToDevWorkflowTaskStatus(2, execution, optional.OK(task), nil, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

		assert.Equal(t, dev.DevWorkflowTaskStatus_pending, actual)
	})

	t.Run("当流程引擎上对应任务 取消是也就是开发任务关闭场景,返回取消", func(t *testing.T) {
		task := &execution_engine.ExecutionTask{State: enums.ExecutionTaskState_CANCELED}
		execution := &execution_engine.Execution{CurrentStage: nil}

		actual := TransformToDevWorkflowTaskStatus(1, execution, optional.OK(task), nil, optional.Nil[*config_service.CIPipelineNodeConfig](), optional.Nil[*entity.TaskHistory]())

		assert.Equal(t, dev.DevWorkflowTaskStatus_canceled, actual)
	})
}
