package pipelinedomain

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bits/devops/deploy"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/functools"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/typing/pipelines_"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/typing/projects_"
	"code.byted.org/devinfra/hagrid/libs/events/devtaskpipelines"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

func (domain *PipelineDomain) SkipProjectPipelines(ctx context.Context, req *deploy.SkipProjectPipelinesRequest) gresult.R[*base.EmptyResponse] {
	if len(req.Projects) == 0 { // 没传递项目过来,那么啥也不做
		return gresult.OK(base.NewEmptyResponse())
	}

	var (
		devBasicId = req.DevBasicId
		taskName   = req.TaskName
		projects   = projects_.FromControlPlaneProjects(req.Projects)
		operation  = platformpb.PipelineRunOperation_PIPELINE_RUN_OPERATION_CANCEL
		stage      = pipelines_.Stage(devBasicId, taskName)
		wg         errgroup.Group
	)

	pipelines := domain.taskPipelineRepository.FindByDevBasicIdAndTaskNameAndDeletedAtIsNull(ctx, devBasicId, taskName).ValueOrZero()
	pipelines = gslice.Filter(pipelines, func(p *entity.TaskPipeline) bool { return projects_.Contains(projects, projects_.FromTaskPipeline(p)) })

	var (
		pipelineIds = gslice.Map(pipelines, func(v *entity.TaskPipeline) int64 { return v.PipelineId })
		runs        []*platformpb.SimplePipelineRun
		operations  []*entity.TaskPipelineOperation
	)

	wg.Go(func() error {
		runs = domain.pipelineApi.BatchGetLatestPipelineRuns(ctx, pipelineIds).ValueOrZero()
		return nil
	})
	wg.Go(func() error {
		operations = domain.taskPipelineOperationRepository.FindByPipelineIdIn(ctx, pipelineIds).ValueOrZero()
		return nil
	})
	_ = wg.Wait()

	for _, run := range runs {
		run := run
		wg.Go(func() error {
			_ = domain.pipelineApi.OperatePipelineRun(ctx, req.Operator, int64(run.RunId), operation, stage)
			return nil
		})
	}
	_ = wg.Wait()

	operationIds := gslice.Map(operations, func(v *entity.TaskPipelineOperation) int64 { return v.Id })
	updated := &entity.TaskPipelineOperation{LastOperation: entity.OperationSkipped}
	if err := domain.taskPipelineOperationRepository.UpdateByIdIn(ctx, operationIds, updated); err != nil {
		log.V2.Warn().With(ctx).Str("failed to update operations").Error(err).Emit()
	}

	event := TransformToDevTaskSkipProjectPipelinesEvent(req)
	domain.producer.SendDevTaskSkipProjectPipelinesEvent(ctx, event)

	return gresult.OK(base.NewEmptyResponse())
}

func TransformToDevTaskSkipProjectPipelinesEvent(req *deploy.SkipProjectPipelinesRequest) *devtaskpipelines.DevTaskSkipProjectPipelinesEvent {
	return &devtaskpipelines.DevTaskSkipProjectPipelinesEvent{
		SpaceId:    req.SpaceId,
		DevBasicId: req.DevBasicId,
		TaskName:   req.TaskName,
		Stage:      functools.TakePrefix(req.TaskName, "Stage"),
		Username:   req.Operator,
		Projects: gslice.Map(req.Projects, func(v *dev.ControlPlaneProject) *devtaskpipelines.Project {
			return devtaskpipelines.NewProject(v.ProjectUniqueId, int(v.ProjectType), int(v.ControlPlane))
		}),
	}
}
