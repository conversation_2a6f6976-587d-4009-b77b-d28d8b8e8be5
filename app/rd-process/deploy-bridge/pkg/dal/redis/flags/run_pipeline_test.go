package flags

import (
	"context"
	"testing"
	"time"

	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"github.com/stretchr/testify/assert"
)

func TestInitializePipelineFlag(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustConnectAtLocally()
	flag := NewInitializePipelineFlag(rdb)

	t.Run("GetFlag 最开始获取不到", func(t *testing.T) {
		f := flag.GetFlag(ctx, 123, "AAA")

		assert.True(t, f.IsNil())
	})

	t.Run("删除 Flag, TTL 值被修改", func(t *testing.T) {
		var (
			devBasicId = int64(123)
			taskName   = "SelfTest"
		)

		flag.CreateFlag(ctx, devBasicId, taskName, 5*time.Second)
		flag.DeleteFlag(ctx, devBasicId, taskName)

		ttl := flag.TTL(ctx, devBasicId, taskName)
		assert.True(t, ttl < 5*time.Second)
	})
}
