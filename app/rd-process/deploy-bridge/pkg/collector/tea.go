package collector

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/data/mario_collector"
	"code.byted.org/data/mario_collector/pb_event"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2/log"
	"google.golang.org/protobuf/proto"
)

type TeaCollector[T Uniquer] struct {
	inner *mario_collector.MarioCollector
}

func NewTeaCollector[T Uniquer](token string) Collector[T] {
	inner := mario_collector.NewMarioCollector(env.PSM(), token, mario_collector.LOG_TYPE_EVENT)

	return &TeaCollector[T]{
		inner: inner,
	}
}

const (
	appId     uint32 = 2881                         // https://data.bytedance.net/byteio/event/schema?page=1&page_size=20&subAppId=2881&tab=schemaScene
	eventName        = "bits_workflow_request_info" // https://data.bytedance.net/byteio/event/schema/bits_workflow_request_info?subAppId=2881&tabKey=basic
)

type Event struct {
	Extra        string      `json:"extra,omitempty"`         // 补充数据（可扩展）
	ProcessId    int64       `json:"process_id,omitempty"`    // 开发任务 或者发布单ID
	ProcessType  int64       `json:"process_type,omitempty"`  // 1: 开发任务 2: 发布单
	ErrMsg       string      `json:"err_msg,omitempty"`       // 错误信息
	ErrCode      int         `json:"err_code,omitempty"`      // 错误码
	TriggerType  TriggerType `json:"trigger_type"`            // 0: 自动 1: 手动
	LogId        string      `json:"log_id,omitempty"`        // log id
	ComputeType  ComputeType `json:"compute_type,omitempty"`  // 1:成功：计算成功数 2:失败：计算失败数
	Scene        string      `json:"scene,omitempty"`         // 指标场景
	BusinessId   string      `json:"business_id,omitempty"`   // 各个业务的唯一键ID
	BusinessType string      `json:"business_type,omitempty"` // business_id 类型
}

type Scene = string

const (
	SceneCreatePipeline Scene = "dev_task_create_pipeline"
	SceneRunPipeline    Scene = "dev_task_run_pipeline"
	SceneUpdatePipeline Scene = "dev_task_update_pipeline"
	SceneCancelPipeline Scene = "dev_task_cancel_pipeline"
)

type TriggerType = int

const (
	TriggerTypeAuto TriggerType = 0 // 自动
	TriggerTypeM    TriggerType = 1 // 手动
)

type ComputeType = int

const (
	ComputeTypeSuccess ComputeType = 1 // 成功
	ComputeTypeFailed  ComputeType = 2 // 失败
)

func NewEvent(ctx context.Context, devBasicId int64, triggerType TriggerType, scene Scene) *Event {
	return &Event{
		ProcessId:    devBasicId,
		ProcessType:  1, // 开发任务
		TriggerType:  triggerType,
		LogId:        ctxvalues.LogIDDefault(ctx),
		ComputeType:  ComputeTypeSuccess,
		Scene:        scene,
		BusinessId:   strconv.FormatInt(devBasicId, 10),
		BusinessType: "DEVTASK",
	}
}

func NewEventWithError(ctx context.Context, devBasicId int64, triggerType TriggerType, scene Scene, e error) *Event {
	var (
		errMsg  = e.Error()
		errCode int
	)

	var be *bits_err.BitsErr
	if errors.As(e, &be) {
		errCode = int(be.Code())
	}

	return &Event{
		ProcessId:    devBasicId,
		ProcessType:  1, // 开发任务
		ErrMsg:       errMsg,
		ErrCode:      errCode,
		TriggerType:  triggerType,
		LogId:        ctxvalues.LogIDDefault(ctx),
		ComputeType:  ComputeTypeFailed,
		Scene:        scene,
		BusinessId:   strconv.FormatInt(devBasicId, 10),
		BusinessType: "DEVTASK",
	}
}

func (e *Event) GetUniqueId() string {
	return fmt.Sprintf("%s_%s", e.BusinessType, e.BusinessId)
}

func (t *TeaCollector[T]) Collect(ctx context.Context, e T) {
	if !env.IsProduct() || t == nil { // 如果不是生产环境,那么不打点,只在生产环境打
		return
	}

	user := &pb_event.User{
		UserUniqueId: proto.String(e.GetUniqueId()),
	}
	header := &pb_event.Header{AppId: proto.Uint32(appId)}
	event := &pb_event.Event{
		Event:  proto.String(eventName),
		Time:   proto.Uint32(uint32(time.Now().Unix())),
		Params: proto.String(jsons.Stringify(e)),
	}

	log.V2.Info().With(ctx).KV("event", event).Emit()
	if err := t.inner.CollectEvent(user, header, event); err != nil {
		log.V2.Warn().With(ctx).Str("failed to collect event to tea").Error(err).Emit()
	}
}
