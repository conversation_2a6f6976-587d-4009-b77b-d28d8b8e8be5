package releaseticketservice

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/backends/releaseticket"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/redis/cache"
	"code.byted.org/devinfra/hagrid/app/rd-process/deploy-bridge/pkg/dal/redis/dictionary"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/gresult"
)

type ReleaseTicketService struct {
	releaseTicketStageCache *cache.ReleaseTicketStageCache
	rtApi                   releaseticket.Api
}

func NewReleaseTicketService(rdb *goredis.Client, rtApi releaseticket.Api) *ReleaseTicketService {
	return &ReleaseTicketService{
		releaseTicketStageCache: cache.NewReleaseTicketStageCache(rdb),
		rtApi:                   rtApi,
	}
}

func (service *ReleaseTicketService) GetReleaseTicketIdByStageId(ctx context.Context, stageId int64) gresult.R[int64] {
	if cached, found := service.releaseTicketStageCache.FindByStageId(ctx, stageId).Get(); found {
		return gresult.OK(cached.ReleaseTicketId)
	}

	rtId, err := service.rtApi.GetReleaseTicketIdByStageId(ctx, stageId).Get()
	if err != nil {
		log.V2.Warn().With(ctx).Str("failed to get release ticket id").Error(err).Emit()
		return gresult.Err[int64](err)
	}

	service.releaseTicketStageCache.Create(ctx, dictionary.NewReleaseTicketStage(stageId, rtId), 24*time.Hour)
	return gresult.OK(rtId)
}
