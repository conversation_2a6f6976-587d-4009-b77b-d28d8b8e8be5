package testrule

import (
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/overpass/bytedance_bits_qa_review/kitex_gen/bytedance/bits/qa_review"
)

const (
	SubmitModeAuto                   = 0
	SubmitModeCheckItemMustSatisfied = 1
	SubmitModeCheckItemCanIgnored    = 2

	anyTeam    = -1
	allProject = "*"
)

var (
	ErrRuleNotMatched = bits_err.QAREVIEW.ErrRuleNotMatched
)

func GetQaReviewMode(mode int8) string {
	switch int(mode) {
	case SubmitModeAuto:
		return qa_review.QaReviewMode_Auto.String()
	case SubmitModeCheckItemMustSatisfied, SubmitModeCheckItemCanIgnored:
		return qa_review.QaReviewMode_Manual.String()
	}
	return ""
}

type testingConfig struct {
	TestingFlowRules []TestingRule `json:"testing_flow_rules"`
}

type FeatureBusiness struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
type FeatureBindSpace struct {
	ID         string            `json:"id"`
	SimpleName string            `json:"simpleName"`
	Name       string            `json:"name"`
	Businesses []FeatureBusiness `json:"businesses"`
}
type TestingRule struct {
	Approvers             []string           `json:"approvers,omitempty"`
	BizGroups             []int64            `json:"bizGroups"`
	MrTypes               []string           `json:"mrTypes"`
	CustomForm            string             `json:"customForm"`
	PassedCheckItems      CheckItems4Pass    `json:"passedCheckItems"`
	QaNeeded              bool               `json:"qaNeeded"`
	SubmitCheckItems      CheckItems4Submit  `json:"submitCheckItems"`
	SubmitMode            int                `json:"submitMode"`
	TargetBranch          string             `json:"targetBranch"`
	IsResetStatus         bool               `json:"isResetStatus"`
	IncludedProjects      []string           `json:"includedProjects"`      // 为空时 等同于 *
	ForceManualSubmission bool               `json:"forceManualSubmission"` // 手动提测模式才有意义，强制必须手动触发提测
	FeatureBindSpaces     []FeatureBindSpace `json:"featureBindSpaces"`
}

type CheckItems4Pass struct {
	CiCheck      bool `json:"ciCheck"`
	CodeReview   bool `json:"codeReview,omitempty"`
	TestCases    bool `json:"testCases,omitempty"`
	AnyWhereDoor bool `json:"anywhereScene,omitempty"`
}

type CheckItems4Submit struct {
	CiCheck      bool `json:"ciCheck"`
	CodeReview   bool `json:"codeReview,omitempty"`
	TestCases    bool `json:"testCases,omitempty"`
	CustomForm   bool `json:"customForm"`
	AnyWhereDoor bool `json:"anywhereScene,omitempty"`
}

func CheckItemSubmit2Pass(items CheckItems4Submit) CheckItems4Pass {
	return CheckItems4Pass{
		CiCheck:      items.CiCheck,
		CodeReview:   items.CodeReview,
		TestCases:    items.TestCases,
		AnyWhereDoor: items.AnyWhereDoor,
	}
}
