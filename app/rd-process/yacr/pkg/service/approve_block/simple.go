package approve_block

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/kitex_gen/bytedance/bits/yacr"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/data"
	userService "code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/service/user"
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/gslice"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func createSimpleBlockTx(ctx context.Context, tx *gorm.DB, devId int64, block *yacr.ApproveBlocks) (*entity.ApproveBlock[entity.SimpleApproveDetail], error) {
	approveBlock := &entity.ApproveBlock[entity.SimpleApproveDetail]{
		DevID:      uint64(devId),
		Type:       entity.ApproveBlockType_Simple,
		Status:     entity.ApproveStatus_Running,
		Detail:     datatypes.NewJSONType[entity.SimpleApproveDetail](entity.NewSimpleApproveDetailFromIDL(block.Detail.Simple)),
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	b, err := data.WriteBlocksTx(ctx, tx, approveBlock)
	if err != nil {
		log.V1.CtxError(ctx, "failed to create simple approve block, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDBError.AddError(err)
	}
	for _, reviewer := range block.Detail.Simple.Approvers {
		err := userService.UpsertUserApproveInfo(ctx, devId, reviewer.Name, reviewer.Operator, true, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to create or update approvers, err:%s", err.Error())
			return nil, err
		}
	}
	return b, nil
}

func getSimpleBlockStatus(ctx context.Context, b *entity.ApproveBlock[entity.IApproveBlock], a map[string]*entity.UserApproveStatus, tx *gorm.DB) (entity.ApproveStatus, error) {
	detail := b.Detail.Data().(entity.SimpleApproveDetail)
	status := entity.ApproveStatus_Running
	approveCount := 0
	for _, reviewer := range detail.Reviewers {
		var userStatus entity.ApproveStatus
		userApproveStatus, ok := a[reviewer.Reviewer]
		if !ok {
			log.V1.CtxWarn(ctx, "user approve info for %s not found", reviewer)
			userStatus = entity.ApproveStatus_Running
		} else {
			userStatus = userApproveStatus.Status
		}
		log.V1.CtxInfo(ctx, "reviewer %s: %s", reviewer.Reviewer, userStatus.String())
		switch userStatus {
		case entity.ApproveStatus_Approved, entity.ApproveStatus_AlwaysApproved:
			approveCount++
		case entity.ApproveStatus_Rejected:
			status = entity.ApproveStatus_Rejected
			goto returnResult
		case entity.ApproveStatus_Running:
		}
	}
	if approveCount == len(detail.Reviewers) {
		status = entity.ApproveStatus_Approved
	}
returnResult:
	return status, nil
}

func deleteSimpleBlockTx(ctx context.Context, tx *gorm.DB, devId int64, b *yacr.ApproveBlocks) error {
	err := data.DeleteBlockById(ctx, devId, b.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to delete simple approve block, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	return nil
}

func updateSimpleBlockTx(ctx context.Context, tx *gorm.DB, devId int64, old, new *yacr.ApproveBlocks) (*entity.ApproveBlock[entity.IApproveBlock], error) {
	blockEntity, err := data.GetApproveBlockById(ctx, old.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDBError.AddError(err)
	}
	if blockEntity == nil {
		log.V1.CtxError(ctx, "approve block not found")
		return nil, bits_err.COMMON.ErrRecordNotFound
	}
	if blockEntity.DevID != uint64(devId) {
		log.V1.CtxError(ctx, "approve block not belong to dev")
		return nil, bits_err.YACR.ErrApproveBlockNotBelongToDev
	}
	oldReviewersMap := make(map[string]bool)
	for _, reviewer := range old.Detail.Simple.Approvers {
		oldReviewersMap[reviewer.Name] = true
	}
	for _, reviewer := range new.Detail.Simple.Approvers {
		if _, ok := oldReviewersMap[reviewer.Name]; !ok {
			err := userService.UpsertUserApproveInfo(ctx, devId, reviewer.Name, reviewer.Operator, true, tx)
			if err != nil {
				log.V1.CtxError(ctx, "failed to create or update approvers, err:%s", err.Error())
				return nil, bits_err.YACR.ErrDBError.AddError(err)
			}
		}
	}
	blockEntity.Detail = datatypes.NewJSONType[entity.IApproveBlock](entity.NewSimpleApproveDetailFromIDL(new.Detail.Simple))
	return blockEntity, nil
}

func getRemovableSimpleBlock(_ context.Context, _ string, _ *yacr.ApproveBlocks) (bool, error) {
	return true, nil // simple 都可以删除
}

func getSimpleBlockCandidate(_ context.Context, _ string, _ *yacr.ApproveBlocks) (*yacr.UserCandidate, error) {
	return &yacr.UserCandidate{ // simple 可以随意替换
		Type:       yacr.CandidateType_All,
		Candidates: nil,
	}, nil
}

func getSimpleBlockReviewers(_ context.Context, block *yacr.ApproveBlocks) ([]string, error) {
	return gslice.Map(block.Detail.Simple.Approvers, func(d *yacr.SimpleApprover) string {
		return d.Name
	}), nil
}

func removeSimpleReviewer(ctx context.Context, username string, block *yacr.ApproveBlocks, tx *gorm.DB) error {
	oldBlock, err := data.GetApproveBlockById(ctx, block.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	if oldBlock == nil {
		log.V1.CtxError(ctx, "approve block not found")
		return bits_err.COMMON.ErrRecordNotFound
	}
	for i := 0; i < len(block.Detail.Simple.Approvers); i++ {
		if block.Detail.Simple.Approvers[i].Name == username {
			block.Detail.Simple.Approvers = append(block.Detail.Simple.Approvers[:i], block.Detail.Simple.Approvers[i+1:]...)
			i--
		}
	}
	oldBlock.Detail = datatypes.NewJSONType[entity.IApproveBlock](entity.NewSimpleApproveDetailFromIDL(block.Detail.Simple))
	_, err = data.UpdateBlocksTx(ctx, tx, oldBlock)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update simple approve block, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	return nil
}

func resetSimpleBlockStatus(ctx context.Context, devId int64, block *yacr.ApproveBlocks, tx *gorm.DB) ([]string, error) {
	realResetSet := set.New[string]()
	reviewers, _ := getSimpleBlockReviewers(ctx, block)
	for _, reviewer := range reviewers {
		reset, err := userService.ResetUserApproveStatus(ctx, devId, reviewer, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to reset user approve status, err:%s", err.Error())
			return nil, bits_err.YACR.ErrDBError.AddError(err)
		}
		if reset {
			log.V1.CtxInfo(ctx, "reviewer %s is reset", reviewer)
			realResetSet.Add(reviewer)
		}
	}
	if len(reviewers) > 0 {
		oldBlock, err := data.GetApproveBlockById(ctx, block.GetId(), tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
			return nil, bits_err.YACR.ErrDBError.AddError(err)
		}
		if oldBlock == nil {
			log.V1.CtxError(ctx, "approve block not found")
			return nil, bits_err.COMMON.ErrRecordNotFound
		}
		oldBlock.Status = entity.ApproveStatus_Running
		_, err = data.UpdateBlocksTx(ctx, tx, oldBlock)
		if err != nil {
			log.V1.CtxError(ctx, "failed to update simple approve block, err:%s", err.Error())
			return nil, bits_err.YACR.ErrDBError.AddError(err)
		}
	}
	return realResetSet.ToSlice(), nil
}
