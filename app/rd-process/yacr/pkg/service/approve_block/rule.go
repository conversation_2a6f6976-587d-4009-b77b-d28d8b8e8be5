package approve_block

import (
	"context"
	"fmt"
	"sync"
	"time"
	"unsafe"

	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/dal/tcc"
	"github.com/samber/lo"

	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/kitex_gen/bytedance/bits/git_server_v2"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/kitex_gen/bytedance/bits/yacr"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/data"
	rpcService "code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/service/rpc"
	userService "code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/service/user"
	"code.byted.org/devinfra/hagrid/app/rd-process/yacr/pkg/utils"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/collection/set"
	"code.byted.org/lang/gg/collection/skipset"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"github.com/lestrrat/go-pcre2"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

func createRuleBlock(ctx context.Context, tx *gorm.DB, devId int64, block *yacr.ApproveBlocks) (*entity.ApproveBlock[entity.RuleApproveDetail], error) {
	approveBlock := &entity.ApproveBlock[entity.RuleApproveDetail]{
		DevID:      uint64(devId),
		Type:       entity.ApproveBlockType_Rule,
		Status:     entity.ApproveStatus_Running,
		Detail:     datatypes.NewJSONType[entity.RuleApproveDetail](entity.NewRuleApproveDetailFromIDL(block.Detail.Rule)),
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	b, err := data.WriteBlocksTx(ctx, tx, approveBlock)
	if err != nil {
		log.V1.CtxError(ctx, "failed to create simple approve block, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDBError.AddError(err)
	}
	reviewers, err := getRuleBlockReviewers(ctx, entity.CopyApproveBlockToIDL(entity.CopyToIApproveBlock(b)))
	if err != nil {
		log.V1.CtxError(ctx, "failed to get rule block reviewers, err:%s", err.Error())
		return nil, err
	}
	for _, reviewer := range reviewers {
		err := userService.UpsertUserApproveInfo(ctx, devId, reviewer, "bits", true, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to create or update approvers, err:%s", err.Error())
			return nil, err
		}
	}
	return b, nil
}

func ResetRuleBlock(ctx context.Context, devId int64, detail *entity.RuleApproveDetail, diff []*git_server.DiffFile) ([]string, error) {
	devInfo, err := rpcService.GetDevInfo(ctx, devId)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get dev info, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	groupMemberUsernames, err := getSameGroupMembers(ctx, devInfo.AppId, devInfo.Author, detail.Global.ReviewGroups)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get same group members:%s", err.Error())
		return nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}

	reviewerSet := set.New[string]()
	reviewerSet.AddN(detail.Global.MaintainerHits...)
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			reviewerSet.AddN(rule.Reviewers...)
		}
	}

	resetUsernameSet := set.New[string]() // 需要重置的人
	resetUsernameSet.AddN(resetMaintainers(detail.Global)...)

	branchRule := make([]*entity.RuleItem, 0)
	fileRule := make([]*entity.RuleItem, 0)
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			if rule.RuleType == entity.RuleType_Branch {
				branchRule = append(branchRule, rule)
			} else {
				fileRule = append(fileRule, rule)
			}
		}
	}

	usernames, _ := resetBranchRule(ctx, devInfo.TrunkBranch, branchRule)
	resetUsernameSet.AddN(usernames...)
	usernames, _ = resetFileRule(ctx, detail.Global, gslice.Map(diff, func(f *git_server.DiffFile) string { return f.Path }), fileRule, reviewerSet, groupMemberUsernames)
	resetUsernameSet.AddN(usernames...)

	return resetUsernameSet.ToSlice(), nil
}

// GenerateRuleBlock generate a rule block, read-only
func GenerateRuleBlock(ctx context.Context, devId int64) (*entity.ApproveBlock[entity.RuleApproveDetail], []string, error) {
	devInfo, err := rpcService.GetDevInfo(ctx, devId)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get dev info:%s", err.Error())
		return nil, nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	var trunkBranch *string
	if ok, _ := IfIndependentBranchEnabled(ctx, devInfo.SpaceID); ok {
		log.V1.CtxInfo(ctx, "match independent branch space, space id:%d, trunk branch:%s", devInfo.SpaceID, devInfo.TrunkBranch)
		trunkBranch = &devInfo.TrunkBranch
	}
	config, err := rpcService.GetReviewConfigByChangeId(ctx, devInfo.CodeChangeID, trunkBranch)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get review config:%s", err.Error())
		return nil, nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	diff, err := rpcService.GetDiffByCodeChangeID(ctx, devInfo.CodeChangeID)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get diff:%s", err.Error())
		return nil, nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	groupMemberUsernames, err := getSameGroupMembers(ctx, devInfo.AppId, devInfo.Author, config.RepositoryConfig.ReviewerGroups)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get same group members:%s", err.Error())
		return nil, nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	reviewBlock := entity.NewRuleApproveDetailFromCaC(config)
	reviewerSet := set.New[string]()
	reviewerSet.AddN(fillMaintainers(ctx, reviewBlock.Global, reviewerSet)...)
	branchRule := make([]*entity.RuleItem, 0)
	fileRule := make([]*entity.RuleItem, 0)
	for _, file := range reviewBlock.Rules {
		for _, rule := range file.Rules {
			if rule.RuleType == entity.RuleType_Branch {
				branchRule = append(branchRule, rule)
			} else {
				fileRule = append(fileRule, rule)
			}
		}
	}
	// 添加Branch Reviewer
	reviewerSet.AddN(fillBranchRule(ctx, reviewBlock.Global, devInfo.TrunkBranch, branchRule, reviewerSet, groupMemberUsernames)...)
	// 添加File Reviewer
	reviewerSet.AddN(fillFileRule(ctx, reviewBlock.Global, gslice.Map(diff, func(f *git_server_v2.DiffFile) string { return f.Path }), fileRule, reviewerSet, groupMemberUsernames)...)
	return &entity.ApproveBlock[entity.RuleApproveDetail]{
		DevID:  uint64(devId),
		Type:   entity.ApproveBlockType_Rule,
		Status: entity.ApproveStatus_Running,
		Detail: datatypes.NewJSONType[entity.RuleApproveDetail](*reviewBlock),
	}, reviewerSet.ToSlice(), nil
}

func matchRule(ctx context.Context, pattern string, toMatch []string) []string {
	regexp, err := pcre2.Compile(pattern)
	if err != nil {
		log.V1.CtxError(ctx, "invalid regexp:%s,%s", pattern, err.Error())
		return nil
	}
	defer func(regexp *pcre2.Regexp) {
		err := regexp.Free()
		if err != nil {
			log.V1.CtxError(ctx, "failed to free regexp:%s", err.Error())
		}
	}(regexp) // 使用了系统PCRE库，需要手动释放
	return gslice.Filter(toMatch, func(s string) bool {
		return regexp.MatchString(s)
	})
}

// 填充Maintainers
func fillMaintainers(ctx context.Context, global *entity.GlobalReviewConfig, reviewerSet *set.Set[string]) []string {
	if global.ReviewMode == entity.ReviewMode_LOOSE {
		global.MaintainerHits = global.MaintainerCandidates
	} else {
		global.MaintainerHits = randomPick(ctx, global.MaintainerCandidates, global.MaintainerRequired, reviewerSet)
	}
	return global.MaintainerHits
}

// 填充branch rule, branch rule永远都是Pick All
func fillBranchRule(ctx context.Context, config *entity.GlobalReviewConfig, targetBranch string, branchRule []*entity.RuleItem, originSet *set.Set[string], sameGroup []string) []string {
	reviewerSet := set.New[string]()
	if config.ReviewMode == entity.ReviewMode_LOOSE {
		for _, rule := range branchRule {
			reviewerSet.AddN(fillPickAll(ctx, set.New(targetBranch), rule, config.PickStrategy == entity.PickStrategy_First)...)
		}
	} else {
		for _, rule := range branchRule {
			reviewerSet.AddN(fillRandomPickWithGroup(ctx, set.New(targetBranch), rule, originSet, sameGroup, config.PickStrategy == entity.PickStrategy_First)...)
		}
	}
	return reviewerSet.ToSlice()
}

func fillFileRule(ctx context.Context, config *entity.GlobalReviewConfig, diffs []string, rules []*entity.RuleItem, originSet *set.Set[string], sameGroup []string) []string {
	reviewerSet := set.New[string]()
	diffSet := set.New(diffs...)
	if config.ReviewMode == entity.ReviewMode_LOOSE {
		for _, rule := range rules {
			reviewerSet.AddN(fillPickAll(ctx, diffSet, rule, config.PickStrategy == entity.PickStrategy_First)...)
		}
	} else {
		for _, rule := range rules {
			reviewerSet.AddN(fillRandomPickWithGroup(ctx, diffSet, rule, originSet, sameGroup, config.PickStrategy == entity.PickStrategy_First)...)
		}
	}
	return reviewerSet.ToSlice()
}

func fillRandomPickWithGroup(ctx context.Context, diff *set.Set[string], rule *entity.RuleItem, originSet *set.Set[string], sameGroup []string, pickFirst bool) []string {
	if hit := matchRule(ctx, rule.Pattern2, diff.ToSlice()); len(hit) > 0 {
		if pickFirst {
			diff.RemoveN(hit...)
		}
		rule.Hits = hit
		sameGroupCandidate := gslice.Intersect(rule.Candidates, sameGroup)
		if int64(len(sameGroupCandidate)) >= rule.Required { // 够用
			rule.Reviewers = randomPick(ctx, sameGroupCandidate, rule.Required, originSet)
		} else {
			candidateSet := set.New(rule.Candidates...)
			rule.Reviewers = sameGroupCandidate // 先添加和作者一个组的人
			candidateSet.RemoveN(rule.Reviewers...)
			//剩下的人因为一定不在共同组里，所以用origin就行了
			rule.Reviewers = append(rule.Reviewers, randomPick(ctx, candidateSet.ToSlice(), rule.Required-int64(len(sameGroupCandidate)), originSet)...)
		}
	}
	return rule.Reviewers
}

func fillPickAll(ctx context.Context, diff *set.Set[string], rule *entity.RuleItem, pickFirst bool) []string {
	hit := matchRule(ctx, rule.Pattern2, diff.ToSlice())
	if len(hit) == 0 {
		return nil
	}
	if pickFirst {
		diff.RemoveN(hit...)
	}
	rule.Reviewers = rule.Candidates
	rule.Hits = hit
	return rule.Hits
}

/*
randomPick 随机选review人
名义上随机选，其实不是，尽量用最少的人数达成要求，所以要先看Set里面有没有，有的话就先用了
*/
func randomPick[T comparable](ctx context.Context, candidates []T, need int64, originSet *set.Set[T]) []T {
	reviewerSet := set.New[T]()
	candidateSet := set.New(candidates...)
	count := int64(0)
	for _, candidate := range candidates {
		if originSet.Contains(candidate) {
			reviewerSet.Add(candidate)
			candidateSet.Remove(candidate)
			count++
			if count == need { // 够了
				break
			}
		}
	}
	// 从剩下的candidate里面随机选
	need -= count
	if need > 0 {
		if need > int64(candidateSet.Len()) {
			log.V1.CtxWarn(ctx, "candidate is not enough!")
			reviewerSet.AddN(candidateSet.ToSlice()...)
		} else {
			reviewerSet.AddN(utils.RandomPick(candidateSet.ToSlice(), need)...)
		}
	}
	return reviewerSet.ToSlice()
}

func getSameGroupMembers(ctx context.Context, appId int64, author string, reviewGroupId []int64) ([]string, error) {
	if appId == 0 {
		return []string{}, nil
	}

	memberGroup, err := rpcService.GetUserTeams(ctx, appId, author)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get user teams:%s", err.Error())
		return nil, err
	}
	commonGroup := gslice.Intersect(memberGroup, reviewGroupId)
	if len(commonGroup) == 0 { // 用户不属于这些group
		return nil, nil
	}
	nakama := skipset.New[string]()
	wg := sync.WaitGroup{}
	for _, groupId := range commonGroup {
		wg.Add(1)
		groupId := groupId
		go func() {
			defer wg.Done()
			groupMember, err := rpcService.GetTeamMembers(ctx, appId, groupId)
			if err != nil {
				log.V1.CtxError(ctx, "failed to get team members:%s", err.Error())
				return
			}
			gslice.ForEach(groupMember, func(v string) { nakama.Add(v) })
		}()
	}
	wg.Done()
	res := make([]string, 0)
	nakama.Range(func(v string) bool { res = append(res, v); return true })
	return res, nil
}

func getRuleBlockReviewers(_ context.Context, block *yacr.ApproveBlocks) ([]string, error) {
	reviewerSet := set.New[string]()
	reviewerSet.AddN(block.Detail.Rule.Global.MaintainerHits...)
	for _, file := range block.Detail.Rule.Rules {
		for _, rule := range file.Rules {
			reviewerSet.AddN(rule.Reviewers...)
		}
	}
	return reviewerSet.ToSlice(), nil
}

// 单条Review状态
func getSingleRuleStatus(ctx context.Context, reviewers []string, required int64, a map[string]*entity.UserApproveStatus, strictMode bool) entity.ApproveStatus {
	approveCount := int64(0)
	status := entity.ApproveStatus_Approved
	for _, reviewer := range reviewers {
		if approveStatus, ok := a[reviewer]; ok {
			if approveStatus.Status == entity.ApproveStatus_Approved || approveStatus.Status == entity.ApproveStatus_AlwaysApproved {
				approveCount++
				continue
			} else if approveStatus.Status == entity.ApproveStatus_Rejected {
				log.V1.CtxInfo(ctx, "%s rejected", reviewer)
				return entity.ApproveStatus_Rejected // 有拒绝直接死刑
			}
		}
		if strictMode { // 走到这里说明有人没审批
			status = entity.ApproveStatus_Running
		}
	}
	if approveCount < required {
		return entity.ApproveStatus_Running
	}
	return status
}

func getRuleBlockStatus(ctx context.Context, b *entity.ApproveBlock[entity.IApproveBlock], a map[string]*entity.UserApproveStatus, tx *gorm.DB) (entity.ApproveStatus, error) {
	detail := b.Detail.Data().(entity.RuleApproveDetail)
	status := entity.ApproveStatus_Approved
	isStrict := detail.Global.ReviewMode == entity.ReviewMode_STRICT
	// 先看maintainer
	{
		result := getSingleRuleStatus(ctx, detail.Global.MaintainerHits, detail.Global.MaintainerRequired, a, isStrict)
		if result == entity.ApproveStatus_Rejected { // 有拒绝直接死刑
			log.V1.CtxInfo(ctx, "maintainer review rejected")
			goto retReject
		} else {
			if result != entity.ApproveStatus_Approved {
				log.V1.CtxInfo(ctx, "maintainer review not passed, status: %s", result.String())
				status = result
			}
		}
	}

	// 看Rule
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			if len(rule.Hits) == 0 {
				continue
			}
			result := getSingleRuleStatus(ctx, rule.Reviewers, rule.Required, a, isStrict)
			if result == entity.ApproveStatus_Rejected {
				log.V1.CtxInfo(ctx, "rule review rejected")
				goto retReject
			} else {
				if result != entity.ApproveStatus_Approved {
					log.V1.CtxInfo(ctx, "rule %s:%s review not passed, status: %s", rule.RuleType, rule.Pattern2, result.String())
					status = result
				}
			}
		}
	}

	// 看总approve人数
	if detail.Global.AllRequired > 0 {
		approveCount := int64(0)
		for _, v := range a {
			if v.Status == entity.ApproveStatus_Approved || v.Status == entity.ApproveStatus_AlwaysApproved {
				approveCount++
			}
		}
		if approveCount < detail.Global.AllRequired {
			log.V1.CtxInfo(ctx, "approve count not enough, required: %d, actual: %d", detail.Global.AllRequired, approveCount)
			status = entity.ApproveStatus_Running
		}
	}
	return status, nil

retReject:
	return entity.ApproveStatus_Rejected, nil
}

func getRemovableRuleBlock(ctx context.Context, username string, block *yacr.ApproveBlocks) (bool, error) {
	detail := block.Detail.Rule
	if detail.Global.ReviewMode == yacr.ReviewMode_LOOSE { // 宽松模式不样删
		return false, nil
	}
	if gslice.Contains(detail.Global.MaintainerHits, username) {
		if int64(len(detail.Global.MaintainerHits)-1) < detail.Global.MaintainerRequired { // 删了不满足 over
			return false, nil
		}
	}
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			if gslice.Contains(rule.Reviewers, username) {
				if int64(len(rule.Reviewers)-1) < rule.Required { // 删了不满足 over
					return false, nil
				}
			}
		}
	}
	return true, nil
}

func getRuleBlockCandidate(ctx context.Context, username string, block *yacr.ApproveBlocks) (*yacr.UserCandidate, error) {
	detail := block.Detail.Rule
	// 宽松模式不给换
	if detail.Global.ReviewMode == yacr.ReviewMode_LOOSE {
		return &yacr.UserCandidate{
			Type:       yacr.CandidateType_None,
			Candidates: nil,
		}, nil
	}
	// 严格模式
	// 先看maintainer
	candidateList := set.New[string]()
	flag := false
	if gslice.Contains(detail.Global.MaintainerHits, username) {
		flag = true
		candidateList.AddN(gslice.Diff(detail.Global.MaintainerCandidates, detail.Global.MaintainerHits)...)
	}
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			if gslice.Contains(rule.Reviewers, username) {
				notSelected := gslice.Diff(rule.Candidates, rule.Reviewers)
				if !flag {
					flag = true
					candidateList.AddN(notSelected...)
				} else {
					candidateList.IntersectInplace(set.New(notSelected...))
				}
			}
		}
	}
	candidateList.Remove(username) // 删掉自己
	if !flag {                     // 不是Review人，ALL
		return &yacr.UserCandidate{
			Type:       yacr.CandidateType_All,
			Candidates: nil,
		}, nil
	}
	if candidateList.Len() == 0 {
		return &yacr.UserCandidate{
			Type:       yacr.CandidateType_None,
			Candidates: nil,
		}, nil
	}
	return &yacr.UserCandidate{
		Type:       yacr.CandidateType_Range,
		Candidates: candidateList.ToSlice(),
	}, nil
}

func updateRuleBlockTx(ctx context.Context, tx *gorm.DB, devId int64, old, new *yacr.ApproveBlocks) (*entity.ApproveBlock[entity.IApproveBlock], error) {
	if gptr.Indirect(new.Id) != 0 && old.Detail.Rule.Revision != new.Detail.Rule.Revision { // 如果ID为空，说明是同步配置，这里让他过
		log.V1.CtxError(ctx, "rule revision not match")
		return nil, bits_err.COMMON.ErrInvalidInput.AddErrMsg("config not latest, please refresh page")
	}
	blockEntity, err := data.GetApproveBlockById(ctx, old.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDBError.AddError(err)
	}
	if blockEntity == nil {
		log.V1.CtxError(ctx, "approve block not found")
		return nil, bits_err.COMMON.ErrRecordNotFound
	}
	if blockEntity.DevID != uint64(devId) {
		log.V1.CtxError(ctx, "approve block not belong to dev")
		return nil, bits_err.YACR.ErrApproveBlockNotBelongToDev
	}
	oldReviewer, err := getRuleBlockReviewers(ctx, old)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get old block reviewers, err:%s", err.Error())
		return nil, bits_err.COMMON.ErrSystemException.AddError(err)
	}
	newReviewer, err := getRuleBlockReviewers(ctx, new)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get new block reviewers, err:%s", err.Error())
		return nil, bits_err.COMMON.ErrSystemException.AddError(err)
	}
	oldReviewersMap := make(map[string]bool)
	for _, reviewer := range oldReviewer {
		oldReviewersMap[reviewer] = true
	}
	for _, reviewer := range newReviewer {
		if _, ok := oldReviewersMap[reviewer]; !ok {
			err := userService.UpsertUserApproveInfo(ctx, devId, reviewer, "bits", true, tx)
			if err != nil {
				log.V1.CtxError(ctx, "failed to create or update approvers, err:%s", err.Error())
				return nil, bits_err.YACR.ErrDBError.AddError(err)
			}
		}
	}
	blockEntity.Detail = datatypes.NewJSONType[entity.IApproveBlock](entity.NewRuleApproveDetailFromIDL(new.Detail.Rule))
	return blockEntity, nil
}

func removeRuleBlockReviewer(ctx context.Context, username string, block *yacr.ApproveBlocks, tx *gorm.DB) error {
	detail := block.Detail.Rule
	removeFlag := false
	// 先看maintainer
	if gslice.Contains(detail.Global.MaintainerHits, username) {
		removeFlag = true
		detail.Global.MaintainerHits = gslice.Remove(detail.Global.MaintainerHits, username)
		if len(detail.Global.MaintainerHits) < int(detail.Global.MaintainerRequired) {
			err := fmt.Errorf("maintainer required can't be satisfied, required %d, got %d", detail.Global.MaintainerRequired, len(detail.Global.MaintainerHits))
			log.V1.CtxError(ctx, err.Error())
			return bits_err.YACR.ErrRuleNotSatifastied.AddError(err)
		}
	}
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			if gslice.Contains(rule.Reviewers, username) {
				removeFlag = true
				rule.Reviewers = gslice.Remove(rule.Reviewers, username)
				if int64(len(rule.Reviewers)) < rule.Required {
					err := fmt.Errorf("rule %s required can't be satisfied, required %d, got %d", rule.Pattern, rule.Required, len(rule.Reviewers))
					log.V1.CtxError(ctx, err.Error())
					return bits_err.YACR.ErrRuleNotSatifastied.AddError(err)
				}
			}
		}
	}
	oldBlock, err := data.GetApproveBlockById(ctx, block.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	oldBlock.Detail = datatypes.NewJSONType[entity.IApproveBlock](entity.NewRuleApproveDetailFromIDL(detail))
	if removeFlag {
		status, err := calculateBlockStatus(ctx, oldBlock, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to calculate block status:%s", err.Error())
			return err
		}
		oldBlock.Status = status
	}
	_, err = data.UpdateBlocksTx(ctx, tx, oldBlock)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update simple approve block, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	return nil
}

func replaceRuleBlockReviewer(ctx context.Context, username, newReviewer string, block *yacr.ApproveBlocks, tx *gorm.DB) error {
	detail := block.Detail.Rule
	replacedFlag := false
	// 先看maintainer
	if i, ok := gslice.Index(detail.Global.MaintainerHits, username).Get(); ok {
		detail.Global.MaintainerHits[i] = newReviewer
		replacedFlag = true
	}
	for _, file := range detail.Rules {
		for _, rule := range file.Rules {
			if i, ok := gslice.Index(rule.Reviewers, username).Get(); ok {
				rule.Reviewers[i] = newReviewer
				replacedFlag = true
			}
		}
	}

	oldBlock, err := data.GetApproveBlockById(ctx, block.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	oldBlock.Detail = datatypes.NewJSONType[entity.IApproveBlock](entity.NewRuleApproveDetailFromIDL(detail))
	if replacedFlag {
		status, err := calculateBlockStatus(ctx, oldBlock, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to calculate block status:%s", err.Error())
			return err
		}
		oldBlock.Status = status
	}
	_, err = data.UpdateBlocksTx(ctx, tx, oldBlock)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update simple approve block, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	return nil
}

func replaceSimpleApproveBlockReviewer(ctx context.Context, username, newReviewer string, block *yacr.ApproveBlocks, tx *gorm.DB) error {
	detail := block.Detail.Simple
	replacedFlag := false
	for _, reviewer := range detail.Approvers {
		if reviewer.Name == username {
			reviewer.Name = newReviewer
			replacedFlag = true
		}
	}
	oldBlock, err := data.GetApproveBlockById(ctx, block.GetId(), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get approve block by id, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	oldBlock.Detail = datatypes.NewJSONType[entity.IApproveBlock](entity.NewSimpleApproveDetailFromIDL(detail))
	if replacedFlag {
		status, err := calculateBlockStatus(ctx, oldBlock, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to calculate block status:%s", err.Error())
			return err
		}
		oldBlock.Status = status
	}
	_, err = data.UpdateBlocksTx(ctx, tx, oldBlock)
	if err != nil {
		log.V1.CtxError(ctx, "failed to update simple approve block, err:%s", err.Error())
		return bits_err.YACR.ErrDBError.AddError(err)
	}
	return nil
}

func resetRuleBlockStatus(ctx context.Context, devId int64, diff []*git_server.DiffFile, block *yacr.ApproveBlocks, tx *gorm.DB) ([]string, error) {
	oldBlock, err := data.GetBlockByID[entity.RuleApproveDetail](ctx, uint64(block.GetId()), tx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get blocks by dev id, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDBError.AddError(err)
	}
	if oldBlock == nil {
		return nil, nil
	}
	oldDetail := oldBlock.Detail.Data()
	devInfo, err := rpcService.GetDevInfo(ctx, devId)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get dev info, err:%s", err.Error())
		return nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	groupMemberUsernames, err := getSameGroupMembers(ctx, devInfo.AppId, devInfo.Author, oldDetail.Global.ReviewGroups)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get same group members:%s", err.Error())
		return nil, bits_err.YACR.ErrDownstreamError.AddError(err)
	}
	reviewerList, _ := getRuleBlockReviewers(ctx, block)
	reviewerSet := set.New[string](reviewerList...)

	resetSet := set.New[string]() // 需要重置的人
	addSet := set.New[string]()   // 需要加的人

	resetSet.AddN(resetMaintainers(oldDetail.Global)...)

	branchRule := make([]*entity.RuleItem, 0)
	fileRule := make([]*entity.RuleItem, 0)
	for _, file := range oldDetail.Rules {
		for _, rule := range file.Rules {
			if rule.RuleType == entity.RuleType_Branch {
				branchRule = append(branchRule, rule)
			} else {
				fileRule = append(fileRule, rule)
			}
		}
	}

	{
		reset, add := resetBranchRule(ctx, devInfo.TargetBranch, branchRule)
		resetSet.AddN(reset...)
		addSet.AddN(add...)
	}
	{
		reset, add := resetFileRule(ctx, oldDetail.Global, gslice.Map(diff, func(f *git_server.DiffFile) string { return f.Path }), fileRule, reviewerSet, groupMemberUsernames)
		resetSet.AddN(reset...)
		addSet.AddN(add...)
	}
	realResetSet := set.New[string]()
	for _, reviewer := range resetSet.ToSlice() {
		reset, err := userService.ResetUserApproveStatus(ctx, devId, reviewer, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to reset user approve status, err:%s", err.Error())
			return nil, bits_err.YACR.ErrDBError.AddError(err)
		}
		if reset {
			log.V1.CtxInfo(ctx, "reviewer %s is reset", reviewer)
			realResetSet.Add(reviewer)
		}
	}
	for _, reviewer := range addSet.ToSlice() {
		err := userService.UpsertUserApproveInfo(ctx, devId, reviewer, "bits", true, tx)
		if err != nil {
			log.V1.CtxError(ctx, "failed to upsert user approve info, err:%s", err.Error())
			return nil, bits_err.YACR.ErrDBError.AddError(err)
		}
	}
	if addSet.Len()+resetSet.Len() > 0 {
		oldBlock.Detail = datatypes.NewJSONType[entity.RuleApproveDetail](oldDetail)
		oldBlock.Status = entity.ApproveStatus_Running
		_, err = data.UpdateBlocksTx(ctx, tx, oldBlock)
		if err != nil {
			log.V1.CtxError(ctx, "failed to update simple approve block, err:%s", err.Error())
			return nil, bits_err.YACR.ErrDBError.AddError(err)
		}
	}
	return realResetSet.ToSlice(), nil
}

func resetMaintainers(global *entity.GlobalReviewConfig) []string {
	return global.MaintainerHits // maintainer 全部重置
}

func resetBranchRule(ctx context.Context, targetBranch string, branchRule []*entity.RuleItem) ([]string, []string) {
	addSet := set.New[string]()
	resetSet := set.New[string]()
	for _, rule := range branchRule {
		if hit := matchRule(ctx, rule.Pattern2, unsafe.Slice(&targetBranch, 1)); len(hit) > 0 {
			if len(rule.Hits) != 0 {
				resetSet.AddN(rule.Reviewers...)
			}
			// branch不可能有新添加，所以不用看了
		}
	}
	return resetSet.ToSlice(), addSet.ToSlice()
}

func resetFileRule(ctx context.Context, global *entity.GlobalReviewConfig, diffs []string, fileRule []*entity.RuleItem, reviewerSet *set.Set[string], nakama []string) ([]string, []string) {
	addSet := set.New[string]()
	resetSet := set.New[string]()
	pickedDiffSet := set.New[string]()
	for _, rule := range fileRule {
		if hits := matchRule(ctx, rule.Pattern2, diffs); len(hits) > 0 {
			if len(rule.Hits) != 0 {
				resetSet.AddN(rule.Reviewers...)
			} else { // 新命中的规则
				if global.PickStrategy == entity.PickStrategy_First {
					if pickedDiffSet.ContainsAny(hits...) { // pick first模式下，已经pick过了
						continue
					}
					pickedDiffSet.AddN(hits...)
				}
				rule.Hits = hits

				if global.ReviewMode == entity.ReviewMode_LOOSE {
					rule.Reviewers = rule.Candidates
				} else {
					sameGroupCandidate := gslice.Intersect(rule.Candidates, nakama)
					if int64(len(sameGroupCandidate)) >= rule.Required { // 够用
						rule.Reviewers = randomPick(ctx, sameGroupCandidate, rule.Required, reviewerSet)
					} else {
						candidateSet := set.New(rule.Candidates...)
						rule.Reviewers = sameGroupCandidate // 先添加和作者一个组的人
						candidateSet.RemoveN(rule.Reviewers...)
						//剩下的人因为一定不在共同组里，所以用origin就行了
						rule.Reviewers = append(rule.Reviewers, randomPick(ctx, candidateSet.ToSlice(), rule.Required-int64(len(sameGroupCandidate)), reviewerSet)...)
					}
				}
				addSet.AddN(rule.Reviewers...)
			}
		}
	}
	return resetSet.ToSlice(), addSet.ToSlice()
}

func IfIndependentBranchEnabled(ctx context.Context, spaceID int64) (bool, error) {
	spaceConfig, err := tcc.GetIndependentBranchEnabledSpace(ctx)
	if err != nil {
		log.V1.CtxError(ctx, "failed to get space config", err.Error())
		return false, err
	}
	if len(spaceConfig.SpaceIDs) > 0 && lo.Contains(spaceConfig.SpaceIDs, spaceID) {
		return true, nil
	}
	return false, nil
}
