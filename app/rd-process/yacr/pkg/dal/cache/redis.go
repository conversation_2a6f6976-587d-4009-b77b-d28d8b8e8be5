package cache

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/libs/connections/redis"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kv/goredis"
	"github.com/shamaton/msgpack/v2"
)

var (
	Optimus *goredis.Client
)

func Get[T any](ctx context.Context, key string) (*T, bool) {
	str := Optimus.WithContext(ctx).Get(key).Val()
	if str == "" {
		return nil, false
	}
	var value T
	err := msgpack.Unmarshal([]byte(str), &value)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to unmarshal value, key: %s, value: %s , error:%s", key, str, err)
		return nil, false
	}
	log.V1.CtxInfo(ctx, "get value from redis, key: "+key)
	return &value, true
}

func GetList[T any](ctx context.Context, key string) ([]*T, bool) {
	str := Optimus.WithContext(ctx).Get(key).Val()
	if str == "" {
		return nil, false
	}
	var value []*T
	err := msgpack.Unmarshal([]byte(str), &value)
	if err != nil {
		log.V1.CtxWarn(ctx, "failed to unmarshal value, key: %s, value: %s , error:%s", key, str, err)
		return nil, false
	}
	log.V1.CtxInfo(ctx, "get value from redis, key: "+key)
	return value, true
}

func Del(ctx context.Context, key string) error {
	log.V1.CtxInfo(ctx, "delete key from redis, key: "+key)
	return Optimus.WithContext(ctx).Del(key).Err()
}

func Set[T any](ctx context.Context, key string, value T, expired time.Duration) error {
	str, _ := msgpack.Marshal(value)
	return Optimus.WithContext(ctx).Set(key, str, expired).Err()
}

// MustInitialize initialize redis connection
func MustInitialize(config *Config) {
	if config.Optimus.PSM != "" {
		Optimus = redis.MustConnect(redis.NewPSMEndpoint(config.Optimus.PSM))
	} else {
		Optimus = redis.MustConnect(redis.NewIPEndpoint(config.Optimus.Host, config.Optimus.Port, nil, nil))
	}
	log.V1.CtxInfo(context.Background(), "redis Optimus init success")
}
