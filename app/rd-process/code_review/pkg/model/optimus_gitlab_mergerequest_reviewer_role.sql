CREATE TABLE `optimus_gitlab_mergerequest_reviewer_role` (
                                                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                             `rid` int(11) NOT NULL COMMENT 'rule id',
                                                             `mr_id` int(11) NOT NULL COMMENT 'mr id',
                                                             `reviewer_name` varchar(255) NOT NULL COMMENT 'review 人',
                                                             `type` varchar(50) NOT NULL DEFAULT 'rule' COMMENT '类型',
                                                             `project_id` int(11) NOT NULL DEFAULT '0' COMMENT 'project id',
                                                             `review_role` varchar(2) NOT NULL DEFAULT 'RD' COMMENT 'QA,RD options:{"with_default":true}',
                                                             `operator` varchar(255) NOT NULL DEFAULT 'system' COMMENT '操作人 options:{"with_default":true}',
                                                             PRIMARY KEY (`id`),
                                                             UNIQUE KEY `uniq_optimus_gitlab_mergerequest_review_reason_pk_2` (`rid`,`reviewer_name`,`mr_id`,`type`),
                                                             <PERSON><PERSON><PERSON> `idx_mr_id` (`mr_id`),
                                                             <PERSON><PERSON><PERSON> `idx_review_name` (`reviewer_name`),
                                                             KEY `idx_rid` (`rid`),
                                                             KEY `idx_project_id` (`project_id`),
                                                             KEY `idx_type` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COMMENT='Review reason options:{"alias":"ReviewerRulesRelation"}';