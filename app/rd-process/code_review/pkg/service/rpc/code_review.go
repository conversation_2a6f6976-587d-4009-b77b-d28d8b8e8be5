package rpc

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/kitex_gen/bytedance/bits/code_review"
	"code.byted.org/gopkg/logs"
)

func GetReviewStatus(ctx context.Context, mrID int64) (r []*code_review.ReviewStatus, err error) {
	reviewResult, err := codeReviewClient.GetReviewStatus(ctx, &code_review.GetReviewStatusReq{MrId: mrID})
	if err != nil {
		return
	} else if reviewResult == nil {
		err = errors.New("reviewResult==nil")
		logs.CtxError(ctx, err.Error())
		return
	}
	r = reviewResult.Status
	return
}
