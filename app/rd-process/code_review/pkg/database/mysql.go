package database

//noinspection ALL
import (
	"fmt"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/pkg/config"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

type mysqlConnectorItem struct {
	Master *gorm.DBProxy
	Slave  *gorm.DBProxy
}

type mysqlConnector struct {
	Optimus     *mysqlConnectorItem
	CommonData  *mysqlConnectorItem
	BitsQuality *mysqlConnectorItem
}

type mysqlConnection struct {
	Connector *mysqlConnector
}

func (m *mysqlConnection) get() interface{} {
	return m.Connector
}

// Init 初始化数据库连接
func (m *mysqlConnection) initial(conf *config.AppConfig) {
	connector := mysqlConnector{
		Optimus: &mysqlConnectorItem{
			m.create(&conf.Mysql.Optimus.Master),
			m.create(&conf.Mysql.Optimus.Slave),
		},
		CommonData: &mysqlConnectorItem{
			m.create(&conf.Mysql.CommonData.Master),
			m.create(&conf.Mysql.CommonData.Slave),
		},
		BitsQuality: &mysqlConnectorItem{
			m.create(&conf.Mysql.BitsQuality.Master),
			m.create(&conf.Mysql.BitsQuality.Slave),
		},
	}
	m.Connector = &connector
}

func (m *mysqlConnection) create(conf *config.CommonDbClient) *gorm.DBProxy {
	connector := m.getClient(&connectionOptions{
		Psm:      conf.PSM,
		Host:     conf.Host,
		Port:     conf.Port,
		Username: conf.Username,
		Password: conf.Password,
		Db:       conf.DB,
	})
	return connector
}

// GetClient 获取客户端
func (m *mysqlConnection) getClient(opts *connectionOptions) *gorm.DBProxy {
	var client *gorm.DBProxy
	var err error
	var dsn string

	if opts.Username != "" && opts.Password != "" {
		// rocksDB 没有dev的寻址用的BOE的
		dsn = fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=utf8&parseTime=True&loc=Local", opts.Username, opts.Password, fmt.Sprintf("%s:%d", opts.Host, opts.Port), opts.Db)
		client, err = gorm.POpen("mysql2", dsn)
	} else {
		dsn = fmt.Sprintf(":@tcp(%s)/%s?charset=utf8&parseTime=True&loc=Local", opts.Psm, opts.Db)
		client, err = gorm.POpen("mysql2", dsn)
	}

	if err != nil {
		logs.Info("mysql init error")
		panic(err)
	} else {
		logs.Info("mysql init success")
	}

	client.LogMode(true)
	client.SetConnMaxLifetime(30 * time.Second)
	client.SetMaxIdleConns(100)
	client.SetMaxOpenConns(1000)
	client.SingularTable(true)

	return client
}
