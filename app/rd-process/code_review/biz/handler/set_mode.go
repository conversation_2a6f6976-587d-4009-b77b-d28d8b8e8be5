package handler

import (
	"context"
	json "github.com/bytedance/sonic"

	code_review_model "code.byted.org/devinfra/hagrid/app/rd-process/code_review/kitex_gen/bytedance/bits/code_review_model"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/pkg/database"
	"code.byted.org/gopkg/logs"
)

func SetFixedReviewerMode(ctx context.Context, req *code_review_model.SetCustomConfigReq) (err error) {
	bytes, err := json.Marshal(req.CustomConfig)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return err
	}

	_, err = database.OptimusWriteOperator.UpdateReviewFlowStatusConfigJSON(ctx, req.HostMrId, string(bytes))
	return err
}
