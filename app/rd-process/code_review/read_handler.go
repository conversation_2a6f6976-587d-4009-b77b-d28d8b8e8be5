package main

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/biz/handler"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/biz/model_handler"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/kitex_gen/base"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/kitex_gen/bytedance/bits/code_review"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/kitex_gen/bytedance/bits/code_review_model"
	"code.byted.org/devinfra/hagrid/app/rd-process/code_review/pkg/service/rpc"
	"code.byted.org/gopkg/logs"
)

// CodeReviewServiceImpl implements the last service interface defined in the IDL.
type CodeReviewServiceImpl struct{}

func (s *CodeReviewServiceImpl) ListOnlineReviewers(ctx context.Context, req *code_review.ListOnlineReviewersReq) (r *code_review.ListOnlineReviewersResp, err error) {
	return handler.ListOnlineReviewers(ctx, req)
}

// ListReviewTabMrRepo implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) ListReviewTabMrRepo(ctx context.Context, req *code_review.ListReviewTabMrRepoReq) (resp *code_review.ListReviewTabMrRepoResp, err error) {
	return handler.ListReviewTabMrRepo(ctx, req)
}

// ListReviewTabMrRepoComponent implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) ListReviewTabMrRepoComponent(ctx context.Context, req *code_review.ListReviewTabMrRepoComponentReq) (resp *code_review.ListReviewTabMrRepoComponentResp, err error) {
	// 获取前端页面展示的子仓的 diff 的 tab
	return handler.ListReviewTabMrRepoComponent(ctx, req)
}

// ListReviewTabVersionRepo implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) ListReviewTabVersionRepo(ctx context.Context, req *code_review.ListReviewTabVersionRepoReq) (resp *code_review.ListReviewTabVersionRepoResp, err error) {
	// 获取前端页面展示的 PodChange 的 diff 的 tab
	return handler.ListReviewTabVersionRepo(ctx, req)
}

// CheckStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) CheckStatus(ctx context.Context, req *code_review.CheckStatusReq) (*code_review.CheckStatusResp, error) {
	hostMrIDInfo, err := rpc.GetHostMrIDInfo(ctx, req.MrId)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}

	// handler.CheckStatus(ctx, req.MrId)
	return model_handler.GetStatus(ctx, hostMrIDInfo.MrID)
}

// CheckSingleStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) CheckSingleStatus(ctx context.Context, req *code_review.CheckStatusReq) (*code_review.CheckStatusResp, error) {
	hostMrIDInfo, err := rpc.GetHostMrIDInfo(ctx, req.MrId)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}

	//res, reason, err := handler.CheckSingleStatus(ctx, req.MrId)
	return model_handler.GetRepoStatus(ctx, hostMrIDInfo.MrID, req.MrId)
}

// GetReviewStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewStatus(ctx context.Context, req *code_review.GetReviewStatusReq) (*code_review.GetReviewStatusResp, error) {
	hostMrIDInfo, err := rpc.GetHostMrIDInfo(ctx, req.MrId)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}

	//return handler.GetReviewStatus(ctx, req)
	modelReviewDetail, err := rpc.ModelGetTreeReviewDetail(ctx, hostMrIDInfo.MrID, true)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	return model_handler.GetReviewStatus(ctx, modelReviewDetail)
}

// GetMRReviewInfo implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetMRReviewInfo(ctx context.Context, req *code_review.GetMRReviewInfoReq) (resp *code_review.GetMRReviewInfoResp, err error) {
	//return handler.GetMrReviewInfo(ctx, req)
	return model_handler.GetMrReviewInfo(ctx, req)
}

// GetQAStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetQAStatus(ctx context.Context, req *code_review.GetQAStatusReq) (*code_review.GetQAStatusResp, error) {
	resp := &code_review.GetQAStatusResp{}
	hostMrIDInfo, err := rpc.GetHostMrIDInfo(ctx, req.MrId)
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return resp, err
	}
	resp.Approved, err = rpc.ModelGetQAStatus(ctx, hostMrIDInfo.MrID)
	return resp, err
}

// BatchGetQAStatusMap implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) BatchGetQAStatusMap(ctx context.Context, req *code_review_model.BatchGetQAStatusMapReq) (resp *code_review_model.BatchGetQAStatusMapResp, err error) {
	return rpc.ModelBatchGetQAStatusMap(ctx, req.MrIds)
}

// GetReviewerStatusDetail implements the CodeReviewServiceImpl interface.
// 按全局与仓库聚合 {"bits_mr":[],"bm":{},"project":{"230522":[{"enName":"mayufeng","zhName":"马钰峰","status":false},{"enName":"lipeinan","zhName":"李培男","status":false},{"enName":"zhangtianhan","zhName":"张天汉","status":true}]}}
func (s *CodeReviewServiceImpl) GetReviewerStatusDetail(ctx context.Context, req *code_review.GetReviewerStatusDetailReq) (*code_review.GetReviewerStatusDetailResp, error) {
	// return handler.GetReviewerStatusDetail(ctx, req.MrId)
	return model_handler.GetReviewerStatusDetail(ctx, req.MrId)
}

// NoticeReview implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) NoticeReview(ctx context.Context, req *code_review.NoticeReviewReq) (*base.EmptyResponse, error) {
	// return &base.EmptyResponse{}, handler.NoticeReview(ctx, req)
	return &base.EmptyResponse{}, model_handler.NoticeReview(ctx, req)
}

// GetPreCreateCandidateList implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetPreCreateCandidateList(ctx context.Context, req *code_review.GetPreCreateCandidateListReq) (resp *code_review.GetPreCreateCandidateListResp, err error) {
	return handler.GetPreCreateCandidateList(ctx, req)
}

// GetReviewInfoMap implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewInfoMap(ctx context.Context, req *code_review.GetMRReviewInfoMapReq) (resp *code_review.GetMRReviewInfoMapResp, err error) {
	respMap := make(map[int64][]*code_review.GetMRReviewInfoMapRespGroup)
	modelReq := new(code_review.GetMRReviewInfoMapReq)
	for i := len(req.MrIds) - 1; i >= 0; i-- {
		modelReq.MrIds = append(modelReq.MrIds, req.MrIds[i])
		req.MrIds = append(req.MrIds[:i], req.MrIds[i+1:]...)
	}
	var originMap, modelMap map[int64][]*code_review.GetMRReviewInfoMapRespGroup
	var fetchMapErr error
	// 这里用于门户页面列表的展示，目前QPS较少，一个分页只会有5个项，加上mysql缓存到没事
	// 如果后面请求量上涨，在数据表上增加一个聚合状态字段
	if len(modelReq.MrIds) > 0 {
		modelMap, fetchMapErr = model_handler.GetReviewStatusMap(ctx, modelReq)
	}
	if len(req.MrIds) > 0 {
		originMap, fetchMapErr = handler.GetReviewInfoMap(ctx, req)
	}
	if fetchMapErr != nil {
		logs.CtxError(ctx, fetchMapErr.Error())
		return nil, fetchMapErr
	}
	for k, v := range originMap {
		respMap[k] = append(respMap[k], v...)
	}
	for k, v := range modelMap {
		respMap[k] = append(respMap[k], v...)
	}

	return &code_review.GetMRReviewInfoMapResp{InfoMap: respMap}, nil
}

// GetReviewerListWithMrID implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewerListWithMrID(ctx context.Context, req *code_review.GetReviewerListWithMrIDReq) (resp *code_review.GetReviewerListWithMrIDResp, err error) {
	//return handler.GetReviewerListWithMrID(ctx, req)
	return model_handler.GetReviewerListWithMrID(ctx, req)
}

// GetMrIDListFilterByNameAndState implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetMrIDListFilterByNameAndState(ctx context.Context, req *code_review.GetMrIDListFilterByNameAndStateReq) (resp *code_review.GetMrIDListFilterByNameAndStateResp, err error) {
	return handler.GetMrIDListFilterByNameAndState(ctx, req)
}

// GetReviewerCommentStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewerCommentStatus(ctx context.Context, req *code_review.GetReviewerCommentStatusReq) (resp *code_review.GetReviewerCommentStatusResp, err error) {
	return handler.GetReviewerCommentStatus(ctx, req)
}

// ---------------------------------------------------------------V2前端接口-----------------------------------------------------

// ListReviewInfoGroups implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) ListReviewInfoGroups(ctx context.Context, req *code_review.ListReviewInfoGroupsReq) (resp *code_review.ListReviewInfoGroupsResp, err error) {
	return model_handler.ListReviewInfoGroups(ctx, req)
}

// ListReviewRuleDetails implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewDetail(ctx context.Context, req *code_review.GetReviewDetailReq) (resp *code_review.GetReviewDetailResp, err error) {
	resp = &code_review.GetReviewDetailResp{}
	resp.Data, err = rpc.ModelGetTreeReviewDetail(ctx, req.HostMrId, false)
	return
}

// ListReviewRuleDetails implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewDetailBeforeNew(ctx context.Context, req *code_review.GetReviewDetailBeforeNewReq) (resp *code_review.GetReviewDetailResp, err error) {
	resp = &code_review.GetReviewDetailResp{}
	resp.Data, err = rpc.ModelGetTreeReviewDetailBeforeNew(ctx, req)
	return
}

// ---------------------------------------------------------------以下abandoned-------------------------------------------------------

// BatchGetCandidateList implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) BatchGetCandidateList(ctx context.Context, req *code_review.BatchGetCandidateListReq) (resp *code_review.BatchGetCandidateListResp, err error) {
	//return model_handler.BatchGetCandidateList(ctx, req.MrId)
	return nil, errors.New("BatchGetCandidateList abandoned")
}

// GetSingleReviewStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetSingleReviewStatus(ctx context.Context, req *code_review.GetReviewStatusReq) (*code_review.GetReviewStatusResp, error) {
	//return handler.GetReviewSingleStatus(ctx, req)
	return nil, errors.New("GetSingleReviewStatus abandoned")
}

// CanFastRemoveReviewer implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) CanFastRemoveReviewer(ctx context.Context, req *code_review.CanFastRemoveReviewerReq) (*code_review.CanFastRemoveReviewerResp, error) {
	//res, reason, err := handler.CanFastRemoveReviewer(ctx, req)
	//return &code_review.CanFastRemoveReviewerResp{CanFastRemove: res, Reasons: reason}, err
	return nil, errors.New("CanFastRemoveReviewer abandoned")
}

// ReviewNotMatchCheck implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) ReviewNotMatchCheck(ctx context.Context, req *code_review.ReviewNotMatchCheckReq) (*code_review.ReviewNotMatchCheckResp, error) {
	//return handler.CheckReviewMatchStatus(ctx, req.MrId)
	return nil, errors.New("ReviewNotMatchCheck abandoned")
}

// RuleNotMatchCheck implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) RuleNotMatchCheck(ctx context.Context, req *code_review.ReviewNotMatchCheckReq) (*code_review.RuleNotMatchCheckResp, error) {
	//res, err := handler.CheckRuleMatchStatus(ctx, req.MrId)
	//return &code_review.RuleNotMatchCheckResp{Status: res}, err
	return nil, errors.New("RuleNotMatchCheck abandoned")
}

// GetPreCreateCandidateBM implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetPreCreateCandidateBM(ctx context.Context, req *code_review.GetPreCreateCandidateBMReq) (*code_review.GetPreCreateCandidateResp, error) {
	//return handler.GetPreCreateCandidateBM(ctx, req)
	return nil, errors.New("GetPreCreateCandidateBM abandoned")
}

// GetPreCreateCandidateMaintainer implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetPreCreateCandidateMaintainer(ctx context.Context, req *code_review.GetPreCreateCandidateMaintainerReq) (*code_review.GetPreCreateCandidateResp, error) {
	//return handler.GetPreCreateCandidateMaintainer(ctx, req.ProjectId)
	return nil, errors.New("GetPreCreateCandidateMaintainer abandoned")
}

// GetPreCreateCandidateBranch implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetPreCreateCandidateBranch(ctx context.Context, req *code_review.GetPreCreateCandidateBranchReq) (*code_review.GetPreCreateBranchCandidateResp, error) {
	//return handler.GetPreCreateCandidateBranch(ctx, req.ProjectId, req.Branch, req.Operator)
	return nil, errors.New("GetPreCreateCandidateBranch abandoned")
}

// GetPreCreateCandidateFile implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetPreCreateCandidateFile(ctx context.Context, req *code_review.GetPreCreateCandidateFileReq) (*code_review.GetPreCreateCandidateFileResp, error) {
	//res, err := handler.GetPreCreateCandidateFile(ctx, req.MrId)
	//return &code_review.GetPreCreateCandidateFileResp{Files: res}, err
	return nil, errors.New("GetPreCreateCandidateFile abandoned")
}

// GetRule implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetRule(ctx context.Context, req *code_review.GetRuleReq) (*code_review.GetRuleResp, error) {
	//return handler.GetRule(ctx, req)
	return nil, errors.New("GetRule abandoned")
}

// GetCandidate implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetCandidate(ctx context.Context, req *code_review.GetCandidateReq) (*code_review.GetCandidateResp, error) {
	//return handler.GetCandidate(ctx, req)
	return nil, errors.New("GetCandidate abandoned")
}

// GetReviewerListByRule implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewerListByRule(ctx context.Context, req *code_review.GetReviewerListByRuleReq) (*code_review.GetReviewerListByRuleResp, error) {
	//return handler.GetReviewerListByRule(ctx, req)
	return nil, errors.New("GetReviewerListByRule abandoned")
}

// GetReviewFlowStatus implements the CodeReviewServiceImpl interface.
func (s *CodeReviewServiceImpl) GetReviewFlowStatus(ctx context.Context, req *code_review.GetReviewFlowStatusReq) (*code_review.GetReviewFlowStatusResp, error) {
	//res, err := handler.GetReviewFlowStatus(ctx, req)
	//return &code_review.GetReviewFlowStatusResp{Status: res}, err
	return nil, errors.New("GetReviewFlowStatus abandoned")
}
