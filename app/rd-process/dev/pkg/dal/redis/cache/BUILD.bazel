load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cache",
    srcs = [
        "dev_basic_info.go",
        "dev_collaborator.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis/cache",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "//app/rd-process/dev/pkg/dal/redis/dictionary",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//optional",
    ],
)
