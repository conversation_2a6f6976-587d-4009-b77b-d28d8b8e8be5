package dictionary

import (
	"encoding"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	json "github.com/bytedance/sonic"
)

type DevBasicInfoDictionary model.DevBasicInfo

func (d *DevBasicInfoDictionary) UnmarshalBinary(data []byte) error {
	return json.Unmarshal(data, d)
}

func (d *DevBasicInfoDictionary) MarshalBinary() (data []byte, err error) {
	return json.Marshal(d)
}

var _ encoding.BinaryMarshaler = &DevBasicInfoDictionary{}
var _ encoding.BinaryUnmarshaler = &DevBasicInfoDictionary{}
