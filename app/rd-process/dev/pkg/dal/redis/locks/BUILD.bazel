load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "locks",
    srcs = [
        "op_lock.go",
        "service.go",
        "utils.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis/locks",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_go_redis_redis//:redis",
        "@org_byted_code_gopkg_logid//:logid",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//optional",
    ],
)
