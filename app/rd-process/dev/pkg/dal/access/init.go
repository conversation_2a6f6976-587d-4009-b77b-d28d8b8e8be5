package access

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/libs/sdk/consistent_cache"
	"code.byted.org/kv/goredis"
	"code.byted.org/lang/gg/choose"
)

var globalCache *consistent_cache.Client

func Init(rdb *goredis.Client) {
	globalCache = consistent_cache.NewSimpleClient(rdb)
}

func getExpireTimeByModel(ctx context.Context, key string) time.Duration {
	cfg, err := tcc.GetCacheExpireConfig(ctx)
	if err != nil {
		return time.Microsecond
	}

	str, ok := cfg[key]
	if !ok {
		return time.Microsecond
	}

	dur, e := time.ParseDuration(str)
	return choose.If(e == nil, dur, time.Microsecond)
}
