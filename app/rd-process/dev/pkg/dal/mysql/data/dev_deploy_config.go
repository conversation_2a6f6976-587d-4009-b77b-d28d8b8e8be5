package data

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
)

func (repo *Repo) CreateDevDeployConfig(ctx context.Context, value *model.DevDeployConfig) gresult.R[int64] {
	err := repo.db.
		WithContext(ctx).
		Create(value).
		Error
	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(value.Id)
}

func (repo *Repo) CreateDevDeployConfigs(ctx context.Context, configs []*model.DevDeployConfig) error {
	if len(configs) == 0 {
		return nil
	}
	err := repo.db.WithContext(ctx).CreateInBatches(configs, 100).Error
	if err != nil {
		return bits_err.COMMON.ErrDBErr.AddError(err)
	}
	return nil
}

func (repo *Repo) FindDevDeployConfigById(ctx context.Context, id int64) gresult.R[*model.DevDeployConfig] {
	value := new(model.DevDeployConfig)

	err := repo.db.
		WithContext(ctx).
		Where("`id` = ?", id).
		Last(value).
		Error
	if err != nil {
		return gresult.Err[*model.DevDeployConfig](err)
	}
	return gresult.OK(value)
}

func (repo *Repo) GetDevDeployConfigsByDevBasicId(ctx context.Context, devBasicId int64) gresult.R[[]*model.DevDeployConfig] {
	answer := make([]*model.DevDeployConfig, 0, 16)
	partial := make([]*model.DevDeployConfig, 0, 20)
	if err := repo.db.WithContext(ctx).
		Where("dev_basic_id = ?", devBasicId).
		FindInBatches(&partial, 10, func(tx *gorm.DB, batch int) error {
			answer = append(answer, partial...)
			return nil
		}).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return gresult.Err[[]*model.DevDeployConfig](bits_err.COMMON.ErrRecordNotFound)
		}
		return gresult.Err[[]*model.DevDeployConfig](bits_err.COMMON.ErrRecordNotFound)
	}
	return gresult.OK(answer)
}

func (repo *Repo) BatchGetDevDeployConfigsByDevBasicIds(ctx context.Context, devBasicIds []int64) gresult.R[[]*model.DevDeployConfig] {
	answer := make([]*model.DevDeployConfig, 0)
	partial := make([]*model.DevDeployConfig, 0, 1000)
	if err := repo.db.WithContext(ctx).
		Where("dev_basic_id in ?", devBasicIds).
		FindInBatches(&partial, 1000, func(tx *gorm.DB, batch int) error {
			answer = append(answer, partial...)
			return nil
		}).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return gresult.Err[[]*model.DevDeployConfig](bits_err.COMMON.ErrRecordNotFound)
		}
		return gresult.Err[[]*model.DevDeployConfig](bits_err.COMMON.ErrDBErr.AddError(err))
	}
	return gresult.OK(answer)
}

func (repo *Repo) UpdateDevDeployConfigById(ctx context.Context, id int64, updated *model.DevDeployConfig) error {
	err := repo.db.
		WithContext(ctx).
		Where("`id` = ?", id).
		Updates(updated).
		Error

	if err != nil {
		return err
	}
	return nil
}

func (repo *Repo) DeleteDeployConfigsByIds(ctx context.Context, ids []int64) error {
	err := repo.db.WithContext(ctx).
		Where("id in ?", ids).
		Delete(&model.DevDeployConfig{}).
		Error
	if err != nil {
		return bits_err.COMMON.ErrDBErr.AddError(err)
	}
	return nil
}

func (repo *Repo) GetNumberOfDevDeployConfigsByDevBasicId(ctx context.Context, id int64) gresult.R[int64] {
	var count int64
	err := repo.db.WithContext(ctx).
		Model(&model.DevDeployConfig{}).
		Where("`dev_basic_id` = ?", id).
		Count(&count).
		Error
	if err != nil {
		return gresult.Err[int64](err)
	}
	return gresult.OK(count)
}
