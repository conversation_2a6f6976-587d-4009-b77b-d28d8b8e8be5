package repository

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/lang/gg/gresult"
	"gorm.io/gorm"
)

type OptimusProjectRepository struct {
	db *gorm.DB
}

func NewOptimusProjectRepository(db *gorm.DB) *OptimusProjectRepository {
	return &OptimusProjectRepository{db: db}
}

func (r *OptimusProjectRepository) FindByCodebaseRepoIds(ctx context.Context, codebaseRepoIds []int64) gresult.R[[]*model.OptimusProject] {
	var result = make([]*model.OptimusProject, 0)
	err := r.db.WithContext(ctx).
		Where("codebase_repo_id in ?", codebaseRepoIds).
		Limit(1000).
		Find(&result).
		Error
	if err != nil {
		return gresult.Err[[]*model.OptimusProject](err)
	}
	return gresult.OK(result)
}

func (r *OptimusProjectRepository) BatchCreate(ctx context.Context, projects []*model.OptimusProject) error {
	return r.db.WithContext(ctx).CreateInBatches(projects, 10).Error
}
