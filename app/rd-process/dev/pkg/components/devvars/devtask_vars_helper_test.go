package devvars

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/bits/monkey"
	bytetreeProvider "code.byted.org/canal/provider/bytetree"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	bitsModel "code.byted.org/devinfra/hagrid/libs/common_lib/model"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb/varstoreservice_mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app/apprpc_mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/appcenter"
	spacerpcpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb/spacerpcapi_mock"
	"code.byted.org/gopkg/metainfo"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/sre/bytetree_go_sdk/schema"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	jsoniter "github.com/json-iterator/go"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
)

func TestGetCustomVariables(t *testing.T) {
	g := VarGetter{
		DevBasicId: 1,
		TemplateId: 1,
		SpaceId:    1,
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	rpc.VarStoreClient = mockVarstoreCli
	mockVarstoreCli.EXPECT().
		QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupByAssociationResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId:     1,
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_RELEASE_TICKET,
				},
			},
			FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
				{
					GroupId: 1,
					Version: 2,
					Definition: &varstorepb.VarDefinition{
						Name: "key1",
					},
				},
				{
					GroupId: 2,
					Version: 2,
					Definition: &varstorepb.VarDefinition{
						Name: "key1",
					},
				},
			},
		}, nil).AnyTimes()

	res, err := g.getCustomVariables(context.Background()).Get()
	assert.Nil(t, err)
	t.Log(res)
}

func TestGetProjectVars(t *testing.T) {
	g := VarGetter{
		DevBasicId: 1,
		TemplateId: 1,
		SpaceId:    1,
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	rpc.VarStoreClient = mockVarstoreCli
	mockVarstoreCli.EXPECT().
		QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupByAssociationResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId: 1,
					Name: &varstorepb.StringInMultiLang{
						Value: "name1",
						Cn:    "名称1",
						En:    "name1",
						Lang:  "cn",
					},
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key1",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_PROJECT,
				},
				{
					GroupId: 2,
					Name: &varstorepb.StringInMultiLang{
						Value: "name2",
						Cn:    "名称2",
						En:    "name2",
						Lang:  "cn",
					},
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key2",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_PROJECT,
				},
			},
			FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
				{
					GroupId: 1,
					Version: 2,
					Definition: &varstorepb.VarDefinition{
						Name: "key1",
					},
				},
				{
					GroupId: 2,
					Version: 1,
					Definition: &varstorepb.VarDefinition{
						Name: "key2",
					},
				},
			},
			Associations: []*varstorepb.VarGroupAssociation{
				{
					GroupId:        1,
					AssociationKey: "project:PROJECT_TYPE_TCE+p.s.m1",
				},
				{
					GroupId:        1,
					AssociationKey: "project:PROJECT_TYPE_TCE+p.s.m2",
				},
				{
					GroupId:        2,
					AssociationKey: "project:PROJECT_TYPE_WEB+p.s.m3",
				},
			},
		}, nil).AnyTimes()

	t.Run("success", func(t *testing.T) {
		resp, err := g.GetProjectVars(context.Background(), []*dev.BcProjectInfo{
			{
				ProjectType:     1,
				ProjectName:     "p.s.m1",
				ProjectUniqueId: "p.s.m1",
			},
		}, GetVarsSceneTypePipelineRun).Get()
		assert.Nil(t, err)
		t.Log(resp)
		assert.Equal(t, true, len(resp) > 0)
	})

	t.Run("pipeline run var without projects", func(t *testing.T) {
		resp, err := g.GetProjectVars(context.Background(), []*dev.BcProjectInfo{}, GetVarsSceneTypePipelineRun).Get()
		assert.Nil(t, err)
		t.Log(resp)
		assert.Equal(t, true, len(resp) == 0)
	})

}

func Test_SortVarsByCustomScopeAndName(t *testing.T) {
	vars := []*dev.Variable{
		{
			Definition: &dev.VariableDefination{
				Name: "custom.c",
			},
			CustomScope: dev.CustomScope_CUSTOM_SCOPE_WORKSPACE,
		},
		{
			Definition: &dev.VariableDefination{
				Name: "custom.b",
			},
			CustomScope: dev.CustomScope_CUSTOM_SCOPE_DEV_TASK,
		},
		{
			Definition: &dev.VariableDefination{
				Name: "custom.a",
			},
			CustomScope: dev.CustomScope_CUSTOM_SCOPE_DEV_TASK,
		},
		{
			Definition: &dev.VariableDefination{
				Name: "custom.d",
			},
			CustomScope: dev.CustomScope_CUSTOM_SCOPE_WORKSPACE,
		},
	}

	got := SortVarsByCustomScopeAndName(vars)
	t.Log(got)
}

func TestGetSystemVars(t *testing.T) {
	g := VarGetter{
		DevBasicId: 1,
		TemplateId: 1,
		SpaceId:    1,
	}
	mockey.Mock((*data.Repo).GetDevBasicConfigByDevBasicId).Return(gresult.OK(&model.DevBasicConfig{
		BizConfig: model.DevBasicConfigBizConfig{VariableRecord: &dev.VariableRecord{SystemAssignmentId: lo.ToPtr(int64(1))}},
	})).Build()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	rpc.VarStoreClient = mockVarstoreCli
	mockVarstoreCli.EXPECT().
		QueryVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId: 1,
				},
			},
		}, nil).AnyTimes()

	got, err := g.getSystemVars(context.Background()).Get()
	assert.NoError(t, err)
	fmt.Println(got)
}

func TestGetByteTreeVars(t *testing.T) {
	g := VarGetter{
		DevBasicId: 1,
		TemplateId: 1,
		SpaceId:    1,
	}
	ctx := context.Background()
	var bffInfo bitsModel.BFFInfo
	bffInfo.JWT = "jwt"
	bffInfoStr, _ := jsoniter.MarshalToString(&bffInfo)
	ctx = metainfo.WithValue(ctx, "bff_info", bffInfoStr)
	monkey.Patch(bytetreeProvider.GetNodeByPSM, func(ctx context.Context, psm string, userJwt *string) (*schema.ServiceTreeNodeDetail, bool, error) {
		return &schema.ServiceTreeNodeDetail{
			ServiceTreeNode: schema.ServiceTreeNode{ID: 1, Name: "name"},
		}, true, nil
	})
	monkey.Patch(bytetreeProvider.GetAllParentNodeInfoByID, func(ctx context.Context, nodeID int64) ([]schema.ServiceTreeNodeDetail, bool, error) {
		return []schema.ServiceTreeNodeDetail{
			schema.ServiceTreeNodeDetail{
				ServiceTreeNode: schema.ServiceTreeNode{ID: 0, Name: "根节点"},
			},
		}, true, nil
	})

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	rpc.VarStoreClient = mockVarstoreCli
	mockVarstoreCli.EXPECT().
		QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupByAssociationResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId: 1,
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key1",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_BYTE_TREE,
				},
				{
					GroupId: 2,
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key2",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_BYTE_TREE,
				},
			},
			Associations: []*varstorepb.VarGroupAssociation{
				{
					GroupId:        1,
					AssociationKey: "byte_tree:PROJECT_TYPE_TCE+1",
				},
				{
					GroupId:        2,
					AssociationKey: "byte_tree:PROJECT_TYPE_WEB+10",
				},
			},
		}, nil).AnyTimes()

	appcenterCliMock := apprpc_mock.NewMockClient(ctrl)
	rpc.AppCenterCli = appcenterCliMock
	appcenterCliMock.EXPECT().GetComponentBytetreeNodeId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&app.GetComponentBytetreeNodeIdResp{
			Data: []*app.GetComponentBytetreeNodeIdRespItem{
				&app.GetComponentBytetreeNodeIdRespItem{
					ProjectUniqueId: "p.s.m2",
					ProjectType:     appcenter.ProjectType_PROJECT_TYPE_WEB,
					NodeId:          10,
				},
				&app.GetComponentBytetreeNodeIdRespItem{
					ProjectUniqueId: "p.s.m3",
					ProjectType:     appcenter.ProjectType_PROJECT_TYPE_WEB,
					NodeId:          10,
				},
			},
			Code:    0,
			Message: "",
		}, nil).AnyTimes()

	t.Run("success", func(t *testing.T) {
		resp, err := g.GetByteTreeVars(ctx, []*dev.BcProjectInfo{
			{
				ProjectType:     1,
				ProjectName:     "p.s.m1",
				ProjectUniqueId: "p.s.m1",
			},
			{
				ProjectType:     4,
				ProjectName:     "p.s.m2",
				ProjectUniqueId: "p.s.m2",
			},
		}, GetVarsSceneTypePipelineRun).Get()
		assert.Nil(t, err)
		t.Log(resp)
		assert.Equal(t, true, len(resp) > 0)
	})

	t.Run("pipeline run vars without projects", func(t *testing.T) {
		resp, err := g.GetByteTreeVars(ctx, []*dev.BcProjectInfo{}, GetVarsSceneTypePipelineRun).Get()
		assert.Nil(t, err)
		t.Log(resp)
		assert.Equal(t, true, len(resp) == 0)
	})
}

func Test_useByteTreeVarsInSpace(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	spaceCliMock := spacerpcapi_mock.NewMockClient(ctrl)
	rpc.SpaceCli = spaceCliMock
	spaceCliMock.EXPECT().GetSpaceInfoByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&spacerpcpb.SpaceDetail{
			Id:    1,
			Extra: "",
		}, nil)

	got, err := UseByteTreeVarsInSpace(ctx, 1)
	assert.NoError(t, err)
	assert.Equal(t, false, got)
}
