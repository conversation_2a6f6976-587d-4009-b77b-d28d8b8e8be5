package codechanges

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/derr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/components/changecomponent/changetypes"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis/locks"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

func (c CodeChangeComponent) EditAttribute(ctx context.Context, change *model.DevBasicChange, req changetypes.IEditAttributeRequest) gresult.R[*model.DevBasicChange] {
	switch req.Type() {
	case changetypes.SetTargetBranch:
		setReq := req.(changetypes.SetTargetBranchRequest)
		handleFunc := choose.If(len(setReq.TargetBranch) == 0, c.removeTargetBranch, c.setTargetBranch)
		return handleFunc(ctx, change, setReq)

	case changetypes.UpdateIsCodebaseDraft:
		setReq := req.(changetypes.UpdateIsCodebaseDraftRequest)
		return c.updateIsCodebaseDraft(ctx, change, setReq.IsCodebaseDraft)

	default:
		return gresult.OK(change)
	}
}

/*
setTargetBranch 处理的是 SetTargetBranchRequest.TargetBranch 非空的场景
- 创建一个 MR
- 更新一个 MR
- 将草稿变成一个 MR (change.is_draft 为 true)
*/
func (c CodeChangeComponent) setTargetBranch(ctx context.Context, change *model.DevBasicChange, req changetypes.SetTargetBranchRequest) gresult.R[*model.DevBasicChange] {
	var err error
	// 1. 将默认分支转化成实际的分支名
	projectId := change.GetManifest().GetCodeElement().GetRepoId()
	req.TargetBranch, err = gitsvr.GetProjectRealBranchName(ctx, projectId, req.TargetBranch).Get()
	if err != nil {
		return gresult.Err[*model.DevBasicChange](err)
	}

	// 2. 获取当前代码变更的详情
	lockSvr := locks.NewDevChangeOpLock(redis.DevClient, change.Id)
	if !lockSvr.Lock(ctx) {
		return gresult.Err[*model.DevBasicChange](bits_err.DEVTASK.ErrGetOperateLock)
	}
	defer func() { lockSvr.UnLock(ctx) }()
	realtimeChange, dbErr := c.changeRepo.FindById(ctx, change.Id).Get()
	if dbErr != nil {
		return gresult.Err[*model.DevBasicChange](dbErr)
	}
	realTimeManifest, err := c.GetManifest(ctx, realtimeChange).Get()
	if err != nil {
		return gresult.Err[*model.DevBasicChange](err)
	}

	// 3. 处理复用已有 MR 的参数(更新 manifest 、关闭当前 MR)
	if req.ReuseMrInfo != nil && req.ReuseMrInfo.Iid > 0 {
		manifest := change.GetManifest()
		manifest.CodeElement.TargetBranch = choose.IfLazyR(len(req.ReuseMrInfo.TargetBranch) == 0, req.TargetBranch, func() string { return req.ReuseMrInfo.TargetBranch })
		manifest.CodeElement.SourceBranch = choose.IfLazyR(len(req.ReuseMrInfo.SourceBranch) == 0, manifest.CodeElement.SourceBranch, func() string { return req.ReuseMrInfo.SourceBranch })
		manifest.CodeElement.Iid = req.ReuseMrInfo.Iid
		manifest.CodeElement.Creator = choose.If(len(req.Username) == 0, change.Author, req.Username)
		change.Author = choose.If(len(req.Username) == 0, change.Author, req.Username)
		change.SetManifest(manifest)
		req.TargetBranch = manifest.GetCodeElement().GetTargetBranch()
		change.BizConfig.MrToReuse = req.ReuseMrInfo
		if change.BizId > 0 && req.TargetBranch != realTimeManifest.GetCodeElement().GetTargetBranch() {
			err = gitsvr.CloseCodeChangeById(ctx, change.BizId)
			if err != nil {
				return gresult.Err[*model.DevBasicChange](err)
			}
		}
	}

	// 4. 校验是否需要实际操作
	if req.TargetBranch == realTimeManifest.CodeElement.GetSourceBranch() {
		err = bits_err.DEVTASK.ErrMrSameSourceAndTargetBranch.AddErrMsg(fmt.Sprintf("repo: %v", realTimeManifest.GetCodeElement().GetRepoPath()))
		return gresult.Err[*model.DevBasicChange](err)
	}
	if req.TargetBranch == realTimeManifest.GetCodeElement().GetTargetBranch() &&
		req.IsDraft == realtimeChange.IsDraft && realtimeChange.BizId > 0 &&
		realTimeManifest.GetCodeElement().GetStatus() == dev.CodeChangeStatus_opened {
		return gresult.OK(change)
	}

	// 5. 执行目标分支设置的动作
	change.IsDraft = req.IsDraft
	change.BizConfig.Manifest.CodeElement.TargetBranch = req.TargetBranch
	params := &createCodeChangeParams{
		DevTaskName:   req.BasicInfo.Title,
		SpaceId:       req.BasicInfo.SpaceId,
		DevBasicType:  req.BasicConfig.Type,
		TitleTemplate: req.TitleTemplate,
		Features:      req.Features,
	}

	if change.BizId == 0 || realTimeManifest.GetCodeElement().GetStatus() != dev.CodeChangeStatus_opened || change.BizConfig.MrToReuse != nil {
		// 5.1 创建 | 无 MR / MR 已关闭 / 需要复用
		err = c.createCodeChange(ctx, change, params).Err()
	} else {
		// 5.2 更新一个 MR
		err = c.updateTargetBranch(ctx, change, realtimeChange, req).Err()
	}
	er := derr.NewErrRecord(dev.DevTaskErrorBizType_change_mr, change.Id).WithDevBasicId(change.DevBasicId)
	er.Delete(ctx)
	if err != nil {
		log.V2.Warn().With(ctx).Error(err).Str("setTargetBranch error").Emit()
		// 记录错误信息
		er.Error(err).Write(ctx)
		// bizId 置空
		change.BizId = 0
	}

	// save change
	change.IsDraft = req.IsDraft
	change.BizConfig.Manifest.CodeElement.TargetBranch = req.TargetBranch
	err = c.changeRepo.UpdateById(ctx, change.Id, change)
	if err != nil {
		return gresult.Err[*model.DevBasicChange](err)
	}
	return gresult.OK(change)
}

/*
removeTargetBranch 处理的是 SetTargetBranchRequest.TargetBranch 为空的场景
- 关闭一个 MR
- 将变更变成一个草稿 (dev_basic_config.is_enable_draft 为 true)
*/
func (c CodeChangeComponent) removeTargetBranch(ctx context.Context, change *model.DevBasicChange, req changetypes.SetTargetBranchRequest) gresult.R[*model.DevBasicChange] {
	if change.IsNotCreated() {
		// 删掉原有错误信息
		devBasicId := change.DevBasicId
		derr.NewErrRecord(dev.DevTaskErrorBizType_change_mr, change.Id).WithDevBasicId(devBasicId).Delete(ctx)
		return gresult.OK(change)
	}

	var err error
	if req.BasicConfig.BizConfig.IsEnableDraft {
		params := &createCodeChangeParams{
			DevTaskName:   req.BasicInfo.Title,
			SpaceId:       req.BasicInfo.SpaceId,
			DevBasicType:  req.BasicConfig.Type,
			TitleTemplate: req.TitleTemplate,
			Features:      req.Features,
		}
		// 将变更变成一个草稿 (dev_basic_config.is_enable_draft 为 true)
		err = c.transferChangeToDraft(ctx, change, params)
	} else {
		// 关闭一个 MR
		var closedChange *model.DevBasicChange
		closedChange, err = c.closeCodeChange(ctx, change).Get()
		if err != nil {
			return gresult.Err[*model.DevBasicChange](err)
		}
		closedChange.IsDraft = false
		*change = *closedChange
	}
	er := derr.NewErrRecord(dev.DevTaskErrorBizType_change_mr, change.Id).WithDevBasicId(change.DevBasicId)
	er.Delete(ctx)
	if err != nil {
		er.Error(err).Write(ctx)
		log.V2.Warn().With(ctx).Error(err).Str("removeTargetBranch error").Emit()
	}

	// save change
	err = c.changeRepo.Save(ctx, change)
	if err != nil {
		return gresult.Err[*model.DevBasicChange](err)
	}
	return gresult.OK(change)
}

func (c CodeChangeComponent) updateIsCodebaseDraft(ctx context.Context, change *model.DevBasicChange, isCodebaseDraft bool) gresult.R[*model.DevBasicChange] {
	// only update when change is opened
	if change.IsNotOpened() {
		return gresult.OK(change)
	}
	// update code change when code change is created
	if !change.IsNotCreated() {
		updateCodeChangeReq := &git_server.UpdateCodeChangeRequest{
			Id: change.BizId,
			CodeChange: &git_server.CodeChangeUpdateOptions{
				Draft: gptr.Of(isCodebaseDraft),
			},
		}
		if err := gitsvr.UpdateCodeChange(ctx, updateCodeChangeReq); err != nil {
			return gresult.Err[*model.DevBasicChange](err)
		}
	}
	// update biz manifest when code change is not created
	manifest := change.GetManifest()
	if manifest.CodeElement == nil {
		log.V2.Info().With(ctx).Str("no code element when edit").Emit()
		return gresult.OK(change)
	}
	manifest.GetCodeElement().IsCodebaseDraft = gptr.Of(isCodebaseDraft)
	change.SetManifest(manifest)
	change.BizConfig.IsSetCodebaseDraft = gptr.OfNotZero(isCodebaseDraft)
	if err := c.changeRepo.UpdateById(ctx, change.Id, change); err != nil {
		return gresult.Err[*model.DevBasicChange](err)
	}
	return gresult.OK(change)
}

// 更新目标分支，如果更新失败，尝试关闭原有 MR
func (c CodeChangeComponent) updateTargetBranch(ctx context.Context, change, realtimeChange *model.DevBasicChange, req changetypes.SetTargetBranchRequest) gresult.R[*model.DevBasicChange] {
	// 1. 目标分支
	targetBranch := req.TargetBranch

	// 2. codebase draft
	codebaseDraft := req.IsDraft
	if !req.IsDraft {
		// 退出预览且用户显示设置为 CodebaseDraft 时，需要保留 codebase draft 状态
		codebaseDraft = realtimeChange.GetManifest().GetCodeElement().GetIsCodebaseDraft() && realtimeChange.GetIsSetCodebaseDraft()
	}
	updateChangeReq := &git_server.UpdateCodeChangeRequest{
		Id: change.BizId,
		CodeChange: &git_server.CodeChangeUpdateOptions{
			Draft:        gptr.Of(codebaseDraft),
			TargetBranch: gptr.OfNotZero(targetBranch),
		},
	}
	err := gitsvr.UpdateCodeChange(ctx, updateChangeReq)
	if err != nil {
		c.closeCodeChange(ctx, change)
		return gresult.Err[*model.DevBasicChange](err)
	}

	// 3. 如果解除了预览，需要更新 check run 状态
	if !req.IsDraft {
		var spaceId int64 = 0
		c.infoRepo.FindById(ctx, change.DevBasicId).IfOK(func(info *model.DevBasicInfo) {
			spaceId = info.SpaceId
		})
		_, draftLockEnabled, _ := tcc.CodebaseLockEnabled(ctx, spaceId)
		if draftLockEnabled {
			checkRunStatus := consts.CodebaseCheckStatusCompleted
			conclusion := "succeeded"

			_ = gitsvr.UpdateDevTaskLock(ctx,
				realtimeChange.GetManifest().GetCodeElement().RepoId,
				realtimeChange.GetManifest().GetCodeElement().Iid,
				realtimeChange.GetManifest().GetCodeElement().CodebaseRepoId,
				change.DevBasicId,
				spaceId,
				dev.DevTaskStatus_opened.String(),
				checkRunStatus,
				conclusion)
		}
	}
	return gresult.OK(change)
}

func (c CodeChangeComponent) transferChangeToDraft(ctx context.Context, change *model.DevBasicChange, CreateParams *createCodeChangeParams) error {
	manifest, err := c.GetManifest(ctx, change).Get()
	if err != nil {
		return err
	}

	var changeOrDraft *model.DevBasicChange
	projects, err := c.projectRepo.FindByDevBasicId(ctx, change.DevBasicId).Get()
	if err != nil {
		return err
	}
	changeOrDraft, err = c.draftComponent.Decide(ctx, change, projects...).Get()
	if err != nil {
		return err
	}
	change.BizConfig = changeOrDraft.BizConfig
	change.IsDraft = changeOrDraft.IsDraft
	if manifest.CodeElement.Status != dev.CodeChangeStatus_opened || change.BizId == 0 {
		err = c.createCodeChange(ctx, change, CreateParams).Err()
	} else {
		err = c.toDraftMr(ctx, change, change.GetManifest().GetCodeElement().GetTargetBranch()).Err()
	}
	if err != nil {
		return err
	}
	return nil
}

func (c CodeChangeComponent) toDraftMr(ctx context.Context, change *model.DevBasicChange, targetBranch string) gresult.R[*model.DevBasicChange] {
	m := change.GetManifest()
	m.CodeElement.TargetBranch = targetBranch
	change.SetManifest(m)
	updateChangeReq := &git_server.UpdateCodeChangeRequest{
		Id: change.BizId,
		CodeChange: &git_server.CodeChangeUpdateOptions{
			Draft:        gptr.Of(true),
			TargetBranch: gptr.OfNotZero(targetBranch),
		},
	}
	err := gitsvr.UpdateCodeChange(ctx, updateChangeReq)
	if err != nil {
		c.closeCodeChange(ctx, change).IfOK(func(v *model.DevBasicChange) {
			change = v
			change.BizConfig.Manifest.CodeElement.TargetBranch = targetBranch
		})
		return gresult.Err[*model.DevBasicChange](err)
	}
	return gresult.OK(change)
}
