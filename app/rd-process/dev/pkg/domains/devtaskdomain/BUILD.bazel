load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "devtaskdomain",
    srcs = [
        "change.go",
        "create.go",
        "domain.go",
        "finish.go",
        "get_partial_dev_task_basic_info.go",
        "init.go",
        "integration.go",
        "lockability.go",
        "mapper.go",
        "project.go",
        "query.go",
        "recreate.go",
        "revert.go",
        "split.go",
        "update.go",
        "update_partial_dev_task_basic_info.go",
        "workflow.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/domains/devtaskdomain",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/kitex_gen/base",
        "//app/rd-process/dev/kitex_gen/bits/track",
        "//app/rd-process/dev/kitex_gen/bits/workflow/enums",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/config_service",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/dev",
        "//app/rd-process/dev/kitex_gen/bytedance/bits/feature",
        "//app/rd-process/dev/pkg/common/consts",
        "//app/rd-process/dev/pkg/common/derr",
        "//app/rd-process/dev/pkg/common/uniquekeys",
        "//app/rd-process/dev/pkg/components/changecomponent",
        "//app/rd-process/dev/pkg/components/devworkflow",
        "//app/rd-process/dev/pkg/components/projectcomponent",
        "//app/rd-process/dev/pkg/dal/mysql/data",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "//app/rd-process/dev/pkg/dal/mysql/repository",
        "//app/rd-process/dev/pkg/handler/devtask",
        "//app/rd-process/dev/pkg/mapper",
        "//app/rd-process/dev/pkg/payloads",
        "//app/rd-process/dev/pkg/service/developmenttask",
        "//app/rd-process/dev/pkg/service/developmenttask/creation",
        "//app/rd-process/dev/pkg/service/developmenttask/devenv",
        "//app/rd-process/dev/pkg/service/developmenttask/devoperation",
        "//app/rd-process/dev/pkg/service/developmenttask/devpipeline",
        "//app/rd-process/dev/pkg/service/developmenttask/query",
        "//app/rd-process/dev/pkg/service/developmenttask/relationship",
        "//app/rd-process/dev/pkg/service/devtask",
        "//app/rd-process/dev/pkg/service/devtask/devtaskrelated",
        "//app/rd-process/dev/pkg/service/messages",
        "//app/rd-process/dev/service/rpc",
        "//idls/byted/devinfra/appcenter:appcenter_go_proto",
        "//libs/bits_err",
        "//libs/common_lib/utils",
        "//libs/compatibletenancy/emails",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_plugin_dbresolver//:dbresolver",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gconv",
        "@org_byted_code_lang_gg//gmap",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_golang_x_sync//errgroup",
    ],
)
