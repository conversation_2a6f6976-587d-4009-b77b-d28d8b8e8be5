package devtaskdomain

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/track"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/workflow/enums"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/handler/devtask"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gresult"
)

func (domain *Domain) FinishDevTask(ctx context.Context, req *dev.FinishDevTaskRequest) gresult.R[*dev.FinishDevTaskResponse] {
	if err := verifyFinishDevTaskRequest(req); err != nil {
		return gresult.Err[*dev.FinishDevTaskResponse](err)
	}

	info, err := domain.infoRepository.FindById(ctx, req.GetDevBasicId()).Get()
	if err != nil {
		return gresult.Err[*dev.FinishDevTaskResponse](err)
	}
	switch info.WorkflowType {
	case enums.UniqueType_DEV_TASK_ADVANCED.String():
		err = domain.operationService.FinishDevTask(ctx, req.GetDevBasicId(), req.GetUsername())
		if err != nil {
			logs.V2.Error().With(ctx).KV(track.BusinessType_DevTask, req.DevBasicId).KV("error", bits_err.DEVTASK.ErrFinishDevTask).Error(err).Emit()
			return gresult.Err[*dev.FinishDevTaskResponse](err)
		}

	default:
		return devtask.FinishDevTask(ctx, req)
	}

	return gresult.OK(&dev.FinishDevTaskResponse{})
}

func verifyFinishDevTaskRequest(req *dev.FinishDevTaskRequest) error {
	if req.GetDevBasicId() <= 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("devBasicId is invalid")
	}
	if len(req.GetUsername()) == 0 {
		return bits_err.DEVTASK.ErrBadRequest.AddErrMsg("username is empty")
	}
	return nil
}
