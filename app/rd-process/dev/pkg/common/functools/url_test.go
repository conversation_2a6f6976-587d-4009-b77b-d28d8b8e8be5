package functools

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestFormContributionUrl(t *testing.T) {
	t.<PERSON><PERSON><PERSON>ow()
	type args struct {
		spaceId        int64
		devBasicId     int64
		contributionId int64
		isSmr          bool
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "client",
			args: args{
				spaceId:        1487010562,
				devBasicId:     2015,
				contributionId: 2008,
				isSmr:          true,
			},
			want: "https://bits.bytedance.net/devops/1487010562/code/smr/detail/2015/change/2008",
		},
		{
			name: "server",
			args: args{
				spaceId:        39151417858,
				devBasicId:     4803,
				contributionId: 4496,
				isSmr:          false,
			},
			want: "https://bits.bytedance.net/devops/39151417858/develop/developmentTask/detail/4803/change/4496",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FormContributionUrl(tt.args.spaceId, tt.args.devBasicId, tt.args.contributionId, tt.args.isSmr); got != tt.want {
				t.Errorf("FormContributionUrl() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFormDevTaskUrlAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getBitsBaseUrlRet1Mock string
			mockey.Mock(GetBitsBaseUrl, mockey.OptUnsafe).Return(getBitsBaseUrlRet1Mock).Build()

			// prepare parameters
			var spaceId int64
			var devBasicId int64
			var isSmr bool

			// run target function and assert
			got1 := FormDevTaskUrl(spaceId, devBasicId, isSmr)
			convey.So(got1, convey.ShouldEqual, "/devops/0/develop/developmentTask/detail/0")
		})
	})

}

func TestFormDevTaskSmrOptimusMergeTaskUrlAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getBitsBaseUrlRet1Mock string
			mockey.Mock(GetBitsBaseUrl, mockey.OptUnsafe).Return(getBitsBaseUrlRet1Mock).Build()

			// prepare parameters
			var spaceID int64
			var mrID int64

			// run target function and assert
			got1 := FormDevTaskSmrOptimusMergeTaskUrl(spaceID, mrID)
			convey.So(got1, convey.ShouldEqual, "/devops/0/code/detail/0?devops_space_type=client&tab=timeline")
		})
	})

}

func TestFormCodebaseMrUrlAutoGen(t *testing.T) {
	// Verify the function under default condition.
	t.Run("testFormCodebaseMrUrl_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var repoPath string
			var iid int64

			// run target function and assert
			got1 := FormCodebaseMrUrl(repoPath, iid)
			convey.So(got1, convey.ShouldEqual, "https://bits.bytedance.net/code//merge_requests/0")
		})
	})

}

func TestFormChangeDetailCRURLAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getBitsBaseUrlRet1Mock string
			mockey.Mock(GetBitsBaseUrl, mockey.OptUnsafe).Return(getBitsBaseUrlRet1Mock).Build()

			// prepare parameters
			var spaceID int64
			var changeID int64
			var devBasicID int64

			// run target function and assert
			got1 := FormChangeDetailCRURL(spaceID, changeID, devBasicID)
			convey.So(got1, convey.ShouldEqual, "/devops/0/develop/detail/0/change/0")
		})
	})

}

func TestFormCodebaseBranchUrlAutoGen(t *testing.T) {
	// Verify the function under default condition.
	t.Run("testFormCodebaseBranchUrl_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var repoPath string
			var branch string

			// run target function and assert
			got1 := FormCodebaseBranchUrl(repoPath, branch)
			convey.So(got1, convey.ShouldEqual, "https://bits.bytedance.net/code//tree/")
		})
	})

}

func TestFormCodeConflictResolveUrlAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var repoPath string
			var iid int64

			// run target function and assert
			got1 := FormCodeConflictResolveUrl(repoPath, iid)
			convey.So(got1, convey.ShouldEqual, "https://bits.bytedance.net/code//merge_requests/0/conflicts")
		})
	})

}

func TestFormDevTaskSmrTestUrlAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var getBitsBaseUrlRet1Mock string
			mockey.Mock(GetBitsBaseUrl, mockey.OptUnsafe).Return(getBitsBaseUrlRet1Mock).Build()

			// prepare parameters
			var spaceID int64
			var devBasicId int64

			// run target function and assert
			got1 := FormDevTaskSmrTestUrl(spaceID, devBasicId)
			convey.So(got1, convey.ShouldEqual, "/devops/0/code/smr/detail/0?access=testing&stage=check")
		})
	})

}

func TestFormCodebaseRepoUrlAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var repoPath string

			// run target function and assert
			got1 := FormCodebaseRepoUrl(repoPath)
			convey.So(got1, convey.ShouldEqual, "https://code.byted.org/")
		})
	})

}
