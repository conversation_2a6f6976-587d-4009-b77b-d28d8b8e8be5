package vars

import (
	"regexp"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestBaseRender_WithKvsAutoGen(t *testing.T) {
	// default condition
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue BaseRender
			receiver := &receiverPtrValue
			var kvs map[string]string

			// run target function and assert
			got1 := receiver.WithKvs(kvs)
			convey.So(len((*got1).EnabledVars), convey.ShouldEqual, 0)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

}

func TestIsContainVarAutoGen(t *testing.T) {
	// regexp:MustCompile()_ret-1 != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var findAllStringRet1Mock []string
			mockey.Mock((*regexp.Regexp).FindAllString).Return(findAllStringRet1Mock).Build()

			var mustCompileRet1MockPtrValue regexp.Regexp
			mustCompileRet1Mock := &mustCompileRet1MockPtrValue
			mockey.Mock(regexp.MustCompile).Return(mustCompileRet1Mock).Build()

			// prepare parameters
			var str string

			// run target function and assert
			got1 := IsContainVar(str)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

