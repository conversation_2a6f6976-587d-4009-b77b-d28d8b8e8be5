package gitsvr

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/derr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/lang/gg/gconv"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

/**
return &git_server.CodeChangeCreateConfig{
	Title:        creationContext.Config.Title,
	TargetBranch: creationContext.Config.TargetBranch,
	SourceBranch: creationContext.Config.SourceBranch,
	Username:     creationContext.Username,
	PlatformType: git_server.CodeChangePlatformType_gitlab,
	Description:  creationContext.Config.Description,
	RepoPath:     &creationContext.Config.RepoPath,
}
*/

const (
	CreateNextCodeChangeMergeMethodMergeCommit                     = "merge_commit"
	CreateNextCodeChangeMergeMethodMergeCommitWithSemiLinerHistory = "merge_commit_with_semi_linear_history"
	CreateNextCodeChangeMergeMethodRebaseMerge                     = "rebase_merge"
)

type CreateNextCodeChangeQuery struct {
	Title        string
	TargetBranch string
	SourceBranch string
	RepoID       string
	Username     *string
	Description  *string
	WIP          *bool
}

// CreateNextCodeChangeWithRetryWhenBranchNotExisted 创建 CodeChange 失败时，会重试指定次数
func CreateNextCodeChangeWithRetryWhenBranchNotExisted(
	ctx context.Context,
	query *CreateNextCodeChangeQuery,
	retryCount int64,
) gresult.R[int64] {

	resp, err := rpc.NextCodeApiClient.CreateMergeRequestV2(ctx, &git_server.CreateMergeRequestV2Request{
		SourceRepoId:                 query.RepoID,
		SourceBranch:                 query.SourceBranch,
		TargetRepoId:                 query.RepoID,
		TargetBranch:                 query.TargetBranch,
		Title:                        query.Title,
		Description:                  query.Description,
		MergeMethod:                  CreateNextCodeChangeMergeMethodMergeCommit,
		Draft:                        query.WIP,
		Username:                     query.Username,
		RemoveSourceBranchAfterMerge: false,
		Record:                       gptr.Of(true),
	})
	if err != nil {
		logs.V2.Error().With(ctx).Error(err).Str("create code change failed").Emit()
		if strings.Contains(err.Error(), "branch doesn't exist") && retryCount > 0 {
			time.Sleep(5 * time.Second)
			logs.CtxInfo(ctx, "retry start")
			return CreateNextCodeChangeWithRetryWhenBranchNotExisted(ctx, query, retryCount-1)
		} else {
			return gresult.Err[int64](err)
		}
	}
	return gresult.OK(gptr.Indirect(resp.CodeChangeId))
}

// CreateCodeChangeWithRetryWhenBranchNotExisted 创建 CodeChange 失败时，会重试指定次数
func CreateCodeChangeWithRetryWhenBranchNotExisted(ctx context.Context, CodeChangeCreateConfig *git_server.CodeChangeCreateConfig, extraCreateConfig *git_server.ExtraCreateConfig, mode git_server.CodeChangeCreationMode, retryCount int) gresult.R[*git_server.CreateCodeChangeResponse] {
	apiType := git_server.CreateCodeChangeApiType_gitlab
	if extraCreateConfig != nil && extraCreateConfig.GitlabMrExtraCreateConfig != nil {
		if tcc.CreateMrWithCodebaseApiCfgEnabled(ctx, 0, extraCreateConfig.GitlabMrExtraCreateConfig.ProjectId) {
			apiType = git_server.CreateCodeChangeApiType_codebase
		}
	}
	codeChange, err := rpc.GitServerClient.CreateCodeChange(ctx, &git_server.CreateCodeChangeRequest{
		CodeChangeCreateConfig: CodeChangeCreateConfig,
		ExtraCreateConfig:      extraCreateConfig,
		CreationMode:           &mode,
		ApiType:                &apiType,
	})
	if err != nil {
		logs.V2.Error().With(ctx).Error(err).Str("create code change failed").Emit()
		if strings.Contains(err.Error(), "branch doesn't exist") && retryCount > 0 {
			time.Sleep(5 * time.Second)
			logs.CtxInfo(ctx, "retry start")
			return CreateCodeChangeWithRetryWhenBranchNotExisted(ctx, CodeChangeCreateConfig, extraCreateConfig, mode, retryCount-1)
		} else {
			return gresult.Err[*git_server.CreateCodeChangeResponse](err)
		}
	}
	return gresult.OK(codeChange)
}

func CreateCodeChange(ctx context.Context, req *git_server.CreateCodeChangeRequest) gresult.R[*git_server.CreateCodeChangeResponse] {
	res := createCodeChangeInternal(ctx, req)
	if res.IsOK() {
		return res
	}

	// 仅未知错误需要处理 (排除了，MR 已存在，分支不存在，无权限等明确定义的错误)
	switch bits_err.ToBizError(res.Err()).Code() {
	case bits_err.DEVTASK.ErrMrCreate.Code(),
		bits_err.DEVTASK.ErrMrAlreadyExisted.Code():
		// 仅处理需要新创建 MR 的情况
		if req.GetCreationMode() == git_server.CodeChangeCreationMode_trackExisted {
			return res
		}

		// codebase 接口可能有延迟，等一等
		time.Sleep(time.Second * 10)

		// 搜索下是否已经创建出来了
		projectId := req.GetExtraCreateConfig().GetGitlabMrExtraCreateConfig().GetProjectId()
		source := req.GetCodeChangeCreateConfig().GetSourceBranch()
		target := req.GetCodeChangeCreateConfig().GetTargetBranch()
		mrs, _ := GetMrsByBranch(ctx, projectId, source, target, gptr.Of(git_server.MergeRequestState_opened)).Get()
		if len(mrs) == 0 || mrs[0].GetTitle() != req.GetCodeChangeCreateConfig().GetTitle() {
			return res
		}

		// 创建出来了就复用
		req.CreationMode = gptr.OfNotZero(git_server.CodeChangeCreationMode_trackExisted)
		req.ExtraCreateConfig = &git_server.ExtraCreateConfig{
			GitlabMrExtraCreateConfig: &git_server.GitlabMrExtraCreateConfig{
				ProjectId: projectId,
				Iid:       gptr.OfNotZero(int64(mrs[0].GetIid())),
			},
		}
		return createCodeChangeInternal(ctx, req)
	}
	return res
}

func createCodeChangeInternal(ctx context.Context, req *git_server.CreateCodeChangeRequest) gresult.R[*git_server.CreateCodeChangeResponse] {
	extra := map[string]string{
		"repo":          req.GetCodeChangeCreateConfig().GetRepoPath(),
		"target_branch": req.GetCodeChangeCreateConfig().GetTargetBranch(),
		"source_branch": req.GetCodeChangeCreateConfig().GetSourceBranch(),
	}
	codeChange, err := rpc.GitServerClient.CreateCodeChange(ctx, req)
	if err != nil {
		err = convertMrCreateError(err).AddExtra(extra)
		logs.CtxError(ctx, "create code change failed error=%s", err.Error())
		return gresult.Err[*git_server.CreateCodeChangeResponse](err)
	}
	if p := derr.NewBaseRespParser(codeChange); p.HasError() {
		err = convertMrCreateError(p.Error()).AddExtra(extra)
		return gresult.Err[*git_server.CreateCodeChangeResponse](err)
	}
	if codeChange == nil || codeChange.GetDetail() == nil {
		err = bits_err.DEVTASK.ErrMrCreate.AddExtra(extra)
		return gresult.Err[*git_server.CreateCodeChangeResponse](err)
	}
	return gresult.OK(codeChange)
}

// UpdateCodeChange @deprecated using UpdateMergeRequestV2 first
func UpdateCodeChange(ctx context.Context, request *git_server.UpdateCodeChangeRequest) error {
	detail, err := GetCodeChangeDetail(ctx, request.GetId()).Get()
	if err != nil {
		return err
	}

	req := &git_server.UpdateMergeRequestV2Request{
		RepoId:                       gconv.To[string](detail.GetGitlabMr().GetCodebaseRepoID()),
		Number:                       gptr.OfNotZero(detail.GetGitlabMr().GetIid()),
		Title:                        request.GetCodeChange().Title,
		Description:                  request.GetCodeChange().Description,
		RemoveSourceBranchAfterMerge: request.GetCodeChange().RemoveSourceBranch,
		TargetBranch:                 request.GetCodeChange().TargetBranch,
		SquashCommits:                request.GetCodeChange().Squash,
		Draft:                        request.GetCodeChange().Draft,
		Record:                       gptr.Of(true),
		Username:                     gptr.OfNotZero(detail.GetGitlabMr().GetAuthor()),
	}

	err = UpdateMergeRequestV2(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

func GetCodeChangeDetail(ctx context.Context, id int64) gresult.R[*git_server.CodeChangeDetail] {
	req := &git_server.GetCodeChangeDetailRequest{
		Id: id,
	}

	logs.CtxInfo(ctx, "will call get code change detail (%v)", utils.ToJson(req))
	resp, err := rpc.GitServerClient.GetCodeChangeDetail(ctx, req)
	logs.CtxInfo(ctx, "did call get code change detail (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)
	if err != nil {
		logs.CtxInfo(ctx, "get code change detail error (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)
		return gresult.Err[*git_server.CodeChangeDetail](err)
	}
	if resp == nil || resp.GetDetail() == nil {
		logs.CtxInfo(ctx, "get code change detail error: resp or detail is nil (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)
		err = fmt.Errorf("GetCodeChangeDetail get empty resp")
		return gresult.Err[*git_server.CodeChangeDetail](err)
	}
	return gresult.OK(resp.GetDetail())
}

func GetCodeChangeDetailGitlabMr(ctx context.Context, id int64) gresult.R[*git_server.CodeChangeGitlabMergeRequest] {
	detail, err := GetCodeChangeDetail(ctx, id).Get()
	if err != nil {
		return gresult.Err[*git_server.CodeChangeGitlabMergeRequest](err)
	}
	if detail.GetGitlabMr() == nil {
		err = fmt.Errorf("GetCodeChangeDetail.gitlab_mr get empty resp")
		return gresult.Err[*git_server.CodeChangeGitlabMergeRequest](err)
	}
	return gresult.OK(detail.GetGitlabMr())
}

func BatchGetMrsByCodebaseChangeIds(ctx context.Context, codebaseChangeIDs []int64) gresult.R[[]*git_server.CodeChangeGitlabMergeRequest] {
	answer := make([]*git_server.CodeChangeGitlabMergeRequest, 0, len(codebaseChangeIDs))
	if len(codebaseChangeIDs) == 0 {
		return gresult.OK(answer)
	}
	req := &git_server.BatchGetCodeChangeGitlabMrByCodebaseChangeIdsRequest{
		CodebaseChangeIDs: codebaseChangeIDs,
	}
	resp, err := rpc.GitServerClient.BatchGetCodeChangeGitlabMrByCodebaseChangeIDs(ctx, req)
	if err != nil {
		return gresult.Err[[]*git_server.CodeChangeGitlabMergeRequest](err)
	}
	if len(resp.GetMrs()) == 0 {
		return gresult.OK(answer)
	}
	return gresult.OK(resp.GetMrs())
}

func BatchGetMrsByCodeChangeIds(ctx context.Context, ids []int64) gresult.R[[]*git_server.CodeChangeGitlabMergeRequest] {
	if len(ids) == 0 {
		return gresult.OK([]*git_server.CodeChangeGitlabMergeRequest{})
	}
	req := &git_server.BatchGetCodeChangeGitlabMrByCodeChangeIdsRequest{
		Cids: ids,
	}
	resp, err := rpc.GitServerClient.BatchGetCodeChangeGitlabMrByCodeChangeIDs(ctx, req)
	if err != nil {
		return gresult.Err[[]*git_server.CodeChangeGitlabMergeRequest](err)
	}
	if resp == nil || resp.GetMrs() == nil {
		err = fmt.Errorf("BatchGetCodeChangeGitlabMrByCodeChangeIDs get empty resp")
		return gresult.Err[[]*git_server.CodeChangeGitlabMergeRequest](err)
	}
	return gresult.OK(resp.GetMrs())
}

func CloseCodeChangeById(ctx context.Context, id int64) error {
	req := &git_server.CloseCodeChangeRequest{
		CodeChangeId: id,
	}

	resp, err := rpc.GitServerClient.CloseCodeChange(ctx, req)
	if err != nil {
		goto doubleCheck
	}

	if ep := derr.NewBaseRespParser(resp); ep.HasError() {
		err = ep.Error()
		goto doubleCheck
	}
	return nil

doubleCheck:
	// 避免因为 MR 已合入导致关闭一直不生效的情况
	time.Sleep(time.Millisecond * 500)
	detail, qErr := GetCodeChangeDetail(ctx, id).Get()
	if qErr != nil {
		return qErr
	}
	if detail.GetGitlabMr().GetState() == git_server.MergeRequestState_merged.String() {
		return nil
	}
	repoInfo, err := GetNextCodeRepositoryById(ctx, detail.GetGitlabMr().GetCodebaseRepoID()).Get()
	// 如果仓库不存在，不阻塞
	if be := bits_err.ToBizError(err); be.Code() == bits_err.GITSERVER.ErrRepoNotFound.Code() {
		return nil
	}
	if err != nil {
		return err
	}
	// archived 的仓库不阻塞（因为无法操作）
	if repoInfo != nil && repoInfo.GetArchived() {
		return nil
	}
	return err
}

func GetCodeChangeIdByProjectIdAndIid(ctx context.Context, projectId, iid int64) gresult.R[int64] {
	req := &git_server.GetCodeChangeGitlabMrRequest{
		ProjectID: projectId,
		Iid:       iid,
	}
	resp, err := rpc.GitServerClient.GetCodeChangeGitlabMr(ctx, req)
	if err != nil {
		return gresult.Err[int64](err)
	}
	if pe := derr.NewBaseRespParser(resp); pe.HasError() {
		return gresult.Err[int64](pe.Error())
	}
	if resp == nil || resp.GetMr() == nil {
		return gresult.Err[int64](fmt.Errorf("empty resp"))
	}
	return gresult.OK(resp.GetMr().GetCodeChangeID())
}

func CloseCodeChange(ctx context.Context, ccid int64) error {
	_, err := rpc.GitServerClient.CloseCodeChange(ctx, &git_server.CloseCodeChangeRequest{
		CodeChangeId: ccid,
	})
	if err != nil {
		return err
	}
	return nil
}

func GetCodeChangeIdByCodebaseRepoIdAndChangeId(ctx context.Context, codebaseRepoId int64, changeId int64) gresult.R[int64] {
	req := &git_server.GetCodeChangeGitlabMrRequest{
		CodebaseRepoId:   codebaseRepoId,
		CodebaseChangeId: changeId,
	}
	resp, err := rpc.GitServerClient.GetCodeChangeGitlabMr(ctx, req)
	if err != nil {
		return gresult.Err[int64](err)
	}
	if pe := derr.NewBaseRespParser(resp); pe.HasError() {
		return gresult.Err[int64](pe.Error())
	}
	if resp == nil || resp.GetMr() == nil {
		return gresult.Err[int64](fmt.Errorf("empty resp"))
	}
	return gresult.OK(resp.GetMr().GetCodeChangeID())
}
