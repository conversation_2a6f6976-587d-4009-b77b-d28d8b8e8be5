package gitsvr

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
)

func GetRepoDiffByCommit(ctx context.Context, repoId int64, sourceCommit string, targetCommit string) ([]*git_server.RepoDiffByCommitFile, error) {

	req := &git_server.GetRepoDiffByCommitRequest{
		RepoId:       repoId,
		SourceCommit: sourceCommit,
		TargetCommit: targetCommit,
	}
	diffResp, err := rpc.GitServerClient.GetRepoDiffByCommit(ctx, req)
	if err != nil {
		return nil, err
	}
	if diffResp == nil {
		err = fmt.Errorf("get repo diff by commit failed error: response is nil")
		return nil, err
	}
	return diffResp.Files, nil
}
