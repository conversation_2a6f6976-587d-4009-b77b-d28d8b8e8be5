load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "varsvr",
    srcs = ["index.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/varsvr",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/service/rpc",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//libs/common_lib/utils",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_lang_gg//gvalue",
        "@org_golang_x_sync//errgroup",
    ],
)
