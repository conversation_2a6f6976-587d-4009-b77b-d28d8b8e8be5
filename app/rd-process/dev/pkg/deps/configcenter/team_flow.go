package configcenter

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/derr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/team_flow_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/gresult"
)

type ITeamFlowSvr interface {
	GetTeamFlowConfig(ctx context.Context, id int64) gresult.R[*team_flow_configpb.TeamFlowConfig]                              // 查询流程配置
	IsSpaceEnableTeamFlow(ctx context.Context, spaceId int64) gresult.R[bool]                                                   // 空间是否启用流程配置
	IsDevTemplateUsedInTeamFlow(ctx context.Context, devTemplateId int64) gresult.R[*team_flow_configpb.WorkflowInTeamFlowResp] // 开发任务模板是否被流程使用
}

type ImplTeamFlowSvr struct{}

func NewTeamFlowSvr() ITeamFlowSvr {
	return &ImplTeamFlowSvr{}
}

func (c *ImplTeamFlowSvr) GetTeamFlowConfig(ctx context.Context, id int64) gresult.R[*team_flow_configpb.TeamFlowConfig] {
	req := &team_flow_configpb.GetTeamFlowConfigReq{
		TeamFlowId:     uint64(id),
		IncludeDeleted: true,
	}

	log.V2.Info().With(ctx).Str(fmt.Sprintf("will call get team flow config (%v)", utils.ToJson(req))).Emit()
	resp, err := rpc.ReleaseTicketClient.GetTeamFlowConfig(ctx, req)
	log.V2.Info().With(ctx).Str(fmt.Sprintf("did call get team flow config (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
	if err != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get team flow config error (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
		return gresult.Err[*team_flow_configpb.TeamFlowConfig](bits_err.DEVTASK.ErrInvokeTeamFlowConfig.AddErrMsg(derr.GetErrorMsg(err)))
	}
	if resp == nil || resp.TeamFlowConfig == nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get team flow config error: config is nil (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
		return gresult.Err[*team_flow_configpb.TeamFlowConfig](bits_err.DEVTASK.ErrInvokeTeamFlowConfig.AddErrMsg("GetTeamFlowConfig return empty response"))
	}
	return gresult.OK(resp.GetTeamFlowConfig())
}

func (c *ImplTeamFlowSvr) IsSpaceEnableTeamFlow(ctx context.Context, spaceId int64) gresult.R[bool] {
	req := &team_flow_configpb.GetIsGrayForTeamFlowReq{
		WorkspaceId: uint64(spaceId),
	}

	log.V2.Info().With(ctx).Str(fmt.Sprintf("will call get is gray for team flow (%v)", utils.ToJson(req))).Emit()
	resp, err := rpc.ReleaseTicketClient.GetIsGrayForTeamFlow(ctx, req)
	log.V2.Info().With(ctx).Str(fmt.Sprintf("did call get is gray for team flow (%v) (%v) (%v)", utils.ToJson(req), utils.ToJson(resp), err)).Emit()
	if err != nil {
		log.V2.Warn().With(ctx).Str("GetIsGrayForTeamFlow return err").Error(err).Emit()
		return gresult.Err[bool](bits_err.DEVTASK.ErrInvokeTeamFlowConfig.AddErrMsg(derr.GetErrorMsg(err)))
	}
	if resp == nil {
		log.V2.Warn().With(ctx).Str("GetIsGrayForTeamFlow return empty").Error(err).Emit()
		return gresult.Err[bool](bits_err.DEVTASK.ErrInvokeTeamFlowConfig.AddErrMsg("GetIsGrayForTeamFlow return empty"))
	}
	return gresult.OK(resp.GetIsGray())
}

func (c *ImplTeamFlowSvr) IsDevTemplateUsedInTeamFlow(ctx context.Context, devTemplateId int64) gresult.R[*team_flow_configpb.WorkflowInTeamFlowResp] {
	req := &team_flow_configpb.WorkflowInTeamFlowReq{
		WorkflowId:   uint64(devTemplateId),
		WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_DEVELOPMENT_TASK,
	}
	resp, err := rpc.ReleaseTicketClient.WorkflowInTeamFlow(ctx, req)
	if err != nil {
		return gresult.Err[*team_flow_configpb.WorkflowInTeamFlowResp](bits_err.DEVTASK.ErrInvokeTeamFlowConfig.AddErrMsg(derr.GetErrorMsg(err)))
	}
	if resp == nil {
		return gresult.Err[*team_flow_configpb.WorkflowInTeamFlowResp](bits_err.DEVTASK.ErrInvokeTeamFlowConfig.AddErrMsg("WorkflowInTeamFlow return empty response"))
	}
	return gresult.OK(resp)
}

type TeamFlowResult struct {
	TeamFlowConfig *team_flow_configpb.TeamFlowConfig
	Enable         bool
}

func GetTeamFlowConfigBySpaceIdAndTeamFlowId(ctx context.Context, spaceId int64, teamFlowId int64) (*TeamFlowResult, error) {
	if teamFlowId == 0 {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("team flow id not valid (%v) (%v)", spaceId, teamFlowId)).Emit()
		return &TeamFlowResult{Enable: false}, nil
	}
	svr := NewTeamFlowSvr()
	enable, enableErr := svr.IsSpaceEnableTeamFlow(ctx, spaceId).Get()
	if enableErr != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("is space enable team flow error (%v) (%v) (%v)", spaceId, teamFlowId, enableErr)).Emit()
		return nil, enableErr
	}
	if !enable {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("space not enable team flow (%v) (%v) (%v)", spaceId, teamFlowId, enable)).Emit()
		return &TeamFlowResult{Enable: false}, nil
	}
	teamFlowConfig, tfErr := svr.GetTeamFlowConfig(ctx, teamFlowId).Get()
	if tfErr != nil {
		log.V2.Info().With(ctx).Str(fmt.Sprintf("get team flow config error (%v) (%v) (%v)", spaceId, teamFlowId, tfErr)).Emit()
		return nil, tfErr
	}

	log.V2.Info().With(ctx).Str(fmt.Sprintf("team flow config enabled (%v) (%v)", spaceId, teamFlowId)).Emit()
	return &TeamFlowResult{Enable: true, TeamFlowConfig: teamFlowConfig}, nil
}

func GetStableBranchByTeamFlowId(ctx context.Context, teamFlowId int64) gresult.R[string] {
	tf, err := NewTeamFlowSvr().GetTeamFlowConfig(ctx, teamFlowId).Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	if tf.BranchingModelConfig == nil {
		return gresult.Err[string](fmt.Errorf("invalid branch model config, %v", tf.BranchingModelConfig))
	}
	var branchModel *branching_model_configpb.BranchNaming
	switch tf.BranchingModelConfig.GetBranchingModelType() {
	case branching_model_configpb.BranchingModelType_BRANCHING_MODEL_TYPE_TRUNK_DEV:
		branchModel = tf.BranchingModelConfig.GetTrunkBranch()

	default:
		branchModel = tf.BranchingModelConfig.GetArchiveBranch()
	}
	if branchModel == nil {
		return gresult.Err[string](fmt.Errorf("invalid branch config, %v", tf.BranchingModelConfig))
	}

	for i := 0; i < 3; i++ {
		// 非 ref 不找 reference
		if branchModel.GetBranchNamingType() != branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_REF {
			break
		}
		// 无引用即可返回
		if branchModel.GetReferenceBranch() == branching_model_configpb.BranchType_BRANCH_TYPE_UNSPECIFIED {
			break
		}
		switch branchModel.GetReferenceBranch() {
		case branching_model_configpb.BranchType_BRANCH_TYPE_INTEGRATION:
			branchModel = tf.BranchingModelConfig.IntegrationBranch

		case branching_model_configpb.BranchType_BRANCH_TYPE_RELEASE:
			branchModel = tf.BranchingModelConfig.ReleaseBranch

		case branching_model_configpb.BranchType_BRANCH_TYPE_ARCHIVE:
			branchModel = tf.BranchingModelConfig.ArchiveBranch

		case branching_model_configpb.BranchType_BRANCH_TYPE_HOTFIX:
			branchModel = tf.BranchingModelConfig.HotfixBranch

		case branching_model_configpb.BranchType_BRANCH_TYPE_TRUNK:
			branchModel = tf.BranchingModelConfig.TrunkBranch
		}
	}

	return gresult.OK(branchModel.GetName())
}
