package gdp

import (
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func TestRawResponse_UnwrapAutoGen(t *testing.T) {
	// receiver.ErrNo != 0
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue RawResponse[int]
			receiver := &receiverPtrValue
			receiver.ErrNo = 1
			convey.So(func() { _ = receiver.Unwrap() }, convey.ShouldNotPanic)
		})
	})

	// receiver.ErrNo == 0
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// prepare parameters
			var receiverPtrValue RawResponse[int]
			receiver := &receiverPtrValue
			receiver.ErrNo = 0
			convey.So(func() { _ = receiver.Unwrap() }, convey.ShouldNotPanic)
		})
	})

}

