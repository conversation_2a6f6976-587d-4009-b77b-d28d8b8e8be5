package deploys

import (
	"context"
	"errors"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/devops/deploy"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/lang/gg/gresult"
)

func GetPipelineTasks(ctx context.Context, devBasicId, workflowSnapshotId int64) gresult.R[map[string]*deploy.PipelineTask] {
	req := &deploy.GetPipelineTasksRequest{
		DevBasicId:         devBasicId,
		WorkflowSnapshotId: workflowSnapshotId,
		Base:               nil,
	}
	resp, err := rpc.DeployBridgeClient.GetPipelineTasks(ctx, req)
	if err != nil {
		return gresult.Err[map[string]*deploy.PipelineTask](err)
	}
	if resp == nil {
		return gresult.Err[map[string]*deploy.PipelineTask](errors.New("empty response"))
	}
	return gresult.OK(resp.Tasks)
}
