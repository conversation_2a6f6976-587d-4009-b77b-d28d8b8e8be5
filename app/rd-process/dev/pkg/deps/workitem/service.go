package workitem

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/feature"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/dcache"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/third_part"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/libs/thirdparty-sdks/meego"
	json "github.com/bytedance/sonic"

	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/mtctx"

	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/lang/gg/optional"
	"github.com/bytedance/sonic"
	"golang.org/x/sync/errgroup"
)

type IWorkItemService interface {
	Bind(ctx context.Context, items []*feature.DevFeatureRecordInfo) error
	Unbind(ctx context.Context, devFeatureRecordIds []int64) error
	GetBondedWorkItems(ctx context.Context, isBrief bool) gresult.R[[]*feature.DevFeatureRecordInfo]
	GetWorkItemQAs(ctx context.Context, workItemId int64, spaceID string, taskType string) gresult.R[[]string]
}

type service struct {
	IWorkItemService
	devId int64
}

func NewService(devId int64) IWorkItemService {
	return &service{
		devId: devId,
	}
}

func getMeegoCreateTime(ctx context.Context, item *feature.DevFeatureRecordInfo) int64 {
	email := utils.ParseBitsAPIBffUserEmail(ctx)
	email = choose.If(len(email) == 0, emails.WithSuffix(item.Username), email)
	ctx = mtctx.WithTenancy(ctx, email)
	workItem := dev.NewWorkItem()
	_ = sonic.UnmarshalString(item.GetCustomContent(), workItem)
	workItemId, _ := strconv.ParseInt(workItem.GetId(), 10, 64)
	if len(email) == 0 ||
		workItemId == 0 ||
		item.Platform != dev.WorkItemPlatform_meego.String() {
		return 0
	} else {
		req := &meego.QueryWorkItemsRequest{
			WorkItemIds:      []int64{workItemId},
			WorkItemTypeKeys: []string{"story", "issue"},
		}
		resp, _, err := third_part.MeegoClient.QueryWorkItemsByEmail(ctx, email, workItem.GetSpaceKey(), req)
		if err != nil || len(resp) == 0 {
			return 0
		}
		return resp[0].CreatedAt
	}
}

func (s *service) Bind(ctx context.Context, items []*feature.DevFeatureRecordInfo) error {
	eg, innerCtx := errgroup.WithContext(ctx)
	for _, item := range items {
		taskCreateTime := choose.If(item.TaskCreateTime == nil, getMeegoCreateTime(ctx, item), item.GetTaskCreateTime())
		req := &feature.BindDevFeatureRecordQuery{
			TaskID:          item.TaskID,
			Platform:        item.Platform,
			TaskType:        item.TaskType,
			DevID:           s.devId,
			Username:        item.Username,
			GroupName:       item.GroupName,
			TaskTitle:       item.TaskTitle,
			FeatureConfigID: item.FeatureConfigID,
			Config:          item.Config,
			CustomContent:   item.CustomContent,
			TaskCreateTime:  gptr.OfNotZero(taskCreateTime),
		}
		eg.Go(func() error {
			return BindFeature(innerCtx, req)
		})
	}
	return eg.Wait()
}

func (s *service) Unbind(ctx context.Context, devFeatureRecordIds []int64) error {
	eg, innerCtx := errgroup.WithContext(ctx)
	for _, recordId := range devFeatureRecordIds {
		req := &feature.UnbindDevFeatureRecordQuery{
			ID:    recordId,
			DevID: s.devId,
		}
		eg.Go(func() error {
			return UnbindFeature(innerCtx, req)
		})
	}
	return eg.Wait()
}

func (s *service) GetBondedWorkItems(ctx context.Context, isBrief bool) gresult.R[[]*feature.DevFeatureRecordInfo] {
	req := &feature.GetDevFeatureRecordQuery{
		DevID: s.devId,
	}
	resp, err := rpc.FeatureClient.GetDevFeatureRecord(ctx, req)
	if err != nil {
		return gresult.Err[[]*feature.DevFeatureRecordInfo](err)
	}
	if resp == nil {
		err = fmt.Errorf("feature.GetDevFeatureRecordQuery get empty resp")
		return gresult.Err[[]*feature.DevFeatureRecordInfo](err)
	}

	if !isBrief {
		var eg errgroup.Group
		for idx := range resp.GetList() {
			i := idx
			eg.Go(func() error {
				s.fillDevFeatureRecordStatus(ctx, resp.List[i])
				return nil
			})
		}
		_ = eg.Wait()
	}
	return gresult.OK(resp.GetList())
}

func (s *service) fillDevFeatureRecordStatus(ctx context.Context, info *feature.DevFeatureRecordInfo) {
	var workItemStatus string
	workItem := dev.NewWorkItem()
	_ = sonic.UnmarshalString(info.GetCustomContent(), workItem)
	workItemId, _ := strconv.ParseInt(workItem.GetId(), 10, 64)

	cacheSvr := dcache.NewCacheSvr(redis.OptimusClient)
	statusKey := dcache.GetMeegoStatusKey(workItemId, workItem.Type)
	err := cacheSvr.Get(ctx, statusKey, &workItemStatus)
	if err != nil {
		// 直接查
		email := utils.ParseBitsAPIBffUserEmail(ctx)
		email = choose.If(len(email) == 0, emails.WithSuffix(info.Username), email)
		if len(email) == 0 ||
			workItemId == 0 ||
			info.Platform != dev.WorkItemPlatform_meego.String() {
			return
		}
		//workItemStatus, err = s.getMeegoWorkItemStatus(ctx, email, workItemId, workItem.GetSpaceKey()).Get()
		//if err != nil {
		//	return
		//}
	}
	//workItem.Status = workItemStatus
	info.CustomContent = gptr.Of(utils.ToJson(workItem))
	info.TaskURL = &workItem.Url
	// 先给个一小时
	cacheSvr.Set(statusKey, workItemStatus, time.Hour)
	return
}

func (s *service) getMeegoWorkItemStatus(ctx context.Context, email string, workItem int64, spaceId string) gresult.R[string] {
	ctx = mtctx.WithTenancy(ctx, email)
	req := &meego.QueryWorkItemsRequest{
		WorkItemIds:      []int64{workItem},
		WorkItemTypeKeys: []string{"story", "issue"},
	}
	resp, _, err := third_part.MeegoClient.QueryWorkItemsByEmail(ctx, email, spaceId, req)
	if err != nil || len(resp) == 0 {
		return gresult.Err[string](errors.New("query work item error"))
	}
	stateKey := choose.If(len(resp[0].SubStage) > 0, resp[0].SubStage, resp[0].WorkItemStatus.StateKey)
	var state string
	if resp[0].WorkItemTypeKey == "story" {
		state, _ = s.getFieldNameByKey(ctx, spaceId, stateKey, email).Get()
	} else {
		state = stateKey
	}
	return gresult.OK(state)
}

type roleStruct struct {
	Role   string   `json:"role"`
	Owners []string `json:"owners"`
}

func (s *service) GetWorkItemQAs(ctx context.Context, workItem int64, spaceID string, taskType string) gresult.R[[]string] {
	qaList := make([]string, 0)
	itemInfo, err := third_part.MeegoClient.QueryWorkItemById(ctx, "bits", spaceID, taskType, workItem).Get()
	if err != nil {
		return gresult.Err[[]string](errors.New("query work item error"))
	}
	for _, field := range itemInfo.Fields {
		if field.FieldKey == "role_owners" {
			str, _ := json.Marshal(field.FieldValue)
			roles := make([]*roleStruct, 0)
			err := json.Unmarshal(str, &roles)
			if err != nil {
				return gresult.Err[[]string](err)
			}
			for _, role := range roles {
				if role.Role == "QA" {
					qaList = append(qaList, role.Owners...)
				}
				if role.Role == "reporter" {
					qaList = append(qaList, role.Owners...)
				}
			}
		}
	}
	return gresult.OK(qaList)
}

func (s *service) getFieldNameByKey(ctx context.Context, projectKey, fieldKey, email string) optional.O[string] {
	ctx = mtctx.WithTenancy(ctx, email)
	var fieldName string
	key := fmt.Sprintf("devTask-meego-filed-%v-%v", projectKey, fieldKey)
	cacheRes, _ := redis.OptimusClient.Get(key).Result()
	if len(cacheRes) > 0 {
		_ = sonic.UnmarshalString(cacheRes, &fieldName)
		return optional.OK(fieldName)
	}

	req := &meego.QuerySimpleFieldsRequest{
		WorkItemTypeKey: "story",
	}
	fields, err := third_part.MeegoClient.QuerySimpleFieldsByEmail(ctx, projectKey, email, req)
	if err != nil {
		return optional.Nil[string]()
	}
	res := getWorkItemState(fields, fieldKey)
	res.IfOK(func(_ string) { redis.OptimusClient.Set(key, res.Value(), time.Hour*24) })
	return res
}

func getWorkItemState(fields []*meego.SimpleField, fieldKey string) optional.O[string] {
	field, ok := gslice.Find(fields, func(simpleField *meego.SimpleField) bool { return simpleField.FieldKey == "work_item_status" }).Get()
	if !ok {
		return optional.Nil[string]()
	}
	option, ok := gslice.Find(field.Options, func(options *meego.Option) bool { return options.Value == fieldKey }).Get()
	if !ok {
		return optional.Nil[string]()
	}
	return optional.OK(option.Label)
}

func BindFeature(ctx context.Context, query *feature.BindDevFeatureRecordQuery) error {
	resp, err := rpc.FeatureClient.BindDevFeatureRecord(ctx, query)
	if err != nil {
		return err
	}
	if resp == nil || resp.GetID() == 0 {
		return fmt.Errorf("feature.BindDevFeatureRecord empty resp")
	}
	return nil
}

func UnbindFeature(ctx context.Context, query *feature.UnbindDevFeatureRecordQuery) error {
	resp, err := rpc.FeatureClient.UnbindDevFeatureRecord(ctx, query)
	if err != nil {
		return err
	}
	if resp == nil {
		return fmt.Errorf("feature.UnbindDevFeatureRecord empty resp")
	}
	return nil
}
