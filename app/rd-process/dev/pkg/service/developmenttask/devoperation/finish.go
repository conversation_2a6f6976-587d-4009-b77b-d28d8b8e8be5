package devoperation

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/workflow/enums"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/rdtasks"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/messages"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/mq/producer"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gslice"
	"gorm.io/plugin/dbresolver"
)

func (s *Service) FinishDevTask(ctx context.Context, devBasicId int64, username string) error {
	info, err := s.infoRepository.FindById(ctx, devBasicId, dbresolver.Write).Get()
	if err != nil {
		return err
	}
	if info.State == dev.DevTaskStatus_finished.String() {
		// 已经完成的，直接返回，可重入
		return nil
	}

	if err = s.validateFinishDevTask(ctx, devBasicId); err != nil {
		return err
	}

	// 1. 修改开发任务状态
	if err = s.infoRepository.UpdateStateById(ctx, devBasicId, dev.DevTaskStatus_finished.String()); err != nil {
		return err
	}

	// 2. 通知 tasks | 仅多批次需要
	if info.WorkflowType == enums.UniqueType_DEV_TASK_ADVANCED.String() {
		rdtasks.DevTaskAdvancedFinished(ctx, devBasicId, username)
	}

	// 3. 发送核心事件
	s.sendDevTaskFinishEvent(ctx, info)
	return nil
}

func (s *Service) validateFinishDevTask(ctx context.Context, devBasicId int64) error {
	changes, err := s.changeRepository.FindByDevBasicId(ctx, devBasicId).Get()
	if err != nil {
		return err
	}
	if gslice.Any(changes, func(v *model.DevBasicChange) bool { return v.Status != dev.DevChangeStatus_completed.String() }) {
		return bits_err.DEVTASK.ErrExistMergedChange.AddErrMsg("exists unfinished changes")
	}
	return nil
}

func (s *Service) sendDevTaskFinishEvent(ctx context.Context, basicInfo *model.DevBasicInfo) {
	// 核心事件
	eventData := &events.DevTaskEvent{
		DevBasicID: basicInfo.Id,
		Type:       basicInfo.Type,
		SpaceID:    basicInfo.SpaceId,
		GroupName:  basicInfo.GroupName,
		Username:   basicInfo.Author,
		Timestamp:  time.Now().In(time.Local).Unix(),
		Action:     events.Finish,
	}
	producer.Client.SendDevTaskDevelopFinish(ctx, eventData)
	producer.Client.SendDevTaskEvent(ctx, eventData)

	// 消息通知
	members, _ := s.collaboratorRepository.FindByDevId(ctx, basicInfo.DevId).Get()
	larkGroups := make([]*dev.DevTaskRelatedLarkGroup, 0)
	s.configRepository.FindByDevBasicId(ctx, basicInfo.Id).IfOK(func(v *model.DevBasicConfig) { larkGroups = v.BizConfig.RelatedLarkGroups })
	messages.SendFinishDevTaskMessage(ctx, basicInfo.Id, basicInfo.SpaceId, basicInfo.Title, basicInfo.Author, members, larkGroups)
}
