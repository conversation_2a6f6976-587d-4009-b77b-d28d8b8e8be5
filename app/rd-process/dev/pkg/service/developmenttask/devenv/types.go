package devenv

import "code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"

type UpdateEnvLanesParams struct {
	LaneConfigs    []*dev.DevTaskEnvLaneConfig
	Username       string
	EnvProjectData map[string]string
}

type UpdateEnvProjectConfigsParams struct {
	ProjectConfigs []*dev.EnvProjectConfig
	Username       string
	EnvProjectData map[string]string
}

type GetEnvInfoForDeployAtomRequest struct {
	DevNodeId       int64  `thrift:"devNodeId,2" frugal:"2,default,i64" json:"devNodeId"`
	ProjectUniqueId string `thrift:"projectUniqueId,3" frugal:"3,default,string" json:"projectUniqueId"`
	ControlPlane    string `thrift:"controlPlane,4" frugal:"4,default,string" json:"controlPlane"`
	EnvName         string `thrift:"envName,5" frugal:"5,default,string" json:"envName"`
}

type GetDevTaskEnvLaneConfigsParams struct {
	ControlPlane *dev.ControlPlane
	TaskName     string
}
