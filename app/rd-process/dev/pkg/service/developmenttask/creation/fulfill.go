package creation

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/optimus/infra"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bits/workflow/enums"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/components/devvars"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/configcenter"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/meta"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/optimusinfra"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/payloads"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gconv"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
)

func (dc *DevTaskCreator) fulfillNecessaryInfo(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	handlers := []func(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail]{
		fillVersionCode,
		fillUsername,
		fillSnapshot,
		fillWorkFlowType,
		fillEnvSetting,
		fillDraftFlag,
		fillBizInfo,
		fillChangeByIntegration,
		fillDevId,
		fillCheckoutFrom,
		fillLarkGroups,
		fillCustomVars,
		fillInitialMessage,
	}

	TaskDetail := params.TaskDetail
	for _, handler := range handlers {
		changed, err := handler(ctx, params, snapshot).Get()
		if err != nil {
			return gresult.Err[*payloads.TaskDetail](err)
		}
		TaskDetail = changed
	}
	return gresult.OK(TaskDetail)
}

// 填写开发任务模板的快照信息
func fillSnapshot(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	// 实时获取快照ID，避免编排结果和配置不一致
	params.TaskDetail.BasicConfig.BizConfig.TemplateSnapShotId = gptr.OfNotZero(snapshot.svr.GetWorkflowSnapShotId())

	// 开发任务模板快照
	params.TaskDetail.BasicConfig.WorkflowTemplateSnapshot = model.WorkflowTemplateSnapShot{
		Workflow: snapshot.svr.GetWorkflow(),
	}

	// 依赖配置的版本号 (未使用) todo: 后续强卡
	spaceId := params.TaskDetail.BasicInfo.SpaceId
	depResp, err := configcenter.GetSpaceDependency(ctx, spaceId)
	if err != nil {
		log.V2.Error().With(ctx).KVs("dependency version code not found, space_id", spaceId).Emit()
	} else {
		params.TaskDetail.BasicConfig.BizConfig.DependencyVersion = gptr.OfNotZero(depResp.GetVersion())
	}
	return gresult.OK(params.TaskDetail)
}

// 填写开发任务对应的工作流类型 | 区分多批次
func fillWorkFlowType(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	workflowType := enums.UniqueType_DEV_TASK_V1.String()
	if (snapshot.teamFlowConfig != nil) &&
		snapshot.teamFlowConfig.TeamFlowType == sharedpb.TeamFlowType_TEAM_FLOW_TYPE_INCREMENTAL_DELIVERY &&
		snapshot.teamFlowConfig.GetIncrementalDeliveryDevTask().GetEnable() {
		workflowType = enums.UniqueType_DEV_TASK_ADVANCED.String()
	}
	params.TaskDetail.BasicInfo.WorkflowType = workflowType
	return gresult.OK(params.TaskDetail)
}

// 填写环境泳道信息
func fillEnvSetting(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	envMap := params.EnvMap
	// 补全阶段信息
	nodes := snapshot.svr.GetWorkflow().GetOrchestration()
	EnvSettings := make([]*dev.DevTaskEnvSettingItem, 0)
	for idStr, setting := range envMap {
		nodeId := gconv.To[int64](idStr)
		if nodeId == 0 {
			continue
		}
		node, ok := gslice.Find(nodes, func(node *config_service.OnesiteWorkflowNode) bool { return node.GetId() == nodeId }).Get()
		if !ok {
			continue
		}
		item := &dev.DevTaskEnvSettingItem{
			NodeId:        nodeId,
			NodeName:      node.Name,
			NodeName18n:   node.GetNameI18n(),
			NodeFixedName: node.GetNodeFixedName(),
			EnvSetting:    setting,
		}
		EnvSettings = append(EnvSettings, item)
	}
	params.TaskDetail.BasicConfig.BizConfig.EnvConfig = EnvSettings
	return gresult.OK(params.TaskDetail)
}

// 从 TCC 读取配置，标记使用启用预览
func fillDraftFlag(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	// TCC 启用预览 且 不是主干研发流程
	params.TaskDetail.BasicConfig.BizConfig.IsEnableDraft = tcc.IsSpaceEnableDraft(ctx, params.TaskDetail.BasicInfo.SpaceId) &&
		snapshot.teamFlowConfig != nil &&
		snapshot.teamFlowConfig.TeamFlowType != sharedpb.TeamFlowType_TEAM_FLOW_TYPE_TBD

	return gresult.OK(params.TaskDetail)
}

// 填充一些业务流程所需的数据
func fillBizInfo(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	taskCtx := params.TaskDetail.BasicConfig.GetBizConfig()
	// 一些业务配置
	if taskCtx != nil && taskCtx.DevContext != nil && taskCtx.DevContext.GetOriginDevTaskId() > 0 {
		params.TaskDetail.BasicConfig.OriginDevBasicId = taskCtx.DevContext.GetOriginDevTaskId()
	}
	params.TaskDetail.BasicConfig.SetInitIntegrationId(params.IntegrationId)
	params.TaskDetail.BasicConfig.BizConfig.EnvProjectData = params.EnvProjectData
	return gresult.OK(params.TaskDetail)
}

// 将变更的目标分支置空
func fillChangeByIntegration(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	// 单开发的不管 | TBD 的不管
	if snapshot == nil || snapshot.teamFlowConfig == nil || snapshot.teamFlowConfig.TeamFlowType == sharedpb.TeamFlowType_TEAM_FLOW_TYPE_TBD {
		return gresult.OK(params.TaskDetail)
	}

	// 如果是需要关联发布单的研发流程，都需要清空目标分支
	// 1. 绑定了集成区，以集成区为准
	// 2. 未绑定集成区，根据预览是否开启，后续会补充目标分支
	// 3. 仅 TBD 目前不需要关联发布单
	params.TaskDetail.BasicChanges = gslice.Map(params.TaskDetail.BasicChanges, func(f *model.DevBasicChange) *model.DevBasicChange {
		f.BizConfig.Manifest.CodeElement.TargetBranch = ""
		return f
	})
	return gresult.OK(params.TaskDetail)
}

// 注册 dev_id | 后续的 MeeGo 绑定 + tag 绑定 + 协作者的绑定，都需要 dev_id
func fillDevId(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	// 注册 DevId
	req := &infra.AddDevRequest{
		Info: &infra.AddDevInfo{
			Title:  params.TaskDetail.BasicInfo.Title,
			Type:   params.TaskDetail.BasicInfo.Type,
			Author: params.TaskDetail.BasicInfo.Author,
		},
	}
	devId, err := optimusinfra.RegisterDevId(ctx, req).Get()
	if err != nil {
		return gresult.Err[*payloads.TaskDetail](err)
	}

	// basic_info 中 dev_id 字段需要更新
	params.TaskDetail.BasicInfo.DevId = devId
	// collaborator 中 dev_id 字段需要更新
	params.TaskDetail.Collaborators = gslice.Map(params.TaskDetail.Collaborators, func(c *model.DevCollaborator) *model.DevCollaborator {
		c.DevId = devId
		return c
	})
	// dev_tags 中 dev_id 字段需要更新
	params.TaskDetail.Tags = gslice.Map(params.TaskDetail.Tags, func(t *model.DevTags) *model.DevTags {
		t.DevId = devId
		return t
	})
	return gresult.OK(params.TaskDetail)
}

// 根据空间分支模型配置的归档分支，用于后续的分支操作
func fillCheckoutFrom(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	getSourceBranchCheckoutFrom := func(ctx context.Context, spaceId int64, taskContext *dev.DevTaskContext) gresult.R[string] {
		// 指定了 checkout 就以指定的值为准
		if taskContext != nil && len(taskContext.GetCheckoutFrom()) > 0 {
			return gresult.OK(taskContext.GetCheckoutFrom())
		}
		return gresult.OK("")
	}

	checkoutFrom, err := getSourceBranchCheckoutFrom(ctx, params.TaskDetail.BasicInfo.SpaceId, params.TaskDetail.BasicConfig.GetBizConfig().GetDevContext()).Get()
	if err != nil {
		return gresult.Err[*payloads.TaskDetail](err)
	}
	params.TaskDetail.BasicConfig.BizConfig.DevContext.CheckoutFrom = gptr.OfNotZero(checkoutFrom)
	return gresult.OK(params.TaskDetail)
}

// 填写飞书群信息
func fillLarkGroups(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	larkGroups := make([]*dev.DevTaskRelatedLarkGroup, 0)
	relatedGroups := params.TaskDetail.BasicConfig.BizConfig.RelatedLarkGroups
	if len(relatedGroups) == 0 {
		return gresult.OK(params.TaskDetail)
	}
	gslice.ForEach(relatedGroups, func(inputLarkGroup *dev.DevTaskRelatedLarkGroup) {
		larkGroup := &dev.DevTaskRelatedLarkGroup{
			Id:               inputLarkGroup.GetId(),
			BizType:          inputLarkGroup.GetBizType(),
			CreatedInProcess: inputLarkGroup.GetCreatedInProcess(),
		}
		// create new lark group when need
		if inputLarkGroup.GetCreatedInProcess() {
			larkGroupId, err := meta.CreateLarkChat(ctx, params.TaskDetail.BasicInfo.Author, nil, choose.If(inputLarkGroup.GetName() != "", inputLarkGroup.GetName(), params.TaskDetail.BasicInfo.Title), nil).Get()
			if err != nil {
				log.V2.Error().With(ctx).Str("create lark group failed").KVs("dev task title", params.TaskDetail.BasicInfo.Title).Error(err).Emit()
				return
			}
			larkGroup.Id = larkGroupId
		}
		larkGroups = append(larkGroups, larkGroup)
	})
	params.TaskDetail.BasicConfig.BizConfig.RelatedLarkGroups = larkGroups
	return gresult.OK(params.TaskDetail)
}

// 填写自定义变量的 assignment id | 避免数据过多超出了 DB 中 text 类型的范围
func fillCustomVars(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	if len(params.CustomVariables) == 0 {
		return gresult.OK(params.TaskDetail)
	}
	spaceId := params.TaskDetail.BasicInfo.SpaceId
	username := params.TaskDetail.BasicInfo.Author
	assignmentId, err := devvars.NewDevTaskVarSvr().GenerateCustomVars(ctx, spaceId, params.CustomVariables, username).Get()
	if err != nil {
		return gresult.Err[*payloads.TaskDetail](err)
	}
	params.TaskDetail.BasicConfig.VarsAssignmentId = assignmentId
	params.TaskDetail.BasicConfig.BizConfig.VariableRecord = &dev.VariableRecord{
		CustomAssignmentId: gptr.OfNotZero(assignmentId),
	}
	return gresult.OK(params.TaskDetail)
}

func fillInitialMessage(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	// basic_info 的状态
	params.TaskDetail.BasicInfo.State = dev.DevTaskStatus_initial.String()

	// basic_change 的状态
	changes := params.TaskDetail.BasicChanges
	gslice.ForEach(changes, func(v *model.DevBasicChange) { v.Status = dev.DevChangeStatus_opened.String() })
	params.TaskDetail.BasicChanges = changes

	return gresult.OK(params.TaskDetail)
}

func fillUsername(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	username := params.TaskDetail.BasicInfo.Author
	// change
	changes := params.TaskDetail.BasicChanges
	gslice.ForEach(changes, func(v *model.DevBasicChange) { v.Author = choose.If(len(v.Author) > 0, v.Author, username) })
	params.TaskDetail.BasicChanges = changes
	// projects
	projects := params.TaskDetail.Projects
	gslice.ForEach(projects, func(v *model.DevDeployConfig) { v.Owner = choose.If(len(v.Owner) > 0, v.Owner, username) })
	params.TaskDetail.Projects = projects
	return gresult.OK(params.TaskDetail)
}

func fillVersionCode(ctx context.Context, params *payloads.CreateDevTaskParams, snapshot *configSnapshot) gresult.R[*payloads.TaskDetail] {
	params.TaskDetail.BasicInfo.VersionCode = 1
	return gresult.OK(params.TaskDetail)
}
