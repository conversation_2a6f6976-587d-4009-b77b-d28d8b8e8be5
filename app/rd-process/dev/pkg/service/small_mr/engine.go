package small_mr

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/engine"
	"code.byted.org/gopkg/logs"
	"context"
	"github.com/pkg/errors"
)

func GetDevCreateEngineTasksInfo(ctx context.Context, devID int64, cID int64, cType dev.ContributionType) ([]*dev.TaskInfo, error) {
	switch cType {
	case dev.ContributionType_dev_mr:
		// dev id higher priority
		devCreateID := int64(0)
		if devID != 0 {
			cid, err := data.OptimusDB.Slave.UseCache().GetContributionDevMRIDByDevID(ctx, devID)
			if err != nil {
				return nil, err
			}
			id, err := data.OptimusDB.Slave.UseCache().GetDevContributionCreationConfigIDByCIDAndCType(ctx, cid, int(cType))
			if err != nil {
				return nil, err
			}
			devCreateID = id
		} else {
			id, err := data.OptimusDB.Slave.UseCache().GetDevContributionCreationConfigIDByCIDAndCType(ctx, cID, int(cType))
			if err != nil {
				return nil, err
			}
			devCreateID = id
		}
		result, err := engine.GetDevCreateEngineTasksStatus(ctx, devCreateID)
		if err != nil {
			return nil, err
		}
		return adaptTaskInfoListToIDL(result), nil
	case dev.ContributionType_mr:
		if cID == 0 {
			logs.CtxError(ctx, "lack of contribution id")
			return nil, errors.New("lack of param")
		}
		devCreateID, err := data.OptimusDB.Slave.UseCache().GetDevContributionCreationConfigIDByCIDAndCType(ctx, cID, int(cType))
		if err != nil {
			return nil, err
		}
		result, err := engine.GetDevCreateEngineTasksStatus(ctx, devCreateID)
		if err != nil {
			return nil, err
		}
		return adaptTaskInfoListToIDL(result), nil
	default:
		logs.CtxError(ctx, "invalid contribution type")
		return nil, errors.New("invalid contribution type")
	}
}

func GetDevCreateEngineStatus(ctx context.Context, tasks []*dev.TaskInfo) dev.GraphStatus {
	status := dev.GraphStatus_unknown
	pendingCount := 0
	runningCount := 0
	for _, task := range tasks {
		if task.Status == engine.OptimusEngineTypeFailed && len(task.ErrMsg) > 0 {
			status = dev.GraphStatus_failed
			break
		}
		if task.GetName() == engine.OptimusEngineTaskDevCreateEngineStart && task.Status == engine.OptimusEngineTypeRunning {
			status = dev.GraphStatus_success
			break
		}
		if task.Status == engine.OptimusEngineTypePending {
			pendingCount += 1
		}
		if task.Status == engine.OptimusEngineTypeRunning {
			runningCount += 1
		}
	}
	if status == dev.GraphStatus_unknown {
		if pendingCount == len(tasks) {
			status = dev.GraphStatus_not_start
		} else {
			status = dev.GraphStatus_running
		}
	}
	return status
}
