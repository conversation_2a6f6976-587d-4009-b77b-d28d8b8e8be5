package devtask

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/rd_process/tasks"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/rdtasks"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"context"
)

func MergeDevChangesTarget(ctx context.Context, devBasicId int64, all bool, changeIds []int64, username string) gresult.R[[]*dev.MergeDevChangeItem] {
	var err error
	var changes = make([]*model.DevBasicChange, 0)
	if all {
		changes, err = data.OptimusDB.Slave.BatchGetDevBasicChangesByDevBasicID(ctx, devBasicId, 10).Get()
	} else {
		changes, err = data.OptimusDB.Slave.GetDevBasicChangeListByDevBasicChangeIds(ctx, changeIds).Get()
	}
	if err != nil {
		return gresult.Err[[]*dev.MergeDevChangeItem](err)
	}

	return mergeDevChangesTarget(ctx, devBasicId, changes, username)
}

func mergeDevChangesTarget(ctx context.Context, devBasicId int64, changes []*model.DevBasicChange, username string) gresult.R[[]*dev.MergeDevChangeItem] {
	changes = gslice.Filter(changes, func(c *model.DevBasicChange) bool {
		return c.Type == dev.ChangeType_CHANGE_TYPE_CODE.String() && !c.IsNotCreated()
	})
	codeChangeIds := gslice.Map(changes, func(c *model.DevBasicChange) int64 { return c.BizId })
	mrs, err := gitsvr.BatchGetMrsByCodeChangeIds(ctx, codeChangeIds).Get()
	if err != nil {
		return gresult.Err[[]*dev.MergeDevChangeItem](err)
	}
	if len(mrs) == 0 {
		return gresult.OK([]*dev.MergeDevChangeItem{})
	}

	var mergeItems = make([]*tasks.MergeTargetItem, 0)
	for _, mr := range mrs {
		item := &tasks.MergeTargetItem{
			Username:     username,
			ProjectId:    mr.GetProjectID(),
			Iid:          mr.GetIid(),
			SourceBranch: mr.GetSourceBranch(),
			TargetBranch: mr.TargetBranch,
		}
		mergeItems = append(mergeItems, item)
	}

	req := &tasks.MergeTargetRequest{
		DevBasicId: &devBasicId,
		Items:      mergeItems,
	}
	items, err := rdtasks.MergeTarget(ctx, req).Get()
	if err != nil {
		return gresult.Err[[]*dev.MergeDevChangeItem](err)
	}

	// formResp
	var res = make([]*dev.MergeDevChangeItem, 0)
	for _, respItem := range items {
		item := &dev.MergeDevChangeItem{
			Username:     username,
			SourceBranch: respItem.GetSourceBranch(),
			TargetBranch: respItem.GetTargetBranch(),
			JobUrl:       respItem.GetUrl(),
			RepoPath: gslice.Find(changes, func(r *model.DevBasicChange) bool {
				return r.SourceBranch == respItem.SourceBranch &&
					r.GetManifest().GetCodeElement().GetRepoId() == respItem.GetProjectId()
			}).ValueOrZero().RepoPath,
		}
		res = append(res, item)
	}
	return gresult.OK(res)
}
