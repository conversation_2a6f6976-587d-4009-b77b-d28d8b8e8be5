package devtask

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/feature"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/devtask/devtaskmodel"
)

type RelatedInfo struct {
	Tags          []*optimus.DevTag
	WorkItems     []*feature.DevFeatureRecordInfo
	ReleaseTicket *dev.RelatedReleaseTicketInfo
}

type ChangeInfo struct {
	Detail       *devtaskmodel.ChangeDetail
	CommentCount int64
	DiffCount    *dev.CodeDiffCount
	ReviewInfo   *dev.ReviewInfo
	CheckStatus  dev.CodeChangeCheckStatus
}

type ChangeCodeReviewInfo struct {
	RepoName       string
	Source         string
	Target         string
	CheckStatus    string
	ReviewInfo     *dev.ReviewInfo
	LatestCommitId string
	Title          string
	CodeChangeID   int64
	CodeElement    *dev.DevCodeChangeElement
	ContributionId int64
	DevChangeID    int64
}

type WorkflowResp struct {
	Stages       []*dev.WorkflowStage
	AutoMerge    bool
	SetAutoMerge bool
	CurrentStage string
	CanMerge     bool
	AutoPassDev  bool
}

type DBBasicInfo struct {
	TaskBasic *model.DevBasicInfo
}
