package devtask

import (
	"context"
	"fmt"
	"math"
	ss "strings"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/es"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/releaseticket"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
	"code.byted.org/devinfra/hagrid/libs/compatibletenancy/emails"

	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/toutiao/elastic/v7"
	json "github.com/bytedance/sonic"
	"gorm.io/gorm"
)

func SearchDevBasicIdListFromDB(ctx context.Context, req *dev.GetDevBasicIdListBySearchRequest) gresult.R[*dev.GetDevBasicIdListBySearchResponse] {
	var idList = make([]int64, 0)
	var size = int64(10)
	if req.GetSize() > 0 {
		size = req.GetSize()
	}
	if req.GetLastId() == 0 {
		req.LastId = gptr.OfNotZero(int64(math.MaxInt64))
	}

	err := formWhereStat(ctx, req).Select("id").Limit(1000).Find(&idList).Error
	if err != nil {
		return gresult.Err[*dev.GetDevBasicIdListBySearchResponse](err)
	}
	total := int64(len(idList))

	if total < size {
		size = total
	}

	resp := &dev.GetDevBasicIdListBySearchResponse{
		IdList:     idList[:size],
		Total:      int64(len(idList)),
		Downgraded: gptr.Of(true),
	}
	return gresult.OK(resp)
}

func formWhereStat(ctx context.Context, req *dev.GetDevBasicIdListBySearchRequest) *gorm.DB {
	devTaskMode := dev.DevTaskMode_standard.String()
	if req.DevTaskMode != nil {
		devTaskMode = req.GetDevTaskMode().String()
	}

	authors := req.GetAuthors()
	if req.Author != nil {
		authors = append(authors, req.GetAuthor())
	}
	db := data.OptimusDB.Slave.
		GetDB(ctx).
		Model(&model.DevBasicInfo{}).
		Where("space_id = ? and type = ?", req.GetSpaceId(), devTaskMode)
	if req.Status != nil {
		db = db.Where("state = ?", req.GetStatus().String())
	}
	if len(authors) > 0 {
		db = db.Where("author in ?", authors)
	}
	if !strings.IsEmpty(req.GetKeyword()) {
		db = db.Where("title like ?", "%"+escapeSpecialCharacter(req.GetKeyword())+"%")
	}
	if req.GetLastId() > 0 {
		db = db.Where("id < ?", req.GetLastId())
	}
	if len(req.GetDevBasicIdsForSearch()) > 0 {
		db = db.Where("id in ?", req.GetDevBasicIdsForSearch())
	}

	if devTaskMode == dev.DevTaskMode_standard.String() {
		filterConfigs, _ := tcc.GetSearchDevBasicIdListFilter(ctx).Get()
		spaceFilterConfigs := gslice.Filter(filterConfigs, func(c *tcc.SearchDevBasicIdListSpaceFilter) bool { return c.SpaceId == req.GetSpaceId() })
		if len(spaceFilterConfigs) > 0 {
			spaceFilterConfig := spaceFilterConfigs[0]
			db = db.Where("id >= ?", spaceFilterConfig.DevBasicId)
		}
	}

	if ndb := formRangeStat(ctx, req, db); ndb != nil {
		db = ndb
	}

	db = db.Order("id desc")
	return db
}

func formRangeStat(ctx context.Context, req *dev.GetDevBasicIdListBySearchRequest, db *gorm.DB) *gorm.DB {
	var wholeScope = make([]int64, 0)
	filtered := false

	var basicScope = make([]int64, 0)
	if len(req.GetUsernames()) > 0 {
		filtered = true
		err := data.OptimusDB.Slave.GetDB(ctx).
			Model(&model.DevBasicInfo{}).
			Joins("join dev_collaborator on dev_collaborator.dev_id = dev_basic_info.dev_id").
			Where("dev_collaborator.name in ?", req.GetUsernames()).
			Where("dev_basic_info.space_id = ?", req.GetSpaceId()).
			Select("dev_basic_info.id").
			Find(&basicScope).Error
		if err != nil {
			return nil
		}
	}

	members := req.GetMemberList()
	if req.Member != nil {
		username := emails.TrimSuffix(req.GetMember().GetEmail())
		idx, ok := gslice.IndexBy(members, func(listItem *dev.DevTaskMemberListItem) bool {
			return listItem.GetRole() == req.GetMember().GetRole()
		}).Get()
		if !ok {
			members = append(members, &dev.DevTaskMemberListItem{
				Role:      req.GetMember().GetRole(),
				Usernames: []string{username},
			})
		} else {
			members[idx].Usernames = append(members[idx].Usernames, username)
		}
	}
	if len(members) > 0 {
		filtered = true
		for _, item := range members {
			var itemScope = make([]int64, 0)
			err := data.OptimusDB.Slave.GetDB(ctx).
				Model(&model.DevBasicInfo{}).
				Joins("join dev_collaborator on dev_collaborator.dev_id = dev_basic_info.dev_id").
				Where("dev_collaborator.type = ? and dev_collaborator.name in ?", item.GetRole(), item.GetUsernames()).
				Select("dev_basic_info.id").
				Find(&itemScope).Error
			if err != nil {
				return nil
			}
			if len(basicScope) == 0 {
				basicScope = itemScope
			} else {
				basicScope = set.NewInt64Set(itemScope...).Intersect(set.NewInt64Set(basicScope...)).ToList()
			}
		}
	}

	var configScope = make([]int64, 0)
	if req.ManifestReady != nil || len(req.GetDevTemplateIds()) > 0 || len(req.GetDevTypes()) > 0 {
		filtered = true
		configDb := data.OptimusDB.Slave.GetDB(ctx).
			Model(&model.DevBasicConfig{})
		if len(req.GetDevBasicIdsForSearch()) > 0 {
			configDb = configDb.Where("dev_basic_id in ?", req.GetDevBasicIdsForSearch())
		}
		if req.ManifestReady != nil {
			val := choose.If(req.GetManifestReady(), 1, 0)
			configDb = configDb.Where("manifest_ready = ?", val)
		}
		if len(req.GetDevTemplateIds()) != 0 {
			configDb = configDb.Where("dev_template_id in ?", req.GetDevTemplateIds())
		}
		if len(req.GetDevTypes()) > 0 {
			types := req.GetDevTypes()
			configDb = configDb.Where("type in ?", slicex.Map(types, func(v dev.DevTaskType) string {
				return v.String()
			}))
		}
		err := configDb.Select("dev_basic_id").Limit(1000).Find(&configScope).Error
		if err != nil {
			return nil
		}
	}

	if !filtered {
		return nil
	}
	if len(basicScope) != 0 && len(configScope) != 0 {
		wholeScope = set.NewInt64Set(basicScope...).Intersect(set.NewInt64Set(configScope...)).ToList()
	} else if len(basicScope) != 0 {
		wholeScope = basicScope
	} else if len(configScope) != 0 {
		wholeScope = configScope
	}

	if req.DevBasicIdsForSearch != nil {
		wholeScope = gslice.Filter(wholeScope, func(i int64) bool { return gslice.Contains(req.GetDevBasicIdsForSearch(), i) })
	}

	return db.Where("id in ?", wholeScope)
}

func escapeSpecialCharacter(text string) string {
	text = ss.ReplaceAll(text, `"`, `\"`)
	text = ss.ReplaceAll(text, `\`, `\\`)
	text = ss.ReplaceAll(text, `%`, `\%`)
	text = ss.ReplaceAll(text, `_`, `\_`)
	return text
}

func SearchDevBasicIdListFromES(ctx context.Context, req *dev.GetDevBasicIdListBySearchRequest) gresult.R[*dev.GetDevBasicIdListBySearchResponse] {
	// 搜索请求参数规范化处理
	req = normalizeSearchRequest(req)

	// 构建 ES 查询条件
	query, isEmpty, err := formEsQueryBySearchRequest(ctx, req)
	if err != nil {
		return gresult.Err[*dev.GetDevBasicIdListBySearchResponse](err)
	}
	if isEmpty {
		resp := &dev.GetDevBasicIdListBySearchResponse{
			IdList: []int64{},
			Total:  0,
		}
		return gresult.OK(resp)
	}

	// 排序规则
	sorts := formSortOptBySearchRequest(req)

	// 执行 ES 搜索
	res, err := es.NewDevTaskClient().GetQuery(ctx, query, 0, int(req.GetSize())).SortBy(sorts...).Do(ctx)
	if err != nil {
		logs.CtxError(ctx, "failed to query from es:%s", err.Error())
		return gresult.Err[*dev.GetDevBasicIdListBySearchResponse](err)
	}
	idList := make([]int64, 0)
	for _, hit := range res.Hits.Hits {
		temp := struct {
			DevBasicID    int64    `json:"dev_basic_id"`
			Collaborators []string `json:"collaborator"`
		}{}
		if err := json.Unmarshal(hit.Source, &temp); err != nil {
			logs.CtxError(ctx, "unmarshal error=%s", err.Error())
			continue
		}
		if req.AboutUser != nil && !gslice.Contains(temp.Collaborators, req.GetAboutUser()) {
			continue
		}
		idList = append(idList, temp.DevBasicID)
	}
	return gresult.OK(&dev.GetDevBasicIdListBySearchResponse{
		IdList: idList,
		Total:  res.Hits.TotalHits.Value,
	})
}

func normalizeSearchRequest(req *dev.GetDevBasicIdListBySearchRequest) *dev.GetDevBasicIdListBySearchRequest {
	// size 默认 10
	req.Size = choose.If(req.GetSize() == 0, gptr.Of(int64(10)), req.Size)
	// lastId 默认 math.MaxInt64
	req.LastId = choose.If(req.GetLastId() == 0, gptr.OfNotZero(int64(math.MaxInt64)), req.LastId)
	// devTaskMode 默认 standard
	req.DevTaskMode = choose.If(req.DevTaskMode == nil, gptr.Of(dev.DevTaskMode_standard), req.DevTaskMode)
	// author 不为空时，添加到 authors 中
	if req.Author != nil {
		req.Authors = append(req.Authors, req.GetAuthor())
	}
	return req
}

func formEsQueryBySearchRequest(ctx context.Context, req *dev.GetDevBasicIdListBySearchRequest) (*elastic.BoolQuery, bool, error) {
	query := elastic.NewBoolQuery()
	// 开发任务模式
	query.Must(elastic.NewTermQuery("type", req.GetDevTaskMode().String()))
	// 空间 ID
	if req.SpaceId != 0 {
		query.Must(elastic.NewTermQuery("space_id", req.SpaceId))
	}
	// 开发任务状态
	if req.Status != nil {
		query.Must(elastic.NewTermQuery("state", req.GetStatus().String()))
	}
	// 开发任务创建人
	if authors := req.GetAuthors(); len(authors) > 0 && req.AboutUser == nil {
		query.Must(elastic.NewBoolQuery().Should(gslice.Map(authors, func(author string) elastic.Query { return elastic.NewTermQuery("author", author) })...))
	}
	// 标题关键字查询
	if !strings.IsEmpty(req.GetKeyword()) {
		query.Should(
			elastic.NewMatchQuery("title", req.GetKeyword()).Boost(1.0),
			elastic.NewWildcardQuery("title.text", fmt.Sprintf("*%s*", req.GetKeyword())).Boost(2.0),
		).MinimumNumberShouldMatch(1)
	}
	// 开发任务类型
	if len(req.DevTypes) > 0 {
		query.Must(elastic.NewTermsQuery("dev_task_type", gslice.Map(req.DevTypes, func(t dev.DevTaskType) any { return t.String() })...))
	}
	// feed 流范围查询参数 - lastId
	if req.GetLastId() > 0 {
		query.Must(elastic.NewRangeQuery("dev_basic_id").Lt(req.GetLastId()))
	}
	// 开发任务完成时间
	if req.FinishTime != nil {
		query.Must(elastic.NewRangeQuery("merge_time").Gte(req.FinishTime.From).Lte(req.FinishTime.To))
	}
	// 开发任务关闭时间
	if req.CloseTime != nil {
		query.Must(elastic.NewRangeQuery("close_time").Gte(req.CloseTime.From).Lte(req.CloseTime.To))
	}
	// 指定开发任务 Id 列表
	if len(req.GetDevBasicIdsForSearch()) > 0 {
		query.Must(elastic.NewTermsQuery("dev_basic_id", gslice.Map(req.GetDevBasicIdsForSearch(), func(i int64) any { return i })...))
	}
	// 指定空间的开发任务查询范围（估计是当时数据有些问题，需要过滤一下，详情需要咨询 zhangyibo）
	if req.GetDevTaskMode() == dev.DevTaskMode_standard {
		filterConfigs, _ := tcc.GetSearchDevBasicIdListFilter(ctx).Get()
		spaceFilterConfigs := gslice.Filter(filterConfigs, func(c *tcc.SearchDevBasicIdListSpaceFilter) bool { return c.SpaceId == req.GetSpaceId() })
		if len(spaceFilterConfigs) > 0 {
			spaceFilterConfig := spaceFilterConfigs[0]
			query.Must(elastic.NewRangeQuery("dev_basic_id").Gte(spaceFilterConfig.DevBasicId))
		}
	}
	// codebase 仓库名称
	if req.RepoName != nil {
		query.Must(elastic.NewMatchQuery("repo_name", *req.RepoName))
	}
	// 开发任务中包含的应用中心服务
	if req.ServiceName != nil && *req.ServiceName != "" {
		query.Must(elastic.NewMatchQuery("services", *req.ServiceName))
	}
	// Codebase MR 源分支
	if req.SourceBranch != nil {
		query.Must(elastic.NewMatchQuery("source_branch", *req.SourceBranch))
	}
	// Codebase MR 目标分支
	if req.TargetBranch != nil {
		query.Must(elastic.NewMatchQuery("target_branch", *req.TargetBranch))
	}
	// Codebase MR （projectId + iid）
	if req.MrItems != nil {
		query.Must(elastic.NewTermsQuery("project_id_iid_combine", gslice.Map(req.MrItems, func(i *dev.MrSearchItem) any { return fmt.Sprintf("%v_%v", i.GetProjectId(), i.GetIid()) })...))
	}
	// version_or_publish 集成区 ID
	if len(req.PublishId) > 0 {
		query.Must(elastic.NewTermsQuery("version_or_publish", gslice.Map(req.PublishId, func(i string) any { return i })...))
	}
	// Codebase MR 的 reviewer
	if req.Reviewer != nil {
		changes, err := rpc.GitServerClient.SearchChange(ctx, &git_server.SearchChangeRequest{
			Options: &git_server.SearchChangeOptions{
				PendingReviewUsername: req.Reviewer,
				Status:                git_server.ChangeStatePtr(git_server.ChangeState_open),
				PerPage:               gptr.Of(int64(math.MaxInt64 - 1)),
			},
		})
		if err != nil {
			logs.CtxError(ctx, "failed to search changes:%s", err.Error())
			return nil, false, err
		}
		if len(changes.Changes) == 0 {
			return nil, true, nil
		}
		query.Must(elastic.NewTermsQuery("repo_id_change_id_combine", gslice.Map(changes.Changes, func(c *git_server.CodebaseChange) any {
			return fmt.Sprintf("%d_%d", c.Target.Repo.Id, c.Id)
		})...))
	}
	// 开发任务的相关用户（创建人/协作者）
	if req.AboutUser != nil {
		query.Must(elastic.NewBoolQuery().Should(elastic.NewTermQuery("author", req.GetAboutUser()), elastic.NewMatchQuery("collaborator", req.GetAboutUser())))
	}
	// 开发任务的工作流阶段
	if req.WorkflowStage != nil {
		query.Must(elastic.NewMatchQuery("workflow_stage", *req.WorkflowStage))
	}
	// 开发任务管理的 MeeGo
	if req.Meego != nil {
		query.Must(elastic.NewMatchQuery("meego", *req.Meego))
	}
	// 开发任务管理的 jira
	if req.Jira != nil {
		query.Must(elastic.NewMatchQuery("jira", *req.Jira))
	}
	// 开发任务协作者
	if req.Member != nil {
		queryStr := emails.TrimSuffix(req.Member.Email)
		if req.Member.Role == dev.DevTaskMemberRole_TESTER {
			queryStr += "_QA"
		} else if req.Member.Role == dev.DevTaskMemberRole_DEVELOPER {
			queryStr += "_RD"
		}
		query.Must(elastic.NewMatchQuery("collaborator_role", queryStr))
	}
	if len(req.MemberList) > 0 {
		for _, ml := range req.MemberList {
			for _, username := range ml.Usernames {
				queryStr := username
				if ml.Role == dev.DevTaskMemberRole_TESTER {
					queryStr += "_QA"
				} else if ml.Role == dev.DevTaskMemberRole_DEVELOPER {
					queryStr += "_RD"
				}
				query.Must(elastic.NewMatchQuery("collaborator_role", queryStr))
			}
		}
	}
	if len(req.Usernames) > 0 && req.AboutUser == nil {
		query.Must(elastic.NewBoolQuery().Should(gslice.Map(req.Usernames, func(username string) elastic.Query { return elastic.NewMatchQuery("collaborator", username) })...))
	}
	// 开发任务模板
	if len(req.DevTemplateIds) > 0 {
		query.Must(elastic.NewBoolQuery().Should(gslice.Map(req.DevTemplateIds, func(id int64) elastic.Query { return elastic.NewTermQuery("template", id) })...).MinimumNumberShouldMatch(1))
	}
	// 开发任务的（代码）变更是否都有了目标分支 - 当前好像没有使用了
	if req.ManifestReady != nil && *req.ManifestReady {
		query.Must(elastic.NewTermQuery("manifest_ready", *req.ManifestReady))
	}

	// 是否研发流程灰度空间，报错不阻塞其他流程
	// 全量后移除
	isTeamFlowGray, err := releaseticket.NewService().GetIsGrayForTeamFlow(ctx, uint64(req.GetSpaceId())).Get()
	if err != nil {
		logs.CtxWarn(ctx, "GetIsGrayForTeamFlow %v:%s", req.GetSpaceId(), err.Error())
	}
	if err == nil && isTeamFlowGray && req.TeamFlowId != nil {
		query.Must(elastic.NewMatchQuery("team_flow_id", *req.TeamFlowId))
	}
	return query, false, nil
}

func formSortOptBySearchRequest(req *dev.GetDevBasicIdListBySearchRequest) []elastic.Sorter {
	if len(req.Sort) > 0 {
		return gslice.Map(req.Sort, func(s *dev.SortOption) elastic.Sorter {
			return elastic.NewFieldSort(s.Field).Order(s.Asc)
		})
	}
	// 兜底排序
	if req.GetKeyword() == "" {
		return []elastic.Sorter{elastic.NewFieldSort("dev_basic_id").Order(false)}
	}
	return []elastic.Sorter{elastic.NewScoreSort(), elastic.NewFieldSort("dev_basic_id").Order(false)}
}

func GetDevTaskIdsByRepoBranchAndTagId(ctx context.Context, req *dev.GetDevTaskByRepoBranchAndTagIdRequest) ([]int64, int64, error) {
	// 标准化请求参数
	req.LastId = choose.If(req.GetLastId() == 0, math.MaxInt64-1, req.GetLastId())
	req.Limit = choose.If(req.GetLimit() == 0 || req.GetLimit() > 1000, 1000, req.GetLimit())

	// build es query
	query := elastic.NewBoolQuery()

	repoBranchUnit := ss.Join([]string{req.GetRepoPath(), req.GetSourceBranch(), req.GetTargetBranch()}, " ")
	query.Must(elastic.NewTermQuery("repo_source_target", repoBranchUnit))
	if req.Status != nil {
		query.Must(elastic.NewTermQuery("state", req.GetStatus().String()))
	}
	if len(req.GetTagIds()) > 0 {
		query.Must(elastic.NewTermsQuery("tag_ids", gslice.Map(req.GetTagIds(), func(id int64) any { return id })))
	}
	if req.Keyword != nil {
		query.Must(elastic.NewMatchQuery("title", req.GetKeyword()))
	}
	if req.GetLastId() > 0 {
		query.Must(elastic.NewRangeQuery("dev_basic_id").Lt(req.GetLastId()))
	}

	res, err := es.NewDevelopmentTaskClient().GetQuery(ctx, query, 0, int(req.GetLimit())).
		Sort("created_at", false).
		Do(ctx)
	if err != nil {
		err = fmt.Errorf("query dev task error")
		return nil, 0, err
	}
	idList := make([]int64, 0)
	for _, hit := range res.Hits.Hits {
		temp := struct {
			DevBasicID int64 `json:"dev_basic_id"`
		}{}
		if err = json.Unmarshal(hit.Source, &temp); err != nil {
			logs.CtxError(ctx, "unmarshal error=%s", err.Error())
			continue
		}
		idList = append(idList, temp.DevBasicID)
	}
	return idList, res.Hits.TotalHits.Value, nil
}
