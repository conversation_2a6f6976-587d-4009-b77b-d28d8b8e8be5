package collaborator

import (
	"testing"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	. "github.com/bytedance/mockey"
	. "github.com/smartystreets/goconvey/convey"
)

func TestIsAlreadyExist(t *testing.T) {
	existed := []*model.DevCollaborator{
		{
			Type: dev.DevTaskMemberRole_TESTER,
			Name: "user1",
		},
	}
	PatchConvey("exist", t, func() {
		another := &model.DevCollaborator{
			Type: dev.DevTaskMemberRole_TESTER,
			Name: "user1",
		}
		So(IsAlreadyExist(existed, another), ShouldBeTrue)
	})

	PatchConvey("not exist", t, func() {
		another := &model.DevCollaborator{
			Type: dev.DevTaskMemberRole_DEVELOPER,
			Name: "user1",
		}
		So(IsAlreadyExist(existed, another), ShouldBeFalse)
	})
}

func TestIsAlreadyCollaborators(t *testing.T) {
	existed := []*model.DevCollaborator{
		{
			Type: dev.DevTaskMemberRole_TESTER,
			Name: "user1",
		},
	}
	PatchConvey("exist", t, func() {
		So(IsAlreadyCollaborators(existed, "user1"), ShouldBeTrue)
	})

	PatchConvey("not exist", t, func() {
		So(IsAlreadyCollaborators(existed, "user2"), ShouldBeFalse)
	})
}

func TestNewCollaboratorsByUsernames(t *testing.T) {
	PatchConvey("new", t, func() {
		usernames := []string{"user1", "user2"}
		collaborators := NewCollaboratorsByUsernames(usernames, dev.DevTaskMemberRole_DEVELOPER)
		So(collaborators, ShouldEqual, []*model.DevCollaborator{
			{
				Type: dev.DevTaskMemberRole_DEVELOPER,
				Name: "user1",
			},
			{
				Type: dev.DevTaskMemberRole_DEVELOPER,
				Name: "user2",
			},
		})
	})
}
