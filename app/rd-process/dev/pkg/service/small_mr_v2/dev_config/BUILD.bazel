load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "dev_config",
    srcs = [
        "dev_smr_biz_config.go",
        "dev_smr_config.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/dev_config",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rd-process/dev/kitex_gen/bytedance/bits/dev",
        "//app/rd-process/dev/pkg/dal/mysql/model",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_lang_gg//gresult",
    ],
)
