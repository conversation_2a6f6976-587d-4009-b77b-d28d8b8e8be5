package change_entity

import (
	"context"
	"errors"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/consts"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/dcache"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/common/derr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/deps/gitsvr"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/biz_config"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/code_review"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/service/small_mr_v2/value_object"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gresult"
)

type CodeChange struct {
	Contribution *model.ContributionCodeChange
	GitlabMr     *git_server.CodeChangeGitlabMergeRequest
}

func GetCodeChange(ctx context.Context, cid int64) gresult.R[*CodeChange] {
	contribution, err := data.OptimusDB.Slave.UseCache().GetContributionCodeChangeByID(ctx, cid).Get()
	if err != nil {
		return gresult.Err[*CodeChange](err)
	}
	mr, err := gitsvr.GetCodeChangeDetailGitlabMr(ctx, contribution.CodeChangeId).Get()
	if err != nil {
		return gresult.Err[*CodeChange](err)
	}
	return gresult.OK(&CodeChange{Contribution: contribution, GitlabMr: mr})
}

func GetCodeChangeByCodeChangeID(ctx context.Context, ccid int64) gresult.R[*CodeChange] {
	contribution, err := data.OptimusDB.Slave.UseCache().GetContributionCodeChangeByCodeChangeID(ctx, ccid).Get()
	if err != nil {
		return gresult.Err[*CodeChange](err)
	}
	mr, err := gitsvr.GetCodeChangeDetailGitlabMr(ctx, contribution.CodeChangeId).Get()
	if err != nil {
		return gresult.Err[*CodeChange](err)
	}
	return gresult.OK(&CodeChange{Contribution: contribution, GitlabMr: mr})
}

func (c *CodeChange) GetBizConfig(ctx context.Context) gresult.R[*biz_config.ChangeBizConfig] {
	return biz_config.NewChangeBizConfigHandler(c.Contribution.BizConfig).GetConfig(ctx)
}

func (c *CodeChange) CleanMergeTaskConfig(ctx context.Context) error {
	handler := biz_config.NewChangeBizConfigHandler(c.Contribution.BizConfig)
	bizConfig, err := handler.GetConfig(ctx).Get()
	if err != nil {
		gresult.Err[*biz_config.ChangeBizConfig](err)
	}
	bizConfig.MergeTask = nil
	bizConfig.MergeTaskBizId = 0
	bizConfig.AutoMergeEnabled = false
	return handler.SaveByConfig(ctx, c.Contribution.Id, bizConfig)
}

func (c *CodeChange) IfConflicted(ctx context.Context) gresult.R[bool] {
	resp, err := gitsvr.GetConflictFilesByGitlabIid(ctx, c.GitlabMr.ProjectID, c.GitlabMr.Iid, false).Get()
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return gresult.Err[bool](err)
	}
	return gresult.OK(resp.HasConflicts)
}

func (c *CodeChange) GetLatestCommitID(ctx context.Context) gresult.R[string] {
	latestCommitId, err := gitsvr.GetLatestCommitId(ctx, c.GitlabMr.ProjectID, c.GitlabMr.GetSourceBranch()).Get()
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return gresult.Err[string](err)
	}
	return gresult.OK(latestCommitId)
}

func (c *CodeChange) GetLatestCommitChecked(ctx context.Context) gresult.R[string] {
	bizConfig, err := c.GetBizConfig(ctx).Get()
	if err != nil {
		return gresult.Err[string](err)
	}
	return gresult.OK(bizConfig.LatestCommitChecked)
}

func (c *CodeChange) IfCommitIsLatestCommitChecked(ctx context.Context, commitId string) gresult.R[bool] {
	bizConfig, err := c.GetBizConfig(ctx).Get()
	if err != nil {
		return gresult.Err[bool](err)
	}
	if len(bizConfig.LatestCommitChecked) == 0 {
		log.V2.Warn().With(ctx).Str("get latest version checked null").Emit()
		return gresult.Err[bool](errors.New("get latest version checked null"))
	}
	if commitId != bizConfig.LatestCommitChecked {
		log.V2.Warn().With(ctx).Str("latest commit not checked").KVs("change", c, "last commit id", commitId).Emit()
		return gresult.OK(false)
	}
	return gresult.OK(true)
}

func (c *CodeChange) UpdateLatestCommitChecked(ctx context.Context, latestCommitID string) error {
	if len(latestCommitID) == 0 {
		return errors.New("latestCommitID empty")
	}
	return biz_config.NewChangeBizConfigHandler(c.Contribution.BizConfig).UpdateAndSave(ctx, c.Contribution.Id, &biz_config.ChangeBizConfigUpdateOptions{LatestCommitChecked: gptr.Of(latestCommitID)})
}

func (c *CodeChange) GetCheckResByBizConfig(ctx context.Context) gresult.R[dev.CheckItemStatus] {
	bizConfig, err := c.GetBizConfig(ctx).Get()
	if err != nil {
		return gresult.Err[dev.CheckItemStatus](err)
	}
	answer := dev.CheckItemStatus_succeeded
	for _, item := range bizConfig.Checks {
		if item.Status == dev.CheckItemStatus_failed.String() {
			answer = dev.CheckItemStatus_failed
			break
		} else if item.Status == dev.CheckItemStatus_running.String() {
			answer = dev.CheckItemStatus_running
		}
	}
	return gresult.OK(answer)
}

func (c *CodeChange) IsNotOpened() bool {
	return c.Contribution.Status != dev.ContributionCodeChangeStatus_opened.String()
}

func (c *CodeChange) GetIfCanFastForward(ctx context.Context, useCache bool) gresult.R[bool] {
	redisKey := dcache.GetChangeCanFastForwardKey(c.Contribution.Id)
	if useCache {
		cacheRes, _ := redis.GetCacheVal[bool](ctx, redisKey)
		if cacheRes != nil {
			return gresult.OK(gptr.Indirect(cacheRes))
		}
	}
	ok, err := gitsvr.CheckIfTwoBranchesAreLinearNoCache(ctx, c.GitlabMr.ProjectID, c.GitlabMr.TargetBranch, c.GitlabMr.SourceBranch, c.Contribution.Author).Get()
	if err != nil {
		log.V2.Info().With(ctx).Str("check change can fast forward failed").KVs("change", c, "error", err).Emit()
		return gresult.Err[bool](err)
	}
	_ = redis.SetCacheVal[bool](ctx, redisKey, &ok, time.Minute*5)
	return gresult.OK(ok)
}

func (c *CodeChange) RefreshCacheIfCanFastForward(ctx context.Context) error {
	redisKey := dcache.GetChangeCanFastForwardKey(c.Contribution.Id)
	return redis.DeleteCacheVal(ctx, redisKey)
}

func (c *CodeChange) RefreshConflictCache(ctx context.Context) error {
	redisKey := dcache.GetChangeIfPassConflictDetect(c.Contribution.Id)
	return redis.DeleteCacheVal(ctx, redisKey)
}

func (d *CodeChange) GetMergeTask(ctx context.Context) gresult.R[*value_object.ChangeMergeTask] {
	bizConfig, err := d.GetBizConfig(ctx).Get()
	if err != nil {
		log.V2.Error().With(ctx).Error(err).Emit()
		return gresult.Err[*value_object.ChangeMergeTask](err)
	}
	if bizConfig.MergeTask != nil {
		return gresult.OK(bizConfig.MergeTask)
	}
	if bizConfig.MergeTaskBizId > 0 || bizConfig.AutoMergeEnabled {
		return gresult.OK(&value_object.ChangeMergeTask{
			BizId:   bizConfig.MergeTaskBizId,
			BizType: bizConfig.MergeTaskType,
		})
	}
	return gresult.Err[*value_object.ChangeMergeTask](derr.MergeTaskNotFoundError)
}

func (c *CodeChange) GetIfReviewPass(ctx context.Context, useCache bool) gresult.R[bool] {
	// spaceId
	var spaceId int64 = 0
	basic, _ := data.OptimusDB.Slave.GetDevBasicInfoByID(ctx, c.Contribution.DevBasicId)
	if basic != nil {
		spaceId = basic.SpaceId
	}
	ok, err := code_review.GetIfReviewPass(ctx, c.GitlabMr.GetCodebaseRepoID(), c.GitlabMr.GetCodebaseChangeID(), spaceId).Get()
	if err != nil {
		return gresult.Err[bool](err)
	}
	return gresult.OK(ok)
}

func (c *CodeChange) GetReviewInfo(ctx context.Context) gresult.R[*dev.ReviewInfo] {
	// spaceId
	var spaceId int64 = 0
	basic, _ := data.OptimusDB.Slave.UseCache().GetDevBasicInfoByID(ctx, c.Contribution.DevBasicId)
	if basic != nil {
		spaceId = basic.SpaceId
	}
	getter := choose.If(tcc.IsUsingNextCodeApi(ctx, spaceId), code_review.GetReviewInfoV3, code_review.GetReviewInfoV2)
	info, err := getter(ctx, c.GitlabMr.GetCodebaseRepoID(), c.GitlabMr.GetCodebaseChangeID(), c.Contribution.Author).Get()
	if err != nil {
		return gresult.Err[*dev.ReviewInfo](err)
	}
	return gresult.OK(info)
}

func (c *CodeChange) GetConflictInfo(ctx context.Context, trunkBranch *string) gresult.R[*value_object.ConflictInfo] {
	answer := &value_object.ConflictInfo{}
	// return not conflicted if not opened
	if c.IsNotOpened() {
		return gresult.OK(answer)
	}
	if trunkBranch != nil {
		conflictInfo, err := gitsvr.GetConflictFilesBranch(ctx, c.GitlabMr.GetSourceBranch(), gptr.Indirect(trunkBranch), c.GitlabMr.GetProjectID(), false).Get()
		if err != nil {
			return gresult.Err[*value_object.ConflictInfo](err)
		}
		answer.HasConflicts = conflictInfo.GetHasConflicts()
		answer.ConflictFiles = conflictInfo.GetConflictFiles()
	} else {
		conflictInfo, err := gitsvr.GetConflictFilesByGitlabIid(ctx, c.GitlabMr.GetProjectID(), c.GitlabMr.GetIid(), false).Get()
		if err != nil {
			return gresult.Err[*value_object.ConflictInfo](err)
		}
		answer.HasConflicts = conflictInfo.GetHasConflicts()
		answer.ConflictFiles = conflictInfo.GetConflictFiles()
	}
	return gresult.OK(answer)
}

func (c *CodeChange) Close(ctx context.Context) error {
	// close code change
	err := gitsvr.CloseCodeChange(ctx, c.Contribution.CodeChangeId)
	if err != nil {
		return err
	}
	// trans ctb to closed
	err = data.OptimusDB.Master.UpdateContributionCodeChangeByID(
		ctx,
		&model.ContributionCodeChange{
			Status: string(consts.ContributionStateClosed),
		},
		c.Contribution.Id,
	)
	if err != nil {
		return err
	}
	return nil
}
