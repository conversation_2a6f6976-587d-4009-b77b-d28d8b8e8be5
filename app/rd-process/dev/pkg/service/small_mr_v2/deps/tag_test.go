package deps

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/service/rpc"
)

func TestSyncDevTagToOptimusMrAutoGen(t *testing.T) {
	// OptimusClientGlobal != nil
	t.Run("case_0", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var bindMrTagResponseMockPtrValue optimus.BindMrTagResponse
			bindMrTagResponseMock := &bindMrTagResponseMockPtrValue
			var bindMrTagRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "BindMrTag"), mockey.OptUnsafe).Return(bindMrTagResponseMock, bindMrTagRet2Mock).Build()

			var getDevTagsResponseMockPtrValue optimus.GetDevTagsResponse
			getDevTagsResponseMock := &getDevTagsResponseMockPtrValue
			var getDevTagsRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetDevTags"), mockey.OptUnsafe).Return(getDevTagsResponseMock, getDevTagsRet2Mock).Build()

			// prepare parameters
			var optimusMrID int64
			ctx := context.Background()
			var DevID int64

			// run target function and assert
			got1 := SyncDevTagToOptimusMr(ctx, DevID, optimusMrID)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// OptimusClientGlobal != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:GetDevTags()_ret-2 != nil
	t.Run("case_1", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var bindMrTagResponseMockPtrValue optimus.BindMrTagResponse
			bindMrTagResponseMock := &bindMrTagResponseMockPtrValue
			var bindMrTagRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "BindMrTag"), mockey.OptUnsafe).Return(bindMrTagResponseMock, bindMrTagRet2Mock).Build()

			var getDevTagsResponseMockPtrValue optimus.GetDevTagsResponse
			getDevTagsResponseMock := &getDevTagsResponseMockPtrValue
			getDevTagsRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetDevTags"), mockey.OptUnsafe).Return(getDevTagsResponseMock, getDevTagsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var DevID int64
			var optimusMrID int64

			// run target function and assert
			got1 := SyncDevTagToOptimusMr(ctx, DevID, optimusMrID)
			convey.So(got1 == nil, convey.ShouldBeFalse)
		})
	})

	// OptimusClientGlobal != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:GetDevTags()_ret-2 == nil
	// len(code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:GetDevTags()_ret-1.DevTags) > 0
	t.Run("case_2", func(t *testing.T) {
		mockey.PatchConvey("case_2", t, func() {
			// mock function returns or global values
			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var bindMrTagResponseMockPtrValue optimus.BindMrTagResponse
			bindMrTagResponseMock := &bindMrTagResponseMockPtrValue
			var bindMrTagRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "BindMrTag"), mockey.OptUnsafe).Return(bindMrTagResponseMock, bindMrTagRet2Mock).Build()

			var getDevTagsResponseMockPtrValue optimus.GetDevTagsResponse
			getDevTagsResponseMock := &getDevTagsResponseMockPtrValue
			var getDevTagsResponseMockDevTagsItem0PtrValue optimus.DevTag
			getDevTagsResponseMockDevTagsItem0 := &getDevTagsResponseMockDevTagsItem0PtrValue
			getDevTagsResponseMock.DevTags = []*optimus.DevTag{getDevTagsResponseMockDevTagsItem0,}
			var getDevTagsRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetDevTags"), mockey.OptUnsafe).Return(getDevTagsResponseMock, getDevTagsRet2Mock).Build()

			// prepare parameters
			var DevID int64
			var optimusMrID int64
			ctx := context.Background()

			// run target function and assert
			got1 := SyncDevTagToOptimusMr(ctx, DevID, optimusMrID)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

	// OptimusClientGlobal != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:GetDevTags()_ret-2 == nil
	// len(code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:GetDevTags()_ret-1.DevTags) > 0
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:GetDevTags()_ret-1.DevTags[0] != nil
	// code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/optimus/optimusservice.Client:BindMrTag()_ret-2 != nil
	t.Run("case_3", func(t *testing.T) {
		mockey.PatchConvey("case_3", t, func() {
			// mock function returns or global values
			var optimusClientMockImpl optimusserviceClientImplForTestAutoGen
			optimusClientMock := &optimusClientMockImpl
			mockey.MockValue(&rpc.OptimusClient).To(optimusClientMock)

			var bindMrTagResponseMockPtrValue optimus.BindMrTagResponse
			bindMrTagResponseMock := &bindMrTagResponseMockPtrValue
			bindMrTagRet2Mock := fmt.Errorf("error")
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "BindMrTag"), mockey.OptUnsafe).Return(bindMrTagResponseMock, bindMrTagRet2Mock).Build()

			var getDevTagsResponseMockPtrValue optimus.GetDevTagsResponse
			getDevTagsResponseMock := &getDevTagsResponseMockPtrValue
			getDevTagsResponseMock.DevTags = []*optimus.DevTag{}
			var getDevTagsResponseMockDevTags0ItemPtrValue optimus.DevTag
			getDevTagsResponseMockDevTags0Item := &getDevTagsResponseMockDevTags0ItemPtrValue
			getDevTagsResponseMock.DevTags = append(getDevTagsResponseMock.DevTags, getDevTagsResponseMockDevTags0Item)
			var getDevTagsRet2Mock error
			mockey.Mock(mockey.GetMethod(rpc.OptimusClient, "GetDevTags"), mockey.OptUnsafe).Return(getDevTagsResponseMock, getDevTagsRet2Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var DevID int64
			var optimusMrID int64

			// run target function and assert
			got1 := SyncDevTagToOptimusMr(ctx, DevID, optimusMrID)
			convey.So(got1 == nil, convey.ShouldBeTrue)
		})
	})

}

