package devtask

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rd-process/dev/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/data"
	"code.byted.org/devinfra/hagrid/app/rd-process/dev/pkg/dal/mysql/model"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gresult"
	"code.byted.org/lang/gg/gslice"
)

func QueryDevTasks(ctx context.Context, req *dev.QueryDevTasksRequest) gresult.R[*dev.QueryDevTasksResponse] {
	err := verifyQueryDevTasksRequest(req)
	if err != nil {
		return gresult.Err[*dev.QueryDevTasksResponse](err)
	}
	codeChangeIDs := gslice.Filter(req.GetCodeChangeIDs(), func(id int64) bool {
		return id != 0
	})
	changes, err := data.OptimusDB.Slave.BatchGetDraftDevBasicChangesByBizID(ctx, codeChangeIDs).Get()
	if err != nil {
		return gresult.Err[*dev.QueryDevTasksResponse](err)
	}
	tasks := gslice.Map(changes, func(c *model.DevBasicChange) *dev.QueryDevTaskItem {
		return &dev.QueryDevTaskItem{
			CodeChangeID: c.BizId,
			DevTaskID:    c.DevBasicId,
		}
	})
	response := &dev.QueryDevTasksResponse{
		Tasks: tasks,
	}
	return gresult.OK[*dev.QueryDevTasksResponse](response)
}

func GetDevTaskMetaInfoByIds(ctx context.Context, req *dev.GetDevTaskMetaInfoByIdsRequest) gresult.R[*dev.GetDevTaskMetaInfoByIdsResponse] {
	var resp = dev.NewGetDevTaskMetaInfoByIdsResponse()
	if len(req.DevBasicIds) == 0 {
		return gresult.OK(resp)
	}

	devBasics, err := data.OptimusDB.Slave.BatchGetDevBasicInfoByIds(ctx, req.GetDevBasicIds()).Get()
	if err != nil {
		return gresult.Err[*dev.GetDevTaskMetaInfoByIdsResponse](err)
	}
	resp.MetaInfos = gslice.Map(devBasics, modelDevTaskInfoToIdlDevTaskMetaInfo)
	return gresult.OK(resp)
}

func BatchGetDevTaskMember(ctx context.Context, req *dev.BatchGetDevTaskMemberRequest) gresult.R[*dev.BatchGetDevTaskMemberResponse] {
	var resp = dev.NewBatchGetDevTaskMemberResponse()
	if len(req.GetDevBasicIds()) == 0 {
		return gresult.OK(resp)
	}

	devBasics, err := data.OptimusDB.Slave.BatchGetDevBasicInfoByIds(ctx, req.GetDevBasicIds()).Get()
	if err != nil {
		return gresult.Err[*dev.BatchGetDevTaskMemberResponse](err)
	}
	devBasicId2DevIdM := gslice.ToMap(devBasics, func(t *model.DevBasicInfo) (int64, int64) { return t.Id, t.DevId })
	collaborators, err := data.OptimusDB.Slave.BatchGetCollaboratorsByDevId(ctx, gmap.Values(devBasicId2DevIdM)).Get()
	if err != nil {
		return gresult.Err[*dev.BatchGetDevTaskMemberResponse](err)
	}
	var devIdCollaboratorM = make(map[int64][]*dev.DevTaskMember)
	for _, item := range collaborators {
		existed := gmap.Load(devIdCollaboratorM, item.DevId).ValueOr([]*dev.DevTaskMember{})
		devIdCollaboratorM[item.DevId] = append(existed, modelCollaboratorToIdlMember(item))
	}

	resp.Members = gslice.ToMap(req.GetDevBasicIds(), func(devBasicId int64) (int64, []*dev.DevTaskMember) { return devBasicId, make([]*dev.DevTaskMember, 0) })
	for _, devBasicId := range req.GetDevBasicIds() {
		if devId, ok := devBasicId2DevIdM[devBasicId]; ok {
			if member, has := devIdCollaboratorM[devId]; has {
				resp.Members[devBasicId] = gslice.UniqBy(append(resp.Members[devBasicId], member...), func(t *dev.DevTaskMember) string {
					return fmt.Sprintf("%v_%v", t.GetRole(), t.GetEmail())
				})
			}
		}
	}
	return gresult.OK(resp)
}
