package devtask_package

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/kitex_gen/bytedance/bits/mr_package"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/db"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/rpc/devtask"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"strconv"
	"strings"

	"context"
	json "github.com/bytedance/sonic"
)

type DevBuildEnvParams struct {
	OpenChanges  string `json:"CUSTOM_CI_BUILD_OPENED_STACK_TOP_CHANGES"`
	BuildType    string `json:"CUSTOM_CI_BUILD_ALL_CHANGE"`
	TargetBranch string `json:"CUSTOM_CI_BUILD_ALL_CHANGE_TARGET_BRANCH"`
	BaseCommit   string `json:"CUSTOM_CI_BUILD_BASE_BRANCH_COMMIT"`
}

type GroupHelper struct {
	*db.OptimusPackageGroup
	DevBuildEnvParams *DevBuildEnvParams
	DevDetail         *devtask.DevTaskDetail
}

func NewGroupHelper(group *db.OptimusPackageGroup, devDetail *devtask.DevTaskDetail) *GroupHelper {
	return &GroupHelper{
		OptimusPackageGroup: group,
		DevDetail:           devDetail,
	}
}

func (h *GroupHelper) IsBuildPackage() bool {
	return h.HasPackage || h.ActionType == int64(mr_package.PackageActionType_BuildPackage)
}

func (h *GroupHelper) IsDevTaskPackage() bool {
	return h.GetDevBuildEnvParams().TargetBranch != ""
}

func (h *GroupHelper) GetDevBuildEnvParams() DevBuildEnvParams {
	if h.DevBuildEnvParams != nil {
		return *h.DevBuildEnvParams
	}
	var params DevBuildEnvParams
	_ = json.Unmarshal([]byte(h.CustomParams), &params)
	h.DevBuildEnvParams = &params
	return params
}

func (h *GroupHelper) ToPackageGroup() *mr_package.PackageGroup {
	r := &mr_package.PackageGroup{
		Id:         h.Id,
		ActionType: &h.ActionType,
		ActionId:   &h.ActionId,
		ActionUrl:  &h.ActionUrl,
		CommitId:   &h.CommitId,
	}

	if h.CreateTime != nil {
		createTime := h.CreateTime.Unix()
		r.CreateTime = &createTime
	}

	var customParamsMap map[string]string
	_ = json.Unmarshal([]byte(h.CustomParams), &customParamsMap)

	customParams := make([]*mr_package.PackageParams, 0)
	for key, value := range customParamsMap {
		customParams = append(customParams, &mr_package.PackageParams{
			Key:   key,
			Value: value,
		})
	}
	r.CustomParams = customParams
	return r
}

func (h *GroupHelper) GetCommits(ctx context.Context) []*mr_package.PackageProjectCommits {
	projectID, _ := strconv.ParseInt(h.ProjectId, 10, 64)
	ref, err := rpc.GitServer.CompareRef(ctx, &git_server.CompareRefRequest{
		ProjectId: int32(projectID),
		From:      h.GetDevBuildEnvParams().BaseCommit,
		To:        h.GetDevBuildEnvParams().TargetBranch,
	})
	if err != nil || ref == nil || ref.GetCompare() == nil {
		return nil
	}
	compare := ref.GetCompare()
	if compare == nil {
		compare = new(git_server.Compare)
	}
	commit := compare.GetCommit()
	if commit == nil {
		commit = new(git_server.Commit)
	}
	_, path := splitRepoPath(h.DevDetail.Info.GetRepoPath())
	commits := gslice.Map(
		gslice.ReverseClone(compare.GetCommits()),
		func(commit *git_server.Commit) *mr_package.PackageCommit {
			return &mr_package.PackageCommit{
				Author:      commit.GetAuthorName(),
				Title:       commit.GetTitle(),
				CommitId:    commit.GetId(),
				Timestamp:   commit.GetCommittedDate(),
				ShortId:     gptr.Of(commit.GetShortId()),
				AuthorEmail: gptr.Of(commit.GetAuthorEmail()),
			}
		},
	)
	return []*mr_package.PackageProjectCommits{
		{
			ProjectName: path,
			ProjectId:   h.ProjectId,
			CommitId:    commit.GetShortId(),
			Timestamp:   commit.GetCommittedDate(),
			Commits:     commits,
		},
	}
}

func splitRepoPath(repoPath string) (string, string) {
	path := strings.Split(repoPath, "/")
	return path[0], path[1]
}
