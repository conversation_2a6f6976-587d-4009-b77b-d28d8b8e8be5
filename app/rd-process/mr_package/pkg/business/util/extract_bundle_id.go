package util

import (
	"context"
	"strings"

	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/utils"
	"code.byted.org/gopkg/logs"
)

const (
	extractTemplateId = 18286
	extractPluginId   = 35949
)

func TriggerExtractBundleIdJob(ctx context.Context, url string) {
	if !strings.HasSuffix(url, ".ipa") {
		return
	}
	resp, err := rpc.Workflow.TriggerTemplateJob(ctx, &workflow.TriggerTemplateJobRequest{
		Params: &workflow.TemplateConfigParams{
			Id: int64(extractTemplateId),
			BuildParams: []*workflow.TemplateConfigPluginParams{
				{
					Id: int64(extractPluginId),
					Inputs: []*workflow.TemplateConfigParam{
						{
							Name:  "DOWNLOAD_URL",
							Value: url,
						},
					},
				},
			},
		},
		Operator: "xiangzilin.wy",
	})
	logs.CtxInfo(ctx, "trigger bundle_id extract job, resp: %s, err: %v", utils.ToJson(resp), err)
}
