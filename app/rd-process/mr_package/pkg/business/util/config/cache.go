package config

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/db"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/localcache"
	"context"
	"fmt"
	"strconv"
	"time"
)

var configCache localcache.SimpleCache

func InitCache() {
	cache, err := localcache.NewSimpleCache(5000, 10*time.Minute, localcache.LRU, localcache.WithName("config_cache"))
	if err != nil {
		panic(err)
	}
	configCache = cache
}

func GetProjectConfig(ctx context.Context, projectId string) (*db.OptimusConfigProject, error) {
	logId, _ := ctxvalues.LogID(ctx)
	key := fmt.Sprintf("%s:p:%s", logId, projectId) // cache single request, use log id as a key

	config, err := configCache.Get(ctx, key)
	if config != nil {
		if val, ok := config.(*db.OptimusConfigProject); ok {
			return val, nil
		}
	}

	projectConfig, err := db.OptimusRead.GetProjectConfigForProjectId(ctx, projectId)
	if err != nil {
		return nil, err
	}
	_ = configCache.Set(ctx, key, projectConfig)
	return projectConfig, nil
}

func GetGroupConfig(ctx context.Context, projectId string) (*db.OptimusConfigGroup, error) {
	projectConfig, err := GetProjectConfig(ctx, projectId)
	if projectConfig == nil {
		return nil, err
	}
	logId, _ := ctxvalues.LogID(ctx)
	key := fmt.Sprintf("%s:g:%s", logId, strconv.FormatInt(projectConfig.MainGroupConfigId, 10))
	config, _ := configCache.Get(ctx, key)
	if config != nil {
		if val, ok := config.(*db.OptimusConfigGroup); ok {
			return val, nil
		}
	}
	groupConfig, err := db.OptimusRead.GetGroupConfigForGroupId(ctx, projectConfig.MainGroupConfigId)
	if err != nil {
		return nil, err
	}
	_ = configCache.Set(ctx, key, groupConfig)
	return groupConfig, nil
}
