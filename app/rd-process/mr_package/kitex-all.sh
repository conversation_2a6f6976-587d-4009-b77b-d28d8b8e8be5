#! /usr/bin/env bash

kitex --disable-self-update -module code.byted.org/devinfra/hagrid -service bytedance.bits.mr_package ../../../idls/app/mr_package.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/infra/git_server/service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/app/devops_settings.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/app/optimus.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/app/pipeline/workflow.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/infra/meta/service.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/app/message_center.thrift 
kitex --disable-self-update -module code.byted.org/devinfra/hagrid ../../../idls/app/rd-process/dev/dev.thrift 