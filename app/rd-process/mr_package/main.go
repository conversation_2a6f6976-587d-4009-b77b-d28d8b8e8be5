package main

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/kitex_gen/bytedance/bits/mr_package/mrpackageservice"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/api"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/business"
	config2 "code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/business/util/config"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/conf"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/db"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/mq"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/mq/webhook"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/redis"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/mr_package/pkg/tos"
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/kite/kitex/server"
)

func main() {
	InitializeLogger()
	conf.Init()
	db.Init()
	tos.Init()
	mq.Init()
	redis.Init()
	rpc.Init()
	config2.InitCache()
	business.Init()
	api.Init()

	options := []server.Option{
		server.WithMiddleware(kitexmw.WarpError),
		server.WithMiddleware(kitexmw.LogRequestResponse),
	}
	svr := mrpackageservice.NewServer(new(MRPackageServiceImpl), options...)
	if err := svr.Run(); err != nil {
		log.V2.Fatal().Str("failed to run server").Error(err).Emit()
	}
	webhook.Close() // assure message can be sent async
}

func InitializeLogger() {
	log.SetDefaultLogger(
		logs.SetPSM(env.PSM()),
		logs.SetCallDepth(2),
		logs.SetFullPath(),
		logs.SetKVPosition(logs.AfterMsg),
		logs.SetDisplayEnvInfo(true),
		logs.SetDisplayFuncName(true),
	)
}
