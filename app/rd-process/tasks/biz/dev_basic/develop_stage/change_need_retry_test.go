package develop_stage

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/dev_basic/base"
)

func TestChangeNeedRetryTask_ResetAutoGen(t *testing.T) {
	// Verify the functionality of the Reset method under default conditions.
	t.Run("testChangeNeedRetryTask_Reset", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var reqPtrValue base.TaskRequest
			req := &reqPtrValue
			var receiverPtrValue ChangeNeedRetryTask
			receiver := &receiverPtrValue
			ctx := context.Background()

			// run target function and assert
			got1, got2, got3 := receiver.Reset(ctx, req)
			convey.So(got1, convey.ShouldEqual, true)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestChangeNeedRetryTask_StartAutoGen(t *testing.T) {
	// Verify the default behavior of the Start function in ChangeNeedRetryTask.
	t.Run("testChangeNeedRetryTask_StartDefault", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue ChangeNeedRetryTask
			receiver := &receiverPtrValue
			ctx := context.Background()
			var reqPtrValue base.TaskRequest
			req := &reqPtrValue

			// run target function and assert
			got1, got2, got3 := receiver.Start(ctx, req)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestChangeNeedRetryTask_CloseAutoGen(t *testing.T) {
	// Verify the normal operation of the Close function under default conditions.
	t.Run("testChangeNeedRetryTask_Close", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// prepare parameters
			var receiverPtrValue ChangeNeedRetryTask
			receiver := &receiverPtrValue
			ctx := context.Background()
			var reqPtrValue base.TaskRequest
			req := &reqPtrValue

			// run target function and assert
			got1, got2, got3 := receiver.Close(ctx, req)
			convey.So(got1, convey.ShouldEqual, true)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

func TestChangeNeedRetryTask_PingAutoGen(t *testing.T) {
	// Verify the default behavior of the Ping function in ChangeNeedRetryTask.
	t.Run("testChangeNeedRetryTask_Ping", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var needRetryTaskPingRet1Mock bool
			var needRetryTaskPingRet2Mock []byte
			var needRetryTaskPingRet3Mock error
			mockey.Mock((*base.NeedRetryTask).NeedRetryTaskPing).Return(needRetryTaskPingRet1Mock, needRetryTaskPingRet2Mock, needRetryTaskPingRet3Mock).Build()

			// prepare parameters
			var receiverPtrValue ChangeNeedRetryTask
			receiver := &receiverPtrValue
			ctx := context.Background()
			var reqPtrValue base.TaskRequest
			req := &reqPtrValue

			// run target function and assert
			got1, got2, got3 := receiver.Ping(ctx, req)
			convey.So(got1, convey.ShouldEqual, false)
			convey.So(len(got2), convey.ShouldEqual, 0)
			convey.So(got3 == nil, convey.ShouldBeTrue)
		})
	})

}

