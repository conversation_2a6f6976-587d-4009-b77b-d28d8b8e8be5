package model

import (
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/dev"
	"code.byted.org/devinfra/hagrid/libs/sdk/ci_pipelinemodule_sdk"
)

type CreatePipelineResult struct {
	Reuse            bool                      `json:"reuse"`
	TemplateID       uint64                    `json:"template_id"`
	TemplateVersion  uint64                    `json:"template_version"`
	TemplateUpdateAt string                    `json:"template_update_at"`
	PipelineID       uint64                    `json:"pipeline_id"`
	ProjectPipeline  []*ProjectPipeline        `json:"project_pipeline"`
	TTPChangeItems   []*dev.PipelineChangeItem `json:"ttp_change_items"`
}

type ProjectPipeline struct {
	ID                       int64                                `json:"id"`
	ControlPlane             dev.ControlPlane                     `json:"control_plane"`
	PipelineTemplateID       uint64                               `json:"pipeline_template_id"`
	PipelineTemplateVersion  uint64                               `json:"pipeline_template_version"`
	PipelineTemplateUpdateAt string                               `json:"pipeline_template_update_at"`
	PipelineID               uint64                               `json:"pipeline_id"`
	ProjectUniqueID          string                               `json:"project_unique_id"`
	ProjectType              dev.ProjectType                      `json:"project_type"`
	ProjectID                int64                                `json:"project_id"`
	PSM                      string                               `json:"psm"`
	ProjectName              string                               `json:"project_name"`
	DeployDependencies       []*dev.DeployProjectInfo             `json:"deploy_dependencies"`
	TargetBranch             string                               `json:"target_branch"`
	IsMainPipeline           bool                                 `json:"is_main_pipeline"`
	Key                      ci_pipelinemodule_sdk.AtomServiceKey `json:"key"`
	AssignmentIds            []uint64                             `json:"assignment_ids"`
	DeployConfig             *dev.DevChangeDeployConfig           `json:"deploy_config"`
}

type CustomDeployTaskConfig struct {
	Enabled      bool                 `json:"enabled"`
	TaskName     string               `json:"task_name"`
	StartURL     string               `json:"start_url"`
	StartHeaders map[string]string    `json:"start_headers"`
	StartMesh    CustomDeployTaskMesh `json:"start_mesh"`
	PingURL      string               `json:"ping_url"`
	PingHeaders  map[string]string    `json:"ping_headers"`
	PingMesh     CustomDeployTaskMesh `json:"ping_mesh"`
	ResetURL     string               `json:"reset_url"`
	ResetHeaders map[string]string    `json:"reset_headers"`
	ResetMesh    CustomDeployTaskMesh `json:"reset_mesh"`
	CloseURL     string               `json:"close_url"`
	CloseHeaders map[string]string    `json:"close_headers"`
	CloseMesh    CustomDeployTaskMesh `json:"close_mesh"`
	TestingEnvs  []string             `json:"testing_envs"`
}

type CustomDeployTaskMesh struct {
	Enabled  bool   `json:"enabled"`
	IDC      string `json:"idc"`
	Protocol string `json:"protocol"`
	Domain   string `json:"domain"`
	URL      string `json:"url"`
}
