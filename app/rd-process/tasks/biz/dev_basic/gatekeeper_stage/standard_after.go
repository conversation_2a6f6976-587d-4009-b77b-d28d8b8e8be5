package gatekeeper_stage

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/dev_basic/base"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bits/gatekeeper/process"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bits/workflow/enums"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/rd_process/tasks"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/model"
)

type StandardAfterTask struct {
	base.ServiceTask
}

func (t *StandardAfterTask) Start(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	if req.UniqueType == base.UniqueType_DEV_TASK_ADVANCED {
		task := &StandardAfterTaskAdvanced{}
		return task.Start(ctx, req)
	}

	//
	extra := &model.ErrTraceInfo{
		DevBasicID: req.UniqueID,
	}

	//
	skip, skipInfo, _ := dal_mysql.ShouldSkipDevBasicTask(ctx, req.UniqueID, req.TaskName)
	if skip {
		_ = dal_mysql.SkipDevBasicTask(ctx, skipInfo.Id)
		_ = rpc.TransitionTaskState(ctx, req.UniqueID, req.TaskName, enums.ExecutionTaskState_SKIPPED, map[string]interface{}{"retry": false, "failed": false}, extra)
		return true, nil, nil
	}

	_ = rpc.ForceTerminateCheckPoint(ctx, req.UniqueID, process.BizScene_BizSceneChangeAccess, tasks.DevGatekeeperStage, extra)

	return true, nil, nil
}

func (t *StandardAfterTask) Reset(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	if req.UniqueType == base.UniqueType_DEV_TASK_ADVANCED {
		task := &StandardAfterTaskAdvanced{}
		return task.Reset(ctx, req)
	}

	return true, nil, nil
}

func (t *StandardAfterTask) Close(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	if req.UniqueType == base.UniqueType_DEV_TASK_ADVANCED {
		task := &StandardAfterTaskAdvanced{}
		return task.Close(ctx, req)
	}

	return true, nil, nil
}

func (t *StandardAfterTask) Ping(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	if req.UniqueType == base.UniqueType_DEV_TASK_ADVANCED {
		task := &StandardAfterTaskAdvanced{}
		return task.Ping(ctx, req)
	}

	return true, nil, nil
}
