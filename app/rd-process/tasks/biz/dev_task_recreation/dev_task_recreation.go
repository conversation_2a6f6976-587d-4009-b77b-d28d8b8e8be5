package dev_task_recreation

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/dev_task_recreation/base"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/model"
)

type DevTaskRecreationTask struct {
	base.ServiceTask
}

func (t *DevTaskRecreationTask) Start(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: req.UniqueID,
	}

	end, _ := rpc.GetRecreationTaskResult(ctx, req.UniqueID, extra)
	return end, nil, nil
}

func (t *DevTaskRecreationTask) Reset(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	return true, nil, nil
}

func (t *DevTaskRecreationTask) Close(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	return true, nil, nil
}

func (t *DevTaskRecreationTask) Ping(ctx context.Context, req *base.TaskRequest) (bool, []byte, error) {
	//
	extra := &model.ErrTraceInfo{
		DevBasicID: req.UniqueID,
	}

	end, _ := rpc.GetRecreationTaskResult(ctx, req.UniqueID, extra)
	return end, nil, nil
}
