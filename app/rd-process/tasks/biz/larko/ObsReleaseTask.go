package larko

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/biz/service/rpc"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/dal/dal_mysql"
	"code.byted.org/devinfra/hagrid/app/rd-process/tasks/kitex_gen/bytedance/bits/aosp"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/gopkg/logs"
)

type ObsReleaseTask struct {
}

func (g ObsReleaseTask) Start(ctx context.Context, paramsJson, contextJson string) (end bool, contextInter, outputInter interface{}, err error) {
	ctx = LarkoCtxEnv(ctx)

	params, err := GetParams(ctx, paramsJson)
	if err != nil {
		return
	}
	return triggerPipeline(ctx, params, aosp.GerritDevPipelineType_released)
}

func (g ObsReleaseTask) IsEnd(ctx context.Context, paramsJson, contextJson string) (end bool, contextInter, outputInter interface{}, err error) {
	ctx = LarkoCtxEnv(ctx)

	params, err := GetParams(ctx, paramsJson)
	if err != nil {
		return
	}

	pipelineId, err := dal_mysql.GerritDevOperator.ListGerritDevLatestPipeline(ctx, params.DevId, aosp.GerritDevPipelineType_released.String())
	if utils.MysqlNoRows(err) {
		err = nil
		return triggerPipeline(ctx, params, aosp.GerritDevPipelineType_released)
	} else if err != nil {
		logs.CtxWarn(ctx, err.Error())
		return
	}
	status, err := rpc.GetPipelineStatus(ctx, pipelineId, nil).Get()
	if err != nil {
		return
	}
	end = checkPipelineEnd(status)

	if err != nil {
		return
	}

	if end {
		AsyncWebsocketGerritGraph(ctx, params.DevId, time.Second*2)
	}
	return
}

func (g ObsReleaseTask) Reset(ctx context.Context, paramsJson, contextJson string) (contextInter, outputInter interface{}, err error) {
	return
}

func (g ObsReleaseTask) Close(ctx context.Context, paramsJson, contextJson string) (contextInter, outputInter interface{}, err error) {
	return
}
