package larko

import (
	"context"
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/bytedance/sonic"
	"github.com/smartystreets/goconvey/convey"
)

func TestGetParamsAutoGen(t *testing.T) {
	// Verify the behavior when UnmarshalString returns an error.
	t.Run("testGetParams_UnmarshalError", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			unmarshalStringRet1Mock := fmt.Errorf("error")
			mockey.Mock(sonic.UnmarshalString, mockey.OptUnsafe).Return(unmarshalStringRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var j string

			// run target function and assert
			got1, got2 := GetParams(ctx, j)
			convey.So(got1.DevId, convey.ShouldEqual, 0)
			convey.So(got1.AppId, convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

