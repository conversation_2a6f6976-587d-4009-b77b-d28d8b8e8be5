#!/bin/bash



OLD_PWD=$PWD
cd app/identity/api


RUN_NAME="bits.identity.api"

mkdir -p output/bin output/conf
cp script/* output/
chmod +x output/bootstrap.sh
find conf/ -type f ! -name "*_local.*" | xargs -I{} cp {} output/conf/

bash ${BUILD_PATH}/scripts/bazel_scm_preparation.sh
bazel build //app/identity/api:api
cp ${BUILD_PATH}/bazel-bin/app/identity/api/api_/${RUN_NAME} output/bin/${RUN_NAME}

# go build -v -o output/bin/${RUN_NAME}

if [[ $OLD_PWD != "" ]]; then
  cp -r output $OLD_PWD
  #bash ${BUILD_PATH}/scripts/post-build.scm.sh
fi

bash -c "$(curl -fsL https://tosv.byted.org/obj/uitesting/tos_upload_blame.sh)" || echo ""
