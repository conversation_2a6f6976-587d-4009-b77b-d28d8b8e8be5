package webhook

import (
	"code.byted.org/devinfra/hagrid/app/webhook/pkg/data"
	"context"
	json "github.com/bytedance/sonic"
	"github.com/go-resty/resty/v2"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"net/url"
	"regexp"
	"time"
)

var bytedanceHost = regexp.MustCompile(`((.*feishu.cn)|(.*byted.org)|(.*feishuapp.cn)|(.*bytedance.net)|(.*goofy.app)|(10\.([0-9]{1,3}){1,3}))`)
var blockHost = regexp.MustCompile(`(.*boe.byted.org)`)

func URLSecurityCheck(URL string) bool {
	requestURI, err := url.ParseRequestURI(URL)
	if err != nil {
		return false
	}
	if requestURI.Scheme != "http" && requestURI.Scheme != "https" {
		return false
	}
	if blockHost.MatchString(requestURI.Host) {
		return false
	}
	if !bytedanceHost.MatchString(requestURI.Host) {
		return false
	}
	return true
}

func Controller(ctx context.Context, _ string, content []byte) error {
	webhookInfo := &data.WebHookData{}
	if err := json.Unmarshal(content, webhookInfo); err != nil {
		utils.LogCtxError(ctx, "Unmarshal JSON failed: data=%s, error=%s", string(content), err.Error())
		return nil
	}

	if len(webhookInfo.URL) == 0 {
		utils.LogCtxError(ctx, "URL does not exit: data=%s", string(content))
		return nil
	}
	if !URLSecurityCheck(webhookInfo.URL) {
		utils.LogCtxWarn(ctx, "URL is illegal: data=%s, URL=%s", string(content), webhookInfo.URL)
		return nil
	}

	if res, err := resty.New().SetHeader("Content-Type", "application/json").SetTimeout(time.Second * 30).R().SetContext(ctx).SetBody(content).Post(webhookInfo.URL); err != nil {
		utils.LogCtxError(ctx, "User hook returns error response: record_id=%d, ULR=%s, err=%s", webhookInfo.RecordID, webhookInfo.URL, err.Error())
		return err
	} else {
		utils.LogCtxInfo(ctx, "User hook returns success response: record_id=%d, ULR=%s, body=%s, resp=%s", webhookInfo.RecordID, webhookInfo.URL, string(content), string(res.Body()))
	}
	return nil
}
