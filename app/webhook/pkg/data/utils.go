package data

import (
	"code.byted.org/devinfra/hagrid/app/webhook/service/cache"
	"code.byted.org/gopkg/logs"
	"fmt"
	"time"
)

type Cache struct {
	Expire      int64
	Key         interface{}
	NeedToCache bool
}

func (c *Cache) Mget(args ...string) ([]interface{}, error) {
	if len(args) == 0 {
		return []interface{}{}, nil
	}
	res := cache.Connection.Mbox.Client.MGet(args...)
	if res != nil && res.Err() != nil {
		logs.Error(res.Err().Error(), len(args))
	}
	return res.Result()
}
func (c *Cache) Mset(args ...interface{}) error {
	if res := cache.Connection.Mbox.Client.MSet(args...); res.Err() != nil {
		return res.Err()
	}
	return nil
}
func (c *Cache) Get(args ...interface{}) ([]byte, error) {
	var key string
	for _, value := range append(args, c.Key) {
		key += fmt.Sprintf("%v_", value)
	}
	cacheInfo := cache.Connection.Optimus.Client.Get(key)
	if len(cacheInfo.Val()) != 0 && cacheInfo.Val() != "null" {
		cacheBytes, _ := cacheInfo.Bytes()
		return cacheBytes, nil
	}
	return nil, nil
}

func (c *Cache) Set(data []byte, args ...interface{}) error {
	var key string
	for _, value := range append(args, c.Key) {
		key += fmt.Sprintf("%v_", value)
	}
	td, _ := time.ParseDuration(fmt.Sprintf("%ds", c.Expire))
	_ = cache.Connection.Optimus.Client.Set(key, string(data), td)
	return nil
}
