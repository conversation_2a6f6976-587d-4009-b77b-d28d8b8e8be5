package config

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"fmt"
	"github.com/spf13/viper"
	"os"
	"path/filepath"
)

type Redis struct {
	Psm      string   `mapstructure:"psm"`
	Host     string   `mapstructure:"host"`
	Port     int64    `mapstructure:"port"`
	Services []string `mapstructure:"services"`
}
type Cache struct {
	Optimus Redis `mapstructure:"optimus"`
}
type CommonDbClient struct {
	PSM      string `mapstructure:"psm"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	DB       string `mapstructure:"db"`
}
type MysqlItem struct {
	DB     string         `mapstructure:"db"`
	Prefix string         `mapstructure:"prefix"`
	Master CommonDbClient `mapstructure:"master"`
	Slave  CommonDbClient `mapstructure:"slave"`
}
type Mysql struct {
	Optimus *MysqlItem `mapstructure:"optimus"`
}
type MqItem struct {
	Name  string `mapstructure:"name"`
	Group string `mapstructure:"group"`
}
type MqConfig struct {
	BitsWebHook struct {
		Consumer MqItem `mapstructure:"consumer"`
		Producer MqItem `mapstructure:"producer"`
	} `mapstructure:"bits_webhook"`
	OptimusMergeRequestCore struct {
		Consumer MqItem `mapstructure:"consumer"`
		Producer MqItem `mapstructure:"producer"`
	} `mapstructure:"optimus_merge_request_core"`
	GitlabWebhook struct {
		Consumer MqItem `mapstructure:"consumer"`
	} `mapstructure:"gitlab_webhook"`
}

type AppConfig struct {
	Mq    MqConfig `mapstructure:"mq"`
	Mysql Mysql    `mapstructure:"mysql"`
	Cache Cache    `mapstructure:"cache"`
}

var App AppConfig

func Init(str string) {
	logs.Info("IDC %s IsProduct %t", env.IDC(), env.IsProduct())

	v := viper.New()
	configFile := "conf/local.json"

	if os.Getenv("RUNTIME_IDC_NAME") == "pre" {
		configFile = "conf/pre.json"
	} else if env.IsBoe() {
		configFile = "conf/boe.json"
	} else if env.IsProduct() {
		configFile = "conf/prod.json"
	}
	configFile = fmt.Sprintf("%s/%s", str, configFile)
	p, _ := filepath.Abs(os.Getenv("CONF_DIR") + configFile)
	v.SetConfigFile(p)

	if err := v.ReadInConfig(); err != nil {
		logs.Error("common_error", err.Error())
		panic(err)
	}

	if err := v.Unmarshal(&App); err != nil {
		logs.Error("common_error", err.Error())
		panic(err)
	}
}
