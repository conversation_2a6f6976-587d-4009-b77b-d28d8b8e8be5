load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "auth",
    srcs = [
        "api.go",
        "client.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/spacehomerpc/biz/provider/auth",
    visibility = ["//visibility:public"],
    deps = [
        "//app/spacehomerpc/biz/utils",
        "//idls/byted/devinfra/authz:authz_go_proto",
        "//idls/byted/devinfra/authz:authz_go_proto_xrpc_and_kitex_AuthzService",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_lang_gg//gresult",
    ],
)
