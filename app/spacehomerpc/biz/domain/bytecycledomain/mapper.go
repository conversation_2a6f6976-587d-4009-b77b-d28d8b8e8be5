package bytecycledomain

import (
	"code.byted.org/devinfra/hagrid/app/spacehomerpc/biz/provider/bytecycle"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/executionpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/spacehomepb"
	"code.byted.org/lang/gg/gslice"
)

func developmentTaskToIdl(item *executionpb.DevTaskItem) *spacehomepb.DevelopmentTask {
	if item == nil {
		return nil
	}
	return &spacehomepb.DevelopmentTask{
		Name:              item.GetName(),
		Status:            item.GetStatus(),
		BranchInfos:       gslice.Map(item.GetBranchInfos(), branchInfoToIdl),
		DevelopmentTaskId: item.GetDevelopmentTaskId(),
	}
}

func branchInfoToIdl(item *executionpb.TaskBranchInfo) *spacehomepb.DevTaskBranchInfo {
	if item == nil {
		return nil
	}
	return &spacehomepb.DevTaskBranchInfo{
		Branch:          item.GetBranch(),
		CloudIdeUrl:     item.GetCloudIdeUrl(),
		ProjectUniqueId: item.GetProjectUniqueId(),
		ProjectName:     item.GetProjectName(),
	}
}

func releaseTicketToIdl(item *executionpb.ReleaseTicketListItem) *spacehomepb.ReleaseTicket {
	if item == nil {
		return nil
	}
	return &spacehomepb.ReleaseTicket{
		Name:            item.GetName(),
		Status:          item.GetStatus(),
		Creator:         item.GetCreator(),
		Id:              item.GetId(),
		ReleaseTicketId: item.GetReleaseTicketId(),
		WorkspaceId:     item.GetWorkspaceId(),
		ReleaseApproves: item.GetReleaseApproves(),
		TestApproves:    item.GetTestApproves(),
	}
}

func projectToIdl(item *bytecycle.ProjectItem) *spacehomepb.MarchProject {
	if item == nil {
		return nil
	}
	return &spacehomepb.MarchProject{
		ProjectName:  item.ProjectName,
		ProjectType:  item.ProjectType,
		Creator:      item.Creator,
		WorkspaceId:  item.WorkspaceId,
		SolutionId:   item.SolutionId,
		SolutionName: item.SolutionName,
		RepoName:     item.RepoName,
		RepoUrl:      item.RepoUrl,
		ProjectId:    item.ProjectId,
	}
}

func workItemToIdl(item *executionpb.WorkItem) *spacehomepb.WorkItem {
	if item == nil {
		return nil
	}
	return &spacehomepb.WorkItem{
		Platform:       item.GetPlatform().String(),
		Type:           item.GetType(),
		Url:            item.GetUrl(),
		Name:           item.GetName(),
		DevTasks:       gslice.Map(item.GetDevTasks(), devTaskToIdl),
		Id:             item.GetId(),
		StorySpaceName: item.GetSpaceKey(),
	}
}

func devTaskToIdl(item *executionpb.BriefTaskInfo) *spacehomepb.DevTask {
	if item == nil {
		return nil
	}
	return &spacehomepb.DevTask{
		DevTaskId:   item.GetDevTaskId(),
		WorkspaceId: item.GetWorkspaceId(),
		Name:        item.GetDevTaskName(),
	}
}
