package migration

import (
	"context"
	_ "embed"
	"strings"

	"code.byted.org/canal/delivery-model/dsl"
	"code.byted.org/devinfra/hagrid/app/oreorpc/pkg/convert"
	atommanage "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/lang/gg/gslice"
)

var (
	//go:embed data/unsupported_atom
	unsupportedData string
	// 判断原子是否不被 Bits 流程支持
	unsupportedMap map[string]bool
)

type CheckAtomResp struct {
	// 不支持的原子
	NotSupport []string
	// 支持的原子
	NotFound []string
	// TCE 部署原子的 ID
	TCEAtomIDs []string
	// Goofy 部署原子的 ID
	GoofyAtomIDs []string
}

const (
	TCEAtomID   = "create_upgrade_deployment"
	GoofyAtomID = "goofy_deploy_full_flow"
)

// init 并返回 unsupportedMap
func getUnsupportedMap() map[string]bool {
	if unsupportedMap == nil {
		unsupportedMap = make(map[string]bool)
		unsupportedList := strings.Split(unsupportedData, "\n")
		for _, id := range unsupportedList {
			unsupportedMap[id] = true
		}
	}
	return unsupportedMap
}

func (w *UseCase) CheckAtom(ctx context.Context, rawDSL string, cp dslpb.ControlPanel) (*CheckAtomResp, error) {
	templateDSL, err := dsl.GetDSLFromJSONBytes([]byte(rawDSL))
	if err != nil {
		return nil, erri.Error(err)
	}

	atomUniqueIDs := make([]string, 0)
	tceIDs := make([]string, 0)
	goofyIDs := make([]string, 0)

	bitsMap, err := w.TCCRepo.GetBitsAtomMap(ctx)
	if err != nil {
		return nil, erri.Error(err)
	}

	// 遍历 DSL 中的各个节点
	err = templateDSL.Traverse(func(stateInfo dsl.State) error {
		if stateInfo.GetType() != dsl.StateTypeTask {
			return nil
		}

		// serviceName 不在候选 list 里直接跳过
		params := stateInfo.GetParameters()

		// 部分原子在新旧原子市场的名称不同，需要转换
		newID, ok := bitsMap[params.ServiceName]
		if !ok {
			newID = params.ServiceName
		}
		atomUniqueIDs = append(atomUniqueIDs, newID)
		if params.ServiceName == TCEAtomID {
			tceIDs = append(tceIDs, stateInfo.GetID())
		} else if params.ServiceName == GoofyAtomID {
			goofyIDs = append(goofyIDs, stateInfo.GetID())
		}

		return nil
	}, templateDSL.StartAt)

	if err != nil {
		return nil, erri.Error(err)
	}

	// 过滤出明确不再支持的原子
	unSupported := gslice.Filter(atomUniqueIDs, func(uID string) bool {
		_, ok := getUnsupportedMap()[uID]
		return ok
	})
	atomUniqueIDs = gslice.Filter(atomUniqueIDs, func(uID string) bool {
		return !gslice.Contains(unSupported, uID)
	})

	atomControlPanel := convert.DSLCpToAtomCP(cp)

	resp, err := w.AtomCli.ListAtoms(ctx, &atommanage.ListAtomsRequest{
		PageNum:          1,
		PageSize:         1000,
		UniqueIds:        atomUniqueIDs,
		WithFilterRegion: atomControlPanel,
	})
	if err != nil {
		return nil, erri.Error(err)
	}

	atomNames := gslice.Map(resp.Atoms, func(atom *atommanage.Atom) string {
		if atom == nil || atom.GetJobAtom() == nil {
			return ""
		}
		return atom.GetJobAtom().GetUniqueId()
	})

	notFound := gslice.Filter(atomUniqueIDs, func(uID string) bool {
		return !gslice.Contains(atomNames, uID)
	})

	return &CheckAtomResp{
		NotSupport:   gslice.Uniq(unSupported),
		NotFound:     gslice.Uniq(notFound),
		TCEAtomIDs:   gslice.Uniq(tceIDs),
		GoofyAtomIDs: gslice.Uniq(goofyIDs),
	}, nil
}
