package presettemplate

import (
	"context"
	"fmt"

	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/entity/model/hpmodel"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/entity/query/hp"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/timeutil"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/templatepb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
)

func (w *UseCase) GetTemplatePresetRuleDetail(ctx context.Context, req *templatepb.GetTemplatePresetRuleBasicReq) (resp *templatepb.GetTemplatePresetRuleBasicResp, err error) {
	// 获取基础信息以及绑定关系字段
	ruleRecord, err := hp.Q.OreoTemplatePresetRule.WithContext(ctx).Where(hp.OreoTemplatePresetRule.RuleID.Eq(req.GetRuleId())).First()
	if err != nil {
		return nil, err
	}
	ownerRecords, err := hp.Q.OreoPresetRuleOwner.WithContext(ctx).Where(hp.OreoPresetRuleOwner.RuleID.Eq(req.GetRuleId())).Find()
	if err != nil {
		return nil, err
	}
	owners := gslice.Map(ownerRecords, func(record *hpmodel.OreoPresetRuleOwner) string {
		return record.Owner
	})
	bindRecords, err := hp.Q.OreoPresetRuleBinding.WithContext(ctx).Where(hp.OreoPresetRuleBinding.RuleID.Eq(req.GetRuleId())).Find()
	if err != nil {
		return nil, err
	}
	if len(bindRecords) == 0 {
		return nil, fmt.Errorf("no binding info found for rule %v", req.GetRuleId())
	}

	spaceMap := make(map[uint64]*usecase.BriefSpaceInfo)
	scope := bindRecords[0].Scope
	simpleBytetreeList := make([]*templatepb.SimpleByteTreeNode, 0)
	switch scope {
	case uint32(templatepb.PresetRuleScope_PRESET_RULE_SCOPE_SPACE):
		spaceIDs := gslice.Map(bindRecords, func(record *hpmodel.OreoPresetRuleBinding) uint64 {
			return record.SpaceID
		})
		if spaceMap, err = w.SpaceRepo.BatchGetSpaceInfoWithCache(ctx, spaceIDs); err != nil {
			return nil, err
		}
	case uint32(templatepb.PresetRuleScope_PRESET_RULE_SCOPE_BYTETREE):
		bytetreeIDs := gslice.Map(bindRecords, func(record *hpmodel.OreoPresetRuleBinding) uint64 {
			return record.BytetreeNodeID
		})
		bytetreeList := make([]*usecase.BriefBytetreeNodeInfo, 0)
		g := errgroup.Group{}
		g.Go(func() error {
			var innerErr error
			defer utils.PanicGuard(ctx)
			if spaceMap, innerErr = w.SpaceRepo.BatchGetSpacesByBytetreeIDs(ctx, bytetreeIDs); innerErr != nil {
				return innerErr
			}
			return nil
		})

		g.Go(func() error {
			var innerErr error
			defer utils.PanicGuard(ctx)
			if bytetreeList, innerErr = w.BytetreeRepo.GetIndexNodesByIdListWithCache(ctx, bytetreeIDs); innerErr != nil {
				return innerErr
			}
			return nil
		})
		if err = g.Wait(); err != nil {
			return nil, err
		}
		simpleBytetreeList = gslice.Map(bytetreeList, func(node *usecase.BriefBytetreeNodeInfo) *templatepb.SimpleByteTreeNode {
			return &templatepb.SimpleByteTreeNode{
				Id:       node.ID,
				Path:     node.Path,
				PathI18N: node.I18NPath,
				Name:     node.Name,
				NameI18N: node.I18NName,
			}
		})

	default:
		return nil, fmt.Errorf("invalid scope, scope = %v", bindRecords[0].Scope)
	}

	spaceBriefs := make([]*templatepb.SimpleBitsSpace, 0)
	for _, space := range gmap.Values(spaceMap) {
		spaceBriefs = append(spaceBriefs, &templatepb.SimpleBitsSpace{
			Id:       space.ID,
			Name:     space.Name,
			NameI18N: space.NameI18N,
		})
	}

	// 组装返回结果
	resp = &templatepb.GetTemplatePresetRuleBasicResp{
		RuleId:           ruleRecord.RuleID,
		Name:             ruleRecord.Name,
		NameI18N:         ruleRecord.NameI18n,
		Owners:           owners,
		Spaces:           spaceBriefs,
		StageEnums:       constvar.DefaultStageEnums,
		Scope:            templatepb.PresetRuleScope(scope),
		RawBytetreeNodes: simpleBytetreeList,
		RawSpaces:        spaceBriefs,
		CreatedAt:        timeutil.TimeToString(ruleRecord.CreatedAt),
		UpdatedAt:        timeutil.TimeToString(ruleRecord.UpdatedAt),
	}
	return resp, nil
}

// IsPresetTemplateID 判断是否为预设模板
func (w *UseCase) IsPresetTemplateID(ctx context.Context, templateID int64) *hpmodel.OreoPresetRuleTemplate {
	t := hp.OreoPresetRuleTemplate
	record, err := hp.Q.OreoPresetRuleTemplate.WithContext(ctx).Where(t.TemplateID.Eq(templateID)).First()
	if err != nil {
		logs.CtxError(ctx, "get preset template failed, err = %v", err)
		return nil
	}
	return record
}
