package template

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/rmq"
	cdutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/entity/query/cd"
	bitsevents "code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/overpass/bytedance_bits_config_service/rpc/bytedance_bits_config_service"
)

type MessageParams struct {
	WorkflowType     workflowpb.WorkflowType
	WorkflowID       uint64
	NodeID           uint64
	Region           sharedpb.ControlPlane
	ProjectType      sharedpb.ProjectType
	TemplateName     string
	TemplateNameI18N string
	SpaceID          uint64
	Username         string
	EventName        bitsevents.InfoRecordEventName
}

// SendTemplateEvent 发送创建模版相关的消息事件
func (w *UseCase) SendTemplateEvent(ctx context.Context, req *MessageParams) {
	defer utils.PanicGuard(ctx)

	nodeName, nodeNameI18N, parent, err := w.GetNodeInfo(ctx, req.WorkflowType, req.WorkflowID, req.NodeID)
	if err != nil {
		logs.Error("get node info failed, err: %v", err)
		return
	}

	// 结构体
	paramValue := map[string]interface{}{
		"region":           []sharedpb.ControlPlane{req.Region},
		"projectType":      req.ProjectType,
		"templateName":     req.TemplateName,
		"templateNameI18N": req.TemplateNameI18N,
		"nodeName":         nodeName,
		"nodeNameI18N":     nodeNameI18N,
		"nodeId":           req.NodeID,
	}

	// 上报操作记录
	event := &bitsevents.InfoRecordEvent{
		SpaceId:            req.SpaceID,
		ContentType:        bitsevents.InfoRecordEventContentType_JustParam,
		Operator:           req.Username,
		OccurUnixTimestamp: time.Now().Unix(),
		Parent:             parent,
		ParentPrimaryKey:   strconv.FormatUint(req.WorkflowID, 10),
		EventType:          bitsevents.InfoRecordEventType_ConfigEditWorkflowNode,
		EventName:          req.EventName,
		ParamValue:         cdutils.JSONToString(ctx, paramValue),
		LogId:              cdutils.GetContextLogID(ctx),
	}

	go rmq.SetOperationRecord(ctx, event)
	logs.CtxInfo(ctx, "[rmq] pipeline event sent! body=%v", cdutils.JSONToString(ctx, event))
}

func (w *UseCase) GetNodeInfo(ctx context.Context, workflowType workflowpb.WorkflowType, workflowID, nodeID uint64) (string, string, bitsevents.InfoRecordEventParent, error) {
	var nodeName string
	var nodeNameI18N string
	var parent bitsevents.InfoRecordEventParent

	switch workflowType {
	case workflowpb.WorkflowType_WORKFLOW_TYPE_DEVELOPMENT_TASK:
		node, err := bytedance_bits_config_service.GetSimpleNodeInfo(ctx, int64(workflowID), int64(nodeID))
		if err != nil {
			return "", "", "", err
		}
		nodeName = node.NodeName
		nodeNameI18N = node.NodeNameI18n
		parent = bitsevents.InfoRecordEventParent_DevTaskWorkflowConfig
	case workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET:
		n := cd.HagridCdNode
		nodeRecord, err := cd.Q.WithContext(ctx).HagridCdNode.Where(n.NodeID.Eq(nodeID), n.WorkflowID.Eq(workflowID)).First()
		if err != nil {
			return "", "", "", err
		}
		nodeName = nodeRecord.Name
		nodeNameI18N = nodeRecord.NameI18n
		parent = bitsevents.InfoRecordEventParent_ReleaseTicketWorkflowConfig
	default:
		return "", "", "", fmt.Errorf("invalid workflow type=%s", workflowType.String())
	}
	return nodeName, nodeNameI18N, parent, nil
}
