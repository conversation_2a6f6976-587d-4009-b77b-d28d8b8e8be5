package tcc

import (
	"context"
	"fmt"

	jsoniter "github.com/json-iterator/go"
	"google.golang.org/genproto/googleapis/rpc/code"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/templatepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"

	"code.byted.org/devinfra/hagrid/libs/bits_err"

	"code.byted.org/devinfra/hagrid/pkg/herror"
)

// 模版相关
const (
	CDEnv = "env"
	CDPub = "publish"

	CIFixNameFeatureTest = "DevDevelopStageFeatureTestTask"
)

// SupportedProjectType 目前研发流程支持的所有项目类型
var SupportedProjectType = []sharedpb.ProjectType{
	sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED,
	sharedpb.ProjectType_PROJECT_TYPE_TCE,
	sharedpb.ProjectType_PROJECT_TYPE_FAAS,
	sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
	sharedpb.ProjectType_PROJECT_TYPE_WEB,
	sharedpb.ProjectType_PROJECT_TYPE_HYBRID,
	sharedpb.ProjectType_PROJECT_TYPE_CUSTOM,
	sharedpb.ProjectType_PROJECT_TYPE_TCC,
}

// SupportedRegionType 目前研发流程支持的所有控制面类型
var SupportedRegionType = []sharedpb.ControlPlane{
	sharedpb.ControlPlane_CONTROL_PLANE_CN,
	sharedpb.ControlPlane_CONTROL_PLANE_I18N,
	sharedpb.ControlPlane_CONTROL_PLANE_TTP,
	sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP,
	sharedpb.ControlPlane_CONTROL_PLANE_US_TTP,
}

// EUTTPSupportedProjectType EUTTP 控制面已经支持了的项目类型 list
var EUTTPSupportedProjectType = []sharedpb.ProjectType{
	sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED,
	sharedpb.ProjectType_PROJECT_TYPE_TCE,
	sharedpb.ProjectType_PROJECT_TYPE_FAAS,
	sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
	sharedpb.ProjectType_PROJECT_TYPE_TCC,
	sharedpb.ProjectType_PROJECT_TYPE_CUSTOM,
}

// USTTPSupportedProjectType USTTP 控制面已经支持了的项目类型 list
var USTTPSupportedProjectType = []sharedpb.ProjectType{
	sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED,
	sharedpb.ProjectType_PROJECT_TYPE_TCE,
	sharedpb.ProjectType_PROJECT_TYPE_FAAS,
	sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
	sharedpb.ProjectType_PROJECT_TYPE_WEB,
	sharedpb.ProjectType_PROJECT_TYPE_TCC,
	sharedpb.ProjectType_PROJECT_TYPE_CUSTOM,
}

// CustomSupportedRegion 自定义项目已经支持的控制面
var CustomSupportedRegion = []sharedpb.ControlPlane{
	sharedpb.ControlPlane_CONTROL_PLANE_CN,
	sharedpb.ControlPlane_CONTROL_PLANE_I18N,
	sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP,
	sharedpb.ControlPlane_CONTROL_PLANE_US_TTP,
}

var CustomizableRollbackProjectList = []sharedpb.ProjectType{
	sharedpb.ProjectType_PROJECT_TYPE_CUSTOM,
}

var NormalTemplateRegions = []sharedpb.ControlPlane{
	sharedpb.ControlPlane_CONTROL_PLANE_CN,
	sharedpb.ControlPlane_CONTROL_PLANE_I18N,
	sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP,
	sharedpb.ControlPlane_CONTROL_PLANE_US_TTP,
}

var WebTemplateRegions = []sharedpb.ControlPlane{
	sharedpb.ControlPlane_CONTROL_PLANE_CN,
	sharedpb.ControlPlane_CONTROL_PLANE_I18N,
	sharedpb.ControlPlane_CONTROL_PLANE_US_TTP,
}

var GeckoTemplateRegions = []sharedpb.ControlPlane{
	sharedpb.ControlPlane_CONTROL_PLANE_CN,
	sharedpb.ControlPlane_CONTROL_PLANE_I18N,
}

var MainTemplateRegions = []sharedpb.ControlPlane{
	sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED,
	sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP,
	sharedpb.ControlPlane_CONTROL_PLANE_US_TTP,
}

func GetProjectSupportRegions(projectType sharedpb.ProjectType) []sharedpb.ControlPlane {
	switch projectType {
	case sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED:
		return MainTemplateRegions
	case sharedpb.ProjectType_PROJECT_TYPE_TCE,
		sharedpb.ProjectType_PROJECT_TYPE_FAAS,
		sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
		sharedpb.ProjectType_PROJECT_TYPE_TCC, sharedpb.ProjectType_PROJECT_TYPE_CUSTOM:
		return NormalTemplateRegions
	case sharedpb.ProjectType_PROJECT_TYPE_WEB:
		return WebTemplateRegions
	case sharedpb.ProjectType_PROJECT_TYPE_HYBRID:
		return GeckoTemplateRegions
	default:
		return nil
	}
}

type GetCDTemplateParams struct {
	Scene       string
	ProjectType sharedpb.ProjectType
	Region      sharedpb.ControlPlane
}

// CDInitPplConfig 某个研发流程对应的初始流水线配置
type CDInitPplConfig struct {
	Env      map[sharedpb.ProjectType]RegionInitTemplateConfig `json:"env,omitempty"`
	Publish  map[sharedpb.ProjectType]RegionInitTemplateConfig `json:"publish,omitempty"`
	Rollback map[sharedpb.ProjectType]RegionInitTemplateConfig `json:"rollback,omitempty"`
	Archive  []int64                                           `json:"archive,omitempty"`
}

type GetCITemplateParams struct {
	FixedName   string
	ProjectType sharedpb.ProjectType
	Region      sharedpb.ControlPlane
}

type CIInitPplConfig struct {
	Feature map[sharedpb.ProjectType]RegionInitTemplateConfig `json:"feature,omitempty"`
}

// RegionInitTemplateConfig 某个项目类型在不同控制面下的模版配置
type RegionInitTemplateConfig struct {
	CN    *InitTemplateItem `json:"cn,omitempty"`
	I18N  *InitTemplateItem `json:"i18n,omitempty"`
	TTP   *InitTemplateItem `json:"ttp,omitempty"`
	EUTTP *InitTemplateItem `json:"euttp,omitempty"`
	USTTP *InitTemplateItem `json:"usttp,omitempty"`
}

// InitTemplateItem 初始模版配置项
type InitTemplateItem struct {
	TemplateID int64  `json:"template_id"`
	Name       string `json:"name"`
	NameI18N   string `json:"name_i18n"`
}

type Rule struct {
	Bind struct {
		Left  string `json:"left"`
		Right string `json:"right"`
	} `json:"bind"`
	Undraggable bool `json:"undraggable"`
	Undeletable bool `json:"undeletable"`
	Uneditable  bool `json:"uneditable"`
	Unaddable   bool `json:"unaddable"`
}
type TemplateUpgradeModeRule struct {
	UpgradeMode paaspb.UpgradeMode                  `json:"upgrade_mode"`
	Rules       map[string]*templatepb.TemplateRule `json:"rules"`
}

// GetCDInitPplConfig 获取发布单模版的初始默认模版配置, 并序列化
func (r *Repo) GetCDInitPplConfig(ctx context.Context) (CDInitPplConfig, error) {
	config, err := r.defaultClient.Get(ctx, CDInitPplTemplateKey)
	if err != nil {
		return CDInitPplConfig{}, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", CDInitPplTemplateKey))
	}
	var res CDInitPplConfig
	err = jsoniter.Unmarshal([]byte(config), &res)
	if err != nil {
		return CDInitPplConfig{}, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config corruption: %v", CDInitPplTemplateKey))
	}
	return res, nil
}

// GetCIInitPplConfig 获取开发任务模版的初始默认模版配置, 并序列化
func (r *Repo) GetCIInitPplConfig(ctx context.Context) (CIInitPplConfig, error) {
	config, err := r.defaultClient.Get(ctx, CIInitPplTemplateKey)
	if err != nil {
		return CIInitPplConfig{}, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", CIInitPplTemplateKey))
	}
	var res CIInitPplConfig
	err = jsoniter.Unmarshal([]byte(config), &res)
	if err != nil {
		return CIInitPplConfig{}, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", CIInitPplTemplateKey))
	}
	return res, nil
}

func CDParamsCheck(p *GetCDTemplateParams) error {
	// EU-TTP 与 US-TTP 仅支持特定项目类型
	switch p.Region {
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		if !slicex.Contains(EUTTPSupportedProjectType, p.ProjectType) {
			return bits_err.PIPELINECOMMON.ErrInvalidInput.SetMsg("unsupported project type for euttp region: %s", p.ProjectType)
		}
		return nil
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		if !slicex.Contains(USTTPSupportedProjectType, p.ProjectType) {
			return bits_err.PIPELINECOMMON.ErrInvalidInput.SetMsg("unsupported project type for usttp region: %s", p.ProjectType)
		}
		return nil
	default:
		return nil
	}
}

// SafelyGetCDInitPplConfig 安全获取 CD 初始模版配置，防止 nil pointer 可能导致的 panic
func (r *Repo) SafelyGetCDInitPplConfig(ctx context.Context, params *GetCDTemplateParams) (*InitTemplateItem, error) {
	err := CDParamsCheck(params)
	if err != nil {
		return nil, err
	}

	fullConfig, err := r.GetCDInitPplConfig(ctx)
	if err != nil {
		return nil, erri.Error(err)
	}
	var configMap map[sharedpb.ProjectType]RegionInitTemplateConfig
	switch params.Scene {
	case CDEnv:
		configMap = fullConfig.Env
		if configMap == nil {
			return nil, erri.Errorf("init template config not found! scene: %s", CDEnv)
		}
	case CDPub:
		configMap = fullConfig.Publish
		if configMap == nil {
			return nil, erri.Errorf("init template config not found! scene: %s", CDPub)
		}
	case CDRollback:
		configMap = fullConfig.Rollback
		if configMap == nil {
			return nil, erri.Errorf("init template config not found! scene: %s", CDRollback)
		}
	default:
		return nil, erri.Errorf("init template config not found! scene: %s", params.Scene)
	}
	regionInitConfig, ok := configMap[params.ProjectType]
	if !ok {
		return nil, erri.Errorf("init template config not found! scene: %s, project_type: %s", params.Scene, params.ProjectType)
	}
	if sharedpb.ControlPlane_CONTROL_PLANE_I18N == params.Region {
		return regionInitConfig.I18N, nil
	}
	if sharedpb.ControlPlane_CONTROL_PLANE_TTP == params.Region {
		return regionInitConfig.TTP, nil
	}
	if sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP == params.Region {
		return regionInitConfig.EUTTP, nil
	}
	if sharedpb.ControlPlane_CONTROL_PLANE_US_TTP == params.Region {
		return regionInitConfig.USTTP, nil
	}
	return regionInitConfig.CN, nil
}

func CIParamsCheck(p *GetCITemplateParams) error {
	// EU-TTP 与 US-TTP 仅支持特定项目类型
	switch p.Region {
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		if !slicex.Contains(EUTTPSupportedProjectType, p.ProjectType) {
			return bits_err.PIPELINECOMMON.ErrInvalidInput.SetMsg("unsupported project type for euttp region: %s", p.ProjectType)
		}
		return nil
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		if !slicex.Contains(USTTPSupportedProjectType, p.ProjectType) {
			return bits_err.PIPELINECOMMON.ErrInvalidInput.SetMsg("unsupported project type for usttp region: %s", p.ProjectType)
		}
		return nil
	default:
		return nil
	}
}

// SafelyGetCIInitPplConfig 安全获取 CI 初始模版配置，防止 nil pointer 可能导致的 panic
func (r *Repo) SafelyGetCIInitPplConfig(ctx context.Context, params *GetCITemplateParams) (*InitTemplateItem, error) {
	err := CIParamsCheck(params)
	if err != nil {
		return nil, err
	}

	fullConfig, err := r.GetCIInitPplConfig(ctx)
	if err != nil {
		return nil, erri.Error(err)
	}
	var configMap map[sharedpb.ProjectType]RegionInitTemplateConfig

	switch params.FixedName {
	case CIFixNameFeatureTest:
		configMap = fullConfig.Feature
		if configMap == nil {
			return nil, erri.Errorf("init template config not found! fixedName: %s", CIFixNameFeatureTest)
		}
	default:
		logs.CtxInfo(ctx, "invalid fixedName=%s, fall back to DevDevelopStageFeatureTestTask", params.FixedName)
		configMap = fullConfig.Feature
		if configMap == nil {
			return nil, erri.Errorf("init template config not found! fixedName: %s", CIFixNameFeatureTest)
		}
	}

	regionInitConfig, ok := configMap[params.ProjectType]
	if !ok {
		return nil, erri.Errorf("init template config not found! fixedName: %s, project_type: %s", params.FixedName, params.ProjectType)
	}
	switch params.Region {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return regionInitConfig.CN, nil
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		return regionInitConfig.I18N, nil
	case sharedpb.ControlPlane_CONTROL_PLANE_TTP:
		return regionInitConfig.TTP, nil
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return regionInitConfig.EUTTP, nil
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return regionInitConfig.USTTP, nil
	default:
		return nil, herror.Errorf(code.Code_INTERNAL, "invalid region = %d", params.Region)
	}
}

// GetInitPplIDs 获取默认模版 ID 列表
func (r *Repo) GetInitPplIDs(ctx context.Context) ([]int64, error) {
	config, err := r.defaultClient.Get(ctx, InitPplTemplateIDKey)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", InitPplTemplateIDKey))
	}
	var res []int64
	err = jsoniter.Unmarshal([]byte(config), &res)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", InitPplTemplateIDKey))
	}
	return res, nil
}

// IsInitTemplate 判断当前模版是否是初始默认模版
func (r *Repo) IsInitTemplate(ctx context.Context, templateID int64) bool {
	initIDs, err := r.GetInitPplIDs(ctx)
	if err != nil {
		logs.CtxError(ctx, "failed to get default ppl ids: %v", err)
		return false
	}
	return slicex.Contains(initIDs, templateID)
}

// IsInitDeployTemplate 判断当前模版是否是发布阶段初始模版
func (r *Repo) IsInitDeployTemplate(ctx context.Context, templateID int64) bool {
	fullConfig, err := r.GetCDInitPplConfig(ctx)
	if err != nil {
		return false
	}

	for _, p := range SupportedProjectType {
		if fullConfig.Publish == nil {
			continue
		}
		config, ok := fullConfig.Publish[p]
		if !ok {
			continue
		}
		targets := []*InitTemplateItem{config.CN, config.I18N, config.TTP, config.EUTTP, config.USTTP}
		for _, t := range targets {
			if t == nil {
				continue
			}
			if t.TemplateID == templateID {
				return true
			}
		}
	}

	return false
}

// IsArchiveTemplate 判断当前模版是否为未开启部署升级的初始模版，即已归档的初始模版
func (r *Repo) IsArchiveTemplate(ctx context.Context, templateID int64) bool {
	fullConfig, err := r.GetCDInitPplConfig(ctx)
	if err != nil {
		return false
	}
	return slicex.Contains(fullConfig.Archive, templateID)
}

// GetInitTemplateBasicStage 获取基础模版阶段信息
func (r *Repo) GetInitTemplateBasicStage(ctx context.Context) ([]*dslpb.Stage, error) {
	config, err := r.defaultClient.Get(ctx, InitTemplateBasicStageKey)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", InitTemplateBasicStageKey))
	}
	var res []*dslpb.Stage
	err = jsoniter.Unmarshal([]byte(config), &res)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", InitTemplateBasicStageKey))
	}
	return res, nil
}

func (r *Repo) GetTemplateUpgradeModeRules(ctx context.Context, upgradeMode paaspb.UpgradeMode) (map[string]*templatepb.TemplateRule, error) {
	if upgradeMode == paaspb.UpgradeMode_UPGRADE_MODE_UNSPECIFIED {
		return nil, nil
	}
	config, err := r.defaultClient.Get(ctx, TemplateUpgradeMoeRulesKey)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", TemplateUpgradeMoeRulesKey))
	}
	var res []*TemplateUpgradeModeRule
	err = jsoniter.Unmarshal([]byte(config), &res)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", TemplateUpgradeMoeRulesKey))
	}
	for _, rule := range res {
		if rule.UpgradeMode == upgradeMode {
			return rule.Rules, nil
		}
	}
	return nil, nil
}

func (r *Repo) GetFaaSTemplateRules(ctx context.Context) (map[string]*templatepb.TemplateRule, error) {
	config, err := r.defaultClient.Get(ctx, FaaSTemplateRulesKey)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", FaaSTemplateRulesKey))
	}
	var rules map[string]*templatepb.TemplateRule
	err = jsoniter.Unmarshal([]byte(config), &rules)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v", FaaSTemplateRulesKey))
	}
	return rules, nil
}

func (r *Repo) GetInitFaasBasicTemplate(ctx context.Context, key string) ([]*dslpb.Stage, error) {
	config, err := r.defaultClient.Get(ctx, InitFaasBasicTemplate)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v,err:%v", InitFaasBasicTemplate, err))
	}
	var res map[string][]*dslpb.Stage
	err = jsoniter.Unmarshal([]byte(config), &res)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v,err:%v", InitFaasBasicTemplate, err))
	}
	if _, ok := res[key]; !ok {
		return nil, herror.New(code.Code_INVALID_ARGUMENT, fmt.Sprintf("not found %s config", key))
	}
	return res[key], nil
}

func (r *Repo) GetTemplateValidateSchema(ctx context.Context) ([]*templatepb.ValidateSchemaItem, error) {
	config, err := r.defaultClient.Get(ctx, TemplateValidateSchemaKey)
	if err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v,err:%v", TemplateValidateSchemaKey, err))
	}
	res := make([]*templatepb.ValidateSchemaItem, 0)
	if err = jsoniter.Unmarshal([]byte(config), &res); err != nil {
		return nil, herror.New(code.Code_INTERNAL, fmt.Sprintf("get tcc config failed: %v,err:%v", TemplateValidateSchemaKey, err))
	}
	return res, nil
}
