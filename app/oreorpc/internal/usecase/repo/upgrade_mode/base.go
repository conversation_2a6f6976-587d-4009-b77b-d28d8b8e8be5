package upgrade_mode

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/structpb"

	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase/constvar"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/infrapb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
)

type UpgradeAtom struct {
	Name              string
	IsDeploy<PERSON>tom      bool
	DisabledForceSkip bool
}

var upgradeModeAtomMap = map[string]UpgradeAtom{
	"build": {
		Name:              "tce_build_stage_driver",
		IsDeployAtom:      false,
		DisabledForceSkip: true,
	},
	"confirm": {
		Name:              "user_confirm",
		IsDeployAtom:      false,
		DisabledForceSkip: false,
	},
	"upgrade_cluster": {
		Name:              "tce_dc_based_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"blue_green_hook": {
		Name:              "tce_blue_green_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"create_cluster": {
		Name:              "tce_create_cluster_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"create_clean": {
		Name:              "tce_create_clean_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"upgrade_smooth_by_idc": {
		Name:              "tce_upgrade_smooth_by_idc_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"upgrade_smooth_by_prod": {
		Name:              "tce_upgrade_smooth_by_prod_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"upgrade_smooth_by_stage": {
		Name:              "tce_upgrade_smooth_by_stage_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"upgrade_canary": {
		Name:              "tce_stage_based_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"upgrade_single_dc": {
		Name:              "tce_stage_based_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
	"upgrade_all_dc": {
		Name:              "tce_stage_based_stage_driver",
		IsDeployAtom:      true,
		DisabledForceSkip: true,
	},
}

type UpgradeModeRepo struct {
	tccRepo usecase.SettingsRepo
}

func Initialize(tccRepo usecase.SettingsRepo) *UpgradeModeRepo {
	return &UpgradeModeRepo{
		tccRepo: tccRepo,
	}
}

func transformMode(mode paaspb.UpgradeMode) (string, string, error) {
	switch mode {
	case paaspb.UpgradeMode_UPGRADE_MODE_STAGE:
		return constvar.UpgradeModeStage, "", nil
	case paaspb.UpgradeMode_UPGRADE_MODE_CLUSTER:
		return constvar.UpgradeModeCluster, "", nil
	case paaspb.UpgradeMode_UPGRADE_MODE_BLUE_GREEN:
		return constvar.UpgradeModeBlueGreen, "", nil
	case paaspb.UpgradeMode_UPGRADE_MODE_SMOOTH_BY_STAGE:
		return constvar.UpgradeModeSmooth, constvar.UpgradeSubModeStage, nil
	case paaspb.UpgradeMode_UPGRADE_MODE_SMOOTH_BY_IDC:
		return constvar.UpgradeModeSmooth, constvar.UpgradeSubModeIdc, nil
	case paaspb.UpgradeMode_UPGRADE_MODE_SMOOTH_BY_PROD:
		return constvar.UpgradeModeSmooth, constvar.UpgradeSubModeProd, nil
	default:
		return "", "", errors.New("not found upgrade mode")
	}
}

func getJobInfo(ctx context.Context, atomName, dependOnJobId string, tceStage map[string]interface{}) (*dslpb.Job, error) {
	if _, ok := tceStage["name"]; !ok {
		logs.CtxError(ctx, "tceStage is not found name %v", tceStage)
		return nil, errors.New("stage is not found name")
	}
	tceStageByte, err := sonic.Marshal(tceStage)
	if err != nil {
		return nil, err
	}
	inputs := &structpb.Struct{}
	err = sonic.Unmarshal(tceStageByte, inputs)
	if err != nil {
		return nil, err
	}
	jobId := fmt.Sprintf("%s-%s", atomName, randString())
	job := &dslpb.Job{
		Id: jobId,
		Name: &infrapb.I18NString{
			Value: tceStage["name"].(string),
			Lang:  "zh",
			Texts: map[string]string{"zh": tceStage["name"].(string), "en": atomName},
		},
		Inputs:   inputs,
		OnFailed: dslpb.OnFailed_ON_FAILED_FAIL,
		Uses:     fmt.Sprintf("job_atoms/%s", atomName),
	}

	if dependOnJobId != "" {
		job.DependsOn = append(job.DependsOn, dependOnJobId)
	}
	return job, nil
}

func randString() string {
	charset := "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	bytes := make([]byte, 6)
	for i := range bytes {
		bytes[i] = charset[r.Intn(len(charset))]
	}
	return string(bytes)
}

func getReplenishStage(upgradeMode paaspb.UpgradeMode, controlPlane string) (*dslpb.Stage, error) {
	if gslice.Contains([]paaspb.UpgradeMode{paaspb.UpgradeMode_UPGRADE_MODE_BLUE_GREEN, paaspb.UpgradeMode_UPGRADE_MODE_STAGE}, upgradeMode) {
		return nil, nil
	}
	jobId := fmt.Sprintf("%s-%s", constvar.ReplenishAtom, randString())
	inputsMap := map[string]interface{}{
		"auto_start": true,
		"deletable":  true,
		"editable":   true,
		"filter": map[string]interface{}{
			"name": "*",
		},
		"launch_mode":      "by_stage",
		"name":             "Deploy for replenishment",
		"need_detect":      true,
		"need_online_test": false,
		"type":             "upgrade_cluster",
	}
	inputs, err := structpb.NewStruct(inputsMap)
	if err != nil {
		return nil, err
	}
	entryDetectionJob, err := getGatekeeperJob("", controlPlane, false)
	if err != nil {
		return nil, err
	}
	replenishJob := &dslpb.Job{
		Id: jobId,
		Name: &infrapb.I18NString{
			Value: "Deploy for replenishment",
			Lang:  "en",
			Texts: map[string]string{"zh": "缺补部署", "en": "Deploy for replenishment"},
		},
		Inputs:            inputs,
		Manual:            true,
		OnFailed:          dslpb.OnFailed_ON_FAILED_FAIL,
		Uses:              fmt.Sprintf("job_atoms/%s", constvar.ReplenishAtom),
		DependsOn:         []string{entryDetectionJob.Id},
		DisableOperations: []dslpb.JobRunOperation{dslpb.JobRunOperation_JOB_RUN_OPERATION_FORCE_SKIP},
	}
	return &dslpb.Stage{
		Id: "stage_4",
		Name: &infrapb.I18NString{
			Value: "Deploy Rest of All",
			Lang:  "zh",
			Texts: map[string]string{"zh": "Deploy Rest of All"},
		},
		Jobs: []*dslpb.Job{
			entryDetectionJob,
			replenishJob,
		},
	}, nil
}

func getGatekeeperJob(dependOnJobId string, controlPlane string, exitDetection bool) (*dslpb.Job, error) {
	jobId := fmt.Sprintf("%s-%s", constvar.GetaKeeperAtomName, randString())
	inputs, err := structpb.NewStruct(map[string]interface{}{
		"psm":           "{{sys.release_ticket.psm}}",
		"control_plane": controlPlane,
		"project_type":  "tce",
	})
	if err != nil {
		return nil, err
	}
	name := &infrapb.I18NString{
		Value: "Entry detection",
		Lang:  "en",
		Texts: map[string]string{"zh": "准入检测", "en": "Entry detection"},
	}
	if exitDetection {
		name = &infrapb.I18NString{
			Value: "Exit detection",
			Lang:  "en",
			Texts: map[string]string{"zh": "准出检测", "en": "Exit detection"},
		}
	}
	job := &dslpb.Job{
		Id:       jobId,
		Name:     name,
		Inputs:   inputs,
		OnFailed: dslpb.OnFailed_ON_FAILED_FAIL,
		Uses:     constvar.GateKeeperAtomUses,
	}
	if dependOnJobId != "" {
		job.DependsOn = append(job.DependsOn, dependOnJobId)
	}
	return job, nil

}

func GetTCEServiceEnvByControlPlane(controlPlane sharedpb.ControlPlane) string {
	switch controlPlane {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return constvar.TCEServiceEnvTCE
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		return constvar.TCEServiceEnvI18N
	case sharedpb.ControlPlane_CONTROL_PLANE_TTP:
		return constvar.TCEServiceEnvTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return constvar.TCEServiceEnvEUTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return constvar.TCEServiceEnvTTP
	default:
		return constvar.TCEServiceEnvUnknown
	}
}

func GetFaasServiceRegionByControlPlane(controlPlane sharedpb.ControlPlane) string {
	switch controlPlane {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return constvar.FaasServiceRegionCn
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		return constvar.FaasServiceRegionI18n
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return constvar.FaasServiceRegionEUTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return constvar.FaasServiceRegionUSTTP
	default:
		return constvar.TCEServiceEnvUnknown
	}
}
