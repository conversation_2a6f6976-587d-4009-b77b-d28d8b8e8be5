package upgrade_mode

import (
	"context"
	"fmt"
	"strings"

	"code.byted.org/gopkg/lang/v2/operatorx"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/structpb"

	"code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase"
	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase/constvar"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/infrapb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/templatepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gslice"
)

func (u *UpgradeModeRepo) TransformBasicTemplate(ctx context.Context, upgradeMode *templatepb.TceUpgradeMode, region sharedpb.ControlPlane, username string) ([]*dslpb.Stage, error) {
	mode, subMode, err := transformMode(upgradeMode.UpgradeMode)
	if err != nil {
		return nil, err
	}
	req := &usecase.GetPipelineTemplateFrameworksRequest{
		GalaxyNodeId: upgradeMode.BytetreeNodeId,
		Type:         constvar.UpgradeModeType,
		Mode:         mode,
		SubMode:      subMode,
	}

	tceTemplateFramework, err := u.tccRepo.GetTCEFrameworkTemplates(ctx, region, mode, subMode)
	if err != nil {
		return nil, err
	}
	stages, err := u.tccRepo.GetInitTemplateBasicStage(ctx)
	if err != nil {
		return nil, err
	}
	// 将升级模式添加到创建TCE工单原子Inputs中
	for _, stage := range stages {
		jobs := make([]*dslpb.Job, 0)
		for _, job := range stage.GetJobs() {
			switch job.Uses {
			case constvar.DeploymentAtomUses:
				job.Inputs.Fields["upgrade_mode"] = structpb.NewStringValue(upgradeMode.UpgradeMode.String())
				job.Inputs.Fields["service_env"] = structpb.NewStringValue(GetTCEServiceEnvByControlPlane(region))
				jobs = append(jobs, job)
			case constvar.GateKeeperAtomUses:
				job.Inputs.Fields["control_plane"] = structpb.NewStringValue(region.String())
				jobs = append(jobs, job)
			default:
				jobs = append(jobs, job)
			}
		}
		stage.Jobs = jobs
	}

	stage := &dslpb.Stage{
		Id: "stage_3",
		Name: &infrapb.I18NString{
			Value: "Deploy",
			Lang:  "en",
			Texts: map[string]string{"en": "Deploy", "zh": "Deploy"},
		},
	}
	dependOnJobId := ""
	for _, c := range tceTemplateFramework.Content.Stages {
		if _, ok := c["type"]; !ok {
			logs.CtxInfo(ctx, "[TransformBasicTemplate] stage type is not found,req:%v,stage:%v", req, c)
			continue
		}
		jobType, ok := c["type"].(string)
		if !ok {
			logs.CtxError(ctx, "[TransformBasicTemplate] jobType is not string,req:%v,stage:%v", req, c)
			return nil, errors.New("jobType is not string")
		}
		upgradeAtom, ok := upgradeModeAtomMap[jobType]
		if !ok {
			logs.CtxInfo(ctx, "[TransformBasicTemplate] tce stage type unmatch atom,%+v", c)
			continue
		}
		if upgradeAtom.Name == constvar.UserConfirm {
			logs.CtxInfo(ctx, "[TransformBasicTemplate] tce user_confirm atom is filtered,%+v", c)
			continue
		}

		if upgradeAtom.IsDeployAtom {
			entryDetection, err := getGatekeeperJob(dependOnJobId, region.String(), false)
			if err != nil {
				return nil, err
			}
			stage.Jobs = append(stage.Jobs, entryDetection)
			dependOnJobId = entryDetection.Id
		}
		job, err := getJobInfo(ctx, upgradeAtom.Name, dependOnJobId, c)
		job.Manual = checkManual(jobType, upgradeAtom.IsDeployAtom, c)
		if err != nil {
			return nil, err
		}
		job.DisableOperations = operatorx.IfThen(upgradeAtom.DisabledForceSkip, []dslpb.JobRunOperation{dslpb.JobRunOperation_JOB_RUN_OPERATION_FORCE_SKIP}, []dslpb.JobRunOperation{})
		dependOnJobId = job.Id
		stage.Jobs = append(stage.Jobs, job)
		if strings.HasPrefix(job.Uses, constvar.TCEBuildUses) && region == sharedpb.ControlPlane_CONTROL_PLANE_US_TTP {
			// US-TTP 需要额外添加一个 US 确认原子
			input, err2 := structpb.NewStruct(map[string]interface{}{
				"idc":   "tx",
				"level": 5,
			})
			if err2 != nil {
				logs.CtxError(ctx, "[TransformBasicTemplate] NewStruct error %+v", err2)
				return nil, bits_err.TEMPLATE.ErrOreoUpdateTemplateBasicConfig.AddOrPass(ctx, err2)
			}
			reviewJob := &dslpb.Job{
				Id: "ttp_tcedeployconfirmtask_ba34",
				Name: &infrapb.I18NString{
					Value: "USTS 审批确认",
					Lang:  "zh",
					Texts: map[string]string{
						"zh": "USTS 审批确认",
						"en": "USTS review confirmation",
					},
				},
				If:        "",
				IfSkip:    "",
				Inputs:    input,
				DependsOn: []string{job.Id},
				ExtraIf:   "",
				Manual:    false,
				Uses:      "job_atoms/ttp_tcedeployconfirmtask",
				Timeout:   604800,
				OnFailed:  1,
				OnTimeout: 1,
				OnIgnored: 2,
			}
			stage.Jobs = append(stage.Jobs, reviewJob)
			dependOnJobId = reviewJob.Id
		}
	}
	replenishStage, err := getReplenishStage(upgradeMode.GetUpgradeMode(), region.String())
	if err != nil {
		return nil, err
	}
	if replenishStage != nil {
		jobs, err := addExitDetectionJobAfterLastDeployJob(ctx, replenishStage.GetJobs(), region.String())
		if err != nil {
			logs.CtxError(ctx, "[TransformBasicTemplate] addGatekeeperJobAfterLastDeployJob error %+v", err)
			return nil, err
		}
		replenishStage.Jobs = jobs
		stages = append(stages, stage)
		stages = append(stages, replenishStage)
		return stages, nil
	}
	jobs, err := addExitDetectionJobAfterLastDeployJob(ctx, stage.GetJobs(), region.String())
	if err != nil {
		logs.CtxError(ctx, "[TransformBasicTemplate] addGatekeeperJobAfterLastDeployJob error %+v", err)
		return nil, err
	}
	stage.Jobs = jobs
	stages = append(stages, stage)
	return stages, nil
}

func (u *UpgradeModeRepo) TransformFaasBasicTemplate(ctx context.Context, p *usecase.TransformFaasParams) ([]*dslpb.Stage, error) {
	if p.Mode == nil {
		return nil, errors.New("mode is nil")
	}

	// 比对入参和现有参数的差异，决定如何编辑模板
	if p.Mode.GetPublishMode() == p.CurrConfig.GetPublishMode() &&
		p.Mode.GetVolumeStrategy() == p.CurrConfig.GetVolumeStrategy() {
		// 全量发布无需比较灰度相关字段，不更新编排
		if p.Mode.GetVolumeStrategy() == paaspb.VolumeStrategy_VOLUME_STRATEGY_ALL {
			return p.Stages, nil
		}
		// 灰度需要额外比较灰度相关字段
		if p.Mode.GetVolumeStrategy() == paaspb.VolumeStrategy_VOLUME_STRATEGY_GREY &&
			p.Mode.GetGrayRounds() == p.CurrConfig.GetGrayRounds() &&
			p.Mode.GetGrayConfigTime() == p.CurrConfig.GetGrayConfigTime() {
			// 配置一致，不重置编排
			if gslice.Equal(p.Mode.GetGrayRatios(), p.CurrConfig.GetGrayRatios()) {
				return p.Stages, nil
			}
			// 如果只有灰度批次的比例变更，仅更新表单项，不覆盖编排
			// key: jobID,  value: 灰度比例
			ratioMap := make(map[string]int32)
			for i, ratio := range p.Mode.GetGrayRatios() {
				jobID := fmt.Sprintf("%s%d", constvar.FaasGrayJobID, i+1)
				ratioMap[jobID] = ratio
			}
			for _, stage := range p.Stages {
				for _, job := range stage.Jobs {
					if job.Uses == constvar.CreateFaasUpgradeUses {
						// 第一次灰度批次的比例配置在创建工单原子里面
						if p.Mode.GetGrayConfigTime() == paaspb.FaasGrayConfigTime_FAAS_GRAY_CONFIG_TIME_TEMPLATE {
							job.Inputs.Fields["grey_percentage"] = structpb.NewNumberValue(float64(p.Mode.GrayRatios[0]))
						}
						job.Inputs.Fields["gray_config_time"] = structpb.NewNumberValue(float64(p.Mode.GrayConfigTime))
						continue
					}
					ratio, ok := ratioMap[job.Id]
					if !ok {
						continue
					}
					job.Name = GetFaaSGrayJobName(ratio, p.Mode.PublishMode)
					if job.Id != GetFaaSGrayJobIDByIndex(1) {
						UpdateFaaSGrayJobField(job, ratio)
					}
				}
			}
			return p.Stages, nil
		}
	}

	tccKey := fmt.Sprintf("%s_%s", p.Mode.GetPublishMode().String(), p.Mode.GetVolumeStrategy().String())
	stages, err := u.tccRepo.GetInitFaasBasicTemplate(ctx, tccKey)
	if err != nil {
		return nil, err
	}
	// 将升级模式添加到创建 FaaS 工单原子中
	userConfirmJob, grayJob := addFaasJobs(stages, p)

	// 补齐 FaaS 灰度原子
	if p.Mode.GetVolumeStrategy() == paaspb.VolumeStrategy_VOLUME_STRATEGY_GREY && p.Mode.GetGrayRounds() > 0 {
		if userConfirmJob == nil || grayJob == nil {
			return nil, bits_err.TEMPLATE.ErrOreoUpdateTemplateBasicConfig.AddOrPass(ctx, fmt.Errorf("no user confirm job or gray job found in init faas template, tccKey: %s", tccKey))
		}
		addFaaSGrayJobs(stages, p, userConfirmJob, grayJob)
	}
	return stages, nil
}

func GetFaaSGrayJobIDByIndex(index int) string {
	return fmt.Sprintf("%s%d", constvar.FaasGrayJobID, index)
}

// 补充 FaaS 灰度原子, index 从 1 开始，第一个原子的灰度比例固定为 0
func addFaaSGrayJobs(stages []*dslpb.Stage, p *usecase.TransformFaasParams, userConfirmJob, grayJob *dslpb.Job) (err error) {
	rounds := p.Mode.GetGrayRounds()
	ratios := p.Mode.GetGrayRatios()
	for _, stage := range stages {
		jobs := make([]*dslpb.Job, 0)
		for _, job := range stage.GetJobs() {
			// 匹配到灰度原子后开始 append 新的灰度原子
			if job.Id == constvar.FaasGrayJobID {
				for i := 1; i <= int(rounds); i++ {
					ratio := ratios[i-1]
					// 给job id补个 index，方便给依赖灰度的原子添加depends on
					newGrayJob := proto.Clone(grayJob).(*dslpb.Job)
					newGrayJob.Id = GetFaaSGrayJobIDByIndex(i)
					newGrayJob.Name = GetFaaSGrayJobName(ratio, p.Mode.PublishMode)
					if i > 1 {
						LastGrayJobID := GetFaaSGrayJobIDByIndex(i - 1)
						newUserConfirmJob := proto.Clone(userConfirmJob).(*dslpb.Job)
						newUserConfirmJob.Id = fmt.Sprintf("%s%d", constvar.UserConfirmJobID, i)
						newUserConfirmJob.DependsOn = []string{LastGrayJobID}
						jobs = append(jobs, newUserConfirmJob)
						newGrayJob.DependsOn = []string{newUserConfirmJob.Id}
						UpdateFaaSGrayJobField(newGrayJob, ratio)
					}
					jobs = append(jobs, newGrayJob)
				}
			} else {
				jobs = append(jobs, job)
			}
		}
		stage.Jobs = jobs
	}
	return
}

// UpdateFaaSGrayJobField 更新 FaaS 灰度原子的表单信息
func UpdateFaaSGrayJobField(job *dslpb.Job, ratio int32) {
	if job.Inputs != nil {
		job.Inputs.Fields["grey_percent"] = structpb.NewNumberValue(float64(ratio))
	}
}

// addFaasJobs 修改创建 FaaS 工单的原子，并定位到灰度原子和用户确认原子
func addFaasJobs(stages []*dslpb.Stage, p *usecase.TransformFaasParams) (userConfirmJob, grayJob *dslpb.Job) {
	gslice.ForEach(stages, func(stage *dslpb.Stage) {
		gslice.ForEach(stage.GetJobs(), func(job *dslpb.Job) {
			if job.Uses == constvar.CreateFaasUpgradeUses {
				job.Inputs.Fields["control_plane"] = structpb.NewStringValue(GetFaasServiceRegionByControlPlane(p.Region))
				// 第一次灰度批次的比例配置在创建工单原子里面
				if p.Mode.GetGrayConfigTime() == paaspb.FaasGrayConfigTime_FAAS_GRAY_CONFIG_TIME_TEMPLATE {
					job.Inputs.Fields["grey_percentage"] = structpb.NewNumberValue(float64(p.Mode.GrayRatios[0]))
				}
				if p.Region == sharedpb.ControlPlane_CONTROL_PLANE_US_TTP {
					// US-TTP 需要额外传入 ReviewTicketID 变量
					job.Inputs.Fields["review_ticket_id"] = structpb.NewStringValue(constvar.ReviewTicketIDVarName)
				}
				job.Inputs.Fields["gray_config_time"] = structpb.NewNumberValue(float64(p.Mode.GrayConfigTime))
			}
			if job.Id == constvar.UserConfirmJobID {
				userConfirmJob = job
			}
			if job.Id == constvar.FaasGrayJobID {
				grayJob = job
			}
		})
	})
	return
}

func addExitDetectionJobAfterLastDeployJob(ctx context.Context, jobs []*dslpb.Job, controlPlane string) ([]*dslpb.Job, error) {
	deployAtoms := gslice.Filter(
		gmap.ToSlice(upgradeModeAtomMap, func(k string, v UpgradeAtom) string {
			if v.IsDeployAtom {
				return fmt.Sprintf("job_atoms/%s", v.Name)
			}
			return ""
		}), func(s string) bool {
			return s != ""
		})
	deployAtoms = append(deployAtoms, fmt.Sprintf("job_atoms/%s", constvar.ReplenishAtom))
	index := gslice.IndexRevBy(jobs, func(job *dslpb.Job) bool {
		return gslice.Contains(deployAtoms, job.Uses)
	})
	if index.IsNil() {
		logs.CtxWarn(ctx, "not found deploy atom %s", utils.JSONToString(ctx, jobs))
		return jobs, nil
	}
	lastDeployJob := jobs[index.Value()]
	exitDetection, err := getGatekeeperJob(lastDeployJob.Id, controlPlane, true)
	if err != nil {
		return nil, err
	}
	if index.Value() < len(jobs)-1 {
		jobs[index.Value()+1].DependsOn = []string{exitDetection.Id}
	}
	return append(jobs[:index.Value()+1], append([]*dslpb.Job{exitDetection}, jobs[index.Value()+1:]...)...), nil
}

func checkManual(jobType string, isDeployAtom bool, c map[string]interface{}) bool {
	if !isDeployAtom {
		return false
	}
	if jobType == constvar.UpgradeCanary {
		return false
	}
	if jobType == constvar.UpgradeCluster {
		stageType, ok := c["stages"].(string)
		if ok && stageType == "canary" {
			return false
		}
	}
	return true
}
func GetFaaSGrayJobName(ratio int32, publishMode paaspb.PublishMode) *infrapb.I18NString {
	prefixZH := operatorx.IfThen(publishMode == paaspb.PublishMode_PUBLISH_MODE_GENERAL, "FaaS 全机房灰度", "FaaS 单机房灰度")
	prefixEN := operatorx.IfThen(publishMode == paaspb.PublishMode_PUBLISH_MODE_GENERAL, "FaaS All DCs canary release", "FaaS Single DC grey")
	return &infrapb.I18NString{
		Value: fmt.Sprintf("%s(%d%%)", prefixZH, ratio),
		Lang:  "zh",
		Texts: map[string]string{
			"zh": fmt.Sprintf("%s(%d%%)", prefixZH, ratio),
			"en": fmt.Sprintf("%s(%d%%)", prefixEN, ratio),
		},
	}
}
