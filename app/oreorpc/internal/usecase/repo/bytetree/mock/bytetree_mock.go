// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package bytetree_mock is a generated GoMock package.
package bytetree_mock

import (
	context "context"
	reflect "reflect"

	schema "code.byted.org/sre/bytetree_go_sdk/schema"
	gomock "github.com/golang/mock/gomock"
)

// MockClient is a mock of client interface.
type MockClient struct {
	ctrl     *gomock.Controller
	recorder *MockClientMockRecorder
}

// MockClientMockRecorder is the mock recorder for MockClient.
type MockClientMockRecorder struct {
	mock *MockClient
}

// NewMockClient creates a new mock instance.
func NewMockClient(ctrl *gomock.Controller) *MockClient {
	mock := &MockClient{ctrl: ctrl}
	mock.recorder = &MockClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClient) EXPECT() *MockClientMockRecorder {
	return m.recorder
}

// AddTag mocks base method.
func (m *MockClient) AddTag(ctx context.Context, rid string, tag schema.Tag, userJwt *schema.UserJwt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTag", ctx, rid, tag, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddTag indicates an expected call of AddTag.
func (mr *MockClientMockRecorder) AddTag(ctx, rid, tag, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTag", reflect.TypeOf((*MockClient)(nil).AddTag), ctx, rid, tag, userJwt)
}

// BatchSearchIndexNodes mocks base method.
func (m *MockClient) BatchSearchIndexNodes(ctx context.Context, nodeQ schema.EsBatchNodeQuery, userJwt *schema.UserJwt) ([]schema.EsNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSearchIndexNodes", ctx, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.EsNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchSearchIndexNodes indicates an expected call of BatchSearchIndexNodes.
func (mr *MockClientMockRecorder) BatchSearchIndexNodes(ctx, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSearchIndexNodes", reflect.TypeOf((*MockClient)(nil).BatchSearchIndexNodes), ctx, nodeQ, userJwt)
}

// BatchSearchIndexNodesWithPage mocks base method.
func (m *MockClient) BatchSearchIndexNodesWithPage(ctx context.Context, nodeQ schema.EsBatchNodeQuery, userJwt *schema.UserJwt) ([]schema.EsNode, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchSearchIndexNodesWithPage", ctx, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.EsNode)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchSearchIndexNodesWithPage indicates an expected call of BatchSearchIndexNodesWithPage.
func (mr *MockClientMockRecorder) BatchSearchIndexNodesWithPage(ctx, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchSearchIndexNodesWithPage", reflect.TypeOf((*MockClient)(nil).BatchSearchIndexNodesWithPage), ctx, nodeQ, userJwt)
}

// CreateHybridResource mocks base method.
func (m *MockClient) CreateHybridResource(ctx context.Context, parentNode uint64, resBrief schema.ResBrief, opts ...schema.ResOption) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, parentNode, resBrief}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateHybridResource", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateHybridResource indicates an expected call of CreateHybridResource.
func (mr *MockClientMockRecorder) CreateHybridResource(ctx, parentNode, resBrief interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, parentNode, resBrief}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHybridResource", reflect.TypeOf((*MockClient)(nil).CreateHybridResource), varargs...)
}

// CreateNode mocks base method.
func (m *MockClient) CreateNode(ctx context.Context, parentNode uint64, node schema.ServiceTreeNodeBrief, userJwt *schema.UserJwt) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNode", ctx, parentNode, node, userJwt)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNode indicates an expected call of CreateNode.
func (mr *MockClientMockRecorder) CreateNode(ctx, parentNode, node, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNode", reflect.TypeOf((*MockClient)(nil).CreateNode), ctx, parentNode, node, userJwt)
}

// CreateResource mocks base method.
func (m *MockClient) CreateResource(ctx context.Context, parentNode uint64, resBrief schema.ResBrief, opts ...schema.ResOption) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, parentNode, resBrief}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateResource", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateResource indicates an expected call of CreateResource.
func (mr *MockClientMockRecorder) CreateResource(ctx, parentNode, resBrief interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, parentNode, resBrief}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateResource", reflect.TypeOf((*MockClient)(nil).CreateResource), varargs...)
}

// CreateServiceNode mocks base method.
func (m *MockClient) CreateServiceNode(ctx context.Context, parentNode uint64, node schema.ServiceTreeNode, opts ...schema.ResOption) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, parentNode, node}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateServiceNode", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServiceNode indicates an expected call of CreateServiceNode.
func (mr *MockClientMockRecorder) CreateServiceNode(ctx, parentNode, node interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, parentNode, node}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceNode", reflect.TypeOf((*MockClient)(nil).CreateServiceNode), varargs...)
}

// CreateServicePSM mocks base method.
func (m *MockClient) CreateServicePSM(ctx context.Context, parentNode uint64, request schema.CreateServicePSMRequest, opts ...schema.ResOption) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, parentNode, request}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateServicePSM", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServicePSM indicates an expected call of CreateServicePSM.
func (mr *MockClientMockRecorder) CreateServicePSM(ctx, parentNode, request interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, parentNode, request}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServicePSM", reflect.TypeOf((*MockClient)(nil).CreateServicePSM), varargs...)
}

// CreateServiceResource mocks base method.
func (m *MockClient) CreateServiceResource(ctx context.Context, nodeId uint64, resBrief schema.ResBrief, opts ...schema.ResOption) (schema.Resource, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, nodeId, resBrief}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateServiceResource", varargs...)
	ret0, _ := ret[0].(schema.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServiceResource indicates an expected call of CreateServiceResource.
func (mr *MockClientMockRecorder) CreateServiceResource(ctx, nodeId, resBrief interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, nodeId, resBrief}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceResource", reflect.TypeOf((*MockClient)(nil).CreateServiceResource), varargs...)
}

// DeleteNode mocks base method.
func (m *MockClient) DeleteNode(ctx context.Context, nodeId uint64, userJwt *string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNode", ctx, nodeId, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNode indicates an expected call of DeleteNode.
func (mr *MockClientMockRecorder) DeleteNode(ctx, nodeId, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNode", reflect.TypeOf((*MockClient)(nil).DeleteNode), ctx, nodeId, userJwt)
}

// DeleteNodeTags mocks base method.
func (m *MockClient) DeleteNodeTags(ctx context.Context, namespace string, nodeTagKeys map[uint64][]string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNodeTags", ctx, namespace, nodeTagKeys)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNodeTags indicates an expected call of DeleteNodeTags.
func (mr *MockClientMockRecorder) DeleteNodeTags(ctx, namespace, nodeTagKeys interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNodeTags", reflect.TypeOf((*MockClient)(nil).DeleteNodeTags), ctx, namespace, nodeTagKeys)
}

// DeleteResource mocks base method.
func (m *MockClient) DeleteResource(ctx context.Context, PSMIdOrPSM, provider, region, env string, opts ...schema.ResOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, PSMIdOrPSM, provider, region, env}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteResource", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteResource indicates an expected call of DeleteResource.
func (mr *MockClientMockRecorder) DeleteResource(ctx, PSMIdOrPSM, provider, region, env interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, PSMIdOrPSM, provider, region, env}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResource", reflect.TypeOf((*MockClient)(nil).DeleteResource), varargs...)
}

// DeleteResourceByBrn mocks base method.
func (m *MockClient) DeleteResourceByBrn(ctx context.Context, brn string, opts ...schema.ResOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, brn}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteResourceByBrn", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteResourceByBrn indicates an expected call of DeleteResourceByBrn.
func (mr *MockClientMockRecorder) DeleteResourceByBrn(ctx, brn interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, brn}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteResourceByBrn", reflect.TypeOf((*MockClient)(nil).DeleteResourceByBrn), varargs...)
}

// DeleteServiceNode mocks base method.
func (m *MockClient) DeleteServiceNode(ctx context.Context, nodeId uint64, opts ...schema.ResOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, nodeId}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteServiceNode", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceNode indicates an expected call of DeleteServiceNode.
func (mr *MockClientMockRecorder) DeleteServiceNode(ctx, nodeId interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, nodeId}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceNode", reflect.TypeOf((*MockClient)(nil).DeleteServiceNode), varargs...)
}

// DeleteServiceResource mocks base method.
func (m *MockClient) DeleteServiceResource(ctx context.Context, nodeId uint64, rid string, opts ...schema.ResOption) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, nodeId, rid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteServiceResource", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceResource indicates an expected call of DeleteServiceResource.
func (mr *MockClientMockRecorder) DeleteServiceResource(ctx, nodeId, rid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, nodeId, rid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceResource", reflect.TypeOf((*MockClient)(nil).DeleteServiceResource), varargs...)
}

// DeleteTag mocks base method.
func (m *MockClient) DeleteTag(ctx context.Context, rid, key string, userJwt *schema.UserJwt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteTag", ctx, rid, key, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteTag indicates an expected call of DeleteTag.
func (mr *MockClientMockRecorder) DeleteTag(ctx, rid, key, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTag", reflect.TypeOf((*MockClient)(nil).DeleteTag), ctx, rid, key, userJwt)
}

// GetIndexNode mocks base method.
func (m *MockClient) GetIndexNode(ctx context.Context, nodeId uint64, userJwt *schema.UserJwt, opt ...schema.NodeOption) (schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, nodeId, userJwt}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIndexNode", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNode indicates an expected call of GetIndexNode.
func (mr *MockClientMockRecorder) GetIndexNode(ctx, nodeId, userJwt interface{}, opt ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, nodeId, userJwt}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNode", reflect.TypeOf((*MockClient)(nil).GetIndexNode), varargs...)
}

// GetIndexNodeAllParent mocks base method.
func (m *MockClient) GetIndexNodeAllParent(ctx context.Context, nodeId uint64, userJwt *schema.UserJwt) ([]schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodeAllParent", ctx, nodeId, userJwt)
	ret0, _ := ret[0].([]schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNodeAllParent indicates an expected call of GetIndexNodeAllParent.
func (mr *MockClientMockRecorder) GetIndexNodeAllParent(ctx, nodeId, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodeAllParent", reflect.TypeOf((*MockClient)(nil).GetIndexNodeAllParent), ctx, nodeId, userJwt)
}

// GetIndexNodeChildren mocks base method.
func (m *MockClient) GetIndexNodeChildren(ctx context.Context, nodeId uint64, nodeQ schema.EsNodeQuery, userJwt *schema.UserJwt) ([]schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodeChildren", ctx, nodeId, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNodeChildren indicates an expected call of GetIndexNodeChildren.
func (mr *MockClientMockRecorder) GetIndexNodeChildren(ctx, nodeId, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodeChildren", reflect.TypeOf((*MockClient)(nil).GetIndexNodeChildren), ctx, nodeId, nodeQ, userJwt)
}

// GetIndexNodeChildrenIdList mocks base method.
func (m *MockClient) GetIndexNodeChildrenIdList(ctx context.Context, parentID uint64, q schema.NodeQ, userJwt *schema.UserJwt) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodeChildrenIdList", ctx, parentID, q, userJwt)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNodeChildrenIdList indicates an expected call of GetIndexNodeChildrenIdList.
func (mr *MockClientMockRecorder) GetIndexNodeChildrenIdList(ctx, parentID, q, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodeChildrenIdList", reflect.TypeOf((*MockClient)(nil).GetIndexNodeChildrenIdList), ctx, parentID, q, userJwt)
}

// GetIndexNodeChildrenWithPage mocks base method.
func (m *MockClient) GetIndexNodeChildrenWithPage(ctx context.Context, nodeId uint64, nodeQ schema.EsNodeQuery, userJwt *schema.UserJwt) ([]schema.EsNode, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodeChildrenWithPage", ctx, nodeId, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.EsNode)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetIndexNodeChildrenWithPage indicates an expected call of GetIndexNodeChildrenWithPage.
func (mr *MockClientMockRecorder) GetIndexNodeChildrenWithPage(ctx, nodeId, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodeChildrenWithPage", reflect.TypeOf((*MockClient)(nil).GetIndexNodeChildrenWithPage), ctx, nodeId, nodeQ, userJwt)
}

// GetIndexNodeRes mocks base method.
func (m *MockClient) GetIndexNodeRes(ctx context.Context, nodeId uint64, resQ schema.EsResQuery, userJwt *schema.UserJwt) ([]schema.Resource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodeRes", ctx, nodeId, resQ, userJwt)
	ret0, _ := ret[0].([]schema.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNodeRes indicates an expected call of GetIndexNodeRes.
func (mr *MockClientMockRecorder) GetIndexNodeRes(ctx, nodeId, resQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodeRes", reflect.TypeOf((*MockClient)(nil).GetIndexNodeRes), ctx, nodeId, resQ, userJwt)
}

// GetIndexNodeResWithNodePage mocks base method.
func (m *MockClient) GetIndexNodeResWithNodePage(ctx context.Context, nodeId uint64, resQ schema.EsResQuery, userJwt *schema.UserJwt) ([]schema.EsResource, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodeResWithNodePage", ctx, nodeId, resQ, userJwt)
	ret0, _ := ret[0].([]schema.EsResource)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetIndexNodeResWithNodePage indicates an expected call of GetIndexNodeResWithNodePage.
func (mr *MockClientMockRecorder) GetIndexNodeResWithNodePage(ctx, nodeId, resQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodeResWithNodePage", reflect.TypeOf((*MockClient)(nil).GetIndexNodeResWithNodePage), ctx, nodeId, resQ, userJwt)
}

// GetIndexNodes mocks base method.
func (m *MockClient) GetIndexNodes(ctx context.Context, nodeQ schema.EsNodeQuery, userJwt *schema.UserJwt) ([]schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodes", ctx, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNodes indicates an expected call of GetIndexNodes.
func (mr *MockClientMockRecorder) GetIndexNodes(ctx, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodes", reflect.TypeOf((*MockClient)(nil).GetIndexNodes), ctx, nodeQ, userJwt)
}

// GetIndexNodesByIdList mocks base method.
func (m *MockClient) GetIndexNodesByIdList(ctx context.Context, IdList []uint64, userJwt *schema.UserJwt) ([]schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodesByIdList", ctx, IdList, userJwt)
	ret0, _ := ret[0].([]schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIndexNodesByIdList indicates an expected call of GetIndexNodesByIdList.
func (mr *MockClientMockRecorder) GetIndexNodesByIdList(ctx, IdList, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodesByIdList", reflect.TypeOf((*MockClient)(nil).GetIndexNodesByIdList), ctx, IdList, userJwt)
}

// GetIndexNodesWithPage mocks base method.
func (m *MockClient) GetIndexNodesWithPage(ctx context.Context, nodeQ schema.EsNodeQuery, userJwt *schema.UserJwt) ([]schema.EsNode, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIndexNodesWithPage", ctx, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.EsNode)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetIndexNodesWithPage indicates an expected call of GetIndexNodesWithPage.
func (mr *MockClientMockRecorder) GetIndexNodesWithPage(ctx, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIndexNodesWithPage", reflect.TypeOf((*MockClient)(nil).GetIndexNodesWithPage), ctx, nodeQ, userJwt)
}

// GetNode mocks base method.
func (m *MockClient) GetNode(ctx context.Context, nodeId uint64) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNode", ctx, nodeId)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNode indicates an expected call of GetNode.
func (mr *MockClientMockRecorder) GetNode(ctx, nodeId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNode", reflect.TypeOf((*MockClient)(nil).GetNode), ctx, nodeId)
}

// GetNodeByID mocks base method.
func (m *MockClient) GetNodeByID(ctx context.Context, nodeId uint64) (schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeByID", ctx, nodeId)
	ret0, _ := ret[0].(schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeByID indicates an expected call of GetNodeByID.
func (mr *MockClientMockRecorder) GetNodeByID(ctx, nodeId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeByID", reflect.TypeOf((*MockClient)(nil).GetNodeByID), ctx, nodeId)
}

// GetNodeByPSM mocks base method.
func (m *MockClient) GetNodeByPSM(ctx context.Context, psm string, userJwt *schema.UserJwt, opt ...schema.NodeOption) (schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, psm, userJwt}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNodeByPSM", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeByPSM indicates an expected call of GetNodeByPSM.
func (mr *MockClientMockRecorder) GetNodeByPSM(ctx, psm, userJwt interface{}, opt ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, psm, userJwt}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeByPSM", reflect.TypeOf((*MockClient)(nil).GetNodeByPSM), varargs...)
}

// GetNodeByPath mocks base method.
func (m *MockClient) GetNodeByPath(ctx context.Context, path string, userJwt *schema.UserJwt, opt ...schema.NodeOption) (schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, path, userJwt}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNodeByPath", varargs...)
	ret0, _ := ret[0].(schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeByPath indicates an expected call of GetNodeByPath.
func (mr *MockClientMockRecorder) GetNodeByPath(ctx, path, userJwt interface{}, opt ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, path, userJwt}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeByPath", reflect.TypeOf((*MockClient)(nil).GetNodeByPath), varargs...)
}

// GetNodeChildren mocks base method.
func (m *MockClient) GetNodeChildren(ctx context.Context, nodeId uint64, nodeQ schema.NodeQuery) ([]schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeChildren", ctx, nodeId, nodeQ)
	ret0, _ := ret[0].([]schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodeChildren indicates an expected call of GetNodeChildren.
func (mr *MockClientMockRecorder) GetNodeChildren(ctx, nodeId, nodeQ interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeChildren", reflect.TypeOf((*MockClient)(nil).GetNodeChildren), ctx, nodeId, nodeQ)
}

// GetNodeChildrenWithPage mocks base method.
func (m *MockClient) GetNodeChildrenWithPage(ctx context.Context, nodeId uint64, nodeQ schema.NodeQuery) ([]schema.ServiceTreeNode, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodeChildrenWithPage", ctx, nodeId, nodeQ)
	ret0, _ := ret[0].([]schema.ServiceTreeNode)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetNodeChildrenWithPage indicates an expected call of GetNodeChildrenWithPage.
func (mr *MockClientMockRecorder) GetNodeChildrenWithPage(ctx, nodeId, nodeQ interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodeChildrenWithPage", reflect.TypeOf((*MockClient)(nil).GetNodeChildrenWithPage), ctx, nodeId, nodeQ)
}

// GetNodesByName mocks base method.
func (m *MockClient) GetNodesByName(ctx context.Context, name string, userJwt *schema.UserJwt) ([]schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNodesByName", ctx, name, userJwt)
	ret0, _ := ret[0].([]schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNodesByName indicates an expected call of GetNodesByName.
func (mr *MockClientMockRecorder) GetNodesByName(ctx, name, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNodesByName", reflect.TypeOf((*MockClient)(nil).GetNodesByName), ctx, name, userJwt)
}

// GetResources mocks base method.
func (m *MockClient) GetResources(ctx context.Context, resQ schema.ResourceQuery, opts ...schema.ResOption) ([]schema.Resource, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, resQ}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetResources", varargs...)
	ret0, _ := ret[0].([]schema.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetResources indicates an expected call of GetResources.
func (mr *MockClientMockRecorder) GetResources(ctx, resQ interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, resQ}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResources", reflect.TypeOf((*MockClient)(nil).GetResources), varargs...)
}

// GetResourcesWithPage mocks base method.
func (m *MockClient) GetResourcesWithPage(ctx context.Context, resQ schema.ResourceQuery, opts ...schema.ResOption) ([]schema.Resource, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, resQ}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetResourcesWithPage", varargs...)
	ret0, _ := ret[0].([]schema.Resource)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetResourcesWithPage indicates an expected call of GetResourcesWithPage.
func (mr *MockClientMockRecorder) GetResourcesWithPage(ctx, resQ interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, resQ}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetResourcesWithPage", reflect.TypeOf((*MockClient)(nil).GetResourcesWithPage), varargs...)
}

// GetSubscribeNodes mocks base method.
func (m *MockClient) GetSubscribeNodes(ctx context.Context, jwt schema.UserJwt) ([]schema.ServiceTreeNodeDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubscribeNodes", ctx, jwt)
	ret0, _ := ret[0].([]schema.ServiceTreeNodeDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSubscribeNodes indicates an expected call of GetSubscribeNodes.
func (mr *MockClientMockRecorder) GetSubscribeNodes(ctx, jwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubscribeNodes", reflect.TypeOf((*MockClient)(nil).GetSubscribeNodes), ctx, jwt)
}

// Is404Error mocks base method.
func (m *MockClient) Is404Error(err error) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Is404Error", err)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Is404Error indicates an expected call of Is404Error.
func (mr *MockClientMockRecorder) Is404Error(err interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Is404Error", reflect.TypeOf((*MockClient)(nil).Is404Error), err)
}

// IsDuplicateError mocks base method.
func (m *MockClient) IsDuplicateError(err error) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsDuplicateError", err)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsDuplicateError indicates an expected call of IsDuplicateError.
func (mr *MockClientMockRecorder) IsDuplicateError(err interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsDuplicateError", reflect.TypeOf((*MockClient)(nil).IsDuplicateError), err)
}

// MigrateNode mocks base method.
func (m *MockClient) MigrateNode(ctx context.Context, migratedNode, newParentNode uint64, userJwt *schema.UserJwt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MigrateNode", ctx, migratedNode, newParentNode, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// MigrateNode indicates an expected call of MigrateNode.
func (mr *MockClientMockRecorder) MigrateNode(ctx, migratedNode, newParentNode, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MigrateNode", reflect.TypeOf((*MockClient)(nil).MigrateNode), ctx, migratedNode, newParentNode, userJwt)
}

// QueryNodes mocks base method.
func (m *MockClient) QueryNodes(ctx context.Context, nodeQ schema.NodeQuery) ([]schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryNodes", ctx, nodeQ)
	ret0, _ := ret[0].([]schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryNodes indicates an expected call of QueryNodes.
func (mr *MockClientMockRecorder) QueryNodes(ctx, nodeQ interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryNodes", reflect.TypeOf((*MockClient)(nil).QueryNodes), ctx, nodeQ)
}

// QueryNodesWithPage mocks base method.
func (m *MockClient) QueryNodesWithPage(ctx context.Context, nodeQ schema.NodeQuery) ([]schema.ServiceTreeNode, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryNodesWithPage", ctx, nodeQ)
	ret0, _ := ret[0].([]schema.ServiceTreeNode)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// QueryNodesWithPage indicates an expected call of QueryNodesWithPage.
func (mr *MockClientMockRecorder) QueryNodesWithPage(ctx, nodeQ interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryNodesWithPage", reflect.TypeOf((*MockClient)(nil).QueryNodesWithPage), ctx, nodeQ)
}

// SearchIndexNodes mocks base method.
func (m *MockClient) SearchIndexNodes(ctx context.Context, nodeQ schema.EsNodeQuery, userJwt *schema.UserJwt) ([]schema.EsNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchIndexNodes", ctx, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.EsNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchIndexNodes indicates an expected call of SearchIndexNodes.
func (mr *MockClientMockRecorder) SearchIndexNodes(ctx, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchIndexNodes", reflect.TypeOf((*MockClient)(nil).SearchIndexNodes), ctx, nodeQ, userJwt)
}

// SearchIndexNodesWithPage mocks base method.
func (m *MockClient) SearchIndexNodesWithPage(ctx context.Context, nodeQ schema.EsNodeQuery, userJwt *schema.UserJwt) ([]schema.EsNode, schema.PageInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchIndexNodesWithPage", ctx, nodeQ, userJwt)
	ret0, _ := ret[0].([]schema.EsNode)
	ret1, _ := ret[1].(schema.PageInfo)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SearchIndexNodesWithPage indicates an expected call of SearchIndexNodesWithPage.
func (mr *MockClientMockRecorder) SearchIndexNodesWithPage(ctx, nodeQ, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchIndexNodesWithPage", reflect.TypeOf((*MockClient)(nil).SearchIndexNodesWithPage), ctx, nodeQ, userJwt)
}

// SearchNodes mocks base method.
func (m *MockClient) SearchNodes(ctx context.Context, s schema.FuzzySearch, userJwt *schema.UserJwt) ([]schema.NodeBrief, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchNodes", ctx, s, userJwt)
	ret0, _ := ret[0].([]schema.NodeBrief)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchNodes indicates an expected call of SearchNodes.
func (mr *MockClientMockRecorder) SearchNodes(ctx, s, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchNodes", reflect.TypeOf((*MockClient)(nil).SearchNodes), ctx, s, userJwt)
}

// Subscribe mocks base method.
func (m *MockClient) Subscribe(ctx context.Context, nodeIds []uint64, userJwt schema.UserJwt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Subscribe", ctx, nodeIds, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// Subscribe indicates an expected call of Subscribe.
func (mr *MockClientMockRecorder) Subscribe(ctx, nodeIds, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Subscribe", reflect.TypeOf((*MockClient)(nil).Subscribe), ctx, nodeIds, userJwt)
}

// UnSubscribe mocks base method.
func (m *MockClient) UnSubscribe(ctx context.Context, nodeId uint64, userJwt schema.UserJwt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnSubscribe", ctx, nodeId, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnSubscribe indicates an expected call of UnSubscribe.
func (mr *MockClientMockRecorder) UnSubscribe(ctx, nodeId, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnSubscribe", reflect.TypeOf((*MockClient)(nil).UnSubscribe), ctx, nodeId, userJwt)
}

// UpdateNode mocks base method.
func (m *MockClient) UpdateNode(ctx context.Context, nodeId uint64, node schema.ServiceTreeNodeBrief, userJwt *schema.UserJwt) (schema.ServiceTreeNode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNode", ctx, nodeId, node, userJwt)
	ret0, _ := ret[0].(schema.ServiceTreeNode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateNode indicates an expected call of UpdateNode.
func (mr *MockClientMockRecorder) UpdateNode(ctx, nodeId, node, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNode", reflect.TypeOf((*MockClient)(nil).UpdateNode), ctx, nodeId, node, userJwt)
}

// UpdateResource mocks base method.
func (m *MockClient) UpdateResource(ctx context.Context, oldRes schema.ResUniq, reqResource schema.UpdateRes, opts ...schema.ResOption) (schema.Resource, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, oldRes, reqResource}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateResource", varargs...)
	ret0, _ := ret[0].(schema.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResource indicates an expected call of UpdateResource.
func (mr *MockClientMockRecorder) UpdateResource(ctx, oldRes, reqResource interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, oldRes, reqResource}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResource", reflect.TypeOf((*MockClient)(nil).UpdateResource), varargs...)
}

// UpdateResourceByBRN mocks base method.
func (m *MockClient) UpdateResourceByBRN(ctx context.Context, brn string, reqRes schema.UpdateResReq, opts ...schema.ResOption) (schema.Resource, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, brn, reqRes}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateResourceByBRN", varargs...)
	ret0, _ := ret[0].(schema.Resource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateResourceByBRN indicates an expected call of UpdateResourceByBRN.
func (mr *MockClientMockRecorder) UpdateResourceByBRN(ctx, brn, reqRes interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, brn, reqRes}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateResourceByBRN", reflect.TypeOf((*MockClient)(nil).UpdateResourceByBRN), varargs...)
}

// UpsertNodeTags mocks base method.
func (m *MockClient) UpsertNodeTags(ctx context.Context, namespace string, nodeTags map[uint64]map[string]string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertNodeTags", ctx, namespace, nodeTags)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpsertNodeTags indicates an expected call of UpsertNodeTags.
func (mr *MockClientMockRecorder) UpsertNodeTags(ctx, namespace, nodeTags interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertNodeTags", reflect.TypeOf((*MockClient)(nil).UpsertNodeTags), ctx, namespace, nodeTags)
}

// UpsertTag mocks base method.
func (m *MockClient) UpsertTag(ctx context.Context, rid string, tag schema.Tag, userJwt *schema.UserJwt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertTag", ctx, rid, tag, userJwt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertTag indicates an expected call of UpsertTag.
func (mr *MockClientMockRecorder) UpsertTag(ctx, rid, tag, userJwt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertTag", reflect.TypeOf((*MockClient)(nil).UpsertTag), ctx, rid, tag, userJwt)
}
