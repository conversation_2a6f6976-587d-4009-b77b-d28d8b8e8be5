package translation

import (
	"code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/translationpb/translationservice"
	"code.byted.org/devinfra/hagrid/pbgen/rpc/bits/translationrpc"
	"code.byted.org/kite/kitex/client"
)

var cli translationservice.Client

func NewTranslationCli() translationservice.Client {
	c := GetClient()
	return c
}

func GetClient() translationservice.Client {
	if cli == nil {
		opts := []client.Option{
			client.WithMiddleware(kitexmw.LogClientSideRequestResponse),
		}
		cli = translationservice.MustNewClient(translationrpc.PSM, opts...)
	}
	return cli
}
