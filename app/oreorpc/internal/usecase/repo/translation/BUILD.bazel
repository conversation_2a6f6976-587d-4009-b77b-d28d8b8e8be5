load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "translation",
    srcs = ["base.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/oreorpc/internal/usecase/repo/translation",
    visibility = ["//app/oreorpc:__subpackages__"],
    deps = [
        "//idls/byted/devinfra/translation:translation_go_proto_kitexcli_TranslationService",
        "//idls/byted/devinfra/translation:translation_go_proto_xrpc_and_kitex_TranslationService",
        "//libs/middleware/kitexmw",
        "@org_byted_code_kite_kitex//client",
    ],
)
