// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package cd

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"code.byted.org/devinfra/hagrid/app/oreorpc/internal/entity/model/cdmodel"
)

func newHagridCdWorkflow(db *gorm.DB, opts ...gen.DOOption) hagridCdWorkflow {
	_hagridCdWorkflow := hagridCdWorkflow{}

	_hagridCdWorkflow.hagridCdWorkflowDo.UseDB(db, opts...)
	_hagridCdWorkflow.hagridCdWorkflowDo.UseModel(&cdmodel.HagridCdWorkflow{})

	tableName := _hagridCdWorkflow.hagridCdWorkflowDo.TableName()
	_hagridCdWorkflow.ALL = field.NewAsterisk(tableName)
	_hagridCdWorkflow.ID = field.NewUint64(tableName, "id")
	_hagridCdWorkflow.WorkflowID = field.NewUint64(tableName, "workflow_id")
	_hagridCdWorkflow.Name = field.NewString(tableName, "name")
	_hagridCdWorkflow.NameI18n = field.NewString(tableName, "name_i18n")
	_hagridCdWorkflow.Description = field.NewString(tableName, "description")
	_hagridCdWorkflow.DescriptionI18n = field.NewString(tableName, "description_i18n")
	_hagridCdWorkflow.CopyFromID = field.NewUint64(tableName, "copy_from_id")
	_hagridCdWorkflow.WorkspaceID = field.NewUint64(tableName, "workspace_id")
	_hagridCdWorkflow.IsDefault = field.NewInt32(tableName, "is_default")
	_hagridCdWorkflow.IsEnabled = field.NewBool(tableName, "is_enabled")
	_hagridCdWorkflow.Orchestration = field.NewString(tableName, "orchestration")
	_hagridCdWorkflow.WorkflowConfig = field.NewString(tableName, "workflow_config")
	_hagridCdWorkflow.Version = field.NewUint64(tableName, "version")
	_hagridCdWorkflow.CreatedAt = field.NewTime(tableName, "created_at")
	_hagridCdWorkflow.UpdatedAt = field.NewTime(tableName, "updated_at")
	_hagridCdWorkflow.DeletedAt = field.NewField(tableName, "deleted_at")
	_hagridCdWorkflow.Creator = field.NewString(tableName, "creator")
	_hagridCdWorkflow.Updater = field.NewString(tableName, "updater")
	_hagridCdWorkflow.WorkflowType = field.NewInt32(tableName, "workflow_type")

	_hagridCdWorkflow.fillFieldMap()

	return _hagridCdWorkflow
}

// hagridCdWorkflow 发布单流程模板表
type hagridCdWorkflow struct {
	hagridCdWorkflowDo hagridCdWorkflowDo

	ALL             field.Asterisk
	ID              field.Uint64 // 主键
	WorkflowID      field.Uint64 // WorkflowID
	Name            field.String // 名称
	NameI18n        field.String // 英文名称
	Description     field.String // 描述
	DescriptionI18n field.String // 英文描述
	CopyFromID      field.Uint64 // 从该Workflow复制而来
	WorkspaceID     field.Uint64 // 团队空间ID
	IsDefault       field.Int32  // 是否默认流程模板. 0: false 1: true
	IsEnabled       field.Bool   // 是否已开启
	Orchestration   field.String // 流程编排信息
	WorkflowConfig  field.String // 流程模板配置信息
	Version         field.Uint64 // 版本
	CreatedAt       field.Time   // 创建时间
	UpdatedAt       field.Time   // 更新时间
	DeletedAt       field.Field  // 删除时间
	Creator         field.String // 创建人
	Updater         field.String // 更新人
	WorkflowType    field.Int32  // 流程模板类型

	fieldMap map[string]field.Expr
}

func (h hagridCdWorkflow) Table(newTableName string) *hagridCdWorkflow {
	h.hagridCdWorkflowDo.UseTable(newTableName)
	return h.updateTableName(newTableName)
}

func (h hagridCdWorkflow) As(alias string) *hagridCdWorkflow {
	h.hagridCdWorkflowDo.DO = *(h.hagridCdWorkflowDo.As(alias).(*gen.DO))
	return h.updateTableName(alias)
}

func (h *hagridCdWorkflow) updateTableName(table string) *hagridCdWorkflow {
	h.ALL = field.NewAsterisk(table)
	h.ID = field.NewUint64(table, "id")
	h.WorkflowID = field.NewUint64(table, "workflow_id")
	h.Name = field.NewString(table, "name")
	h.NameI18n = field.NewString(table, "name_i18n")
	h.Description = field.NewString(table, "description")
	h.DescriptionI18n = field.NewString(table, "description_i18n")
	h.CopyFromID = field.NewUint64(table, "copy_from_id")
	h.WorkspaceID = field.NewUint64(table, "workspace_id")
	h.IsDefault = field.NewInt32(table, "is_default")
	h.IsEnabled = field.NewBool(table, "is_enabled")
	h.Orchestration = field.NewString(table, "orchestration")
	h.WorkflowConfig = field.NewString(table, "workflow_config")
	h.Version = field.NewUint64(table, "version")
	h.CreatedAt = field.NewTime(table, "created_at")
	h.UpdatedAt = field.NewTime(table, "updated_at")
	h.DeletedAt = field.NewField(table, "deleted_at")
	h.Creator = field.NewString(table, "creator")
	h.Updater = field.NewString(table, "updater")
	h.WorkflowType = field.NewInt32(table, "workflow_type")

	h.fillFieldMap()

	return h
}

func (h *hagridCdWorkflow) WithContext(ctx context.Context) IHagridCdWorkflowDo {
	return h.hagridCdWorkflowDo.WithContext(ctx)
}

func (h hagridCdWorkflow) TableName() string { return h.hagridCdWorkflowDo.TableName() }

func (h hagridCdWorkflow) Alias() string { return h.hagridCdWorkflowDo.Alias() }

func (h hagridCdWorkflow) Columns(cols ...field.Expr) gen.Columns {
	return h.hagridCdWorkflowDo.Columns(cols...)
}

func (h *hagridCdWorkflow) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := h.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (h *hagridCdWorkflow) fillFieldMap() {
	h.fieldMap = make(map[string]field.Expr, 19)
	h.fieldMap["id"] = h.ID
	h.fieldMap["workflow_id"] = h.WorkflowID
	h.fieldMap["name"] = h.Name
	h.fieldMap["name_i18n"] = h.NameI18n
	h.fieldMap["description"] = h.Description
	h.fieldMap["description_i18n"] = h.DescriptionI18n
	h.fieldMap["copy_from_id"] = h.CopyFromID
	h.fieldMap["workspace_id"] = h.WorkspaceID
	h.fieldMap["is_default"] = h.IsDefault
	h.fieldMap["is_enabled"] = h.IsEnabled
	h.fieldMap["orchestration"] = h.Orchestration
	h.fieldMap["workflow_config"] = h.WorkflowConfig
	h.fieldMap["version"] = h.Version
	h.fieldMap["created_at"] = h.CreatedAt
	h.fieldMap["updated_at"] = h.UpdatedAt
	h.fieldMap["deleted_at"] = h.DeletedAt
	h.fieldMap["creator"] = h.Creator
	h.fieldMap["updater"] = h.Updater
	h.fieldMap["workflow_type"] = h.WorkflowType
}

func (h hagridCdWorkflow) clone(db *gorm.DB) hagridCdWorkflow {
	h.hagridCdWorkflowDo.ReplaceConnPool(db.Statement.ConnPool)
	return h
}

func (h hagridCdWorkflow) replaceDB(db *gorm.DB) hagridCdWorkflow {
	h.hagridCdWorkflowDo.ReplaceDB(db)
	return h
}

type hagridCdWorkflowDo struct{ gen.DO }

type IHagridCdWorkflowDo interface {
	gen.SubQuery
	Debug() IHagridCdWorkflowDo
	WithContext(ctx context.Context) IHagridCdWorkflowDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IHagridCdWorkflowDo
	WriteDB() IHagridCdWorkflowDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IHagridCdWorkflowDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IHagridCdWorkflowDo
	Not(conds ...gen.Condition) IHagridCdWorkflowDo
	Or(conds ...gen.Condition) IHagridCdWorkflowDo
	Select(conds ...field.Expr) IHagridCdWorkflowDo
	Where(conds ...gen.Condition) IHagridCdWorkflowDo
	Order(conds ...field.Expr) IHagridCdWorkflowDo
	Distinct(cols ...field.Expr) IHagridCdWorkflowDo
	Omit(cols ...field.Expr) IHagridCdWorkflowDo
	Join(table schema.Tabler, on ...field.Expr) IHagridCdWorkflowDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IHagridCdWorkflowDo
	RightJoin(table schema.Tabler, on ...field.Expr) IHagridCdWorkflowDo
	Group(cols ...field.Expr) IHagridCdWorkflowDo
	Having(conds ...gen.Condition) IHagridCdWorkflowDo
	Limit(limit int) IHagridCdWorkflowDo
	Offset(offset int) IHagridCdWorkflowDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IHagridCdWorkflowDo
	Unscoped() IHagridCdWorkflowDo
	Create(values ...*cdmodel.HagridCdWorkflow) error
	CreateInBatches(values []*cdmodel.HagridCdWorkflow, batchSize int) error
	Save(values ...*cdmodel.HagridCdWorkflow) error
	First() (*cdmodel.HagridCdWorkflow, error)
	Take() (*cdmodel.HagridCdWorkflow, error)
	Last() (*cdmodel.HagridCdWorkflow, error)
	Find() ([]*cdmodel.HagridCdWorkflow, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*cdmodel.HagridCdWorkflow, err error)
	FindInBatches(result *[]*cdmodel.HagridCdWorkflow, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*cdmodel.HagridCdWorkflow) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IHagridCdWorkflowDo
	Assign(attrs ...field.AssignExpr) IHagridCdWorkflowDo
	Joins(fields ...field.RelationField) IHagridCdWorkflowDo
	Preload(fields ...field.RelationField) IHagridCdWorkflowDo
	FirstOrInit() (*cdmodel.HagridCdWorkflow, error)
	FirstOrCreate() (*cdmodel.HagridCdWorkflow, error)
	FindByPage(offset int, limit int) (result []*cdmodel.HagridCdWorkflow, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IHagridCdWorkflowDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (h hagridCdWorkflowDo) Debug() IHagridCdWorkflowDo {
	return h.withDO(h.DO.Debug())
}

func (h hagridCdWorkflowDo) WithContext(ctx context.Context) IHagridCdWorkflowDo {
	return h.withDO(h.DO.WithContext(ctx))
}

func (h hagridCdWorkflowDo) ReadDB() IHagridCdWorkflowDo {
	return h.Clauses(dbresolver.Read)
}

func (h hagridCdWorkflowDo) WriteDB() IHagridCdWorkflowDo {
	return h.Clauses(dbresolver.Write)
}

func (h hagridCdWorkflowDo) Session(config *gorm.Session) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Session(config))
}

func (h hagridCdWorkflowDo) Clauses(conds ...clause.Expression) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Clauses(conds...))
}

func (h hagridCdWorkflowDo) Returning(value interface{}, columns ...string) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Returning(value, columns...))
}

func (h hagridCdWorkflowDo) Not(conds ...gen.Condition) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Not(conds...))
}

func (h hagridCdWorkflowDo) Or(conds ...gen.Condition) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Or(conds...))
}

func (h hagridCdWorkflowDo) Select(conds ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Select(conds...))
}

func (h hagridCdWorkflowDo) Where(conds ...gen.Condition) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Where(conds...))
}

func (h hagridCdWorkflowDo) Order(conds ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Order(conds...))
}

func (h hagridCdWorkflowDo) Distinct(cols ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Distinct(cols...))
}

func (h hagridCdWorkflowDo) Omit(cols ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Omit(cols...))
}

func (h hagridCdWorkflowDo) Join(table schema.Tabler, on ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Join(table, on...))
}

func (h hagridCdWorkflowDo) LeftJoin(table schema.Tabler, on ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.LeftJoin(table, on...))
}

func (h hagridCdWorkflowDo) RightJoin(table schema.Tabler, on ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.RightJoin(table, on...))
}

func (h hagridCdWorkflowDo) Group(cols ...field.Expr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Group(cols...))
}

func (h hagridCdWorkflowDo) Having(conds ...gen.Condition) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Having(conds...))
}

func (h hagridCdWorkflowDo) Limit(limit int) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Limit(limit))
}

func (h hagridCdWorkflowDo) Offset(offset int) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Offset(offset))
}

func (h hagridCdWorkflowDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Scopes(funcs...))
}

func (h hagridCdWorkflowDo) Unscoped() IHagridCdWorkflowDo {
	return h.withDO(h.DO.Unscoped())
}

func (h hagridCdWorkflowDo) Create(values ...*cdmodel.HagridCdWorkflow) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Create(values)
}

func (h hagridCdWorkflowDo) CreateInBatches(values []*cdmodel.HagridCdWorkflow, batchSize int) error {
	return h.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (h hagridCdWorkflowDo) Save(values ...*cdmodel.HagridCdWorkflow) error {
	if len(values) == 0 {
		return nil
	}
	return h.DO.Save(values)
}

func (h hagridCdWorkflowDo) First() (*cdmodel.HagridCdWorkflow, error) {
	if result, err := h.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*cdmodel.HagridCdWorkflow), nil
	}
}

func (h hagridCdWorkflowDo) Take() (*cdmodel.HagridCdWorkflow, error) {
	if result, err := h.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*cdmodel.HagridCdWorkflow), nil
	}
}

func (h hagridCdWorkflowDo) Last() (*cdmodel.HagridCdWorkflow, error) {
	if result, err := h.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*cdmodel.HagridCdWorkflow), nil
	}
}

func (h hagridCdWorkflowDo) Find() ([]*cdmodel.HagridCdWorkflow, error) {
	result, err := h.DO.Find()
	return result.([]*cdmodel.HagridCdWorkflow), err
}

func (h hagridCdWorkflowDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*cdmodel.HagridCdWorkflow, err error) {
	buf := make([]*cdmodel.HagridCdWorkflow, 0, batchSize)
	err = h.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (h hagridCdWorkflowDo) FindInBatches(result *[]*cdmodel.HagridCdWorkflow, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return h.DO.FindInBatches(result, batchSize, fc)
}

func (h hagridCdWorkflowDo) Attrs(attrs ...field.AssignExpr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Attrs(attrs...))
}

func (h hagridCdWorkflowDo) Assign(attrs ...field.AssignExpr) IHagridCdWorkflowDo {
	return h.withDO(h.DO.Assign(attrs...))
}

func (h hagridCdWorkflowDo) Joins(fields ...field.RelationField) IHagridCdWorkflowDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Joins(_f))
	}
	return &h
}

func (h hagridCdWorkflowDo) Preload(fields ...field.RelationField) IHagridCdWorkflowDo {
	for _, _f := range fields {
		h = *h.withDO(h.DO.Preload(_f))
	}
	return &h
}

func (h hagridCdWorkflowDo) FirstOrInit() (*cdmodel.HagridCdWorkflow, error) {
	if result, err := h.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*cdmodel.HagridCdWorkflow), nil
	}
}

func (h hagridCdWorkflowDo) FirstOrCreate() (*cdmodel.HagridCdWorkflow, error) {
	if result, err := h.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*cdmodel.HagridCdWorkflow), nil
	}
}

func (h hagridCdWorkflowDo) FindByPage(offset int, limit int) (result []*cdmodel.HagridCdWorkflow, count int64, err error) {
	result, err = h.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = h.Offset(-1).Limit(-1).Count()
	return
}

func (h hagridCdWorkflowDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = h.Count()
	if err != nil {
		return
	}

	err = h.Offset(offset).Limit(limit).Scan(result)
	return
}

func (h hagridCdWorkflowDo) Scan(result interface{}) (err error) {
	return h.DO.Scan(result)
}

func (h hagridCdWorkflowDo) Delete(models ...*cdmodel.HagridCdWorkflow) (result gen.ResultInfo, err error) {
	return h.DO.Delete(models)
}

func (h *hagridCdWorkflowDo) withDO(do gen.Dao) *hagridCdWorkflowDo {
	h.DO = *do.(*gen.DO)
	return h
}
