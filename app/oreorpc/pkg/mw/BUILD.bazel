load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "mw",
    srcs = [
        "limiter.go",
        "log.go",
        "region_mark.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/oreorpc/pkg/mw",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cdrpc/utils",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//internal/pipeline/constvar",
        "//internal/pipeline/limiter",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_metainfo//:metainfo",
        "@org_byted_code_iesarch_paas_sdk//util/trace/regionmark",
        "@org_byted_code_kite_kitex//pkg/endpoint",
        "@org_byted_code_kite_kitex//pkg/rpcinfo",
        "@org_byted_code_kite_kitex//pkg/streaming",
    ],
)

go_test(
    name = "mw_test",
    srcs = [
        "limiter_test.go",
        "log_test.go",
        "region_mark_test.go",
    ],
    embed = [":mw"],
    deps = [
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//internal/pipeline/limiter",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_bits_monkey//:monkey",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_metainfo//:metainfo",
        "@org_byted_code_iesarch_paas_sdk//util/trace/regionmark",
        "@org_byted_code_iesarch_simple_rate_limit_v2//:simple_rate_limit",
    ],
)
