package convert

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
)

// TranslateSharedCpToDSLCp 将 sharedpb 中的 control_plane 翻译为流水线的控制面打标
func TranslateSharedCpToDSLCp(p sharedpb.ControlPlane) dslpb.ControlPanel {
	switch p {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return dslpb.ControlPanel_CONTROL_PANEL_CN
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		return dslpb.ControlPanel_CONTROL_PANEL_CN
	case sharedpb.ControlPlane_CONTROL_PLANE_TTP:
		return dslpb.ControlPanel_CONTROL_PANEL_CN
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return dslpb.ControlPanel_CONTROL_PANEL_EUTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return dslpb.ControlPanel_CONTROL_PANEL_USTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED:
		return dslpb.ControlPanel_CONTROL_PANEL_CN
	default:
		return dslpb.ControlPanel_CONTROL_PANEL_UNSPECIFIED
	}
}

func SharedCpToString(p sharedpb.ControlPlane) string {
	switch p {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return "cn"
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		return "i18n"
	case sharedpb.ControlPlane_CONTROL_PLANE_TTP:
		return "ttp"
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return "eu_ttp"
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return "us_ttp"
	default:
		return ""
	}
}

// DSLCpToAtomCP 将流水线控制面转换为原子控制面
func DSLCpToAtomCP(cp dslpb.ControlPanel) atommanagepb.RegionEnum {
	return atommanagepb.RegionEnum(cp)
}
