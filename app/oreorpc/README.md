## 服务开发指南
https://bytedance.feishu.cn/docx/Obl1d0k7Xo9K4QxeMffcmPaHnxd#KV0CdxMnSo8OoIxsvpYc7izFnXf

## 工程架构
### `cmd/app/main.go`
初始化各个能力

### `conf`
配置，首先读取 `config.yml`中的内容，如果环境变量里面有符合的变量，将其覆盖yml中的配置
配置的结构在 `config.go`中
`env-required:true` 标签强制你指定值（在yml文件或者环境变量中）

对于配置，我们选择[cleanenv](https://github.com/ilyakaznacheev/cleanenv) 库
cleanenv没有很多的starts数，但是它简单并能满足我们的需求
从yaml配置文件中读取配置违背了12种原则。但是比起从环境变量种读取所有的配置，它确实是更加方便的。
这种模式假设配置默认值在yaml文件种，对安全敏感的配置在环境变量种

### `docs`
Swagger 文档。由  [swag](https://github.com/swaggo/swag) 库自动生成
你不需要自己修改任何内容

### `integration-test`
集成测试
它会在应用容器旁启动独立的容器
使用[go-hit](https://github.com/Eun/go-hit) 会很方便的测试Rest API

## `internal/app`
这里只有通常只有一个 _Run_ 函数在 `app.go` 文件种。它是 _main_ 函数的延续

主要的对象在这里生成
依赖注入通过 "New ..." 构造 (阅读依赖注入)，这个技术允许使用[依赖注入](#依赖注入)的原则进行分层，使得业务逻辑独立于其他层。

接下来，我们启动服务器并阻塞等待_select_ 中的信号正常完成。

如果 `app.go`的规模增长了，你可以将它拆分为多个文件.

对于大量的依赖，可以使用[wire](https://github.com/google/wire)

`migrate.go` 文件用于是数据库自动构建
它显示的包扣了 _migrate_ 标签
```sh
$ go run -tags migrate ./cmd/app
```

### `internal/controller`
服务器处理层（MVC 控制层），这个模块展示两个服务
- RPC 
- REST http 

服务的路由用同样的风格进行编写
- handler 按照应用领域进行分组（又共同的基础）
- 对于每一个分组，创建自己的路由结构体和请求path路径
- 业务逻辑的结构被注入到路由器结构中，它将被处理程序调用

#### `internal/controller/rpc`
简单的 REST 版本控制
对于v2版本，我们需要添加`rpc/v2`文件夹，内容相同
可以在这层进行小流量操作
在 `v1/router.go` 及以上的处理程序方法中，可以使用[swag](https://github.com/swaggo/swag) swagger 通过注释生成swagger文档.

### `internal/entity`
业务逻辑实体（模型）可用于任何层. 
这里包括一些方法，例如：参数检验.

### `internal/usecase`
业务逻辑.
- 方法按应用领域分组（在共同的基础上）
- 每个组都有自己的结构
- 一个文件对应一个结构
Repositories、webapi、rpc等业务逻辑结构被注入到业务逻辑结构中
(阅读 [依赖注入](#dependency-injection)).

#### `internal/usecase/repo`
是持久化存储的业务逻辑逻辑抽象,如数据库.

#### `internal/usecase/webapi`
是webapi业务逻辑使用的抽象.
例如，它可能是业务逻辑通过 REST API 访问的另一个微服务。
包名称根据业务的实际用途进行命名

## 依赖注入
为了去除业务逻辑对外部包的依赖，使用了依赖注入.
例如，通过New构造函数，我们将依赖注入到业务逻辑的结构中.
这使得业务逻辑独立（且可移植）
我们可以覆盖接口的实现，而无需更改 `usecase` 包.

它还将允许我们自动生成相关mock（例如使用 [mockery](https://github.com/vektra/mockery)），以便进行单元测试.
> 我们不依赖于特定的实现，以便始终能够将一个组件更改为另一个组件
> 如果新组件实现了接口，则业务逻辑无需更改。
```go
package usecase

import (
    // Nothing!
)

type Repository interface {
    Get()
}

type UseCase struct {
    repo Repository
}

func New(r Repository) *UseCase{
    return &UseCase{
        repo: r,
    }
}


func (uc *UseCase) Do()  {
    uc.repo.Get()
}
```
