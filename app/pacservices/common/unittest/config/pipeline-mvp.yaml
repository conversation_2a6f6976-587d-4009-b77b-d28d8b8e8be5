platform: BYTECYCLE
pipelines:
  - id: "pac_pipeline_1"
    name: "test pac pipeline 1"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: [ "-" ]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_2"
    name: "test pac pipeline 2"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["-"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_3"
    name: "test pac pipeline 3"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["-"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_4"
    name: "test pac pipeline 4"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["-"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_5"
        name: "scm compile 5"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_5"
    name: "test pac pipeline 5"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_5"
        name: "scm compile 5"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_3", "scm_compile_4"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_6"
    name: "test pac pipeline 6"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_3"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_5"
        name: "scm compile 5"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_3"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_6"
        name: "scm compile 6"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_7"
    name: "test pac pipeline 7"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_3"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_5"
        name: "scm compile 5"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_3"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_8"
    name: "test pac pipeline 8"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["-"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1", "scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1", "scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
  - id: "pac_pipeline_9"
    name: "test pac pipeline 9"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_2"
        name: "scm compile 2"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["-"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_3"
        name: "scm compile 3"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1", "scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_4"
        name: "scm compile 4"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1", "scm_compile_2"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_5"
        name: "scm compile 5"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_3", "scm_compile_4"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
      - id: "scm_compile_6"
        name: "scm compile 6"
        uses: "pac/scm_compile"                 # action/scm_compile; bytecycle/scm_compile; pac/scm_compile
        depends_on: ["scm_compile_1", "scm_compile_1"]
        input:
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"
