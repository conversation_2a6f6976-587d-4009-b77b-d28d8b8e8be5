---
platform: BYTECYCLE
workspace_label: leon_1
id: aef098
pipelines:
  - id: "pac_pipeline_1"
    name: "sample pac pipeline 1"
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: "scm_compile_1"
        name: "scm compile 1"
        uses: "pac/scm_compile"
        depends_on:
          - "-"
        input:
          '@type': type.googleapis.com/scm.SCMInput
          repo_name: canale/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: "sample test"