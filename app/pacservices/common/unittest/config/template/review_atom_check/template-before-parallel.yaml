id: yfe97367c118
name: PPE Deployment
pipelineAttribute: {}
platform: BYTECYCLE
platformId: "8430"
steps:
- dependsOn:
  - '-'
  id: ttp_executiontask-3acb81
  input:
    description: '{{context.build.description|json}}'
    idc: tx
    psm: '{{context.build.psm|json}}'
    title: '{{context.build.title|json}}'
  name: Ticket Information
  uses: bytecycle/ttp_executiontask
- dependsOn:
  - ttp_executiontask-3acb81
  id: ttp_locktask-87ac4f
  input:
    app_env: '{{context.build.ppe_env_name|json}}'
    psm: '{{context.build.psm|json}}'
    resource_type: tce
    should_skip: true
    tce_env: ppe
  name: Lock PPE
  uses: bytecycle/ttp_locktask
- dependsOn:
  - ttp_locktask-87ac4f
  id: ttp_review_task-856021
  input:
    extra_content:
      Notice: Please review whether to approve the TTP Ticket Deployment
    notifier_types: '{{context.build.ttops_reviewers|json}}'
    notify_status:
    - PENDING
    should_skip: true
  name: ttp_review_task
  uses: bytecycle/ttp_review_task
- dependsOn:
  - ttp_review_task-856021
  id: ttp_version_select_beta-80599e
  input:
    advanced_settings: "true"
    choice_version_when_run: "false"
    env: tx
    env_label: prod
    psm: data.user_feature.muse
    scene: tce
    specify_recommended_strategy: "false"
    update_info: '{{context.build.data_user_feature_muse-base-version}}'
    use_scm_params: false
  name: SCM version for user_feature muse base
  uses: bytecycle/version_select
- dependsOn:
  - ttp_version_select_beta-80599e
  id: ttp_ppe_create_env-1f49fc
  input:
    cancel_pre_upgrade_ticket: true
    cluster_env_params: ""
    cluster_names: ""
    env_name: '{{context.build.data_user_feature_muse-base-env}}'
    feature_ids: ""
    idc: tx
    need_skip: false
    overtime: 2400
    overtime_action: fail
    psm_list:
    - data.user_feature.muse
    skip_if_scm_version_nochange: false
    target_cluster_configs: '{{context.build.tx_ppe_clusters|json}}'
  name: ttp_ppe_create_env
  uses: bytecycle/ppe_deploy
- dependsOn:
  - ttp_review_task-856021
  id: ttp_version_select_beta-6af358
  input:
    advanced_settings: "true"
    choice_version_when_run: "false"
    env: tx-useast8
    env_label: prod
    psm: data.user_feature.muse
    scene: tce
    specify_recommended_strategy: "false"
    update_info: '{{context.build.data_user_feature_muse-test-version}}'
    use_scm_params: false
  name: SCM version for user_feature muse test
  uses: bytecycle/version_select
- dependsOn:
  - ttp_version_select_beta-6af358
  id: ttp_ppe_create_env-6538e6
  input:
    cancel_pre_upgrade_ticket: false
    cluster_env_params: ""
    cluster_names: ""
    env_name: '{{context.build.data_user_feature_muse-test-env}}'
    feature_ids: ""
    idc:
    - tx-useast8
    need_skip: false
    psm_list:
    - data.user_feature.muse
    skip_if_scm_version_nochange: false
  name: ttp_ppe_create_env
  uses: bytecycle/ppe_deploy
- dependsOn:
  - ttp_ppe_create_env-1f49fc
  - ttp_ppe_create_env-6538e6
  id: holmes_stress_ttp-e18a98
  input:
    expire_remote_timeout_ms: 1500
    filter_method: ""
    is_sanitizer: false
    master_env_input: '{{context.build.data_user_feature_muse-base-env}}'
    master_ip_port: ""
    press_time: 30
    qps: 2000
    region: ""
    scene_id: 7
    should_skip: true
    skip_on_fail: true
    task_name: ufs_load_test
    test_env_input: '{{context.build.data_user_feature_muse-test-env}}'
    test_ip_port: ""
  name: Holmes Stress TTP
  uses: bytecycle/holmes_stress_ttp
- dependsOn:
  - ttp_ppe_create_env-1f49fc
  - ttp_ppe_create_env-6538e6
  id: loadtest_holmes_abparams_ttp-3daf92
  input:
    expire_remote_timeout_ms: 1500
    filter_method: ""
    is_sanitizer: false
    master_env_input: '{{context.build.data_user_feature_muse-base-env}}'
    master_ip_port: ""
    press_time: 30
    qps: '{{context.build.qps}}'
    region: ""
    scene_id: 7
    should_skip: false
    skip_on_fail: true
    task_name: ufs_load_test
    test_env_input: '{{context.build.data_user_feature_muse-test-env}}'
    test_ip_port: ""
  name: loadtest_holmes_abparams_debug_ttp
  uses: bytecycle/loadtest_holmes_abparams_ttp
- dependsOn:
  - holmes_stress_ttp-e18a98
  - loadtest_holmes_abparams_ttp-3daf92
  id: ttp_unlocktask-aab676
  input:
    app_env: '{{context.build.ppe_env_name|json}}'
    psm: '{{context.build.psm|json}}'
    resource_type: tce
    tce_env: ppe
  name: Unlock PPE
  uses: bytecycle/ttp_unlocktask
type: release
workspaceLabel: bytecycle
