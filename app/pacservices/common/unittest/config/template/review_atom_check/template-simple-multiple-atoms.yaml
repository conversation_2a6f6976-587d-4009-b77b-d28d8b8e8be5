id: wbd4a7bd147e
name: x<PERSON><PERSON>.jin的模板20230504170017
pipelineAttribute: {}
platform: BYTECYCLE
platformId: "8427"
steps:
- dependsOn:
  - '-'
  id: ttp_scm_compile_beta-911905
  input:
    compile_trigger_branch: false
    rest_upgrade_policy: latest
  name: ttp_scm_compile_beta
  uses: bytecycle/scm_compile
- dependsOn:
  - ttp_scm_compile_beta-911905
  id: ttp_user_confirm-631de6
  input:
    change_trigger: false
    confirm_user_types:
    - pipeline_builder
    repeated_confirm: false
  name: Manual Confirmation
  uses: bytecycle/user_confirm
- dependsOn:
  - ttp_user_confirm-631de6
  id: ttp_review_task-2f6ed2
  input:
    extra_content:
      Notice: Please review whether to approve the TTP Ticket Deployment
    notifier_types: '{{context.build.ttops_reviewers|json}}'
    notify_status:
    - PENDING
  name: ttp_review_task
  uses: bytecycle/ttp_review_task
type: release
workspaceLabel: pac_space
