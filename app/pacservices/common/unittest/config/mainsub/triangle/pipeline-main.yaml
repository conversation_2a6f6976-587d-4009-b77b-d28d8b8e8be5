platform: BYTECYCLE
workspace_label: pac_test_online
id: aef098
pipelines:
  - id: "pac_pipeline_1"
    is_main: True
    steps:
      - id: "scm_1"
        uses: "pac/scm_compile"
        input:
          repo_name: canal/pac/engine
          branch_publish:
            git_branch: master
          product_storages:
            - CN
            - USEAST
          product_formats:
            - TAR
            - BVC
          version_type: TEST
          description: sample test
      - id: "sub_pipeline1_step"
        uses: "pac/subpipeline_driver"
        input:
          file_path: "unittest/config/mainsub/triangle/pipeline-sub1.yaml"
      - id: "sub_pipeline2_step"
        uses: "pac/subpipeline_driver"
        input: 
          file_path: "unittest/config/mainsub/triangle/pipeline-sub2.yaml"