id: n9d23a0ffdf9
platform: BYTECYCLE
workspaceLabel: pac_test
pipelines:
  - id: pac_pipeline_1_selector
    isMain: true
    name: sample pac pipeline with selector nested
    variables:
      k1:
        val: "v1"
        required_at_runtime: false
      k2:
        val: "v2"
        required_at_runtime: true
    steps:
      - id: selector1
        name: selector step
        uses: bytecycle/selector
        input:
          scm-compile-1:
            expr: x==1
            next: scm-compile-1
            rule_idx: 1
            ruleIdxTip: Rule 1
            text: ""
      - id: scm-compile-1
        name: scm compile step
        uses: pac/scm_compile
        dependsOn:
          - selector1
        input:
          branch_publish:
            git_branch: master
          description: sample test
          product_formats:
            - TAR
            - BVC
          product_storages:
            - CN
            - USEAST
          repo_name: canal/pac/engine
          version_type: TEST
      - id: selector2
        name: inner selector step
        uses: bytecycle/selector
        depends_on:
          - scm-compile-1
        input:
          ppe-deploy-1:
            expr: x==2
            next: ppe-deploy-1
            rule_idx: 2
            ruleIdxTip: Rule 2
            text: ""
          version-selector-1:
            expr: x==1
            next: version-selector-1
            rule_idx: 1
            ruleIdxTip: Rule 1
            text: ""
      - id: ppe-deploy-1
        name: ppe deploy step
        uses: bytecycle/ppe_deploy
        dependsOn:
          - selector2
        input:
          auth_type: '1'
          cancel_pre_upgrade_ticket: false
          cluster_env_params: ''
          cluster_names: ''
          cluster_names_switch: user_defined
          env_name: ''
          feature_ids: ''
          image_tag: ''
          image_version: ''
          ipv6only_enabled: ''
          is_single_idc: 'true'
          keep_days: 15
          need_skip: false
          psm_list:
            - canal.pac.engine
          resource_type: short
          rollout_strategy: default
          single_idc: lq
          skip_if_scm_version_nochange: false
          use_image_model: false
          should_not_add_env_prefix: false
          idc:
            - i18n-va
          cancel_last_deployment: false
      - id: version-selector-1
        name: version selector step
        uses: bytecycle/version_select
        dependsOn:
          - selector2
        input:
          advanced_settings: "false"
          choice_version_when_run: "true"
          env: tce_cn
          env_label: prod
          psm: canal.pac.engine
          scene: tce
          specify_recommended_strategy: "false"
          use_scm_params: false