package pipeline

import (
	"fmt"
	"io/ioutil"

	"code.byted.org/devinfra/hagrid/app/pacservices/cli/utils"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/spf13/cobra"
)

type getOptions struct {
	platformType string
}

func newCmdGet() *cobra.Command {
	opts := getOptions{}

	cmd := &cobra.Command{
		Use:     "get <pipeline id> <configuration file path>",
		Short:   "Get an existing pipeline to yaml config file",
		Example: `  example 1:  bc-cli pipeline get 123456 config.yaml`,
		Args:    cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			log.V2.SetLevel(logs.WarnLevel)

			return translatePipeline(args[0], args[1], opts)
		},
	}
	cmd.Flags().StringVarP(&opts.platformType, "platform-type", "p", "bits", "type of platform where the pipeline exists, default bits")

	return cmd
}

func translatePipeline(bcPipelineID string, targetFile string, opts getOptions) error {
	c, nErr := utils.NewCLIHTTPClient()
	if nErr != nil {
		return nErr
	}

	resp, sendErr := c.TranslatePipeline(bcPipelineID, opts.platformType)
	if sendErr != nil {
		return sendErr
	}
	writeErr := createConfigFile(targetFile, resp.YamlFile)
	if writeErr != nil {
		return writeErr
	}
	fmt.Println("Successfully translate existing pipeline into config file")
	return nil

}

func createConfigFile(filePath, content string) error {
	writeErr := ioutil.WriteFile(filePath, []byte(content), 0644)
	return writeErr
}
