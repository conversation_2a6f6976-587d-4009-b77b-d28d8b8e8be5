package template

import (
	"errors"
	"fmt"
	"io/ioutil"
	"os"

	"code.byted.org/devinfra/hagrid/app/pacservices/cli/utils"
	"code.byted.org/devinfra/hagrid/app/pacservices/common/translator"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/gopkg/logs/v2/log"

	"github.com/spf13/cobra"
)

func newCmdInit() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "init <configuration file path>",
		Short: "Generate a new template config file with uuid at the top line",
		Long: "If the file does not exist, a new file will be created with top level id set to a generated uuid. " +
			"If the file does exist, the top level id field will be replaced by a newly generated uuid.",
		Example: `  example 1:  bc-cli template init config.yaml`,
		Args:    cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			log.V2.SetLevel(logs.WarnLevel)

			return initFile(args[0])
		},
	}

	return cmd
}

func initFile(filePath string) error {
	c, nErr := utils.NewCLIHTTPClient()
	if nErr != nil {
		return nErr
	}

	resp, gErr := c.GenerateUUID()
	if gErr != nil {
		return gErr
	}

	newID := resp.Uuid

	if _, sErr := os.Stat(filePath); sErr == nil {
		// file exists, replace the uuid
		fmt.Printf("[WARN] file %s already exists, uuid will be replaced with a new one\n", filePath)

		yb, rErr := ioutil.ReadFile(filePath)
		if rErr != nil {
			return rErr
		}

		template, y2pbErr := translator.ConvertYamlToTemplatePB(string(yb))
		if y2pbErr != nil {
			return y2pbErr
		}

		template.Id = newID

		nyb, pb2yErr := translator.ConvertTemplatePbToYaml(template)

		if pb2yErr != nil {
			return pb2yErr
		}

		if wErr := ioutil.WriteFile(filePath, []byte(nyb), 0666); wErr != nil {
			return wErr
		}

		return nil
	} else if errors.Is(sErr, os.ErrNotExist) {
		if wErr := ioutil.WriteFile(filePath, []byte("id: "+newID), 0666); wErr != nil {
			return wErr
		}

		fmt.Println("Successfully init config files with uuid")

		return nil
	} else {
		return sErr
	}
}
