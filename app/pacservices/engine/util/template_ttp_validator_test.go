package util

import (
	"io/ioutil"
	"testing"

	"code.byted.org/devinfra/hagrid/app/pacservices/common/translator"
	"github.com/stretchr/testify/assert"
)

func Test_IsTemplateValidWithReviewAtom(t *testing.T) {
	// single review atom is valid in the main path
	yamlBytes, err := ioutil.ReadFile("../../common/unittest/config/template/review_atom_check/template-simple-single-atom.yaml")
	assert.NoError(t, err)
	template, err := translator.ConvertYamlToTemplatePB(string(yamlBytes))
	assert.NoError(t, err)
	isValid := IsTemplateValidWithReviewAtom(template)
	assert.Equal(t, true, isValid)

	// review atom is valid in the main path with multiple other atoms
	yamlBytes, err = ioutil.ReadFile("../../common/unittest/config/template/review_atom_check/template-simple-multiple-atoms.yaml")
	assert.NoError(t, err)
	template, err = translator.ConvertYamlToTemplatePB(string(yamlBytes))
	assert.NoError(t, err)
	isValid = IsTemplateValidWithReviewAtom(template)
	assert.Equal(t, true, isValid)

	// review atom is invalid due to selector atom
	yamlBytes, err = ioutil.ReadFile("../../common/unittest/config/template/review_atom_check/template-selector.yaml")
	assert.NoError(t, err)
	template, err = translator.ConvertYamlToTemplatePB(string(yamlBytes))
	assert.NoError(t, err)
	isValid = IsTemplateValidWithReviewAtom(template)
	assert.Equal(t, false, isValid)

	// review atom is invalid due to sub pipeline
	yamlBytes, err = ioutil.ReadFile("../../common/unittest/config/template/review_atom_check/template-sub-pipeline.yaml")
	assert.NoError(t, err)
	template, err = translator.ConvertYamlToTemplatePB(string(yamlBytes))
	assert.NoError(t, err)
	isValid = IsTemplateValidWithReviewAtom(template)
	assert.Equal(t, false, isValid)

	// review atom is invalid due to parallel
	yamlBytes, err = ioutil.ReadFile("../../common/unittest/config/template/review_atom_check/template-parallel.yaml")
	assert.NoError(t, err)
	template, err = translator.ConvertYamlToTemplatePB(string(yamlBytes))
	assert.NoError(t, err)
	isValid = IsTemplateValidWithReviewAtom(template)
	assert.Equal(t, false, isValid)

	// review atom is valid right before the parallel
	yamlBytes, err = ioutil.ReadFile("../../common/unittest/config/template/review_atom_check/template-before-parallel.yaml")
	assert.NoError(t, err)
	template, err = translator.ConvertYamlToTemplatePB(string(yamlBytes))
	assert.NoError(t, err)
	isValid = IsTemplateValidWithReviewAtom(template)
	assert.Equal(t, true, isValid)
}
