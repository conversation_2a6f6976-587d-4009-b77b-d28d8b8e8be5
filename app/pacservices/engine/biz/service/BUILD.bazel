load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "service",
    srcs = [
        "executor.go",
        "executor_delete.go",
        "executor_materialize.go",
        "executor_reconcile.go",
        "template_executor.go",
        "uuidGenerator.go",
        "validator.go",
        "yamlGenerator.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/pacservices/engine/biz/service",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pacservices/common/translator",
        "//app/pacservices/common/util",
        "//app/pacservices/engine/biz/manager",
        "//app/pacservices/engine/biz/processor",
        "//app/pacservices/engine/constvar",
        "//app/pacservices/engine/util",
        "//app/pacservices/idl/kitex_gen/canal/pac/engine",
        "//idls/byted/devinfra/pac/proto/atom:atom_go_proto",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "@org_byted_code_canal_trigger_common//errors",
        "@org_byted_code_gopkg_logs_v2//log",
    ],
)

go_test(
    name = "service_test",
    srcs = [
        "executor_delete_test.go",
        "executor_materialize_test.go",
        "executor_reconcile_test.go",
        "executor_test.go",
        "executor_validator_test.go",
        "template_executor_test.go",
        "uuidGenerator_test.go",
        "yamlGenerator_test.go",
    ],
    data = [
        "//app/pacservices/common/unittest/config:common_unittest_config_files",
        "//app/pacservices/engine/kitex/conf:config_files",
    ],
    embed = [":service"],
    gc_goopts = [
        "-l",
        "-N",
    ],
    deps = [
        "//app/pacservices/common/translator",
        "//app/pacservices/engine/biz/manager",
        "//app/pacservices/engine/biz/processor",
        "//app/pacservices/engine/config",
        "//app/pacservices/engine/platform/bytecycle/pipeline",
        "//app/pacservices/engine/stub/bytecycle",
        "//app/pacservices/idl/kitex_gen/canal/pac/engine",
        "//idls/byted/devinfra/pac/proto/atom:atom_go_proto",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_smartystreets_goconvey//convey",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)
