package aslpb

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestFindMergePoint(t *testing.T) {
	assert.Equal(t, []int{3, 4, 2}, findMergePoint([][]string{{"a", "p1", "p2", "k", "ep"}, {"b", "f", "h", "p2", "k", "ep"}, {"c", "g", "k", "ep"}}))

	assert.Equal(t, []int{0, 0, 0}, findMergePoint([][]string{{"a"}, {"a"}, {"a"}}))

	assert.Equal(t, []int{0, 0, 0}, findMergePoint([][]string{{"a", "b"}, {"a", "b"}, {"a", "b"}}))

	assert.Equal(t, []int{1, 1, 1, 1}, findMergePoint([][]string{{"a"}, {"b"}, {"c"}, {"d"}}))

	assert.Equal(t, []int{1, 2, 3}, findMergePoint([][]string{{"a"}, {"b", "c"}, {"d", "e", "f"}}))

	assert.Equal(t, []int{1, 2, 3, 1}, findMergePoint([][]string{{"a"}, {"b", "c"}, {"d", "e", "f"}, {"g"}}))

	assert.Equal(t, []int{0, 1, 1}, findMergePoint([][]string{{"a", "b"}, {"c", "a", "b"}, {"d", "a", "b"}}))

	assert.Equal(t, []int{1, 0, 1}, findMergePoint([][]string{{"c", "a", "b"}, {"a", "b"}, {"d", "a", "b"}}))

	assert.Equal(t, []int{2, 1, 2}, findMergePoint([][]string{{"e", "f", "a", "b"}, {"c", "a", "b"}, {"g", "h", "a", "b"}}))

	assert.Equal(t, []int{0}, findMergePoint([][]string{{"a"}}))

	assert.Equal(t, []int{1, 3}, findMergePoint([][]string{{"a", "b", "c"}, {"d", "f", "g", "b", "c"}}))
}
