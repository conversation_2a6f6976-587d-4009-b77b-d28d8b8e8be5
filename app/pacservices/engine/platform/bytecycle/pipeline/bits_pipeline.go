package pipeline

import (
	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"code.byted.org/devinfra/hagrid/app/pacservices/common/translator"
	"code.byted.org/devinfra/hagrid/app/pacservices/engine/biz/intf"
	"code.byted.org/devinfra/hagrid/app/pacservices/engine/constvar"
	"code.byted.org/devinfra/hagrid/app/pacservices/engine/stub/bits"
	"code.byted.org/devinfra/hagrid/app/pacservices/engine/stub/model"
	"code.byted.org/devinfra/hagrid/app/pacservices/engine/util"
	pac "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/resourcespb"
	"code.byted.org/gopkg/lang/slices"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/types/known/structpb"
)

type BitsPipeliner struct {
	ApiServer bits.IApiServer
}

func (bp *BitsPipeliner) CreatePipeline(ctx context.Context, param intf.CreatePipelineParam) (string, string, error) {
	spaceIDInt, err := strconv.Atoi(param.WorkspaceLabel)
	if err != nil {
		return "", "", fmt.Errorf("failed to transfer workspace label from string to int %v", err)
	}
	bitsPipeline, err := convertPacPipelineToBits(param.PipelinePB, spaceIDInt)
	if err != nil {
		return "", "", fmt.Errorf("failed to convertPacPipelineToBits %v", err)
	}
	req := model.CreateBitsPipelineReq{
		SpaceID:  uint64(spaceIDInt),
		Pipeline: *bitsPipeline,
	}

	res, createErr := bp.ApiServer.CreatePipeline(ctx, req)
	log.V2.Info().With(ctx).Str("[BitsPipeliner#CreatePipeline]").KV("result of CreatePipeline", res).Emit()
	if createErr != nil {
		return "", "", createErr
	}
	pipelineUrl := fmt.Sprintf("https://bits.bytedance.net/devops/%d/pipeline/detail/%d", spaceIDInt, res.PipelineId)
	return strconv.Itoa(int(res.PipelineId)), pipelineUrl, nil
}

func (bp *BitsPipeliner) RunPipeline(ctx context.Context, param intf.RunPipelineParam) (string, error) {
	idInt, parseErr := strconv.ParseInt(param.PipelineID, 10, 64)
	if parseErr != nil {
		return "", parseErr
	}

	return bp.ApiServer.RunPipeline(ctx, idInt)
}

func (bp *BitsPipeliner) UpdatePipeline(ctx context.Context, param intf.UpdatePipelineParam) error {
	workspaceLabelID, err := strconv.Atoi(param.WorkspaceLabel)
	if err != nil {
		return err
	}
	bitsPipeline, err := convertPacPipelineToBits(param.NewPipeline, workspaceLabelID)
	if err != nil {
		return err
	}
	pipelineIdInt, parseErr := strconv.Atoi(param.ExistingPipeline.PlatformId)
	if parseErr != nil {
		return parseErr
	}
	if param.ExistingPipeline.Name != param.NewPipeline.Name ||
		isDSLChanged(param.ExistingPipeline, param.NewPipeline) || param.ForcedToUpdate {
		_, uErr := bp.ApiServer.UpdatePipeline(ctx, pipelineIdInt, model.UpdateBitsPipelineReq{
			Stages:        bitsPipeline.Stages,
			Name:          bitsPipeline.Name,
			Notifications: bitsPipeline.Notifications,
		})
		if uErr != nil {
			return uErr
		}
	}
	log.V2.Info().With(ctx).Str("[UpdatePipeline] comparing current variables").
		KV("current pipeline variables", param.ExistingPipeline.Variables).
		KV("new pipeline variables", param.NewPipeline.Variables).KV("ForcedToUpdate", param.ForcedToUpdate).Emit()
	if !reflect.DeepEqual(param.ExistingPipeline.Variables, param.NewPipeline.Variables) || param.ForcedToUpdate {
		if len(bitsPipeline.VarDefinitions) > 0 {
			_, uErr := bp.ApiServer.UpdatePipelineVariables(ctx, pipelineIdInt, model.UpdateBitsPipelineVarReq{
				VarDefinitions: bitsPipeline.VarDefinitions,
			})
			if uErr != nil {
				return uErr
			}
		}
	}
	return nil
}

func (bp *BitsPipeliner) DeletePipeline(ctx context.Context, param intf.DeletePipelineParam) error {
	//TODO implement me
	panic("implement me")
}

func (bp *BitsPipeliner) MaterializePipeline(ctx context.Context, param intf.MaterializePipelineParam) (string, error) {
	pipelineID, err := bp.ApiServer.MaterializePipeline(ctx, param.TemplateID, param.MaterializeMeta)
	if err != nil {
		return "", err
	}
	body := model.MaterializeBitsPipelineReq{}
	if marshalErr := sonic.Unmarshal([]byte(param.MaterializeMeta), &body); marshalErr != nil {
		return "", marshalErr
	}
	vars := make([]model.BitsCreateVarDefinition, 0)
	for _, variable := range body.TemplateVars {
		variableName := variable.Name
		if strings.HasPrefix(variable.Name, "custom.") {
			variableName = strings.TrimPrefix(variable.Name, "custom.")
		}
		vars = append(vars, model.BitsCreateVarDefinition{
			Name: variableName,
			DefaultValue: model.BitsCreateVarValue{
				Text:      variable.Value.Text,
				Number:    variable.Value.Number,
				Boolean:   variable.Value.Boolean,
				JsonArray: variable.Value.JsonArray,
			},
		})
	}
	pipelineID, err = bp.ApiServer.UpdatePipelineVariables(ctx, pipelineID, model.UpdateBitsPipelineVarReq{
		VarDefinitions: vars,
	})
	if err != nil {
		return "", err
	}
	return strconv.Itoa(pipelineID), nil
}

func (bp *BitsPipeliner) PipelineExists(ctx context.Context, pipelineID int64) bool {
	//TODO implement me
	panic("implement me")
}

func NewBitsPipeliner() *BitsPipeliner {
	return &BitsPipeliner{
		ApiServer: bits.NewApiServer(),
	}
}

func (bp *BitsPipeliner) TranslatePipeline(ctx context.Context, rootPipelineID int64) (*pac.PipelineSet, string, error) {
	rootBitsPipeline, gpErr := bp.ApiServer.GetPipelineWithID(ctx, rootPipelineID)
	if gpErr != nil {
		return nil, "", fmt.Errorf("failed to get pipeline with id %d: %w", rootPipelineID, gpErr)
	}

	ps := &pac.PipelineSet{
		Id:             util.GenerateRandomID(constvar.RandomIDLength_File),
		Platform:       pac.Platform_BITS,
		WorkspaceLabel: strconv.FormatInt(rootBitsPipeline.SpaceID, 10),
		Pipelines:      make([]*pac.Pipeline, 0),
	}
	rootPacPipeline, err := convertBitsPipelineToPac(rootBitsPipeline)
	rootPacPipeline.Result = fmt.Sprintf("https://bits.bytedance.net/devops/%d/pipeline/detail/%d", rootBitsPipeline.SpaceID, rootPipelineID)
	if err != nil {
		return nil, "", err
	}
	ps.Pipelines = append(ps.Pipelines, rootPacPipeline)

	return ps, rootBitsPipeline.CreatedBy, nil
}

func convertBitsPipelineToPac(bitsPipeline *model.BitsPipeline) (ppl *pac.Pipeline, err error) {
	pacPipelineID := strings.Replace(bitsPipeline.Pipeline.Name.Value, " ", "_", -1) + fmt.Sprintf("_%d", bitsPipeline.Pipeline.ID)
	steps := make([]*pac.Step, 0)
	for _, stage := range bitsPipeline.Pipeline.Stages {
		for _, job := range stage.Jobs {
			input := &structpb.Struct{}
			if job.Inputs != nil {
				input, err = translator.MarshalFromMap(job.Inputs)
				if err != nil {
					return nil, fmt.Errorf("failed to translate inputs %v", err)
				}
			}
			steps = append(steps, &pac.Step{
				Id:        job.ID,
				Name:      job.Name.Value,
				Uses:      job.Uses,
				DependsOn: job.DependsOn,
				Input:     input,
				Stage:     stage.ID,
			})
		}
	}
	variables := make(map[string]*pac.VariableInfo)
	for _, varInfo := range bitsPipeline.Pipeline.VarGroup.VarDefinitions {
		var variableType string
		var variableValue string
		var variableOptions string
		if varInfo.DefaultValue.Value.Text != nil {
			variableType = "string"
			variableValue = *varInfo.DefaultValue.Value.Text
			if varInfo.UIOption.Options != nil {
				variableType = "single_option"
				optionsStr, err := sonic.MarshalString(varInfo.UIOption.Options)
				if err != nil {
					return nil, fmt.Errorf("failed to marshal options %s %v", varInfo.UIOption.Options, err)
				}
				variableOptions = optionsStr
			}
		} else if varInfo.DefaultValue.Value.Number != nil {
			variableType = "number"
			variableValue = strconv.Itoa(*varInfo.DefaultValue.Value.Number)
		} else if varInfo.DefaultValue.Value.Boolean != nil {
			variableType = "boolean"
			variableValue = strconv.FormatBool(*varInfo.DefaultValue.Value.Boolean)
		} else if varInfo.DefaultValue.Value.JsonArray != nil {
			variableType = "multiple_options"
			variableValue = *varInfo.DefaultValue.Value.JsonArray
			optionsStr, err := sonic.MarshalString(varInfo.UIOption.Options)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal options %s %v", varInfo.UIOption.Options, err)
			}
			variableOptions = optionsStr
		} else {
			return nil, fmt.Errorf("variable type not supported")
		}
		variables[varInfo.FullName] = &pac.VariableInfo{
			Val:  variableValue,
			Type: variableType,
		}
		if variableOptions != "" {
			variables[varInfo.FullName].Options = variableOptions
		}
	}
	return &pac.Pipeline{
		Id:               pacPipelineID,
		Name:             bitsPipeline.Pipeline.Name.Value,
		IsMain:           true,
		PlatformId:       strconv.FormatInt(bitsPipeline.Pipeline.ID, 10),
		Steps:            steps,
		MaxRunningBuilds: strconv.Itoa(bitsPipeline.Pipeline.Concurrency.Max),
		Variables:        variables,
		Notifications:    bitsPipeline.Pipeline.Notifications,
	}, nil
}

func convertPacPipelineToBits(pacPipeline *pac.Pipeline, spaceID int) (*model.BitsPipelineDetail, error) {
	stageList := make([][]*pac.Step, 0)
	currentStage := ""
	for _, step := range pacPipeline.Steps {
		if step.Stage != currentStage {
			currentStage = step.Stage
			stageList = append(stageList, []*pac.Step{step})
		} else {
			stageList[len(stageList)-1] = append(stageList[len(stageList)-1], step)
		}
	}
	stages := make([]model.BitsPipelineStage, 0)
	for _, stage := range stageList {
		jobs := make([]model.BitsPipelineJob, 0)
		selectorJobIDs := make([]string, 0)
		for _, step := range stage {
			input, err := translator.UnmarshalToMap(step.GetInput())
			if err != nil {
				return nil, fmt.Errorf("UnmarshalToMap failed %v", err)
			}
			job := model.BitsPipelineJob{
				ID: step.GetId(),
				Name: model.BitsNameDetail{
					Value: step.GetName(),
				},
				Uses:      step.GetUses(),
				DependsOn: step.GetDependsOn(),
				Inputs:    input,
			}
			if strings.Contains(step.GetUses(), "job_atoms/selector") {
				selectorJobIDs = append(selectorJobIDs, job.ID)
			}
			jobs = append(jobs, job)
		}
		if len(selectorJobIDs) != 0 {
			for i, job := range jobs {
				for _, selectorJobID := range selectorJobIDs {
					if slices.Contains(job.DependsOn, selectorJobID) {
						jobs[i].ExtraIf = fmt.Sprintf("{{succeeded(\"%s\")&&context.%s.selected_rule == \"%s\"}}", selectorJobID, selectorJobID, job.ID)
					}
				}
			}
		}
		stages = append(stages, model.BitsPipelineStage{
			ID:   stage[0].GetStage(),
			Jobs: jobs})
	}
	variables := make([]model.BitsCreateVarDefinition, 0)
	for key, variableInfo := range pacPipeline.Variables {
		var value model.BitsCreateVarValue
		switch variableInfo.Type {
		case "string", "single_option":
			value = model.BitsCreateVarValue{
				Text: &variableInfo.Val,
			}
		case "number":
			num, err := strconv.Atoi(variableInfo.Val)
			if err != nil {
				return nil, fmt.Errorf("failed to transfer variable value from string to int: %v", err)
			}
			value = model.BitsCreateVarValue{
				Number: &num,
			}
		case "boolean":
			boolVal, err := strconv.ParseBool(variableInfo.Val)
			if err != nil {
				return nil, fmt.Errorf("failed to transfer variable value from string to boolean: %v", err)
			}
			value = model.BitsCreateVarValue{
				Boolean: &boolVal,
			}
		case "multiple_options":
			value = model.BitsCreateVarValue{
				JsonArray: &variableInfo.Val,
			}
		default:
			return nil, fmt.Errorf("unsupported variable type: %s", variableInfo.Type)
		}
		variableName := key
		if strings.HasPrefix(variableName, "custom.") {
			variableName = strings.TrimPrefix(variableName, "custom.")
		}
		variables = append(variables, model.BitsCreateVarDefinition{
			Name:         variableName,
			DefaultValue: value,
		})
	}
	concurrencyMax, err := strconv.Atoi(pacPipeline.MaxRunningBuilds)
	if err != nil {
		return nil, fmt.Errorf("MaxRunningBuilds cannot be transferred to int %v", err)
	}
	return &model.BitsPipelineDetail{
		Name: model.BitsNameDetail{
			Value: pacPipeline.Name,
			Lang:  "en",
			Texts: map[string]string{
				"en": pacPipeline.Name,
				"zh": pacPipeline.Name,
			},
		},
		Concurrency: model.BitsPipelineConcurrency{
			Max: concurrencyMax,
		},
		Stages:         stages,
		VarDefinitions: variables,
		Notifications:  pacPipeline.Notifications,
	}, nil
}
