package util

import (
	"code.byted.org/devinfra/hagrid/app/pacservices/common/constvar"

	"context"
)

// GetJWTTokenByCtx get jwt token from http header
func GetJWTTokenByCtx(ctx context.Context) string {
	val := ctx.Value(constvar.JWTTokenKey)
	if jwtToken, ok := val.(string); ok {
		return jwtToken
	}
	return ""
}

// GetUsername get username from *gin.Context/context.Context
func GetUsername(ctx context.Context) string {
	val := ctx.Value(constvar.UsernameKey)
	if username, ok := val.(string); ok {
		return username
	}
	return ""
}
