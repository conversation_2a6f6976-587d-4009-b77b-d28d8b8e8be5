package tcesolution

import (
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/service/creation/solution/solutionconsts"
)

var GuluXConfig = dao.SolutionConfig{
	Key:           solutionconsts.Key_GuluX,
	ComponentType: constants.ComponentType_TCE,
	Category:      dao.SolutionCategoryGeneral,
	Language:      solutionconsts.Language_Node,
	Name:          "GuluX",
	NameI18N:      "GuluX",
	Desc:          "基于 GuluX 框架创建 HTTP/RPC 服务",
	DescI18N:      "",
	Icon:          "",
	Link:          "https://gulux.bytedance.net/guide/intro.html",
	Status:        solutionconsts.Status_Online,
	Order:         3,
	Features:      []string{solutionconsts.Feature_PSM, solutionconsts.Feature_SCM, solutionconsts.Feature_Form, solutionconsts.Feature_Cluster},
	Resources:     []string{solutionconsts.Resource_PSM, solutionconsts.Resource_Codebase, solutionconsts.Resource_SCM, solutionconsts.Resource_TCE},
	CodeConfig: dao.CodeConfig{
		Type:      dao.CodeConfigTypeTemplate,
		Framework: solutionconsts.Codebase_Framework_Gulux,
	},
	SCMConfig: dao.SCMConfig{
		Language:    solutionconsts.SCM_Language_Node,
		Image:       solutionconsts.SCM_Image_Node_V20,
		CompileType: solutionconsts.SCM_Compile_Type_Script,
		BuildScript: "build.sh",
	},
	TCEConfig: dao.TCEConfig{
		Language:  solutionconsts.SCM_Language_Node,
		Framework: solutionconsts.Framework_Gulux,
		Protocol:  solutionconsts.Protocol_THRIFT,
	},
	CustomForm: GuluXForm,
}

var GuluXForm = []dao.CustomFormItem{
	{
		Label:         "模板",
		LabelEn:       "Template",
		Field:         "customFormData.template",
		InitialValue:  "http-server",
		Disabled:      false,
		Required:      true,
		ComponentType: "select",
		ComponentProps: dao.CustomFormComponentProps{
			Placeholder:   "选择模板",
			PlaceholderEn: "Select Template",
			Options: []dao.CustomOption{
				{
					Label: "http-server",
					Value: "http-server",
				},
				{
					Label: "rpc-server",
					Value: "rpc-server",
				},
			},
		},
	},
}
