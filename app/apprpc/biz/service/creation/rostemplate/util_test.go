package rostemplate

import "testing"

func TestGetEnv(t *testing.T) {
	type args struct {
		env string
		psm string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "test",
			args: args{
				env: "BOE-FEATURE",
				psm: "bits.app.api",
			},
			want: "boe_bits_app_api",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetEnv(tt.args.env, tt.args.psm); got != tt.want {
				t.Errorf("GetEnv() = %v, want %v", got, tt.want)
			}
		})
	}
}
