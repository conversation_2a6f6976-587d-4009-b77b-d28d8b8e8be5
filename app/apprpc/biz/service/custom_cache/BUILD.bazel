load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "custom_cache",
    srcs = [
        "base.go",
        "base_mock.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/apprpc/biz/service/custom_cache",
    visibility = ["//visibility:public"],
    deps = [
        "//app/apprpc/biz/clients/bytedoc",
        "//app/apprpc/biz/dao",
        "//app/apprpc/biz/util",
        "@com_github_bytedance_mockey//:mockey",
        "@org_byted_code_canal_provider//goofy_deploy",
        "@org_byted_code_gopkg_logs_v2//:logs",
        "@org_byted_code_iesarch_cdaas_utils//utils",
        "@org_byted_code_iesarch_paas_sdk//goofy_deploy",
        "@org_byted_code_lang_gg//gslice",
    ],
)
