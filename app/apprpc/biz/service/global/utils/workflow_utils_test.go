package utils

import (
	"fmt"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/internal/global"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
)

func TestGetWorkflowSolution(t *testing.T) {
	t.Run("case #1 pending tickets", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := "deployment in progress"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
			}
			// expectation
			sol := Solution{
				Suggestion: "请先关闭进行中工单: https://bits.bytedance.net/app_center/global_detail/tce/g1/workflow/wf2",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "there is an in-progress ticket, please cancel it and retry",
						prefixNo2 + ticket:      "https://bits.bytedance.net/app_center/global_detail/tce/g1/workflow/wf2",
						prefixNo3 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(&dao.GlobalComponentWorkflow{}, false, fmt.Errorf("your error")).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, false)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #2 pending tickets", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := "deployment in progress"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
			}
			// expectation
			sol := Solution{
				Suggestion: "please contact oncall",
				Concise:    false,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "there is an in-progress ticket, please cancel it and retry",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(&dao.GlobalComponentWorkflow{}, false, fmt.Errorf("your error")).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, false)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #3 other unknown errors", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := "500 internal error"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
			}
			// expectation
			sol := Solution{
				Suggestion: "please contact oncall",
				Concise:    false,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "please contact oncall",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Act
			got := GetWorkflowSolution(wf, false)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #4 pending tickets with batchID", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := "deployment in progress"
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "请先关闭进行中工单: https://bits.bytedance.net/app_center/global_detail/tce/g1/workflow/wf2",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "there is an in-progress ticket, please cancel it and retry",
						prefixNo2 + ticket:      "https://bits.bytedance.net/app_center/global_detail/tce/g1/workflow/wf2",
						prefixNo3 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #5 pending tickets with batchID but not hit", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := "deployment in progress"
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "请先关闭进行中工单: https://bits.bytedance.net/app_center/global_detail/tce/g1/workflow/wf2",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "there is an in-progress ticket, please cancel it and retry",
						prefixNo2 + ticket:      "https://bits.bytedance.net/app_center/global_detail/tce/g1/workflow/wf2",
						prefixNo3 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(nil, false, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #6 permission denied", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := `
[
  {
    ""LogicalId"": ""grn:cac:tiktok.gdp.auto_test"",
    ""ResourceType"": ""TCC_SERVICE"",
    ""Placement"": ""VGeo-EU"",
    ""Status"": ""CreateFailed"",
    ""FailedReason"": ""ErrorCode: -1, ErrorType:PERMISSION_DENIED, ErrorMessage: user do not have destination node opt auth logid:021733106786213fdbdfdbdfdbdfdbdffffffff000002a7547875""
  },
  {
    ""LogicalId"": ""grn:cac:tiktok.gdp.auto_test.release"",
    ""ResourceType"": ""TCC_BATCH_RELEASE_CONFIG"",
    ""Placement"": ""VGeo-US"",
    ""Status"": ""CreateFailed"",
    ""FailedReason"": ""rsp: {\""base_resp\"":{\""error_code\"":-300,\""error_type\"":\""SHOULD_ESCAPE\"",\""error_message\"":\""Access denied, please check service strategy on console, This is an operation freeze from ByteCloud. Freeze Policy Name: 140-usttp-2024-Thansgiving-TikTok Eng Deployment Freeze, Involved Scope: |TikTok, Admins: zhenyu.li,wangxin.arlen,peihao.yuan,yuansijia.ysj,yangfeilong.beyond, Operator: jixiang.324(person_account). You can describe the necessity to apply for an exception if needed.Apply for an exception through the link below (if there are more than one, you need to apply one by one):https://cloud-ttp-us.bytedance.net/bpm/apply?cid=189\\u0026defaultValues=%7B%22galaxy_node_id%22%3A********%2C%22brn_scope%22%3A%22%22%2C%22brn_target%22%3A%22%22%2C%22policy_id%22%3A%22c550877f-e382-4d86-af3e-976103cc775c%22%2C%22psm_create%22%3A%22%22%2C%22psm_exist%22%3Atrue%2C%22exception_user%22%3A%22jixiang.324%22%2C%22permission%22%3A%22tcc.service.deploy%22%7D logid:02173310679301500000000000000000000ffff0a71bb46511258\"",\""extra\"":{\""logID\"":\""02173310679301500000000000000000000ffff0a71bb46511258\""},\""apply_info\"":{\""node_id\"":********,\""permission\"":\""tcc.service.deploy\"",\""env\"":\""prod\"",\""parameters\"":{\""request.deploy.method\"":\""api\"",\""request.platform\"":\""tcc_v3\"",\""request.resource.dcs\"":[\""useast5\"",\""useast8\""],\""request.resource.tag\"":\""prod\"",\""tcc.deploy.action\"":\""start\"",\""tcc.deploy.keys\"":[{\""directory_path\"":\""/default\"",\""key\"":\""simple_str_key\"",\""namespace\"":\""tiktok.gdp.auto_test\"",\""tags\"":[\""\""],\""type\"":\""\""},{\""directory_path\"":\""/default\"",\""key\"":\""simple_str_key\"",\""namespace\"":\""tiktok.gdp.auto_test\"",\""tags\"":[\""\""],\""type\"":\""\""}],\""tcc.enable.canary\"":true,\""tcc.force.review\"":true,\""tcc.platform.name\"":\""TX\"",\""tcc.service.id\"":****************,\""tcc.service.name\"":\""tiktok.gdp.auto_test\""},\""region\"":\""cn\"",\""user_type\"":\""person_account\"",\""user_name\"":\""jixiang.324\"",\""psm\"":\""\"",\""flags\"":{\""disable_with_condition\"":false,\""disable_with_temporary\"":false,\""enable_aggregate\"":false,\""enable_close_exception\"":false,\""enable_rbac_exception\"":false,\""enable_skip_condition\"":false,\""enable_skip_rbac\"":true,\""enable_skip_scp\"":false,\""enable_skip_temporary\"":false,\""enable_ttp_check\"":false,\""ignore_root_node\"":false,\""no_inherit\"":false,\""quick_return\"":false,\""show_details\"":true,\""skip_quick_return\"":false}}}}, err: json: cannot unmarshal array into Go struct field Parameters.base_resp.apply_info.parameters.tcc.deploy.keys of type string""
  }
]`
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "no permission, please apply permission and retry",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "no permission, please apply permission and retry",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(nil, false, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #7 timeout", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := `
{"message":"body.code=400, body.message=Internal Error: CallRemote: Catch Error: timeout, Trace info: \n\tcode.byted.org/iesarch/cdaas_utils/http_client/client.go:457\thttp_client.(*HertzHttpClient).doRequest\tError Info: timeout\n\tcode.byted.org/iesarch/cdaas_utils/http_client/client.go:457\thttp_client.(*HertzHttpClient).doRequest\tError Info: request error\n\tcode.byted.org/iesarch/cdaas_utils/http_client/client.go:421\thttp_client.(*HertzHttpClient).doRequestFollowRedirects\t\n\tcode.byted.org/iesarch/cdaas_utils/http_client/client.go:361\thttp_client.(*HertzHttpClient).do\t\n\tcode.byted.org/iesarch/cdaas_utils/http_client/middlewares.go:142\thttp_client.NewHertzHttpClient.NewStoppableSupportMW.func3.1\t\n\tcode.byted.org/iesarch/cdaas_utils/http_client/middlewares.go:85\thttp_client.NewHertzHttpClient.NewRetryMW.func2.1\t\n\tcode.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder/atom.go:50\tcrossborder.CallRemote\tError Info: failure body=\n\t/opt/tiger/compile_path/src/code.byted.org/devinfra/xregion_api/biz/handler/adapter.go:296\tcode.byted.org/bytefaas/bytefaas_native_hertz/biz/handler.AdapterHandler\t\n"}
`
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "downstream api timout, please manually retry, if multiple retries still fail, please contact oncall",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "downstream api timout, please manually retry, if multiple retries still fail, please contact oncall",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(nil, false, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #8 deployment status is not legal", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := `
log_id: 20241204175226BC28858AD1095F673BAF
http error status code: 500, status: 500 Internal Server Error, response body: remote or network error[remote]: biz error: can't cancel deployment which status=FAILURE
[ErrorPrimitive] failed to CancelStandardDeployment
at (*RolloutClient).CancelStandardDeployment(app/rolloutservices/utils/stubs/rollout/rollout_client.go:844)
at (*AppCenterInfraUpgradeAtom).Stop(app/rolloutatoms/appcenter_atoms/atoms/appcenter_infra_upgrade/infra_upgrade.go:329)
[ErrorPrimitive] failed to CancelStandardDeployment ppe_gdp_iac_appcenter_tiktok.effect.config_tcc
at (*AppCenterInfraUpgradeAtom).Stop(app/rolloutatoms/appcenter_atoms/atoms/appcenter_infra_upgrade/infra_upgrade.go:331)
at (*Plugin).PluginStop(app/rolloutatoms/appcenter_atoms/adapter/adapter.go:237)
at invoke(external/org_byted_code_devops_devops_go_faas/devops/v0/handler.go:42)
at Dispatcher(external/org_byted_code_devops_devops_go_faas/devops/v0/handler.go:159)
at httpHandler(app/rolloutatoms/appcenter_atoms/main.go:18)
at init.func1.HandleHttpEvent.4(external/org_byted_code_bytefaas_faas_go/internal/http_event_handler.go:58)
at funcServer.ServeHTTP(external/org_byted_code_bytefaas_faas_go/internal/entry.go:351)
at createHttpServer.createHttpServer.TraceMiddleware.func1.func2(external/org_byted_code_bytefaas_faas_go/middlewares/bytedtrace.go:39)
at HandlerFunc.ServeHTTP(GOROOT/src/net/http/server.go:2167)
at createHttpServer.CtxMiddleware.func3(external/org_byted_code_bytefaas_faas_go/middlewares/ctx.go:16)
at HandlerFunc.ServeHTTP(GOROOT/src/net/http/server.go:2167)
at (*serverConn).runHandler(external/org_byted_code_bytefaas_golang_x_net/http2/server.go:2216)
at goexit(src/runtime/asm_amd64.s:1695)
`
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "currently deployment is in its final status(succeeded/failed/outdated), please cancel currently ticket and re-create a new ticket",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "currently deployment is in its final status(succeeded/failed/outdated), please cancel currently ticket and re-create a new ticket",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(nil, false, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #9 iam rules freeze", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := `
[
  {
    "LogicalId": "grn:cac:tiktok.gdp.auto_test.release",
    "ResourceType": "TCC_BATCH_RELEASE_CONFIG",
    "Placement": "VGeo-US",
    "Status": "CreateFailed",
    "FailedReason": "rsp: {\"base_resp\":{\"error_code\":-300,\"error_type\":\"SHOULD_ESCAPE\",\"error_message\":\"Access denied, please check service strategy on console, This is an operation freeze from ByteCloud. Freeze Policy Name: 140-usttp-2024-Thansgiving-TikTok Eng Deployment Freeze, Involved Scope: |TikTok, Admins: zhenyu.li,wangxin.arlen,peihao.yuan,yuansijia.ysj,yangfeilong.beyond, Operator: jixiang.324(person_account). You can describe the necessity to apply for an exception if needed.Apply for an exception through the link below (if there are more than one, you need to apply one by one):https://cloud-ttp-us.bytedance.net/bpm/apply?cid=189\\u0026defaultValues=%7B%22galaxy_node_id%22%3A********%2C%22brn_scope%22%3A%22%22%2C%22brn_target%22%3A%22%22%2C%22policy_id%22%3A%22c550877f-e382-4d86-af3e-976103cc775c%22%2C%22psm_create%22%3A%22%22%2C%22psm_exist%22%3Atrue%2C%22exception_user%22%3A%22jixiang.324%22%2C%22permission%22%3A%22tcc.service.deploy%22%7D logid:02173310679301500000000000000000000ffff0a71bb46511258\",\"extra\":{\"logID\":\"02173310679301500000000000000000000ffff0a71bb46511258\"},\"apply_info\":{\"node_id\":********,\"permission\":\"tcc.service.deploy\",\"env\":\"prod\",\"parameters\":{\"request.deploy.method\":\"api\",\"request.platform\":\"tcc_v3\",\"request.resource.dcs\":[\"useast5\",\"useast8\"],\"request.resource.tag\":\"prod\",\"tcc.deploy.action\":\"start\",\"tcc.deploy.keys\":[{\"directory_path\":\"/default\",\"key\":\"simple_str_key\",\"namespace\":\"tiktok.gdp.auto_test\",\"tags\":[\"\"],\"type\":\"\"},{\"directory_path\":\"/default\",\"key\":\"simple_str_key\",\"namespace\":\"tiktok.gdp.auto_test\",\"tags\":[\"\"],\"type\":\"\"}],\"tcc.enable.canary\":true,\"tcc.force.review\":true,\"tcc.platform.name\":\"TX\",\"tcc.service.id\":****************,\"tcc.service.name\":\"tiktok.gdp.auto_test\"},\"region\":\"cn\",\"user_type\":\"person_account\",\"user_name\":\"jixiang.324\",\"psm\":\"\",\"flags\":{\"disable_with_condition\":false,\"disable_with_temporary\":false,\"enable_aggregate\":false,\"enable_close_exception\":false,\"enable_rbac_exception\":false,\"enable_skip_condition\":false,\"enable_skip_rbac\":true,\"enable_skip_scp\":false,\"enable_skip_temporary\":false,\"enable_ttp_check\":false,\"ignore_root_node\":false,\"no_inherit\":false,\"quick_return\":false,\"show_details\":true,\"skip_quick_return\":false}}}}, err: json: cannot unmarshal array into Go struct field Parameters.base_resp.apply_info.parameters.tcc.deploy.keys of type string"
  }
]
`
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "operation is frozen by IAM rules",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "operation is frozen by IAM rules",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(nil, false, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})
	t.Run("case #10 quota failed", func(t *testing.T) {
		mockey.PatchConvey("", t, func() {
			// input
			rawMsg := `
quota checking failed
`
			batchId := "123"
			wf := &dao.GlobalComponentWorkflow{
				WorkflowID:        "wf1",
				GlobalComponentID: "g1",
				DeploymentName:    "dp",
				Org:               "tiktok-online",
				ErrorMsg:          rawMsg,
				BatchId:           batchId,
			}
			// expectation
			sol := Solution{
				Suggestion: "insufficient resource quota, please expand resource quota and retry",
				Concise:    true,
				ErrorDetails: &ErrorDetails{
					ErrorDetails: map[string]string{
						prefixNo1 + errorReason: "insufficient resource quota, please expand resource quota and retry",
						prefixNo2 + errorMsg:    wf.ErrorMsg,
					},
				},
			}
			// Arrange
			mockey.Mock(dao.GetGlobalComponentWorkflowDao).Return(&dao.GlobalComponentWorkflowDao{}).Build()
			mockey.Mock(global.NewDpWrapperWithNamespace).Return(&global.DeploymentWrapper{}).Build()
			mockey.Mock((*global.DeploymentWrapper).GetDeployment).Return(&rolloutpb.Deployment{}, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflowWithBatchID).Return(nil, false, nil).Build()
			mockey.Mock((*dao.GlobalComponentWorkflowDao).SearchBlockingWorkflow).Return(&dao.GlobalComponentWorkflow{
				WorkflowID:        "wf2",
				GlobalComponentID: "g1",
			}, true, nil).Build()
			// Act
			got := GetWorkflowSolution(wf, true)
			// Assert
			convey.So(got, convey.ShouldResemble, sol)
		})
	})

}
