package global

import (
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/service/errors"

	"context"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/internal/global"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/lang/gg/gslice"
)

func (s *Service) ValidateGlobalComponentServices(ctx context.Context, req *app.ValidateGlobalServiceOwnershipRequest) (res *app.ValidateGlobalServiceOwnershipResponse, err error) {
	psms := make([]string, 0)
	gslice.ForEach(req.Services, func(t *app.RegionalTCEService) {
		if t == nil {
			return
		}
		psms = append(psms, t.Psms...)
		return
	})
	psmSet := util.DuplicateRemoving(psms)
	// ownership validation
	resp, err := global.ValidateOwnershipByPSM(ctx, psmSet, req.Services)
	if err != nil {
		return nil, errors.BitsError(err, constants.HTTPStatusPermissionDenied)
	}
	return resp, nil
}
