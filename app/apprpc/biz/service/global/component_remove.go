package global

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/internal/global"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/fg"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/service/errors"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app/resources"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/deploymentpb"
)

func (s *Service) RemoveGlobalComponent(ctx context.Context, req *app.RemoveGlobalComponentRequest) (res *app.RemoveGlobalComponentResponse, err error) {
	// 精确查找
	var globalComponent *dao.GlobalComponentDocument
	var exists bool
	globalComponent, exists, err = dao.GetGlobalComponentDao().GetByID(ctx, req.GlobalComponentId)
	if err != nil {
		return nil, errors.BitsError(err, constants.HTTPStatusInternalServerError)
	}
	if !exists {
		return &app.RemoveGlobalComponentResponse{}, nil
	}

	// ownership validation
	if !fg.MatchAdmin(ctx) {
		services := make(map[resources.ControlPlane][]string)
		tceServices := globalComponent.Resources.TCEServices
		for _, s := range tceServices {
			for _, rs := range s.RegionalInfos {
				if len(services[rs.ControlPlane]) == 0 {
					services[rs.ControlPlane] = make([]string, 0)
				}
				services[rs.ControlPlane] = append(services[rs.ControlPlane], rs.Psm)
			}
		}
		regionalServices := make([]*app.RegionalTCEService, 0)
		psmSet := make([]string, 0)
		for cp, s := range services {
			regionalServices = append(regionalServices, &app.RegionalTCEService{
				ControlPlane: cp,
				Psms:         s,
			})
			psmSet = append(psmSet, s...)
		}
		checkResp, err := global.ValidateOwnershipByPSM(ctx, psmSet, regionalServices)
		if err != nil {
			return nil, errors.BitsError(err, constants.HTTPStatusInternalServerError)
		}
		if len(checkResp.LackingOwnershipInfos) != 0 {
			return nil, errors.NewBitsErrorf(constants.HTTPStatusPermissionDenied, "no owner of psms in specified controle planes: %v", checkResp.LackingOwnershipInfos)
		}
	}

	if globalComponent.Constrained {
		// 清理deployment
		dpWrapper := global.NewDpWrapper(globalComponent.Name, globalComponent.Description)
		_, err := dpWrapper.CleanupDeployment(ctx, &deploymentpb.CleanupDeploymentRequest{
			Namespace:      constants.ROSNameSpace,
			DeploymentName: globalComponent.Name,
		})
		if err != nil {
			return nil, errors.NewBitsError(constants.HTTPStatusInternalServerError, "remove deployment failed")
		}
	}
	_, err = dao.GetGlobalComponentDao().RemoveByID(ctx, req.GlobalComponentId)
	if err != nil {
		return nil, errors.NewBitsError(constants.HTTPStatusInternalServerError, "remove global component failed")
	}
	err = dao.GetDeploymentCacheDao().Delete(ctx, globalComponent.Name)
	if err != nil {
		return nil, errors.NewBitsError(constants.HTTPStatusInternalServerError, "remove global component cache failed")
	}
	return &app.RemoveGlobalComponentResponse{}, nil
}
