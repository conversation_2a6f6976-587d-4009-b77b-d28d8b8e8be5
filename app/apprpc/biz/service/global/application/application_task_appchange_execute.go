package application

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/internal/global/application/controller"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/service/errors"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
)

func (s *Service) ExecuteChangeOrder(ctx context.Context, req *app.ExecuteAppChangeTaskStepReq) (resp *app.ExecuteAppChangeTaskStepResp, err error) {
	task, exists, gErr := dao.GetTaskByTaskID[dao.AppChangeInfo](ctx, req.OrderId)
	if gErr != nil {
		return nil, errors.BitsError(gErr, constants.HTTPStatusInternalServerError)
	}
	if !exists {
		return nil, errors.NewBitsErrorf(constants.HTTPStatusNotFound, "task[ID=%s] not found", req.OrderId)
	}
	// validate step
	if task.AdditionalInfo.Step != req.GetStep() {
		return nil, errors.NewBitsErrorf(constants.HTTPStatusInvalidParams, "current step is [%s]", task.AdditionalInfo.Step)
	}
	ctrl := controller.GetAppchangeController(task.TaskID, false)
	_, err = ctrl.Execute(ctx, task.TaskID, true)
	if err != nil {
		return nil, err
	}
	return &app.ExecuteAppChangeTaskStepResp{}, nil
}
