package application

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/codebase"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/internal/global/application/controller/utils"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/service/errors"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/lang/gg/gslice"
)

func (s *Service) GetLatestResources(ctx context.Context, req *app.GetLatestResourcesReq) (resp *app.GetLatestResourcesResp, err error) {
	gb, existed, err := dao.GetGlobalComponentDao().GetByID(ctx, req.AppId)
	if err != nil {
		return nil, err
	}
	if !existed {
		return nil, errors.NewBitsErrorf(constants.HTTPStatusNotFound, "app[ID=%s] not found", req.AppId)
	}

	commitSummary, err := codebase.GetCommitSummary(ctx, gb.ApplicationDetail.IacRepo)
	if err != nil {
		return nil, err
	}

	globalResources := gslice.Map(gb.ApplicationDetail.ManagedResources, func(r *dao.GlobalResource) *dao.GlobalResource {
		r.IacRepo.LatestCommit = commitSummary.CommitID
		r.IacRepo.CommitMsg = commitSummary.CommitMsg
		return r
	})
	// update application info in global component
	gb.ApplicationDetail.ManagedResources = globalResources
	err = dao.GetGlobalComponentDao().UpdateApplicationInfo(ctx, gb.GlobalComponentID, gb.ApplicationDetail)
	if err != nil {
		return nil, errors.BitsError(err, constants.HTTPStatusInternalServerError)
	}
	return &app.GetLatestResourcesResp{
		Resources: utils.ConvertResourceDocToResources(globalResources, gb.ApplicationDetail.IacDefaultBranch),
	}, nil
}
