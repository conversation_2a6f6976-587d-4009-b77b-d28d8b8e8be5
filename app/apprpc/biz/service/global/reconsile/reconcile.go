package reconsile

import (
	"context"
	"fmt"
	"sync"
	"time"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/cache"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/internal/global/application/controller"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util/jwtutil"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util/statusutil"
	"code.byted.org/devinfra/hagrid/libs/routinerecover"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logid"
	"code.byted.org/gopkg/logs/v2/log"
)

type Reconciler struct {
	ticker       *time.Ticker
	gLockFactory cache.DistributedLockFactory
	stopping     chan struct{}
	stopped      chan struct{}
}

type TaskManager interface {
}

var instance *Reconciler
var once sync.Once

const (
	expiration                     = 20 * time.Second
	statusCheckIntervalSecs        = 3 * time.Second
	executeCreationTaskCachePrefix = "ExecuteCreationTask"
)

func GetInstance() *Reconciler {
	once.Do(func() {
		instance = &Reconciler{
			ticker:       time.NewTicker(statusCheckIntervalSecs),
			stopped:      make(chan struct{}),
			stopping:     make(chan struct{}),
			gLockFactory: cache.NewDistributedLockFactory(),
		}
	})
	return instance
}

// only list task not yet finished with step job not finished;
// why? step is not auto transferred so no need to reconcile, another reason, execute step api will deal with step transferring for now.
func searchUnfinishedTasks() ([]*dao.TaskSummary, error) {
	ongoingTasks, err := dao.GetOngoingTaskSummaries(context.Background())
	if err != nil {
		return nil, err
	}
	return ongoingTasks, nil
}

func (s *Reconciler) Start() {
	log.V2.Info().Str("[reconcile.go] starting").Emit()
	for {
		select {
		case <-s.ticker.C:
			log.V2.Info().Str("[reconcile.go] enter tick loop").Emit()
			tasks, err := searchUnfinishedTasks()
			if err != nil {
				log.V2.Error().Str("[reconcile.go] searches tasks failed").Str("task_failure", fmt.Sprintf(" [%v]", err)).Emit()
				continue
			}

			taskIDs := make([]string, len(tasks))
			for i, task := range tasks {
				taskIDs[i] = task.TaskID
			}

			log.V2.Info().Str("[reconcile.go] deals with tasks").
				KV("task_count", len(tasks)).
				KV("task list", taskIDs).
				Emit()
			var wg sync.WaitGroup
			wg.Add(len(tasks))
			for _, task := range tasks {
				// Capture task in a local variable to avoid closure issue
				tmp := task
				routinerecover.SafeGo(func() {
					defer wg.Done()
					lock := s.gLockFactory.NewDistributedLock(generateTaskKey(tmp.TaskType, tmp.TaskID))
					ctx := context.Background()
					err = lock.TryLock(ctx, expiration)
					defer lock.Unlock(ctx)
					if err != nil {
						log.V2.Info().With(ctx).Str("[reconcile.go] try lock failed").
							Str("task_type", fmt.Sprintf(" [%v]", tmp.TaskType)).
							Str("task_id", fmt.Sprintf(" [%v]", tmp.TaskID)).
							Str("err", fmt.Sprintf(" [%v]", err)).
							Emit()
						return
					}
					// if lock task with failure, do nothing
					ctx = auth.WithUsername(ctx, tmp.UserCtx.Username)
					if tmp.UserCtx.JwtToken == "" || tmp.UserCtx.Username == "" {
						log.V2.Error().With(ctx).Str("[reconcile.go] missing username or jwt token").
							Str("task_type", fmt.Sprintf(" [%v]", tmp.TaskType)).
							Str("task_id", fmt.Sprintf(" [%v]", tmp.TaskID)).
							Emit()
						return
					}
					logId := logid.GenLogID()
					ctx = ctxvalues.SetLogID(ctx, logId)
					jwt, err := jwtutil.GetUserJWT(ctx, tmp.UserCtx.Username)
					if err != nil {
						log.V2.Info().With(ctx).Str("[reconcile.go] get jwt token failed").
							Str("task_type", fmt.Sprintf(" [%v]", tmp.TaskType)).
							Str("task_id", fmt.Sprintf(" [%v]", tmp.TaskID)).
							Str("err", fmt.Sprintf(" [%v]", err)).
							Str("log_id", fmt.Sprintf(" [%v]", logId)).
							Emit()
						return
					}
					ctx = auth.WithJWT(ctx, jwt)
					log.V2.Info().With(ctx).Str("[reconcile.go] deals with tasks").
						Str("task_type", fmt.Sprintf(" [%v]", tmp.TaskType)).
						Str("task_id", fmt.Sprintf(" [%v]", tmp.TaskID)).
						Str("log_id", fmt.Sprintf(" [%v]", logId)).
						Emit()
					switch tmp.TaskType {
					case dao.TASK_APP_CREATION:
						ctrl := controller.GetApplicationCreationController(tmp.TaskID, false)
						taskFromDB, eErr := ctrl.Execute(ctx, tmp.TaskID, false)
						if eErr != nil {
							taskFromDB.Error = eErr.Error()
							log.V2.Error().With(ctx).Str("[reconcile.go] deals with tasks failed in app creation").
								KV("task id", task.TaskID).
								Error(eErr).Emit()
							if err := dao.UpdateTaskAdditionalInfoAndErr(ctx, task.TaskID, app.TaskStatus_TASK_STATUS_FAILED,
								app.StepStatus_STEP_STATUS_FAILED, taskFromDB.AdditionalInfo, taskFromDB.Error); err != nil {
								log.V2.Error().With(ctx).Str("[reconcile.go] failed to save task to dbf").
									KV("task", taskFromDB).Emit()
							}
						}
					case dao.TASK_NEW_PLACEMENT:
						ctrl := controller.GetNewPlacementController(tmp.TaskID, false)
						taskFromDB, eErr := ctrl.Execute(ctx, tmp.TaskID, false)
						if eErr != nil {
							taskFromDB.Error = eErr.Error()
							log.V2.Error().With(ctx).Str("[reconcile.go] deals with tasks failed in new placement").
								KV("task id", task.TaskID).
								Error(eErr).Emit()

							if err := dao.UpdateTaskAdditionalInfoAndErr(ctx, task.TaskID, app.TaskStatus_TASK_STATUS_FAILED,
								app.StepStatus_STEP_STATUS_FAILED, taskFromDB.AdditionalInfo, taskFromDB.Error); err != nil {
								log.V2.Error().With(ctx).Str("[reconcile.go] failed to save task to db").
									KV("task", taskFromDB).Emit()
							}
						}
					case dao.TASK_APP_CHANGE:
						ctrl := controller.GetAppchangeController(tmp.TaskID, false)
						taskFromDB, eErr := ctrl.Execute(ctx, tmp.TaskID, false)
						if eErr != nil {
							taskFromDB.Error = eErr.Error()
							log.V2.Error().With(ctx).Str("[reconcile.go] deals with tasks failed in app change").
								KV("task id", task.TaskID).
								Error(eErr).Emit()

							if err := dao.UpdateTaskAdditionalInfoAndErr(ctx, tmp.TaskID, app.TaskStatus_TASK_STATUS_FAILED,
								app.StepStatus_STEP_STATUS_FAILED, taskFromDB.AdditionalInfo, taskFromDB.Error); err != nil {
								log.V2.Error().With(ctx).Str("[reconcile.go] failed to save task to db").
									KV("task", taskFromDB).Emit()
							}

							if err := statusutil.UpdateAppWithStatus(ctx, taskFromDB.AdditionalInfo.AppID,
								app.AppStatus_APP_STATUS_EXCEPTION); err != nil {
								log.V2.Error().With(ctx).Str("[reconcile.go] failed to update app status to exception").
									KV("task", taskFromDB).Emit()
							}
						}
					case dao.TASK_APP_DRIFT:
						ctrl := controller.GetAppDriftController(tmp.TaskID, false)
						taskFromDB, eErr := ctrl.Execute(ctx, tmp.TaskID, false)
						if eErr != nil {
							taskFromDB.Error = eErr.Error()
							log.V2.Error().With(ctx).Str("[reconcile.go] deals with tasks failed in app drift").
								KV("task id", task.TaskID).
								Error(eErr).Emit()
							if err := dao.UpdateTaskAdditionalInfoAndErr(ctx, tmp.TaskID, app.TaskStatus_TASK_STATUS_FAILED,
								app.StepStatus_STEP_STATUS_FAILED, taskFromDB.AdditionalInfo, taskFromDB.Error); err != nil {
								log.V2.Error().With(ctx).Str("[reconcile.go] failed to save task to db").
									KV("task", taskFromDB).Emit()
							}
						}
					}
					return
				})
			}
			wg.Wait()

		case <-s.stopping:
			log.V2.Info().Str("[reconcile.go] stopping").Emit()
			s.ticker.Stop()
			close(s.stopped)
		}
	}
}

func (s *Reconciler) Stop() {
	s.stopping <- struct{}{}
	<-s.stopped
}

func generateTaskKey(taskType dao.CreationType, taskID string) string {
	return fmt.Sprintf("%s/%s/%s", executeCreationTaskCachePrefix, taskType, taskID)
}
