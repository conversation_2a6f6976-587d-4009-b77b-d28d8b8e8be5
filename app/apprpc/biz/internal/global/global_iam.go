package global

import (
	"context"
	"sync"

	"google.golang.org/genproto/googleapis/rpc/code"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util"
	"code.byted.org/devinfra/hagrid/pkg/herror"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app/resources"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/deploymentpb"
	"code.byted.org/lang/gg/gslice"
)

func ValidateOwnershipByPSM(ctx context.Context, psmList []string, services []*app.RegionalTCEService) (*app.ValidateGlobalServiceOwnershipResponse, error) {
	result := new(app.ValidateGlobalServiceOwnershipResponse)
	result.LackingOwnershipInfos = make([]*app.LackingOwnershipInfo, 0)
	lackingOwnershipMap := make(map[string][]*deploymentpb.LackingAclInfo)
	dpWrapper := NewDpWrapper("none", "none")
	// 聚合满足services约束的所有控制面
	workers := make([]func(baseCtx *util.BaseCtx) error, 0, len(psmList))
	psmMap := getPSMControlPlaneMap(services)
	userName := util.GetUsernameFromCtx(ctx)
	for i := range psmList {
		workers = append(workers, func(psm string) func(baseCtx *util.BaseCtx) error {
			return func(baseCtx *util.BaseCtx) error {
				baseCtx.Locker.Lock()
				defer baseCtx.Locker.Unlock()
				req := &deploymentpb.GlobalAclUserCheckRequest{
					ScopeType:  "psm",
					ScopeValue: psm,
					UserName:   userName,
					RoleName:   "owner",
					Vgeos:      getVGeosFromControlPlane(psmMap[psm]),
				}
				aclResp, err := dpWrapper.CheckACLForUser(ctx, req)
				if err != nil {
					return err
				}
				if lackingOwnershipMap[psm] == nil {
					lackingOwnershipMap[psm] = make([]*deploymentpb.LackingAclInfo, 0)
				}
				lackingOwnershipMap[psm] = append(lackingOwnershipMap[psm], aclResp.LackingAclInfos...)
				return nil
			}
		}(psmList[i]))
	}
	err := util.ConcurrentBatchProcess(ctx, &util.BaseCtx{Locker: &sync.Mutex{}}, workers...)
	if err != nil {
		return nil, herror.Wrap(err, code.Code_INTERNAL, "query iam owners failed")
	}
	for i, infos := range lackingOwnershipMap {
		for _, v := range infos {
			result.LackingOwnershipInfos = append(result.LackingOwnershipInfos, &app.LackingOwnershipInfo{
				Psm:          i,
				ControlPlane: constants.VGeoToControlPlane[constants.VGeo(v.Vgeo)],
			})
		}
	}
	return result, nil
}

func getPSMControlPlaneMap(services []*app.RegionalTCEService) map[string][]resources.ControlPlane {
	serviceMap := make(map[string][]resources.ControlPlane)
	for _, value := range services {
		for j := range value.Psms {
			if len(serviceMap[value.Psms[j]]) == 0 {
				serviceMap[value.Psms[j]] = make([]resources.ControlPlane, 0)
			}
			serviceMap[value.Psms[j]] = append(serviceMap[value.Psms[j]], value.ControlPlane)
		}
	}
	return serviceMap
}

func getVGeosFromControlPlane(controlPlanes []resources.ControlPlane) []string {
	return gslice.Map(controlPlanes, func(cp resources.ControlPlane) string {
		return string(constants.ControlPlaneToVGeo[cp])
	})
}
