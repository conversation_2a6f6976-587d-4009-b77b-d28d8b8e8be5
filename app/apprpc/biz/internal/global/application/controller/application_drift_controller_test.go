package controller

import (
	"context"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/gitlab"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/tccsdk"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	appauth "code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/parser"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/stubs/entry"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/dryrunpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/generatorpb"
	"code.byted.org/gopkg/metainfo"
)

func TestApplicationDriftController_Execute(t *testing.T) {
	ctx := context.Background()
	ctx = metainfo.WithValue(ctx, appauth.UsernameKey, "yongning.hu")
	ctx = metainfo.WithValue(ctx, appauth.JWTKey, "jwt")
	t.Run("case #1 task starting", func(t *testing.T) {
		mockey.PatchConvey("first job started with failure", t, func() {
			// Input
			taskID := "123"
			// Arrange
			mockey.Mock(dao.GetTaskByTaskID[dao.AppDriftInfo], mockey.OptGeneric).Return(&dao.AppDriftTask{
				TaskID:     taskID,
				Status:     app.TaskStatus_TASK_STATUS_IN_PROGRESS.String(),
				StepStatus: app.StepStatus_STEP_STATUS_FINISHED.String(),
				AdditionalInfo: dao.AppDriftInfo{
					Step: app.AppDriftStep_APP_DRIFT_STEP_UNSPECIFIED,
				},
			}, true, nil).Build()
			mockey.Mock(dao.UpdateTaskAdditionalInfo[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			// Act
			ctrl := GetAppDriftController(taskID, false)
			_, err := ctrl.Execute(ctx, taskID, true)

			// Assert
			convey.So(err != nil, convey.ShouldEqual, true)
		})
	})

	t.Run("case #2 task starting", func(t *testing.T) {
		mockey.PatchConvey("job starting from clean state", t, func() {
			// Input
			taskID := "123"
			taskInput := &dao.AppDriftTask{
				TaskID:     taskID,
				Status:     app.TaskStatus_TASK_STATUS_UNSPECIFIED.String(),
				StepStatus: app.StepStatus_STEP_STATUS_UNSPECIFIED.String(),
				AdditionalInfo: dao.AppDriftInfo{
					AppDriftHolder: &dao.AppDriftHolder{},
					Step:           app.AppDriftStep_APP_DRIFT_STEP_DRIFT_DIFF_REVIEW,
				},
			}

			requestID := "234"
			driftResp := &dryrunpb.GenerateDiffAsyncResponse{
				RequestId: requestID,
			}
			// Arrange
			mockey.Mock(dao.GetTaskByTaskID[dao.AppDriftInfo], mockey.OptGeneric).Return(taskInput, true, nil).Build()
			mockey.Mock(dao.UpdateTaskAdditionalInfo[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			mockey.Mock((*entry.DeployEntryServiceClient).GenerateDriftDiffAsync).Return(driftResp, nil, nil).Build()
			// Act
			ctrl := GetAppDriftController(taskID, false)
			_, err := ctrl.Execute(ctx, taskID, false)

			// Assert
			convey.So(err == nil, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftHolder.RequestID == requestID, convey.ShouldEqual, true)
		})
	})

	t.Run("case #3 task query drift", func(t *testing.T) {
		mockey.PatchConvey("job starting from query drift state", t, func() {
			// Input
			taskID := "123"
			requestID := "234"
			taskInput := &dao.AppDriftTask{
				TaskID:     taskID,
				Status:     app.TaskStatus_TASK_STATUS_IN_PROGRESS.String(),
				StepStatus: app.StepStatus_STEP_STATUS_IN_PROGRESS.String(),
				AdditionalInfo: dao.AppDriftInfo{
					AppDriftHolder: &dao.AppDriftHolder{
						RequestID: requestID,
					},
					Step: app.AppDriftStep_APP_DRIFT_STEP_DRIFT_DIFF_REVIEW,
				},
			}

			driftResp := &dryrunpb.GetDiffAsyncResponse{}

			cErr := parser.ConvertJsonToPB(driftRespJson, driftResp)
			convey.So(cErr == nil, convey.ShouldEqual, true)
			// Arrange
			mockey.Mock(dao.GetTaskByTaskID[dao.AppDriftInfo], mockey.OptGeneric).Return(taskInput, true, nil).Build()
			mockey.Mock(dao.UpdateTaskAdditionalInfo[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			mockey.Mock((*entry.DeployEntryServiceClient).GetDriftDiffAsync).Return(driftResp, nil, nil).Build()
			// Act
			ctrl := GetAppDriftController(taskID, false)
			_, err := ctrl.Execute(ctx, taskID, false)

			// Assert
			convey.So(err == nil, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftHolder.RequestID == requestID, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftHolder.DiffDone == true, convey.ShouldEqual, true)
		})
	})

	t.Run("case #4 create drift mr", func(t *testing.T) {
		mockey.PatchConvey("job starting mr initial create", t, func() {
			// Input
			taskID := "123"
			requestID := "234"
			taskInput := &dao.AppDriftTask{
				TaskID:     taskID,
				Status:     app.TaskStatus_TASK_STATUS_IN_PROGRESS.String(),
				StepStatus: app.StepStatus_STEP_STATUS_UNSPECIFIED.String(),
				AdditionalInfo: dao.AppDriftInfo{
					AppDriftHolder: &dao.AppDriftHolder{
						RequestID: requestID,
					},
					AppDriftMRHolder: &dao.AppDriftMRHolder{},
					Step:             app.AppDriftStep_APP_DRIFT_STEP_SOT_MR,
				},
			}

			mrResp := &generatorpb.UpdateOnlineBranchAsyncResponse{
				QueryId: requestID,
			}

			// Arrange
			mockey.Mock(dao.GetTaskByTaskID[dao.AppDriftInfo], mockey.OptGeneric).Return(taskInput, true, nil).Build()
			mockey.Mock(dao.UpdateTaskAdditionalInfo[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			mockey.Mock((*entry.DeployEntryServiceClient).UpdateOnlineBranch).Return(mrResp, nil, nil).Build()
			// Act
			ctrl := GetAppDriftController(taskID, false)
			_, err := ctrl.Execute(ctx, taskID, false)

			// Assert
			convey.So(err == nil, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftMRHolder.RequestID == requestID, convey.ShouldEqual, true)
		})
	})

	t.Run("case #5 query mr", func(t *testing.T) {
		mockey.PatchConvey("job starting mr query state", t, func() {
			// Input
			taskID := "123"
			requestID := "234"
			taskInput := &dao.AppDriftTask{
				TaskID:     taskID,
				Status:     app.TaskStatus_TASK_STATUS_IN_PROGRESS.String(),
				StepStatus: app.StepStatus_STEP_STATUS_IN_PROGRESS.String(),
				AdditionalInfo: dao.AppDriftInfo{
					AppDriftHolder: &dao.AppDriftHolder{
						RequestID: requestID,
					},
					AppDriftMRHolder: &dao.AppDriftMRHolder{},
					Step:             app.AppDriftStep_APP_DRIFT_STEP_SOT_MR,
				},
			}

			mrResp := &generatorpb.QueryUpdateOnlineBranchAsyncResponse{
				IsFinished:       true,
				ErrorMessage:     "",
				HasMergeConflict: false,
				MrInfo: &generatorpb.QueryUpdateOnlineBranchAsyncResponse_MergeRequestInfo{
					MrId:     "123",
					MrUrl:    "https://code.byted.org/devinfra/hagrid/merge_requests/19912",
					MrNumber: 19912,
					CommitId: "1234",
					RepoId:   "1234",
				},
			}

			// Arrange
			mockey.Mock(dao.GetTaskByTaskID[dao.AppDriftInfo], mockey.OptGeneric).Return(taskInput, true, nil).Build()
			mockey.Mock(dao.UpdateTaskAdditionalInfo[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			mockey.Mock(dao.UpdateTaskResourcesAndAdditionalInfoWithSuccess[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			mockey.Mock((*entry.DeployEntryServiceClient).QueryUpdateOnlineBranch).Return(mrResp, nil, nil).Build()
			mockey.Mock(tccsdk.GetGitLabToken).Return("some gitlab token").Build()
			mockey.Mock(gitlab.IsMRMerged).Return(true, nil).Build()
			// Act
			ctrl := GetAppDriftController(taskID, false)
			_, err := ctrl.Execute(ctx, taskID, false)

			// Assert
			convey.So(err == nil, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftMRHolder.IsMerged == false, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftMRHolder.MergeNumber == 19912, convey.ShouldEqual, true)

			//mockey.Mock(gitlab.IsMRMerged).Return(true, nil).Build()
			_, err = ctrl.Execute(ctx, taskID, false)
			convey.So(err == nil, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftMRHolder.IsMerged == true, convey.ShouldEqual, true)
		})
	})

	t.Run("case #6 task query drift transfer step", func(t *testing.T) {
		mockey.PatchConvey("job starting from finished query drift state", t, func() {
			// Input
			taskID := "123"
			requestID := "234"
			taskInput := &dao.AppDriftTask{
				TaskID:     taskID,
				Status:     app.TaskStatus_TASK_STATUS_IN_PROGRESS.String(),
				StepStatus: app.StepStatus_STEP_STATUS_FINISHED.String(),
				AdditionalInfo: dao.AppDriftInfo{
					AppDriftHolder: &dao.AppDriftHolder{
						RequestID: requestID,
					},
					Step: app.AppDriftStep_APP_DRIFT_STEP_DRIFT_DIFF_REVIEW,
				},
			}

			driftResp := &dryrunpb.GetDiffAsyncResponse{}

			cErr := parser.ConvertJsonToPB(driftRespJson, driftResp)
			convey.So(cErr == nil, convey.ShouldEqual, true)
			// Arrange
			mockey.Mock(dao.GetTaskByTaskID[dao.AppDriftInfo], mockey.OptGeneric).Return(taskInput, true, nil).Build()
			mockey.Mock(dao.UpdateTaskAdditionalInfo[dao.AppDriftInfo], mockey.OptGeneric).Return(nil).Build()
			mockey.Mock((*entry.DeployEntryServiceClient).GetDriftDiffAsync).Return(driftResp, nil, nil).Build()
			// Act
			ctrl := GetAppDriftController(taskID, false)
			_, err := ctrl.Execute(ctx, taskID, true)

			// Assert
			convey.So(err == nil, convey.ShouldEqual, true)
			convey.So(taskInput.AdditionalInfo.AppDriftHolder.RequestID == requestID, convey.ShouldEqual, true)
		})
	})
}
