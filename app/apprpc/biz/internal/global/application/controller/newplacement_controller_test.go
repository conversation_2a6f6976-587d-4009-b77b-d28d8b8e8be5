package controller

import (
	"context"
	"fmt"
	"sort"
	"testing"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util"
	"code.byted.org/devinfra/hagrid/app/permissionservices/permissionapi/sdk"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/permission"
	"github.com/bytedance/mockey"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/gitlab"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	appauth "code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util/statusutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/parser"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/stubs/entry"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app/resources"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/resourcespb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/deploymentpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/generatorpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/importerpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
	"code.byted.org/gopkg/metainfo"
	"code.byted.org/lang/gg/gptr"
)

func TestExecute(t *testing.T) {
	ctx := context.Background()
	ctx = metainfo.WithValue(ctx, appauth.UsernameKey, "yifei.feng")
	ctx = metainfo.WithValue(ctx, appauth.JWTKey, "somejwt")

	mockey.PatchConvey("TestReconcileNewPlacement", t, func() {
		// mock db
		mockey.Mock(dao.GetOngoingTaskSummaries).To(dao.GetOngoingTaskSummariesMock).Build()
		mockey.MockGeneric(dao.CreateTask[dao.NewPlacementInfo]).To(dao.CreateTaskMock[dao.NewPlacementInfo]).Build()
		mockey.MockGeneric(dao.GetTaskByTaskID[dao.NewPlacementInfo]).To(dao.GetTaskByTaskIDMock[dao.NewPlacementInfo]).Build()
		mockey.MockGeneric(dao.UpdateTaskAdditionalInfo[dao.NewPlacementInfo]).To(dao.UpdateTaskAdditionalInfoMock[dao.NewPlacementInfo]).Build()
		mockey.MockGeneric(dao.UpdateTaskAdditionalInfoAndErr[dao.NewPlacementInfo]).To(dao.UpdateTaskAdditionalInfoAndErrMock[dao.NewPlacementInfo]).Build()
		mockey.MockGeneric(dao.UpdateTaskResourcesAndAdditionalInfoWithSuccess[dao.NewPlacementInfo]).To(dao.UpdateTaskResourcesAndAdditionalInfoWithSuccessMock[dao.NewPlacementInfo]).Build()

		mockey.Mock(dao.UpdateSelectedResources).To(dao.UpdateSelectedResourcesMock).Build()
		mockey.Mock((*sdk.PermissionClient).CheckPermission).Return(&permission.PermissionCheckResp{HasPassed: true}, nil).Build()

		// mock rollout api
		mockey.Mock((*entry.DeployEntryServiceClient).ResourceImporter).Return(&importerpb.ImportResourceResponse{
			Result: uuid.NewString(),
		}, nil, nil).Build()
		mockey.Mock((*entry.DeployEntryServiceClient).QueryImport).Return(&importerpb.QueryImportResponse{
			Finished:  true,
			Resources: "some import result",
		}, nil, nil).Build()
		mockey.Mock((*entry.DeployEntryServiceClient).CreateImportMRAsync).Return(&generatorpb.CreateImportMRAsyncResponse{
			QueryId: uuid.NewString(),
		}, nil, nil).Build()
		mockey.Mock((*entry.DeployEntryServiceClient).QueryImportMR).Return(
			mockey.Sequence(&generatorpb.QueryImportMRAsyncResponse{
				IsFinished: false,
			}, nil, nil).Then(&generatorpb.QueryImportMRAsyncResponse{
				IsFinished:      true,
				MergeRequestUrl: "https://code.byted.org/devinfra/rollout_sots/merge_requests/283",
				FeatureBranch:   "feat/rollout_import_7665",
			}, nil, nil)).Build()

		mockey.Mock((*entry.DeployEntryServiceClient).CreateBatchEditMR).Return(&generatorpb.CreateBatchEditMRResponse{
			QueryId: uuid.NewString(),
		}, nil, nil).Build()

		mockey.Mock((*entry.DeployEntryServiceClient).QueryBatchEditMR).Return(
			mockey.Sequence(&generatorpb.QueryBatchEditMRResponse{
				Finished: false,
			}, nil, nil).Then(&generatorpb.QueryBatchEditMRResponse{
				Finished:        true,
				MergeRequestUrl: "https://code.byted.org/devinfra/rollout_sots/merge_requests/284",
				ResourceInputs: []*rolloutpb.ResourceInput{
					{
						Name:         "grn:tce:iac.rollout.input:cls:default",
						ResourceType: rolloutpb.ResourceType_TCE_CLUSTER,
						SotLocation: &rolloutpb.SotLocation{
							RepoNamespace: "devinfra",
							RepoName:      "rollout_sots",
							FilePath:      "tce/iac/rollout/input/grn:tce:iac.rollout.input:cls:default.yaml",
						},
						TemplateName: "tce_cluster.default",
						GroupName:    gptr.Of("tcetest1"),
					},
					{
						Name:         "grn:tce:iac.rollout.input:svc",
						ResourceType: rolloutpb.ResourceType_TCE_SERVICE,
						SotLocation: &rolloutpb.SotLocation{
							RepoNamespace: "devinfra",
							RepoName:      "rollout_sots",
							FilePath:      "tce/iac/rollout/input/grn:tce:iac.rollout.input:svc.yaml",
						},
						TemplateName: "tce_service.default",
						GroupName:    gptr.Of("tcetest1"),
					},
				},
			}, nil, nil)).Build()

		mockey.Mock((*entry.DeployEntryServiceClient).RegisterDeployment).Return(&generatorpb.RegisterDeploymentResponse{}, nil, nil).Build()
		mockey.Mock((*entry.DeployEntryServiceClient).RolloutSingleDeployment).Return(&generatorpb.RolloutSingleDeploymentResponse{
			DeploymentId: uuid.NewString(),
		}, nil, nil).Build()
		stateResp1 := &generatorpb.QueryDeploymentStateResponse{
			DeploymentStateInfo: &rolloutpb.DeploymentStateInfo{
				CurrState: rolloutpb.DeploymentState_GENERATING_DIFF,
			},
		}
		stateResp2 := &generatorpb.QueryDeploymentStateResponse{
			DeploymentStateInfo: &rolloutpb.DeploymentStateInfo{
				CurrState: rolloutpb.DeploymentState_PENDING_DIFF_CONFIRM,
			},
		}
		stateResp3 := &generatorpb.QueryDeploymentStateResponse{
			DeploymentStateInfo: &rolloutpb.DeploymentStateInfo{
				CurrState: rolloutpb.DeploymentState_ACTUATING,
			},
		}
		stateResp4 := &generatorpb.QueryDeploymentStateResponse{
			DeploymentStateInfo: &rolloutpb.DeploymentStateInfo{
				CurrState: rolloutpb.DeploymentState_READY,
			},
		}
		mockey.Mock((*entry.DeployEntryServiceClient).QueryDeploymentState).Return(
			mockey.Sequence(stateResp1, nil, nil).Times(1). // gen diff once
				Then(stateResp2, nil, nil).Times(1). // pending diff once
				Then(stateResp3, nil, nil).Times(3). // actuating 3 Times
				Then(stateResp4, nil, nil).Times(1)). // ready once
			Build()
		mockey.Mock((*entry.DeployEntryServiceClient).ActuateSingleDeployment).
			Return(&deploymentpb.ActuateSingleDeploymentResponse{}, nil, nil).Build()

		dplResp1 := &rolloutpb.Deployment{
			OverallStatus: &rolloutpb.OverallStatus{
				Status: rolloutpb.DeploymentStatus_IN_PROGRESS,
			},
		}

		dplResp1Yml, cpbErr1 := parser.ConvertPBToYaml(dplResp1)
		assert.NoError(t, cpbErr1)

		cls := &resourcespb.TCECluster{
			Meta: &resourcespb.TCECluster_ClusterInfoMeta{
				Name:       gptr.Of("default"),
				ServicePsm: gptr.Of("test.psm.fake"),
			},
		}

		serv := &resourcespb.TCEService{
			Meta: &resourcespb.TCEService_Meta{
				Psm: gptr.Of("test.psm.fake"),
			},
		}
		anyCls := new(anypb.Any)
		mErr := anypb.MarshalFrom(anyCls, cls, proto.MarshalOptions{})
		assert.NoError(t, mErr)

		anyServ := new(anypb.Any)
		mErr = anypb.MarshalFrom(anyServ, serv, proto.MarshalOptions{})
		assert.NoError(t, mErr)

		cid1 := &resourcespb.TCEClusterPrimaryIdentifier{
			Meta: &resourcespb.TCEClusterPrimaryIdentifier_ClusterInfoMeta{
				Id: gptr.Of[uint64](123456),
			},
		}
		cid1Any, nErr1 := anypb.New(cid1)
		assert.NoError(t, nErr1)

		cid2 := &resourcespb.TCEClusterPrimaryIdentifier{
			Meta: &resourcespb.TCEClusterPrimaryIdentifier_ClusterInfoMeta{
				Id: gptr.Of[uint64](234567),
			},
		}
		cid2Any, nErr2 := anypb.New(cid2)
		assert.NoError(t, nErr2)

		dplResp2 := &rolloutpb.Deployment{
			OverallStatus: &rolloutpb.OverallStatus{
				Status: rolloutpb.DeploymentStatus_SUCCESS,
			},
			Resources: []*rolloutpb.Resource{
				{
					Metadata: &rolloutpb.Metadata{
						Type: rolloutpb.ResourceType_TCE_SERVICE,
					},
					Spec: &rolloutpb.Spec{
						Template: anyServ,
					},
					Status: map[string]*rolloutpb.RegionalResourceStatus{
						"VGeo-RoW": {
							ControlPlane: "VGeo-RoW",
							Tickets: []*rolloutpb.ResourceOpTicket{
								{
									Link: "https://cloud-i18n.bytedance.net/tce/deployment_new/23030538",
								},
							},
						},
						"VGeo-US": {
							ControlPlane: "VGeo-US",
							Tickets: []*rolloutpb.ResourceOpTicket{
								{
									Link: "https://cloud-ttp-us.bytedance.net/tce/deployment_new/400064753",
								},
							},
						},
					},
				},
				{
					Metadata: &rolloutpb.Metadata{
						Type: rolloutpb.ResourceType_TCE_CLUSTER,
					},
					Spec: &rolloutpb.Spec{
						Template: anyCls,
					},
					Status: map[string]*rolloutpb.RegionalResourceStatus{
						"VGeo-RoW|Singapore-Central|sg1": {
							ControlPlane: "VGeo-RoW",
							Tickets: []*rolloutpb.ResourceOpTicket{
								{
									Link: "https://cloud-i18n.bytedance.net/tce/deployment_new/508222242",
								},
							},
							PrimaryIdentifier: cid1Any,
						},
						"VGeo-US|US-TTP|useast5": {
							ControlPlane: "VGeo-US",
							Tickets: []*rolloutpb.ResourceOpTicket{
								{
									Link: "https://cloud-ttp-us.bytedance.net/tce/deployment_new/400064753",
								},
							},
							PrimaryIdentifier: cid2Any,
						},
					},
				},
			},
		}

		dplResp2Yml, cpbErr2 := parser.ConvertPBToYaml(dplResp2)
		assert.NoError(t, cpbErr2)

		mockey.Mock((*entry.DeployEntryServiceClient).GetStandardDeployment).
			Return(mockey.Sequence(&deploymentpb.GetDeploymentStrResponse{
				Deployment: dplResp1Yml,
			}, nil, nil).Then(&deploymentpb.GetDeploymentStrResponse{
				Deployment: dplResp2Yml,
			}, nil, nil)).Build()

		// mock codebase api
		mockey.Mock(gitlab.IsMRMerged).Return(mockey.Sequence(false, nil).Times(2).Then(true, nil)).Build()

		// create task
		globalResources := []*dao.GlobalResource{
			{
				ResourceType: app.ResourceType_RESOURCE_TYPE_TCE.String(),
				Name:         "p.s.m1",
				RegionalResources: []*dao.RegionalResource{
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_I18N.String(),
						Name:         "p.s.m1",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_EU_TTP.String(),
						Name:         "p.s.m1",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_CN.String(),
						Name:         "p.s.m1",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_US_TTP.String(),
						Name:         "p.s.m1",
					},
				},
			},
			{
				ResourceType: app.ResourceType_RESOURCE_TYPE_TCE.String(),
				Name:         "p.s.m2",
				RegionalResources: []*dao.RegionalResource{
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_I18N.String(),
						Name:         "p.s.m2",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_EU_TTP.String(),
						Name:         "p.s.m2",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_CN.String(),
						Name:         "p.s.m2",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_US_TTP.String(),
						Name:         "p.s.m2",
					},
				},
			},
			{
				ResourceType: app.ResourceType_RESOURCE_TYPE_TCE.String(),
				Name:         "p.s.m3",
				RegionalResources: []*dao.RegionalResource{
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_I18N.String(),
						Name:         "p.s.m3",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_EU_TTP.String(),
						Name:         "p.s.m3",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_CN.String(),
						Name:         "p.s.m3",
					},
					{
						ControlPlane: resources.ControlPlane_CONTROL_PLANE_US_TTP.String(),
						Name:         "p.s.m3",
					},
				},
			},
		}
		task := &dao.NewPlacementTask{
			TaskType:          dao.TASK_NEW_PLACEMENT,
			Creator:           "yifei.feng",
			LogID:             "some logid",
			Auto:              false,
			Status:            app.TaskStatus_TASK_STATUS_UNSPECIFIED.String(),
			StepStatus:        app.StepStatus_STEP_STATUS_UNSPECIFIED.String(),
			StagingResource:   globalResources,
			SelectedResources: globalResources,
			AdditionalInfo: dao.NewPlacementInfo{
				Source: "ie",
				Target: "no1a",
				Step:   app.NewPlacementStep_NEW_PLACEMENT_STEP_RESOURCE_IMPORTING,
				ResourceNewPlacementHolder: &dao.ResourceNewPlacementHolder{
					BatchEditStatus: dao.INIT,
				},
				ResourceImportingHolder: &dao.ResourceImportingHolder{
					ImportingStatus:      dao.INIT,
					CodeGeneratingStatus: dao.INIT,
				},
				Namespace: "default",
				NewPlacementOption: &dao.NewPlacementOption{
					TcePhysicalCluster: "federation",
					TceLogicalCluster:  "default",
					TceZone:            "EU-TTP",
				},
				IaCRepoNamespace: "devinfra",
				IaCRepo:          "rollout_sots",
				IaCSubPath:       "",
				IaCDefaultBranch: "master",
			},
			UserCtx: dao.UserCtx{
				Username: util.GetUsernameFromCtx(ctx),
				JwtToken: util.GetUserJwtFromCtx(ctx),
			},
		}

		tid, ctErr := dao.CreateTaskMock(ctx, task)
		assert.NoError(t, ctErr)
		fmt.Println("task id", tid)

		// mimic reconcile loop and user execute next step
		npCtrler := GetNewPlacementController(tid, false)
		totalSteps := 0
		for {
			beforeTask, _, gErr1 := dao.GetTaskByTaskIDMock[dao.NewPlacementInfo](ctx, tid)
			require.NoError(t, gErr1)
			userClickNext := false
			if beforeTask.StepStatus == app.StepStatus_STEP_STATUS_FINISHED.String() {
				if beforeTask.AdditionalInfo.Step == app.NewPlacementStep_NEW_PLACEMENT_STEP_RESOURCE_IMPORTING ||
					beforeTask.AdditionalInfo.Step == app.NewPlacementStep_NEW_PLACEMENT_STEP_SOT_SOURCE_MR_APPROVAL ||
					beforeTask.AdditionalInfo.Step == app.NewPlacementStep_NEW_PLACEMENT_STEP_SOT_TARGET_MR_APPROVAL {
					userClickNext = true
				}
			}

			_, eErr := npCtrler.Execute(ctx, tid, userClickNext)
			require.NoError(t, eErr)

			afterTask, _, gErr2 := dao.GetTaskByTaskIDMock[dao.NewPlacementInfo](ctx, tid)
			require.NoError(t, gErr2)
			if afterTask.Status == app.TaskStatus_TASK_STATUS_FINISHED.String() {
				break
			}
			totalSteps++
			if totalSteps == 100 {
				t.Errorf("dead loop")
			}
		}

		// verify
		task, exist, gErr := dao.GetTaskByTaskIDMock[dao.NewPlacementInfo](ctx, tid)
		assert.NoError(t, gErr)
		assert.True(t, exist)
		assert.NotNil(t, task)
		assert.Equal(t, task.Status, app.TaskStatus_TASK_STATUS_FINISHED.String())

		tickets, _, gtErr := statusutil.GetTicketsFromDeployment(dplResp2, getChangedPlacementsMap(dplResp2, task.AdditionalInfo.Target, task.AdditionalInfo.PlacementType), true)
		assert.NoError(t, gtErr)

		sort.Slice(tickets, func(i int, j int) bool {
			return tickets[i].TicketLink < tickets[j].TicketLink
		})

		sort.Slice(task.AdditionalInfo.DeploymentTickets, func(i int, j int) bool {
			return task.AdditionalInfo.DeploymentTickets[i].TicketLink < task.AdditionalInfo.DeploymentTickets[j].TicketLink
		})

		assert.Equal(t, tickets, task.AdditionalInfo.DeploymentTickets)
		assert.Equal(t, app.TaskStatus_TASK_STATUS_FINISHED.String(), task.Status)
		assert.Equal(t, app.StepStatus_STEP_STATUS_FINISHED.String(), task.StepStatus)
		assert.Equal(t, app.NewPlacementStep_NEW_PLACEMENT_STEP_TICKETS, task.AdditionalInfo.Step)
		for _, sr := range task.SelectedResources {
			assert.Equal(t, app.ResourceType_RESOURCE_TYPE_TCE.String(), sr.ResourceType)
			assert.Empty(t, sr.ErrorMsg)
			assert.Equal(t, app.ResourceStatus_RESOURCE_IMPORTING_STATUS_FINISHED, sr.ResourceStatus)
		}
	})

}

func Test_getMergeIDByUrl(t *testing.T) {
	type args struct {
		mergeUrl string
	}
	tests := []struct {
		name string
		args args
		want int64
	}{
		{
			name: "",
			args: args{
				mergeUrl: "https://code.byted.org/devinfra/rollout_sots/merge_requests/294",
			},
			want: 294,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _ := getMergeIDByUrl(tt.args.mergeUrl)
			assert.Equalf(t, tt.want, got, "getMergeIDByUrl(%v)", tt.args.mergeUrl)
		})
	}
}

func TestGetChangedPlacementsMap(t *testing.T) {
	tceSvcYaml := "metadata:\n  logicalId: grn:tce:demo.kitex.xianhao:svc\n  type: TCE_SERVICE\nspec:\n  regionalResources:\n    VGeo-CN:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEServicePrimaryIdentifier\n        Meta:\n          Id: \"5178613\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEService\n        Auth:\n          IamInfo:\n            Operators:\n            - Email: <EMAIL>\n              Username: huangmiao.73\n            - Email: <EMAIL>\n              Username: yuanmengfan\n            Owners:\n            - Email: <EMAIL>\n              Username: huangmiao.73\n            - Email: <EMAIL>\n              Username: kun.xia\n            - Email: <EMAIL>\n              Username: xianhao.jin\n        Build:\n          ScmRepoInfo:\n          - MainRepo: false\n            Name: toutiao/load\n            NeedNotify: false\n            Path: toutiao/load\n            RemoteId: 667\n          - MainRepo: false\n            Name: toutiao/runtime\n            NeedNotify: false\n            Path: toutiao/runtime\n            RemoteId: 631\n          - MainRepo: true\n            Name: toutiao/demo/xianhao_kitex\n            NeedNotify: false\n            Path: xianhao\n            RemoteId: 314438\n        Meta:\n          Name: demo_xianhao_kitex\n        Runtime:\n          AppPath: xianhao/bootstrap.sh\n          NewPorts:\n          - Intent: primary\n            IsPrimary: true\n            Port: 8888\n            Type: tcp\n          - Intent: debug\n            IsPrimary: false\n            Port: 18888\n            Type: tcp\n    VGeo-EU:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEServicePrimaryIdentifier\n        Meta:\n          Id: \"1611598\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEService\n        Auth:\n          ApprovalControls:\n          - reset\n          IamInfo:\n            Operators:\n            - Email: <EMAIL>\n              Username: yongning.hu\n            Owners:\n            - Email: <EMAIL>\n              Username: chenxuancai\n            - Email: <EMAIL>\n              Username: kun.xia\n            - Email: <EMAIL>\n              Username: xianhao.jin\n            - Email: <EMAIL>\n              Username: yifei.feng\n        Build:\n          ScmRepoInfo:\n          - MainRepo: false\n            Name: toutiao/load\n            NeedNotify: false\n            Path: /opt/tiger/toutiao/load\n            RemoteId: 667\n          - MainRepo: false\n            Name: toutiao/runtime\n            NeedNotify: false\n            Path: /opt/tiger/toutiao/runtime\n            RemoteId: 631\n          - MainRepo: true\n            Name: toutiao/demo/xianhao_kitex\n            NeedNotify: false\n            Path: /opt/tiger/xianhao\n            RemoteId: 314438\n        Meta:\n          Name: default\n        Runtime:\n          AppPath: /xianhao/bootstrap.sh\n          NewPorts:\n          - Intent: primary\n            IsPrimary: true\n            Port: 6789\n            Type: tcp\n          - Intent: debug\n            IsPrimary: false\n            Port: 6790\n            Type: tcp\n    VGeo-RoW:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEServicePrimaryIdentifier\n        Meta:\n          Id: \"1611598\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEService\n        Auth:\n          ApprovalControls:\n          - reset\n          IamInfo:\n            Operators:\n            - Email: <EMAIL>\n              Username: jie.huang\n            Owners:\n            - Email: <EMAIL>\n              Username: chenxuancai\n            - Email: <EMAIL>\n              Username: xianhao.jin\n            - Email: <EMAIL>\n              Username: yifei.feng\n        Build:\n          ScmRepoInfo:\n          - MainRepo: false\n            Name: toutiao/load\n            NeedNotify: false\n            Path: /opt/tiger/toutiao/load\n            RemoteId: 667\n          - MainRepo: false\n            Name: toutiao/runtime\n            NeedNotify: false\n            Path: /opt/tiger/toutiao/runtime\n            RemoteId: 631\n          - MainRepo: true\n            Name: toutiao/demo/xianhao_kitex\n            NeedNotify: false\n            Path: /opt/tiger/xianhao\n            RemoteId: 314438\n        Meta:\n          Name: default\n        Runtime:\n          AppPath: xianhao/bootstrap.sh\n          NewPorts:\n          - Intent: primary\n            IsPrimary: true\n            Port: 6789\n            Type: tcp\n          - Intent: debug\n            IsPrimary: false\n            Port: 6790\n            Type: tcp\n    VGeo-US:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEServicePrimaryIdentifier\n        Meta:\n          Id: \"58100\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEService\n        Auth:\n          ApprovalControls:\n          - reset\n          IamInfo:\n            Operators:\n            - Email: <EMAIL>\n              Username: yongning.hu\n            Owners:\n            - Email: <EMAIL>\n              Username: chenxuancai\n            - Email: <EMAIL>\n              Username: xianhao.jin\n            - Email: <EMAIL>\n              Username: yifei.feng\n        Build:\n          ScmRepoInfo:\n          - MainRepo: false\n            Name: toutiao/load\n            NeedNotify: false\n            Path: /opt/tiger/toutiao/load\n            RemoteId: 667\n          - MainRepo: false\n            Name: toutiao/runtime\n            NeedNotify: false\n            Path: /opt/tiger/toutiao/runtime\n            RemoteId: 631\n          - MainRepo: true\n            Name: toutiao/demo/xianhao_kitex\n            NeedNotify: false\n            Path: /opt/tiger/xianhao\n            RemoteId: 314438\n        Meta:\n          Name: default\n        Runtime:\n          AppPath: xianhao/bootstrap.sh\n          NewPorts:\n          - Intent: primary\n            IsPrimary: true\n            Port: 6789\n            Type: tcp\n          - Intent: debug\n            IsPrimary: false\n            Port: 6790\n            Type: tcp\n  template:\n    '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEService\n    Auth:\n      GalaxyInfo:\n        ParentId: 4776327\n    Build:\n      BaseImage: toutiao.debian:latest\n      EnableForbiddenPyc: false\n      FrameworkSupport: none\n      Language: go\n      TechStack: kitex\n    ExtraFeatures:\n      EnableSidecar: true\n    Meta:\n      Env: prod\n      Psm: demo.kitex.xianhao\n    Runtime:\n      EnableSameCluster: false\n      EnforceHa: 0\n      FailureThreshold: 0\n      HealthCheckPath: \"\"\n      HostType: \"\"\n      IsRuntime: false\n      IsServiceHostuniq: false\n      PeriodSeconds: 0\n      ServiceHostuniqNum: 0\n      SplitsParam: \"\"\n      UmountSsConf: false"
	tceClusterYaml := "metadata:\n  logicalId: grn:tce:demo.kitex.xianhao:cls:default\n  type: TCE_CLUSTER\nspec:\n  regionalResources:\n    VGeo-CN|China-North|lf|default#Federation#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"204070991\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[\"{\\\"containerName\\\":\\\"$mainContainer\\\",\\\"request\\\":\\\"0\\\",\\\"limit\\\":\\\"0\\\",\\\"idc\\\":\\\"LF\\\"}\"]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"5178613\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Federation\n          ResourceType: mini\n          Zone: China-North\n        Runtime:\n          ImageVersion: *********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 0\n              Idc: LF\n            DcInfo:\n            - Count: 0\n              Idc: LF\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.588\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.778\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: true\n            SidecarHttpEgressEnable: true\n            SidecarIngressEnable: true\n    VGeo-EU|EU-TTP|ie|default#Federation#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"300460592\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Federation\n          ResourceType: mini\n          Zone: EU-TTP\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 0\n              Idc: IE\n            DcInfo:\n            - Count: 1\n              Idc: IE\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.588\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.778\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: true\n            SidecarHttpEgressEnable: true\n            SidecarIngressEnable: true\n    VGeo-EU|EU-TTP2|no1a|default#Federation#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"300005340\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"false\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 3\n          PhysicalCluster: Federation\n          ResourceType: custom\n          Zone: EU-TTP2\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 1\n              Idc: NO1A\n            DcInfo:\n            - Count: 1\n              Idc: NO1A\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.519\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.645\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: false\n    VGeo-RoW|Singapore-Central|my|default#Federation#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"501622072\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Federation\n          ResourceType: mini\n          Zone: Singapore-Central\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 1\n              Idc: MY\n            DcInfo:\n            - Count: 1\n              Idc: MY\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.576\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.766\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 0\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: false\n    VGeo-RoW|Singapore-Central|sg1|default#Federation#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"4363735\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"false\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 1\n          PhysicalCluster: Federation\n          ResourceType: mini\n          Zone: Aliyun_SG\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 1\n              Idc: SG1\n            DcInfo:\n            - Count: 1\n              Idc: SG1\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.576\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.766\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 0\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: false\n    VGeo-RoW|US-East|maliva|default#Florida#normal-bytenas:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"2706443\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: normal-bytenas\n          Mem: 2\n          PhysicalCluster: Florida\n          ResourceType: custom\n          Zone: Aliyun_VA\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: autohost\n          Replicas:\n            CanaryDcInfo:\n            - Count: 0\n              Idc: Aliyun_VA\n            DcInfo:\n            - Count: 1\n              Idc: Aliyun_VA\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.576\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.766\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: true\n    VGeo-RoW|US-East|useast3|default#TopbuzzAli-va#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"3693637\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"false\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: TopbuzzAli-va\n          ResourceType: mini\n          Zone: Aliyun_VA\n        Runtime:\n          ImageVersion: *********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 1\n              Idc: USEAST3\n            DcInfo:\n            - Count: 1\n              Idc: USEAST3\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.576\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.766\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: true\n    VGeo-RoW|US-SouthWest|ussw1a|default#Normal01#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"500429536\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"true\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Normal01\n          ResourceType: mini\n          Zone: US-SouthWest\n        Runtime:\n          ImageVersion: *********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 0\n              Idc: USSW1A\n            - Count: 0\n              Idc: USSW1B\n            DcInfo:\n            - Count: 1\n              Idc: USSW1A\n            - Count: 1\n              Idc: USSW1B\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.576\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.766\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 0\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: false\n    VGeo-RoW|US-West|uswest2|default#Normal#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"3940664\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"true\"\n            EnableZTIToken: \"false\"\n            IPv6OnlyCompatible: \"true\"\n        Meta:\n          Service: \"1611598\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Normal\n          ResourceType: mini\n          Zone: US-West\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 0\n              Idc: USWEST2\n            DcInfo:\n            - Count: 1\n              Idc: USWEST2\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: 1.0.2.576\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: 1.0.1.766\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: false\n            SidecarHttpEgressEnable: false\n            SidecarIngressEnable: false\n    VGeo-US|US-TTP|useast5|default#Normal02#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"188160\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"false\"\n            EnableZTIToken: \"true\"\n            IPv6OnlyCompatible: \"false\"\n        Meta:\n          Service: \"58100\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Normal02\n          ResourceType: mini\n          Zone: US-TTP\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 1\n              Idc: USEAST5\n            DcInfo:\n            - Count: 1\n              Idc: USEAST5\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: ********\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: ********\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: true\n            SidecarHttpEgressEnable: true\n            SidecarIngressEnable: true\n    VGeo-US|US-TTP2|useast8|default#Normal01#default:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCEClusterPrimaryIdentifier\n        Meta:\n          Id: \"179164\"\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n        ExtraFeatures:\n          FeatureGates:\n            BandWidthResource: '[]'\n            CanBePreempted: \"true\"\n            EnableIPv6: \"false\"\n            EnableZTIToken: \"true\"\n            IPv6OnlyCompatible: \"false\"\n        Meta:\n          Service: \"58100\"\n        Resource:\n          LogicalCluster: default\n          Mem: 2\n          PhysicalCluster: Normal01\n          ResourceType: mini\n          Zone: US-TTP2\n        Runtime:\n          ImageVersion: ********\n          NetworkMode: bridge\n          Replicas:\n            CanaryDcInfo:\n            - Count: 1\n              Idc: USEAST8\n            DcInfo:\n            - Count: 1\n              Idc: USEAST8\n          RepoInfo:\n          - Name: toutiao/demo/xianhao_kitex\n            ScmRepoId: \"314438\"\n            Version: ********\n          - Name: toutiao/load\n            ScmRepoId: \"667\"\n            Version: ********\n          - Name: toutiao/runtime\n            ScmRepoId: \"631\"\n            Version: ********\n        Sidecar:\n          ServiceMesh:\n            EnableSidecarPercent: 100\n            SidecarEgressEnable: true\n            SidecarHttpEgressEnable: true\n            SidecarIngressEnable: true\n  template:\n    '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCECluster\n    ExtraFeatures:\n      FeatureGates:\n        AVX2SupportNeeded: \"false\"\n        AffinityScheduler: '[]'\n        AliyunDCleavedNeed: \"false\"\n        AntiCgroupv2: \"false\"\n        AutoEvictionEnable: \"false\"\n        AutoMountLocalDisks: \"false\"\n        AvailableZoneRebalance: \"true\"\n        BytedanceIntranetServices: \"false\"\n        CarmaOverlay: \"false\"\n        CloudNativeLabel: '[]'\n        CombineDeploy: \"false\"\n        ContainerCMD: '[]'\n        ContainerPrivileged: \"false\"\n        ContainerResourceView: '\"\"'\n        ContainerRuntime: '\"Disabled\"'\n        ContainerSRIOV: \"false\"\n        ContainerShmSize: '\"\"'\n        ContainerUlimits: '\"\"'\n        CpuGenerationSelector: '\"\"'\n        CpuTypeSelector: '\"\"'\n        CustomPriorityClass: '\"\"'\n        DNSPolicy: '\"Default\"'\n        DaemonIncludeNotReadyNode: \"false\"\n        Daemonset: '\"\"'\n        DisableAutoPort: \"false\"\n        EnableLazyLoadImage: \"false\"\n        EnablePrivileged: \"false\"\n        EnableServerlessConsulRegister: \"false\"\n        EnableSmoothUpgrade: \"false\"\n        EnableSysMount: \"true\"\n        FPGA: '\"\"'\n        FederationMemberPolicy: '[]'\n        GodelSchedulerEnhancement: '[]'\n        HistoryConsulWeight: \"false\"\n        HostMeshPercentage: '\"\"'\n        HostNameSelector: '\"\"'\n        HugeTLB: '\"\"'\n        IPv6OnlyNetwork: \"false\"\n        IPv6OverlayNetwork: \"false\"\n        IgnoreReadyCheck: \"false\"\n        InplaceIfPossible: \"false\"\n        IsCriticalPod: \"false\"\n        IsUpdateByQuantity: \"false\"\n        KatalystMemoryEnhancement: '[]'\n        KatalystQosEnhancement: '\"\"'\n        KatalystQosLevel: '\"\"'\n        MemRank2: \"false\"\n        MemTypeSelector: '\"\"'\n        MemcgReclaimValue: '\"\"'\n        MemoryInterleavedNeed: \"false\"\n        MountJwtBundles: \"true\"\n        NetAreaSelector: '\"\"'\n        NetPodIpv4Selector: '\"\"'\n        NetPodIpv6Selector: '\"\"'\n        OOMScoreAdj: '\"\"'\n        P2PConf: '\"\"'\n        PIDIsolation: \"true\"\n        PodAnnotations: '[]'\n        PodDisruptionBudgetAcrossApp: '\"\"'\n        PodDisruptionBudgetExtension: '[]'\n        PodDisruptionBudgetMaxUnavailable: '\"\"'\n        PodType: '\"\"'\n        PreemptScope: '\"\"'\n        RDMA: \"false\"\n        SdConvergenceSecond: '\"\"'\n        ServiceAccountName: '\"\"'\n        ServiceAffinity: '[]'\n        ShareGPU: '\"\"'\n        SkipCgroupSubsystems: '\"\"'\n        SolarReservationEnable: \"false\"\n        SolarServiceInplaceUpgrade: \"false\"\n        SpecificationAffinity: '[]'\n        UseHybridResource: \"false\"\n        UseScheduledResource: \"false\"\n        UseVKResource: \"false\"\n        VauTopologySpread: \"false\"\n        VolumeMount: '[]'\n    Meta:\n      HostType: docker\n      Name: default\n      ServiceEnv: prod\n      ServicePriority: normal\n      ServicePsm: demo.kitex.xianhao\n    Resource:\n      Cpu: 1\n      Gpu: 0\n      Package: \"\"\n      Socket: 0\n    Runtime:\n      AmsTag: \"\"\n      CanaryWeight: 10\n      HostuniqNum: 0\n      ImageTag: tce\n      IsHostuniq: false\n      IsStateful: false\n      MaxFailureFraction: 0\n      ShardNum: 0\n      ShardParams: {}\n      Weight: 10\n    Sidecar:\n      ServiceMesh:\n        SidecarHttpIngressEnable: false\n        SidecarMongoEgressEnable: false\n        SidecarMysqlEgressEnable: false\n        SidecarRedisEgressEnable: false\n        SidecarTosEgressEnable: false\n    Upgrade:\n      CanarySurgePercent: 100\n      IsStandaloneRelease: false\n      MinReadySecond: 10\n      StandaloneReason: \"\"\n      SurgePercent: 25\n      TerminationSecond: 30\n      UpdateAction: restart\n      UpdateMode: delete_first\n      UpdateStrategy: RollingUpdate"
	tccConfigYaml := "metadata:\n  logicalId: grn:tcc:test:cfg\n  type: TCC_CONFIG\nspec:\n  regionalResources:\n    VGeo-CN|CN:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfigPrimaryIdentifier\n        Id: \"2825665958714976\"\n        ServiceNameSpace: demo.rollout-appcenter.jxh-tcc\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfig\n        Region: CN\n        Value: \"123456\"\n    VGeo-EU|US-EastRed:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfigPrimaryIdentifier\n        Id: \"2544190988345856\"\n        ServiceNameSpace: demo.rollout-appcenter.jxh-tcc\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfig\n        Region: US-EastRed\n        Value: \"123456\"\n    VGeo-RoW|Singapore-Central:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfigPrimaryIdentifier\n        Id: \"1418291095671728\"\n        ServiceNameSpace: demo.rollout-appcenter.jxh-tcc\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfig\n        Region: Singapore-Central\n        Value: \"123456\"\n    VGeo-US|US-TTP:\n      primaryIdentifier:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfigPrimaryIdentifier\n        Id: \"1981241059979376\"\n        ServiceNameSpace: demo.rollout-appcenter.jxh-tcc\n      propertyOverrides:\n        '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfig\n        Region: US-TTP\n        Value: \"123456\"\n  template:\n    '@type': type.googleapis.com/byted.devinfra.rollout.resources.TCCConfig\n    BatchRelease: true\n    ConfigurationType: static\n    Description: test\n    Directory: /default\n    Env: prod\n    Name: test\n    ServiceNameSpace: demo.rollout-appcenter.jxh-tcc\n    ValueType: string"
	tceSvc, err := parser.ConvertYamlToResourceV2PB(tceSvcYaml)
	assert.NoError(t, err)
	tceCluster, err := parser.ConvertYamlToResourceV2PB(tceClusterYaml)
	assert.NoError(t, err)
	tccConfig, err := parser.ConvertYamlToResourceV2PB(tccConfigYaml)
	assert.NoError(t, err)
	deployment := &rolloutpb.Deployment{
		Resources: []*rolloutpb.Resource{
			tceSvc,
			tceCluster,
			tccConfig,
		},
	}
	changedMap := getChangedPlacementsMap(deployment, "sg1", app.PlacementType_PLACEMENT_VDC.String())
	assert.Len(t, changedMap, 1)
	changedMap = getChangedPlacementsMap(deployment, "Singapore-Central", app.PlacementType_PLACEMENT_VREGION.String())
	assert.Len(t, changedMap, 2)
	changedMap = getChangedPlacementsMap(deployment, "VGeo-RoW", app.PlacementType_PLACEMENT_VGEO.String())
	assert.Len(t, changedMap, 3)
}
