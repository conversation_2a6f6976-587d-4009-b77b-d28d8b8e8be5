package global

import (
	"context"
	"strings"
	"sync"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/dao"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/canal/provider/tce"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/constants"
	"code.byted.org/devinfra/hagrid/app/apprpc/biz/util"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app/resources"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/crossborderpb"
	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/iesarch/paas_sdk/tce_config"
	tceopensdk "code.byted.org/tce/tce_golang_sdk"
)

func SearchTCEClusters(ctx context.Context, req *app.SearchTCEClustersReq) (resp *app.SearchTCEClustersResponse, err error) {
	result := &app.SearchTCEClustersResponse{
		Clusters: make([]*resources.TCECluster, 0),
	}
	var regionsFilter []*dao.RegionalTCEService
	if req.GlobalComponentId != "" {
		globalComponent, exists, err := dao.GetGlobalComponentDao().GetByID(ctx, req.GlobalComponentId)
		if err != nil {
			return nil, erri.Error(err)
		}
		if !exists {
			return nil, erri.Errorf("global component: %s not exist", req.GlobalComponentId)
		}
		if globalComponent.Resources.TCEServices == nil || len(globalComponent.Resources.TCEServices) == 0 {
			return nil, erri.Errorf("global component: %s has no services", req.GlobalComponentId)
		}
		regionsFilter = gslice.Filter(globalComponent.Resources.TCEServices[0].RegionalInfos, func(service *dao.RegionalTCEService) bool {
			return service.Psm == req.Psm
		})
	}

	// control plane filter
	controlPlanes := util.FilterControlPlanesByResourceControlPlanes(ctx, []resources.ControlPlane{
		resources.ControlPlane_CONTROL_PLANE_CN,
		resources.ControlPlane_CONTROL_PLANE_I18N,
		resources.ControlPlane_CONTROL_PLANE_US_TTP,
		resources.ControlPlane_CONTROL_PLANE_EU_TTP,
	})
	workerList := make([]func(*util.BaseCtx) error, 0)
	for _, controlPlane := range controlPlanes {
		switch controlPlane {
		case resources.ControlPlane_CONTROL_PLANE_CN:
			workerList = append(workerList, func(r *util.BaseCtx) error {
				_, exist, err := tce.GetServiceByPSM(ctx, req.Psm, tce_config.APP_ENV_PROD, tce_config.IDC_CN, tce_config.ENV_PROD)
				if err != nil {
					return erri.Error(err)
				}
				if !exist {
					return nil
				}
				if regionsFilter != nil {
					if !filterByRegionInfos(resources.ControlPlane_CONTROL_PLANE_CN, regionsFilter) {
						return nil
					}
				}
				cnList, err := tce.GetAllClusterListByPSM(ctx, req.Psm, tce_config.APP_ENV_PROD, tce_config.IDC_CN, tce_config.ENV_PROD)
				if err != nil {
					return erri.Error(err)
				}
				r.Locker.Lock()
				defer r.Locker.Unlock()
				for _, c := range cnList {
					resCluster, err := tceClusterToResourceCluster(ctx, req.Psm, resources.ControlPlane_CONTROL_PLANE_CN, c)
					if err != nil {
						return erri.Error(err)
					}
					result.Clusters = append(result.Clusters, resCluster)
				}
				return nil
			})
		case resources.ControlPlane_CONTROL_PLANE_I18N:
			workerList = append(workerList, func(r *util.BaseCtx) error {
				_, exist, err := tce.GetServiceByPSM(ctx, req.Psm, tce_config.APP_ENV_PROD, tce_config.IDC_I18N, tce_config.ENV_PROD)
				if !exist {
					return nil
				}
				if len(regionsFilter) != 0 {
					if !filterByRegionInfos(resources.ControlPlane_CONTROL_PLANE_I18N, regionsFilter) {
						return nil
					}
				}
				i18nList, err := tce.GetAllClusterListByPSM(ctx, req.Psm, tce_config.APP_ENV_PROD, tce_config.IDC_I18N, tce_config.ENV_PROD)
				if err != nil {
					return erri.Error(err)
				}
				r.Locker.Lock()
				defer r.Locker.Unlock()
				for _, c := range i18nList {
					resCluster, err := tceClusterToResourceCluster(ctx, req.Psm, resources.ControlPlane_CONTROL_PLANE_I18N, c)
					if err != nil {
						return erri.Error(err)
					}
					result.Clusters = append(result.Clusters, resCluster)
				}
				return nil
			})
		case resources.ControlPlane_CONTROL_PLANE_US_TTP:
			workerList = append(workerList, func(r *util.BaseCtx) error {
				sdk := crossborder.NewCrossborderSDK()
				res, err := sdk.GetTceInfo(ctx, &crossborderpb.ResourceGetTceInfoReq{
					Psm: req.Psm,
				})
				if err != nil {
					logs.CtxError(ctx, "get tce info error: %+v", erri.Error(err))
				}
				if res == nil || res.Data == nil || len(res.Data.ServiceInfoMap) == 0 {
					return nil
				}
				if len(regionsFilter) != 0 {
					if !filterByRegionInfos(resources.ControlPlane_CONTROL_PLANE_US_TTP, regionsFilter) {
						return nil
					}
				}
				ttpList, err := sdk.GetClusterOption(ctx, &crossborderpb.ABDetailGetClusterOptionsReq{
					Psm:    req.Psm,
					Config: "tce-tx",
					Env:    tce_config.APP_ENV_PROD,
				})
				if err != nil {
					return erri.Error(err)
				}
				r.Locker.Lock()
				defer r.Locker.Unlock()
				for _, c := range ttpList.Data {
					resCluster, err := ttpTCEClusterToResourceCluster(ctx, req.Psm, resources.ControlPlane_CONTROL_PLANE_US_TTP, c)
					if err != nil {
						return erri.Error(err)
					}
					result.Clusters = append(result.Clusters, resCluster)
				}
				return nil
			})
		case resources.ControlPlane_CONTROL_PLANE_EU_TTP:
			workerList = append(workerList, func(r *util.BaseCtx) error {
				sdk := crossborder.NewCrossborderSDK()
				if len(regionsFilter) != 0 {
					if !filterByRegionInfos(resources.ControlPlane_CONTROL_PLANE_EU_TTP, regionsFilter) {
						return nil
					}
				}
				ttpList, err := sdk.GetClusterOption(ctx, &crossborderpb.ABDetailGetClusterOptionsReq{
					Psm:    req.Psm,
					Config: "tce-eu",
					Env:    tce_config.APP_ENV_PROD,
				})
				if err != nil {
					return erri.Error(err)
				}
				r.Locker.Lock()
				defer r.Locker.Unlock()
				for _, c := range ttpList.Data {
					resCluster, err := ttpTCEClusterToResourceCluster(ctx, req.Psm, resources.ControlPlane_CONTROL_PLANE_EU_TTP, c)
					if err != nil {
						return erri.Error(err)
					}
					result.Clusters = append(result.Clusters, resCluster)
				}
				return nil
			})
		}
	}
	locker := sync.Mutex{}
	err = util.ConcurrentBatchProcess(ctx, &util.BaseCtx{Locker: &locker}, workerList...)
	if err != nil {
		return nil, erri.Error(err)
	}

	return result, nil
}

func filterByRegionInfos(controlPlane resources.ControlPlane, regionFilters []*dao.RegionalTCEService) bool {
	matched := gslice.Filter(regionFilters, func(service *dao.RegionalTCEService) bool {
		return service.ControlPlane == controlPlane
	})
	return len(matched) != 0
}

func tceClusterToResourceCluster(ctx context.Context, psm string, controlPlane resources.ControlPlane,
	c *tceopensdk.ClusterInfo) (*resources.TCECluster, error) {
	vregion, ok := constants.TCEToZoneMap[strings.ToLower(c.Resource.Zone)]
	if !ok {
		vregion = c.Resource.Zone
		logs.CtxError(ctx, "zone:%s not found", c.Resource.Zone)
	}
	r := &resources.TCECluster{
		Vgeo:    string(constants.VGeoRoW),
		Vregion: vregion,
		Vdc:     "",
		Replicas: &resources.ClusterInfoRuntimeReplicas{
			DcInfos:       make([]*resources.ClusterDcInfo, 0),
			CanaryDcInfos: make([]*resources.ClusterDcInfo, 0),
		},
		ClusterId:       int32(c.Meta.Id),
		Name:            c.Meta.Name,
		Status:          c.Meta.Status,
		Cpu:             float32(*c.Resource.Cpu),
		Mem:             float32(*c.Resource.Mem),
		Gpu:             float32(*c.Resource.Gpu),
		Zone:            c.Resource.Zone,
		PhysicalCluster: c.Resource.PhysicalCluster,
		LogicalCluster:  c.Resource.LogicalCluster,
		ResourceType:    c.Resource.ResourceType,
		InstanceCount:   0,
		Service:         uint64(c.Meta.Service),
		ServicePsm:      psm,
		ServiceEnv:      tce_config.ENV_PROD,
		ControlPlane:    controlPlane,
	}
	if c.Runtime != nil && c.Runtime.Replicas != nil {
		for _, replica := range *c.Runtime.Replicas.CanaryDcInfo {
			r.InstanceCount += int64(replica.Count)
			r.Replicas.CanaryDcInfos = append(r.Replicas.CanaryDcInfos, &resources.ClusterDcInfo{
				Count: int64(replica.Count),
				Idc:   replica.Idc,
			})
		}
		for _, replica := range *c.Runtime.Replicas.DcInfo {
			r.InstanceCount += int64(replica.Count)
			r.Replicas.DcInfos = append(r.Replicas.DcInfos, &resources.ClusterDcInfo{
				Count: int64(replica.Count),
				Idc:   replica.Idc,
			})
		}
	}
	return r, nil
}

func ttpTCEClusterToResourceCluster(ctx context.Context, psm string, controlPlane resources.ControlPlane,
	c *crossborderpb.Cluster) (*resources.TCECluster, error) {
	if c == nil {
		return nil, erri.Errorf("invalid cluster nil")
	}
	vregion, ok := constants.TCEToZoneMap[strings.ToLower(c.Zone)]
	if !ok {
		logs.CtxError(ctx, "zone:%s not found", c.Zone)
		vregion = c.Zone
	}
	r := &resources.TCECluster{
		Vgeo:    string(constants.ControlPlaneToVGeo[controlPlane]),
		Vregion: vregion,
		Vdc:     "",
		Replicas: &resources.ClusterInfoRuntimeReplicas{
			DcInfos:       make([]*resources.ClusterDcInfo, 0),
			CanaryDcInfos: make([]*resources.ClusterDcInfo, 0),
		},
		ClusterId: c.ClusterId,
		Name:      c.Name,
		Status:    c.Status,
		Cpu:       c.Cpu,
		Mem:       c.Mem,
		// TODO: GPU
		Gpu:             0,
		Zone:            c.Zone,
		PhysicalCluster: c.PhysicalCluster,
		LogicalCluster:  c.LogicalCluster,
		// TODO: Resource type
		ResourceType:  "",
		InstanceCount: 0,
		Service:       uint64(c.ServiceId),
		ServicePsm:    psm,
		ServiceEnv:    tce_config.ENV_PROD,
		ControlPlane:  controlPlane,
	}
	// TODO: DC info
	if len(c.DcInfos) != 0 {
		for _, replica := range c.DcInfos {
			r.InstanceCount += int64(replica.Count)
			r.Replicas.DcInfos = append(r.Replicas.DcInfos, &resources.ClusterDcInfo{
				Count: int64(replica.Count),
				Idc:   replica.Idc,
			})
		}
	}
	return r, nil
}

func GetTCEClustersByPSM(ctx context.Context, tceServices []*dao.TCEServiceResource) (map[string]map[int32]*resources.TCECluster, error) {
	allClusters := make(map[string]map[int32]*resources.TCECluster)
	locker := &sync.Mutex{}
	workers := make([]func(baseCtx *util.BaseCtx) error, 0)
	for _, tceResource := range tceServices {
		for _, regionalInfo := range tceResource.RegionalInfos {
			workers = append(workers, func(psm string) func(*util.BaseCtx) error {
				return func(baseCtx *util.BaseCtx) error {
					clustersResp, err := SearchTCEClusters(ctx, &app.SearchTCEClustersReq{
						Psm: psm,
					})
					if err != nil {
						return erri.Error(err)
					}
					baseCtx.Locker.Lock()
					defer baseCtx.Locker.Unlock()
					for i, cluster := range clustersResp.Clusters {
						if allClusters[cluster.Name] == nil {
							allClusters[cluster.Name] = make(map[int32]*resources.TCECluster)
						}
						allClusters[cluster.Name][cluster.ClusterId] = clustersResp.Clusters[i]
					}
					return nil
				}
			}(regionalInfo.Psm))
		}
	}
	err := util.ConcurrentBatchProcess(ctx, &util.BaseCtx{Locker: locker}, workers...)
	if err != nil {
		return nil, erri.Error(err)
	}
	return allClusters, nil
}
