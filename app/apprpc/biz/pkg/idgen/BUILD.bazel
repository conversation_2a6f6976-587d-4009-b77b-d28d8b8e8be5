load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "idgen",
    srcs = [
        "config.go",
        "init.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/apprpc/biz/pkg/idgen",
    visibility = ["//visibility:public"],
    deps = [
        "@org_byted_code_gopkg_idgenerator_v2//:idgenerator",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "idgen_test",
    srcs = ["init_test.go"],
    embed = [":idgen"],
    deps = [
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_smartystreets_goconvey//convey",
    ],
)
