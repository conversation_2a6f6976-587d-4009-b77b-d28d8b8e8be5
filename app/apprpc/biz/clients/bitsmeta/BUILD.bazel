load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "bitsmeta",
    srcs = ["client.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/bitsmeta",
    visibility = ["//visibility:public"],
    deps = [
        "@org_byted_code_overpass_bytedance_bits_meta//kitex_gen/bytedance/bits/meta",
        "@org_byted_code_overpass_bytedance_bits_meta//kitex_gen/bytedance/bits/meta/metaservice",
    ],
)

go_test(
    name = "bitsmeta_test",
    srcs = ["client_test.go"],
    embed = [":bitsmeta"],
)
