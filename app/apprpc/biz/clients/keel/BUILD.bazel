load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "keel",
    srcs = ["client.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/keel",
    visibility = ["//visibility:public"],
    deps = [
        "//app/apprpc/biz/clients/tccsdk",
        "//app/apprpc/biz/constants",
        "@org_byted_code_bytecloud_helm//:helm",
        "@org_byted_code_bytecloud_intelligen//hertz_gen/bytecloud/meta/keel/model",
    ],
)

go_test(
    name = "keel_test",
    srcs = ["client_test.go"],
    embed = [":keel"],
    deps = ["//app/apprpc/biz/constants"],
)
