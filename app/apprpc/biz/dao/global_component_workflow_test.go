package dao

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/apprpc/biz/clients/bytedoc"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
)

func TestGlobalComponentWorkflowDao_DeleteAllByComponentID(t *testing.T) {
	ctx := bytedoc.NewTestDBContext(t, context.Background())
	defer bytedoc.DropContextTestDB(t, ctx)
	bytedoc.PrepareDocsForTesting(t, ctx, GlobalComponentWorkflowCollection, []*GlobalComponentWorkflow{
		{WorkflowID: "workflow_1", GlobalComponentID: "global_component_1"},
		{WorkflowID: "workflow_2", GlobalComponentID: "global_component_1"},
		{WorkflowID: "workflow_3", GlobalComponentID: "global_component_2"},
	})

	w := &GlobalComponentWorkflowDao{}
	if err := w.DeleteAllByComponentID(ctx, "global_component_1"); err != nil {
		t.Fatal(err)
	}

	total, workflows, err := w.SearchByConditions(ctx, "global_component_2", "", nil, nil, 1, 100)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 1, int(total))
	assert.Equal(t, workflows[0].WorkflowID, "workflow_3")

	if err := w.DeleteAllByComponentID(ctx, "global_component_2"); err != nil {
		t.Fatal(err)
	}
	total, _, err = w.SearchByConditions(ctx, "global_component_2", "", nil, nil, 1, 100)
	if err != nil {
		t.Fatal(err)
	}
	assert.Equal(t, 0, int(total))
}

func TestGlobalComponentWorkflowDao_MustSearchByWorkflowID(t *testing.T) {
	ctx := bytedoc.NewTestDBContext(t, context.Background())
	defer bytedoc.DropContextTestDB(t, ctx)
	bytedoc.PrepareDocsForTesting(t, ctx, GlobalComponentWorkflowCollection, []*GlobalComponentWorkflow{
		{WorkflowID: "workflow_1", GlobalComponentID: "global_component_1"},
	})
	type args struct {
		ctx        context.Context
		workflowID string
	}
	tests := []struct {
		name    string
		args    args
		want    *GlobalComponentWorkflow
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "find",
			args: args{
				ctx:        ctx,
				workflowID: "workflow_1",
			},
			want: &GlobalComponentWorkflow{
				WorkflowID:        "workflow_1",
				GlobalComponentID: "global_component_1",
			},
			wantErr: assert.NoError,
		},
		{
			name: "not found",
			args: args{
				ctx:        ctx,
				workflowID: "workflow_2",
			},
			want:    nil,
			wantErr: assert.Error,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &GlobalComponentWorkflowDao{}
			got, err := w.MustSearchByWorkflowID(tt.args.ctx, tt.args.workflowID)
			if !tt.wantErr(t, err, fmt.Sprintf("MustSearchByWorkflowID(%v, %v)", tt.args.ctx, tt.args.workflowID)) {
				return
			}
			assert.Equalf(t, tt.want, got, "MustSearchByWorkflowID(%v, %v)", tt.args.ctx, tt.args.workflowID)
		})
	}
}

func Test_updateGlobalComponentWorkflowWithParams(t *testing.T) {
	ctx := bytedoc.NewTestDBContext(t, context.Background())
	defer bytedoc.DropContextTestDB(t, ctx)
	bytedoc.PrepareDocsForTesting(t, ctx, GlobalComponentWorkflowCollection, []*GlobalComponentWorkflow{
		{WorkflowID: "workflow_1", GlobalComponentID: "global_component_1", Version: 1},
		{WorkflowID: "workflow_2", GlobalComponentID: "global_component_1", Version: 1},
	})
	now := time.Now()

	t.Run("", func(t *testing.T) {
		params := &UpdateGlobalComponentWorkflowParams{
			WorkflowStatus: int32(app.WorkflowStatus_WORKFLOW_STATUS_FINISHED),
			Stage:          1,
			StageStatus:    "mock_stage_status",
			StageError:     "mock_stage_error",
			OtherFields: map[string]interface{}{
				"batch_id": "mock_batch_id",
			},
		}

		mockey.PatchConvey("right version id", t, func() {
			mockey.Mock(time.Now).Return(now).Build()
			err := updateGlobalComponentWorkflowWithParams(ctx, "workflow_1", 1, params)
			convey.So(err, convey.ShouldBeNil)
			workflow, err := GetGlobalComponentWorkflowDao().MustSearchByWorkflowID(ctx, "workflow_1")
			convey.So(err, convey.ShouldBeNil)
			convey.So(workflow, convey.ShouldEqual, &GlobalComponentWorkflow{
				WorkflowID:        "workflow_1",
				GlobalComponentID: "global_component_1",
				WorkflowStatus:    int32(app.WorkflowStatus_WORKFLOW_STATUS_FINISHED),
				Version:           2,
				UpdateAt:          now.Unix(),
				FinishedAt:        now.Unix(),
				LatestErrorTime:   now.Unix(),
				Stage:             1,
				StageStatus:       "mock_stage_status",
				ErrorMsg:          "mock_stage_error",
				BatchId:           "mock_batch_id",
			})
		})
		mockey.PatchConvey("wrong version id", t, func() {
			mockey.Mock(time.Now).Return(now).Build()
			err := updateGlobalComponentWorkflowWithParams(ctx, "workflow_1", 0, params)
			convey.So(err, convey.ShouldNotBeNil)
		})
	})
}

func Test_isWorkflowStatusFinished(t *testing.T) {
	type args struct {
		status int32
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_FINISHED),
			},
			want: true,
		},
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_CANCELLED),
			},
			want: true,
		},
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_ROLLED_BACK),
			},
			want: true,
		},
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_FAILED),
			},
			want: true,
		},
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_RUNNING),
			},
			want: false,
		},
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_INIT),
			},
			want: false,
		},
		{
			name: "",
			args: args{
				status: int32(app.WorkflowStatus_WORKFLOW_STATUS_ROLLING_BACK),
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equalf(t, tt.want, isWorkflowStatusFinished(tt.args.status), "isWorkflowStatusFinished(%v)", tt.args.status)
		})
	}
}
