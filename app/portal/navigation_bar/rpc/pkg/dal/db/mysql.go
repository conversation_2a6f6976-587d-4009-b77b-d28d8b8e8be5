package db

import (
	"code.byted.org/devinfra/hagrid/app/portal/navigation_bar/rpc/pkg/dal/db/repository"
	"code.byted.org/devinfra/hagrid/pkg/mysql"
	"code.byted.org/gorm/bytedgorm"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	Conn                              *gorm.DB
	DefaultNavigationBarRepository    *repository.DefaultNavigationBarRepository
	CustomizedNavigationBarRepository *repository.CustomizedNavigationBarRepository
)

func Init(config *Config) {
	if config.PSM != "" {
		opts := []gorm.Option{bytedgorm.WithDefaults(), bytedgorm.Logger{LogLevel: logger.Info}}
		Conn = mysql.MustConnect(mysql.NewPSMEndpoint(config.PSM, config.DB), opts...)
	} else {
		Conn = mysql.MustConnect(mysql.NewIPEndpoint(config.Username, config.Password, config.Host, uint16(config.Port), config.DB))
	}
	Conn.Logger.LogMode(logger.Info)
	DefaultNavigationBarRepository = repository.NewDefaultNavigationBarRepository(Conn)
	CustomizedNavigationBarRepository = repository.NewCustomizedNavigationBarRepository(Conn)
}
