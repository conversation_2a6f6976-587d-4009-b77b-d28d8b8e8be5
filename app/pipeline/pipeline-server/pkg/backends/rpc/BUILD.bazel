load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "rpc",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/backends/rpc",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/authz:authz_go_proto_xrpc_and_kitex_AuthzService",
        "@org_byted_code_kite_kitex//client",
        "@org_byted_code_overpass_bytedance_bits_meta//kitex_gen/bytedance/bits/meta/metaservice",
    ],
)
