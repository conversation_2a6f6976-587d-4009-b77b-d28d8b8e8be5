load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "cron",
    srcs = [
        "api.go",
        "client.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/backends/cron",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pipeline/pipeline-server/kitex_gen/bytedance/bits/cronjob",
        "//app/pipeline/pipeline-server/kitex_gen/bytedance/bits/cronjob/cronjobservice",
        "//app/pipeline/pipeline-server/pkg/backends",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_lang_gg//gresult",
    ],
)
