/**
 * @Date: 2022/3/9
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package triggerservice

import (
	"context"
	"sync"

	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/kitex_gen/bytedance/bits/cronjob"
	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/backends/cron"
)

type TriggerService struct {
	cron cron.Api
}

func NewTriggerService(cron cron.Api) *TriggerService {
	return &TriggerService{
		cron: cron,
	}
}

// DeleteCronjobByConfigID
// configID 是 pipeline_template_config 表的 id
func (service *TriggerService) DeleteCronjobByConfigID(ctx context.Context, configID int64) error {
	jobs := service.cron.QueryJobs(ctx, configID).ValueOr(make([]*cronjob.Job, 0))

	wg := new(sync.WaitGroup)
	for _, job := range jobs {
		wg.Add(1)
		go func(id int64) {
			defer wg.Done()
			_ = service.cron.DeleteJob(ctx, id)
		}(job.Id)
	}
	wg.Wait()
	return nil
}
