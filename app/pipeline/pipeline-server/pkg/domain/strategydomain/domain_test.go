/**
 * @Date: 2022/3/24
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package strategydomain

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/kitex_gen/bits/devops/pipeline_server"
	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/dal/redis"
	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline-server/pkg/dal/redis/cache"
	"code.byted.org/devinfra/hagrid/libs/connections/sqlite"
	"github.com/stretchr/testify/assert"
)

func TestStrategyDomain(t *testing.T) {
	ctx := context.Background()
	rdb := redis.MustInitialize(redis.NewLocalConfig())
	db := sqlite.MustInitialize()
	_ = db.AutoMigrate(new(entity.GreyStrategy), new(entity.GreyStrategyRelease))
	greyStrategyCache := cache.NewGreyStrategyCache(rdb)
	greyStrategyRepository := repository.NewGreyStrategyRepository(db)
	greyStrategyReleaseCache := cache.NewGreyStrategyReleaseCache(rdb)
	greyStrategyReleaseRepository := repository.NewGreyStrategyReleaseRepository(db)
	domain := NewStrategyDomain(rdb, db)

	t.Run("CreateGreyStrategy", func(t *testing.T) {
		request := &pipeline_server.CreateGreyStrategyRequest{
			Name:      "abc",
			ProjectID: 123,
			ConfigID:  234,
			Rate:      10,
			Director:  "<EMAIL>",
		}
		domain.CreateGreyStrategy(ctx, request).Must()
		defer func() {
			request := &pipeline_server.DeleteGreyStrategyRequest{ProjectID: 123}
			domain.DeleteGreyStrategy(ctx, request)
		}()

		{
			strategy := greyStrategyCache.FindByProjectID(ctx, 123).Must()
			assert.Equal(t, int64(234), strategy.ConfigID)
			assert.Equal(t, int32(10), strategy.Rate)
		}
		{
			strategy := greyStrategyRepository.FindLastByProjectID(ctx, 123).Must()
			assert.Equal(t, int64(234), strategy.ConfigID)
			assert.Equal(t, int32(10), strategy.Rate)
		}
	})

	t.Run("HitGreyStrategy", func(t *testing.T) {
		t.Run("没有设置灰度策略时", func(t *testing.T) {
			request := &pipeline_server.HitGreyStrategyRequest{ProjectID: 123, MrIID: 345}
			response := domain.HitGreyStrategy(ctx, request).Must()
			assert.Equal(t, int64(0), response.GetConfigID())
		})

		t.Run("如果配置累灰度,并且命中过灰度,retry pipeline时候,依旧命中", func(t *testing.T) {
			strategy := entity.NewGreyStrategy("", 222, 444, 1, "")
			err := greyStrategyRepository.Create(ctx, strategy)
			assert.Nil(t, err)

			release := entity.NewGreyStrategyRelease(222, 333, 444)
			err = greyStrategyReleaseRepository.Create(ctx, release)
			assert.Nil(t, err)

			defer func() {
				greyStrategyReleaseCache.DeleteByProjectIDAndMrIID(ctx, 222, 333)
				_ = greyStrategyReleaseRepository.DeleteByProjectID(ctx, 222)
			}()

			request := &pipeline_server.HitGreyStrategyRequest{ProjectID: 222, MrIID: 333}
			response := domain.HitGreyStrategy(ctx, request).Must()
			assert.Equal(t, int64(444), response.GetConfigID())
		})
	})
}
