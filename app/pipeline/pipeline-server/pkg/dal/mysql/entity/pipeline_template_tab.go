/**
 * @Date: 2022/3/9
 * @Author: <PERSON> "Long Middle Name Looks Cool" Liu
 * @Email: <EMAIL>
 */

package entity

import (
	"time"

	"gorm.io/gorm/schema"
)

type Tab struct {
	ID        int64     `gorm:"column:id"`
	ConfigID  int64     `gorm:"column:config_id"`
	Name      string    `gorm:"column:name"`
	AppID     int64     `gorm:"column:app_id"`
	CreatedAt time.Time `gorm:"column:created_at"`
}

var _ schema.Tabler = &Tab{}

func (Tab) TableName() string {
	return "pipeline_template_tab"
}

func NewTab(configID int64, name string, appID int64) *Tab {
	return &Tab{
		ConfigID: configID,
		Name:     name,
		AppID:    appID,
	}
}
