package rpc

import (
	"context"
	"errors"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	"code.byted.org/devinfra/hagrid/app/pipeline/pipeline_template/kitex_gen/bytedance/bits/workflow"
	"code.byted.org/gopkg/logs/v2/log"
)

func GetMPipelineMap(ctx context.Context, ids []int64) (map[int64]*workflow.Pipeline, error) {
	req := workflow.NewMGetPipelineRequest()
	req.SetPipelineIds(ids)
	r := make(map[int64]*workflow.Pipeline)
	resp, err := Workflow.MGetPipeline(ctx, req)
	if err != nil {
		log.V1.CtxError(ctx, "query pipelines from workflow failed, reason: %v\nrequest is: %v", err, jsons.Stringify(req))
		return r, err
	} else if resp == nil || resp.Pipelines == nil {
		return r, errors.New("empty resp")
	}
	for _, pipeline := range resp.Pipelines {
		r[pipeline.PipelineID] = pipeline
	}
	for _, id := range req.PipelineIds {
		if _, ok := r[id]; !ok {
			r[id] = &workflow.Pipeline{}
		}
	}
	return r, nil
}
