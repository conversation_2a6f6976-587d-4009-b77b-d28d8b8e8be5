load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "db",
    srcs = ["init.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/pipeline/pipeline_template/pkg/db",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pipeline/pipeline_template/pkg/config",
        "//app/pipeline/pipeline_template/pkg/dal/mysql",
        "//app/pipeline/pipeline_template/pkg/dal/mysql/repository",
        "@io_gorm_gorm//:gorm",
        "@io_gorm_plugin_dbresolver//:dbresolver",
    ],
)
