package main

import (
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/devmind"
	"context"

	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/bytecycle"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/bytest"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/calendar"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/codebase"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/codereview"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/configservice"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/dev"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/identity"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/identity_manager"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/integration"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/integrationworkflow"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/larkapi"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/meego"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/meta"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/oncall"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/optimus"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/portal"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/qareview"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/space"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/backends/translation"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/dal/redis"
	dal_tcc "code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/domain/bridgedomain"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/domain/factorydomain"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/domain/layoutdomain"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/domain/previewdomain"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/domain/workbenchdomain"
	"code.byted.org/devinfra/hagrid/app/workbenchrpc/script"
	"code.byted.org/devinfra/hagrid/pkg/tcc"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/workbenchpb"
	"google.golang.org/protobuf/types/known/emptypb"
)

type WorkbenchServiceImpl struct {
	workbench *workbenchdomain.WorkbenchDomain
	layout    *layoutdomain.LayoutDomain
	factory   *factorydomain.FactoryDomain
	preview   *previewdomain.PreviewDomain
	bridge    *bridgedomain.BridgeDomain
}

var _ workbenchpb.WorkbenchService = &WorkbenchServiceImpl{}

func NewWorkbenchService(config *Config) workbenchpb.WorkbenchService {
	db := mysql.MustInitialize(config.MySQL)
	rdb := redis.MustInitialize(config.Redis)
	tccConfigCenter := dal_tcc.NewConfigCenter(tcc.MustConnect(config.TCC))

	larkAPI := larkapi.New(rdb)
	meegoAPI := meego.New()
	optimusAPI := optimus.New()
	oncallAPI := oncall.New()
	codeReviewAPI := codereview.New()
	integrationAPI := integration.New()
	portalAPI := portal.New()
	devAPI := dev.New()
	metaAPI := meta.New()
	spaceAPI := space.New()
	calendarAPI := calendar.New()
	codebaseAPI := codebase.New()
	bytestAPI := bytest.New()
	bytecycleAPI := bytecycle.New()
	qaReviewAPI := qareview.New()
	identityAPI := identity.New()
	identityManagerAPI := identity_manager.New()
	translationAPI := translation.New()
	integrationWorkflowAPI := integrationworkflow.New()
	configAPI := configservice.New()
	devmindAPI := devmind.New()

	return &WorkbenchServiceImpl{
		workbench: workbenchdomain.NewWorkbenchDomain(rdb, db, tccConfigCenter, metaAPI, spaceAPI, identityAPI, translationAPI, configAPI, bytestAPI),
		layout:    layoutdomain.NewLayoutDomain(rdb, db, identityAPI, devmindAPI, tccConfigCenter),
		factory:   factorydomain.NewFactoryDomain(rdb, db, identityAPI, devmindAPI),
		preview:   previewdomain.NewPreviewDomain(),
		bridge:    bridgedomain.NewBridgeDomain(rdb, db, meegoAPI, optimusAPI, codeReviewAPI, integrationAPI, portalAPI, devAPI, calendarAPI, codebaseAPI, metaAPI, bytestAPI, bytecycleAPI, qaReviewAPI, identityAPI, identityManagerAPI, spaceAPI, integrationWorkflowAPI, larkAPI, configAPI, oncallAPI, devmindAPI),
	}
}

func (impl *WorkbenchServiceImpl) InitializeWorkbench(ctx context.Context, req *workbenchpb.InitializeWorkbenchRequest) (*workbenchpb.InitializeWorkbenchResponse, error) {
	return impl.workbench.InitializeWorkbench(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) SwitchWorkbench(ctx context.Context, req *workbenchpb.SwitchWorkbenchRequest) (*workbenchpb.SwitchWorkbenchResponse, error) {
	return impl.workbench.SwitchWorkbench(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListDefaultLayouts(ctx context.Context, req *workbenchpb.ListDefaultLayoutsRequest) (*workbenchpb.ListDefaultLayoutsResponse, error) {
	return impl.layout.ListDefaultLayouts(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetLayout(ctx context.Context, req *workbenchpb.GetLayoutRequest) (*workbenchpb.GetLayoutResponse, error) {
	return impl.layout.GetLayout(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) UpdateLayout(ctx context.Context, req *workbenchpb.UpdateLayoutRequest) (*workbenchpb.UpdateLayoutResponse, error) {
	return impl.layout.UpdateLayout(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) UpdateLayoutV2(ctx context.Context, req *workbenchpb.UpdateLayoutV2Request) (*workbenchpb.UpdateLayoutResponse, error) {
	return impl.layout.UpdateLayoutV2(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetUserRole(ctx context.Context, req *workbenchpb.GetUserRoleRequest) (*workbenchpb.GetUserRoleResponse, error) {
	return impl.workbench.GetUserRole(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetUserRoleV2(ctx context.Context, req *workbenchpb.GetUserRoleRequest) (*workbenchpb.GetUserRoleV2Response, error) {
	return impl.workbench.GetUserRoleV2(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) DeleteUserRole(ctx context.Context, req *workbenchpb.DeleteUserRoleRequest) (*workbenchpb.DeleteUserRoleResponse, error) {
	return impl.workbench.DeleteUserRole(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListComponents(ctx context.Context, req *workbenchpb.ListComponentsRPCRequest) (*workbenchpb.ListComponentsResponse, error) {
	return impl.factory.ListComponents(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetLastVisitSpace(ctx context.Context, req *workbenchpb.GetLastVisitSpaceRequest) (*workbenchpb.GetLastVisitSpaceResponse, error) {
	return impl.workbench.GetLastVisitSpace(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListComponentPreviews(ctx context.Context, req *workbenchpb.ListComponentPreviewsRequest) (*workbenchpb.ListComponentPreviewsResponse, error) {
	return impl.preview.ListComponentPreviews(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListMeegoWorkItems(ctx context.Context, req *workbenchpb.ListMeegoWorkItemsRequest) (*workbenchpb.ListMeegoWorkItemsResponse, error) {
	return impl.bridge.ListMeegoWorkItems(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetMeegoWorkItemAmounts(ctx context.Context, req *workbenchpb.GetMeegoWorkItemAmountsRequest) (*workbenchpb.GetMeegoWorkItemAmountsResponse, error) {
	return impl.bridge.GetMeegoWorkItemAmounts(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListQuickEntries(ctx context.Context, req *workbenchpb.ListQuickEntriesRequest) (*workbenchpb.ListQuickEntriesResponse, error) {
	return impl.workbench.ListQuickEntries(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) TrackQuickEntry(ctx context.Context, req *workbenchpb.TrackQuickEntryRequest) (*workbenchpb.TrackQuickEntryResponse, error) {
	return impl.workbench.TrackQuickEntry(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListMergeRequests(ctx context.Context, req *workbenchpb.ListMergeRequestsRequest) (*workbenchpb.ListMergeRequestsResponse, error) {
	return impl.bridge.ListMergeRequests(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetMergeRequestReviewGroups(ctx context.Context, req *workbenchpb.GetMergeRequestReviewGroupsRequest) (*workbenchpb.GetMergeRequestReviewGroupsResponse, error) {
	return impl.bridge.GetMergeRequestReviewGroups(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListTools(ctx context.Context, req *workbenchpb.ListToolsRequest) (*workbenchpb.ListToolsResponse, error) {
	return impl.bridge.ListTools(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListSmallMergeRequests(ctx context.Context, req *workbenchpb.ListSmallMergeRequestsRequest) (*workbenchpb.ListSmallMergeRequestsResponse, error) {
	return impl.bridge.ListSmallMergeRequests(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListIntegratedQueues(ctx context.Context, req *workbenchpb.ListIntegratedQueuesRequest) (*workbenchpb.ListIntegratedQueuesResponse, error) {
	return impl.bridge.ListIntegratedQueues(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListCalendarSpaces(ctx context.Context, req *workbenchpb.ListCalendarSpacesRequest) (*workbenchpb.ListCalendarSpacesResponse, error) {
	return impl.bridge.ListCalendarSpaces(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetCalendar(ctx context.Context, req *workbenchpb.GetCalendarRequest) (*workbenchpb.GetCalendarResponse, error) {
	return impl.bridge.GetCalendar(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListCodebaseChanges(ctx context.Context, req *workbenchpb.ListCodebaseChangesRequest) (*workbenchpb.ListCodebaseChangesResponse, error) {
	return impl.bridge.ListCodebaseChanges(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetCodebaseChangeAmounts(ctx context.Context, req *workbenchpb.GetCodebaseChangeAmountsRequest) (*workbenchpb.GetCodebaseChangeAmountsResponse, error) {
	return impl.bridge.GetCodebaseChangeAmounts(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListCodebaseCheckRuns(ctx context.Context, req *workbenchpb.ListCodebaseCheckRunsRequest) (*workbenchpb.ListCodebaseCheckRunsResponse, error) {
	return impl.bridge.ListCodebaseCheckRuns(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListTestPlans(ctx context.Context, req *workbenchpb.ListTestPlansRequest) (*workbenchpb.ListTestPlansResponse, error) {
	return impl.bridge.ListTestPlans(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListTestCases(ctx context.Context, req *workbenchpb.ListTestCasesRequest) (*workbenchpb.ListTestCasesResponse, error) {
	return impl.bridge.ListTestCases(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListDevelopmentTasks(ctx context.Context, req *workbenchpb.ListDevelopmentTasksRequest) (*workbenchpb.ListDevelopmentTasksResponse, error) {
	return impl.bridge.ListDevelopmentTasks(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListReleaseTickets(ctx context.Context, req *workbenchpb.ListReleaseTicketsRequest) (*workbenchpb.ListReleaseTicketsResponse, error) {
	return impl.bridge.ListReleaseTickets(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetMergeRequestAmounts(ctx context.Context, req *workbenchpb.GetMergeRequestAmountsRequest) (*workbenchpb.GetMergeRequestAmountsResponse, error) {
	return impl.bridge.GetMergeRequestAmounts(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) RemindMergeRequestReviewers(ctx context.Context, req *workbenchpb.RemindMergeRequestReviewersRequests) (*workbenchpb.RemindMergeRequestReviewersResponse, error) {
	return impl.bridge.RemindMergeRequestReviewers(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) RemindMergeRequestQaReviewers(ctx context.Context, req *workbenchpb.RemindMergeRequestReviewersRequests) (*workbenchpb.RemindMergeRequestReviewersResponse, error) {
	return impl.bridge.RemindMergeRequestQaReviewers(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) RemindMergeRequestRdReviewers(ctx context.Context, req *workbenchpb.RemindMergeRequestReviewersRequests) (*workbenchpb.RemindMergeRequestReviewersResponse, error) {
	return impl.bridge.RemindMergeRequestRdReviewers(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) CreateMergeRequestChatGroup(ctx context.Context, req *workbenchpb.CreateMergeRequestChatGroupRequest) (*workbenchpb.CreateMergeRequestChatGroupResponse, error) {
	return impl.bridge.CreateMergeRequestChatGroup(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListQuickEntriesV2(ctx context.Context, req *workbenchpb.ListQuickEntriesV2Request) (*workbenchpb.ListQuickEntriesV2Response, error) {
	return impl.workbench.ListQuickEntriesV2(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetQuickEntryActionPrompt(ctx context.Context, req *workbenchpb.GetQuickEntryActionPromptRequest) (*workbenchpb.GetQuickEntryActionPromptResponse, error) {
	return impl.workbench.GetQuickEntryActionPrompt(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListMeegoWorkItemInfos(ctx context.Context, req *workbenchpb.ListMeegoWorkItemInfosRequest) (*workbenchpb.ListMeegoWorkItemInfosResponse, error) {
	return impl.bridge.ListMeegoWorkItemInfos(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListLarkApproveTasks(ctx context.Context, req *workbenchpb.ListLarkApproveTasksRequest) (res *workbenchpb.ListLarkApproveTasksResponse, err error) {
	return impl.bridge.ListLarkApproveTasks(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) MeegoWorkItemWatch(ctx context.Context, req *workbenchpb.MeegoWorkItemWatchRequest) (res *emptypb.Empty, err error) {
	return impl.bridge.MeegoWorkItemWatch(ctx, req).Get()
}

// API For Tester
func (impl *WorkbenchServiceImpl) SwitchUserLeaderRole(ctx context.Context, req *workbenchpb.SwitchUserLeaderRoleRequest) (*workbenchpb.SwitchUserLeaderRoleResponse, error) {
	return impl.workbench.SwitchUserLeaderRole(ctx, req).Get()
}

// API For Work Summary
func (impl *WorkbenchServiceImpl) ListWorkSummaryCounts(ctx context.Context, req *workbenchpb.ListWorkSummaryRequest) (*workbenchpb.ListWorkSummaryResponse, error) {
	return impl.bridge.ListWorkSummaryCounts(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetOncallItems(ctx context.Context, req *workbenchpb.GetOncallItemsRequest) (*workbenchpb.GetOncallItemsResponse, error) {
	return impl.bridge.GetOncallItems(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetOncallItemChatInfo(ctx context.Context, req *workbenchpb.GetOncallItemChatInfoRequest) (*workbenchpb.GetOncallItemChatInfoResponse, error) {
	return impl.bridge.GetOncallItemChatInfo(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListMyTasks(ctx context.Context, req *workbenchpb.ListMyTasksRequest) (*workbenchpb.ListMyTasksResponse, error) {
	return impl.bridge.ListLarkTasks(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) FinishMyTask(ctx context.Context, req *workbenchpb.FinishMyTaskRequest) (*workbenchpb.TaskBaseUpdateResponse, error) {
	resp := &workbenchpb.TaskBaseUpdateResponse{}
	return resp, nil
}

func (impl *WorkbenchServiceImpl) UpdateTaskMembers(ctx context.Context, req *workbenchpb.UpdateTaskMembersRequest) (*workbenchpb.TaskBaseUpdateResponse, error) {
	resp := &workbenchpb.TaskBaseUpdateResponse{}
	return resp, nil
}

func (impl *WorkbenchServiceImpl) UpdateTaskDueDate(ctx context.Context, req *workbenchpb.UpdateTaskDueDateRequest) (*workbenchpb.TaskBaseUpdateResponse, error) {
	resp := &workbenchpb.TaskBaseUpdateResponse{}
	return resp, nil
}

func (impl *WorkbenchServiceImpl) ListMeegoWorkItemInfosByTime(ctx context.Context, req *workbenchpb.ListMeegoWorkItemsByTimeRequest) (*workbenchpb.ListMeegoWorkItemsByTimeResponse, error) {
	return impl.bridge.ListMeegoWorkItemInfosByTime(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) GetMeegoWorkflow(ctx context.Context, req *workbenchpb.GetMeegoWorkflowRequest) (*workbenchpb.GetMeegoWorkflowResponse, error) {
	return impl.bridge.GetMeegoWorkflow(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) BatchGetMeegoWorkflow(ctx context.Context, req *workbenchpb.BatchGetMeegoWorkflowRequest) (*workbenchpb.BatchGetMeegoWorkflowResponse, error) {
	return impl.bridge.BatchGetMeegoWorkflow(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) UpdateMeegoNodeSchedule(ctx context.Context, req *workbenchpb.UpdateMeegoNodeScheduleRequest) (*workbenchpb.UpdateMeegoNodeScheduleResponse, error) {
	return impl.bridge.UpdateMeegoNodeSchedule(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) WorkbenchOpt(ctx context.Context, req *workbenchpb.WorkbenchOptRequest) (*workbenchpb.WorkbenchOptResponse, error) {
	return script.OptHandler(ctx, req.Opt)
}

func (impl *WorkbenchServiceImpl) GetUserDefaultLayoutStatus(ctx context.Context, req *workbenchpb.GetUserDefaultLayoutStatusRequest) (*workbenchpb.GetUserDefaultLayoutStatusResponse, error) {
	return impl.layout.GetUserDefaultLayoutStatus(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ChangeUserDefaultLayoutStatus(ctx context.Context, req *workbenchpb.ChangeUserDefaultLayoutStatusRequest) (*workbenchpb.ChangeUserDefaultLayoutStatusResponse, error) {
	return impl.layout.ChangeUserDefaultLayoutStatus(ctx, req).Get()
}

func (impl *WorkbenchServiceImpl) ListRecommendTool(ctx context.Context, req *workbenchpb.ListRecommendToolRequest) (*workbenchpb.ListRecommendToolResponse, error) {
	return impl.bridge.ListRecommendTool(ctx, req).Get()
}
