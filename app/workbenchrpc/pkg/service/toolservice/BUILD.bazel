load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "toolservice",
    srcs = [
        "filter.go",
        "service.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/service/toolservice",
    visibility = ["//visibility:public"],
    deps = [
        "//app/workbenchrpc/pkg/backends/identity_manager:identity",
        "//app/workbenchrpc/pkg/backends/portal",
        "//app/workbenchrpc/pkg/dal/redis/cache",
        "//app/workbenchrpc/pkg/typing",
        "//idls/byted/devinfra/portal:portal_go_proto",
        "//idls/byted/devinfra/workbench:workbench_go_proto",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_bytecloud_intelligen//kitex_gen/bytecloud/identity/manager",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_kv_goredis//:goredis",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gslice",
    ],
)
