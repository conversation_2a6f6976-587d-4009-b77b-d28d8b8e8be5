load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "previewdomain",
    srcs = ["domain.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/domain/previewdomain",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/infra:infra_go_proto",
        "//idls/byted/devinfra/workbench:workbench_go_proto",
        "@org_byted_code_lang_gg//gresult",
        "@org_byted_code_lang_gg//gutil",
    ],
)
