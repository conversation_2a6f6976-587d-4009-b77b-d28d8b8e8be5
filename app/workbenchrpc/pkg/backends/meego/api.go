package meego

import (
	"context"

	"code.byted.org/lang/gg/collection/tuple"
	"code.byted.org/lang/gg/gresult"
)

type Api interface {
	QueryUsers(ctx context.Context, request *QueryUsersRequest) gresult.R[[]*User]
	QueryUserByEmail(ctx context.Context, email string) gresult.R[*User]
	QueryUserByUserKey(ctx context.Context, userKey string) gresult.R[*User]
	QueryUsersByUserKeys(ctx context.Context, userKeys []string) gresult.R[[]*User]
	QueryProjects(ctx context.Context, request *QueryProjectsRequest) gresult.R[map[string]*Project]

	QueryWorkflow(ctx context.Context, userKey string, request *QueryWorkflowRequest) gresult.R[[]*WorkflowNode]

	ListAcrossProjectWorkItems(ctx context.Context, userKey string, request *ListAcrossProjectWorkItemsRequest) gresult.R[tuple.T2[[]*WorkItemInfo, *Pagination]]

	ListSubTasks(ctx context.Context, userKey string, request *ListSubTasksRequest) gresult.R[[]*SubDetail]

	UpdateFields(ctx context.Context, userKey, projectKey, workItemType string, id int, request *UpdateFieldsRequest) (err error)

	UpdateNodeSchedule(ctx context.Context, userKey string, request *UpdateScheduleRequest) (err error)
}
