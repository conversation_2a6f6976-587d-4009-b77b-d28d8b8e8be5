load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "typing",
    srcs = [
        "meego.go",
        "page.go",
        "role.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/workbenchrpc/pkg/typing",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/workbench:workbench_go_proto",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_lang_gg//gresult",
    ],
)
