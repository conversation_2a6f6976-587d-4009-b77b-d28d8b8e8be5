CREATE TABLE IF NOT EXISTS `workbench_layout`
(
    `id`         BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY COMMENT 'id',
    `created_at` TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'created at',
    `updated_at` TIMESTAMP       NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'updated at',
    `username`   VA<PERSON>HA<PERSON>(255)    NOT NULL COMMENT 'ByteDance SSO username',
    `config`     VARCHAR(1024)   NOT NULL COMMENT 'user workbench layout config',
    UNIQUE `uniq_username` (`username`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
    COMMENT 'store user workbench layout config';
