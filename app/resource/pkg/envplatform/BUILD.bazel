load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "envplatform",
    srcs = [
        "consts.go",
        "env_platform.go",
        "messages.go",
        "tce.go",
        "utils.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/resource/pkg/envplatform",
    visibility = ["//visibility:public"],
    deps = [
        "//libs/bits_err",
        "//pkg/net/httpclient",
        "@com_github_allegro_bigcache_v3//:bigcache",
        "@com_github_samber_lo//:lo",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_iesarch_cdaas_utils//erri",
    ],
)
