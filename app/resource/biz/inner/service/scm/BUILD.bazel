load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "scm",
    srcs = [
        "api.go",
        "data_sync.go",
        "init.go",
        "repo.go",
        "version.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/resource/biz/inner/service/scm",
    visibility = ["//visibility:public"],
    deps = [
        "//app/resource/pkg/inner/remote",
        "//app/resource/pkg/inner/remote/scm",
        "//app/resource/pkg/runtime",
        "//libs/bits_err",
        "//pkg/resource/protocol",
        "//pkg/resource/scm",
        "//pkg/scm",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_canal_provider//scm",
    ],
)
