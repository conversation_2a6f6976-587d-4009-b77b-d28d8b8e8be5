package tce

import (
	tce_provider "code.byted.org/canal/provider/tce"
	"code.byted.org/devinfra/hagrid/app/resource/pkg/protocol"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/resource/tce"
	"context"
	"github.com/cloudwego/hertz/pkg/app"
)

func GetAllClusterListByServiceID(ctx context.Context, c *app.RequestContext,
	req *protocol.GenericRequest[tce.GetAllClusterListByServiceIDReq]) (*tce.GetAllClusterListByServiceIDResp, error) {
	resp, err := tce_provider.GetAllClusterListByServiceID(ctx, req.Payload.ServiceID, req.Payload.Idc,
		req.Payload.TceEnv)
	if err != nil {
		return nil, bits_err.RESOURCE.ErrTCEGetClusterList.AddError(err)
	}
	return &tce.GetAllClusterListByServiceIDResp{
		ClusterInfos: resp,
	}, nil
}
