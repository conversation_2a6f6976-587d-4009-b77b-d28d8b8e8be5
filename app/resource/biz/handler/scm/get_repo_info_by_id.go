package scm

import (
	scm_service "code.byted.org/devinfra/hagrid/app/resource/biz/service/scm"
	"code.byted.org/devinfra/hagrid/app/resource/pkg/protocol"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/resource/scm"
	"context"
	"github.com/cloudwego/hertz/pkg/app"
)

func GetRepoInfoById(ctx context.Context, c *app.RequestContext,
	r *protocol.GenericRequest[scm.GetRepoInfoReq]) (*scm.GetRepoInfoResp, error) {
	req := r.Payload
	scmService := scm_service.GetScmService()
	repoInfo, exist, err := scmService.GetRepoInfo(ctx, &req)
	if err != nil {
		return nil, bits_err.COMMON.ErrSystemException.AddOrPass(ctx, err)
	}
	resp := &scm.GetRepoInfoResp{
		RepoInfo: repoInfo,
		Exists:   exist,
	}
	return resp, nil
}
