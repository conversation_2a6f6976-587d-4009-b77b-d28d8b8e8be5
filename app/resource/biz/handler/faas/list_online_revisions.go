package faas

import (
	"code.byted.org/devinfra/hagrid/app/resource/biz/service/faas"
	"code.byted.org/devinfra/hagrid/app/resource/pkg/protocol"
	faas_pkg "code.byted.org/devinfra/hagrid/pkg/resource/faas"
	"context"
	"github.com/cloudwego/hertz/pkg/app"
)

func ListOnlineRevisions(ctx context.Context, c *app.RequestContext,
	r *protocol.GenericRequest[faas_pkg.ListOnlineRevisionsReq]) (*faas_pkg.ListOnlineRevisionsResp, error) {
	req := r.Payload
	revisions, err := faas.GetClient().ListOnlineRevisions(ctx, req.ServiceID, req.Cluster, req.Region)
	if err != nil {
		return nil, err
	}
	return &faas_pkg.ListOnlineRevisionsResp{
		Revisions: revisions,
	}, nil
}