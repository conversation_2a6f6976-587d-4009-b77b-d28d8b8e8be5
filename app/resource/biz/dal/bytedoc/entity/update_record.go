package entity

type UpdateRecord struct {
	ServiceType  ServiceType `json:"service_type" bson:"service_type"`
	EntityType   EntityType  `json:"entity_type" bson:"entity_type"`
	Region       string      `json:"region" bson:"region"`
	EnvType      string      `json:"env_type" bson:"env_type"`
	Lane         string      `json:"lane" bson:"lane"`
	PSM          string      `json:"psm" bson:"psm"`
	ServiceID    int64       `json:"service_id" bson:"service_id"`
	UpdatedAt    int64       `json:"updated_at" bson:"updated_at"`
	TOSPath      string      `json:"tos_path" bson:"tos_path"`
	UpdateSource string      `json:"update_source" bson:"update_source"`
}

type ServiceType string

const (
	ServiceTypeTCE ServiceType = "tce"
	ServiceTypeSCM ServiceType = "scm"
)

// EntityType 变更的实体类型
type EntityType string

const (
	EntityTypeServiceInfo         EntityType = "ServiceInfo"
	EntityTypeRepoInfo            EntityType = "RepoInfo"
	EntityTypeRepoVersion         EntityType = "RepoVersion"
	EntityTypeLatestOnlineVersion EntityType = "LatestOnlineVersion"
)
