package repository

import (
	"code.byted.org/bytedoc/mongo-go-driver/bson"
	"code.byted.org/devinfra/hagrid/app/resource/biz/dal/bytedoc"
	"code.byted.org/devinfra/hagrid/app/resource/biz/dal/bytedoc/entity"
	"code.byted.org/devinfra/hagrid/pkg/resource/tce"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"context"
	"github.com/pkg/errors"
)

type TceServiceInfoDao interface {
	Upsert(ctx context.Context, serviceInfo *entity.TceServiceInfo) error
	FindByPSM(ctx context.Context, identifier tce.ServiceIdentifier) (*entity.TceServiceInfo, error)
}

type tceServiceInfoDaoImpl struct {
	collection string
	TceServiceInfoDao
}

func NewTceServiceInfoDao() TceServiceInfoDao {
	return &tceServiceInfoDaoImpl{
		collection: "tce_service_info",
	}
}

func (i *tceServiceInfoDaoImpl) Upsert(ctx context.Context, serviceInfo *entity.TceServiceInfo) error {
	_, err := bytedoc.Upsert(ctx, bytedoc.GetBitsResourceWriteConn(), i.collection, bson.M{
		"region":  serviceInfo.Region,
		"tce_env": serviceInfo.TceEnv,
		"psm":     serviceInfo.PSM,
		"app_env": serviceInfo.AppEnv,
	}, bson.M{
		"$set": serviceInfo,
	})
	if err != nil {
		return bits_err.RESOURCE.ErrDBUpsert.AddError(errors.WithStack(err))
	}

	return nil
}

func (i *tceServiceInfoDaoImpl) FindByPSM(ctx context.Context, identifier tce.ServiceIdentifier) (*entity.TceServiceInfo, error) {
	ret, err := bytedoc.FindOne[entity.TceServiceInfo](ctx, bytedoc.GetBitsResourceWriteConn(), i.collection, bson.M{
		"region":  identifier.Region,
		"tce_env": identifier.TceEnv,
		"psm":     identifier.PSM,
		"app_env": identifier.AppEnv,
	})
	if err != nil {
		return nil, bits_err.RESOURCE.ErrDBFindByPSM.AddError(errors.WithStack(err))
	}
	return ret, nil
}
