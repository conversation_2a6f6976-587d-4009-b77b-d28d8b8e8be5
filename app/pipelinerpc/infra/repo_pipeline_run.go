package infra

import (
	"context"
	"fmt"
	"strings"
	"time"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/security/go-polaris/sql"
	"github.com/pkg/errors"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/hints"
	"gorm.io/plugin/dbresolver"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/model"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/perror"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
)

//go:generate mockgen -source=repo_pipeline_run.go -destination=./mock/repo_pipeline_run_mock.go -package=mock
type IPipelineRunRepo interface {
	Create(ctx context.Context, value *entity.SimplePipelineRun) (uint64, error)
	FindLastByPipelineIdIn(ctx context.Context, pipelineIds []uint64) ([]*entity.SimplePipelineRun, error)
	DeleteById(ctx context.Context, id uint64) error

	GetLastRunIdEventBlocked(ctx context.Context, pipelineId uint64) (runId uint64, err error)
	GetPipelineRun(ctx context.Context, runID uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error)
	GetPipelineRunFromReadOnlyAndContainNotVisible(ctx context.Context, pipelineRunID uint64) (r entity.PipelineRun, err error)
	GetPipelineRunsByIDs(ctx context.Context, runIDs []uint64) ([]*entity.PipelineRun, error)
	GetPipelineRunsByIdempotentToken(ctx context.Context, pipelineID uint64, token string) ([]*entity.PipelineRun, error)
	GetPipelineRunsByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) ([]*entity.PipelineRun, error)
	GetPipelineRunsByPipelineIDAndStatusWithLimit(ctx context.Context, pipelineID uint64, limit int, status ...entity.PipelineRunStatus) ([]*entity.PipelineRun, error)
	GetFirstPipelineRunByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (*entity.PipelineRun, error)
	HasPipelineRunsByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (bool, error)
	GetPipelineRunByEngineRunID(ctx context.Context, engineRunID int64, withPipelineRunTrigger bool) (*entity.PipelineRun, error)
	GetPipelineRunsByEngineRunIDs(ctx context.Context, engineRunIDs ...int64) ([]*entity.PipelineRun, error)
	UpdateBlockingPipelineRunStatus(ctx context.Context, pipelineID uint64, runStatus entity.PipelineRunStatus) error
	UpdatePipelineRunStatus(ctx context.Context, runID uint64, runStatus entity.PipelineRunStatus) error
	GetMaxPipelineRunSeq(ctx context.Context, pipelineID uint64) (uint64, error)
	GetInvisibleMaxPipelineRunSeq(ctx context.Context, pipelineID uint64) (uint64, error)
	GetPipelineRunList(ctx context.Context, opts *ListPipelineRunOpts) ([]*entity.PipelineRun, int64, error)
	UpdatePipelineRunStatusByEngineRunID(ctx context.Context, engineRunID int64, runStatus entity.PipelineRunStatus, startedAt, completedAt *time.Time) error
	GetPipelineRunBySeq(ctx context.Context, pipelineId uint64, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error)
	GetPipelineRunMap(ctx context.Context, runIDs []uint64) (map[uint64]*entity.PipelineRun, error)
	GetPipelineRunIDByRunSeq(ctx context.Context, pipelineID, runSeq uint64) (uint64, error)
	GetPipelineRunIDsByPipelineID(ctx context.Context, pipelineID uint64, statuses []entity.PipelineRunStatus) ([]uint64, error)
	UpdatePipelineRunStatusByIDs(ctx context.Context, runIDs []uint64, status entity.PipelineRunStatus) error
	GetPipelineRunCountByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (int64, error)
	FilterPipelineNotEnd(ctx context.Context, pipelineIds []uint64, limit int) (filterPipelineIds []uint64, err error) // 批量删除流水线时，前端区分状态，弹框提醒用户
	// git pipeline run
	GetRunSeqByPipelineRunId(ctx context.Context, pipelineRunId uint64) (uint64, error)
	GetGitMaxPipelineRunSeqINChange(ctx context.Context, repo, branch, yaml string) (uint64, error)
	GetGitMaxPipelineRunSeq(ctx context.Context, repo, branch, tag, yaml string) (uint64, error)
	GetLatestPipelineRunsByYamlNames(ctx context.Context, repo, branch, tag string, yamls []string) ([]*entity.PipelineRun, error)
	GetGitPipelineRunBySeq(ctx context.Context, repo, branch, tag, yaml string, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error)
	GetGitPipelineRunBySeqAndTag(ctx context.Context, repo, tag, yaml string, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error)
	GetPipelineRunsByYamlFile(ctx context.Context, opts *SearchGitPipelineRunOpts) ([]*entity.PipelineRun, int64, error)
	GetGitUncompletedPipelineRuns(ctx context.Context, runIDs []uint64) ([]*entity.PipelineRun, error)
	GetGitPipelineRunIDs(ctx context.Context, repo, branch, tag, yaml string, statuses []entity.PipelineRunStatus) ([]uint64, error)
	GetGitPipelineRunIDsByTag(ctx context.Context, repo, tag, yaml string, statuses []entity.PipelineRunStatus) ([]uint64, error)
	GetGitPipelineRunIdByMaxSeq(ctx context.Context, repo, branch, tag, yamlFilename string) (uint64, error)
	GetRecentGitPipelineRunList(ctx context.Context, opts *SearchRecentGitPipelineRunOpts) ([]*entity.PipelineRun, error)
	GetGitPipelineRunIDSuccess(ctx context.Context, repo, branch, tag, yaml string) (uint64, error)
	// UpdatePipelineRunNote 更新pipeline run备注
	UpdatePipelineRunNote(ctx context.Context, runID uint64, note string) error
	UpdatePipelineRunNoteByEngineRunId(ctx context.Context, engineRunId uint64, note string) error
	GetLastPipelineRunByTriggerUser(ctx context.Context, pipelineId uint64, triggerUser string) (*entity.PipelineRun, error)
	GetLastPipelineRun(ctx context.Context, pipelineID uint64) (*entity.PipelineRun, error)
	GetLatestPipelineAndRunIDsByTriggerUser(ctx context.Context, triggerUser string, limit, offset int) (int64, []*model.PipelineRunPair, error)
	UpdatePipelineRun(ctx context.Context, runID uint64, pipelineRun *entity.PipelineRun) error
}

type pipelineRunRepo struct {
	db                     *gorm.DB
	unscopedDB             *gorm.DB // only used for pipeline run var
	pipelineRunTriggerRepo IPipelineRunTriggerRepo
}

func newPipelineRunRepo(db *gorm.DB) IPipelineRunRepo {
	// By default, invisible parts won't be queried, if needed please use `Unscoped`.
	return &pipelineRunRepo{
		db:                     db.Scopes(visiblePipelineRun),
		unscopedDB:             db,
		pipelineRunTriggerRepo: newPipelineRunTriggerRepo(db),
	}
}

func visiblePipelineRun(db *gorm.DB) *gorm.DB {
	return db.Where("is_invisible = ?", false)
}

type SearchRecentGitPipelineRunOpts struct {
	GitRepo      string
	GitBranches  []string
	GitTags      []string
	Statuses     []entity.PipelineRunStatus
	TriggerTypes []entity.TriggerType
	TriggeredBy  []string
	YamlFile     string
	Limit        int32
	Offset       int32
}

func (p *pipelineRunRepo) GetRecentGitPipelineRunList(ctx context.Context, opts *SearchRecentGitPipelineRunOpts) ([]*entity.PipelineRun, error) {
	var pipelineRuns []*entity.PipelineRun
	db := p.db.WithContext(ctx)
	if opts != nil {
		if len(opts.GitRepo) > 0 {
			db = db.Where("repo_name = ?", opts.GitRepo)
		}
		if len(opts.GitBranches) > 0 {
			db = db.Where("branch IN (?)", opts.GitBranches)
		}
		if len(opts.GitTags) > 0 {
			db = db.Where("tag IN (?)", opts.GitTags)
		}
		if len(opts.TriggeredBy) > 0 {
			db = db.Where("created_by IN (?)", opts.TriggeredBy)
		}
		if len(opts.Statuses) > 0 {
			db = db.Where("status IN (?)", opts.Statuses)
		}
		if len(opts.TriggerTypes) > 0 {
			db = db.Where("trigger_type IN (?)", opts.TriggerTypes)
		}
		if len(opts.YamlFile) > 0 {
			db = db.Where("yaml_filename = ?", opts.YamlFile)
		}
		if opts.Limit > 0 {
			db = db.Limit(int(opts.Limit))
		}
		if opts.Offset > 0 {
			db = db.Offset(int(opts.Offset))
		}
		db = db.Order("created_at DESC")
	}
	if err := db.Find(&pipelineRuns).Error; err != nil {
		return nil, err
	}
	return pipelineRuns, nil
}

func (p *pipelineRunRepo) GetLastRunIdEventBlocked(ctx context.Context, pipelineId uint64) (runId uint64, err error) {
	err = p.db.WithContext(ctx).
		Clauses(
			hints.ForceIndex("idx_pipeline_run_pipeline_id_invisible"),
		).
		Model(&entity.PipelineRun{}).Select("run_id").Where("pipeline_id = ?", pipelineId).Order("id DESC").Limit(1).Find(&runId).Error
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	return
}

func (p *pipelineRunRepo) GetPipelineRun(ctx context.Context, pipelineRunID uint64, withTrigger bool) (*entity.PipelineRun, error) {
	pipelineRun := &entity.PipelineRun{}
	if err := p.db.WithContext(ctx).
		Clauses(dbresolver.Write).
		Where("run_id = ?", pipelineRunID).
		First(&pipelineRun).Error; err != nil {
		logs.CtxError(ctx, err.Error())
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, bits_err.PIPELINECOMMON.ErrRecordNotFound
		}
		return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
	}

	if withTrigger {
		trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, pipelineRunID)
		if err != nil {
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}

		pipelineRun.TriggerInfo = trigger
	}

	return pipelineRun, nil
}

func (p *pipelineRunRepo) GetPipelineRunFromReadOnlyAndContainNotVisible(ctx context.Context, pipelineRunID uint64) (r entity.PipelineRun, err error) {
	err = p.unscopedDB.WithContext(ctx).Where("run_id = ?", pipelineRunID).First(&r).Error
	if err != nil {
		logs.CtxError(ctx, err.Error())
		if errors.Is(err, gorm.ErrRecordNotFound) {
			err = bits_err.PIPELINECOMMON.ErrRecordNotFound
		} else {
			err = bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
		return
	}
	return
}

func (p *pipelineRunRepo) Create(ctx context.Context, value *entity.SimplePipelineRun) (uint64, error) { // just for test
	err := p.db.
		WithContext(ctx).
		Create(value).
		Error
	if err != nil {
		return 0, err
	}
	return value.Id, nil
}

func (p *pipelineRunRepo) FindLastByPipelineIdIn(ctx context.Context, pipelineIds []uint64) ([]*entity.SimplePipelineRun, error) {
	values := make([]*entity.SimplePipelineRun, 0, len(pipelineIds))

	subQuery := p.db.
		WithContext(ctx).
		Table("`pipeline_run`").
		Select("`pipeline_id`, MAX(`id`) as `max_id`").
		Where("`pipeline_id` IN ?", pipelineIds).
		Group("pipeline_id")

	err := p.db.
		WithContext(ctx).
		Table("`pipeline_run`").
		InnerJoins("INNER JOIN (?) `p2` ON `p2`.`pipeline_id` = `pipeline_run`.`pipeline_id` AND `p2`.`max_id` = `pipeline_run`.`id`", subQuery).
		Limit(200).
		Find(&values).
		Error

	if err != nil {
		return make([]*entity.SimplePipelineRun, 0), err
	}
	return values, nil
}

func (p *pipelineRunRepo) DeleteById(ctx context.Context, id uint64) error {
	err := p.db.
		WithContext(ctx).
		Where("`id` = ?", id).
		Delete(new(entity.SimplePipelineRun)).
		Error

	if err != nil {
		return err
	}
	return nil
}

func (p *pipelineRunRepo) GetPipelineRunsByIdempotentToken(ctx context.Context, pipelineID uint64, token string) ([]*entity.PipelineRun, error) {
	var pipelineRuns []*entity.PipelineRun

	if err := p.db.WithContext(ctx).
		Where("pipeline_id = ? AND idempotent_token = ?", pipelineID, token).
		Find(&pipelineRuns).Error; err != nil {
		return nil, err
	}

	return pipelineRuns, nil
}

func (p *pipelineRunRepo) GetMaxPipelineRunSeq(ctx context.Context, pipelineID uint64) (uint64, error) {
	var maxRunSeq uint64

	err := p.db.Clauses(dbresolver.Write).WithContext(ctx).
		Table("pipeline_run").
		Where(" pipeline_id = ? ", pipelineID).
		Select("IFNULL(MAX(run_seq),0)").
		Scan(&maxRunSeq).Error

	if err != nil {
		if strings.Contains(err.Error(), "converting NULL to uint64 is unsupported") {
			logs.CtxWarn(ctx, "scan to max run seq failed, error: %s", err)
			return 0, nil
		}
		logs.CtxError(ctx, "query max run seq failed, error: %s", err)
		return 0, err
	}

	return maxRunSeq, nil
}

func (p *pipelineRunRepo) GetInvisibleMaxPipelineRunSeq(ctx context.Context, pipelineID uint64) (uint64, error) {
	var maxRunSeq uint64

	err := p.unscopedDB.Clauses(dbresolver.Write).WithContext(ctx).
		Table("pipeline_run").
		Where(" pipeline_id = ? ", pipelineID).
		Select("IFNULL(MAX(run_seq),0)").
		Scan(&maxRunSeq).Error

	if err != nil {
		if strings.Contains(err.Error(), "converting NULL to uint64 is unsupported") {
			logs.CtxWarn(ctx, "scan to max run seq failed, error: %s", err)
			return 0, nil
		}
		logs.CtxError(ctx, "query max run seq failed, error: %s", err)
		return 0, err
	}

	return maxRunSeq, nil
}

func (p *pipelineRunRepo) GetPipelineRunsByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) ([]*entity.PipelineRun, error) {
	var pipelineRuns []*entity.PipelineRun

	if len(status) == 1 {
		if err := p.db.WithContext(ctx).
			Order("id").
			Where("pipeline_id = ? AND status = ?", pipelineID, status[0]).
			Find(&pipelineRuns).Error; err != nil {
			err = perror.GetBitsError(ctx, err)
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	} else {
		if err := p.db.WithContext(ctx).
			Order("id").
			Where("pipeline_id = ? AND status in (?)", pipelineID, status).
			Find(&pipelineRuns).Error; err != nil {
			err = perror.GetBitsError(ctx, err)
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	}

	return pipelineRuns, nil
}

func (p *pipelineRunRepo) GetPipelineRunsByPipelineIDAndStatusWithLimit(ctx context.Context, pipelineID uint64, limit int, status ...entity.PipelineRunStatus) ([]*entity.PipelineRun, error) {
	var pipelineRuns []*entity.PipelineRun

	if len(status) == 1 {
		query := p.db.WithContext(ctx).
			Order("id").
			Where("pipeline_id = ? AND status = ?", pipelineID, status[0])
		if limit > 0 {
			query = query.Limit(limit)
		}
		if err := query.Find(&pipelineRuns).Error; err != nil {
			err = perror.GetBitsError(ctx, err)
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	} else {
		query := p.db.WithContext(ctx).
			Order("id").
			Where("pipeline_id = ? AND status in (?)", pipelineID, status)
		if limit > 0 {
			query = query.Limit(limit)
		}
		if err := query.Find(&pipelineRuns).Error; err != nil {
			err = perror.GetBitsError(ctx, err)
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	}

	logs.CtxInfo(ctx, "GetPipelineRunsByPipelineIDAndStatusWithLimit: pipelineID=%d, limit=%d, status=%v, returned=%d records", pipelineID, limit, status, len(pipelineRuns))
	return pipelineRuns, nil
}

func (p *pipelineRunRepo) GetFirstPipelineRunByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun

	if len(status) == 1 {
		if err := p.db.WithContext(ctx).
			Order("id").
			Where("pipeline_id = ? AND status = ?", pipelineID, status[0]).
			Limit(1).
			First(&pipelineRun).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, nil // 没有找到记录时返回 nil，而不是错误
			}
			err = perror.GetBitsError(ctx, err)
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	} else {
		if err := p.db.WithContext(ctx).
			Order("id").
			Where("pipeline_id = ? AND status in (?)", pipelineID, status).
			Limit(1).
			First(&pipelineRun).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, nil // 没有找到记录时返回 nil，而不是错误
			}
			err = perror.GetBitsError(ctx, err)
			return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	}

	return pipelineRun, nil
}

func (p *pipelineRunRepo) HasPipelineRunsByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (bool, error) {
	var count int64

	if len(status) == 1 {
		if err := p.db.WithContext(ctx).
			Model(&entity.PipelineRun{}).
			Where("pipeline_id = ? AND status = ?", pipelineID, status[0]).
			Limit(1).
			Count(&count).Error; err != nil {
			err = perror.GetBitsError(ctx, err)
			return false, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	} else {
		if err := p.db.WithContext(ctx).
			Model(&entity.PipelineRun{}).
			Where("pipeline_id = ? AND status in (?)", pipelineID, status).
			Limit(1).
			Count(&count).Error; err != nil {
			err = perror.GetBitsError(ctx, err)
			return false, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
		}
	}

	return count > 0, nil
}

func (p *pipelineRunRepo) UpdateBlockingPipelineRunStatus(ctx context.Context, pipelineID uint64, runStatus entity.PipelineRunStatus) error {
	if err := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Where("pipeline_id = ? AND status = ?", pipelineID, entity.PipelineRunStatusBlocking).
		Update("status", runStatus).Error; err != nil {
		return err
	}
	return nil
}

func (p *pipelineRunRepo) UpdatePipelineRunStatus(ctx context.Context, runID uint64, runStatus entity.PipelineRunStatus) error {
	if err := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Where("run_id = ?", runID).
		Update("status", runStatus).Error; err != nil {
		return err
	}

	return nil
}

func (p *pipelineRunRepo) DeletePipelineRunAndTrigger(ctx context.Context, runID uint64) error {
	if err := p.db.WithContext(ctx).
		Where("run_id = ?", runID).
		Delete(&entity.PipelineRun{}).Error; err != nil {
		return err
	}

	return p.pipelineRunTriggerRepo.DeletePipelineRunTrigger(ctx, runID)
}

func (p *pipelineRunRepo) GetPipelineRunWithTrigger(ctx context.Context, runID uint64) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun

	if err := p.db.WithContext(ctx).
		Clauses(dbresolver.Write).
		Where("run_id = ?", runID).
		First(&pipelineRun).Error; err != nil {
		return nil, err
	}

	trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, runID)
	if err != nil {
		return nil, err
	}

	pipelineRun.TriggerInfo = trigger
	return pipelineRun, nil
}

func (p *pipelineRunRepo) GetPipelineRunWithTriggerByEngineRunID(ctx context.Context, engineRunID int64) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun

	if err := p.db.WithContext(ctx).
		Clauses(dbresolver.Write).
		Where("engine_run_id = ?", engineRunID).
		First(&pipelineRun).Error; err != nil {
		return nil, err
	}

	trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, pipelineRun.RunId)
	if err != nil {
		return nil, err
	}

	pipelineRun.TriggerInfo = trigger

	return pipelineRun, nil

}

func (p *pipelineRunRepo) GetPipelineRunsByEngineRunIDs(ctx context.Context, engineRunIDs ...int64) ([]*entity.PipelineRun, error) {
	var pipelineRuns []*entity.PipelineRun

	if err := p.db.WithContext(ctx).
		Clauses(dbresolver.Write).
		Where("engine_run_id IN (?)", engineRunIDs).
		Find(&pipelineRuns).Error; err != nil {
		return nil, err
	}
	return pipelineRuns, nil
}

func (p *pipelineRunRepo) GetPipelineRunByEngineRunID(ctx context.Context, engineRunID int64, withTrigger bool) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun

	if err := p.db.WithContext(ctx).
		Clauses(dbresolver.Write).
		Where("engine_run_id = ?", engineRunID).
		First(&pipelineRun).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, bits_err.PIPELINECOMMON.ErrRecordNotFound
		}
		return nil, err
	}

	if withTrigger {
		trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, pipelineRun.RunId)
		if err != nil {
			return nil, err
		}

		pipelineRun.TriggerInfo = trigger
	}
	return pipelineRun, nil
}

func (p *pipelineRunRepo) GetGitMaxPipelineRunSeqINChange(ctx context.Context, repo, branch, yamlFilename string) (uint64, error) {
	var maxRunSeq uint64
	mrTriggerType := []entity.TriggerType{
		entity.TriggerTypeGitMrPushed,
		entity.TriggerTypeGitMrMerged,
		entity.TriggerTypeGitMrReopened,
		entity.TriggerTypeGitMrOpened,
		entity.TriggerTypeGitMrMerged,
		entity.TriggerTypeGitMrClosed,
		entity.TriggerTypeGitMrUpdated,
	}
	rows, err := p.db.WithContext(ctx).
		Table("pipeline_run").
		Select(" IFNULL(MAX(run_seq),0) AS max ").
		Where(" repo_name = ? AND branch = ? AND yaml_filename = ? AND trigger_type IN (?)", repo, branch, yamlFilename, mrTriggerType).
		Rows()

	if err != nil {
		logs.CtxError(ctx, "query max run seq failed, error: %s", err)
		return 0, err
	}

	defer rows.Close()

	if rows.Next() {
		err = rows.Scan(&maxRunSeq)
		if err != nil {
			logs.CtxError(ctx, "scan to max run seq failed, error: %s", err)
			return 0, nil
		}
	}

	return maxRunSeq, nil
}

func (p *pipelineRunRepo) GetRunSeqByPipelineRunId(ctx context.Context, pipelineRunId uint64) (uint64, error) {
	var runSeq uint64
	if err := p.db.WithContext(ctx).
		Table(constvar.TableName_PipelineRun).
		Where("run_id =?", pipelineRunId).
		Select("run_seq").
		Scan(&runSeq).Error; err != nil {
		return 0, err
	}
	return runSeq, nil
}

func (p *pipelineRunRepo) GetGitMaxPipelineRunSeq(ctx context.Context, repo, branch, tag, yamlFilename string) (uint64, error) {
	var maxRunSeq uint64
	query := p.db.WithContext(ctx).
		Table("pipeline_run").
		Select(" IFNULL(MAX(run_seq),0) AS max ").
		Where("repo_name = ?", repo).
		Where("yaml_filename = ?", yamlFilename)
	if len(branch) > 0 {
		query = query.Where("branch = ?", branch)
	}
	if len(tag) > 0 {
		query = query.Where("tag = ?", tag)
	}
	rows, err := query.Rows()
	if err != nil {
		logs.CtxError(ctx, "query max run seq failed, error: %s", err)
		return 0, err
	}
	if rows != nil {
		defer rows.Close()
	}

	if rows.Next() {
		err = rows.Scan(&maxRunSeq)
		if err != nil {
			if strings.Contains(err.Error(), "converting NULL to uint64 is unsupported") {
				logs.CtxWarn(ctx, "scan to max run seq failed, error: %s", err)
				return 0, nil
			}
			logs.CtxError(ctx, "scan to max run seq failed, error: %s", err)
			return 0, err
		}
	}

	return maxRunSeq, nil
}

func (p *pipelineRunRepo) GetPipelineRunsByIDs(ctx context.Context, runIDs []uint64) ([]*entity.PipelineRun, error) {
	if len(runIDs) == 0 {
		return nil, nil
	}

	var runs []*entity.PipelineRun
	if err := p.db.WithContext(ctx).
		Where("run_id IN (?)", runIDs).
		Find(&runs).Error; err != nil {
		return nil, err
	}
	return runs, nil
}

type ListPipelineRunOpts struct {
	PipelineId  uint64
	RunIDs      []uint64
	TriggeredBy []string
	Statuses    []entity.PipelineRunStatus
	OrderBy     string
	Limit       int32
	Offset      int32
	TabType     platformpb.PipelineRunListTab
	StartedAt   time.Time
	EndedAt     time.Time
	Branch      *string
}

func (p *pipelineRunRepo) GetPipelineRunList(ctx context.Context, opts *ListPipelineRunOpts) ([]*entity.PipelineRun, int64, error) {
	var (
		pipelineRuns []*entity.PipelineRun
		total        int64
	)
	db := p.db.WithContext(ctx)
	if opts != nil {
		if opts.PipelineId > 0 {
			db = db.Where("pipeline_id = ?", opts.PipelineId)
		}
		if len(opts.RunIDs) > 0 {
			db = db.Where("run_id IN (?)", opts.RunIDs)
		}

		if len(opts.TriggeredBy) > 0 {
			db = db.Where("created_by IN (?)", opts.TriggeredBy)
		}
		if len(opts.Statuses) > 0 {
			db = db.Where("status IN (?)", opts.Statuses)
		}
		if !opts.StartedAt.IsZero() && !opts.EndedAt.IsZero() {
			db = db.Where("created_at >= ?", opts.StartedAt)
			db = db.Where("created_at <= ?", opts.EndedAt)
		}

		if branch := gptr.Indirect(opts.Branch); branch != "" {
			// 对应 hagrid/app/triggerrpc/biz/gitevent/service.go:125 buildRunParams
			branch = "%" + branch + "%"
			db.Where(
				p.unscopedDB.WithContext(ctx).Where(
					datatypes.JSONQuery("run_params").Likes(branch, "branch")).
					Or(datatypes.JSONQuery("run_params").Likes(branch, "source_branch")).
					Or(datatypes.JSONQuery("run_params").Likes(branch, "target_branch")))
		}

		db.Model(&entity.PipelineRun{}).Count(&total) // 不区分tab

		// if it is all tab, total should be counted after filters
		// if not, total should be counted just by statuses and pipeline id
		switch opts.TabType {
		case platformpb.PipelineRunListTab_PIPELINE_RUN_LIST_TAB_BLOCKING:
			db = db.Where("status IN (?)", []entity.PipelineRunStatus{entity.PipelineRunStatusBlocking})
		case platformpb.PipelineRunListTab_PIPELINE_RUN_LIST_TAB_RUNNING:
			db = db.Where("status IN (?)", []entity.PipelineRunStatus{entity.PipelineRunStatusRunning})
		}

		if opts.Limit > 0 {
			db = db.Limit(int(opts.Limit))
		}
		if opts.Offset > 0 {
			db = db.Offset(int(opts.Offset))
		}
		if len(opts.OrderBy) > 0 {
			db = db.Order(sql.QuoteSQLOrder(fmt.Sprintf("%s DESC", opts.OrderBy)))
		}

		if opts.PipelineId > 0 && opts.OrderBy == "run_seq" &&
			len(opts.RunIDs) == 0 && len(opts.TriggeredBy) == 0 && len(opts.Statuses) == 0 && opts.StartedAt.IsZero() && opts.EndedAt.IsZero() && (opts.TabType == platformpb.PipelineRunListTab_PIPELINE_RUN_LIST_TAB_ALL || opts.TabType == platformpb.PipelineRunListTab_PIPELINE_RUN_LIST_TAB_UNSPECIFIED) {
			db.Clauses(
				hints.ForceIndex("idx_ppl_id_seq_invisible_v2"),
			)
		}
	}
	if err := db.Find(&pipelineRuns).Error; err != nil {
		logs.CtxError(ctx, err.Error())
		err = perror.GetBitsError(ctx, err)
		return nil, 0, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
	}
	return pipelineRuns, total, nil
}

func (p *pipelineRunRepo) GetLastPipelineRunByTriggerUser(ctx context.Context, pipelineId uint64, triggerUser string) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun
	if err := p.unscopedDB.
		WithContext(ctx).
		Clauses(
			hints.ForceIndex("idx_ppl_id_status_created_by_at_seq_invisible"),
		).
		Where("pipeline_id = ? and created_by = ?", pipelineId, triggerUser).
		Order("run_seq DESC").
		Limit(1).
		Find(&pipelineRun).Error; err != nil {
		return nil, err
	}
	return pipelineRun, nil
}

func (p *pipelineRunRepo) GetLastPipelineRun(ctx context.Context, pipelineID uint64) (*entity.PipelineRun, error) {
	var run *entity.PipelineRun
	if err := p.unscopedDB.WithContext(ctx).
		Order("run_seq DESC").
		Where("pipeline_id = ?", pipelineID).
		Limit(1).
		Find(&run).Error; err != nil {
		return nil, err
	}
	return run, nil
}

func (p *pipelineRunRepo) UpdatePipelineRunStatusByEngineRunID(ctx context.Context, engineRunID int64, runStatus entity.PipelineRunStatus, startedAt, completedAt *time.Time) error {
	if err := p.db.WithContext(ctx).Model(&entity.PipelineRun{}).
		Where("engine_run_id = ?", engineRunID).
		Updates(entity.PipelineRun{Status: runStatus, StartedAt: startedAt, CompletedAt: completedAt}).Error; err != nil {
		return err
	}

	return nil
}

func (p *pipelineRunRepo) GetPipelineRunBySeq(ctx context.Context, pipelineId uint64, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun
	if err := p.db.WithContext(ctx).
		Where("pipeline_id = ? AND run_seq = ?", pipelineId, runSeq).
		First(&pipelineRun).Error; err != nil {
		logs.CtxError(ctx, err.Error())
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, bits_err.PIPELINECOMMON.ErrRecordNotFound
		}
		return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
	}

	if withPipelineRunTrigger {
		trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, pipelineRun.RunId)
		if err != nil {
			return nil, err
		}

		pipelineRun.TriggerInfo = trigger
	}

	return pipelineRun, nil
}

func (p *pipelineRunRepo) GetLatestPipelineRunsByYamlNames(ctx context.Context, repoName, branch, tag string, yamlNames []string) ([]*entity.PipelineRun, error) {
	if len(yamlNames) == 0 {
		return nil, nil
	}
	runs := make([]*entity.PipelineRun, 0)
	for _, yamlName := range yamlNames {
		var run *entity.PipelineRun
		query := p.db.WithContext(ctx).Table(constvar.TableName_PipelineRun).Where("repo_name = ?", repoName)
		if len(branch) > 0 {
			query = query.Where("branch = ?", branch)
		}
		if len(tag) > 0 {
			query = query.Where("tag = ?", tag)
		}
		if err := query.Where("yaml_filename = ?", yamlName).
			Order("run_seq DESC").
			Limit(1).
			Find(&run).Error; err != nil {
			return nil, err
		}
		runs = append(runs, run)
	}
	return runs, nil
}

func (p *pipelineRunRepo) GetGitPipelineRunIdByMaxSeq(ctx context.Context, repo, branch, tag, yamlFilename string) (uint64, error) {
	var pipelineRun *entity.PipelineRun
	query := p.db.WithContext(ctx).Where("repo_name = ? AND yaml_filename = ?", repo, yamlFilename)
	if len(branch) > 0 {
		query = query.Where("branch = ?", branch)
	}
	if len(tag) > 0 {
		query = query.Where("tag =?", tag)
	}
	if err := query.Order("run_seq DESC").
		First(&pipelineRun).Error; err != nil {
		return 0, err
	}
	return pipelineRun.RunId, nil
}

func (p *pipelineRunRepo) GetGitPipelineRunBySeqAndTag(ctx context.Context, repo, tag, yaml string, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun

	if err := p.db.WithContext(ctx).
		Where("repo_name = ? AND tag =? AND yaml_filename = ? AND run_seq = ?", repo, tag, yaml, runSeq).
		First(&pipelineRun).Error; err != nil {
		return nil, err
	}

	if withPipelineRunTrigger {
		trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, pipelineRun.RunId)
		if err != nil {
			return nil, err
		}

		pipelineRun.TriggerInfo = trigger
	}

	return pipelineRun, nil
}

func (p *pipelineRunRepo) GetGitPipelineRunBySeq(ctx context.Context, repo, branch, tag, yamlFilename string, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	var pipelineRun *entity.PipelineRun

	query := p.db.WithContext(ctx).
		Clauses(hints.UseIndex("`idx_repo_branch_file_seq_invisible`")).
		Where("repo_name = ?", repo).
		Where("yaml_filename = ?", yamlFilename).
		Where("run_seq = ?", runSeq)
	if len(branch) > 0 {
		query = query.Where("branch = ?", branch)
	}
	if len(tag) > 0 {
		query = query.Where("tag = ?", tag)
	}
	if err := query.First(&pipelineRun).Error; err != nil {
		return nil, err
	}

	if withPipelineRunTrigger {
		trigger, err := p.pipelineRunTriggerRepo.GetPipelineRunTrigger(ctx, pipelineRun.RunId)
		if err != nil {
			return nil, err
		}

		pipelineRun.TriggerInfo = trigger
	}

	return pipelineRun, nil
}

type SearchGitPipelineRunOpts struct {
	GitRepo      string
	GitBranch    string
	CommitSha    string
	GitTag       string
	YamlFileName string
	// TriggeredBy  string
	TriggeredBy []string
	// Status       entity.PipelineRunStatus
	Statuses     []entity.PipelineRunStatus
	TriggerTypes []entity.TriggerType
	OrderBy      string
	Since        *time.Time
	Until        *time.Time
	Limit        int32
	Offset       int32
}

func (p *pipelineRunRepo) GetPipelineRunsByYamlFile(ctx context.Context, opts *SearchGitPipelineRunOpts) ([]*entity.PipelineRun, int64, error) {
	var (
		pipelineRuns []*entity.PipelineRun
		total        int64
	)
	db := p.db.Debug().WithContext(ctx)
	if opts != nil {
		if opts.GitRepo != "" {
			db = db.Where(fmt.Sprintf("%s.repo_name = ?", constvar.TableName_PipelineRun), opts.GitRepo)
		}
		if opts.GitBranch != "" {
			db = db.Where(fmt.Sprintf("%s.branch = ?", constvar.TableName_PipelineRun), opts.GitBranch)
		}
		if opts.GitTag != "" {
			db = db.Where(fmt.Sprintf("%s.tag = ?", constvar.TableName_PipelineRun), opts.GitTag)
		}
		if opts.YamlFileName != "" {
			db = db.Where(fmt.Sprintf("%s.yaml_filename = ?", constvar.TableName_PipelineRun), opts.YamlFileName)
		}

		if len(opts.TriggeredBy) > 0 {
			db = db.Where(fmt.Sprintf("%s.created_by IN (?)", constvar.TableName_PipelineRun), opts.TriggeredBy)
		}
		if len(opts.Statuses) > 0 {
			if len(opts.Statuses) == 1 {
				db = db.Where(fmt.Sprintf("%s.status = ?", constvar.TableName_PipelineRun), opts.Statuses[0])
			} else {
				db = db.Where(fmt.Sprintf("%s.status IN (?)", constvar.TableName_PipelineRun), opts.Statuses)
			}
		}
		if opts.Since != nil {
			db = db.Where(fmt.Sprintf("%s.created_at >= ?", constvar.TableName_PipelineRun), opts.Since)
		}
		if opts.Until != nil {
			db = db.Where(fmt.Sprintf("%s.created_at <= ?", constvar.TableName_PipelineRun), opts.Until)
		}

		if len(opts.TriggerTypes) > 0 {
			db = db.Where(fmt.Sprintf("%s.trigger_type IN (?)", constvar.TableName_PipelineRun), opts.TriggerTypes)
		}
		if opts.CommitSha != "" {
			joinQuery := fmt.Sprintf(
				"INNER JOIN `%s` ON `%s`.`run_id` = `%s`.`run_id`",
				constvar.TableName_PipelineRunTrigger, constvar.TableName_PipelineRun, constvar.TableName_PipelineRunTrigger,
			)
			db = db.InnerJoins(joinQuery)
			db = db.Where(fmt.Sprintf("%s.commit_sha LIKE ?", constvar.TableName_PipelineRunTrigger), opts.CommitSha+"%")
		}

		db.Model(&entity.PipelineRun{}).Count(&total)

		if opts.Limit > 0 {
			db = db.Limit(int(opts.Limit))
		}
		if opts.Offset > 0 {
			db = db.Offset(int(opts.Offset))
		}
		if len(opts.OrderBy) > 0 {
			db = db.Order(sql.QuoteSQLOrder(fmt.Sprintf("%s.%s DESC", constvar.TableName_PipelineRun, opts.OrderBy)))
		}
	}
	if err := db.Find(&pipelineRuns).Error; err != nil {
		logs.CtxError(ctx, err.Error())
		return nil, 0, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
	}
	return pipelineRuns, total, nil
}

func (p *pipelineRunRepo) UpdatePipelineRunNote(ctx context.Context, runID uint64, note string) error {
	result := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Where("run_id =?", runID).
		Update("note", note)

	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.Errorf("id=<%d> pipeline run not found", runID)
	}

	return nil
}

func (p *pipelineRunRepo) GetPipelineRunMap(ctx context.Context, runIDs []uint64) (map[uint64]*entity.PipelineRun, error) {
	runs, err := p.GetPipelineRunsByIDs(ctx, runIDs)
	if err != nil {
		return nil, err
	}

	runMap := make(map[uint64]*entity.PipelineRun, len(runs))
	for _, r := range runs {
		runMap[r.RunId] = r
	}
	return runMap, nil
}

func (p *pipelineRunRepo) UpdatePipelineRunNoteByEngineRunId(ctx context.Context, engineRunId uint64, note string) error {
	result := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Where("engine_run_id = ?", engineRunId).
		Update("note", note)
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.Errorf("engine_run_id=<%d> pipeline run not found", engineRunId)
	}
	return nil
}

func (p *pipelineRunRepo) GetGitUncompletedPipelineRuns(ctx context.Context, runIDs []uint64) ([]*entity.PipelineRun, error) {
	var runs []*entity.PipelineRun
	inCompleteStatus := []entity.PipelineRunStatus{
		entity.PipelineRunStatusRunning,
		entity.PipelineRunStatusBlocking,
		entity.PipelineRunStatusCancelling,
		entity.PipelineRunStatusQueuing,
		entity.PipelineRunStatusWaiting,
	}
	if err := p.db.WithContext(ctx).
		Where("status IN (?)", inCompleteStatus).
		Where("run_id IN (?)", runIDs).
		Find(&runs).Error; err != nil {
		return nil, err
	}
	return runs, nil
}

// GetLatestPipelineRunsByYamlFile Deprecated
func GetLatestPipelineRunsByYamlFile(ctx context.Context, repoName, branch string, yamlNames []string) ([]*entity.PipelineRun, error) {
	if len(yamlNames) == 0 {
		return nil, nil
	}
	var runs []*entity.PipelineRun
	if err := mysql.DB.WithContext(ctx).
		Where("pipeline_run.repo_name = ?", repoName).
		Where("pipeline_run.branch = ?", branch).
		Where("pipeline_run.yaml_filename IN (?)", yamlNames).
		Joins(`LEFT JOIN
				pipeline_run right_hand_side
			ON
				pipeline_run.repo_name = right_hand_side.repo_name
				AND pipeline_run.branch = right_hand_side.branch
				AND pipeline_run.yaml_filename = right_hand_side.yaml_filename
				AND pipeline_run.created_at < right_hand_side.created_at`).
		Where("right_hand_side.id IS NULL").
		Find(&runs).Error; err != nil {
		return nil, err
	}
	return runs, nil
}

func (p *pipelineRunRepo) GetPipelineRunIDByRunSeq(ctx context.Context, pipelineID, runSeq uint64) (uint64, error) {
	var pipelineRun *entity.PipelineRun
	if err := p.db.WithContext(ctx).
		Where("pipeline_id =? AND run_seq =?", pipelineID, runSeq).
		First(&pipelineRun).
		Select("run_id").Error; err != nil {
		return 0, err
	}
	return pipelineRun.RunId, nil
}

func (p *pipelineRunRepo) GetPipelineRunIDsByPipelineID(ctx context.Context, pipelineID uint64, statuses []entity.PipelineRunStatus) ([]uint64, error) {
	var runIDs []uint64
	if err := p.db.WithContext(ctx).
		Table(constvar.TableName_PipelineRun).
		Where("pipeline_id =?", pipelineID).
		Where("status IN (?)", statuses).
		Select("run_id").
		Scan(&runIDs).Error; err != nil {
		return nil, err
	}
	return runIDs, nil
}

func (p *pipelineRunRepo) GetGitPipelineRunIDsByTag(ctx context.Context, repo, tag, yaml string, statuses []entity.PipelineRunStatus) ([]uint64, error) {
	var runIDs []uint64
	if err := p.db.WithContext(ctx).
		Table(constvar.TableName_PipelineRun).
		Where("repo_name =? AND tag =? AND yaml_filename =? AND status IN (?)", repo, tag, yaml, statuses).
		Select("run_id").
		Scan(&runIDs).Error; err != nil {
		return nil, err
	}
	return runIDs, nil
}

func (p *pipelineRunRepo) GetGitPipelineRunIDs(ctx context.Context, repo, branch, tag, yaml string, statuses []entity.PipelineRunStatus) ([]uint64, error) {
	var runIDs []uint64
	query := p.db.WithContext(ctx).
		Table(constvar.TableName_PipelineRun).
		Where("repo_name = ?", repo).Where("yaml_filename = ?", yaml).Where("status IN (?)", statuses)

	if len(branch) > 0 {
		query = query.Where("branch = ?", branch)
	}
	if len(tag) > 0 {
		query = query.Where("tag = ?", tag)
	}
	if err := query.
		Select("run_id").
		Scan(&runIDs).Error; err != nil {
		return nil, err
	}
	return runIDs, nil
}

func (p *pipelineRunRepo) UpdatePipelineRunStatusByIDs(ctx context.Context, runIDs []uint64, status entity.PipelineRunStatus) error {
	if err := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Where("run_id IN ?", runIDs).
		Update("status", status).Error; err != nil {
		return err
	}
	return nil
}

/*
GetLatestPipelineAndRunIDsByTriggerUser
https://meego.larkoffice.com/bitsdevops/issue/detail/5358135243
我主动找工作台的PM @金雅谊 沟通，通过调整产品确认了一种保证后端性能的数据库友好的查询方式

	最多查询100条流水线运行历史，基于这100条展示对应的流水线
*/
func (p *pipelineRunRepo) GetLatestPipelineAndRunIDsByTriggerUser(ctx context.Context, triggerUser string, limit, offset int) (total int64, res []*model.PipelineRunPair, err error) {
	totalLimit := 100
	subQuery := p.db.WithContext(ctx).Model(&entity.PipelineRun{}).Select("pipeline_id, run_id, created_at").
		Where("created_by = ? AND pipeline_id > 0", triggerUser).
		Limit(totalLimit)

	query := p.unscopedDB.Table("(?) as pr", subQuery).Group("pipeline_id")
	// count
	err = query.Count(&total).Error
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}

	// res
	// 其实应该再查一次来填充created_at，不过产品没这么严谨，还是优化掉吧
	err = query.
		Select("pipeline_id, max(run_id) as run_id, max(created_at) as created_at").
		Order("run_id DESC").
		Limit(limit).
		Offset(offset).
		Find(&res).Error
	if err != nil {
		logs.CtxError(ctx, err.Error())
		return
	}
	return
}

func (p *pipelineRunRepo) GetPipelineRunCountByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (int64, error) {
	var count int64
	query := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Order("id").
		Where("pipeline_id = ?", pipelineID)

	if len(status) == 1 {
		query = query.Where("status = ?", status[0])
	} else {
		query = query.Where("status IN (?)", status)
	}

	if err := query.Count(&count).Error; err != nil {
		logs.CtxError(ctx, err.Error())
		return 0, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
	}
	return count, nil
}

func (p *pipelineRunRepo) GetGitPipelineRunIDSuccess(ctx context.Context, repo, branch, tag, yaml string) (uint64, error) {
	var runID uint64
	query := p.db.WithContext(ctx).
		Table(constvar.TableName_PipelineRun).
		Where("repo_name = ?", repo).Where("yaml_filename = ?", yaml).Where("status IN (?)", entity.PipelineRunStatusSucceeded)

	if len(branch) > 0 {
		query = query.Where("branch = ?", branch)
	}
	if len(tag) > 0 {
		query = query.Where("tag = ?", tag)
	}
	if err := query.
		Order("created_at DESC").
		Select("run_id").
		Scan(&runID).Error; err != nil {
		return 0, err
	}
	return runID, nil
}

func (p *pipelineRunRepo) UpdatePipelineRun(ctx context.Context, runID uint64, pipelineRun *entity.PipelineRun) error {
	updates := &entity.PipelineRun{
		VarAssignmentIds: pipelineRun.VarAssignmentIds,
		RunParams:        pipelineRun.RunParams,
		UpdatedAt:        time.Now(),
		UpdatedBy:        pipelineRun.UpdatedBy,
	}
	if err := p.db.WithContext(ctx).
		Model(&entity.PipelineRun{}).
		Where("run_id = ?", runID).
		Updates(updates).Error; err != nil {
		return err
	}
	return nil
}

func (p *pipelineRunRepo) FilterPipelineNotEnd(ctx context.Context, pipelineIds []uint64, limit int) (filterPipelineIds []uint64, err error) {
	var allStatus []entity.PipelineRunStatus
	for status := range entity.PipelineRunOngoingWithBlockingStatus {
		allStatus = append(allStatus, status)
	}

	err = p.db.WithContext(ctx).Model(&entity.PipelineRun{}).
		Distinct("pipeline_id").
		Where("pipeline_id in (?) and status in (?)", pipelineIds, allStatus).
		Limit(limit).Find(&filterPipelineIds).Error
	if err != nil {
		return
	}
	return
}
