// Code generated by MockGen. DO NOT EDIT.
// Source: repo_pipeline_run.go

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"
	time "time"

	model "code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/model"
	entity "code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	infra "code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	gomock "github.com/golang/mock/gomock"
)

// MockIPipelineRunRepo is a mock of IPipelineRunRepo interface.
type MockIPipelineRunRepo struct {
	ctrl     *gomock.Controller
	recorder *MockIPipelineRunRepoMockRecorder
}

// MockIPipelineRunRepoMockRecorder is the mock recorder for MockIPipelineRunRepo.
type MockIPipelineRunRepoMockRecorder struct {
	mock *MockIPipelineRunRepo
}

// NewMockIPipelineRunRepo creates a new mock instance.
func NewMockIPipelineRunRepo(ctrl *gomock.Controller) *MockIPipelineRunRepo {
	mock := &MockIPipelineRunRepo{ctrl: ctrl}
	mock.recorder = &MockIPipelineRunRepoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIPipelineRunRepo) EXPECT() *MockIPipelineRunRepoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIPipelineRunRepo) Create(ctx context.Context, value *entity.SimplePipelineRun) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, value)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIPipelineRunRepoMockRecorder) Create(ctx, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIPipelineRunRepo)(nil).Create), ctx, value)
}

// DeleteById mocks base method.
func (m *MockIPipelineRunRepo) DeleteById(ctx context.Context, id uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteById", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteById indicates an expected call of DeleteById.
func (mr *MockIPipelineRunRepoMockRecorder) DeleteById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteById", reflect.TypeOf((*MockIPipelineRunRepo)(nil).DeleteById), ctx, id)
}

// FilterPipelineNotEnd mocks base method.
func (m *MockIPipelineRunRepo) FilterPipelineNotEnd(ctx context.Context, pipelineIds []uint64, limit int) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterPipelineNotEnd", ctx, pipelineIds, limit)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterPipelineNotEnd indicates an expected call of FilterPipelineNotEnd.
func (mr *MockIPipelineRunRepoMockRecorder) FilterPipelineNotEnd(ctx, pipelineIds, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterPipelineNotEnd", reflect.TypeOf((*MockIPipelineRunRepo)(nil).FilterPipelineNotEnd), ctx, pipelineIds, limit)
}

// FindLastByPipelineIdIn mocks base method.
func (m *MockIPipelineRunRepo) FindLastByPipelineIdIn(ctx context.Context, pipelineIds []uint64) ([]*entity.SimplePipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindLastByPipelineIdIn", ctx, pipelineIds)
	ret0, _ := ret[0].([]*entity.SimplePipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindLastByPipelineIdIn indicates an expected call of FindLastByPipelineIdIn.
func (mr *MockIPipelineRunRepoMockRecorder) FindLastByPipelineIdIn(ctx, pipelineIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindLastByPipelineIdIn", reflect.TypeOf((*MockIPipelineRunRepo)(nil).FindLastByPipelineIdIn), ctx, pipelineIds)
}

// GetGitMaxPipelineRunSeq mocks base method.
func (m *MockIPipelineRunRepo) GetGitMaxPipelineRunSeq(ctx context.Context, repo, branch, tag, yaml string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitMaxPipelineRunSeq", ctx, repo, branch, tag, yaml)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitMaxPipelineRunSeq indicates an expected call of GetGitMaxPipelineRunSeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitMaxPipelineRunSeq(ctx, repo, branch, tag, yaml interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitMaxPipelineRunSeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitMaxPipelineRunSeq), ctx, repo, branch, tag, yaml)
}

// GetGitMaxPipelineRunSeqINChange mocks base method.
func (m *MockIPipelineRunRepo) GetGitMaxPipelineRunSeqINChange(ctx context.Context, repo, branch, yaml string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitMaxPipelineRunSeqINChange", ctx, repo, branch, yaml)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitMaxPipelineRunSeqINChange indicates an expected call of GetGitMaxPipelineRunSeqINChange.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitMaxPipelineRunSeqINChange(ctx, repo, branch, yaml interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitMaxPipelineRunSeqINChange", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitMaxPipelineRunSeqINChange), ctx, repo, branch, yaml)
}

// GetGitPipelineRunBySeq mocks base method.
func (m *MockIPipelineRunRepo) GetGitPipelineRunBySeq(ctx context.Context, repo, branch, tag, yaml string, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitPipelineRunBySeq", ctx, repo, branch, tag, yaml, runSeq, withPipelineRunTrigger)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitPipelineRunBySeq indicates an expected call of GetGitPipelineRunBySeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitPipelineRunBySeq(ctx, repo, branch, tag, yaml, runSeq, withPipelineRunTrigger interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitPipelineRunBySeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitPipelineRunBySeq), ctx, repo, branch, tag, yaml, runSeq, withPipelineRunTrigger)
}

// GetGitPipelineRunBySeqAndTag mocks base method.
func (m *MockIPipelineRunRepo) GetGitPipelineRunBySeqAndTag(ctx context.Context, repo, tag, yaml string, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitPipelineRunBySeqAndTag", ctx, repo, tag, yaml, runSeq, withPipelineRunTrigger)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitPipelineRunBySeqAndTag indicates an expected call of GetGitPipelineRunBySeqAndTag.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitPipelineRunBySeqAndTag(ctx, repo, tag, yaml, runSeq, withPipelineRunTrigger interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitPipelineRunBySeqAndTag", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitPipelineRunBySeqAndTag), ctx, repo, tag, yaml, runSeq, withPipelineRunTrigger)
}

// GetGitPipelineRunIDSuccess mocks base method.
func (m *MockIPipelineRunRepo) GetGitPipelineRunIDSuccess(ctx context.Context, repo, branch, tag, yaml string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitPipelineRunIDSuccess", ctx, repo, branch, tag, yaml)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitPipelineRunIDSuccess indicates an expected call of GetGitPipelineRunIDSuccess.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitPipelineRunIDSuccess(ctx, repo, branch, tag, yaml interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitPipelineRunIDSuccess", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitPipelineRunIDSuccess), ctx, repo, branch, tag, yaml)
}

// GetGitPipelineRunIDs mocks base method.
func (m *MockIPipelineRunRepo) GetGitPipelineRunIDs(ctx context.Context, repo, branch, tag, yaml string, statuses []entity.PipelineRunStatus) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitPipelineRunIDs", ctx, repo, branch, tag, yaml, statuses)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitPipelineRunIDs indicates an expected call of GetGitPipelineRunIDs.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitPipelineRunIDs(ctx, repo, branch, tag, yaml, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitPipelineRunIDs", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitPipelineRunIDs), ctx, repo, branch, tag, yaml, statuses)
}

// GetGitPipelineRunIDsByTag mocks base method.
func (m *MockIPipelineRunRepo) GetGitPipelineRunIDsByTag(ctx context.Context, repo, tag, yaml string, statuses []entity.PipelineRunStatus) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitPipelineRunIDsByTag", ctx, repo, tag, yaml, statuses)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitPipelineRunIDsByTag indicates an expected call of GetGitPipelineRunIDsByTag.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitPipelineRunIDsByTag(ctx, repo, tag, yaml, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitPipelineRunIDsByTag", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitPipelineRunIDsByTag), ctx, repo, tag, yaml, statuses)
}

// GetGitPipelineRunIdByMaxSeq mocks base method.
func (m *MockIPipelineRunRepo) GetGitPipelineRunIdByMaxSeq(ctx context.Context, repo, branch, tag, yamlFilename string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitPipelineRunIdByMaxSeq", ctx, repo, branch, tag, yamlFilename)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitPipelineRunIdByMaxSeq indicates an expected call of GetGitPipelineRunIdByMaxSeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitPipelineRunIdByMaxSeq(ctx, repo, branch, tag, yamlFilename interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitPipelineRunIdByMaxSeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitPipelineRunIdByMaxSeq), ctx, repo, branch, tag, yamlFilename)
}

// GetGitUncompletedPipelineRuns mocks base method.
func (m *MockIPipelineRunRepo) GetGitUncompletedPipelineRuns(ctx context.Context, runIDs []uint64) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGitUncompletedPipelineRuns", ctx, runIDs)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGitUncompletedPipelineRuns indicates an expected call of GetGitUncompletedPipelineRuns.
func (mr *MockIPipelineRunRepoMockRecorder) GetGitUncompletedPipelineRuns(ctx, runIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGitUncompletedPipelineRuns", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetGitUncompletedPipelineRuns), ctx, runIDs)
}

// GetInvisibleMaxPipelineRunSeq mocks base method.
func (m *MockIPipelineRunRepo) GetInvisibleMaxPipelineRunSeq(ctx context.Context, pipelineID uint64) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvisibleMaxPipelineRunSeq", ctx, pipelineID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvisibleMaxPipelineRunSeq indicates an expected call of GetInvisibleMaxPipelineRunSeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetInvisibleMaxPipelineRunSeq(ctx, pipelineID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvisibleMaxPipelineRunSeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetInvisibleMaxPipelineRunSeq), ctx, pipelineID)
}

// GetLastPipelineRun mocks base method.
func (m *MockIPipelineRunRepo) GetLastPipelineRun(ctx context.Context, pipelineID uint64) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastPipelineRun", ctx, pipelineID)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastPipelineRun indicates an expected call of GetLastPipelineRun.
func (mr *MockIPipelineRunRepoMockRecorder) GetLastPipelineRun(ctx, pipelineID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastPipelineRun", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetLastPipelineRun), ctx, pipelineID)
}

// GetLastPipelineRunByTriggerUser mocks base method.
func (m *MockIPipelineRunRepo) GetLastPipelineRunByTriggerUser(ctx context.Context, pipelineId uint64, triggerUser string) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastPipelineRunByTriggerUser", ctx, pipelineId, triggerUser)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastPipelineRunByTriggerUser indicates an expected call of GetLastPipelineRunByTriggerUser.
func (mr *MockIPipelineRunRepoMockRecorder) GetLastPipelineRunByTriggerUser(ctx, pipelineId, triggerUser interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastPipelineRunByTriggerUser", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetLastPipelineRunByTriggerUser), ctx, pipelineId, triggerUser)
}

// GetLastRunIdEventBlocked mocks base method.
func (m *MockIPipelineRunRepo) GetLastRunIdEventBlocked(ctx context.Context, pipelineId uint64) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLastRunIdEventBlocked", ctx, pipelineId)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLastRunIdEventBlocked indicates an expected call of GetLastRunIdEventBlocked.
func (mr *MockIPipelineRunRepoMockRecorder) GetLastRunIdEventBlocked(ctx, pipelineId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLastRunIdEventBlocked", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetLastRunIdEventBlocked), ctx, pipelineId)
}

// GetLatestPipelineAndRunIDsByTriggerUser mocks base method.
func (m *MockIPipelineRunRepo) GetLatestPipelineAndRunIDsByTriggerUser(ctx context.Context, triggerUser string, limit, offset int) (int64, []*model.PipelineRunPair, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestPipelineAndRunIDsByTriggerUser", ctx, triggerUser, limit, offset)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*model.PipelineRunPair)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetLatestPipelineAndRunIDsByTriggerUser indicates an expected call of GetLatestPipelineAndRunIDsByTriggerUser.
func (mr *MockIPipelineRunRepoMockRecorder) GetLatestPipelineAndRunIDsByTriggerUser(ctx, triggerUser, limit, offset interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestPipelineAndRunIDsByTriggerUser", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetLatestPipelineAndRunIDsByTriggerUser), ctx, triggerUser, limit, offset)
}

// GetLatestPipelineRunsByYamlNames mocks base method.
func (m *MockIPipelineRunRepo) GetLatestPipelineRunsByYamlNames(ctx context.Context, repo, branch, tag string, yamls []string) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestPipelineRunsByYamlNames", ctx, repo, branch, tag, yamls)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestPipelineRunsByYamlNames indicates an expected call of GetLatestPipelineRunsByYamlNames.
func (mr *MockIPipelineRunRepoMockRecorder) GetLatestPipelineRunsByYamlNames(ctx, repo, branch, tag, yamls interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestPipelineRunsByYamlNames", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetLatestPipelineRunsByYamlNames), ctx, repo, branch, tag, yamls)
}

// GetMaxPipelineRunSeq mocks base method.
func (m *MockIPipelineRunRepo) GetMaxPipelineRunSeq(ctx context.Context, pipelineID uint64) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxPipelineRunSeq", ctx, pipelineID)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxPipelineRunSeq indicates an expected call of GetMaxPipelineRunSeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetMaxPipelineRunSeq(ctx, pipelineID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxPipelineRunSeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetMaxPipelineRunSeq), ctx, pipelineID)
}

// GetPipelineRun mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRun(ctx context.Context, runID uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRun", ctx, runID, withPipelineRunTrigger)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRun indicates an expected call of GetPipelineRun.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRun(ctx, runID, withPipelineRunTrigger interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRun", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRun), ctx, runID, withPipelineRunTrigger)
}

// GetPipelineRunByEngineRunID mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunByEngineRunID(ctx context.Context, engineRunID int64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunByEngineRunID", ctx, engineRunID, withPipelineRunTrigger)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunByEngineRunID indicates an expected call of GetPipelineRunByEngineRunID.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunByEngineRunID(ctx, engineRunID, withPipelineRunTrigger interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunByEngineRunID", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunByEngineRunID), ctx, engineRunID, withPipelineRunTrigger)
}

// GetPipelineRunBySeq mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunBySeq(ctx context.Context, pipelineId, runSeq uint64, withPipelineRunTrigger bool) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunBySeq", ctx, pipelineId, runSeq, withPipelineRunTrigger)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunBySeq indicates an expected call of GetPipelineRunBySeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunBySeq(ctx, pipelineId, runSeq, withPipelineRunTrigger interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunBySeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunBySeq), ctx, pipelineId, runSeq, withPipelineRunTrigger)
}

// GetPipelineRunCountByPipelineIDAndStatus mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunCountByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, pipelineID}
	for _, a := range status {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPipelineRunCountByPipelineIDAndStatus", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunCountByPipelineIDAndStatus indicates an expected call of GetPipelineRunCountByPipelineIDAndStatus.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunCountByPipelineIDAndStatus(ctx, pipelineID interface{}, status ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, pipelineID}, status...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunCountByPipelineIDAndStatus", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunCountByPipelineIDAndStatus), varargs...)
}

// GetPipelineRunFromReadOnlyAndContainNotVisible mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunFromReadOnlyAndContainNotVisible(ctx context.Context, pipelineRunID uint64) (entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunFromReadOnlyAndContainNotVisible", ctx, pipelineRunID)
	ret0, _ := ret[0].(entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunFromReadOnlyAndContainNotVisible indicates an expected call of GetPipelineRunFromReadOnlyAndContainNotVisible.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunFromReadOnlyAndContainNotVisible(ctx, pipelineRunID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunFromReadOnlyAndContainNotVisible", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunFromReadOnlyAndContainNotVisible), ctx, pipelineRunID)
}

// GetPipelineRunIDByRunSeq mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunIDByRunSeq(ctx context.Context, pipelineID, runSeq uint64) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunIDByRunSeq", ctx, pipelineID, runSeq)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunIDByRunSeq indicates an expected call of GetPipelineRunIDByRunSeq.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunIDByRunSeq(ctx, pipelineID, runSeq interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunIDByRunSeq", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunIDByRunSeq), ctx, pipelineID, runSeq)
}

// GetPipelineRunIDsByPipelineID mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunIDsByPipelineID(ctx context.Context, pipelineID uint64, statuses []entity.PipelineRunStatus) ([]uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunIDsByPipelineID", ctx, pipelineID, statuses)
	ret0, _ := ret[0].([]uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunIDsByPipelineID indicates an expected call of GetPipelineRunIDsByPipelineID.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunIDsByPipelineID(ctx, pipelineID, statuses interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunIDsByPipelineID", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunIDsByPipelineID), ctx, pipelineID, statuses)
}

// GetPipelineRunList mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunList(ctx context.Context, opts *infra.ListPipelineRunOpts) ([]*entity.PipelineRun, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunList", ctx, opts)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPipelineRunList indicates an expected call of GetPipelineRunList.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunList(ctx, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunList", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunList), ctx, opts)
}

// GetPipelineRunMap mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunMap(ctx context.Context, runIDs []uint64) (map[uint64]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunMap", ctx, runIDs)
	ret0, _ := ret[0].(map[uint64]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunMap indicates an expected call of GetPipelineRunMap.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunMap(ctx, runIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunMap", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunMap), ctx, runIDs)
}

// GetPipelineRunsByEngineRunIDs mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunsByEngineRunIDs(ctx context.Context, engineRunIDs ...int64) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range engineRunIDs {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPipelineRunsByEngineRunIDs", varargs...)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunsByEngineRunIDs indicates an expected call of GetPipelineRunsByEngineRunIDs.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunsByEngineRunIDs(ctx interface{}, engineRunIDs ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, engineRunIDs...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunsByEngineRunIDs", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunsByEngineRunIDs), varargs...)
}

// GetPipelineRunsByIDs mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunsByIDs(ctx context.Context, runIDs []uint64) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunsByIDs", ctx, runIDs)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunsByIDs indicates an expected call of GetPipelineRunsByIDs.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunsByIDs(ctx, runIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunsByIDs", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunsByIDs), ctx, runIDs)
}

// GetPipelineRunsByIdempotentToken mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunsByIdempotentToken(ctx context.Context, pipelineID uint64, token string) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunsByIdempotentToken", ctx, pipelineID, token)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunsByIdempotentToken indicates an expected call of GetPipelineRunsByIdempotentToken.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunsByIdempotentToken(ctx, pipelineID, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunsByIdempotentToken", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunsByIdempotentToken), ctx, pipelineID, token)
}

// GetPipelineRunsByPipelineIDAndStatus mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunsByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, pipelineID}
	for _, a := range status {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPipelineRunsByPipelineIDAndStatus", varargs...)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPipelineRunsByPipelineIDAndStatus indicates an expected call of GetPipelineRunsByPipelineIDAndStatus.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunsByPipelineIDAndStatus(ctx, pipelineID interface{}, status ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, pipelineID}, status...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunsByPipelineIDAndStatus", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunsByPipelineIDAndStatus), varargs...)
}

// GetFirstPipelineRunByPipelineIDAndStatus mocks base method.
func (m *MockIPipelineRunRepo) GetFirstPipelineRunByPipelineIDAndStatus(ctx context.Context, pipelineID uint64, status ...entity.PipelineRunStatus) (*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, pipelineID}
	for _, a := range status {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFirstPipelineRunByPipelineIDAndStatus", varargs...)
	ret0, _ := ret[0].(*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstPipelineRunByPipelineIDAndStatus indicates an expected call of GetFirstPipelineRunByPipelineIDAndStatus.
func (mr *MockIPipelineRunRepoMockRecorder) GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineID interface{}, status ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, pipelineID}, status...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstPipelineRunByPipelineIDAndStatus", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetFirstPipelineRunByPipelineIDAndStatus), varargs...)
}

// GetPipelineRunsByYamlFile mocks base method.
func (m *MockIPipelineRunRepo) GetPipelineRunsByYamlFile(ctx context.Context, opts *infra.SearchGitPipelineRunOpts) ([]*entity.PipelineRun, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPipelineRunsByYamlFile", ctx, opts)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetPipelineRunsByYamlFile indicates an expected call of GetPipelineRunsByYamlFile.
func (mr *MockIPipelineRunRepoMockRecorder) GetPipelineRunsByYamlFile(ctx, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPipelineRunsByYamlFile", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetPipelineRunsByYamlFile), ctx, opts)
}

// GetRecentGitPipelineRunList mocks base method.
func (m *MockIPipelineRunRepo) GetRecentGitPipelineRunList(ctx context.Context, opts *infra.SearchRecentGitPipelineRunOpts) ([]*entity.PipelineRun, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecentGitPipelineRunList", ctx, opts)
	ret0, _ := ret[0].([]*entity.PipelineRun)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRecentGitPipelineRunList indicates an expected call of GetRecentGitPipelineRunList.
func (mr *MockIPipelineRunRepoMockRecorder) GetRecentGitPipelineRunList(ctx, opts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentGitPipelineRunList", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetRecentGitPipelineRunList), ctx, opts)
}

// GetRunSeqByPipelineRunId mocks base method.
func (m *MockIPipelineRunRepo) GetRunSeqByPipelineRunId(ctx context.Context, pipelineRunId uint64) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRunSeqByPipelineRunId", ctx, pipelineRunId)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRunSeqByPipelineRunId indicates an expected call of GetRunSeqByPipelineRunId.
func (mr *MockIPipelineRunRepoMockRecorder) GetRunSeqByPipelineRunId(ctx, pipelineRunId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRunSeqByPipelineRunId", reflect.TypeOf((*MockIPipelineRunRepo)(nil).GetRunSeqByPipelineRunId), ctx, pipelineRunId)
}

// UpdateBlockingPipelineRunStatus mocks base method.
func (m *MockIPipelineRunRepo) UpdateBlockingPipelineRunStatus(ctx context.Context, pipelineID uint64, runStatus entity.PipelineRunStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateBlockingPipelineRunStatus", ctx, pipelineID, runStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateBlockingPipelineRunStatus indicates an expected call of UpdateBlockingPipelineRunStatus.
func (mr *MockIPipelineRunRepoMockRecorder) UpdateBlockingPipelineRunStatus(ctx, pipelineID, runStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBlockingPipelineRunStatus", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdateBlockingPipelineRunStatus), ctx, pipelineID, runStatus)
}

// UpdatePipelineRun mocks base method.
func (m *MockIPipelineRunRepo) UpdatePipelineRun(ctx context.Context, runID uint64, pipelineRun *entity.PipelineRun) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineRun", ctx, runID, pipelineRun)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePipelineRun indicates an expected call of UpdatePipelineRun.
func (mr *MockIPipelineRunRepoMockRecorder) UpdatePipelineRun(ctx, runID, pipelineRun interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineRun", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdatePipelineRun), ctx, runID, pipelineRun)
}

// UpdatePipelineRunNote mocks base method.
func (m *MockIPipelineRunRepo) UpdatePipelineRunNote(ctx context.Context, runID uint64, note string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineRunNote", ctx, runID, note)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePipelineRunNote indicates an expected call of UpdatePipelineRunNote.
func (mr *MockIPipelineRunRepoMockRecorder) UpdatePipelineRunNote(ctx, runID, note interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineRunNote", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdatePipelineRunNote), ctx, runID, note)
}

// UpdatePipelineRunNoteByEngineRunId mocks base method.
func (m *MockIPipelineRunRepo) UpdatePipelineRunNoteByEngineRunId(ctx context.Context, engineRunId uint64, note string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineRunNoteByEngineRunId", ctx, engineRunId, note)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePipelineRunNoteByEngineRunId indicates an expected call of UpdatePipelineRunNoteByEngineRunId.
func (mr *MockIPipelineRunRepoMockRecorder) UpdatePipelineRunNoteByEngineRunId(ctx, engineRunId, note interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineRunNoteByEngineRunId", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdatePipelineRunNoteByEngineRunId), ctx, engineRunId, note)
}

// UpdatePipelineRunStatus mocks base method.
func (m *MockIPipelineRunRepo) UpdatePipelineRunStatus(ctx context.Context, runID uint64, runStatus entity.PipelineRunStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineRunStatus", ctx, runID, runStatus)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePipelineRunStatus indicates an expected call of UpdatePipelineRunStatus.
func (mr *MockIPipelineRunRepoMockRecorder) UpdatePipelineRunStatus(ctx, runID, runStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineRunStatus", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdatePipelineRunStatus), ctx, runID, runStatus)
}

// UpdatePipelineRunStatusByEngineRunID mocks base method.
func (m *MockIPipelineRunRepo) UpdatePipelineRunStatusByEngineRunID(ctx context.Context, engineRunID int64, runStatus entity.PipelineRunStatus, startedAt, completedAt *time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineRunStatusByEngineRunID", ctx, engineRunID, runStatus, startedAt, completedAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePipelineRunStatusByEngineRunID indicates an expected call of UpdatePipelineRunStatusByEngineRunID.
func (mr *MockIPipelineRunRepoMockRecorder) UpdatePipelineRunStatusByEngineRunID(ctx, engineRunID, runStatus, startedAt, completedAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineRunStatusByEngineRunID", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdatePipelineRunStatusByEngineRunID), ctx, engineRunID, runStatus, startedAt, completedAt)
}

// UpdatePipelineRunStatusByIDs mocks base method.
func (m *MockIPipelineRunRepo) UpdatePipelineRunStatusByIDs(ctx context.Context, runIDs []uint64, status entity.PipelineRunStatus) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePipelineRunStatusByIDs", ctx, runIDs, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePipelineRunStatusByIDs indicates an expected call of UpdatePipelineRunStatusByIDs.
func (mr *MockIPipelineRunRepoMockRecorder) UpdatePipelineRunStatusByIDs(ctx, runIDs, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePipelineRunStatusByIDs", reflect.TypeOf((*MockIPipelineRunRepo)(nil).UpdatePipelineRunStatusByIDs), ctx, runIDs, status)
}
