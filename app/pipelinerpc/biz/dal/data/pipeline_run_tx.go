// Package data is to define all the db manipulation with transaction
// all the db manipulation without transaction should be put into `infra`
package data

import (
	"context"
	"time"

	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
)

func TxUpdatePipelineRunStatus(ctx context.Context, runID uint64, runStatus entity.PipelineRunStatus, tx *gorm.DB) error {
	if err := tx.Model(&entity.PipelineRun{}).
		Where("run_id = ?", runID).
		Update("status", runStatus).Error; err != nil {
		return err
	}

	return nil
}

func TxDeletePipelineRun(ctx context.Context, runID uint64, tx *gorm.DB) error {
	if err := tx.Where("run_id = ?", runID).
		Delete(&entity.PipelineRun{}).Error; err != nil {
		return err
	}

	return nil
}

func TxDeletePipelineRunTrigger(ctx context.Context, runID uint64, tx *gorm.DB) error {
	if err := tx.Where("run_id = ?", runID).
		Delete(&entity.PipelineRunTrigger{}).Error; err != nil {
		return err
	}

	return nil
}

func TxCreatePipelineRunTrigger(ctx context.Context, runTrigger *entity.PipelineRunTrigger, tx *gorm.DB) error {
	res := tx.Create(runTrigger)
	if res.Error != nil {
		return res.Error
	}

	return nil
}

func TxCreatePipelineRun(ctx context.Context, pipelineRun *entity.PipelineRun, tx *gorm.DB) error {
	res := tx.Create(pipelineRun)
	if res.Error != nil {
		return res.Error
	}

	return nil
}

func TxUpdateEngineRunID(ctx context.Context, runID uint64, engineRunID int64, tx *gorm.DB) error {
	if err := tx.Model(&entity.PipelineRun{}).
		Where("run_id = ?", runID).
		Update("engine_run_id", engineRunID).Error; err != nil {
		return err
	}

	return nil
}

func TxCreateSensitiveVariables(ctx context.Context, pipelineRunSensitiveVariables *entity.PipelineRunSensitiveVariables, tx *gorm.DB) error {
	if err := tx.Create(pipelineRunSensitiveVariables).Error; err != nil {
		return err
	}
	return nil
}

func TxUpdatePipelineRunTrigger(ctx context.Context, runID uint64, triggerType entity.TriggerType, tx *gorm.DB) error {
	updates := &entity.PipelineRunTrigger{
		TriggerType: triggerType,
		CreatedAt:   time.Now(),
	}
	if err := tx.Model(&entity.PipelineRunTrigger{}).
		Where("run_id = ?", runID).
		Updates(updates).Error; err != nil {
		return err
	}

	return nil
}

func TxUpdatePipelineRun(ctx context.Context, runID uint64, pipelineRun *entity.PipelineRun, tx *gorm.DB) error {
	updates := &entity.PipelineRun{
		VarAssignmentIds: pipelineRun.VarAssignmentIds,
		RunParams:        pipelineRun.RunParams,
		TriggerType:      pipelineRun.TriggerType,
		UpdatedAt:        time.Now(),
		UpdatedBy:        pipelineRun.UpdatedBy,
	}
	if err := tx.Model(&entity.PipelineRun{}).
		Where("run_id = ?", runID).
		Updates(updates).Error; err != nil {
		return err
	}

	return nil
}
