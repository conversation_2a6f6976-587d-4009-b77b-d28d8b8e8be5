package auto_step

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"gotest.tools/v3/assert"
)

func TestAddCacheStepsForNodeJob(t *testing.T) {
	t.Run("normal", func(t *testing.T) {
		jobDSL := &dslpb.Job{
			RunsOn: &dslpb.RunnerSpec{
				Image: "hub.byted.org/codebase/ci_nvm",
			},
			Steps: []*dslpb.Step{{
				Id:   "action_test",
				Name: "action test",
				Uses: "actions/test",
			}},
		}
		AddCacheStepsForNodeJob(context.Background(), jobDSL)
		assert.Equal(t, len(jobDSL.Steps), 2)
		assert.Equal(t, jobDSL.Env[npmCacheEnv], npmCacheDir)
		assert.Equal(t, jobDSL.Env[yarnCacheEnv], yarnCacheDir)
		assert.Equal(t, jobDSL.Env[pnpmCacheEnv], pnpmCacheDir)
		assert.Equal(t, jobDSL.Steps[0].Uses, constvar.CacheAction)
	})
	t.Run("normal", func(t *testing.T) {
		jobDSL := &dslpb.Job{
			RunsOn: &dslpb.RunnerSpec{
				Image: "hub.byted.org/codebase/ci_nvm",
			},
			Steps: []*dslpb.Step{{
				Id:   "action_test",
				Name: "action test",
				Uses: "actions/test",
			}},
		}
		AddCacheStepsForNodeJob(context.Background(), jobDSL)
		assert.Equal(t, len(jobDSL.Steps), 2)
	})
	t.Run("not node", func(t *testing.T) {
		jobDSL := &dslpb.Job{
			RunsOn: &dslpb.RunnerSpec{
				Image: "hub.byted.org/codebase/debian",
			},
			Steps: []*dslpb.Step{{
				Id:   "action_test",
				Name: "action test",
				Uses: "actions/test",
			}},
		}
		AddCacheStepsForNodeJob(context.Background(), jobDSL)
		assert.Equal(t, len(jobDSL.Steps), 1)
	})
	t.Run("already exists", func(t *testing.T) {
		jobDSL := &dslpb.Job{
			RunsOn: &dslpb.RunnerSpec{
				Image: "hub.byted.org/codebase/ci_nvm",
			},
			Env: map[string]string{
				"NPM_CONFIG_CACHE": "/boot",
			},
			Steps: []*dslpb.Step{{
				Id:   "action_test",
				Name: "action test",
				Uses: "actions/test",
			}},
		}
		AddCacheStepsForNodeJob(context.Background(), jobDSL)
		assert.Equal(t, len(jobDSL.Steps), 1)
	})
}
