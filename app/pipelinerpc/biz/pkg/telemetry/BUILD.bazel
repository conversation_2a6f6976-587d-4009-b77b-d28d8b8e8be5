load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "telemetry",
    srcs = [
        "git_event.go",
        "git_pipeline.go",
        "utils.go",
        "webhook_callback.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/telemetry",
    visibility = ["//visibility:public"],
    deps = [
        "//pkg/codebase/model",
        "//pkg/metrics",
        "@org_byted_code_bytedtrace_interface_go//:interface-go",
        "@org_byted_code_codebase_sdk//:sdk",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)
