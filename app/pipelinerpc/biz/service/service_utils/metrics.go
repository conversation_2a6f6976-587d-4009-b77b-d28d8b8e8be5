package service_utils

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/pkg/leafboat"
	"code.byted.org/devinfra/hagrid/pkg/metrics"
	enginesdk "code.byted.org/pipeline/go-sdk"
)

const (
	JobCompleteEmitKey = "pipeline.job.complete"
	JobRunTimeEmitKey  = "pipeline.job.run_time"

	StepCompleteEmitKey = "pipeline.step.complete"
	StepRunTimeEmitKey  = "pipeline.step.run_time"
)

func EmitJobMetrics(ctx context.Context, job *enginesdk.Job, pipelineRun *entity.PipelineRun, status entity.JobRunStatus) {
	if !status.IsFinalStatus() {
		return
	}
	tags := map[string]string{
		"repo.name":      pipelineRun.RepoName,
		"job.label":      job.AgentLabel,
		"job.status":     status.String(),
		"job.conclusion": leafboat.ConclusionToString(job.Status),
	}
	metrics.EmitTimer(ctx, JobCompleteEmitKey, 1, tags)
	if job.StartedAt != nil && job.CompletedAt != nil {
		metrics.EmitTimer(ctx, JobRunTimeEmitKey, job.CompletedAt.Sub(*job.StartedAt).Milliseconds(), tags)
	}
}

func EmitStepMetrics(ctx context.Context, step *enginesdk.Step, pipelineRun *entity.PipelineRun, status entity.JobRunStatus) {
	if !leafboat.IsCompleted(step.Status) {
		return
	}
	tags := map[string]string{
		"repo.name":       pipelineRun.RepoName,
		"step.conclusion": leafboat.ConclusionToString(step.Status),
	}
	if step.Action != nil {
		tags["step.action"] = step.Action.UID
	}
	metrics.EmitTimer(ctx, StepCompleteEmitKey, 1, tags)
	if step.StartedAt != nil && step.CompletedAt != nil {
		metrics.EmitTimer(ctx, StepRunTimeEmitKey, step.CompletedAt.Sub(*step.StartedAt).Milliseconds(), tags)
	}
}
