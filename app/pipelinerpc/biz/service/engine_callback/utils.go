package engine_callback

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/metrics"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/pipeline/perror"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/pipeline/go-sdk"
)

func GetPipelineRunDurationMetrics(ctx context.Context, RunId uint64, enginePpl *sdk.Pipeline, pipelineRun *entity.PipelineRun) (resp *platformpb.GetPipelineRunDurationMetricsResponse, err error) {
	if pipelineRun.EngineRunId <= 0 {
		logs.CtxError(ctx, "pipelineRun.EngineRunId is not valid")
		return nil, perror.GetInvalidArgHerror("pipelineRun.EngineRunId is not valid")
	}
	enginePipelineRun := enginePpl

	err = pipelineRun.SyncEnginePipelineRun(enginePipelineRun)
	if err != nil {
		logs.CtxError(ctx, "illegal pipeline run status from engine %v", enginePipelineRun.Status)
		return nil, err
	}

	if !pipelineRun.Status.IsFinalStatus() {
		return nil, perror.GetInvalidArgHerror("pipelineRun is not finished")
	}
	completedJobs, existRerunJobs := filterCompletedAndExistRerunJobs(enginePipelineRun)

	// engineJobRunId -> reRunList
	rerunJobsById := make(map[int64][]*sdk.JobRerun)
	if len(existRerunJobs) > 0 {
		for _, existRerunJob := range existRerunJobs {
			rerunJobsResp, err := engine.Client.GetJobRerunsByJobID(ctx, existRerunJob.ID, pipelineRun.ControlPanel)
			if err != nil {
				logs.CtxError(ctx, "failed to get job reruns, error: %s", err.Error())
				return nil, err
			}
			metrics.ReviseJobReruns(rerunJobsResp.Reruns, existRerunJob)
			rerunJobsById[existRerunJob.ID] = rerunJobsResp.Reruns
		}
	}

	// 从 job run中获取等待耗时
	jobRuns, err := infra.JobRunRepo().GetJobRunsByPipelineRunIDs(ctx, []uint64{RunId})
	if err != nil {
		logs.CtxError(ctx, "failed to get job run list, error:%s", err.Error())
		return nil, err
	}
	// key:engineJobId
	latestJobs := make(map[int64]*entity.JobRun)
	for _, item := range jobRuns {
		latestJobs[item.EngineJobID] = item
	}

	// calculate job list
	jobRunTotalDurations := make([]*platformpb.JobRunTotalDuration, 0)
	jobRunDurationsForCalculate := make([]*metrics.JobRunDurationForCalculate, 0)

	for _, job := range completedJobs {
		latestJob := latestJobs[job.ID]
		revisedWaitingTimes, err := metrics.FilterValidWaitingTimes(job, rerunJobsById[job.ID], latestJob.GetWaitingTimes())
		if err != nil {
			return nil, err
		}
		latestJob.SetWaitingTimes(revisedWaitingTimes)

		jobRunTotalDuration := metrics.GenerateJobRunTotalDuration(job, rerunJobsById[job.ID], latestJob.GetWaitingTimes())
		jobRunTotalDurations = append(jobRunTotalDurations, jobRunTotalDuration)
		for _, rerun := range rerunJobsById[job.ID] {
			jobRunDurationsForCalculate = append(jobRunDurationsForCalculate, &metrics.JobRunDurationForCalculate{
				QueuedAt:    rerun.QueuedAt,
				StartedAt:   rerun.StartedAt,
				CompletedAt: rerun.CompletedAt,
			})
		}
		jobRunDurationsForCalculate = append(jobRunDurationsForCalculate, &metrics.JobRunDurationForCalculate{
			QueuedAt:     job.QueuedAt,
			StartedAt:    job.StartedAt,
			CompletedAt:  job.CompletedAt,
			WaitingTimes: latestJob.GetWaitingTimes(),
		})
	}

	var pipelineRunDuration *platformpb.PipelineRunDuration

	// 计算流水线耗时
	if len(existRerunJobs) > 0 || pipelineRun.CompletedAt == nil || pipelineRun.StartedAt == nil {
		// job存在重试，或者 pipeline run 数据不完整（异常情况下的兜底）
		pipelineRunDuration = metrics.GeneratePipelineRunDurationWithRetry(jobRunDurationsForCalculate)
	} else {
		totalSec := uint64(pipelineRun.CompletedAt.Sub(*pipelineRun.StartedAt).Seconds())
		pipelineRunDuration = metrics.GeneratePipelineRunDuration(jobRunDurationsForCalculate, totalSec)
	}

	// 计算job相关信息
	pipelineRunDuration.CompletedJobCount = uint64(len(completedJobs))
	pipelineRunDuration.CompletedJobStatusCounts = metrics.ClassifyJobStatus(completedJobs)
	pipelineRunDuration.RerunCount = metrics.CountRerun(completedJobs)
	pipelineRunDuration.RerunJobCounts = metrics.CalculateRerunJobRanking(completedJobs)

	jobTotalTimes, jobQueuingTimes, jobWaitingTimes := metrics.CalculateJobTimeRanking(jobRunTotalDurations)
	pipelineRunDuration.JobTotalTimes = jobTotalTimes
	pipelineRunDuration.JobQueuingTimes = jobQueuingTimes
	pipelineRunDuration.JobWaitingTimes = jobWaitingTimes

	return &platformpb.GetPipelineRunDurationMetricsResponse{
		PipelineId:           pipelineRun.PipelineId,
		OrcaId:               uint64(pipelineRun.EngineRunId),
		RunId:                RunId,
		PipelineRunDuration:  pipelineRunDuration,
		JobRunTotalDurations: jobRunTotalDurations,
	}, nil
}

func filterCompletedAndExistRerunJobs(enginePipelineRun *sdk.Pipeline) ([]*sdk.Job, []*sdk.Job) {
	// filter completed jobs
	completedJobs := make([]*sdk.Job, 0)
	// 获取存在重试的jobs. filter engineJobRun which tried > 1
	existRerunJobs := make([]*sdk.Job, 0)
	for _, stage := range enginePipelineRun.Stages {
		for _, job := range stage.Jobs {
			if job.CompletedAt == nil || job.QueuedAt == nil || job.StartedAt == nil {
				// job 非正常执行，可能处于未执行、取消等情况，不计入统计
				continue
			}
			completedJobs = append(completedJobs, job)
			if job.Tried > 1 {
				existRerunJobs = append(existRerunJobs, job)
			}
		}
	}
	return completedJobs, existRerunJobs
}
