package pipeline_run

import (
	"context"
	"fmt"
	stringSdk "strings"
	"time"

	"code.byted.org/gopkg/lang/strings"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitex/byted/kitexutil"
	"code.byted.org/lang/gg/gslice"
	enginesdk "code.byted.org/pipeline/go-sdk"
	json "github.com/bytedance/sonic"
	"github.com/google/uuid"
	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/structpb"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/data"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/model"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/internal/pipeline/pentity"
	"code.byted.org/devinfra/hagrid/internal/pipeline/perror"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	"code.byted.org/devinfra/hagrid/pkg/pipeline_event/operator_record_event"
	"code.byted.org/devinfra/hagrid/pkg/pvariable"
)

const PipelineDriverPSM = "toutiao.devops.pipeline_driver"

const (
	varKeyContext = "context"
	varKeyBuild   = "build"
	varKeyJWT     = "__jwt__"
)

func (p *pipelineRunService) checkRunParams(ctx context.Context, varAssigns []*varstorepb.VarAssignment, runParams *structpb.Struct) (bool, error) {
	caller, _ := kitexutil.GetCaller(ctx)
	// 驱动器完全不受限制
	if caller == PipelineDriverPSM {
		return true, nil
	}
	allowedKeys := make([]string, 0)
	for _, assign := range varAssigns {
		if stringSdk.HasPrefix(assign.GetName(), constvar.SysPrefix) {
			allowedKeys = append(allowedKeys, pvariable.FullNameToLastName(assign.GetName()))
		}
	}
	for key := range runParams.AsMap() {
		if !gslice.Contains(allowedKeys, pvariable.FullNameToLastName(key)) {
			logs.CtxError(ctx, "key: %s in runParams must be in system variables", key)
			return false, errors.New("key in runParams must be in system variables")
		}
	}
	return true, nil
}

func (p *pipelineRunService) GenerateRunContext(ctx context.Context, req *servicepb.RunPipelineRequest) (*RunContext, error) {
	// 因为目前重新运行和触发器会注入build_ctx，限制暂时取消
	// pass, err := p.checkRunParams(ctx, ListAssignmentResp.GetFlattenValues(), req.GetRunParams())
	// if !pass || err != nil {
	//	return nil, err
	// }

	triggerType := entity.TriggerTypeFromPB(req.TriggerType)
	// https://meego.larkoffice.com/bitsdevops/story/detail/********** 中支持触发器流水线重试，并且认为属于不需要同步触发器状态
	// if triggerType == entity.TriggerTypeDriverAtom && req.ParentRunId == 0 {
	// 	return nil, bits_err.PIPELINECOMMON.ErrInvalidInput
	// }

	runParams, err := utils.ConvertPBStructToMap(req.GetRunParams())
	if err != nil {
		return nil, err
	}

	pipelineTags, err := infra.PipelineRepo().GetPipelineTagByType(ctx, req.PipelineId, entity.PipelineTagTypePsm)
	if err != nil {
		return nil, err
	} else if len(pipelineTags) > 0 && !strings.IsEmpty(pipelineTags[0].Tag) {
		runParams["psm"] = pipelineTags[0].Tag
	}

	switch triggerType {
	case entity.TriggerTypeAuto:
		runParams["type"] = "auto trigger"
		runParams["type_cn"] = "自动触发"
		runParams["build_reason"] = fmt.Sprintf("%s 自动触发", req.Username)
	case entity.TriggerTypeManual:
		runParams["type"] = "handle trigger"
		runParams["type_cn"] = "手动触发"
		runParams["build_reason"] = fmt.Sprintf("%s 手动触发", req.Username)
	case entity.TriggerTypeApi:
		runParams["type"] = "api trigger"
		runParams["type_cn"] = "api触发"
		runParams["build_reason"] = fmt.Sprintf("%s api触发", req.Username)
	case entity.TriggerTypeRollbacked:
		runParams["type"] = "rollback trigger"
		runParams["type_cn"] = "回滚触发"
		runParams["build_reason"] = fmt.Sprintf("%s 回滚触发", req.Username)
	case entity.TriggerTypeSchedule:
		runParams["type"] = "triggered by cron event"
		runParams["type_cn"] = "由定时任务触发"
		runParams["build_reason"] = "由定时任务触发"
	case entity.TriggerTypeDriverAtom:
		runParams["type"] = "triggered by driver atom"
		runParams["type_cn"] = "驱动器触发"
		runParams["build_reason"] = "驱动器触发"
	case entity.TriggerTypeOnlyCreate:
		runParams["type"] = "pending trigger"
		runParams["type_cn"] = "待触发"
		runParams["build_reason"] = "待触发"
	case entity.TriggerTypeRerun:
		runParams["type"] = "rerun"
		runParams["type_cn"] = "重新运行"
		runParams["build_reason"] = "重新运行"
	default: // Git 触发
		runParams["type"] = "triggered by git event"
		runParams["type_cn"] = "由git事件触发"
		// if unmarshal run_params failed when trigger type is git, ignore build reason for now
		structBytes, err := protojson.Marshal(req.RunParams)
		if err != nil {
			logs.CtxError(ctx, "failed to marshal pb struct to bytes: %v", err)
			runParams["build_reason"] = fmt.Sprintf("%s 触发", req.Username) // 兜底
		} else {
			var gitRunParams model.GitTriggerRunParam
			err = json.Unmarshal(structBytes, &gitRunParams)
			if err != nil {
				logs.CtxError(ctx, "failed to unmarshal to git run params: %v", err)
				runParams["build_reason"] = fmt.Sprintf("%s 触发", req.Username)
			} else {
				if triggerType == entity.TriggerTypeGitPushed {
					runParams["build_reason"] = fmt.Sprintf("%s 仓库 push %s 触发", gitRunParams.RepoName, gitRunParams.Branch)
				} else if triggerType == entity.TriggerTypeGitMrMerged {
					runParams["build_reason"] = fmt.Sprintf("%s 仓库 由 %s分支合入%s分支触发", gitRunParams.RepoName, gitRunParams.SourceBranch, gitRunParams.TargetBranch)
				}
			}
		}

	}

	pipeline, err := infra.PipelineRepo().GetPipelineWithOrca(ctx, req.PipelineId, req.PipelineVersion)
	if err != nil {
		return nil, perror.GetBitsError(ctx, err)
	}
	if len(pipeline.Stages) == 0 {
		logs.CtxWarn(ctx, "pipeline %d cannot be run because orca is empty", req.PipelineId)
		return nil, bits_err.PIPELINE.ErrRunPipelineEmptyStages
	}
	InjectJobNameToJobInput(pipeline.Stages)

	pipelineVersion := pipeline.PipelineVersion
	if req.PipelineVersion != 0 {
		pipelineVersion = req.PipelineVersion
	}

	return &RunContext{
		PipelineID:      req.PipelineId,
		PipelineVersion: pipelineVersion,
		Username:        req.Username,
		CustomVars:      req.CustomVars,
		AssignmentIds:   req.AssignmentIds,
		TriggerType:     triggerType,
		RunParams:       runParams,
		ParentRunID:     req.ParentRunId,
		Pipeline:        pipeline,
		BlockingRunList: make([]*entity.PipelineRun, 0),
		OngoingRunList:  make([]*entity.PipelineRun, 0),
		Notifications:   req.Notifications,
		Note:            req.Note,
	}, nil
}

/*
InjectJobNameToJobInput
为了让jobName支持变量，并且orca层不方便支持，因此复用job.inputs结构体，将I18N拆解注入为内部字段
*/
func InjectJobNameToJobInput(stages []*dslpb.Stage) {
	for _, stage := range stages {
		for _, job := range stage.Jobs {
			// 不想全量job数据都加上
			goAhead := utils.ContainVars(job.Name.Value)
			for _, str := range job.Name.Texts {
				goAhead = goAhead || utils.ContainVars(str)
			}
			if !goAhead {
				continue
			}

			// 注入jobName到inputs中，通过orca引擎自动进行变量替换
			if job.Inputs == nil {
				job.Inputs = &structpb.Struct{
					Fields: make(map[string]*structpb.Value),
				}
			}

			if job.Name.Value != "" && job.Name.Lang != "" {
				key := fmt.Sprintf(constvar.JobInputsKeyJobNameFmt, job.Name.Lang)
				job.Inputs.Fields[key] = &structpb.Value{Kind: &structpb.Value_StringValue{StringValue: job.Name.Value}}
			}
			for lang, value := range job.Name.Texts {
				key := fmt.Sprintf(constvar.JobInputsKeyJobNameFmt, lang)
				job.Inputs.Fields[key] = &structpb.Value{Kind: &structpb.Value_StringValue{StringValue: value}}
			}
		}
	}
}

func (p *pipelineRunService) AddChildRun(ctx context.Context, parentRunID uint64, childRun *entity.PipelineRun) error {
	if parentRunID == 0 || childRun == nil {
		return nil
	}

	parentRun, err := infra.PipelineRunRepo().GetPipelineRun(ctx, parentRunID, false)
	if err != nil {
		return err
	}

	err = infra.PipelineRunRelationRepo().CreateChildPipelineRun(ctx, parentRun, childRun)
	if err != nil {
		logs.CtxError(ctx, "failed to update child run id %+v of pipeline run %d, error %s", childRun.RunId, parentRun.RunId, err)
		return err
	}
	return nil
}

func (p *pipelineRunService) GetConcurrencyBudget(ctx context.Context, runCtx *RunContext) (int, error) {
	if runCtx.Pipeline.Concurrency == -1 {
		return -1, nil
	}

	ongoingRuns, blockingRunCount, err := p.getPipelineConcurrency(ctx, runCtx.PipelineID, runCtx)
	if err != nil {
		return 0, err
	}
	runCtx.OngoingRunList = ongoingRuns
	runCtx.BlockingRunCount = blockingRunCount
	runCtx.OngoingRunCount = int64(len(ongoingRuns))

	if int64(runCtx.Pipeline.Concurrency) > runCtx.OngoingRunCount {
		return int(int64(runCtx.Pipeline.Concurrency) - runCtx.OngoingRunCount), nil
	}
	return 0, nil
}

func (p *pipelineRunService) getPipelineConcurrency(ctx context.Context, pipelineID uint64, runCtx *RunContext) ([]*entity.PipelineRun, int64, error) {
	// 根据 pipelineId 获取数据中存在的 pipeline Run 信息
	blockingPipelineCount, err := infra.PipelineRunRepo().GetPipelineRunCountByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusBlocking)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline run list, error:%s", err.Error())
		return nil, 0, err
	}
	ongoingRealRuns := make([]*entity.PipelineRun, 0)
	ongoingPipelineRuns, err := infra.PipelineRunRepo().GetPipelineRunsByPipelineIDAndStatus(ctx, pipelineID, entity.GetPipelineRunOngoingStatus()...)
	if err != nil {
		logs.CtxWarn(ctx, "failed to get pipeline run list, error:%s", err.Error())
		return nil, 0, err
	}

	engineRuns := make([]*enginesdk.Pipeline, 0)
	if len(ongoingPipelineRuns) > 0 {
		engineRuns, _ = engine.Client.BatchGetOrcaPipelineRunStatus(ctx, ongoingPipelineRuns)
	}
	engineRunsMap := gslice.ToMap(engineRuns, func(t *enginesdk.Pipeline) (int64, *enginesdk.Pipeline) {
		return t.ID, t
	})

	for _, pipelineRun := range ongoingPipelineRuns {
		engineRun, ok := engineRunsMap[pipelineRun.EngineRunId]
		if !ok || pipelineRun.EngineRunId == 0 {
			ongoingRealRuns = append(ongoingRealRuns, pipelineRun)
			continue
		}
		engineRunStatus, err := entity.GetPipelineRunStatusFromSDK(engineRun.Status)
		if err != nil {
			return nil, 0, err
		}
		pipelineRun.Status = engineRunStatus
		if engineRunStatus.IsOngoingStatus() {
			ongoingRealRuns = append(ongoingRealRuns, pipelineRun)
		}
	}
	gslice.SortBy(ongoingRealRuns, func(run1 *entity.PipelineRun, run2 *entity.PipelineRun) bool {
		return run1.RunSeq > run2.RunSeq
	})
	return ongoingRealRuns, blockingPipelineCount, nil
}

func (p *pipelineRunService) RunPipeline(ctx context.Context, runCtx *RunContext) (*platformpb.PipelineRun, error) {
	var err error
	runCtx.PipelineRun, err = p.createPipelineRun(ctx, runCtx)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "create pipeline run with pipeline run id %d and run seq %d and pipeline run %s", runCtx.PipelineRun.RunId, runCtx.PipelineRun.RunSeq, utils.JSONText(runCtx.PipelineRun))

	// call engine to try to run
	err = p.tryToRun(ctx, runCtx)
	if err != nil {
		return nil, err
	}

	// 发送操作记录事件
	p.pipelineEventService.SyncSendRecordEvent(ctx, operator_record_event.OperateEvent{
		EventType:   operator_record_event.EventType_Pipeline,
		PipelineId:  runCtx.PipelineRun.PipelineId,
		Operator:    runCtx.Username,
		OperateType: operator_record_event.OperateType_Pipeline_Run,
		Params: &operator_record_event.OperateEventParams{
			SpaceId:       runCtx.Pipeline.SpaceID,
			PipelineRunNo: runCtx.PipelineRun.RunSeq,
			SceneType:     runCtx.Pipeline.SceneType,
		},
	})

	return p.getPipelineRunPBFromRunContext(ctx, runCtx)
}

func (p *pipelineRunService) RunPipelineLite(ctx context.Context, runCtx *RunContext) (*platformpb.LitePipelineRun, error) {
	var err error
	runCtx.PipelineRun, err = p.createPipelineRun(ctx, runCtx)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "create pipeline run with pipeline run id %d and run seq %d and pipeline run %s", runCtx.PipelineRun.RunId, runCtx.PipelineRun.RunSeq, utils.JSONText(runCtx.PipelineRun))

	// call engine to try to run
	err = p.tryToRun(ctx, runCtx)
	if err != nil {
		return nil, err
	}

	// 发送操作记录事件
	p.pipelineEventService.SyncSendRecordEvent(ctx, operator_record_event.OperateEvent{
		EventType:   operator_record_event.EventType_Pipeline,
		PipelineId:  runCtx.PipelineRun.PipelineId,
		Operator:    runCtx.Username,
		OperateType: operator_record_event.OperateType_Pipeline_Run,
		Params: &operator_record_event.OperateEventParams{
			SpaceId:       runCtx.Pipeline.SpaceID,
			PipelineRunNo: runCtx.PipelineRun.RunSeq,
			SceneType:     runCtx.Pipeline.SceneType,
		},
	})

	return &platformpb.LitePipelineRun{
		PipelineId: runCtx.PipelineRun.PipelineId,
		RunId:      runCtx.PipelineRun.RunId,
		RunSeq:     runCtx.PipelineRun.RunSeq,
	}, nil
}

func (p *pipelineRunService) tryToRun(ctx context.Context, runCtx *RunContext) (err error) {
	lockKey := utils.GetPipelineRunConcurrencyLockKey(runCtx.PipelineID)
	lockVal := time.Now().String()
	lock, err := redis.TryLock(ctx, lockKey, lockVal, 40, 20*time.Second, 100*time.Millisecond)
	if err != nil {
		logs.CtxError(ctx, "fail to get pipeline run init lock, key=%v,err=%v", lockKey, err)
		return bits_err.PIPELINECOMMON.ErrRedisError
	}
	if !lock {
		// 多次重试后还是获取不到锁，说明争抢很多或者上一个锁还在执行中，等待重试
		logs.CtxWarn(ctx, "fail to get pipeline run init lock, key=%v", lockKey)
		return bits_err.PIPELINE.ErrRunPipelineGetLockFailed
	}

	defer func() {
		if lock {
			innerErr := redis.TryUnLock(ctx, lockKey, lockVal, 6, 500*time.Millisecond)
			if innerErr != nil {
				logs.CtxError(ctx, "fail to release pipeline run init lock, key=%s, val=%s, err=%v", lockKey, lockVal, innerErr)
			} else {
				logs.CtxInfo(ctx, "pipeline run init lock released, key=%s, val=%s", lockKey, lockVal)
			}
		}
		if err != nil {
			logs.CtxError(ctx, "try to run pipeline failed, error: %s", err)
			if runCtx.PipelineRun.Status == entity.PipelineRunStatusQueuing {
				logs.CtxInfo(ctx, "try to run pipeline failed, deletePipelineRunAndTrigger")
				p.deletePipelineRunAndTrigger(ctx, runCtx)
			}
		}
	}()

	logs.CtxInfo(ctx, "try to run with pipeline run id %d and run seq %d and pipeline run %s", runCtx.PipelineRun.RunId, runCtx.PipelineRun.RunSeq, utils.JSONText(runCtx.PipelineRun))
	budget, err := p.GetConcurrencyBudget(ctx, runCtx)
	if err != nil {
		logs.CtxError(ctx, "failed to get concurrency budget, error: %s", err)
		return err
	}

	if budget != 0 {
		// change run status to running and run, transaction
		err = p.Run(ctx, runCtx)
		if err != nil {
			logs.CtxError(ctx, "run engine transaction failed, pipeline run status is %d, error: %s", runCtx.PipelineRun.Status, err)
			return err
		}
	} else {
		// new run first or not
		if runCtx.Pipeline.NewRunFirst {
			err = p.matchNewRunFirstBudget(ctx, runCtx)
			if err != nil {
				return err
			}
			err = p.Run(ctx, runCtx)
			if err != nil {
				logs.CtxError(ctx, "run engine transaction failed, error: %s", err)
				return err
			}
		} else {
			// new run into blocking
			err = infra.PipelineRunRepo().UpdatePipelineRunStatus(ctx, runCtx.PipelineRun.RunId, entity.PipelineRunStatusBlocking)
			if err != nil {
				return err
			}
			runCtx.PipelineRun.Status = entity.PipelineRunStatusBlocking
			p.pipelineEventService.CreatePipelineRunStatusEvent(ctx, nil, runCtx.PipelineRun, dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_BLOCKING)
		}
	}
	return nil
}

func (p *pipelineRunService) Run(ctx context.Context, runCtx *RunContext) error {
	err := mysql.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := data.TxUpdatePipelineRunStatus(ctx, runCtx.PipelineRun.RunId, entity.PipelineRunStatusRunning, tx)
		if err != nil {
			return err
		}

		// update pipeline last run at
		err = data.TxUpdatePipelineLastRun(ctx, runCtx.PipelineRun.PipelineId, runCtx.PipelineRun.RunId, time.Now(), tx)
		if err != nil {
			logs.CtxError(ctx, "failed to update pipeline run time, error:%s", err.Error())
			return err
		}

		enginePipelineRun, err := p.engineRun(ctx, runCtx)
		if err != nil {
			logs.CtxError(ctx, "engineRun failed, error: %s", err)
			return perror.GetBitsError(ctx, err)
		}
		if enginePipelineRun.ID == 0 {
			logs.CtxError(ctx, "engine run id cannot be 0, something is wrong")
			return errors.New("engine run id cannot be 0, something is wrong")
		}
		logs.CtxInfo(ctx, "pipeline run id %d, run seq %d, engine run id %d", runCtx.PipelineRun.RunId, runCtx.PipelineRun.RunSeq, enginePipelineRun.ID)
		// update engine_run_id
		err = data.TxUpdateEngineRunID(ctx, runCtx.PipelineRun.RunId, enginePipelineRun.ID, tx)
		if err != nil {
			logs.CtxError(ctx, "failed to update engine run id, error: %s", err)
			return err
		}

		sensitiveVariables := p.GetSensitiveVariablesFromEngineRunVariables(enginePipelineRun.Variables, HasJwtCitedInPipeline(ctx, runCtx.Pipeline.Stages))
		if len(sensitiveVariables) > 0 {
			var jsonSensitiveVariables []byte
			jsonSensitiveVariables, err = json.Marshal(gslice.Uniq(sensitiveVariables))
			if err != nil {
				return err
			}
			err = data.TxCreateSensitiveVariables(ctx, &entity.PipelineRunSensitiveVariables{
				RunID:              runCtx.PipelineRun.RunId,
				SensitiveVariables: jsonSensitiveVariables,
			}, tx)
			if err != nil {
				logs.CtxError(ctx, "failed to create sensitive variables, error: %s", err)
				return err
			}
		}

		jobRuns, err := p.buildJobRuns(ctx, runCtx, enginePipelineRun)
		if err != nil {
			return err
		}

		err = data.TxCreateJobRuns(ctx, jobRuns, tx)
		if err != nil {
			logs.CtxError(ctx, "failed to create job run, error:%s", err)
			return err
		}
		if err = data.TxUpdatePipelineScheduledDeleteLatestCalculateTime(ctx, runCtx.PipelineID, tx); err != nil {
			logs.CtxError(ctx, "failed to update pipeline_schedule_delete lastest_calculate_time, error: %s", err)
			return err
		}
		runCtx.PipelineRun.Status = entity.PipelineRunStatusRunning
		runCtx.PipelineRun.EngineRunId = enginePipelineRun.ID
		return nil
	})

	if err != nil {
		return err
	}

	p.pipelineEventService.CreatePipelineRunStatusEvent(ctxutil.GetAsyncContext(ctx), nil, runCtx.PipelineRun, dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_QUEUING)
	return nil
}

func (p *pipelineRunService) engineRun(ctx context.Context, runCtx *RunContext) (*enginesdk.Pipeline, error) {
	space, err := p.spaceService.GetPipelineSpace(ctx, runCtx.Pipeline.SpaceID)
	if err != nil {
		logs.CtxError(ctx, "failed to get space info by id %d, error: %v", runCtx.Pipeline.SpaceID, err)
		return nil, err
	}

	varAssigns, err := p.pipelineVarService.GetVariable(ctx, runCtx.PipelineRun.VarAssignmentIds)
	if err != nil {
		return nil, err
	}

	if cloudBuildCompileExtraField, err := p.GetCloudBuildCompileExtraField(ctx); err == nil {
		runCtx.CompileExtraField = &model.CompileExtraField{CloudBuildCompileExtraField: cloudBuildCompileExtraField}
	}

	res, err := engine.Client.CreateOrcaPipeline(ctx, runCtx.Pipeline, runCtx.Username, runCtx.PipelineRun, space, runCtx.CompileExtraField, varAssigns)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "created orca pipeline is %s", utils.JSONText(res))
	return res, nil
}

func (p *pipelineRunService) buildJobRuns(ctx context.Context, runCtx *RunContext, enginePipelineRun *enginesdk.Pipeline) ([]*entity.JobRun, error) {
	// Get pipeline run orca version
	version, err := infra.PipelineRepo().GetPipelineVersion(ctx, runCtx.PipelineRun.PipelineId, runCtx.PipelineRun.PipelineVersion)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline run version, error:%s", err.Error())
		return nil, err
	}

	pipelineOrca := &entity.PipelineOrca{}
	err = json.Unmarshal(version.Orca, pipelineOrca)
	if err != nil {
		logs.CtxError(ctx, "failed to unmarshal pipeline, error:%s", err.Error())
		return nil, err
	}

	jobMap := make(map[string]*dslpb.Job)
	for _, stage := range pipelineOrca.Stages {
		for _, job := range stage.Jobs {
			jobMap[stage.Id+job.Id] = job
		}
	}

	now := time.Now()
	jobRuns := make([]*entity.JobRun, 0, len(jobMap))
	for _, stage := range enginePipelineRun.Stages {
		for _, job := range stage.Jobs {
			jobPB := jobMap[stage.UID+job.UID]
			if jobPB == nil {
				logs.CtxError(ctx, "engine job not in pb: %+v", job)
				continue
			}
			notifications, err := json.Marshal(jobPB.Notifications)
			if err != nil {
				logs.CtxError(ctx, "failed to marshal notifications, error:%s", err.Error())
				return nil, err
			}
			logs.CtxInfo(ctx, "engine pipeline run is %s, pipeline run is %s", utils.JSONText(enginePipelineRun), utils.JSONText(runCtx.PipelineRun))
			jobRun := &entity.JobRun{
				JobID:         jobPB.Id,
				StageID:       stage.UID,
				NameEN:        jobPB.Name.Texts[constvar.AcceptLanguage_EN],
				NameZH:        jobPB.Name.Texts[constvar.AcceptLanguage_ZH],
				PipelineRunID: runCtx.PipelineRun.RunId,
				EngineJobID:   job.ID,
				Notifications: notifications,
				Status:        entity.GetJobRunStatusFromSDK(job.Status),
				Manual:        jobPB.Manual,
				CreatedAt:     now,
				ControlPanel:  runCtx.PipelineRun.ControlPanel,
			}
			jobRuns = append(jobRuns, jobRun)
		}
	}
	return jobRuns, nil
}

func (p *pipelineRunService) createPipelineRun(ctx context.Context, runCtx *RunContext) (*entity.PipelineRun, error) {
	// redis lock
	lockKey := utils.GetPipelineRunInitLockKey(runCtx.PipelineID)
	lockVal := time.Now().String()
	lock, err := redis.TryLock(ctx, lockKey, lockVal, 3, 10*time.Second, 500*time.Millisecond)
	if err != nil {
		logs.CtxError(ctx, "fail to get pipeline run init lock, key=%v,err=%v", lockKey, err)
		return nil, bits_err.PIPELINECOMMON.ErrRedisError.AddOrPass(ctx, err)
	}
	if !lock {
		// 多次重试后还是获取不到锁，说明争抢很多或者上一个锁还在执行中，等待重试
		logs.CtxWarn(ctx, "fail to get pipeline run init lock, key=%v", lockKey)
		return nil, bits_err.PIPELINE.ErrRunPipelineGetLockFailed.AddOrPass(ctx, err)
	}

	defer func() {
		if lock {
			err = redis.TryUnLock(ctx, lockKey, lockVal, 3, 500*time.Millisecond)
			if err != nil {
				logs.CtxError(ctx, "fail to release pipeline run init lock, key=%v,err=%v", lockKey, err)
			} else {
				logs.CtxInfo(ctx, "pipeline run init lock released, key=%v", lockKey)
			}
		}
	}()

	runID, err := utils.GenPipelineRunID(ctx)
	if err != nil {
		logs.CtxError(ctx, "failed to get id from id generator, error:%v", err)
		return nil, bits_err.PIPELINECOMMON.ErrUndefined.AddOrPass(ctx, err)
	}
	// query the max run seq of current pipeline
	nextRunSeq, err := p.getNextPipelineRunSeq(ctx, runCtx)
	if err != nil {
		return nil, err
	}

	pplRunTrigger, err := p.buildPipelineRunTrigger(ctx, runCtx, runID)
	if err != nil {
		return nil, err
	}

	runParams, err := json.Marshal(runCtx.RunParams)
	if err != nil {
		return nil, err
	}

	if len(runCtx.Pipeline.Notifications) == 0 {
		notificationPBs, err := p.pipelineNotificationService.GetByInstance(ctx, entity.InstanceTypePipeline, runCtx.PipelineID)
		if err != nil {
			logs.CtxError(ctx, "failed to get pipeline notifications, error:%s", err.Error())
			return nil, err
		}
		runCtx.Pipeline.Notifications, err = json.Marshal(notificationPBs)
		if err != nil {
			logs.CtxError(ctx, "failed to marshal pipeline notifications, error:%s", err.Error())
			return nil, err
		}
	}

	controlPanel := runCtx.Pipeline.ControlPanel
	if controlPanel == pentity.ControlPanelCN && tcc.IsStageStray(ctx, runCtx.Username, runCtx.Pipeline.SpaceID, runCtx.PipelineID) {
		controlPanel = pentity.ControlPanelCNSTAGE
	}

	pplRun := &entity.PipelineRun{
		RunId:           runID,
		RunSeq:          nextRunSeq,
		Status:          entity.PipelineRunStatusQueuing,
		PipelineId:      runCtx.PipelineID,
		PipelineVersion: runCtx.PipelineVersion,
		TriggerType:     runCtx.TriggerType,
		IdempotentToken: uuid.NewString(), // TODO: if token is passed by req, replace it
		CreatedBy:       runCtx.Username,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
		Notifications:   runCtx.Pipeline.Notifications,
		TriggerInfo:     pplRunTrigger,
		RunParams:       runParams,
		ControlPanel:    controlPanel,
		Note:            runCtx.Note,
	}
	// create assignment
	assignmentIds, err := p.createAssignment(ctx, runCtx, pplRun)
	if err != nil {
		logs.CtxError(ctx, "failed to create assignment, error:%v", err)
		return nil, err
	}
	logs.CtxInfo(ctx, "created assignment ids: %v", assignmentIds)
	jsonAssignIds, err := json.Marshal(operatorx.IfThen(len(assignmentIds) > 0, assignmentIds, []uint64{}))
	if err != nil {
		return nil, err
	}
	pplRun.VarAssignmentIds = jsonAssignIds
	// create notifications of pipelineRun
	err = p.createNotifications(ctx, runCtx, pplRun.RunId)
	if err != nil {
		return nil, err
	}
	// mysql transaction to create a new pipeline run, with transaction
	err = mysql.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		logs.CtxInfo(ctx, "starting to create pipeline run and trigger")
		err = data.TxCreatePipelineRunTrigger(ctx, pplRunTrigger, tx)
		if err != nil {
			logs.CtxError(ctx, "failed to create pipeline run trigger, error:%s", err.Error())
			return err
		}
		err = data.TxCreatePipelineRun(ctx, pplRun, tx)
		if err != nil {
			logs.CtxError(ctx, "failed to create pipeline run, error:%s", err.Error())
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return pplRun, nil
}

func (p *pipelineRunService) getNextPipelineRunSeq(ctx context.Context, runCtx *RunContext) (uint64, error) {
	runSeq, err := infra.PipelineRunRepo().GetMaxPipelineRunSeq(ctx, runCtx.Pipeline.PipelineID)
	if err != nil {
		return 0, err
	}
	if runSeq == 0 {
		runSeq, err = infra.PipelineRunRepo().GetInvisibleMaxPipelineRunSeq(ctx, runCtx.Pipeline.PipelineID)
		if err != nil {
			return 0, err
		}
		if runSeq == 0 {
			return runSeq + 1, nil
		}
		return runSeq + 10, nil
	}
	return runSeq + 1, nil
}

func (p *pipelineRunService) createAssignment(ctx context.Context, runCtx *RunContext, pipelineRun *entity.PipelineRun) ([]uint64, error) {
	vars, err := p.pipelineVarService.GetPipelineRunSysVars(ctx, pipelineRun, runCtx.Pipeline)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "[createAssignment] pipeline run sys vars: %s", utils.JSONText(vars))

	spaceVars, err := p.pipelineVarService.GetVarsByWorkspaceId(ctx, runCtx.Pipeline.SpaceID)
	if err != nil {
		return nil, err
	}

	customVars, err := p.dealWithDynamicCustomVars(ctx, runCtx)
	if err != nil {
		return nil, err
	}
	if len(customVars) > 0 {
		vars = append(vars, customVars...)
	}
	if len(spaceVars) > 0 {
		vars = append(vars, spaceVars...)
	}
	// 合并assign和vars
	vars, err = p.mergeAssignAndVars(ctx, runCtx.AssignmentIds, runCtx.Pipeline.SpaceID, vars)
	if err != nil {
		return nil, err
	}

	// 创建自定义变量以及系统变量的assign
	assignmentIds, err := p.BatchCreateAssignment(ctx, runCtx.Pipeline.SpaceID, vars, runCtx.Username)
	if err != nil {
		logs.CtxError(ctx, "failed to create assignment, error: %s", err)
		return nil, bits_err.VARIABLE.ErrCallVarstore.AddExtra(map[string]string{
			"err": err.Error(),
		})
	}
	return assignmentIds, nil
}

// createNotifications create notifications of pipelineRun
// 这些通知可能在运行中更新，需要独立存储，不固化到pipelineRun中
func (p *pipelineRunService) createNotifications(ctx context.Context, runCtx *RunContext, runID uint64) error {
	if len(runCtx.Notifications) == 0 {
		return nil
	}

	notificationGroup, err := p.pipelineNotificationService.Save(ctx, entity.InstanceTypePipelineRun, runID, runCtx.Notifications, runCtx.Username)
	if err != nil {
		return err
	}
	runCtx.Notifications = notificationGroup.Notifications

	return nil
}

func (p *pipelineRunService) buildPipelineRunTrigger(ctx context.Context, runCtx *RunContext, runID uint64) (*entity.PipelineRunTrigger, error) {
	pplRunTrigger := &entity.PipelineRunTrigger{
		PipelineID:  runCtx.PipelineID,
		TriggerType: runCtx.TriggerType,
		RunID:       runID,
		CreatedBy:   runCtx.Username,
		CreatedAt:   time.Now(),
	}

	triggerType := runCtx.TriggerType
	if triggerType == entity.TriggerTypeRerun && runCtx.RunParams["first_run_trigger_type"] != nil {
		pbTriggerType, _ := runCtx.RunParams["first_run_trigger_type"].(float64) // utils.ConvertMapToPBStruct 中转为float64了
		triggerType = entity.TriggerTypeFromPB(platformpb.TriggerType(pbTriggerType))
	}
	if triggerType.IsGitTriggerType() {
		bytesParams, err := json.Marshal(runCtx.RunParams)
		if err != nil {
			logs.CtxError(ctx, "failed to marshal pb struct to bytes: %v", err)
			return nil, fmt.Errorf("failed to marshal pb struct to bytes: %v", err)
		}
		var gitRunParams model.GitTriggerRunParam
		err = json.Unmarshal(bytesParams, &gitRunParams)
		if err != nil {
			return nil, fmt.Errorf("failed to unmarshal to git run params: %v", err)
		}
		pplRunTrigger.RepoID = int64(gitRunParams.RepoID)
		pplRunTrigger.RepoName = gitRunParams.RepoName
		pplRunTrigger.Branch = gitRunParams.Branch
		pplRunTrigger.MrID = int64(gitRunParams.GitMrID)
		pplRunTrigger.MrTitle = gitRunParams.GitMrTitle
		pplRunTrigger.MrTargetBranch = gitRunParams.TargetBranch
		pplRunTrigger.MrSourceBranch = gitRunParams.SourceBranch
		pplRunTrigger.MrUrl = gitRunParams.GitMrUrl
		pplRunTrigger.CommitSha = gitRunParams.CommitID
		pplRunTrigger.CommitMessage = []byte(gitRunParams.CommitMessage)
	}
	return pplRunTrigger, nil
}

func (p *pipelineRunService) dealWithDynamicCustomVars(ctx context.Context, runCtx *RunContext) ([]*varstorepb.VarAssignEntry, error) {
	// custom vars without group id and with the same key
	var (
		customVars             = make([]*varstorepb.VarAssignEntry, 0)
		visitedVarMap          = make(map[string]bool)
		pipelineVarGroupDefMap = make(map[string]*varstorepb.VarDefinition)
		varGroup               = &varstorepb.VarGroup{}
	)

	// 自定义变量白名单空间，允许为定义的自定义变量传入
	customVarSpaceIDWhiteList, err := tcc.GetCustomVarsSpaceIDWhiteList(ctx)
	if err != nil {
		logs.CtxError(ctx, "failed to get custom vars space id white list, error: %+v", err)
	}
	spaceInWhiteList := gslice.Contains(customVarSpaceIDWhiteList, runCtx.Pipeline.SpaceID)

	if runCtx.Pipeline.VarGroupID != 0 {
		varGroupResp, err := p.varStoreCli.GetVarGroup(ctx, &varstorepb.GetVarGroupReq{GroupId: runCtx.Pipeline.VarGroupID})
		if err != nil {
			logs.CtxError(ctx, "failed to get pipeline %d vargroup, error: %s", runCtx.PipelineID, err)
			return customVars, err
		}
		pipelineVarGroupDefMap = gslice.ToMap(varGroupResp.GetGroup().GetVarDefinitions(), func(elem *varstorepb.VarDefinition) (string, *varstorepb.VarDefinition) {
			return elem.GetFullName(), elem
		})
		varGroup = varGroupResp.GetGroup()
	}

	for _, customVar := range runCtx.CustomVars {
		if customVar.GroupId == 0 {
			if existed, ok := visitedVarMap[customVar.Name]; ok && existed {
				continue
			}

			if varDef, exist := pipelineVarGroupDefMap[customVar.Name]; exist {
				varEntry := &varstorepb.VarAssignEntry{
					Name:         varDef.FullName,
					Value:        customVar.Value,
					GroupId:      varGroup.GetGroupId(),
					GroupVersion: varGroup.GetVersion(),
				}
				customVars = append(customVars, varEntry)
				visitedVarMap[customVar.Name] = true
			} else if spaceInWhiteList ||
				runCtx.TriggerType == entity.TriggerTypeApi {
				customVars = append(customVars, customVar)
				visitedVarMap[customVar.Name] = true
			}
		}
	}
	for _, customVar := range runCtx.CustomVars {
		if customVar.GroupId != 0 {
			if existed, ok := visitedVarMap[customVar.Name]; !ok || !existed {
				customVars = append(customVars, customVar)
				visitedVarMap[customVar.Name] = true
			}
		}
	}
	recommendGroup, err := p.pipelineVarService.GetRecommendVars(ctx, runCtx.PipelineID, 0, runCtx.Username)
	if err != nil {
		return nil, err
	}
	if recommendGroup != nil {
		for _, varDef := range recommendGroup.VarDefinitions {
			if existed, ok := visitedVarMap[varDef.FullName]; !ok || !existed {
				varEntry := &varstorepb.VarAssignEntry{
					Name:         varDef.FullName,
					Value:        varDef.DefaultValue,
					GroupId:      varGroup.GroupId,
					GroupVersion: varGroup.Version,
				}
				customVars = append(customVars, varEntry)
			}
		}
	}
	return customVars, nil
}

func (p *pipelineRunService) mergeAssignAndVars(
	ctx context.Context,
	assignIds []uint64,
	spaceID uint64,
	vars []*varstorepb.VarAssignEntry,
) ([]*varstorepb.VarAssignEntry, error) {

	notRegisteredVars, err := tcc.GetNotRegisteredSystemVars(ctx)
	if err != nil {
		logs.CtxError(ctx, "failed to get not registered system vars, error: %+v", err)
	}
	// 合并变量与Assignments
	// 1. 对于流水线相关的系统变量均以最新生成的为准
	// 2. 其余均以传入为准
	// 3. group version均取最大值
	pipelineVarM := gslice.ToMap(vars, func(elem *varstorepb.VarAssignEntry) (string, *varstorepb.VarAssignEntry) {
		return elem.GetName(), elem
	})
	mergedVars := make([]*varstorepb.VarAssignEntry, 0)
	visitedVarMap := make(map[string]bool)

	if len(assignIds) > 0 {
		resp, err := p.varStoreCli.ListAssignment(ctx, &varstorepb.ListAssignmentReq{
			AssignmentIds: assignIds,
			WithSnapshots: true,
			FlattenValues: true,
		})
		if err != nil {
			return nil, bits_err.VARIABLE.ErrCallVarstore.AddExtra(map[string]string{
				"err": err.Error(),
			})
		}
		for _, assignVar := range resp.GetFlattenValues() {
			// 不允许传入未定义的变量,除了研发模式遗留的变量...
			if assignVar.GetDefSnapshot() == nil &&
				!gslice.Contains(notRegisteredVars, assignVar.GetName()) {
				continue
			}
			_, exist := pipelineVarM[assignVar.GetName()]
			if exist && IsSystemVar(assignVar.GetName()) {
				continue
			}
			mergedVars = append(mergedVars, &varstorepb.VarAssignEntry{
				Name:         assignVar.Name,
				Value:        assignVar.Value,
				GroupId:      assignVar.GetDefSnapshot().GetGroupId(),
				GroupVersion: assignVar.GetDefSnapshot().GetVersion(),
			})
			visitedVarMap[assignVar.Name] = true
		}
	}

	for _, varDef := range vars {
		if _, ok := visitedVarMap[varDef.Name]; !ok {
			mergedVars = append(mergedVars, varDef)
			visitedVarMap[varDef.Name] = true
		}
	}
	p.MaximizeVarAssignEntriesGroupVer(mergedVars)
	return mergedVars, nil
}

func (p *pipelineRunService) matchNewRunFirstBudget(ctx context.Context, runCtx *RunContext) error {
	gap := len(runCtx.OngoingRunList) - int(runCtx.Pipeline.Concurrency)
	if gap >= 0 {
		for i := 0; i < gap+1; i++ {
			// Ongoing 状态包括 running / waiting / cancelling / rollbacking 四种，但显然其中 cancelling 状态是不允许再次执行取消的，所以此处特判一下跳过。（否则运行流水线接口报错）
			if runCtx.OngoingRunList[i].Status == entity.PipelineRunStatusCancelling {
				continue
			}
			if canCancel := p.IsCancelOperationAllowed(runCtx.OngoingRunList[i]); !canCancel {
				logs.CtxInfo(ctx, "pipeline[%d] run[%d] status[%s] can't cancel", runCtx.OngoingRunList[i].PipelineId, runCtx.OngoingRunList[i].RunId, runCtx.OngoingRunList[i].Status.String())
				return errors.Errorf("current pipeline run can't cancel")
			}
			if err := engine.Client.CancelOrcaPipeline(ctx, runCtx.OngoingRunList[i], runCtx.Username); err != nil {
				logs.CtxError(ctx, "cancel engine run with id %d failed, error: %s", runCtx.OngoingRunList[0].EngineRunId, err)
				return errors.Errorf("engine CancelPipeline failed, error: %v", err)
			}
			if err := infra.PipelineRunRepo().UpdatePipelineRunStatus(ctx, runCtx.OngoingRunList[i].RunId, entity.PipelineRunStatusCancelling); err != nil {
				logs.CtxError(ctx, "update pipeline run db status failed, error: %s", err)
				return errors.Errorf("update pipeline run db status failed, error: %v", err)
			}
		}
	}

	err := infra.PipelineRunRepo().UpdateBlockingPipelineRunStatus(ctx, runCtx.PipelineID, entity.PipelineRunStatusCancelled)
	if err != nil {
		return err
	}
	return nil
}

func (p *pipelineRunService) deletePipelineRunAndTrigger(ctx context.Context, runCtx *RunContext) {
	mysql.DB.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		deleteErr := data.TxDeletePipelineRun(ctx, runCtx.PipelineRun.RunId, tx)
		if deleteErr != nil {
			logs.CtxWarn(ctx, "failed to delete pipeline run with id %d, error:%s", runCtx.PipelineRun.RunId, deleteErr)
			return deleteErr
		}
		deleteTriggerErr := data.TxDeletePipelineRunTrigger(ctx, runCtx.PipelineRun.RunId, tx)
		if deleteTriggerErr != nil {
			logs.CtxWarn(ctx, "failed to delete pipeline run trigger with id %d, error:%s", runCtx.PipelineRun.RunId, deleteErr)
			return deleteTriggerErr
		}
		return nil
	})
}

func (p *pipelineRunService) getPipelineRunPBFromRunContext(ctx context.Context, runCtx *RunContext) (*platformpb.PipelineRun, error) {
	var enginePipelineRun *enginesdk.Pipeline
	var err error
	if runCtx.PipelineRun.EngineRunId > 0 {
		enginePipelineRun, err = GetEngineRunLite(ctx, runCtx.PipelineRun, false)
		if err != nil {
			return nil, err
		}
	}
	pipelineRunPB, err := p.GetPipelineRunPB(ctx, runCtx.PipelineRun, enginePipelineRun, false, false)
	if err != nil {
		return nil, err
	}
	pipelineRunPB.RunNotificationIds = p.GetPipelineRunNotificationIDs(runCtx.Notifications)
	return pipelineRunPB, nil
}

func (p *pipelineRunService) GetCloudBuildCompileExtraField(ctx context.Context) (*model.CloudBuildCompileExtraField, error) {
	configMap, err := tcc.GetCloudBuildConfig(ctx)
	if configMap == nil || err != nil {
		logs.CtxError(ctx, "get tcc ResourceConfig failed err:%v", err)
		return nil, errors.Errorf("get tcc ResourceConfig failed err:%v", err)
	}
	return &model.CloudBuildCompileExtraField{CloudBuildConfigMap: configMap}, nil
}

func (p *pipelineRunService) GetSensitiveVariablesFromEngineRunVariables(variables enginesdk.Variables, citedJWT bool) []interface{} {
	sensitiveVariables := gslice.FilterMap(variables, func(v *enginesdk.Variable) (interface{}, bool) {
		return v.Value, v.Secret
	})

	for _, v := range variables {
		if v.Key != varKeyContext {
			continue
		}

		ctxM, ok := v.Value.(map[string]interface{})
		if !ok {
			return sensitiveVariables
		}

		buildV, ok := ctxM[varKeyBuild]
		if !ok {
			return sensitiveVariables
		}

		buildM, ok := buildV.(map[string]interface{})
		if !ok {
			return sensitiveVariables
		}

		jwt, ok := buildM[varKeyJWT]
		if ok && citedJWT {
			logs.CtxInfo(context.Background(), "append jwt to sensitiveVariables")
			return append(sensitiveVariables, jwt)
		}
	}

	return sensitiveVariables
}

func (p *pipelineRunService) CreatePipelineRun(ctx context.Context, runCtx *RunContext) (*platformpb.LitePipelineRun, error) {
	var err error
	runCtx.PipelineRun, err = p.createPipelineRun(ctx, runCtx)
	if err != nil {
		return nil, err
	}
	logs.CtxInfo(ctx, "create pipeline run with pipeline run id %d and run seq %d and pipeline run %s", runCtx.PipelineRun.RunId, runCtx.PipelineRun.RunSeq, utils.JSONText(runCtx.PipelineRun))
	return &platformpb.LitePipelineRun{
		PipelineId: runCtx.PipelineRun.PipelineId,
		RunId:      runCtx.PipelineRun.RunId,
		RunSeq:     runCtx.PipelineRun.RunSeq,
	}, nil
}
