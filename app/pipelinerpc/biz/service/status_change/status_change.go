package status_change

import (
	"context"
	"errors"
	"fmt"
	"time"

	json "github.com/bytedance/sonic"
	"gorm.io/gorm"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	engineSDK "code.byted.org/pipeline/go-sdk"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/ai"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/frontier_rpc"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/ci_pipeline_run"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/ci_post_trigger"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/engine_callback"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_event"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_run"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/service_utils"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/no_cancel_ctx"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/timeutil"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	libUtil "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/frontierpb"
)

//go:generate mockgen -source=status_change.go -destination=./mock/status_change_mock.go -package=mock

type IStatusChangeService interface {
	HandleEnginePipelineStatusChange(ctx context.Context, enginePipeline *engineSDK.Pipeline) error
	HandleEngineStageStatusChange(ctx context.Context, engineStage *engineSDK.Stage) error
	HandleEngineJobStatusChange(ctx context.Context, engineJob *engineSDK.Job) error
	HandleEngineStepStatusChange(ctx context.Context, engineStep *engineSDK.Step) error
}

type statusChangeService struct {
	pipelineRunService   pipeline_run.IPipelineRunService
	pipelineEventService pipeline_event.IPipelineEventService
	frontier             frontier_rpc.FrontierImpl
	postTriggerService   ci_post_trigger.IPostTriggerService
}

func NewStatusChangeService() IStatusChangeService {
	return &statusChangeService{
		pipelineRunService:   pipeline_run.NewPipelineRunService(),
		pipelineEventService: pipeline_event.GetPipelineEventService(),
		postTriggerService:   ci_post_trigger.NewPostTriggerService(),
		frontier:             frontier_rpc.GetFrontierRpc(),
	}
}

func (p *statusChangeService) HandleEnginePipelineStatusChange(ctx context.Context, enginePipeline *engineSDK.Pipeline) error {
	logs.CtxInfo(ctx, "HandleEnginePipelineStatusChange start")
	// 查询当前流水线信息
	pipelineRun, err := infra.PipelineRunRepo().GetPipelineRunByEngineRunID(ctx, enginePipeline.ID, true)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		logs.CtxInfo(ctx, "pipeline run not found, skip")
		return nil
	}

	if err != nil {
		logs.CtxError(ctx, "unable to get pipeline run by engine pipeline id %d, error: %v", enginePipeline.ID, err)
		return err
	}

	// 转换orca的状态
	status, err := entity.GetPipelineRunStatusFromSDKForWebhookCallback(enginePipeline.Status)
	if err != nil {
		return nil
	}

	// 状态机判断, 不合法的状态变更不处理并埋点
	if !entity.PipelineRunStatusFlowMap[pipelineRun.Status][status] {
		logs.CtxError(ctx, "pipeline run status cannot be changed from %s to %s", pipelineRun.Status.String(), status.String())
		return nil
	}

	// 引擎侧没有返回 stages 编排信息，需要重新get。
	// 分别用于codebase ci pipeline与pipeline级别失败通知中jobFailReason的获取
	var orcaRealReqPipeline *engineSDK.Pipeline
	if pipelineRun.PipelineId == 0 || enginePipeline.Status == engineSDK.StatusFailed {
		orcaRealReqPipeline, err = engine.Client.GetOrcaPipelineRun(ctx, pipelineRun)
		if err != nil {
			logs.CtxError(ctx, "failed to get engine pipeline, error: %v", err)
			return err
		}
	}

	// 更新流水线状态
	pipelineRun.Status, pipelineRun.StartedAt, pipelineRun.CompletedAt = status, enginePipeline.StartedAt, enginePipeline.CompletedAt
	err = infra.PipelineRunRepo().UpdatePipelineRunStatusByEngineRunID(ctx, enginePipeline.ID, pipelineRun.Status, pipelineRun.StartedAt, pipelineRun.CompletedAt)
	if err != nil {
		logs.CtxError(ctx, "update pipeline run status by engine run id failed, error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEnginePipelineStatusChange] UpdatePipelineRunStatusByEngineRunID success")

	// send pipeline event for open mq event and notify(lark、webhook) event
	p.pipelineEventService.PipelineRunStatusChange(ctx, enginePipeline, orcaRealReqPipeline, pipelineRun)

	gCtx := no_cancel_ctx.WithoutCancel(ctx)
	libUtil.SafeGoWithoutCopyCtx(gCtx, func() {
		engine_callback.TeaPipelineRunFinish(gCtx, enginePipeline, pipelineRun)
	})

	if pipelineRun.PipelineId > 0 {
		// 如果流水线是新的运行发送websocket给前端
		if gslice.Contains([]engineSDK.Status{engineSDK.StatusCreated, engineSDK.StatusQueued}, enginePipeline.Status) {
			logs.CtxError(ctx, "[HandleEnginePipelineStatusChange] SendPipelineLastRunChangeToFe pipeline_id:%d,run_id:%d, orcaCallbackPipeline status:%s", pipelineRun.PipelineId, pipelineRun.RunId, enginePipeline.Status)
			p.frontier.SendPipelineLastRunChangeToFe(ctx, pipelineRun.PipelineId, pipelineRun.RunId)
		}
		p.frontier.SendPipelineRunChangeToFe(ctx, pipelineRun.PipelineId, []uint64{pipelineRun.RunId}, status.String())

		err = p.tryNextBlockingRun(ctx, status, enginePipeline.ID)
		if err != nil {
			logs.CtxError(ctx, "[HandleEnginePipelineStatusChange] try to run next blocking pipeline run failed, error: %s", err)
			return err
		}
	} else {
		parentRunId, err := infra.PipelineRunRelationRepo().GetParentPipelineRunID(ctx, pipelineRun.RunId)
		if err != nil {
			logs.CtxError(ctx, "[HandleEnginePipelineStatusChange] unable to get parent run id by pipeline run id, error: %v", err)
			return err
		}

		// 检测CI是否有子流水线触发，必须是终态触发且子流水线不能作为父流水线（防止无限循环）
		if status.IsFinalStatus() && parentRunId == 0 {
			if err := p.postTriggerService.TriggerPostTrigger(ctx, orcaRealReqPipeline, pipelineRun); err != nil {
				logs.CtxError(ctx, "[HandleEnginePipelineStatusChange] post trigger failed, error: %v", err)
				return err
			}
		}

		// 判断是否为mr触发，如果不是则不进行update check run相关逻辑
		// 此处增加对于post trigger状态更新的判断，子流水线统一为manual触发，但需要更新对应的check run卡片。
		if pipelineRun.TriggerType.IsMrTriggerType() || parentRunId > 0 {
			// sync pipeline
			if err = ci_pipeline_run.DoSyncPipeline(ctx, orcaRealReqPipeline, pipelineRun, parentRunId); err != nil {
				return err
			}
		} else {
			return nil
		}
	}
	return nil
}

func (p *statusChangeService) HandleEngineJobStatusChange(ctx context.Context, engineJob *engineSDK.Job) error {
	if engineJob.StartedAt != nil && engineJob.StartedAt.After(time.Now().Add(-time.Second*3)) {
		ctx = infra.SetCtxUseWriteDB(ctx) // orca的回调涉及时间戳、状态的写入，回调时从库可能查不到数据，因此认为此流程需要写库
	}
	logs.CtxInfo(ctx, "HandleEngineJobStatusChange start")
	controlPanel, err := infra.JobRunRepo().GetJobRunControlPanel(ctx, uint64(engineJob.ID))
	if err != nil {
		return err
	}

	if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, bits_err.PIPELINECOMMON.ErrRecordNotFound) {
		logs.CtxInfo(ctx, "[HandleEngineJobStatusChange] job controlPanel not found, skip")
		return nil
	}

	orcaRealReqJob, err := engine.Client.GetOrcaJobRun(ctx, engineJob.ID, controlPanel)
	if err != nil {
		logs.CtxError(ctx, "[HandleEngineJobStatusChange] failed to get engine job, error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineJobStatusChange] GetOrcaJobRun %s", utils.JSONText(orcaRealReqJob))
	// Orca mq 数据延迟2s发送，在接收到s1状态信息时，查询orca可能会查询到s2状态，导致s2状态所需的操作会重复发送，例如「待交互」
	// 因此当前处理时仍然使用request中解析处的job状态，由于提供给外部的mq需要steps信息，避免重复查询orca放大流量，因此增加一个参数engineJob
	p.pipelineEventService.JobRunStatusChange(ctx, engineJob, orcaRealReqJob)

	status, err := p.UpdateJobRun(ctx, orcaRealReqJob)
	if err != nil {
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineJobCallback] UpdateJobRun status %s", status.String())
	pipelineRun, err := infra.PipelineRunRepo().GetPipelineRunByEngineRunID(ctx, orcaRealReqJob.PipelineID, true)
	if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, bits_err.PIPELINECOMMON.ErrRecordNotFound) {
		logs.CtxInfo(ctx, "[HandleEngineJobStatusChange] GetPipelineRunByEngineRunID not found, skip")
		return nil
	}
	if err != nil {
		logs.CtxError(ctx, "[HandleEngineJobStatusChange] unable to get pipeline run by engine pipeline id, error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineJobCallback] pipeline run status: %s", status.String())
	if pipelineRun.PipelineId > 0 {
		p.frontier.SendJobRunChangeToFe(ctx, pipelineRun.PipelineId, pipelineRun.RunId, uint64(orcaRealReqJob.ID), status.String(), frontierpb.JobRunChangeType_JOB_RUN_CHANGE_TYPE_STATUS_CHANGE)
	}

	// 打点上报tea数据
	ctx2 := no_cancel_ctx.WithoutCancel(ctx)
	libUtil.SafeGoWithoutCopyCtx(ctx, func() {
		engine_callback.TeaJobFinish(ctx2, orcaRealReqJob, status, pipelineRun)
	})

	// 子流水线关联表信息
	parentRunId, err := infra.PipelineRunRelationRepo().GetParentPipelineRunID(ctx, pipelineRun.RunId)
	// 自动诊断
	go p.syncCallAiGptAnalysis(ctx, orcaRealReqJob, pipelineRun)

	if err != nil {
		logs.CtxError(ctx, "[HandleEngineJobStatusChange] syncCallAiGptAnalysis error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineJobCallback] parent run id: %d", parentRunId)
	// 判断是否为mr触发，如果不是则不进行update checkrun 相关逻辑
	// 子流水线触发方式为手动触发，但是仍需要在mr页面更新checkrun卡片，检测下是否有父流水线决定是否更新
	// 另：mr页面内手动触发ppl，触发类型为manual，但存在mr_id, 需要进行卡片更新操作
	if pipelineRun.TriggerType.IsMrTriggerType() || pipelineRun.TriggerInfo.MrID > 0 || parentRunId > 0 {
		// sync pipeline
		if err = ci_pipeline_run.DoSyncJob(ctx, orcaRealReqJob, pipelineRun, parentRunId); err != nil {
			return err
		}
	}

	service_utils.EmitJobMetrics(ctx, orcaRealReqJob, pipelineRun, status)

	return nil
}

func (p *statusChangeService) syncCallAiGptAnalysis(ctx context.Context, engineJob *engineSDK.Job, pipelineRun *entity.PipelineRun) {
	defer ctxutil.RecoverAndPrintWithContext(ctx)
	if engineJob.Status != engineSDK.StatusFailed {
		logs.CtxInfo(ctx, "[syncCallAiGptAnalysis] job_id=%d is not failed", engineJob.ID)
		return
	}
	// 非mr的case不处理
	if !pipelineRun.TriggerType.IsMrTriggerType() || pipelineRun.TriggerInfo.MrID == 0 {
		logs.CtxInfo(ctx, "[syncCallAiGptAnalysis] run_id=%d is not mr trigger", pipelineRun.RunId)
		return
	}
	if len(engineJob.Steps) == 0 {
		logs.CtxInfo(ctx, "[syncCallAiGptAnalysis] job_id=%d step is empty", engineJob.ID)
		return
	}

	stepFailedIDs := gslice.FilterMap(engineJob.Steps, func(stepInfo *engineSDK.Step) (int64, bool) {
		return stepInfo.ID, stepInfo.Status == engineSDK.StatusFailed
	})
	// 获取引擎侧报错日志
	if len(stepFailedIDs) == 0 {
		stepFailedIDs = append(stepFailedIDs, engineJob.Steps[0].ID)
	}
	failedStepID := stepFailedIDs[len(stepFailedIDs)-1]

	logs.CtxInfo(ctx, "[syncCallAiGptAnalysis] repoName=%s, jobID=%d, stepID=%d, jobRunSeq=%d", pipelineRun.RepoName, uint64(engineJob.ID), failedStepID, uint64(engineJob.Tried))
	// 忽略掉错误
	if err := ai.NewClient().AnalyzeJobRunLog(ctx, pipelineRun.RepoName, uint64(engineJob.ID), uint64(engineJob.Tried), failedStepID); err != nil {
		logs.CtxInfo(ctx, "[syncCallAiGptAnalysis] failed toAnalyzeJobRunLog,err:%v", err)
	}
}

func (p *statusChangeService) HandleEngineStepStatusChange(ctx context.Context, engineStep *engineSDK.Step) error {
	logs.CtxInfo(ctx, "HandleEngineStepStatusChange start")
	controlPanel, err := infra.JobRunRepo().GetJobRunControlPanel(ctx, uint64(engineStep.JobID))
	if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, bits_err.PIPELINECOMMON.ErrRecordNotFound) {
		logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] job controlPanel not found, skip")
		return nil
	}
	if err != nil {
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] GetJobRunControlPanel controlPanel:%s", controlPanel.ToString())
	engineJob, err := engine.Client.GetOrcaJobRun(ctx, engineStep.JobID, controlPanel)
	if err != nil {
		logs.CtxError(ctx, "failed to get engine job, error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] GetOrcaJobRun %s", utils.JSONText(engineJob))
	status, err := p.UpdateJobRun(ctx, engineJob)
	if err != nil {
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] UpdateJobRun status %s", status.String())
	if engineStep.Status == engineSDK.StatusCreated {
		return nil
	}

	pipelineRun, err := infra.PipelineRunRepo().GetPipelineRunByEngineRunID(ctx, engineJob.PipelineID, true)
	if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, bits_err.PIPELINECOMMON.ErrRecordNotFound) {
		logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] job controlPanel not found, skip")
		return nil
	}
	if err != nil {
		logs.CtxError(ctx, "[HandleEngineStepStatusChange] GetPipelineRunByEngineRunID error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] GetPipelineRunByEngineRunID %s", utils.JSONText(pipelineRun))
	if pipelineRun.PipelineId > 0 {
		p.frontier.SendJobRunChangeToFe(ctx, pipelineRun.PipelineId, pipelineRun.RunId, uint64(engineJob.ID), status.String(), frontierpb.JobRunChangeType_JOB_RUN_CHANGE_TYPE_STEP_CHANGE)
	}
	// 子流水线关联表信息
	parentRunId, err := infra.PipelineRunRelationRepo().GetParentPipelineRunID(ctx, pipelineRun.RunId)
	if err != nil {
		logs.CtxError(ctx, "[HandleEngineStepStatusChange] GetParentPipelineRunID error: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "[HandleEngineStepStatusChange] parent run id: %d", parentRunId)
	// 判断是否为mr触发，如果不是则不进行update checkrun 相关逻辑
	// 子流水线触发方式为手动触发，但是仍需要在mr页面更新checkrun卡片，检测下是否有父流水线决定是否更新
	// 另：mr页面内手动触发ppl，触发类型为manual，但存在mr_id, 需要进行卡片更新操作
	if pipelineRun.TriggerType.IsMrTriggerType() || pipelineRun.TriggerInfo.MrID > 0 || parentRunId > 0 {
		// sync pipeline
		if err = ci_pipeline_run.DoSyncJob(ctx, engineJob, pipelineRun, parentRunId); err != nil {
			return err
		}
	}

	service_utils.EmitStepMetrics(ctx, engineStep, pipelineRun, status)
	return nil
}

func (p *statusChangeService) tryNextBlockingRun(ctx context.Context, status entity.PipelineRunStatus, engineRunID int64) error {
	if !status.IsFinalStatus() {
		return nil
	}
	pipelineRun, err := infra.PipelineRunRepo().GetPipelineRunByEngineRunID(ctx, engineRunID, true)
	if err != nil {
		return err
	}
	firstBlockingRun, err := infra.PipelineRunRepo().GetFirstPipelineRunByPipelineIDAndStatus(ctx, pipelineRun.PipelineId, entity.PipelineRunStatusBlocking)
	if err != nil {
		return err
	}
	if firstBlockingRun == nil {
		return nil
	}
	pipeline, err := infra.PipelineRepo().GetPipelineWithOrca(ctx, firstBlockingRun.PipelineId, firstBlockingRun.PipelineVersion)
	if err != nil {
		return err
	}
	pipeline_run.InjectJobNameToJobInput(pipeline.Stages)
	parentRunID, err := infra.PipelineRunRelationRepo().GetParentPipelineRunID(ctx, firstBlockingRun.RunId)
	if err != nil {
		return err
	}
	runCtx := &pipeline_run.RunContext{
		PipelineID:  firstBlockingRun.PipelineId,
		Username:    firstBlockingRun.CreatedBy,
		ParentRunID: parentRunID,
		Pipeline:    pipeline,
		PipelineRun: firstBlockingRun, // trying to run this one
		// no need to set values of these fields, already in pipeline run
		// triggerType:     pipelineRun.TriggerType,
		// runParams:       nil,
		// notifications:   nil,
		// customVars:      nil,
		// assignmentIds:   nil,
		// blockingRunList: nil,
		// ongoingRunList:  nil,
		// idempotentToken: "",

	}
	err = p.RunNextBlockingRun(ctx, runCtx)
	if err != nil {
		return err
	}

	err = p.pipelineRunService.AddChildRun(ctx, runCtx.ParentRunID, runCtx.PipelineRun)
	if err != nil {
		// do not return err to make the whole run failed, just print log
		// since if run pipeline is ok, actual pipeline run is created and running now
		logs.CtxError(ctx, "failed to append child pipeline runs %d to parent run id %d", runCtx.PipelineRun.RunId, runCtx.ParentRunID)
	}
	return nil
}

func (p *statusChangeService) RunNextBlockingRun(ctx context.Context, runCtx *pipeline_run.RunContext) error {
	lockKey := utils.GetPipelineRunConcurrencyLockKey(runCtx.PipelineRun.PipelineId)
	lockVal := time.Now().String()
	lock, err := redis.TryLock(ctx, lockKey, lockVal, 3, 20*time.Second, 500*time.Millisecond)
	if err != nil {
		logs.CtxError(ctx, "fail to get pipeline run cncr lock, key=%v,err=%v", lockKey, err)
		return err
	}
	if !lock {
		// 多次重试后还是获取不到锁，说明争抢很多或者上一个锁还在执行中，等待重试
		logs.CtxWarn(ctx, "fail to get pipeline run init lock, key=%v", lockKey)
		return errors.New(fmt.Sprintf("fail to get pipeline run init lock, key=%v", lockKey))
	}

	defer func() {
		if lock {
			err = redis.TryUnLock(ctx, lockKey, lockVal, 3, 500*time.Millisecond)
			if err != nil {
				logs.CtxError(ctx, "fail to release pipeline run init lock, key=%v,err=%v", lockKey, err)
			} else {
				logs.CtxInfo(ctx, "pipeline run init lock released, key=%v", lockKey)
			}
		}
	}()

	budget, err := p.pipelineRunService.GetConcurrencyBudget(ctx, runCtx)
	if err != nil {
		return err
	}

	if budget != 0 {
		err = p.pipelineRunService.Run(ctx, runCtx)
		if err != nil {
			logs.CtxError(ctx, "run engine transaction failed, error: %s", err)
			return err
		}
	}
	return nil
}

func (p *statusChangeService) UpdateJobRun(ctx context.Context, engineJob *engineSDK.Job) (entity.JobRunStatus, error) {
	status := entity.GetJobRunStatusFromSDK(engineJob.Status)
	jobRun, err := infra.JobRunRepo().GetJobRunByEngineJobID(ctx, engineJob.ID)
	if err != nil {
		return status, err
	}
	waitingTimes := jobRun.GetWaitingTimes()
	if jobRun.Status != entity.JobRunStatusWaiting && status == entity.JobRunStatusWaiting {
		waitingTime := entity.WaitingTime{
			StartedAt: timeutil.TimePtr(engineJob.LastUpdatedAt),
		}
		waitingTimes = append(waitingTimes, waitingTime)
	} else if jobRun.Status == entity.JobRunStatusWaiting && status != entity.JobRunStatusWaiting {
		if len(waitingTimes) > 0 && waitingTimes[len(waitingTimes)-1].CompletedAt == nil {
			waitingTimes[len(waitingTimes)-1].CompletedAt = timeutil.TimePtr(engineJob.LastUpdatedAt)
		}
	}
	if jobRun.Status == entity.JobRunStatusCreated {
		var (
			engineScheduleAt *time.Time
			engineRunAt      *time.Time
		)
		engineScheduleAt = engineJob.StartedAt
		if status != entity.JobRunStatusWaiting { // 等待手动执行
			engineRunAt = engineJob.StartedAt
		}
		_ = infra.JobRunRepo().UpdateJobRunEngineTime(ctx, engineJob.ID, engineScheduleAt, engineRunAt)
	}
	byteWaitingTimes, err := json.Marshal(waitingTimes)
	if err != nil {
		return status, err
	}
	err = infra.JobRunRepo().UpdateJobRunStatusByEngineJobID(ctx, engineJob.ID, status, engineJob.StartedAt, engineJob.CompletedAt, byteWaitingTimes)
	if err != nil {
		return status, err
	}
	return status, nil
}

func (p *statusChangeService) HandleEngineStageStatusChange(ctx context.Context, enginePipeline *engineSDK.Stage) error {
	return nil
}
