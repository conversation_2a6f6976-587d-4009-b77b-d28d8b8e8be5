package status_change

import (
	"context"
	"errors"
	"testing"
	"time"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/infrapb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/structpb"

	"code.byted.org/lang/gg/gptr"
	engineSDK "code.byted.org/pipeline/go-sdk"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/frontier_rpc"
	pipeline_event_mock "code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_event/mock"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_run"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_run/mock"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	infraMock "code.byted.org/devinfra/hagrid/app/pipelinerpc/infra/mock"
	"code.byted.org/devinfra/hagrid/internal/pipeline/pentity"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	mockengine "code.byted.org/devinfra/hagrid/pkg/leafboat/mock"
)

func setupTest(t *testing.T) (*gomock.Controller, statusChangeService, context.Context) {
	ctrl := gomock.NewController(t)
	infra.NewMockRepo()
	svc := statusChangeService{}
	ctx := context.Background()
	return ctrl, svc, ctx
}

func TestHandleEnginePipelineStatusChange(t *testing.T) {
	req := &engineSDK.Pipeline{
		ID:     1,
		Status: engineSDK.StatusRunning,
	}
	t.Run("获取pipeline run失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("GetPipelineRunByEngineRunID failed")).Times(1)
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("获取pipeline run成功的场景，但是状态转换失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{}, nil).Times(1)
		req.Status = engineSDK.StatusIgnored
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		req.Status = engineSDK.StatusRunning
		assert.NoError(t, err)
	})
	t.Run("状态机判断", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			Status: entity.PipelineRunStatusRunning,
		}, nil).Times(1)
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.NoError(t, err)
	})
	t.Run("更新状态失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 2,
			RunId:      1,
			Status:     entity.PipelineRunStatusWaiting,
		}, nil).AnyTimes()
		mockPipelineRunRepo.EXPECT().UpdatePipelineRunStatusByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("UpdatePipelineRunStatusByEngineRunID failed")).AnyTimes()
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("运行下一个流水线失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		frontier := frontier_rpc.GetFrontierRpc()
		svc.pipelineEventService = pipelineEventService
		svc.frontier = frontier

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 3,
			RunId:      1,
			Status:     entity.PipelineRunStatusWaiting,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().UpdatePipelineRunStatusByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		defer mockey.Mock(mockey.GetMethod(frontier, "SendPipelineRunChangeToFe")).Return().Build().UnPatch()
		pipelineEventService.EXPECT().PipelineRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		defer mockey.Mock((*statusChangeService).tryNextBlockingRun).Return(errors.New("error")).Build().UnPatch()
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("运行下一个流水线成功", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		frontier := frontier_rpc.GetFrontierRpc()
		svc.pipelineEventService = pipelineEventService
		svc.frontier = frontier

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 4,
			RunId:      1,
			Status:     entity.PipelineRunStatusWaiting,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().UpdatePipelineRunStatusByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		defer mockey.Mock(mockey.GetMethod(frontier, "SendPipelineRunChangeToFe")).Return().Build().UnPatch()
		pipelineEventService.EXPECT().PipelineRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		defer mockey.Mock((*statusChangeService).tryNextBlockingRun).Return(nil).Build().UnPatch()
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.NoError(t, err)
	})
	t.Run("GetPipelineRunByEngineRunID 失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		svc.pipelineEventService = pipelineEventService

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:  2,
			Status: entity.PipelineRunStatusWaiting,
		}, nil).Times(1)
		mockCli.EXPECT().GetOrcaPipelineRun(gomock.Any(), gomock.Any()).Return(&engineSDK.Pipeline{}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().UpdatePipelineRunStatusByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(1), errors.New("error")).Times(1)
		pipelineEventService.EXPECT().PipelineRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.Error(t, err)
	})

	t.Run("success", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		svc.pipelineEventService = pipelineEventService

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:  3,
			Status: entity.PipelineRunStatusWaiting,
		}, nil).Times(1)
		mockCli.EXPECT().GetOrcaPipelineRun(gomock.Any(), gomock.Any()).Return(&engineSDK.Pipeline{}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().UpdatePipelineRunStatusByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(1), nil).Times(1)
		pipelineEventService.EXPECT().PipelineRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		err := svc.HandleEnginePipelineStatusChange(ctx, req)
		assert.NoError(t, err)
	})

}

func TestHandleEngineJobStatusChange(t *testing.T) {
	req := &engineSDK.Job{
		ID:        1,
		Status:    engineSDK.StatusRunning,
		StartedAt: gptr.Of(time.Now()),
	}
	t.Run("GetJobRunControlPanel失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, errors.New("error")).Times(1)
		err := svc.HandleEngineJobStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("GetOrcaJobRun失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, errors.New("error")).Times(1)
		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		err := svc.HandleEngineJobStatusChange(ctx, req)
		assert.Error(t, err)
	})

	t.Run("UpdateJobRun失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		svc.pipelineEventService = pipelineEventService

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		pipelineEventService.EXPECT().JobRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusRunning, errors.New("error")).Build().UnPatch()

		err := svc.HandleEngineJobStatusChange(ctx, req)
		assert.Error(t, err)
	})

	t.Run("GetPipelineRunByEngineRunID失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		svc.pipelineEventService = pipelineEventService

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		pipelineEventService.EXPECT().JobRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusRunning, nil).Build().UnPatch()
		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{}, errors.New("error")).Times(1)

		err := svc.HandleEngineJobStatusChange(ctx, req)
		assert.Error(t, err)
	})

	t.Run("GetParentPipelineRunID失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		svc.pipelineEventService = pipelineEventService

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		pipelineEventService.EXPECT().JobRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusRunning, nil).Build().UnPatch()
		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), errors.New("error")).Times(1)

		err := svc.HandleEngineJobStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("success", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		pipelineEventService := pipeline_event_mock.NewMockIPipelineEventService(ctrl)
		svc.pipelineEventService = pipelineEventService

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		pipelineEventService.EXPECT().JobRunStatusChange(gomock.Any(), gomock.Any(), gomock.Any()).Return().Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusSucceeded, nil).Build().UnPatch()
		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			TriggerType: entity.TriggerTypeAuto,
			TriggerInfo: &entity.PipelineRunTrigger{},
		}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), nil).Times(1)

		err := svc.HandleEngineJobStatusChange(ctx, req)
		assert.NoError(t, err)
	})

}

func TestHandleEngineStepStatusChange(t *testing.T) {
	req := &engineSDK.Step{
		ID:     1,
		Status: engineSDK.StatusRunning,
	}
	t.Run("GetJobRunControlPanel失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, errors.New("error")).Times(1)
		err := svc.HandleEngineStepStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("GetOrcaJobRun失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, errors.New("error")).Times(1)
		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		err := svc.HandleEngineStepStatusChange(ctx, req)
		assert.Error(t, err)
	})

	t.Run("UpdateJobRun失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusRunning, errors.New("error")).Build().UnPatch()

		err := svc.HandleEngineStepStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("GetPipelineRunByEngineRunID失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusRunning, nil).Build().UnPatch()
		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{}, errors.New("error")).Times(1)

		err := svc.HandleEngineStepStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("GetParentPipelineRunID失败的场景", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusRunning, nil).Build().UnPatch()
		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), errors.New("error")).Times(1)

		err := svc.HandleEngineStepStatusChange(ctx, req)
		assert.Error(t, err)
	})
	t.Run("success", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockCli := mockengine.NewMockOrcaSdk(ctrl)
		engine.Client = mockCli

		mockJobRunRepo.EXPECT().GetJobRunControlPanel(gomock.Any(), gomock.Any()).Return(pentity.ControlPanelCN, nil).Times(1)
		mockCli.EXPECT().GetOrcaJobRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&engineSDK.Job{}, nil).Times(1)
		defer mockey.Mock((*statusChangeService).UpdateJobRun).Return(entity.JobRunStatusSucceeded, nil).Build().UnPatch()
		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			TriggerType: entity.TriggerTypeAuto,
			TriggerInfo: &entity.PipelineRunTrigger{},
		}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), nil).Times(1)

		err := svc.HandleEngineStepStatusChange(ctx, req)
		assert.NoError(t, err)
	})

}
func TestHandleEngineStageStatusChange(t *testing.T) {
	svc := statusChangeService{}
	err := svc.HandleEngineStageStatusChange(context.Background(), &engineSDK.Stage{})
	assert.NoError(t, err)
}

func TestUpdateJobRun(t *testing.T) {
	engineJob := engineSDK.Job{
		ID:     1,
		Status: engineSDK.StatusRunning,
	}
	t.Run("GetJobRunByEngineJobID error", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo

		mockJobRunRepo.EXPECT().GetJobRunByEngineJobID(gomock.Any(), gomock.Any()).Return(nil, errors.New("error")).Times(1)
		_, err := svc.UpdateJobRun(ctx, &engineJob)
		assert.Error(t, err)
	})
	t.Run("UpdateJobRunStatusByEngineJobID failed", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		engineJob.Status = engineSDK.StatusWaiting
		mockJobRunRepo.EXPECT().GetJobRunByEngineJobID(gomock.Any(), gomock.Any()).Return(&entity.JobRun{Status: entity.JobRunStatusWaiting}, nil).Times(1)
		mockJobRunRepo.EXPECT().UpdateJobRunStatusByEngineJobID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error")).Times(1)
		_, err := svc.UpdateJobRun(ctx, &engineJob)

		assert.Error(t, err)
	})
	t.Run("UpdateJobRunStatusByEngineJobID failed", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		engineJob.Status = engineSDK.StatusWaiting
		mockJobRunRepo.EXPECT().GetJobRunByEngineJobID(gomock.Any(), gomock.Any()).Return(&entity.JobRun{Status: entity.JobRunStatusRunning}, nil).Times(1)
		mockJobRunRepo.EXPECT().UpdateJobRunStatusByEngineJobID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		_, err := svc.UpdateJobRun(ctx, &engineJob)

		assert.NoError(t, err)
	})
	t.Run("jobRun is created", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockJobRunRepo := infraMock.NewMockIJobRunRepo(ctrl)
		infra.Repo.JobRun = mockJobRunRepo
		engineJob.Status = engineSDK.StatusWaiting
		mockJobRunRepo.EXPECT().GetJobRunByEngineJobID(gomock.Any(), gomock.Any()).Return(&entity.JobRun{Status: entity.JobRunStatusCreated}, nil).Times(1)
		mockJobRunRepo.EXPECT().UpdateJobRunEngineTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		mockJobRunRepo.EXPECT().UpdateJobRunStatusByEngineJobID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		_, err := svc.UpdateJobRun(ctx, &engineJob)

		assert.NoError(t, err)
	})
}

func TestRunNextBlockingRun(t *testing.T) {
	// 公共测试数据
	mockRunCtx := &pipeline_run.RunContext{
		PipelineRun: &entity.PipelineRun{
			PipelineId: 1001,
		},
	}

	t.Run("获取锁失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(false, errors.New("lock error")).Build().UnPatch()
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.Error(t, err)
	})

	t.Run("获取锁超时", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(false, nil).Build().UnPatch()
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "fail to get pipeline run init lock")
	})

	t.Run("GetConcurrencyBudget失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		pipelineService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = pipelineService
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(true, nil).Build().UnPatch()
		defer mockey.Mock(redis.TryUnLock).Return(nil).Build().UnPatch()
		pipelineService.EXPECT().GetConcurrencyBudget(gomock.Any(), gomock.Any()).Return(0, errors.New("GetConcurrencyBudget error")).Times(1)
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.Error(t, err)
	})

	t.Run("budget为0，不执行Run", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		pipelineService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = pipelineService
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(true, nil).Build().UnPatch()
		defer mockey.Mock(redis.TryUnLock).Return(nil).Build().UnPatch()
		pipelineService.EXPECT().GetConcurrencyBudget(gomock.Any(), gomock.Any()).Return(0, nil).Times(1)
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.NoError(t, err)
	})

	t.Run("Run执行失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		pipelineService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = pipelineService
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(true, nil).Build().UnPatch()
		defer mockey.Mock(redis.TryUnLock).Return(nil).Build().UnPatch()
		pipelineService.EXPECT().GetConcurrencyBudget(gomock.Any(), gomock.Any()).Return(1, nil).Times(1)
		pipelineService.EXPECT().Run(gomock.Any(), gomock.Any()).Return(errors.New("Run failed")).Times(1)
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.Error(t, err)
	})

	t.Run("释放锁失败但不影响结果", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		pipelineService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = pipelineService
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(true, nil).Build().UnPatch()
		defer mockey.Mock(redis.TryUnLock).Return(errors.New("unlock failed")).Build().UnPatch()
		pipelineService.EXPECT().GetConcurrencyBudget(gomock.Any(), gomock.Any()).Return(1, nil).Times(1)
		pipelineService.EXPECT().Run(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.NoError(t, err) // 释放锁失败不影响整体结果
	})

	t.Run("成功执行完整流程", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		pipelineService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = pipelineService
		defer mockey.Mock(utils.GetPipelineRunConcurrencyLockKey).Return("lock_key_1001").Build().UnPatch()
		defer mockey.Mock(redis.TryLock).Return(true, nil).Build().UnPatch()
		defer mockey.Mock(redis.TryUnLock).Return(nil).Build().UnPatch()
		pipelineService.EXPECT().GetConcurrencyBudget(gomock.Any(), gomock.Any()).Return(1, nil).Times(1)
		pipelineService.EXPECT().Run(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := svc.RunNextBlockingRun(ctx, mockRunCtx)
		assert.NoError(t, err)
	})
}

func TestTryNextBlockingRun(t *testing.T) {
	t.Run("状态不是终态，直接返回", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()

		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusRunning, 1)
		assert.NoError(t, err)
	})

	t.Run("GetPipelineRunByEngineRunID失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("GetPipelineRunByEngineRunID failed")).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.Error(t, err)
	})

	t.Run("GetFirstPipelineRunByPipelineIDAndStatus失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("GetFirstPipelineRunByPipelineIDAndStatus failed")).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.Error(t, err)
	})

	t.Run("没有blocking状态的pipeline run，直接返回", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.NoError(t, err)
	})

	t.Run("GetPipelineWithOrca失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRepo := infraMock.NewMockIPipelineRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.Pipeline = mockPipelineRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:           1,
			PipelineId:      1,
			PipelineVersion: 1,
		}, nil).Times(1)
		mockPipelineRepo.EXPECT().GetPipelineWithOrca(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("GetPipelineWithOrca failed")).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.Error(t, err)
	})

	t.Run("GetParentPipelineRunID失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRepo := infraMock.NewMockIPipelineRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.Pipeline = mockPipelineRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:           1,
			PipelineId:      1,
			PipelineVersion: 1,
		}, nil).Times(1)
		mockPipelineRepo.EXPECT().GetPipelineWithOrca(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.Pipeline{
			Stages: []*dslpb.Stage{},
		}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), errors.New("GetParentPipelineRunID failed")).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.Error(t, err)
	})

	t.Run("RunNextBlockingRun失败", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRepo := infraMock.NewMockIPipelineRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.Pipeline = mockPipelineRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:           1,
			PipelineId:      1,
			PipelineVersion: 1,
			CreatedBy:       "test_user",
		}, nil).Times(1)
		mockPipelineRepo.EXPECT().GetPipelineWithOrca(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.Pipeline{
			Stages: []*dslpb.Stage{},
		}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), nil).Times(1)
		defer mockey.Mock(mockey.GetMethod(svc, "RunNextBlockingRun")).Return(errors.New("RunNextBlockingRun failed")).Build().UnPatch()
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.Error(t, err)
	})

	t.Run("AddChildRun失败但不影响整体流程", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRepo := infraMock.NewMockIPipelineRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.Pipeline = mockPipelineRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:           1,
			PipelineId:      1,
			PipelineVersion: 1,
			CreatedBy:       "test_user",
		}, nil).Times(1)
		mockPipelineRepo.EXPECT().GetPipelineWithOrca(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.Pipeline{
			Stages: []*dslpb.Stage{},
		}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), nil).Times(1)
		defer mockey.Mock(mockey.GetMethod(svc, "RunNextBlockingRun")).Return(nil).Build().UnPatch()

		mockPipelineRunService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = mockPipelineRunService
		mockPipelineRunService.EXPECT().AddChildRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("AddChildRun failed")).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.NoError(t, err) // AddChildRun失败不影响整体流程
	})

	t.Run("成功执行完整流程", func(t *testing.T) {
		ctrl, svc, ctx := setupTest(t)
		defer ctrl.Finish()
		mockPipelineRunRepo := infraMock.NewMockIPipelineRunRepo(ctrl)
		mockPipelineRepo := infraMock.NewMockIPipelineRepo(ctrl)
		mockPipelineRunRelationRepo := infraMock.NewMockIPipelineRunRelationRepo(ctrl)
		infra.Repo.PipelineRun = mockPipelineRunRepo
		infra.Repo.Pipeline = mockPipelineRepo
		infra.Repo.PipelineRunRelationRepo = mockPipelineRunRelationRepo

		mockPipelineRunRepo.EXPECT().GetPipelineRunByEngineRunID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			PipelineId: 1,
		}, nil).Times(1)
		mockPipelineRunRepo.EXPECT().GetFirstPipelineRunByPipelineIDAndStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.PipelineRun{
			RunId:           1,
			PipelineId:      1,
			PipelineVersion: 1,
			CreatedBy:       "test_user",
		}, nil).Times(1)
		mockPipelineRepo.EXPECT().GetPipelineWithOrca(gomock.Any(), gomock.Any(), gomock.Any()).Return(&entity.Pipeline{
			Stages: []*dslpb.Stage{
				{
					Jobs: []*dslpb.Job{
						{
							Name: &infrapb.I18NString{
								Value: "cn-{{custom.a}}",
								Lang:  "cn",
								Texts: map[string]string{
									"en": "en",
									"cn": "cn",
								},
							},
							Inputs: &structpb.Struct{
								Fields: map[string]*structpb.Value{
									"a": {
										Kind: &structpb.Value_StringValue{
											StringValue: "a",
										},
									},
								},
							},
						},
					},
				},
			},
		}, nil).Times(1)
		mockPipelineRunRelationRepo.EXPECT().GetParentPipelineRunID(gomock.Any(), gomock.Any()).Return(uint64(0), nil).Times(1)
		defer mockey.Mock(mockey.GetMethod(svc, "RunNextBlockingRun")).Return(nil).Build().UnPatch()

		mockPipelineRunService := mock.NewMockIPipelineRunService(ctrl)
		svc.pipelineRunService = mockPipelineRunService
		mockPipelineRunService.EXPECT().AddChildRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
		err := svc.tryNextBlockingRun(ctx, entity.PipelineRunStatusSucceeded, 1)
		assert.NoError(t, err)
	})
}
