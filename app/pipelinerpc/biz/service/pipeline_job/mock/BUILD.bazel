load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["job_service_mock.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_job/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pipelinerpc/domain/entity",
        "//idls/byted/devinfra/pipeline/dsl:dsl_go_proto",
        "//idls/byted/devinfra/pipeline/platform:platform_go_proto",
        "//internal/pipeline/pentity",
        "@com_github_golang_mock//gomock",
        "@org_byted_code_pipeline_go_sdk//:go-sdk",
    ],
)
