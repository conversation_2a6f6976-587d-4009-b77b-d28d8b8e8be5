load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "pipeline_notification",
    srcs = [
        "notification.go",
        "notification_group.go",
        "notification_service.go",
        "utils.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/pipeline_notification",
    visibility = ["//visibility:public"],
    deps = [
        "//app/pipelinerpc/biz/dal/data",
        "//app/pipelinerpc/biz/dal/mysql",
        "//app/pipelinerpc/biz/pkg/idgen",
        "//app/pipelinerpc/domain/entity",
        "//app/pipelinerpc/infra",
        "//idls/byted/devinfra/pipeline/dsl:dsl_go_proto",
        "//internal/pipeline/perror",
        "//libs/bits_err",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "pipeline_notification_test",
    srcs = [
        "notification_group_test.go",
        "notification_test.go",
        "utils_test.go",
    ],
    embed = [":pipeline_notification"],
    deps = [
        "//app/pipelinerpc/biz/dal/mysql",
        "//app/pipelinerpc/biz/pkg/idgen",
        "//app/pipelinerpc/domain/entity",
        "//app/pipelinerpc/testfactory",
        "//idls/byted/devinfra/pipeline/dsl:dsl_go_proto",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_pkg_errors//:errors",
        "@com_github_smartystreets_goconvey//convey",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_lang_gg//gslice",
    ],
)
