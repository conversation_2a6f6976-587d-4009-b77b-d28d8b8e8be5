package handler

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/config"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
)

func TestCancelPipelineRuns(t *testing.T) {
	t.Skip()
	config.InitBoeDependency()
	runHandler := NewPipelineRunHandler()
	ctx := context.Background()

	res, err := runHandler.OperatePipelineRunList(ctx, &servicepb.OperatePipelineRunListRequest{
		RunIds:       nil,
		PipelineId:   1842612736,
		GitRepo:      "",
		GitBranch:    "",
		YamlFilename: "",
		Username:     "liuyang.leon",
	})
	assert.Nil(t, err)
	fmt.Println(res)
}
