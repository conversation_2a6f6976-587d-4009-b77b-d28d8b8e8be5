package handler

import (
	"context"
	"sync"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/trigger"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/service/template"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/no_cancel_ctx"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	libUtil "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/triggerpb"
	"code.byted.org/devinfra/hagrid/pkg/leafboat"
	"code.byted.org/devinfra/hagrid/pkg/pipeline_event/operator_record_event"

	"github.com/pkg/errors"
)

// MaxArchiveCount 单次归档流水线的最大数量
const MaxArchiveCount = 50

func (p *PipelineHandler) ArchivePipeline(ctx context.Context, req *servicepb.ArchivePipelineRequest) (*servicepb.ArchivePipelineResponse, error) {
	if len(req.PipelineIds) == 0 || len(req.PipelineIds) > MaxArchiveCount {
		err := bits_err.PIPELINECOMMON.ErrInvalidInput.AddErrMsg("archive pipeline length should between 1 and 50")
		return &servicepb.ArchivePipelineResponse{Count: 0}, err
	}

	pipelines, _, err := infra.PipelineRepo().GetPipelineList(ctx, &infra.SearchPipelineOpts{
		PipelineIDs: req.PipelineIds,
	})
	if err != nil {
		return nil, err
	}

	_, err = trigger.GetClient().DisablePipelineTriggers(ctx, &triggerpb.DisablePipelineTriggersRequest{
		PipelineIds: req.PipelineIds,
		OperatedBy:  req.Username,
	})
	if err != nil {
		return nil, err
	}

	if gslice.Contains([]platformpb.PipelineArchivedType{platformpb.PipelineArchivedType_PIPELINE_ARCHIVED_TYPE_MANUAL, platformpb.PipelineArchivedType_PIPELINE_ARCHIVED_TYPE_SCHEDULED}, req.ArchivedType) {
		_, err := template.GetClient().UnbindPipelines(ctx, &platformpb.UnbindPipelinesRequest{
			PipelineIds: req.PipelineIds,
		})
		if err != nil {
			logs.CtxError(ctx, "failed to unbind pipelines with ids %v, error:%s", req.PipelineIds, err.Error())
			return nil, err
		}
	}

	err = infra.PipelineRepo().ArchivePipeline(ctx, req.PipelineIds, req.Username, entity.ArchivedTypeFromPB(req.ArchivedType))
	if err != nil {
		logs.CtxError(ctx, "failed to archive pipelines with ids %v, error:%s", req.PipelineIds, err.Error())
		return nil, err
	}

	// 发送操作记录事件
	for _, pipeline := range pipelines {
		p.pipelineEventService.SyncSendRecordEvent(ctx, operator_record_event.OperateEvent{
			EventType:   operator_record_event.EventType_Pipeline,
			PipelineId:  pipeline.PipelineID,
			Operator:    req.Username,
			OperateType: operator_record_event.OperateType_Pipeline_Archive,
			Params: &operator_record_event.OperateEventParams{
				SpaceId: pipeline.SpaceID,
			},
		})
	}

	var wg sync.WaitGroup
	for _, pplID := range req.PipelineIds {
		wg.Add(1)
		go func(pipelineID uint64) {
			defer wg.Done()
			err = p.CancelOngoingPipelineRuns(ctx, pipelineID, req.Username)
			if err != nil {
				logs.CtxError(ctx, "failed to cancel ongoing pipeline runs of pipeline id %d", pipelineID, err.Error())
				return
			}
		}(pplID)
	}
	wg.Wait()
	ctx2 := no_cancel_ctx.WithoutCancel(ctx)
	libUtil.SafeGoWithoutCopyCtx(ctx, func() {
		TeaDeletePipeline(ctx2, req)
	})
	return &servicepb.ArchivePipelineResponse{Count: int32(len(req.PipelineIds))}, nil
}

func (p *PipelineHandler) RecoverArchivedPipeline(ctx context.Context, req *servicepb.RecoverArchivedPipelineRequest) (*servicepb.RecoverArchivedPipelineResponse, error) {
	if len(req.PipelineIds) == 0 {
		return &servicepb.RecoverArchivedPipelineResponse{}, nil
	}

	pipelines, err := infra.PipelineRepo().GetArchivedPipelineByIDs(ctx, req.PipelineIds)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipelines with ids %v, error:%s", req.PipelineIds, err.Error())
		return nil, err
	}

	// no need to deal with triggers notifications and variables
	// they all existed there, and no need to do anything
	err = infra.PipelineRepo().RecoverPipeline(ctx, req.PipelineIds)
	if err != nil {
		logs.CtxError(ctx, "failed to recover pipelines with ids %v, error:%s", req.PipelineIds, err.Error())
		return nil, err
	}

	if !req.FromTemplate {
		_, err := template.GetClient().UnbindPipelines(ctx, &platformpb.UnbindPipelinesRequest{
			PipelineIds: req.PipelineIds,
		})
		if err != nil {
			logs.CtxError(ctx, "failed to unbind pipelines with ids %v, error:%s", req.PipelineIds, err.Error())
			return nil, err
		}
	}

	succeededPipelines := make([]*platformpb.RecoverArchivedPipelineResult, 0, len(pipelines))
	for _, ppl := range pipelines {
		succeededPipelines = append(succeededPipelines, &platformpb.RecoverArchivedPipelineResult{
			PipelineId:   ppl.PipelineID,
			PipelineName: ppl.Name,
			PipelineUrl:  leafboat.GetPipelineUrl(ppl),
		})

		// 发送操作记录事件
		p.pipelineEventService.SyncSendRecordEvent(ctx, operator_record_event.OperateEvent{
			EventType:   operator_record_event.EventType_Pipeline,
			PipelineId:  ppl.PipelineID,
			Operator:    req.Username,
			OperateType: operator_record_event.OperateType_Pipeline_Recover,
			Params: &operator_record_event.OperateEventParams{
				SpaceId: ppl.SpaceID,
			},
		})
	}

	return &servicepb.RecoverArchivedPipelineResponse{
		SucceededPipelines: succeededPipelines,
		FailedPipelines:    make([]*platformpb.RecoverArchivedPipelineResult, 0),
	}, nil
}

func (p *PipelineHandler) CancelOngoingPipelineRuns(ctx context.Context, pipelineID uint64, username string) error {
	ongoingRuns, blockingRunCount, err := p.pipelineRunHandler.getPipelineRunsAndBlockCount(ctx, pipelineID)
	if err != nil {
		return err
	}

	if blockingRunCount > 0 {
		err = infra.PipelineRunRepo().UpdateBlockingPipelineRunStatus(ctx, pipelineID, entity.PipelineRunStatusCancelled)
		if err != nil {
			return err
		}
	}
	if len(ongoingRuns) == 0 {
		return nil
	}
	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	for _, ongoingRun := range ongoingRuns {
		wg.Add(1)
		go func(run *entity.PipelineRun) {
			defer wg.Done()
			if canCancel := p.pipelineRunService.IsCancelOperationAllowed(run); !canCancel {
				logs.CtxInfo(ctx, "pipeline[%d] run[%d] status[%s] can't cancel", pipelineID, run.RunId, run.Status.String())
				return
			}
			if err := engine.Client.CancelOrcaPipeline(ctx, run, username); err != nil {
				logs.CtxError(ctx, "cancel engine run with id %d failed, error: %s", run.EngineRunId, err)
				errChan <- errors.Errorf("engine CancelPipeline failed, error: %v", err)
				return
			}
			if err := infra.PipelineRunRepo().UpdatePipelineRunStatus(ctx, run.RunId, entity.PipelineRunStatusCancelling); err != nil {
				logs.CtxError(ctx, "update pipeline run db status failed, error: %s", err)
				errChan <- errors.Errorf("update pipeline run db status failed, error: %v", err)
				return
			}
		}(ongoingRun)
	}
	wg.Wait()
	close(errChan)
	if err = <-errChan; err != nil {
		logs.CtxError(ctx, "cancel pipeline runs failed, error: %s", err)
		return err
	}
	return nil
}
