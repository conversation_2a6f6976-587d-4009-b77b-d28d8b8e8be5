package handler

import (
	"context"

	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
	enginesdk "code.byted.org/pipeline/go-sdk"

	"code.byted.org/devinfra/hagrid/app/pipelinerpc/biz/pkg/engine"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/domain/entity"
	"code.byted.org/devinfra/hagrid/app/pipelinerpc/infra"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/timeutil"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/sharedpb"
)

func (p *PipelineRunHandler) GetPipelineConcurrency(ctx context.Context, req *servicepb.GetPipelineConcurrencyRequest) (*servicepb.GetPipelineConcurrencyResponse, error) {
	var (
		pipelineRuns         = make([]*platformpb.PipelineRun, 0)
		blockingPipelineRuns = make([]*platformpb.PipelineRun, 0)
		ongoingPipelineRuns  = make([]*platformpb.PipelineRun, 0)
		concurrency          *dslpb.Concurrency
	)
	// 根据 pipelineId 获取数据中存在的 pipeline 信息
	pipeline, err := infra.PipelineRepo().GetPipeline(ctx, req.PipelineId)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline data, error:%s", err.Error())
		return nil, err
	}
	concurrency = &dslpb.Concurrency{
		Max:         pipeline.Concurrency,
		NewRunFirst: pipeline.NewRunFirst,
	}
	pipelineRunList, runCount, err := infra.PipelineRunRepo().GetPipelineRunList(ctx, &infra.ListPipelineRunOpts{
		PipelineId: req.PipelineId,
		Statuses:   entity.GetPipelineRunOngoingAndBlockingStatus(),
		OrderBy:    "run_seq",
		Limit:      req.PageSize,
		Offset:     (req.PageNum - 1) * req.PageSize,
	})
	engineRuns, err := engine.Client.BatchGetOrcaPipelineRunStatus(ctx, pipelineRunList)
	if err != nil {
		logs.CtxError(ctx, "failed to BatchGetOrcaPipelineRunStatus, error:%s", err.Error())
		return nil, err
	}
	engineRunsMap := gslice.ToMap(engineRuns, func(t *enginesdk.Pipeline) (int64, *enginesdk.Pipeline) {
		return t.ID, t
	})
	blockingPipelineRunCount, err := infra.PipelineRunRepo().GetPipelineRunCountByPipelineIDAndStatus(ctx, req.PipelineId, entity.PipelineRunStatusBlocking)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline run count, error:%s", err.Error())
		return nil, err
	}
	ongoingPipelineRunCount, err := infra.PipelineRunRepo().GetPipelineRunCountByPipelineIDAndStatus(ctx, req.PipelineId, entity.GetPipelineRunOngoingStatus()...)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline run count, error:%s", err.Error())
		return nil, err
	}
	for _, pipelineRun := range pipelineRunList {
		if engineRun, ok := engineRunsMap[pipelineRun.EngineRunId]; ok {
			engineRunStatus, err := entity.GetPipelineRunStatusFromSDK(engineRun.Status)
			if err != nil {
				return nil, err
			}
			pipelineRun.Status = engineRunStatus
		}
		run := &platformpb.PipelineRun{
			PipelineId:  pipelineRun.PipelineId,
			RunId:       pipelineRun.RunId,
			RunSeq:      pipelineRun.RunSeq,
			RunStatus:   pipelineRun.Status.ToPB(),
			Note:        pipelineRun.Note,
			CreatedAt:   timeutil.TimeToString(pipelineRun.CreatedAt),
			CreatedBy:   pipelineRun.CreatedBy,
			TimeCostSec: utils.GetRunTimeCost(pipelineRun.Status.IsFinalStatus(), pipelineRun.StartedAt, pipelineRun.CompletedAt),
			TriggerInfo: &platformpb.TriggerInfo{
				TriggeredBy: pipelineRun.CreatedBy,
				TriggeredAt: timeutil.TimeToString(pipelineRun.CreatedAt),
				TriggerType: pipelineRun.TriggerType.ToPB(),
			},
		}
		pipelineRuns = append(pipelineRuns, run)
		if pipelineRun.Status == entity.PipelineRunStatusBlocking {
			blockingPipelineRuns = append(blockingPipelineRuns, run)
		}
		if gslice.Contains(entity.GetPipelineRunOngoingStatus(), pipelineRun.Status) {
			ongoingPipelineRuns = append(ongoingPipelineRuns, run)
		}
	}

	return &servicepb.GetPipelineConcurrencyResponse{
		BlockingPipelineRuns: blockingPipelineRuns,
		OngoingPipelineRuns:  ongoingPipelineRuns,
		PipelineRuns:         pipelineRuns,
		Pagination: &sharedpb.Pagination{
			Total:    int32(runCount),
			PageNum:  req.PageNum,
			PageSize: req.PageSize,
		},
		Concurrency:              concurrency,
		BlockingPipelineRunCount: blockingPipelineRunCount,
		OngoingPipelineRunCount:  ongoingPipelineRunCount,
	}, nil
}

func (p *PipelineRunHandler) getPipelineRunsAndBlockCount(ctx context.Context, pipelineID uint64) ([]*entity.PipelineRun, int64, error) {
	// 根据 pipelineId 获取数据中存在的 pipeline Run 信息
	blockingPipelineCount, err := infra.PipelineRunRepo().GetPipelineRunCountByPipelineIDAndStatus(ctx, pipelineID, entity.PipelineRunStatusBlocking)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline run list, error:%s", err.Error())
		return nil, 0, err
	}
	ongoingRealRuns := make([]*entity.PipelineRun, 0)
	ongoingPipelineRuns, err := infra.PipelineRunRepo().GetPipelineRunsByPipelineIDAndStatus(ctx, pipelineID, entity.GetPipelineRunOngoingStatus()...)
	if err != nil {
		logs.CtxError(ctx, "failed to get pipeline run list, error:%s", err.Error())
		return nil, 0, err
	}
	engineRuns, err := engine.Client.BatchGetOrcaPipelineRunStatus(ctx, ongoingPipelineRuns)
	if err != nil {
		logs.CtxError(ctx, "failed to BatchGetOrcaPipelineRunStatus, error:%s", err.Error())
		return nil, 0, err
	}
	engineRunsMap := gslice.ToMap(engineRuns, func(t *enginesdk.Pipeline) (int64, *enginesdk.Pipeline) {
		return t.ID, t
	})
	for _, pipelineRun := range ongoingPipelineRuns {
		engineRun, ok := engineRunsMap[pipelineRun.EngineRunId]
		if !ok || pipelineRun.EngineRunId == 0 {
			ongoingRealRuns = append(ongoingRealRuns, pipelineRun)
			continue
		}
		engineRunStatus, err := entity.GetPipelineRunStatusFromSDK(engineRun.Status)
		if err != nil {
			return nil, 0, err
		}
		pipelineRun.Status = engineRunStatus
		if engineRunStatus.IsOngoingStatus() {
			ongoingRealRuns = append(ongoingRealRuns, pipelineRun)
		}
	}
	gslice.SortBy(ongoingRealRuns, func(run1 *entity.PipelineRun, run2 *entity.PipelineRun) bool {
		return run1.RunSeq > run2.RunSeq
	})
	return ongoingRealRuns, blockingPipelineCount, nil
}
