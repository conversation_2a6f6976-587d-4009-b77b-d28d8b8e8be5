package testfactory

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"google.golang.org/protobuf/encoding/protojson"
)

const ListAssignmentResp = "{\"assignments\":[{\"assignment_id\":\"2892569856\",\"assign_context\":{\"workspace_id\":\"1\"},\"assignments\":[{\"name\":\"custom.a\",\"value\":{\"json_array\":\"[1]\",\"raw_json\":\"[1]\"},\"def_snapshot\":{\"group_id\":\"2889036800\",\"version\":\"1\",\"name\":\"custom.a\",\"definition\":{\"name\":\"a\",\"kind\":4,\"default_value\":{\"json_array\":\"[1]\",\"raw_json\":\"[1]\"},\"full_name\":\"custom.a\",\"need_load_when_render\":true},\"created_by\":\"qiaozhi.george\"},\"def_snapshot_key\":\"group:{2889036800}:v1:var:custom.a\",\"created_by\":\"qiaozhi.george\",\"created_at\":\"1673775759\"}],\"created_by\":\"qiaozhi.george\",\"created_at\":\"1673775759\"}],\"flatten_values\":[{\"name\":\"custom.a\",\"value\":{\"json_array\":\"[1]\",\"raw_json\":\"[1]\"},\"def_snapshot\":{\"group_id\":\"2889036800\",\"version\":\"1\",\"name\":\"custom.a\",\"definition\":{\"name\":\"a\",\"kind\":4,\"default_value\":{\"json_array\":\"[1]\",\"raw_json\":\"[1]\"},\"full_name\":\"custom.a\",\"need_load_when_render\":true},\"created_by\":\"qiaozhi.george\"},\"def_snapshot_key\":\"group:{2889036800}:v1:var:custom.a\",\"created_by\":\"qiaozhi.george\",\"created_at\":\"1673775759\"}]}"

func GetAssignments() []*varstorepb.VarAssignment {
	r := &varstorepb.ListAssignmentResp{}

	_ = protojson.Unmarshal([]byte(ListAssignmentResp), r)
	return r.FlattenValues
}

func GetAssignments2() []*varstorepb.VarAssignment {
	return []*varstorepb.VarAssignment{
		{
			Name: "custom.bbb",
			Value: &varstorepb.VarValue{
				Value: &varstorepb.VarValue_Text{
					Text: "old",
				},
				RawJson: "old",
			},
			DefSnapshot: &varstorepb.VarDefSnapshot{
				Name: "custom.bb",
				Definition: &varstorepb.VarDefinition{
					Kind: varstorepb.VarKind_VAR_KIND_STRING,
				},
			},
			CreatedBy: "",
			CreatedAt: 0,
		},
	}
}
