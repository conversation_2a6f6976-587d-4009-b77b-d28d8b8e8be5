package event

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/businessevent/pkg/service/notification"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/gopkg/logs/v2/log"
)

func HandleDevV1StateEvent(ctx context.Context, event *events.DevV1StateEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevV1StateEvent").KVs("event", event).Emit()
	err := SendUpdateDevTaskWorkflow(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateDevTaskWorkflow error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleDevV1StateEvent succeed").Emit()
	return nil
}

func HandleCheckPointCheckStateChange(ctx context.Context, event *events.GatekeeperCheckPointCheckStateChangeEvent) error {
	log.V2.Info().With(ctx).Str("HandleCheckPointCheckStateChange").KVs("event", event).Emit()
	err := SendUpdateCheckPointCheckState(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateCheckPointCheckState error: ", err.Error()).Emit()
	}
	return nil
}

func HandleReleaseTicketStageStatusChange(ctx context.Context, event *events.ReleaseTicketStageStatusEvent) error {
	log.V2.Info().With(ctx).Str("HandleReleaseTicketStageStatusChange").KVs("event", event).Emit()
	err := SendUpdateReleaseTicketStageStatus(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("HandleReleaseTicketStageStatusChange error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleReleaseTicketStageStatusChange succeed").Emit()
	return nil
}

func HandleReleaseTicketExecutionTaskStatus(ctx context.Context, event *events.ReleaseTicketExecutionTaskStatusEvent) error {
	log.V2.Info().With(ctx).Str("HandleReleaseTicketExecutionTaskStatus").KVs("event", event).Emit()
	err := SendReleaseTicketExecutionTaskStatus(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendReleaseTicketExecutionTaskStatus error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleReleaseTicketExecutionTaskStatus succeed").Emit()
	return nil
}

func HandleReleaseTicketPipelineStatusV2(ctx context.Context, event *events.ReleaseTicketPipelineStatusEventV2) error {
	log.V2.Info().With(ctx).Str("HandleReleaseTicketExecutionTaskStatus").KVs("event", event).Emit()
	err := SendReleaseTicketPipelineStatusV2(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendReleaseTicketExecutionTaskStatus error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleReleaseTicketExecutionTaskStatus succeed").Emit()
	return nil
}

func HandleReleaseTicketPipelineRerunChange(ctx context.Context, event *events.ReleaseTicketPipelineRerunChangeEvent) error {
	log.V2.Info().With(ctx).Str("HandleReleaseTicketExecutionTaskStatus").KVs("event", event).Emit()
	err := SendReleaseTicketPipelineRerunChange(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendReleaseTicketExecutionTaskStatus error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleReleaseTicketExecutionTaskStatus succeed").Emit()
	return nil
}

func HandleReleaseTicketProjectStageStatusChange(ctx context.Context, event *events.ReleaseTicketProjectStageStatusChangeEvent) error {
	log.V2.Info().With(ctx).Str("HandleReleaseTicketProjectStageStatusChange").KVs("event", event).Emit()
	err := SendReleaseTicketProjectStageStatusChange(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendReleaseTicketProjectStageStatusChange error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleReleaseTicketProjectStageStatusChange succeed").Emit()
	return nil
}

func HandleReleaseTicketBranchBackWard(ctx context.Context, event *events.ReleaseTicketBranchBackWardEvent) error {
	log.V2.Info().With(ctx).Str("HandleReleaseTicketBranchBackWard").KVs("event", event).Emit()
	err := SendReleaseTicketBranchBackWard(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendReleaseTicketBranchBackWard error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("HandleReleaseTicketBranchBackWard succeed").Emit()
	return nil
}

func HandleReleaseTicketBackgroundTaskError(ctx context.Context, event *events.ReleaseTicketBackgroundTaskErrorEvent) error {
	log.V2.Info().With(ctx).Str("ReleaseTicketBackgroundTaskError").KVs("event", event).Emit()
	err := SendReleaseTicketBackgroundTaskError(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("ReleaseTicketBackgroundTaskError error: ", err.Error()).Emit()
	}
	log.V2.Info().With(ctx).Str("ReleaseTicketBackgroundTaskError succeed").Emit()
	return nil
}

func HandleDevTaskChangesUpdateEvent(ctx context.Context, event *events.DevTaskChangesUpdateEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevTaskChangesUpdateEvent").KVs("event", event).Emit()
	err := SendDevChangeListUpdate(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateDevTaskChanges error: ", err.Error()).Emit()
	}
	return nil
}

// DevTaskEvent
func HandleDevTaskEvent(ctx context.Context, event *events.DevTaskEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevTaskEvent").KVs("event", event).Emit()
	go func() {
		if err := DevTaskStatusChangeEvent(ctx, event); err != nil {
			log.V2.Error().With(ctx).Str("SendUpdateDevTask failed").Error(err).Emit()
		}
	}()
	go func() {
		if err := notification.DoNotificationsByEvent(ctx, event); err != nil {
			log.V2.Error().With(ctx).Str("DoNotificationsByEvent failed").Error(err).Emit()
		}
	}()
	return nil
}

func HandleDevTaskCollaboratorsChangeEvent(ctx context.Context, event *events.DevTaskCollaboratorsChangeEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevTaskCollaboratorsChangeEvent").KVs("event", event).Emit()
	err := DevTaskCollaboratorsChangeEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateDevTaskCollaborators error: ", err.Error()).Emit()
	}
	return nil
}

func HandleIntegrationV1StateEvent(ctx context.Context, event *events.IntegrationV1StateEvent) error {
	log.V2.Info().With(ctx).Str("HandleIntegrationV1StateEvent").KVs("event", event).Emit()
	err := SendIntegrationInfoChangeEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateIntegrationV1State error: ", err.Error()).Emit()
	}
	return nil
}

func HandleIntegrationV1DevStateEvent(ctx context.Context, event *events.IntegrationV1DevStateEvent) error {
	log.V2.Info().With(ctx).Str("HandleIntegrationV1DevStateEvent").KVs("event", event).Emit()
	err := IntegrationDevTaskChangeEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateIntegrationV1DevState error: ", err.Error()).Emit()
	}
	return nil
}

// HandleDevTaskControlPlaneProject 处理开发任务多批次场景下项目状态变化
func HandleDevTaskControlPlaneProject(ctx context.Context, event *events.DevTaskControlPlaneProjectEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevTaskControlPlaneProject").KVs("event", event).Emit()
	err := DevTaskControlPlaneProjectEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("SendUpdateDevTaskControlPlaneProject error: ", err.Error()).Emit()
	}
	return nil
}

// HandleDevTaskChangeIntegration 处理开发任务与集成的关联关系变化
func HandleDevTaskChangeIntegration(ctx context.Context, event *events.DevTaskChangeIntegrationEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevTaskChangeIntegration").KVs("event", event).Emit()
	err := DevTaskChangeIntegrationEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("HandleDevTaskChangeIntegration error: ", err.Error()).Emit()
	}
	return nil
}

func HandleDevTaskWorkItem(ctx context.Context, event *events.DevTaskWorkItemsEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevTaskWorkItem").KVs("event", event).Emit()
	err := DevTaskWorkItemEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("HandleDevTaskWorkItem error: ", err.Error()).Emit()
	}
	return nil
}

func HandleDevChange(ctx context.Context, event *events.DevChangeEvent) error {
	log.V2.Info().With(ctx).Str("HandleDevChange").KVs("event", event).Emit()
	err := DevChangeEvent(ctx, event)
	if err != nil {
		log.V2.Error().With(ctx).Str("HandleDevChange error: ", err.Error()).Emit()
	}
	return nil
}
