package dbconv

import (
	"code.byted.org/devinfra/hagrid/libs/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	resources "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rollout/resourcespb"
	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"
)

func ConvertDeploymentFromPBToDB(deploymentPB *rollout.Deployment) (DeploymentDB, error) {
	resources := make([]ResourceDB, 0)
	for _, resource := range deploymentPB.GetResources() {
		spec, err := protojson.Marshal(resource.GetSpec())
		if err != nil {
			return DeploymentDB{}, err
		}
		metaDB := convertMetadataFromPBToDB(resource.GetMetadata())
		metaData := metaDB
		resources = append(resources, ResourceDB{
			Status:   resource.Status,
			Spec:     spec,
			Metadata: metaData,
		})
	}
	// make sure no invalid status in DB. It is better to initialize it at the beginning.
	status := deploymentPB.GetOverallStatus().GetStatus()
	if status == rollout.OverallStatus_INVALID_STATUS {
		status = rollout.OverallStatus_DEPLOYMENT_PENDING
	}
	overallStatus := OverallStatusDB{
		CreatedAt:     int64(deploymentPB.GetOverallStatus().GetCreatedAt()),
		LastUpdatedAt: int64(deploymentPB.GetOverallStatus().GetLastUpdatedAt()),
		Creator:       deploymentPB.GetOverallStatus().GetCreator(),
		LastUpdater:   deploymentPB.GetOverallStatus().GetLastUpdater(),
		RosStackIds:   deploymentPB.GetOverallStatus().GetRosStackIds(),
		Status:        int32(status),
		ErrMsg:        deploymentPB.GetOverallStatus().GetErrMsg(),
	}

	var customStrategyDB StrategyDB
	if deploymentPB.GetCustomStrategy() != nil {
		predeployStrategyByte, err := protojson.Marshal(deploymentPB.GetCustomStrategy().PreDeployStrategy)
		if err != nil {
			return DeploymentDB{}, err
		}
		customStrategyDB = StrategyDB{
			Name:              deploymentPB.GetCustomStrategy().GetName(),
			DeletionStrategy:  int32(deploymentPB.GetCustomStrategy().GetDeletionStrategy()),
			PreDeployStrategy: predeployStrategyByte,
		}
		if deploymentPB.GetCustomStrategy().PostDeployStrategy != nil {
			customStrategyDB.PostDeployStrategy = PostDeployStrategyDB{
				DisableRollback:     deploymentPB.GetCustomStrategy().PostDeployStrategy.DisableRollback,
				RollbackWaitMinutes: deploymentPB.GetCustomStrategy().PostDeployStrategy.RollbackWaitMinutes,
			}
		}
	}
	return DeploymentDB{
		DeploymentId:   deploymentPB.Id,
		Name:           deploymentPB.Name,
		Namespace:      deploymentPB.Namespace,
		Version:        deploymentPB.Version,
		OverallStatus:  overallStatus,
		Resources:      resources,
		BatchId:        deploymentPB.BatchId,
		PresetStrategy: deploymentPB.PresetStrategy,
		CustomStrategy: customStrategyDB,
		OpType:         deploymentPB.OpType,
	}, nil
}

func ConvertDeploymentFromDBToPB(deploymentDB DeploymentDB) (deploymentPB *rollout.Deployment, err error) {
	resources := make([]*rollout.Resource, 0)
	if deploymentDB.Resources != nil {
		for _, resource := range deploymentDB.Resources {
			metadataPB := convertMetadataFromDBToPB(resource.Metadata)
			resourcePB := &rollout.Resource{
				Status:   resource.Status,
				Spec:     &rollout.Spec{},
				Metadata: metadataPB,
			}

			err = protojson.Unmarshal(resource.Spec, resourcePB.Spec)
			if err != nil {
				return nil, errors.Wrapf(err, "failed to unmarshal from db resource spec to pb resource spec in deployment %s", deploymentDB.Name)
			}

			resources = append(resources, resourcePB)
		}
	}

	var customStrategyPB *rollout.Strategy
	if deploymentDB.CustomStrategy.Name != "" {
		predeployStrategy := &rollout.PreDeployStrategy{}
		err := protojson.Unmarshal(deploymentDB.CustomStrategy.PreDeployStrategy, predeployStrategy)
		if err != nil {
			return nil, errors.Wrapf(err, "failed to unmarshal from db to pb strategy in deployment %s", deploymentDB.Name)
		}
		customStrategyPB = &rollout.Strategy{
			Name:              deploymentDB.CustomStrategy.Name,
			DeletionStrategy:  rollout.DeletionStrategy(deploymentDB.CustomStrategy.DeletionStrategy),
			PreDeployStrategy: predeployStrategy,
		}
		customStrategyPB.PostDeployStrategy = &rollout.PostDeployStrategy{
			DisableRollback:     deploymentDB.CustomStrategy.PostDeployStrategy.DisableRollback,
			RollbackWaitMinutes: deploymentDB.CustomStrategy.PostDeployStrategy.RollbackWaitMinutes,
		}
	}

	return &rollout.Deployment{
		OverallStatus: &rollout.OverallStatus{
			CreatedAt:     uint64(deploymentDB.OverallStatus.CreatedAt),
			LastUpdatedAt: uint64(deploymentDB.OverallStatus.LastUpdatedAt),
			Creator:       deploymentDB.OverallStatus.Creator,
			LastUpdater:   deploymentDB.OverallStatus.LastUpdater,
			RosStackIds:   deploymentDB.OverallStatus.RosStackIds,
			Status:        rollout.OverallStatus_DeploymentStatus(deploymentDB.OverallStatus.Status),
			ErrMsg:        deploymentDB.OverallStatus.ErrMsg,
		},
		Id:             deploymentDB.DeploymentId,
		Name:           deploymentDB.Name,
		Namespace:      deploymentDB.Namespace,
		Version:        deploymentDB.Version,
		Resources:      resources,
		BatchId:        deploymentDB.BatchId,
		PresetStrategy: deploymentDB.PresetStrategy,
		CustomStrategy: customStrategyPB,
		OpType:         deploymentDB.OpType,
	}, nil
}

func ConvertVarchFromDBToPB(varchDB VarchDB) (*rollout.Resource, error) {
	metaPB := convertMetadataFromDBToPB(varchDB.Metadata)
	if rollout.ResourceType(varchDB.Metadata.Type) == rollout.ResourceType_VDC_CONFIG {
		template := &resources.VDCConfig{
			Vdc: rollout.VDC(varchDB.Vdc),
		}

		anyTmpl, nErr := anypb.New(template)
		if nErr != nil {
			return nil, errors.Wrap(nErr, "failed to create anypb template")
		}

		return &rollout.Resource{
			Metadata: metaPB,
			Spec: &rollout.Spec{
				Template: anyTmpl,
			},
		}, nil
	} else if rollout.ResourceType(varchDB.Metadata.Type) == rollout.ResourceType_VREGION_CONFIG {
		template := &resources.VRegionConfig{
			Vregion: rollout.VRegion(varchDB.Vregion),
		}

		anyTmpl, nErr := anypb.New(template)
		if nErr != nil {
			return nil, errors.Wrap(nErr, "failed to create anypb template")
		}

		return &rollout.Resource{
			Metadata: metaPB,
			Spec: &rollout.Spec{
				Template: anyTmpl,
			},
		}, nil
	} else {
		template := &resources.VGeoConfig{
			Vgeo: rollout.VGeo(varchDB.Vgeo),
		}

		anyTmpl, nErr := anypb.New(template)
		if nErr != nil {
			return nil, errors.Wrap(nErr, "failed to create anypb template")
		}

		return &rollout.Resource{
			Metadata: metaPB,
			Spec: &rollout.Spec{
				Template: anyTmpl,
			},
		}, nil
	}
}

func ConvertVarchFromPBToDB(varchPB *rollout.Resource) (VarchDB, error) {
	metaDB := convertMetadataFromPBToDB(varchPB.Metadata)
	if varchPB.Metadata.Type == rollout.ResourceType_VDC_CONFIG {
		vdcConf := new(resources.VDCConfig)
		if uErr := anypb.UnmarshalTo(varchPB.Spec.Template, vdcConf, proto.UnmarshalOptions{}); uErr != nil {
			return VarchDB{}, errors.Wrap(uErr, "failed to unmarshal to vdc config")
		}

		return VarchDB{
			Metadata: metaDB,
			Vdc:      int32(vdcConf.Vdc),
		}, nil
	} else if varchPB.Metadata.Type == rollout.ResourceType_VREGION_CONFIG {
		vregionConf := new(resources.VRegionConfig)
		if uErr := anypb.UnmarshalTo(varchPB.Spec.Template, vregionConf, proto.UnmarshalOptions{}); uErr != nil {
			return VarchDB{}, errors.Wrap(uErr, "failed to unmarshal to vregion config")
		}

		return VarchDB{
			Metadata: metaDB,
			Vregion:  int32(vregionConf.Vregion),
		}, nil
	} else {
		vgeoConf := new(resources.VGeoConfig)
		if uErr := anypb.UnmarshalTo(varchPB.Spec.Template, vgeoConf, proto.UnmarshalOptions{}); uErr != nil {
			return VarchDB{}, errors.Wrap(uErr, "failed to unmarshal to vgeo config")
		}

		return VarchDB{
			Metadata: metaDB,
			Vgeo:     int32(vgeoConf.Vgeo),
		}, nil
	}
}

func ConvertNamespaceFromDBToPB(namespaceDB NamespaceDB) (*rollout.Resource, error) {
	metaPB := convertMetadataFromDBToPB(namespaceDB.Metadata)

	vdcList := make([]rollout.VDC, 0)
	for _, vdc := range namespaceDB.VdcList {
		vdcList = append(vdcList, rollout.VDC(rollout.VDC_value[vdc]))
	}
	vregionList := make([]rollout.VRegion, 0)
	for _, vregion := range namespaceDB.VregionList {
		vregionList = append(vregionList, rollout.VRegion(rollout.VRegion_value[vregion]))
	}
	vgeoList := make([]rollout.VGeo, 0)
	for _, vgeo := range namespaceDB.VgeoList {
		vgeoList = append(vgeoList, rollout.VGeo(rollout.VGeo_value[vgeo]))
	}
	namespace := &resources.Namespace{
		VdcList:     vdcList,
		VregionList: vregionList,
		VgeoList:    vgeoList,
	}
	anyTmpl, nErr := anypb.New(namespace)
	if nErr != nil {
		return nil, errors.Wrap(nErr, "failed to create anypb template")
	}
	return &rollout.Resource{
		Metadata: metaPB,
		Spec: &rollout.Spec{
			Template: anyTmpl,
		},
	}, nil
}

func ConvertNamespaceFromPBToDB(namespacePB *rollout.Resource) (NamespaceDB, error) {
	metaDB := convertMetadataFromPBToDB(namespacePB.Metadata)
	namespace := new(resources.Namespace)
	if uErr := anypb.UnmarshalTo(namespacePB.GetSpec().GetTemplate(), namespace, proto.UnmarshalOptions{}); uErr != nil {
		return NamespaceDB{}, errors.Wrap(uErr, "failed to unmarshal to namespace")
	}
	vdcList := make([]string, 0)
	for _, vdc := range namespace.GetVdcList() {
		vdcList = append(vdcList, vdc.String())
	}
	vregionList := make([]string, 0)
	for _, vregion := range namespace.GetVregionList() {
		vregionList = append(vregionList, vregion.String())
	}
	vgeoList := make([]string, 0)
	for _, vgeo := range namespace.GetVgeoList() {
		vgeoList = append(vgeoList, vgeo.String())
	}
	return NamespaceDB{
		Metadata:    metaDB,
		VgeoList:    vgeoList,
		VregionList: vregionList,
		VdcList:     vdcList,
	}, nil
}

func convertMetadataFromDBToPB(metaDB MetadataDB) *rollout.Metadata {
	psmCopy := metaDB.Psm
	psmPtr := &psmCopy
	if psmCopy == "" {
		psmPtr = nil
	}
	bpCopy := metaDB.BytetreeParent
	bpPtr := &bpCopy
	if bpCopy == 0 {
		bpPtr = nil
	}
	addonPtr := &anypb.Any{
		TypeUrl: metaDB.AddOn.TypeUrl,
		Value:   metaDB.AddOn.Value,
	}
	if metaDB.AddOn.TypeUrl == "" && len(metaDB.AddOn.Value) == 0 {
		addonPtr = nil
	}
	return &rollout.Metadata{
		LogicalID:      metaDB.LogicalID,
		Namespace:      metaDB.Namespace,
		Name:           metaDB.Name,
		Type:           rollout.ResourceType(metaDB.Type),
		Psm:            psmPtr,
		BytetreeParent: bpPtr,
		Description:    metaDB.Description,
		Labels:         metaDB.Labels,
		Tags:           metaDB.Tags,
		Dependencies:   metaDB.Dependencies,
		AddOn:          addonPtr,
	}
}

func convertMetadataFromPBToDB(metaPB *rollout.Metadata) MetadataDB {
	return MetadataDB{
		LogicalID:      metaPB.GetLogicalID(),
		Namespace:      metaPB.GetNamespace(),
		Name:           metaPB.GetName(),
		Type:           int64(metaPB.GetType()),
		Psm:            metaPB.GetPsm(),
		BytetreeParent: metaPB.GetBytetreeParent(),
		Description:    metaPB.Description,
		Labels:         metaPB.GetLabels(),
		Tags:           metaPB.GetTags(),
		Dependencies:   metaPB.GetDependencies(),
		AddOn: AnyDB{
			TypeUrl: metaPB.GetAddOn().GetTypeUrl(),
			Value:   metaPB.GetAddOn().GetValue(),
		},
	}
}

func ConvertLockDataToDB(input *rollout.LockData) (*LockDataDB, error) {
	return &LockDataDB{
		Psm:          input.Psm,
		ResourceType: input.ResourceType,
		BytetreeNode: input.BytetreeNode,
		PolicyId:     input.PolicyId,
		CreateTime:   input.CreateTime,
		Vgeo:         input.Vgeo,
	}, nil
}
