load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "tccconv",
    srcs = [
        "tcc_batch_rel_cfg_conv.go",
        "tcc_config_conv.go",
        "tcc_service_conv.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/utils/deployment_convertor/resource_convertor/tccconv",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutservices/utils/deployment_convertor/constvar",
        "//app/rolloutservices/utils/deployment_convertor/resource_convertor/util/metaconv",
        "//app/rolloutservices/utils/deployment_convertor/resource_convertor/util/ptrconv",
        "//app/rolloutservices/utils/nsutil",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "//idls/byted/devinfra/iac/rollout/resources:resources_go_proto",
        "//idls/byted/devinfra/rollout:rollout_go_proto",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "//libs/errors",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/anypb",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)

go_test(
    name = "tccconv_test",
    srcs = [
        "tcc_batch_rel_cfg_conv_test.go",
        "tcc_config_conv_test.go",
        "tcc_service_conv_test.go",
    ],
    embed = [":tccconv"],
    deps = [
        "//app/rolloutservices/utils/parser",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "//idls/byted/devinfra/rollout:rollout_go_proto",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_stretchr_testify//assert",
        "@org_golang_google_protobuf//proto",
    ],
)
