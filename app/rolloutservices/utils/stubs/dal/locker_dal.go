package dal

import (
	"context"
	"fmt"
	"sync"
	"time"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error"

	"code.byted.org/bytedoc/mongo-go-driver/bson"
	"code.byted.org/bytedoc/mongo-go-driver/mongo"
	"code.byted.org/bytedoc/mongo-go-driver/mongo/options"
	bd "code.byted.org/devinfra/hagrid/app/rolloutservices/utils/stubs/dal/bytedoc"
	"code.byted.org/devinfra/hagrid/libs/errors"
	"code.byted.org/devinfra/hagrid/libs/routinerecover"
	"code.byted.org/gopkg/logs/v2/log"
)

const (
	DefaultLockExpiration = 20 * time.Second
)

type LockerDoc struct {
	ID        string    `json:"_id,omitempty"`
	LockedAt  time.Time `json:"locked_at,omitempty"`
	ExpiresAt time.Time `json:"expires_at,omitempty"`
	DocID     string    `json:"doc_id,omitempty"` // the corresponding doc ID
	Owner     string    `json:"owner,omitempty"`
}

type PaginatedResult struct {
	PageSize   uint32       `json:"page_size"`
	PageIndex  uint32       `json:"page_index"`
	TotalCount uint32       `json:"total_count"`
	QueryAt    time.Time    `json:"query_at"`
	Docs       []*LockerDoc `json:"docs"`
}

type LockerInterface interface {
	// Acquire a lock.
	// Note: doc.ExpiresAt - doc.Locked should be longer than or equal to auto extend duration.
	Acquire(ctx context.Context, doc *LockerDoc) error

	// Get a doc by id.
	// Return (nil, nil) if the record does not exist.
	Get(ctx context.Context, id string) (*LockerDoc, error)

	// Release a lock.
	// Returns (false, nil) if the doc does not exist
	Release(ctx context.Context, id string, docID string) (bool, error)

	GetPaginatedDocs(ctx context.Context, pageSize uint32, pageIndex uint32) (*PaginatedResult, error)
}

type DBLocker struct {
	name string
	coll *mongo.Collection

	autoExtendDuration time.Duration // Auto extend the expire time if it is under processing
	docsMutex          sync.Mutex
	ticker             *time.Ticker
	closing            chan bool
	closed             chan bool
	docs               []*LockerDoc
}

func (l *DBLocker) Acquire(ctx context.Context, doc *LockerDoc) error {
	l.docsMutex.Lock()
	defer l.docsMutex.Unlock()
	log.V2.Info().With(ctx).Str("DBLocker.Acquire()").KV("lock", doc.ID).Emit()
	mongoCtx, cancel := context.WithTimeout(ctx, bd.GetMongoConfig().Timeout)
	defer cancel()

	filter := bson.M{"_id": doc.ID,
		"expires_at": bson.M{"$lt": time.Now()}}
	update := bson.M{"$set": bson.M{
		"_id":        doc.ID,
		"locked_at":  doc.LockedAt,
		"expires_at": doc.ExpiresAt,
		"doc_id":     doc.DocID,
		"owner":      doc.Owner,
	}}
	opts := options.FindOneAndUpdate().SetUpsert(true)
	err := l.coll.FindOneAndUpdate(mongoCtx, filter, update, opts).Err()

	if err == nil || errors.Is(err, mongo.ErrNoDocuments) {
		l.docs = append(l.docs, doc)
		return nil
	} else if mongo.IsDuplicateKeyError(err) {
		return errors.StatusConflict.Newf("[%v] is locked already", doc.ID)
	} else {
		return std_error.StatusDbWrite.Wrapf(err, "Acquire [%v]", doc.ID)
	}
}

func (l *DBLocker) Get(ctx context.Context, id string) (*LockerDoc, error) {
	mongoCtx, cancel := context.WithTimeout(ctx, bd.GetMongoConfig().Timeout)
	defer cancel()
	doc := LockerDoc{}
	err := l.coll.FindOne(mongoCtx, bson.M{"_id": id}).Decode(&doc)
	if err == nil {
		return &doc, nil
	} else if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, nil
	} else {
		return nil, errors.Wrapf(err, "Get [%v]", id)
	}
}

func (l *DBLocker) Release(ctx context.Context, id string, docID string) (bool, error) {
	l.docsMutex.Lock()
	defer l.docsMutex.Unlock()
	log.V2.Info().With(ctx).Str("DBLocker.Release()").KV("lock", id).KV("doc_id", docID).Emit()
	mongoCtx, cancel := context.WithTimeout(ctx, bd.GetMongoConfig().Timeout)
	defer cancel()

	filter := bson.M{"_id": id, "doc_id": docID}
	r, err := l.coll.DeleteOne(mongoCtx, filter)
	if err != nil {
		return false, errors.Wrapf(err, "Release {id: %v, doc_id: %v}", id, docID)
	}
	if r.DeletedCount == 0 {
		return false, nil
	}
	newDocs := make([]*LockerDoc, 0, len(l.docs))
	for _, doc := range l.docs {
		if doc.ID != id {
			newDocs = append(newDocs, doc)
		}
	}
	l.docs = newDocs
	return true, nil
}

func (l *DBLocker) GetPaginatedDocs(ctx context.Context, pageSize uint32, pageIndex uint32) (*PaginatedResult, error) {
	log.V2.Info().With(ctx).Str("DBLocker.GetPaginatedDocs()").KV("pageSize", pageSize).KV("pageIndex", pageIndex).Emit()
	mongoCtx, cancel := context.WithTimeout(ctx, bd.GetMongoConfig().Timeout)
	defer cancel()

	skip := pageIndex * pageSize
	pipeline := mongo.Pipeline{
		{{"$facet", bson.D{
			{"metadata", bson.A{bson.D{{"$count", "total_count"}}}},
			{"docs", bson.A{
				bson.D{{"$sort", bson.D{{"locked_at", 1}}}},
				bson.D{{"$skip", skip}},
				bson.D{{"$limit", pageSize}},
			}},
		},
		}},
	}

	cursor, err := l.coll.Aggregate(mongoCtx, pipeline)
	if err != nil {
		return nil, errors.Wrapf(err, "GetPaginatedDocs(pageSize=%v, pageIndex=%v) name=%v", pageSize, pageIndex, l.name)
	}
	defer func() { _ = cursor.Close(mongoCtx) }()

	type Metadata struct {
		TotalCount uint32 `json:"total_count"`
	}
	var result struct {
		Metadata []Metadata   `json:"metadata,omitempty"`
		Docs     []*LockerDoc `json:"docs"`
	}

	for cursor.Next(mongoCtx) {
		if err := cursor.Decode(&result); err != nil {
			return nil, errors.Wrapf(err, "Failed to retrieve paginated result(pageSize=%v, pageIndex=%v) name=%v", pageSize, pageIndex, l.name)
		}
	}
	if len(result.Metadata) == 0 {
		return nil, errors.Newf("No total_count found. GetPaginatedDocs(pageSize=%v, pageIndex=%v) name=%v", pageSize, pageIndex, l.name)
	}

	pr := &PaginatedResult{}
	pr.PageSize = pageSize
	pr.PageIndex = pageIndex
	pr.TotalCount = result.Metadata[0].TotalCount
	pr.QueryAt = time.Now()
	pr.Docs = result.Docs
	return pr, nil
}

func (l *DBLocker) extendExpiresAt(doc *LockerDoc) bool {
	mongoCtx, cancel := context.WithTimeout(context.Background(), bd.GetMongoConfig().Timeout)
	log.V2.Info().With(mongoCtx).Str("DBLocker.extendExpiresAt()").KV("lock", doc.ID).Emit()
	defer cancel()

	filter := bson.M{"_id": doc.ID}
	update := bson.M{"$set": bson.M{"expires_at": doc.ExpiresAt}}
	opts := options.FindOneAndUpdate().SetUpsert(false)
	sr := l.coll.FindOneAndUpdate(mongoCtx, filter, update, opts)
	err := sr.Err()
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			log.V2.Error().With(mongoCtx).Str("record does not exist").KV("lock", doc.ID).Emit()
			return false // This should not happen normally. Indicate we should remove it from the list.
		} else {
			log.V2.Error().With(mongoCtx).Str(fmt.Sprintf("failed to extend expires: %+v\n", err)).KV("lock", doc.ID).Emit()
			return true
		}
	} else {
		return true
	}
}

// deleteExpired deletes expired lock records.
// This should not happen normally.
// One case it could happen is, a worker dies before it releases the lock.
func (l *DBLocker) deleteExpired() {
	mongoCtx, cancel := context.WithTimeout(context.Background(), bd.GetMongoConfig().Timeout)
	defer cancel()
	dr, err := l.coll.DeleteMany(mongoCtx, bson.M{"expires_at": bson.M{"$lte": time.Now()}}, options.Delete())
	if err != nil {
		log.V2.Error().With(mongoCtx).Str(fmt.Sprintf("failed to delete expired records: %+v\n", err)).KV("locker", l.name).Emit()
	}
	if dr != nil && dr.DeletedCount >= 0 {
		log.V2.Info().With(mongoCtx).KV("locker", l.name).KV("delete_expired", dr.DeletedCount).Emit()
	}
}

// extend extends a lock.
// Expire time is hard to be predefined correctly.
// To prevent the locker is expired before the job is done, the worker will auto extend the expired time before releasing it.
// This is called every T = 0.4 autoExtendDuration.
// At the check time (CT), if expires_at < CT + 0.5 autoExtendDuration, update expires_at to expires_at + autoExtendDuration.
// In the worst case, expires_at = CT + 0.5 autoExtendDuration, no update.
// The next check time, expires_at = CT + 0.1 autoExtendDuration.
// If the cpu is too slow, deleteExpired() costs more than 0.1 autoExtendDuration, the record may be deleted.
// This should not happen in PROD because autoExtendDuration should be at 10 seonds level at least.
func (l *DBLocker) extend() {
	if len(l.docs) == 0 {
		return
	}
	l.docsMutex.Lock()
	defer l.docsMutex.Unlock()
	cutTime := time.Now().Add(time.Duration(float64(l.autoExtendDuration) * 0.5))
	newDocs := make([]*LockerDoc, 0, len(l.docs))
	for _, doc := range l.docs {
		if doc.ExpiresAt.Before(cutTime) {
			doc.ExpiresAt = doc.ExpiresAt.Add(l.autoExtendDuration)
			if l.extendExpiresAt(doc) {
				newDocs = append(newDocs, doc)
			}
		} else {
			newDocs = append(newDocs, doc)
		}
	}
	l.docs = newDocs
}

func (l *DBLocker) start() {
	for {
		select {
		case <-l.ticker.C:
			l.deleteExpired()
			l.extend()
		case <-l.closing:
			close(l.closed)
			return
		}
	}
}

func (l *DBLocker) Close() {
	l.ticker.Stop()
	close(l.closing)
	<-l.closed
}

type LockerOptions struct {
	autoExtendDuration time.Duration
}

type LockerOption func(opts *LockerOptions)

func WithAutoExtendDuration(d time.Duration) LockerOption {
	return func(opts *LockerOptions) {
		opts.autoExtendDuration = d
	}
}

// InitDBLocker initializes a DBLocker with the given collection/table name.
// Caller should call it once and reuse the result.
func InitDBLocker(name string, opt ...LockerOption) *DBLocker {
	opts := LockerOptions{
		autoExtendDuration: 10 * time.Second,
	}
	for _, o := range opt {
		o(&opts)
	}
	md := bd.GetMongoDB()
	tableName := bd.GetTableName(name)
	coll := md.Collection(tableName)
	dbLocker := &DBLocker{
		name:               tableName,
		coll:               coll,
		autoExtendDuration: opts.autoExtendDuration,
		docsMutex:          sync.Mutex{},
		ticker:             time.NewTicker(time.Duration(float64(opts.autoExtendDuration) * 0.4)),
		closing:            make(chan bool),
		closed:             make(chan bool),
	}
	names, err := coll.Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys: bson.D{{Key: "locked_at", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "expires_at", Value: 1}},
		},
		{
			Keys: bson.D{{Key: "doc_id", Value: 1}},
		},
	})
	if err != nil {
		panic(err)
	}
	log.V2.Info().With(context.Background()).Str("[create index]").
		KV("table name", tableName).
		KV("indices", names).Emit()

	routinerecover.SafeGo(func() {
		dbLocker.start()
	})
	return dbLocker
}
