load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "ros",
    srcs = ["client.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/utils/stubs/ros",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutservices/iac/sdk/pointifier",
        "//app/rolloutservices/iac/sdk/retry",
        "//app/rolloutservices/strategist/biz/config",
        "//app/rolloutservices/strategist/biz/utils",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "//libs/errors",
        "@com_github_bytedance_gopkg//cloud/metainfo",
        "@org_byted_code_gopkg_ctxvalues//:ctxvalues",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_ros_ros_sdk_go//client",
        "@org_byted_code_ros_ros_sdk_go//model",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "ros_test",
    srcs = ["client_test.go"],
    embed = [":ros"],
    deps = [
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_ros_ros_sdk_go//client",
        "@org_byted_code_ros_ros_sdk_go//model",
        "@org_golang_google_protobuf//proto",
    ],
)
