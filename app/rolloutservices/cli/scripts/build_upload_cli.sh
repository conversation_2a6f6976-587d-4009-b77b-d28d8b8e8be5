#!/bin/bash

set -e

need_upload=false
for file in $(git diff origin/main... --name-only)
do
  if [[ $file == projects/rollout/cli* ]]; then
    need_upload=true
  fi
done

if [[ "$need_upload" == false ]]; then
  echo "no need to upload cli, skipping..."
  exit 0
else
  echo "uploading..."
fi

RUN_NAME="rollout-cli"
go env
mkdir -p output
export GO111MODULE=on
go env -w GOPROXY="https://goproxy.byted.org|https://goproxy.cn|direct"
go env -w GOPRIVATE="*.byted.org,*.everphoto.cn,git.smartisan.com"
go env -w GOSUMDB="sum.golang.google.cn"
go mod download
go build -o output/${RUN_NAME}
# This is strange as the arch should be amd64 by default.
go build -o output/${RUN_NAME}-linux
export GOARCH=amd64
go build -o output/${RUN_NAME}-linux-amd64
export GOOS=darwin
go build -o output/${RUN_NAME}-mac-amd64
export GOARCH=arm64
go build -o output/${RUN_NAME}-mac-arm64

echo upload "data=@output/${RUN_NAME}" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}
curl -X PUT -H "By-Path: 1" --data-binary "@output/${RUN_NAME}" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}

echo "data=@output/${RUN_NAME}-mac-amd64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-mac
curl -X PUT -H "By-Path: 1" --data-binary "@output/${RUN_NAME}-mac-amd64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-mac

echo "data=@output/${RUN_NAME}-mac-amd64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-mac-amd64
curl -X PUT -H "By-Path: 1" --data-binary "@output/${RUN_NAME}-mac-amd64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-mac-amd64

echo "data=@output/${RUN_NAME}-mac-arm64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-mac-arm64
curl -X PUT -H "By-Path: 1" --data-binary "@output/${RUN_NAME}-mac-arm64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-mac-arm64

echo "data=@output/${RUN_NAME}-linux" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-linux
curl -X PUT -H "By-Path: 1" --data-binary "@output/${RUN_NAME}-linux" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-linux

echo "data=@output/${RUN_NAME}-linux-amd64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-linux-amd64
curl -X PUT -H "By-Path: 1" --data-binary "@output/${RUN_NAME}-linux-amd64" http://betmng.bytedance.net/obj/bet-result-sg/${RUN_NAME}-linux-amd64
