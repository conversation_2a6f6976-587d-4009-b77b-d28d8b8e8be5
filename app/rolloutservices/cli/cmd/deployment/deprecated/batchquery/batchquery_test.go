package batchquery

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/stubs/entry"
	"code.byted.org/devinfra/hagrid/libs/errors"
	entrypb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/hertz/entry"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/parser"
	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/cli/security"
)

var deployment = `id: eb6d96df-a148-4da6-89c6-fa704cd02eg1
name: example-deployment1
batchId: test_batch_id
resources:
  - metadata:
      bytetreeParent: "4776327"
      name: newiac_resource_importer
      psm: newiac.resource.importer
      type: TCE_SERVICE
    spec:
      template:
        '@type': type.googleapis.com/resources.TCEService
        permInfo:
          riskControls:
            - DELETE_CLUSTER
        serviceBuild:
          basedOnScm:
            baseImage: toutiao.debian:latest
            scmRepoInfo:
              - deploymentPath: /opt/tiger/toutiao/load
                name: toutiao/load
              - deploymentPath: /opt/tiger/toutiao/runtime
                name: toutiao/runtime
              - deploymentPath: /opt/tiger/iac/resource/importer
                mainRepo: true
                name: iac/resource/importer
        serviceRuntime:
          meetRuntimeSpec: false
          ports:
            - portNumber: 6789
              usage: PRIMARY
            - portNumber: 6790
              usage: DEBUG
          startupPath: /opt/tiger/iac/resource/importer/bootstrap.sh
        serviceToken:
          framework: kitex
          language: go
          serviceLevel: P2
          servicePurpose: regular
          serviceType: stateless_compute
      vgeoPlacements:
        vgeos:
          - VGEO_ROW
  - metadata:
      bytetreeParent: "4776327"
      dependencies:
        - newiac_resource_importer
      name: default
      psm: newiac.resource.importer
      type: TCE_CLUSTER
    spec:
      template:
        '@type': type.googleapis.com/resources.TCECluster
        advanced:
          availableZoneRebalance: true
          avx2SupportNeeded: false
          canBePreempted: false
          enableIpv6: true
          enableZtiToken: false
          historyConsulWeight: false
          ipv6OnlyCompatible: false
          mountJwtBundles: true
        clusterRuntime:
          canaryWeight: 10
          docker:
            cpu: 1
            gpu: 0
            mem: 2
          networkMode: BRIDGE
          prodWeight: 10
          replicas:
            canary: 0
            prod: 1
          scmRepos:
            - name: toutiao/load
              version: 1.0.2.499
            - name: toutiao/runtime
              version: 1.0.1.626
            - name: iac/resource/importer
              version: ********
        regionalRollingPolicy:
          canaryRollingGranularity: 100
          failureTolerance: 0
          prodRollingGranularity: 25
          rollingInterval: 10
          terminationGracePeriod: 30
        serviceMesh:
          aclEnable: false
          bytedocEgressTrafficProxy: false
          enableSidecarPercent: 100
          httpEgressTrafficProxy: false
          httpIngressTrafficProxy: false
          mysqlEgressTrafficProxy: false
          redisEgressTrafficProxy: false
          rpcEgressTrafficProxy: false
          rpcIngressTrafficProxy: false
      vdcPlacements:
        overrides:
          VDC_USEAST2A:
            propertyOverrides:
              clusterRuntime.replicas.prod: "2"
        vdcs:
          - VDC_MALIVA
          - VDC_USEAST2A
          - VDC_SG1
version: "1694639278"`

func TestBatchQueryDeployment(t *testing.T) {
	mockey.PatchConvey("TestBatchQueryDeployment", t, func() {
		mockey.Mock(security.GetUsernameAndJWT).Return("test_username", "test_jwt", nil).Build()
		rDep, pErr := parser.ConvertYamlToDeploymentPB(deployment)
		assert.NoError(t, pErr)
		mockey.Mock((*entry.DeployEntryServiceClient).GetBatchDeploy).Return(
			&entrypb.GetBatchDeployResp{Deployments: []*rollout.Deployment{rDep}}, nil, nil).Build()

		deployments, err := queryBatchDeployments(context.Background(), "test_batch_id")
		assert.NoError(t, err)
		assert.Equal(t, 1, len(deployments))
		assert.Equal(t, "example-deployment1", deployments[0].Name)
	})
}

func Test_calGDeploymentStatus(t *testing.T) {
	mockey.PatchConvey("Test_calGDeploymentStatus", t, func() {
		testCases := []struct {
			name           string
			mockReturn     []*rollout.Deployment
			mockErr        error
			expectedStatus string
		}{
			{
				name: "Test DeploymentStatusFailure",
				mockReturn: []*rollout.Deployment{
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_SUCCESS}},
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_FAILURE}},
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_INVALID_STATUS}},
				},
				mockErr:        nil,
				expectedStatus: DeploymentStatusFailure,
			},
			{
				name: "Test DeploymentStatusInProgress",
				mockReturn: []*rollout.Deployment{
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_SUCCESS}},
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_IN_PROGRESS}},
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_SUCCESS}},
				},
				mockErr:        nil,
				expectedStatus: DeploymentStatusInProgress,
			},
			{
				name: "Test DeploymentStatusSuccess",
				mockReturn: []*rollout.Deployment{
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_SUCCESS}},
					{Name: "test_deployment", OverallStatus: &rollout.OverallStatus{Status: rollout.OverallStatus_DEPLOYMENT_SUCCESS}},
				},
				mockErr:        nil,
				expectedStatus: DeploymentStatusSuccess,
			},
			{
				name:           "Test DeploymentStatusError",
				mockReturn:     []*rollout.Deployment{},
				mockErr:        errors.New("test error"),
				expectedStatus: DeploymentStatusError,
			},
		}

		mockey.Mock(security.GetUsernameAndJWT).Return("test_username", "test_jwt", nil).Build()
		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				gDeployment := &rollout.Deployment{Name: "test_deployment"}
				mock := mockey.Mock((*entry.DeployEntryServiceClient).GetDeploy).Return(
					&entrypb.GetDeployResp{RegionalDeployments: tc.mockReturn}, nil, tc.mockErr).Build()

				status := calGDeploymentStatus(context.Background(), gDeployment)

				assert.Equal(t, tc.expectedStatus, status)
				mock.Release()
			})
		}
	})

}
