package plan

import (
	"context"
	"time"

	"github.com/briandowns/spinner"
	"github.com/spf13/cobra"

	"code.byted.org/devinfra/hagrid/app/rolloutdeploymentrpc/pkg/specutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/cli/cmd/util"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/cli/rolloutcfg"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/comparator"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/parser"
	"code.byted.org/devinfra/hagrid/libs/errors"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/generatorpb"
	entrypb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/hertz/entry"
)

type DeploymentPlanOpts struct {
	DiffBranch string
}

func PlanCmd() *cobra.Command {
	opts := DeploymentPlanOpts{}
	cmd := &cobra.Command{
		Use:   "plan <deployment name>",
		Short: "Preview the change of a deployment between online and the one defined by SOTs in remote branch",
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			dn := args[0]
			dErr := Diff(context.Background(), dn, opts)
			if dErr != nil {
				return errors.Wrap(dErr, "failed to show diff")
			}

			return nil
		},
	}
	cmd.Flags().StringVarP(&opts.DiffBranch, "diff-branch", "d", "", "dry run the plan diff with a feature branch")
	return cmd
}

func Diff(ctx context.Context, dn string, opts DeploymentPlanOpts) error {
	ec := util.GetEntryClient()

	rcfgMgr, nErr := rolloutcfg.NewRolloutConfigMgr()
	if nErr != nil {
		return errors.Wrap(nErr, "failed to create rollout config manager")
	}

	ns, gnErr := rcfgMgr.GetCurrNamespace()
	if gnErr != nil {
		return errors.Wrap(gnErr, "failed to get current namespace")
	}

	s := spinner.New(spinner.CharSets[9], 100*time.Millisecond) // Choose a spinner character set and delay
	s.Start()
	defer s.Stop()
	resp, _, gbErr := ec.GenerateStandardDeployment(ctx, &generatorpb.GenerateStandardDeploymentRequest{
		Namespace:      ns,
		DeploymentName: dn,
		Branch:         opts.DiffBranch,
	})

	// TODO: poll for generate errors
	if gbErr != nil {
		return errors.Wrap(gbErr, "failed to generate batch deployment")
	}

	batchID := resp.GetRequestId()

	if pErr := PrintDiff(ctx, batchID); pErr != nil {
		return errors.Wrapf(pErr, "failed to show diff, deployment id = %s", batchID)
	}

	return nil
}

// PrintDiff queries dryrun service for up to 10 minutes and prints the diff between the online and the newly generated deployment.
func PrintDiff(ctx context.Context, batchID string) error {
	var diffResp *entrypb.GetPaginatedBatchDiffV2StrResponse
	var gErr2 error
	// timeout 10 min
	const maxRetries = 120
	retryInterval := 5 * time.Second

	ec := util.GetEntryClient()
	// Retry loop
	for i := 0; i < maxRetries; i++ {
		diffResp, _, gErr2 = ec.GetPaginatedBatchDiffV2(ctx, &entrypb.GetPaginatedBatchDiffV2Request{
			BatchId:   batchID,
			PageToken: "0",
			PageSize:  10,
		})
		if gErr2 == nil && len(diffResp.GetPageBatchDiff().GetDeploymentDiffs()) > 0 {
			break
		}

		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	if gErr2 != nil {
		return errors.Wrapf(gErr2, "failed to retrieve paginated diff for deployment id %s", batchID)
	}

	if diffResp == nil || diffResp.GetPageBatchDiff() == nil {
		return errors.Newf("response of paginated diff for deployment id %s is empty", batchID)
	}

	diffs := diffResp.GetPageBatchDiff().GetDeploymentDiffs()

	if len(diffs) == 0 {
		return errors.Wrapf(gErr2, "no deployment diffs returned for deployment id %s after %d retries", batchID, maxRetries)
	}

	diff := diffs[0]
	newDplStr := diff.GetNewDeploymentStr()
	onlineDplStr := diff.GetOnlineDeploymentStr()

	if newDplStr == "" {
		return errors.Newf("the newly generated deployment str is empty for deployment id %s", batchID)
	}

	newDpl, cErr1 := parser.ConvertYamlToDeploymentV2PB(newDplStr)
	if cErr1 != nil {
		return errors.Wrapf(cErr1, "failed to convert new deployment str to pb, new deployment str = %s",
			newDplStr)
	}

	n1Err := specutil.NormalizeDeploymentSpec(newDpl)
	if n1Err != nil {
		return errors.Wrapf(n1Err, "failed to normalize new deployment spec %s", newDplStr)
	}

	onlineDpl, cErr2 := parser.ConvertYamlToDeploymentV2PB(onlineDplStr)
	if cErr2 != nil {
		return errors.Wrapf(cErr2, "failed to convert new deployment str to pb, new deployment str = %s",
			newDplStr)
	}

	n2Err := specutil.NormalizeDeploymentSpec(onlineDpl)
	if n2Err != nil {
		return errors.Wrapf(n2Err, "failed to normalize online deployment spec %s", onlineDplStr)
	}

	dplDiff, cErr := comparator.ComputeDeploymentDiff(onlineDpl, newDpl)
	if cErr != nil {
		return errors.Wrap(cErr, "failed to ComputeDeploymentsDiff")
	}

	comparator.PrettyprintDeploymentDiff(dplDiff)

	return nil
}
