package importutil

import (
	"context"
	"fmt"
	"os"
	"time"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/cli/cmd/util"
	"code.byted.org/devinfra/hagrid/libs/errors"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/importerpb"
	"github.com/ghodss/yaml"
)

func LoopQueryImport(ctx context.Context, queryID string) (string, error) {
	ec := util.GetEntryClient()
	cnt := 0
	for {
		resp, _, qErr := ec.QueryImport(ctx, &importerpb.QueryImportRequest{
			QueryId: queryID,
		})
		if qErr != nil {
			return "", errors.Wrapf(qErr, "failed to query resource import progress, query id = %v", queryID)
		}

		if resp.Finished {
			return resp.Resources, nil
		}
		time.Sleep(time.Second)

		cnt++
		if cnt == 60 {
			return "", fmt.Errorf("import progress not complete after 60 seconds timeout")
		}
	}
}

func WriteSot(data map[string]string, outdir string) error {
	if len(outdir) == 0 {
		outdir, _ = os.Getwd()
	}
	for k, v := range data {
		yamlBytes, err := yaml.JSONToYAML([]byte(v))
		if err != nil {
			return err
		}
		fileHandle, err := os.Create(fmt.Sprint(outdir, "/", k, ".yaml"))
		if err != nil {
			return err
		}
		_, err = fileHandle.Write(yamlBytes)
		if err != nil {
			return err
		}
		fileHandle.Close()
	}
	return nil
}
