load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")
load("@rules_pkg//pkg:tar.bzl", "pkg_tar")
load("@rules_pkg//pkg:mappings.bzl", "pkg_attributes", "pkg_files", "strip_prefix")

go_library(
    name = "input_lib",
    srcs = [
        "main.go",
        "router.go",
        "router_gen.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/input",
    visibility = ["//visibility:private"],
    deps = [
        "//app/rolloutservices/input/biz/handler",
        "//app/rolloutservices/input/biz/router",
        "@org_byted_code_middleware_hertz//byted",
        "@org_byted_code_middleware_hertz//pkg/app/server",
        "@org_byted_code_temai_blame_helper//:blame_helper",
    ],
)

go_binary(
    name = "input",
    basename = "iac.rollout.input",
    embed = [":input_lib"],
    visibility = ["//visibility:public"],
)

pkg_tar(
    name = "pkg",
    srcs = [
        "script/bootstrap.sh",
        ":bin",
        ":conf",
    ],
)

pkg_files(
    name = "bin",
    srcs = [":input"],
    attributes = pkg_attributes(
        mode = "0755",
    ),
    prefix = "bin",
)

pkg_files(
    name = "conf",
    srcs = glob(["conf/*"]),
    prefix = "conf",
)
