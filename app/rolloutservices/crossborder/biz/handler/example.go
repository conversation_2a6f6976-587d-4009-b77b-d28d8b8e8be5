package handler

import (
	"context"
	"encoding/base64"
	"encoding/json"

	crossborder "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/hertz/crossborder"
	"code.byted.org/gopkg/logs/v2/log"
)

type ExampleRequest struct {
	Url  string `json:"url"`
	Body string `json:"body"`
}
type ExampleResponse struct {
	Status string `json:"status"`
	Url    string `json:"url"`
	Body   string `json:"body"`
}

func exampleHandler(ctx context.Context, req crossborder.GetCrossBorderRequest) string {
	decodedBytes, err := base64.URLEncoding.DecodeString(req.Data)
	if err != nil {
		log.V2.Error().With(ctx).Str("base64 decode failed").Emit()
	}
	var example ExampleRequest
	err = json.Unmarshal(decodedBytes, &example)
	if err != nil {
		log.V2.Error().With(ctx).Str("json unmarshal failed").Emit()
	}
	var resp ExampleResponse
	resp.Status = "success"
	resp.Url = example.Url
	resp.Body = example.Body
	respBytes, err := json.Marshal(resp)
	if err != nil {
		log.V2.Error().With(ctx).Str("json marshal failed").Emit()
	}
	return base64.URLEncoding.EncodeToString(respBytes)
}
