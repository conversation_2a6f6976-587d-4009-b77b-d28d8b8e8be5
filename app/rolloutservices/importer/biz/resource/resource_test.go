package resource

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
	json "github.com/bytedance/sonic"
	"github.com/golang/protobuf/ptypes/wrappers"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
)

func TestDeploymentGenerate(t *testing.T) {
	t.Skip()
	testJson := "[{\"placement\":\"VGeo-CN\",\"service_id\":84084,\"logical_cluster\":\"default\",\"cluster_id\":341245,\"dc_infos\":[\"HL\",\"LF\",\"LQ\"],\"zone\":\"China-North\"},{\"placement\":\"VGeo-CN\",\"service_id\":84084,\"logical_cluster\":\"default\",\"cluster_id\":100024112,\"dc_infos\":[\"sinfonline\",\"sinfonlinea\",\"sinfonlinec\"],\"zone\":\"huabei2\"},{\"placement\":\"VGeo-EU\",\"service_id\":56642,\"logical_cluster\":\"default\",\"cluster_id\":300000111,\"dc_infos\":[\"IEDT\"],\"zone\":\"EU-TTP\"},{\"placement\":\"VGeo-EU\",\"service_id\":56642,\"logical_cluster\":\"default\",\"cluster_id\":300002493,\"dc_infos\":[\"DEDT\"],\"zone\":\"EU-TTP\"},{\"placement\":\"VGeo-RoW\",\"service_id\":56642,\"logical_cluster\":\"default\",\"cluster_id\":2078649,\"dc_infos\":[\"Aliyun_VA\"],\"zone\":\"Aliyun_VA\"},{\"placement\":\"VGeo-RoW\",\"service_id\":56642,\"logical_cluster\":\"default\",\"cluster_id\":2520635,\"dc_infos\":[\"USEASTDT\"],\"zone\":\"US-East\"},{\"placement\":\"VGeo-RoW\",\"service_id\":56642,\"logical_cluster\":\"stress\",\"cluster_id\":4182307,\"dc_infos\":[\"SG1\"],\"zone\":\"Aliyun_SG\"},{\"placement\":\"VGeo-US\",\"service_id\":3964,\"logical_cluster\":\"default\",\"cluster_id\":1447,\"dc_infos\":[\"USEAST5\"],\"zone\":\"US-TTP\"}]"
	var tceResource []TCEResult
	err := json.Unmarshal([]byte(testJson), &tceResource)
	if err != nil {
		t.Error(err)
	}
	var finalErrors []string
	TCEDeploymentGenerate(context.Background(), tceResource, &finalErrors)
}

func TestPersist(t *testing.T) {
	tmpl, err := anypb.New(&wrappers.StringValue{Value: "<PROTOBUF_ANY>"})
	assert.NoError(t, err)
	ctx := context.Background()
	data := map[string]*rolloutpb.Resource{
		"key": {
			Spec: &rolloutpb.Spec{
				Template: tmpl,
			},
		},
	}
	str, err := DataToStr(ctx, data)
	assert.NoError(t, err)
	dataOut, err := StrToData(ctx, str)
	assert.NoError(t, err)
	assert.Equal(t, data, dataOut)
	for k, v := range dataOut {
		assert.True(t, proto.Equal(data[k], v))
	}
}
