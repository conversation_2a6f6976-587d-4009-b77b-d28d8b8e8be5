package main

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/feature_gate"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/resourcespb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/importerpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
	"code.byted.org/gopkg/metainfo"
	"github.com/bytedance/mockey"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/importer/biz/model"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/importer/biz/resource"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/stubs/dal"
	"code.byted.org/devinfra/hagrid/libs/errors"
)

func generateTestResource(t *testing.T) map[string]*rolloutpb.Resource {
	tmpl, err := anypb.New(&resourcespb.TCCBatchReleaseConfig{
		CanaryRelease: &resourcespb.TCCBatchReleaseConfig_ReleaseStrategySetting{
			RollingRatio:  proto.Int32(30),
			NextBatchMode: proto.String("Manual"),
			Interval:      proto.Int32(0),
		},
		EmergencyRelease: proto.Bool(false),
		ProductionRelease: &resourcespb.TCCBatchReleaseConfig_ReleaseStrategySetting{
			RollingRatio:  proto.Int32(30),
			NextBatchMode: proto.String("Manual"),
			Interval:      proto.Int32(0),
		},
		ForceCreateHash:  proto.String(""),
		EnvName:          proto.String("prod"),
		ServiceNamespace: proto.String("ReleaseStrategy"),
		ReleaseStrategy:  proto.String("Parallel"),
		SingledcRelease: &resourcespb.TCCBatchReleaseConfig_ReleaseStrategySetting{
			RollingRatio:  proto.Int32(30),
			NextBatchMode: proto.String("Manual"),
			Interval:      proto.Int32(0),
		},
	})
	assert.NoError(t, err)
	propOverrides, err := anypb.New(&resourcespb.TCCBatchReleaseConfig{})
	assert.NoError(t, err)
	tmpl2, err := anypb.New(&resourcespb.TCCConfig{
		ServiceNamespace:  proto.String("inf.nero.api"),
		Env:               proto.String("prod"),
		Directory:         proto.String("/default"),
		Name:              proto.String("config.yml"),
		BatchRelease:      proto.Bool(true),
		ConfigurationType: proto.String("static"),
	})
	assert.NoError(t, err)
	return map[string]*rolloutpb.Resource{
		"grn:tcc:inf.nero.api:batch:cfg": {
			Metadata: &rolloutpb.Metadata{
				LogicalId: "grn:tcc:inf.nero.api:batch:cfg",
				Type:      rolloutpb.ResourceType_TCC_BATCH_RELEASE_CONFIG,
			},
			Spec: &rolloutpb.Spec{
				Template: tmpl,
				RegionalResources: map[string]*rolloutpb.RegionalResourceOverride{
					"VGeo-Row": {
						PropertyOverrides: propOverrides,
					},
				},
			},
		},
		"grn:tcc:inf.nero.api:config.yml:cfg": {
			Metadata: &rolloutpb.Metadata{
				LogicalId: "grn:tcc:inf.nero.api:config.yml:cfg",
				Type:      rolloutpb.ResourceType_TCC_CONFIG,
			},
			Spec: &rolloutpb.Spec{
				Template: tmpl2,
			},
		},
	}
}

func TestImportResourceInterrupted(t *testing.T) {
	mockey.PatchConvey("Test importing is interrupted", t, func() {
		ctx := metainfo.WithPersistentValue(context.Background(), "username", "jie.huang")
		mockey.Mock(resource.GetResource).Return().Build()
		mockey.Mock(feature_gate.UseRscCollSplit).Return(false).Build()
		resp, err := (&ImportResourceServiceImpl{}).ImportResource(ctx, &importerpb.ImportResourceRequest{})
		assert.NoError(t, err)
		r, err := dal.NewImporterDBClient().Search(ctx, &model.DBResult{UUID: resp.Result})
		assert.NoError(t, err)
		assert.Equal(t, resp.Result, r.UUID)
		assert.Equal(t, "jie.huang", r.UserName)
		assert.Equal(t, "InProcess...", r.Errors)
		qResp, err := (&ImportResourceServiceImpl{}).QueryImport(ctx, &importerpb.QueryImportRequest{
			QueryId: r.UUID,
		})
		assert.NoError(t, err)
		assert.Equal(t, &importerpb.QueryImportResponse{
			Finished:         false,
			AvailableRegions: make([]string, 0),
		}, qResp)

		data := generateTestResource(t)
		resource.DataInsert(ctx, r.UUID, data, nil)
		qResp, err = (&ImportResourceServiceImpl{}).QueryImport(ctx, &importerpb.QueryImportRequest{
			QueryId: r.UUID,
		})
		assert.NoError(t, err)
		dataOut, err := resource.StrToData(ctx, qResp.Resources)
		assert.NoError(t, err)
		for key, res := range dataOut {
			assert.True(t, proto.Equal(data[key], res))
		}
	})
}

func TestQueryResourceNotExist(t *testing.T) {
	ctx := context.Background()
	_, err := (&ImportResourceServiceImpl{}).QueryImport(ctx, &importerpb.QueryImportRequest{
		QueryId: "Not-Exists",
	})
	assert.ErrorContains(t, err, "query_id [Not-Exists] does not exist")
	assert.True(t, errors.StatusBadRequest.Is(err))
}

func TestQueryResource(t *testing.T) {
	ctx := context.Background()
	id := uuid.New().String()
	assert.NoError(t, dal.NewImporterDBClient().Insert(ctx, &model.DBResult{
		UUID:     id,
		UserName: "jie.huang",
		Errors:   "InProcess...",
	}))
	data := generateTestResource(t)
	dataStr, err := resource.DataToStr(ctx, data)
	resource.DataInsert(ctx, id, data, nil)
	assert.NoError(t, err)
	{
		qResp, err := (&ImportResourceServiceImpl{}).QueryImport(ctx,
			&importerpb.QueryImportRequest{QueryId: id})
		assert.NoError(t, err)
		assert.Len(t, qResp.Resources, len(dataStr)) // map marshal is not stable
		assert.Nil(t, qResp.ProtoResources)
	}
	{
		qResp, err := (&ImportResourceServiceImpl{}).QueryImport(ctx,
			&importerpb.QueryImportRequest{QueryId: id, ReturnProto: true})
		assert.NoError(t, err)
		assert.Empty(t, qResp.Resources)
		assert.Equal(t, len(data), len(qResp.ProtoResources))
		for k, v := range data {
			actual := qResp.ProtoResources[k]
			assert.True(t, proto.Equal(v, actual))
		}
	}
	assert.NoError(t, dal.NewImporterDBClient().Delete(ctx, &model.DBResult{UUID: id}))
}
