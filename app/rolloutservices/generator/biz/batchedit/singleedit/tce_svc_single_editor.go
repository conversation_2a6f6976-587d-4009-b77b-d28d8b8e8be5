package singleedit

import (
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rolloutdeploymentrpc/pkg/specutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/batchedit/protoutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/parser"
	"code.byted.org/devinfra/hagrid/libs/errors"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/batchedit"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/resourcespb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
	"code.byted.org/gopkg/logs/v2/log"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
)

// SingleEditTCEService edit a tce service
func SingleEditTCEService(ctx context.Context, r *rolloutpb.Resource,
	tceSvcEditspec *batchedit.TCEServiceActions) (*rolloutpb.Resource, error) {
	log.V2.Info().With(ctx).Str("SingleEditTCEService invoked").KV("tce service resource", r).
		KV("tce service edit spec", tceSvcEditspec).Emit()

	if tceSvcEditspec == nil {
		// nothing to modify
		return r, nil
	}

	if len(tceSvcEditspec.GetToAddOwners()) == 0 && len(tceSvcEditspec.GetToDelOwners()) == 0 &&
		len(tceSvcEditspec.GetToAddOperators()) == 0 && len(tceSvcEditspec.GetToDelOperators()) == 0 {
		// nothing to modify
		return r, nil
	}

	// check if r is of type tce service
	if r.GetMetadata().GetType() != rolloutpb.ResourceType_TCE_SERVICE {
		return nil, errors.Newf("resource is not of type TCE servce, logical id = %s, type = %v",
			r.GetMetadata().GetLogicalId(), r.GetMetadata().GetType())
	}

	finalRet, fok := proto.Clone(r).(*rolloutpb.Resource)
	if !fok {
		return nil, errors.Newf("[SingleEditTCEService] proto.Clone *rollout.Resource failed,"+
			" tce svc logical id: %s", r.GetMetadata().GetLogicalId())
	}

	// unfold spec
	tceSvcTmpl := new(resourcespb.TCEService)
	if r.GetSpec().GetTemplate() != nil {
		if uErr := anypb.UnmarshalTo(r.Spec.Template, tceSvcTmpl, proto.UnmarshalOptions{}); uErr != nil {
			return nil, errors.Wrapf(uErr, "failed to unmarshal template to tce service, logical id = %s",
				r.GetMetadata().GetLogicalId())
		}
	}

	regionalOwnersSet := false
	regionalOperatorsSet := false

	for placement, rro := range r.GetSpec().GetRegionalResources() {
		tceSvcRegional := new(resourcespb.TCEService)
		if rro.GetPropertyOverrides() != nil {
			if uErr := anypb.UnmarshalTo(rro.GetPropertyOverrides(), tceSvcRegional, proto.UnmarshalOptions{}); uErr != nil {
				return nil, errors.Wrapf(uErr, "failed to unmarshal regional resources property overrides to "+
					"TCE service, logical id = %s", r.GetMetadata().GetLogicalId())
			}
		}

		tceSvcRegionalAfter, ok := proto.Clone(tceSvcRegional).(*resourcespb.TCEService)
		if !ok {
			return nil, errors.Newf("[SingleEditTCEService] proto.Clone *rollout.Resource failed,"+
				" tce svc logical id: %s, placement = %s", r.GetMetadata().GetLogicalId(), placement)
		}

		if len(tceSvcRegional.GetAuth().GetIamInfo().GetOwners()) == 0 {
			protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.owners",
				tceSvcTmpl.GetAuth().GetIamInfo().GetOwners(), protoutil.ReplaceAll, nil)
		}

		if len(tceSvcRegional.GetAuth().GetIamInfo().GetOperators()) == 0 {
			protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.operators",
				tceSvcTmpl.GetAuth().GetIamInfo().GetOperators(), protoutil.ReplaceAll, nil)
		}

		if len(tceSvcEditspec.GetToDelOwners()) != 0 {
			users := usernamesToUsers(tceSvcEditspec.GetToDelOwners())
			if len(tceSvcEditspec.GetToDelOwners()) == 1 && tceSvcEditspec.GetToDelOwners()[0] == "*" {
				// delete all
				protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.owners", []string{}, protoutil.Clear, nil)
			} else {
				// delete user specified
				protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.owners", users, protoutil.Remove, nil)
			}
		}

		if len(tceSvcEditspec.GetToDelOperators()) != 0 {
			users := usernamesToUsers(tceSvcEditspec.GetToDelOperators())
			if len(tceSvcEditspec.GetToDelOperators()) == 1 && tceSvcEditspec.GetToDelOperators()[0] == "*" {
				// delete all
				protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.operators", []string{}, protoutil.Clear, nil)
			} else {
				// delete user specified
				protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.operators", users, protoutil.Remove, nil)
			}
		}

		if len(tceSvcEditspec.GetToAddOwners()) != 0 {
			users := usernamesToUsers(tceSvcEditspec.GetToAddOwners())
			protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.owners", users, protoutil.Append, nil)
		}

		if len(tceSvcEditspec.GetToAddOperators()) != 0 {
			users := usernamesToUsers(tceSvcEditspec.GetToAddOperators())
			protoutil.SetFieldValList(tceSvcRegionalAfter, "auth.iam_info.operators", users, protoutil.Append, nil)
		}

		if len(tceSvcRegionalAfter.GetAuth().GetIamInfo().GetOwners()) != 0 {
			regionalOwnersSet = true
		}

		if len(tceSvcRegionalAfter.GetAuth().GetIamInfo().GetOperators()) != 0 {
			regionalOperatorsSet = true
		}

		newRRO, cok := proto.Clone(rro).(*rolloutpb.RegionalResourceOverride)

		if !cok {
			return nil, errors.Newf("proto.Clone *rollout.RegionalResourceOverride failed, "+
				"resource logical id = %s, placement = %s", r.GetMetadata().GetLogicalId(), placement)
		}

		newRRO.PropertyOverrides = new(anypb.Any)
		if mErr := anypb.MarshalFrom(newRRO.PropertyOverrides, tceSvcRegionalAfter, proto.MarshalOptions{}); mErr != nil {
			return nil, errors.Wrapf(mErr, "failed to marshal %v to any", tceSvcRegionalAfter)
		}

		finalRet.Spec.RegionalResources[placement] = newRRO
	}

	if !regionalOwnersSet {
		// if owners are empty in all regional resources, then clear them in template as well
		protoutil.SetFieldValList(tceSvcTmpl, "auth.iam_info.owners", []string{}, protoutil.Clear, nil)
	}

	if !regionalOperatorsSet {
		// if operators are empty in all regional resources, then clear them in template as well
		protoutil.SetFieldValList(tceSvcTmpl, "auth.iam_info.operators", []string{}, protoutil.Clear, nil)
	}

	newTceSvcTmplAny := &anypb.Any{}
	if mErr := anypb.MarshalFrom(newTceSvcTmplAny, tceSvcTmpl, proto.MarshalOptions{}); mErr != nil {
		return nil, errors.Wrapf(mErr, "failed to marshal %v to any", tceSvcTmpl)
	}

	finalRet.Spec.Template = newTceSvcTmplAny

	finalRetYml, cErr := parser.ConvertPBToYaml(finalRet)
	if cErr != nil {
		return nil, errors.Wrapf(cErr, "failed to convert resource to yaml, resource %v", finalRet)
	}

	log.V2.Info().With(ctx).Str("final single tce service resource before normalize").
		KV("tce svc final return in yaml", finalRetYml).
		KV("tce svc final return in proto", finalRet).
		Emit()

	if nErr := specutil.NormalizeSingleResourceSpec(finalRet); nErr != nil {
		return nil, errors.Wrapf(nErr, "failed to nomalize resource, resource = %v", finalRet)
	}

	finalRetYml, cErr = parser.ConvertPBToYaml(finalRet)
	if cErr != nil {
		return nil, errors.Wrapf(cErr, "failed to convert resource to yaml, resource %v", finalRet)
	}

	log.V2.Info().With(ctx).Str("final single tce service resource after edit").
		KV("tce svc final return in yaml", finalRetYml).
		KV("tce svc final return in proto", finalRet).
		Emit()

	return finalRet, nil
}

func usernamesToUsers(users []string) []*resourcespb.TCEService_ServiceInfoAuthIamInfoUsers {
	ret := make([]*resourcespb.TCEService_ServiceInfoAuthIamInfoUsers, 0, len(users))
	for _, user := range users {
		ret = append(ret, &resourcespb.TCEService_ServiceInfoAuthIamInfoUsers{
			Email:    proto.String(fmt.Sprintf("%<EMAIL>", user)),
			Username: proto.String(user),
		})
	}

	return ret
}
