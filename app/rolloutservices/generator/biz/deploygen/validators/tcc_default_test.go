package validators

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/parser"
)

func TestValidateTCCService(t *testing.T) {
	tccSvcYml := `metadata:
  bytetreeParent: 3421244
  name: default
  psm: canal.pac.engine
  type: TCC_SERVICE
  description: testing
spec:
  template:
    '@type': type.googleapis.com/resources.TCCService
    rootDirectory:
      dirName: /
    meta:
      name: canal.pac.engine
    serviceTreeNode:
      parentId: "3421244"
    vregions:
    - US-TTP2
  vgeoPlacements:
    vgeos:
      - VGEO_ROW
    overrides:
      VGEO_ROW:
        propertyOverridesBody:
          '@type': type.googleapis.com/resources.TCCService
          vregions:
            - US-EastRed
            - Singapore-Central
            - US-East
            - US-West`

	svc, cErr := parser.ConvertYamlToResourcePB(tccSvcYml)
	assert.NoError(t, cErr)

	assert.NoError(t, ValidateTCCService(svc))

	faultyTccSvcYml := `metadata:
  bytetreeParent: 3421244
  name: default
  psm: canal.pac.engine
  type: TCC_SERVICE
spec:
  template:
    '@type': type.googleapis.com/resources.TCCService
    meta:
      name: canal.pac.engine
    serviceTreeNode:
      parentId: "3421244"
    vregions:
    - US-TTP2
  vgeoPlacements:
    vgeos:
      - VGEO_ROW
    overrides:
      VGEO_ROW:
        propertyOverridesBody:
          '@type': type.googleapis.com/resources.TCCService
          vregions:
            - US-EastRed
            - Singapore-Central
            - US-East
            - US-West`

	faultySvc, cErr := parser.ConvertYamlToResourcePB(faultyTccSvcYml)
	assert.NoError(t, cErr)

	assert.Error(t, ValidateTCCService(faultySvc))
}

func TestValidateTCCCfg(t *testing.T) {
	var cfgYaml = `metadata:
  bytetreeParent: "3233432"
  description: vkcfg1 tcc config test
  logicalID: tcc-svc-iac.rollout.dryrun-i18n-vkcfg1
  name: vkcfg1
  psm: iac.rollout.dryrun
  type: TCC_CONFIG
spec:
  template:
    '@type': type.googleapis.com/resources.TCCConfig
    dir: /default/vkazas
    name: vkconfig1
    svcName: iac.rollout.dryrun_tcc
    value: |-
      {
          "key1": "val1",
          "key2": "val2",
          "key3": "val3"
      }
    valueType: VTYPE_JSON
  vregionPlacements:
    vregions:
      - VREGION_SINGAPORE_CENTRAL
`
	cfg, cErr := parser.ConvertYamlToResourcePB(cfgYaml)
	assert.NoError(t, cErr)

	assert.NoError(t, ValidateTCCConfig(cfg))

	var faultyCfgYaml = `metadata:
  bytetreeParent: "3233432"
  description: vkcfg1 tcc config test
  logicalID: tcc-svc-iac.rollout.dryrun-i18n-vkcfg1
  name: vkcfg1
  psm: iac.rollout.dryrun
  type: TCC_CONFIG
spec:
  template:
    '@type': type.googleapis.com/resources.TCCConfig
    dir: /default/vkazas
    name: vkconfig1
    svcName: iac.rollout.dryrun_tcc
    valueType: VTYPE_JSON
  vregionPlacements:
    vregions:
      - VREGION_SINGAPORE_CENTRAL
`
	faultyCfg, cErr := parser.ConvertYamlToResourcePB(faultyCfgYaml)
	assert.NoError(t, cErr)

	assert.Error(t, ValidateTCCConfig(faultyCfg))

	var selectorCfgYml = `metadata:
  bytetreeParent: "4776327"
  description: vkcfg1 tcc config test
  logicalID: tcc-cfg-canal.pac.engine-i18n-yf1
  name: vkcfg1
  type: TCC_CONFIG
spec:
  template:
    '@type': type.googleapis.com/resources.TCCConfig
    dir: /default
    name: vkconfig1
    svcName: canal.pac.enginetccyf1
    value: |-
      {
          "k1": "v1"
      }
    valueType: VTYPE_JSON
    batchRelease: true
  vregionPlacements:
    vregionSelector:
      matchExpressions:
        - key: vgeo
          operator: IN
          values:
            - VGEO_ROW`
	selectorCfg, csErr := parser.ConvertYamlToResourcePB(selectorCfgYml)
	assert.NoError(t, csErr)

	assert.NoError(t, ValidateTCCConfig(selectorCfg))
}

func TestValidateTCCBatchConfig(t *testing.T) {
	tccBatchConfigYml := `metadata:
  logicalID: render
  type: TCC_BATCH_RELEASE_CONFIG
spec:
  template:
    '@type': type.googleapis.com/resources.TCCBatchReleaseConfig
    canaryRelease:
      interval: 1
      nextBatchMode: Manual
      rollingRatio: 30
    productionRelease:
      interval: 1
      nextBatchMode: Manual
      rollingRatio: 30
    releaseStrategy: Parallel
    serviceName: canal.pac.engine4
    singleDcRelease:
      interval: 1
      nextBatchMode: Manual
      rollingRatio: 30
    vregions:
    - Singapore-Central
    - US-East
  vgeoPlacements:
    vgeos:
    - VGEO_ROW
    overrides:
      VGEO_ROW:
        propertyOverridesBody:
          '@type': type.googleapis.com/resources.TCCBatchReleaseConfig
          vregions:
            - US-EastRed
            - Singapore-Central
            - US-East
            - US-West`

	bcfg, cErr := parser.ConvertYamlToResourcePB(tccBatchConfigYml)
	assert.NoError(t, cErr)

	assert.NoError(t, ValidateTCCBatchConfig(bcfg))

	faultyTccBatchConfigYml := `metadata:
  logicalID: render
  type: TCC_BATCH_RELEASE_CONFIG
spec:
  template:
    '@type': type.googleapis.com/resources.TCCBatchReleaseConfig
    canaryRelease:
      interval: 1
      nextBatchMode: Manual
      rollingRatio: 30
    productionRelease:
      interval: 1
      nextBatchMode: Manual
      rollingRatio: 30
    releaseStrategy: Parallel
    singleDcRelease:
      interval: 1
      nextBatchMode: Manual
      rollingRatio: 30
    vregions:
    - Singapore-Central
    - US-East
  vgeoPlacements:
    vgeos:
    - VGEO_ROW
    overrides:
      VGEO_ROW:
        propertyOverridesBody:
          '@type': type.googleapis.com/resources.TCCBatchReleaseConfig
          vregions:
            - US-EastRed
            - Singapore-Central
            - US-East
            - US-West`

	faultyBcfg, cErr := parser.ConvertYamlToResourcePB(faultyTccBatchConfigYml)
	assert.NoError(t, cErr)

	assert.Error(t, ValidateTCCBatchConfig(faultyBcfg))
}
