load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "sgsingleinst",
    srcs = [
        "sgsingleinst_default_gen.go",
        "sgsingleinst_default_merger.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies/sg/sgsingleinst",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutservices/generator/biz/deploygen/strategies",
        "//app/rolloutservices/generator/biz/deploygen/strategies/sg",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "sgsingleinst_test",
    srcs = ["sgsingleinst_default_gen_test.go"],
    embed = [":sgsingleinst"],
    deps = [
        "//app/rolloutservices/generator/biz/deploygen/strategies",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
    ],
)
