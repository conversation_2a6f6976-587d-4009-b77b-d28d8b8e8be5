package ability

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies/sg"
	resources "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rollout/resourcespb"
	"code.byted.org/lang/gg/gmap"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/tiktok/iac/sdk/go/component"
)

func validRateLimit(conf *resources.ServGovernFlowCtrl_RateLimitConfig) error {
	if gptr.IsNil(conf) {
		return nil
	}

	if conf.GetCon() < 0 {
		return fmt.Errorf("con limit must be positive, con: %d", *conf.Con)
	}
	if conf.GetQps() < 0 {
		return fmt.Errorf("qps limit must be positive, qps: %d", *conf.Qps)
	}
	if conf.GetQpm() < 0 {
		return fmt.Errorf("qpm limit must be positive, qpm: %d", *conf.Qpm)
	}
	if conf.GetQph() < 0 {
		return fmt.Errorf("qph limit must be positive, qph: %d", *conf.Qph)
	}

	if !isRateLimitEnabled(conf) || (conf.Enable != nil && !conf.GetEnable()) {
		conf.Con = nil
		conf.Qps = nil
		conf.Qpm = nil
		conf.Qph = nil
		conf.Enable = gptr.Of(false)
		return nil
	}

	conf.Enable = gptr.Of(true)

	return nil
}

func rlToSpec(rl *component.FlowCtrl_RateLimitConfig) *resources.ServGovernFlowCtrl_RateLimitConfig {
	if isRateLimitNil(rl) {
		return nil
	}
	return &resources.ServGovernFlowCtrl_RateLimitConfig{
		Enable: rl.Enable,
		Con:    rl.Con,
		Qps:    rl.Qps,
		Qpm:    rl.Qpm,
		Qph:    rl.Qph,
	}
}

func isRateLimitEnabled(rl *resources.ServGovernFlowCtrl_RateLimitConfig) bool {
	return gptr.IsNotNil(rl) && (gptr.Indirect(rl.Con) > 0 || gptr.Indirect(rl.Qps) > 0 || gptr.Indirect(rl.Qpm) > 0 || gptr.Indirect(rl.Qph) > 0)
}

func isRateLimitNil(rl *component.FlowCtrl_RateLimitConfig) bool {
	return gptr.IsNil(rl) || (gptr.IsNil(rl.Con) && gptr.IsNil(rl.Qps) && gptr.IsNil(rl.Qpm) && gptr.IsNil(rl.Qph))
}

type MeshRateLimit struct{}

func (rl *MeshRateLimit) GovernAllCluster(fcSot *component.FlowCtrl, clusters []string) error {
	return nil
}

func (rl *MeshRateLimit) toRpcMeta(meta *component.Metadata, gKey sg.GovernKey) (*sg.RpcMeta, error) {
	if !gKey.IsRpc() {
		return nil, fmt.Errorf("[%s] govern key must be rpc meta, govern key: %+v", rl, gKey)
	}

	rpcMeta, err := sg.NewRpcMeta(gKey)
	if err != nil {
		return nil, err
	}

	if rpcMeta.Callee != meta.Psm {
		return nil, fmt.Errorf("[%s] callee must be equal to PSM in metadata, govern key: %+v", rl, rpcMeta.ToKey())
	}

	if rpcMeta.CalleeCluster == "*" {
		return nil, fmt.Errorf("[%s] callee cluster must be specified, govern key: %+v", rl, rpcMeta.ToKey())
	}
	return rpcMeta, nil
}

func (rl *MeshRateLimit) SplitByCluster(meta *component.Metadata, fc *component.FlowCtrl, clusterFCMap map[string]*component.FlowCtrl) error {
	fc.MeshRateLimit = gmap.Union(fc.MeshRateLimit)
	for key, config := range fc.MeshRateLimit {
		rpcMeta, err := rl.toRpcMeta(meta, sg.GovernKey(key))
		if err != nil {
			return err
		}

		cluster := rpcMeta.CalleeCluster
		clusterFC, _ := gmap.LoadOrStore(clusterFCMap, cluster, &component.FlowCtrl{})
		clusterFC.MeshRateLimit = gmap.Union(clusterFC.MeshRateLimit)
		clusterFC.MeshRateLimit[key] = config
	}
	return nil
}

func (rl *MeshRateLimit) TransToSpec(fcSot *component.FlowCtrl, fcSpec *resources.ServGovernFlowCtrl) error {
	fcSpec.MeshRateLimit = gmap.Compact(gmap.MapValues(fcSot.MeshRateLimit, func(confSot *component.FlowCtrl_MeshRateLimit) *resources.ServGovernFlowCtrl_MeshRateLimit {
		spec := rlToSpec(confSot.GetConfig())
		if spec == nil {
			return nil
		}
		return &resources.ServGovernFlowCtrl_MeshRateLimit{
			Config: rlToSpec(confSot.Config),
		}
	}))

	return nil
}

func (rl *MeshRateLimit) MergeCentral(fcSpec *resources.ServGovernFlowCtrl, centralSot *sg.CentralConfig) error {
	return nil
}

func (rl *MeshRateLimit) ValidateSpec(fcSpec *resources.ServGovernFlowCtrl) error {
	for _, s := range gmap.Values(fcSpec.MeshRateLimit) {
		if err := validRateLimit(s.GetConfig()); err != nil {
			return err
		}
	}
	return nil
}

func (rl *MeshRateLimit) ValidateOverride(baseSpec, overrideSpec *resources.ServGovernFlowCtrl, central *sg.CentralConfig) error {
	for key, config := range overrideSpec.MeshRateLimit {
		if gmap.Contains(baseSpec.MeshRateLimit, key) {
			overrideSpec.MeshRateLimit[key] = gptr.Of(gptr.Indirect(config))
			config = overrideSpec.MeshRateLimit[key]
			baseConfig := baseSpec.MeshRateLimit[key]
			if err := sg.Merge(config, baseConfig); err != nil {
				return fmt.Errorf("merge regional rate limit config failed, key: %s, err: %v", key, err)
			}
		}

		if err := validRateLimit(config.GetConfig()); err != nil {
			return fmt.Errorf("validate regional rate limit config failed, key: %s, err: %v", key, err)
		}
	}

	return nil
}

func (rl *MeshRateLimit) DeploymentType() sg.DeploymentType {
	return sg.DeploymentTypeVRegionType
}

func (rl *MeshRateLimit) String() string {
	return "MeshRateLimit"
}

type MeshClusterRateLimit struct {
	MeshRateLimit
}

func (crl *MeshClusterRateLimit) SplitByCluster(meta *component.Metadata, fc *component.FlowCtrl, clusterFCMap map[string]*component.FlowCtrl) error {
	fc.MeshClusterRateLimit = gmap.Union(fc.MeshClusterRateLimit)
	for key, config := range fc.MeshClusterRateLimit {
		rpcMeta, err := crl.toRpcMeta(meta, sg.GovernKey(key))
		if err != nil {
			return err
		}

		cluster := rpcMeta.CalleeCluster
		clusterFC, _ := gmap.LoadOrStore(clusterFCMap, cluster, &component.FlowCtrl{})
		clusterFC.MeshClusterRateLimit = gmap.Union(clusterFC.MeshClusterRateLimit)
		clusterFC.MeshClusterRateLimit[key] = config
	}
	return nil
}

func (crl *MeshClusterRateLimit) TransToSpec(fcSot *component.FlowCtrl, fcSpec *resources.ServGovernFlowCtrl) error {
	fcSpec.MeshClusterRateLimit = gmap.Compact(gmap.MapValues(fcSot.MeshClusterRateLimit, func(confSot *component.FlowCtrl_MeshClusterRateLimit) *resources.ServGovernFlowCtrl_MeshClusterRateLimit {
		idcRateLimits := gmap.Compact(gmap.MapValues(confSot.GetConfig(), rlToSpec))
		if len(idcRateLimits) <= 0 {
			return nil
		}
		return &resources.ServGovernFlowCtrl_MeshClusterRateLimit{
			Config: &resources.ServGovernFlowCtrl_MeshClusterRateLimit_Config{IdcRateLimits: idcRateLimits},
		}
	}))
	return nil
}

func (crl *MeshClusterRateLimit) MergeCentral(fcSpec *resources.ServGovernFlowCtrl, centralSot *sg.CentralConfig) error {
	return nil
}

func (crl *MeshClusterRateLimit) ValidateSpec(fcSpec *resources.ServGovernFlowCtrl) error {
	for _, s := range gmap.Values(fcSpec.MeshClusterRateLimit) {
		for _, v := range gmap.Values(s.GetConfig().GetIdcRateLimits()) {
			if err := validRateLimit(v); err != nil {
				return err
			}
		}
	}
	return nil
}

func (crl *MeshClusterRateLimit) DeploymentType() sg.DeploymentType {
	return sg.DeploymentTypeVRegionType
}

func (crl *MeshClusterRateLimit) String() string {
	return "MeshClusterRateLimit"
}

func (crl *MeshClusterRateLimit) ValidateOverride(baseSpec, overrideSpec *resources.ServGovernFlowCtrl, central *sg.CentralConfig) error {
	for key, config := range overrideSpec.MeshClusterRateLimit {
		overrideConfig := config.GetConfig().GetIdcRateLimits()
		if gmap.Contains(baseSpec.MeshClusterRateLimit, key) {
			baseConfig := baseSpec.MeshClusterRateLimit[key].GetConfig().GetIdcRateLimits()
			for vdc, rl := range overrideConfig {
				overrideConfig[vdc] = gptr.Of(gptr.Indirect(rl))
				if gmap.Contains(baseConfig, vdc) {
					if err := sg.Merge(overrideConfig[vdc], baseConfig[vdc]); err != nil {
						return fmt.Errorf("merge regional [%s] failed, key: %s, vdc: %v, config: %v, err: %v", crl, key, vdc, overrideConfig[vdc], err)
					}
				}
			}
		}
		for vdc, rl := range overrideConfig {
			if err := validRateLimit(rl); err != nil {
				return fmt.Errorf("validate regional [%s] failed, key: %s, vdc: %v, config: %v, err: %v", crl, key, vdc, rl, err)
			}
		}
	}
	return nil
}
