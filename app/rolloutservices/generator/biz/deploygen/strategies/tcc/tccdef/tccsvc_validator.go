package tccdef

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutpb"
)

func validateAndFillTCCService(r *rolloutpb.Resource) (*rolloutpb.Resource, error) {
	return r, nil
	//svc := new(resources.TCCService)
	//ret, ok := proto.Clone(r).(*rollout.Resource)
	//if !ok {
	//	return nil, fmt.Errorf("proto.Clone *rollout.Resource failed")
	//}
	//if ret.Metadata.Description == "" {
	//	ret.Metadata.Description = "Mandatory default TCC Service description, please modify."
	//}
	//
	//if uErr := anypb.UnmarshalTo(r.Spec.Template, svc, proto.UnmarshalOptions{}); uErr != nil {
	//	return nil, fmt.Errorf("failed to unmarshal template to TCCService: %w", uErr)
	//}
	//
	//newSvcAny := &anypb.Any{}
	//if mErr := anypb.MarshalFrom(newSvcAny, svc, proto.MarshalOptions{}); mErr != nil {
	//	return nil, fmt.Errorf("failed to marshal %v to any: %w", svc, mErr)
	//}
	//
	//ret.Spec.Template = newSvcAny
	//
	//if vErr := validators.ValidateTCCService(ret); vErr != nil {
	//	return nil, fmt.Errorf("tcc svc validate error: %w", vErr)
	//}
	//
	//return ret, nil
}
