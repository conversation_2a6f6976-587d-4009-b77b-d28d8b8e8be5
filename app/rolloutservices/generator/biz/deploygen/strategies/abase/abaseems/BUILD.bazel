load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "abaseems",
    srcs = [
        "abase_ems_gen.go",
        "abase_ems_merger.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies/abase/abaseems",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutservices/generator/biz/deploygen/sot",
        "//app/rolloutservices/generator/biz/deploygen/strategies",
        "//idls/byted/devinfra/iac/rollout:rolloutv1_go_proto",
        "//idls/byted/devinfra/rollout:rollout_go_proto",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "//libs/errors",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_bytemeta_siac//sot/model",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/anypb",
    ],
)

go_test(
    name = "abaseems_test",
    srcs = ["abase_ems_merger_test.go"],
    embed = [":abaseems"],
    gc_linkopts = [
        "-X=google.golang.org/protobuf/reflect/protoregistry.conflictPolicy=warn",
    ],
    deps = [
        "//idls/byted/devinfra/rollout:rollout_go_proto",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_nsf_jsondiff//:jsondiff",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_bytemeta_siac//sot/model",
        "@org_golang_google_protobuf//types/known/anypb",
    ],
)
