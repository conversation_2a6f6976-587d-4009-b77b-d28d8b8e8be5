package rdsdbdef

import (
	"code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies/rds/rdsutils"
	"context"
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies/common/default_gen"
	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"
	"code.byted.org/gopkg/logs/v2/log"
	"google.golang.org/protobuf/proto"
)

const (
	RDSDBDefMergerKey = "RDSDB-def-merger"
)

// RDSDefaultMerger Assemble RDS resources here. Implemented by rollout team.
type RDSDefaultMerger struct {
}

func NewRDSDefaultMerger() strategies.IGenerator {
	return &RDSDefaultMerger{}
}

func (tdm *RDSDefaultMerger) Generate(ctx context.Context, inputs proto.Message, results map[string]*strategies.GenOutput) error {
	defRDSInput, ok := inputs.(*rollout.DefaultRDSDBGenInputs)
	if !ok {
		return fmt.Errorf("input message is not *rollout.DefaultRDSDBGenInputs")
	}
	log.V2.Info().With(ctx).Str("RDSDBDefaultMerger#Generate invoked").
		KV("defRDSDBInput", defRDSInput).
		KV("results", results).
		Emit()

	rs := make([]*rollout.Resource, 0)

	// add generated RDS DB resource.
	rdsDBRsc, dgErr := default_gen.DefaultGen(ctx, defRDSInput.RdsDbSot)
	if dgErr != nil {
		return fmt.Errorf("failed to generate RDS DB resource from sot %v: %w", defRDSInput.RdsDbSot, dgErr)
	}

	processedRDSDBRsc, cErr := rdsutils.ValidateAndFillRDSDB(rdsDBRsc)
	if cErr != nil {
		return fmt.Errorf("failed to validate and fill in default values for RDS DB: %w", cErr)
	}

	rs = append(rs, processedRDSDBRsc)

	results[RDSDBDefMergerKey] = &strategies.GenOutput{
		Resources: rs,
	}

	return nil
}
