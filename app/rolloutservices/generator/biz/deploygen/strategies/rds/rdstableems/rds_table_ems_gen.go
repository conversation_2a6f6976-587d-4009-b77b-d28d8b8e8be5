package rdstableems

import (
	"context"
	"fmt"

	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/generator/biz/deploygen/strategies"
	"code.byted.org/gopkg/logs/v2/log"
	"google.golang.org/protobuf/proto"
)

// EMSRDSTableScheduler RDS EMS gen strategy, schedule generation orchestration here
type EMSRDSTableScheduler struct{}

func NewEMSRDSTableScheduler() strategies.IScheduler {
	return &EMSRDSTableScheduler{}
}

func (dcs *EMSRDSTableScheduler) Schedule(ctx context.Context, inputs proto.Message) ([]*rollout.Resource, error) {
	ttRDSTableInputs, ok := inputs.(*rollout.TTRDSTableGenInputs)
	if !ok {
		return nil, fmt.Errorf("cannot convert to TTRDSTableGenInputs type")
	}

	log.V2.Info().With(ctx).Str("Schedule generating resources using tiktok ems rds table generation strategy").
		KV("ttRDSTableInputs", ttRDSTableInputs).Emit()

	// Default ems rds table generator simply calls EMSRDSTableMerger.Generate
	erm := NewEMSRDSTableMerger()
	res := make(map[string]*strategies.GenOutput)

	gErr := erm.Generate(ctx, inputs, res)
	if gErr != nil {
		return nil, fmt.Errorf("failed to generate resources using ems rds table generator: %w", gErr)
	}

	r, found := res[emsRDSTableMergerKey]
	if !found {
		return nil, fmt.Errorf("final generated resources with key %s not found in results", emsRDSTableMergerKey)
	}
	return r.Resources, nil
}
