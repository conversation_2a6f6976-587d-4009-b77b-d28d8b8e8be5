# Generator service
A generation service that is responsible for generating rollout.Deployment structure for all deployments in a batch.   
Framework: `kitex`, Protocol: `rpc`.

## Links
[TCE i18n](https://cloud-i18n.bytedance.net/tce/services/1754235?cluster-type=cluster&module=cluster&page=1&page_size=10)   
[SCM](https://cloud.bytedance.net/scm/detail/327569/versions)


## Development
### Update code after updating idl 
Automatically done by [pbgen](https://code.byted.org/devinfra/pbgen/blob/main/byted/devinfra/rolloutapp/generatorpb/generator_service.pb.go)   
Need to go to handler.go to add missing implementations if new RPCs are added.

### Build
Run 
```
bazel build //projects/rollout/generator:pkg
```
