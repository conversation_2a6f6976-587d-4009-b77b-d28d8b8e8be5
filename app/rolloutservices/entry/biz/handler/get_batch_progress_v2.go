// Code generated by hertztool.

package handler

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/rpcclient"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error/hertz_server"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/deploymentpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
)

// GetBatchProgressV2 .
// @router /batch_deployments/progress/v2 [GET]
func GetBatchProgressV2(ctx context.Context, c *app.RequestContext) {
	req := &deploymentpb.GetBatchProgressRequest{}
	if hertz_server.ParseQueryStrNonEmpty(c, "batch_id", &req.BatchId) != nil {
		return
	}

	newCtx := ctxutil.SetupCtx(ctx, c)
	log.V2.Info().With(newCtx).StrKV("batch_id", req.BatchId).Str("GetBatchProgress").Emit()
	resp, err := rpcclient.NewStandardDeploymentRPCClient().GetBatchProgress(ctx, req)
	if err != nil {
		hertz_server.SetRpcError(c, err)
		return
	}
	hertz_server.WriteResp(c, resp)
}
