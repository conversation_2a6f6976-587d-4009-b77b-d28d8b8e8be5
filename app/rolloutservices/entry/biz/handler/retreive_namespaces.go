// Code generated by hertztool.

package handler

import (
	"context"
	"net/http"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/rpcclient"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error/hertz_server"

	generatorpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/generatorpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
)

// RetreiveNamespaces .
// @router /namespaces [GET]
func RetreiveNamespaces(ctx context.Context, c *app.RequestContext) {
	admin := c.Query("admin")
	follower := c.Query("follower")

	req := &generatorpb.GetNamespacesRequest{
		Admin:    admin,
		Follower: follower,
	}

	log.V2.Info().With(ctx).Str("[Entry]RetrieveNamespaces received").KV("request", req).Emit()

	newCtx := ctxutil.SetupCtx(ctx, c)
	resp, rErr := rpcclient.NewGeneratorRPCClient().GetNamespaces(newCtx, req)
	if rErr != nil {
		hertz_server.SetRpcError(c, rErr)
		return
	}

	hertz_server.WriteRespWithStatus(c, resp, http.StatusCreated)
}
