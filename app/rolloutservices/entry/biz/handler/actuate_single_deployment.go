// Code generated by hertztool.

package handler

import (
	"context"
	"net/http"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/rpcclient"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error/hertz_server"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/deploymentpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
)

// ActuateSingleDeployment .
// @router /single_deployment/actuation [POST]
func ActuateSingleDeployment(ctx context.Context, c *app.RequestContext) {
	var req deploymentpb.ActuateSingleDeploymentRequest
	if hertz_server.BindAndValidate(c, &req) != nil {
		return
	}

	newCtx := ctxutil.SetupCtx(ctx, c)
	log.V2.Info().With(ctx).Str("[Entry] invoking ActuateSingleDeployment").KV("req", &req).Emit()
	resp, err := rpcclient.NewStandardDeploymentRPCClient().ActuateSingleDeployment(newCtx, &req)
	if err != nil {
		hertz_server.SetRpcError(c, err)
		return
	}

	hertz_server.WriteRespWithStatus(c, resp, http.StatusAccepted)
}
