// Code generated by hertztool.

package handler

import (
	"context"
	"net/http"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/rpcclient"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error/hertz_server"

	generatorpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/generatorpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/middleware/hertz/pkg/app"
)

// UpdateDeploymentRegistry .
// @router /deployment/registry [PUT]
func UpdateDeploymentRegistry(ctx context.Context, c *app.RequestContext) {
	var req generatorpb.RegisterDeploymentRequest
	if hertz_server.BindAndValidate(c, &req) != nil {
		return
	}

	log.V2.Info().With(ctx).Str("[Entry]UpdateDeploymentRegistry received").KV("request", &req).Emit()

	newCtx := ctxutil.SetupCtx(ctx, c)
	resp, err := rpcclient.NewGeneratorRPCClient().UpdateDeploymentRegistry(newCtx, &req)
	if err != nil {
		hertz_server.SetRpcError(c, err)
		return
	}

	hertz_server.WriteRespWithStatus(c, resp, http.StatusAccepted)
}
