package stub

import (
	"fmt"
	"net/http"

	iachttp "code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/http"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/model"
)

const (
	scmCreateEndpoint       = "https://scm.byted.org/api/v2/repos/ci_create/"
	scmUpdateEndpointFormat = "https://scm.byted.org/api/repos/%d"
	scmQueryEndpoint        = "https://scm.byted.org/api/v2/repos/by_names"
)

type SCMClient struct {
	client *http.Client
	svcJWT string
}

func NewSCMClient(svcJWT string) *SCMClient {
	return &SCMClient{
		client: iachttp.NewStdClient(),
		svcJWT: svcJWT,
	}
}

func (sc *SCMClient) CreateSCMRepo(req *model.CreateSCMRepoRequest) (string, error) {
	hc := iachttp.NewHTTPClient[model.CreateSCMRepoRequest, model.CreateSCMRepoResponse](sc.client)

	headers := map[string]string{
		"x-jwt-token":  sc.svcJWT,
		"content-type": "application/json",
	}

	createRes, cErr := hc.Do(http.MethodPost, scmCreateEndpoint, headers, nil, req)
	if cErr != nil {
		return "", fmt.Errorf("failed to process http request, %w", cErr)
	}

	if createRes.Data.State == "failed" {
		return "", fmt.Errorf("create scm response contains error: %s", createRes.Data.Context.Error)
	}

	return createRes.Data.Context.Url, nil
}

func (sc *SCMClient) UpdateSCMRepo(req *model.UpdateSCMRepoRequest, repoID int) error {
	scmUpdateEndpoint := fmt.Sprintf(scmUpdateEndpointFormat, repoID)

	hc := iachttp.NewHTTPClient[model.UpdateSCMRepoRequest, model.UpdateSCMRepoResponse](sc.client)

	headers := map[string]string{
		"x-jwt-token":  sc.svcJWT,
		"content-type": "application/json",
	}

	updateRes, uErr := hc.Do(http.MethodPatch, scmUpdateEndpoint, headers, nil, req)
	if uErr != nil {
		return fmt.Errorf("failed to process http request, %w", uErr)
	}

	if updateRes.Status != 0 {
		return fmt.Errorf("update scm repo returned failed status")
	}

	return nil
}

func (sc *SCMClient) SCMRepoExist(repoName string) bool {
	_, found, err := sc.GetSCMRepo(repoName)
	if err != nil {
		return false
	}
	return found
}

func (sc *SCMClient) GetSCMRepo(repoName string) (model.GetSingleSCMRepoResponse, bool, error) {
	hc := iachttp.NewHTTPClient[struct{}, model.GetSCMRepoResponse](sc.client)

	queries := map[string]string{
		"repo_names": repoName,
	}

	getRes, gErr := hc.Do(http.MethodGet, scmQueryEndpoint, nil, queries, nil)
	if gErr != nil {
		return model.GetSingleSCMRepoResponse{}, false, fmt.Errorf("failed to get scm: %w", gErr)
	}

	if len(*getRes) == 0 {
		return model.GetSingleSCMRepoResponse{}, false, nil
	}

	return (*getRes)[0], true, nil
}

func (sc *SCMClient) PublishSCMVervsion() {

}
