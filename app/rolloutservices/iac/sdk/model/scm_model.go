package model

type CreateSCMRepoRequest struct {
	Name       string `json:"name"`
	Language   string `json:"language"`
	GitSource  string `json:"git_source"`
	CreateUser string `json:"create_user"`
	GitURL     string `json:"git_url"`
	Image      string `json:"image"`
	Desc       string `json:"desc,omitempty"`
	HasCompile int    `json:"has_compile"`
	ScriptPath string `json:"compile_script_path"`
}

type CreateSCMRepoResponse struct {
	Code int `json:"code"`
	Data struct {
		State   string `json:"state"`
		Context struct {
			Url    string `json:"url"`
			RepoId int    `json:"repo_id"`
			Error  string `json:"error"`
		} `json:"context"`
	} `json:"data"`
	Error string `json:"error"`
}

type UpdateSCMRepoRequest struct {
	Name       string `json:"name"`
	Language   string `json:"language"`
	GitSource  string `json:"git_source"`
	CreateUser string `json:"create_user"`
	HttpUrl    string `json:"http_url"`
	Image      string `json:"image"`
	Desc       string `json:"desc,omitempty"`
	GitID      int    `json:"git_id,omitempty"`
	HasCompile int    `json:"has_compile"`
	ScriptPath string `json:"compile_script_path"`
}

type UpdateSCMRepoResponse struct {
	Status int `json:"status"`
}

type RepoPermission struct {
	DeployPermission    string `json:"deploy_permission"`
	ViewPermission      string `json:"view_permission"`
	DownloadPermission  string `json:"download_permission"`
	ConfigurePermission string `json:"configure_permission"`
}

type GetSingleSCMRepoResponse struct {
	Id                       int            `json:"id"`
	Name                     string         `json:"name"`
	HttpUrl                  string         `json:"http_url"`
	GitUrl                   string         `json:"git_url"`
	CreateUser               string         `json:"create_user"`
	Desc                     string         `json:"desc"`
	Type                     string         `json:"type"`
	GitSource                string         `json:"git_source"`
	RepoName                 string         `json:"repo_name"`
	Image                    string         `json:"image"`
	Arch                     []string       `json:"arch"`
	IsDeleted                bool           `json:"is_deleted"`
	GitId                    int            `json:"git_id,omitempty"`
	HasCompile               int            `json:"has_compile"`
	CompileScriptPath        string         `json:"compile_script_path"`
	AutoBuild                bool           `json:"auto_build"`
	RepoPermission           RepoPermission `json:"repopermissions"`
	ResourceUploadType       string         `json:"resource_upload_type"`
	ResourceUploadRegionPath string         `json:"resource_upload_region_path"`
	ResourceUploadPathPrefix string         `json:"resource_upload_path_prefix"`
	OnlineBranches           string         `json:"online_branches"`
	EnableCache              bool           `json:"enable_cache"`
	DeepClone                bool           `json:"deep_clone"`
	ParallelConcurrent       int            `json:"parallel_concurrent"`
	ProxySwitch              bool           `json:"proxy_switch"`
	NotifyBotTokens          string         `json:"notify_bot_tokens"`
}

type GetSCMRepoResponse []GetSingleSCMRepoResponse
