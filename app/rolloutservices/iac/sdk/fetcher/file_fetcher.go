package fetcher

import (
	"context"
	"fmt"
)

type FileFetcher struct {
	repo Repo
}

func NewFileFetcher(privateToken string, codebaseUserJWT string) (*FileFetcher, error) {
	if privateToken != "" && codebaseUserJWT != "" {
		return nil, fmt.Errorf("private token and codebase user jwt cannot be both provided")
	}

	if privateToken == "" && codebaseUserJWT == "" {
		return nil, fmt.Errorf("must provide at least private token or codebase user jwt")
	}

	if privateToken != "" {
		// gitlab
		gc, nErr := NewGitlabClient(privateToken)
		if nErr != nil {
			return nil, fmt.Errorf("failed to create gitlab client: %w", nErr)
		}

		return &FileFetcher{
			repo: gc,
		}, nil
	}

	// codebase
	cc, nErr := NewCodebaseClient(codebaseUserJWT)
	if nErr != nil {
		return nil, fmt.Errorf("failed to create codebase client: %w", nErr)
	}

	return &FileFetcher{
		repo: cc,
	}, nil
}

func (ff *FileFetcher) GetFile(ctx context.Context, repo string, filePath string, branch string) (string, string, error) {
	return ff.repo.FetchFile(ctx, repo, filePath, branch)
}

func (ff *FileFetcher) GetFileWithRegexDefaultBranch(ctx context.Context, repo string, regex string, withoutFileName bool) (map[string]string, error) {
	defaultBranch, gErr := ff.repo.GetDefaultBranch(ctx, repo)
	if gErr != nil {
		return nil, fmt.Errorf("failed to get blueprint file default branch %s: %w", defaultBranch, gErr)
	}
	return ff.repo.FetchFileByRegex(ctx, repo, defaultBranch, regex, withoutFileName)
}

func (ff *FileFetcher) GetFileWithRegex(ctx context.Context, repo string, regex string, withoutFileName bool, branch string) (map[string]string, error) {
	return ff.repo.FetchFileByRegex(ctx, repo, branch, regex, withoutFileName)
}

func (ff *FileFetcher) GetFileInDefaultBranch(ctx context.Context, repo string, filePath string) (string, string, error) {
	defaultBranch, gErr := ff.repo.GetDefaultBranch(ctx, repo)
	if gErr != nil {
		return "", "", fmt.Errorf("failed to get blueprint file default branch %s: %w", defaultBranch, gErr)
	}

	return ff.repo.FetchFile(ctx, repo, filePath, defaultBranch)
}

// GetFileInDefaultBranchWithFolder fetch file in repo <namespace>/<reponame> at folder/relatviePath if folder is
// not empty. Otherwise, fetch file in the above repo at relativePath. <relativePath> must contain file name.
// @return string: file content
//
//	string: file name
//	error: in case of any error
func (ff *FileFetcher) GetFileInDefaultBranchWithFolder(ctx context.Context, namespace string, repoName string,
	folder string, relativePath string) (string, string, error) {
	var rp string
	if folder != "" {
		rp = fmt.Sprintf("%s/%s", folder, relativePath)
	} else {
		rp = relativePath
	}

	return ff.GetFileInDefaultBranch(ctx, fmt.Sprintf("%s/%s", namespace, repoName), rp)
}
