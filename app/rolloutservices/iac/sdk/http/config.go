package http

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/http/conf"
)

const (
	LOCAL_URI = "http://127.0.0.1:6789"
)

const (
	IMPORT_PATH = "/import"
)

const (
	PPE_ENV = "ppe_staging"
	BOE_ENV = "prod"
)

type RunEnv int64

const (
	_RunEnv = iota
	RunEnvProd
	RunEnvI18N
	RunEnvTTP
	RunEnvTTP_TX
	RunEnvPPE
	RunEnvBOE
	RunEnvLocal
)

type Config struct {
	URI    string
	Header map[string]string
}

type URLConfigFunc func(RunEnv, string) (Config, error)

func GetImporterServerConfig(env RunEnv, lane string) (Config, error) {
	var serverConfig Config
	serverConfig.Header = make(map[string]string)
	serverConfig.Header["Content-Type"] = "application/json"
	switch env {
	case RunEnvLocal:
		serverConfig.URI = LOCAL_URI
	case RunEnvPPE:
		serverConfig.URI = conf.IMPORTER_PPE_URI + IMPORT_PATH
		setLane := PPE_ENV
		if lane != "prod" {
			setLane = lane
		}
		serverConfig.Header["x-tt-env"] = setLane
		serverConfig.Header["x-use-ppe"] = "1"
	case RunEnvBOE:
		serverConfig.URI = conf.IMPORTER_BOE_URI + IMPORT_PATH
		setLane := BOE_ENV
		if lane != "prod" {
			setLane = lane
		}
		serverConfig.Header["x-tt-env"] = setLane
		serverConfig.Header["x-use-boe"] = "1"
	case RunEnvI18N:
		serverConfig.URI = conf.IMPORTER_I18N_URI + IMPORT_PATH
	case RunEnvProd:
		serverConfig.URI = conf.IMPORTER_PROD_URI + IMPORT_PATH
	case RunEnvTTP:
		serverConfig.URI = conf.IMPORTER_TTP_URI + IMPORT_PATH
	case RunEnvTTP_TX:
		serverConfig.URI = conf.IMPORTER_TTP_TX_URI + IMPORT_PATH

	default:
		return serverConfig, fmt.Errorf("wrong input of env, allowed values: [local, ppe, boe, prod, ttp]")
	}
	return serverConfig, nil
}

const (
	UsernameKey = "x-bytedev-username"
	XTTEnv      = "X-TT-ENV"
	XUsePPE     = "X-USE-PPE"
)
