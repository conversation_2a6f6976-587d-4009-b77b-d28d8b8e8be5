package http

import (
	"fmt"
	"net/http"
)

var (
	envstr2envMap = map[string]RunEnv{
		"local": RunEnvLocal,
		"prod":  RunEnvProd,
		"i18n":  RunEnvI18N,
		"boe":   RunEnvBOE,
		"ppe":   RunEnvPPE,
		"ttp":   RunEnvTTP,
	}
)

type BDHTTPClient[T any, U any] struct {
	client   *HttpClient[T, U]
	env      RunEnv
	username string
	jwtToken string
	lane     string
}

// NewBDHTTPClientWithEnvStr build new client by env
func NewBDHTTPClientWithEnvStr[T any, U any](c *http.Client, username string, jwt string, envStr string, lane string) (*BDHTTPClient[T, U], error) {
	env, ok := envstr2envMap[envStr]
	if !ok {
		return nil, fmt.Errorf("unknown env str: %s, accepted values are [prod, i18n, ttp, ppe, boe, local]", envStr)
	}

	return NewBDHTTPClient[T, U](c, username, jwt, env, lane), nil
}

func NewBDHTTPClient[T any, U any](c *http.Client, username string, jwt string, env RunEnv, lane string) *BDHTTPClient[T, U] {
	client := NewHTTPClient[T, U](c)

	return &BDHTTPClient[T, U]{
		client:   client,
		env:      env,
		jwtToken: jwt,
		username: username,
		lane:     lane,
	}
}

// DoBD is client to import tce through iac importer service
func (hc *BDHTTPClient[T, U]) DoBD(method string, headers map[string]string, queries map[string]string, req *T, uFunc URLConfigFunc) (*U, error) {
	config, configErr := uFunc(hc.env, hc.lane)
	if configErr != nil {
		return nil, configErr
	}

	for k, v := range config.Header {
		headers[k] = v
	}

	for k, v := range GetHeaders(hc.username, hc.jwtToken) {
		headers[k] = v
	}

	var resp *U
	resp, getErr := hc.client.Do(method, config.URI, headers, queries, req)
	if getErr != nil {
		return nil, fmt.Errorf("failed to get service config: %w", getErr)
	}

	return resp, nil
}
