package tce

import (
	"bytes"
	"github.com/stretchr/testify/assert"
	"testing"
)

const (
	SVCPath = "iac/ros/consumereb/service_template.yaml"
	SVCVal  = `targetSCMs:
- deploymentPath: toutiao/load
  name: toutiao/load
  scmId: 667
- deploymentPath: iac/ros/consumer
  main: true
  name: iac/ros/tceconsumer
  scmId: 294863
- deploymentPath: toutiao/runtime
  name: toutiao/runtime
  scmId: 631
tceService:
  bootstrapPath: iac/ros/consumer/bootstrap.sh
  image: toutiao.nodejs.v18:latest
  language: go
  name: iac_ros_consumereb
  parentId: 4776327
  serviceId: "4417595"
  users:
  - email: <EMAIL>
    name: yongning.hu
`
	CLRPath  = "iac/ros/consumereb/Federation/default/default_6438250/cluster_template.yaml"
	CLRPath2 = "iac/ros/consumereb/Federation/default/default_16438250/cluster_template.yaml"
	CLRPath3 = "iac/ros/consumereb/Federation/default/notDefault/cluster_template.yaml"
	CLRVal   = `tceClusters:
- SCMVersions:
  - name: toutiao/load
    version: 1.0.2.499
  - name: iac/ros/tceconsumer
    version: ********
  - name: toutiao/runtime
    version: 1.0.1.623
  cpuCount: 4
  hostType: docker
  memSize: 32
  zone: China-North
`
	CLRVal2 = `tceClusters:
- SCMVersions:
  - name: toutiao/load
    version: 1.0.2.499
  - name: iac/ros/tceconsumer
    version: ********
  - name: toutiao/runtime
    version: 1.0.1.623
  clusterId: "123456"
  cpuCount: 4
  hostType: docker
  memSize: 32
  zone: China-North
`
	HLPath  = "iac/ros/consumereb/Federation/default/default_6438250/HL/value.yaml"
	HLPath2 = "iac/ros/consumereb/Federation/default/default_16438250/HL/value.yaml"
	HLPath3 = "iac/ros/consumereb/Federation/default/notDefault/HL/value.yaml"
	HLVal   = `tceClusters:
- canaryReplicas:
  - idcCount: 0
  replicas:
  - idcCount: 1
`
	LFPath  = "iac/ros/consumereb/Federation/default/default_6438250/LF/value.yaml"
	LFPath2 = "iac/ros/consumereb/Federation/default/default_16438250/LF/value.yaml"
	LFPath3 = "iac/ros/consumereb/Federation/default/notDefault/LF/value.yaml"
	LFVal   = `tceClusters:
- canaryReplicas:
  - idcCount: 0
  replicas:
  - idcCount: 0
`
	LQPath  = "iac/ros/consumereb/Federation/default/default_6438250/LQ/value.yaml"
	LQPath2 = "iac/ros/consumereb/Federation/default/default_16438250/LQ/value.yaml"
	LQPath3 = "iac/ros/consumereb/Federation/default/notDefault/LQ/value.yaml"
	LQVal   = `tceClusters:
- canaryReplicas:
  - idcCount: 0
  replicas:
  - idcCount: 0
`
	YGPath  = "iac/ros/consumereb/Federation/default/default_6438250/YG/value.yaml"
	YGPath2 = "iac/ros/consumereb/Federation/default/default_16438250/YG/value.yaml"
	YGPath3 = "iac/ros/consumereb/Federation/default/notDefault/YG/value.yaml"
	YGVal   = `tceClusters:
- canaryReplicas:
  - idcCount: 0
  replicas:
  - idcCount: 0
`

	CompleteConfig = `targetSCMs:
- deploymentPath: toutiao/load
  name: toutiao/load
  scmId: 667
- deploymentPath: iac/ros/consumer
  main: true
  name: iac/ros/tceconsumer
  scmId: 294863
- deploymentPath: toutiao/runtime
  name: toutiao/runtime
  scmId: 631
tceClusters:
- canaryReplicas:
  - idc: HL
    idcCount: 0
  - idc: LF
    idcCount: 0
  - idc: LQ
    idcCount: 0
  - idc: YG
    idcCount: 0
  SCMVersions:
  - name: toutiao/load
    version: 1.0.2.499
  - name: iac/ros/tceconsumer
    version: ********
  - name: toutiao/runtime
    version: 1.0.1.623
  cpuCount: 4
  hostType: docker
  logicalCluster: default
  memSize: 32
  name: default
  physicalCluster: Federation
  replicas:
  - idc: HL
    idcCount: 1
  - idc: LF
    idcCount: 0
  - idc: LQ
    idcCount: 0
  - idc: YG
    idcCount: 0
  zone: China-North
  clusterId: "6438250"
- canaryReplicas:
  - idc: HL
    idcCount: 0
  - idc: LF
    idcCount: 0
  - idc: LQ
    idcCount: 0
  - idc: YG
    idcCount: 0
  SCMVersions:
  - name: toutiao/load
    version: 1.0.2.499
  - name: iac/ros/tceconsumer
    version: ********
  - name: toutiao/runtime
    version: 1.0.1.623
  cpuCount: 4
  hostType: docker
  logicalCluster: default
  memSize: 32
  name: default
  physicalCluster: Federation
  replicas:
  - idc: HL
    idcCount: 1
  - idc: LF
    idcCount: 0
  - idc: LQ
    idcCount: 0
  - idc: YG
    idcCount: 0
  zone: China-North
  clusterId: "16438250"
- canaryReplicas:
  - idc: HL
    idcCount: 0
  - idc: LF
    idcCount: 0
  - idc: LQ
    idcCount: 0
  - idc: YG
    idcCount: 0
  SCMVersions:
  - name: toutiao/load
    version: 1.0.2.499
  - name: iac/ros/tceconsumer
    version: ********
  - name: toutiao/runtime
    version: 1.0.1.623
  cpuCount: 4
  hostType: docker
  logicalCluster: default
  memSize: 32
  name: notDefault
  physicalCluster: Federation
  replicas:
  - idc: HL
    idcCount: 1
  - idc: LF
    idcCount: 0
  - idc: LQ
    idcCount: 0
  - idc: YG
    idcCount: 0
  zone: China-North
  clusterId: "123456"
tceService:
  bootstrapPath: iac/ros/consumer/bootstrap.sh
  image: toutiao.nodejs.v18:latest
  language: go
  name: iac_ros_consumereb
  parentId: 4776327
  psm: iac.ros.consumereb
  serviceId: "4417595"
  users:
  - email: <EMAIL>
    name: yongning.hu
`
)

var (
	resultMap = map[string][]byte{
		SVCPath:  []byte(SVCVal),
		CLRPath:  []byte(CLRVal),
		HLPath:   []byte(HLVal),
		LQPath:   []byte(LQVal),
		YGPath:   []byte(YGVal),
		LFPath:   []byte(LFVal),
		CLRPath2: []byte(CLRVal),
		HLPath2:  []byte(HLVal),
		LQPath2:  []byte(LQVal),
		YGPath2:  []byte(YGVal),
		LFPath2:  []byte(LFVal),
		CLRPath3: []byte(CLRVal2),
		HLPath3:  []byte(HLVal),
		LQPath3:  []byte(LQVal),
		YGPath3:  []byte(YGVal),
		LFPath3:  []byte(LFVal),
	}
)

func TestMakePathValMap(t *testing.T) {
	makeMap, mErr := makePathValueMap([]byte(CompleteConfig))
	assert.NoError(t, mErr)
	assert.True(t, len(makeMap) == len(resultMap))
	for k, _ := range makeMap {
		_, ok := resultMap[k]
		assert.True(t, ok)
		assert.True(t, bytes.Equal(makeMap[k], resultMap[k]))
	}
}
