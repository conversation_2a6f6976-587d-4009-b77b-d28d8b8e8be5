// Code generated by hertztool.

package main

import (
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/importer/biz/handler/mw"
	resource_import "code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/import"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/kms"
	"code.byted.org/middleware/hertz/byted"
)

func main() {
	byted.Init()
	kms.Init()
	resource_import.Init()
	r := byted.Default()
	r.Use(mw.AddEnv)
	r.Use(mw.AddJwtToken)
	r.Use(mw.AddUsername)

	register(r)
	r.Spin()
}
