package model

import (
	"code.byted.org/bytefaas/faas-go/events"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/model"
	"code.byted.org/gopkg/logs/v2/log"
	"github.com/bytedance/sonic"
)

func ConstructUpdateMetaDataResponse() (*events.EventResponse, error) {
	return &events.EventResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: []byte{},
	}, nil
}

func ConstructFetchIaCResponse(result *GetRawIaCResults) (*events.EventResponse, error) {
	respBody, mErr := sonic.Marshal(result)
	if mErr != nil {
		return model.ConstructEventErrResponse("failed to marshal response: %w", mErr)
	}

	log.V2.Info().KV("fetch iac response body", respBody).Emit()
	return &events.EventResponse{
		StatusCode: 200,
		Headers: map[string]string{
			"Content-Type": "application/json",
		},
		Body: respBody,
	}, nil
}
