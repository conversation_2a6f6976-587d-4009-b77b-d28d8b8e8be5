package handler

import (
	"fmt"
	"net/http"
	"strconv"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/consumers/permission/model"
	iachttp "code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/http"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/iac/sdk/processor"
	meta "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/blueprint/metapb"
	permission "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/blueprint/operation/permissionpb"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/lang/gg/collection/set"
	"google.golang.org/protobuf/proto"
)

const (
	cloudIAMCNURI    = "https://paas-gw.byted.org"
	cloudIAMI18NURI  = "https://paas-gw-i18n.byted.org"
	resultLinkFormat = "https://cloud.bytedance.net/console/acls/user-group/detail?id=%s"
)

type PermissionSystem int64

const (
	_ PermissionSystem = iota
	PermissionSystemCloudIAM
	PermissionSystemKani
	PermissionSystemTriton
)

type PermissionProcessor struct {
	processor.GenericProcessor
	permSystem PermissionSystem
	client     *http.Client
}

func NewPermissionProcessor(gp processor.GenericProcessor, permSystem PermissionSystem) (*PermissionProcessor, error) {
	pp := &PermissionProcessor{
		GenericProcessor: gp,
		permSystem:       permSystem,
		client:           iachttp.NewStdClient(),
	}

	return pp, nil
}

// PreprocessPermissionList preprocess PermissionList
// 1. Break permission whose Control_Panel == ALL into two permissions CN and I18N
func preprocessPermissionList(pl *permission.PermissionList) []*permission.Permission {
	var res []*permission.Permission
	for _, perm := range pl.Permissions {
		if perm.ControlPanel == permission.Permission_ALL {
			res = append(res, &permission.Permission{
				GroupName:    perm.GroupName,
				ControlPanel: permission.Permission_CN,
			})

			res = append(res, &permission.Permission{
				GroupName:    perm.GroupName,
				ControlPanel: permission.Permission_I18N,
			})
		} else {
			res = append(res, perm)
		}
	}

	return res
}

func (pp *PermissionProcessor) ProcessPermissionList(groups []*meta.Group, pl *permission.PermissionList) error {
	// preprocessing
	perms := preprocessPermissionList(pl)

	changeset := set.New[string](pp.Changes...)
	for _, perm := range perms {
		if changeset.Len() != 0 && !changeset.Contains(perm.GroupName) {
			// skip this permission only if pp.Changes has values and this permission's group name does not hit.
			// This means the permission's group has not changed so no need to check.
			// In case of pp.Changes is empty itself, this means it comes from cli instead of ci action, in this case,
			// we check all permissions.
			continue
		}

		match := false
		for _, newGroup := range groups {
			if newGroup.Name == perm.GroupName {
				match = true
				log.V2.Info().Str("[PermissionProcessor#ProcessPermissionList] processing cloudIAM custom group").
					KV("group name", newGroup.Name).Emit()

				oldGroup, oldGroupID, found, fErr := pp.findGroupInCloudIAMWithName(newGroup.Name, perm.ControlPanel)
				if fErr != nil {
					pp.Reporter.ReportErrWithName(newGroup.Name, "failed to find group in the remote permission system", fErr)
					continue
				}

				if !found {
					// not found in PermissionSystemCloudIAM, create a new one
					resultLink, uErr := pp.createCloudIAMGroup(newGroup, perm.ControlPanel)
					if uErr != nil {
						pp.Reporter.ReportErrWithName(newGroup.Name, "failed to create cloudIAM group", uErr)
						continue
					}

					log.V2.Info().Str("[PermissionProcessor#ProcessPermissionList] "+
						"created custom group in cloudIAM, reporting back...").
						KV("result link", resultLink).
						Emit()

					pp.Reporter.Report(newGroup.Name, resultLink, true)
					continue
				}

				if proto.Equal(oldGroup, newGroup) {
					// same, no need to update
					resultLink := fmt.Sprintf(resultLinkFormat, oldGroupID)
					log.V2.Info().Str("[PermissionProcessor#ProcessPermissionList] "+
						"no need to update custom group in cloudIAM").
						KV("resultLink", resultLink).Emit()

					pp.Reporter.Report(newGroup.Name, resultLink, true)
					continue
				}

				// update group in PermissionSystemCloudIAM
				resultLink, uErr := pp.updateCloudIAMGroup(newGroup, oldGroupID, perm.ControlPanel)
				if uErr != nil {
					return fmt.Errorf("failed to update cloudIAM group: %w", uErr)
				}

				log.V2.Info().Str("[PermissionProcessor#ProcessPermissionList] "+
					"updated custom group in cloudIAM").
					KV("result link", resultLink).
					Emit()

				pp.Reporter.Report(newGroup.Name, resultLink, true)
			}
		}
		if !match {
			pp.Reporter.ReportErrWithName(perm.GroupName, "failed to find a matching group",
				fmt.Errorf("group name %s in PermissionList message must match one of the name field in Group message, please check again", perm.GroupName))
		}
	}
	return nil
}

// findGroupInCloudIAMWithName find a PermissionSystemCloudIAM group matching groupName and convert it to meta.Group
// return: *meta.Group: meta.Group object
//
//	string: cloudIAM group ID
//	bool: found ot not
//	error: error in case any
func (pp *PermissionProcessor) findGroupInCloudIAMWithName(groupName string, panel permission.Permission_ControlPanel) (*meta.Group, string, bool, error) {
	cGroups, lErr := pp.listCloudIAMGroups(panel)
	if lErr != nil {
		return nil, "", false, fmt.Errorf("failed to list cloudIAM groups: %w", lErr)
	}

	// members field is set to null in list, so we need to call get with id again.
	for _, cGrp := range cGroups {
		if cGrp.Name == groupName {
			realCgrp, gErr := pp.getGroupWithID(cGrp.Id, panel)
			if gErr != nil {
				return nil, "", false, fmt.Errorf("failed to get cloudIAM group with id %s: %w", cGrp.Id, gErr)
			}

			group := &meta.Group{
				Name:        realCgrp.Name,
				Description: realCgrp.Description,
				Leaders:     buildEmployeesFromAdmins(realCgrp.Admins),
				Members:     buildEmployeesFromMembers(realCgrp.Members),
			}

			return group, cGrp.Id, true, nil
		}
	}

	return nil, "", false, nil
}

func (pp *PermissionProcessor) createCloudIAMGroup(group *meta.Group, panel permission.Permission_ControlPanel) (string, error) {
	createReq := &model.CreateCloudIAMGroupReq{
		Admins: getAdminsFromGroup(group),
		Members: model.MemberList{
			Users: getMembersFromGroup(group),
		},
		Description:   group.Description,
		DescriptionEn: group.Description,
		Name:          group.Name,
		Labels:        pp.buildCreateLabels(),
	}

	ep := fmt.Sprintf("%s/api/v1/group_manager/custom", getEndpoint(panel))

	queries := map[string]string{
		"must_valid_employee": "true",
	}

	hc := iachttp.NewHTTPClient[model.CreateCloudIAMGroupReq, model.CreateCloudIAMGroupResp](pp.client)

	resp, pErr := hc.Do(http.MethodPost, ep, getHeaders(panel), queries, createReq)
	if pErr != nil {
		return "", fmt.Errorf("failed to do http post: %w", pErr)
	}

	if resp.ErrorCode != 0 {
		return "", fmt.Errorf("failed to create custom group in cloudIAM, %s", resp.ErrMsg)
	}

	resultLink := fmt.Sprintf(resultLinkFormat, resp.Data.Id)
	log.V2.Info().Str("[createCloudIAMGroup] custom group created in cloudIAM").
		KV("resultLink", resultLink).Emit()

	return resultLink, nil
}

func (pp *PermissionProcessor) listCloudIAMGroups(panel permission.Permission_ControlPanel) ([]model.CloudIAMGroup, error) {
	var res []model.CloudIAMGroup
	curPage := 1
	pageSize := 20
	ep := fmt.Sprintf("%s/api/v1/group_manager/custom/list", getEndpoint(panel))
	hc := iachttp.NewHTTPClient[struct{}, model.ListCloudIAMGroupsResp](pp.client)
	queries := pp.buildQueryLabels()
	queries["size"] = strconv.Itoa(pageSize) // page size

	for {
		queries["page"] = strconv.Itoa(curPage)
		resp, gErr := hc.Do(http.MethodGet, ep, getHeaders(panel), queries, nil)
		if gErr != nil {
			return nil, fmt.Errorf("failed to do http post, current page %d: %w", curPage, gErr)
		}

		res = append(res, resp.Data...)

		if batchLen := len(resp.Data); batchLen < pageSize {
			// this is the last batch
			break
		}

		curPage++
	}

	return res, nil
}

func (pp *PermissionProcessor) getGroupWithID(groupID string, panel permission.Permission_ControlPanel) (*model.CloudIAMGroup, error) {
	ep := fmt.Sprintf("%s/api/v1/group_manager/custom?id=%s", getEndpoint(panel), groupID)

	hc := iachttp.NewHTTPClient[struct{}, model.GetCloudIAMGroupsResp](pp.client)

	gResp, gErr := hc.Do(http.MethodGet, ep, getHeaders(panel), nil, nil)
	if gErr != nil {
		return nil, fmt.Errorf("failed to do http get: %w", gErr)
	}

	return &gResp.Data, nil
}

// updateCloudIAMGroup update cloudIAM group with new group, returns link
func (pp *PermissionProcessor) updateCloudIAMGroup(newGroup *meta.Group, oldGroupID string, panel permission.Permission_ControlPanel) (string, error) {
	updateReq := &model.UpdateCloudIAMGroupReq{
		Admins: getAdminsFromGroup(newGroup),
		Members: model.MemberList{
			Users: getMembersFromGroup(newGroup),
		},
		Description:   newGroup.Description,
		DescriptionEn: newGroup.Description,
		Name:          newGroup.Name,
		ID:            oldGroupID,
		Labels:        pp.buildCreateLabels(),
	}

	ep := fmt.Sprintf("%s/api/v1/group_manager/custom", getEndpoint(panel))

	hc := iachttp.NewHTTPClient[model.UpdateCloudIAMGroupReq, model.UpdateCloudIAMGroupResp](pp.client)

	uResp, pErr := hc.Do(http.MethodPut, ep, getHeaders(panel), nil, updateReq)
	if pErr != nil {
		return "", fmt.Errorf("failed to do http get: %w", pErr)
	}

	if uResp.ErrorCode != 0 {
		return "", fmt.Errorf("failed to update custom group in cloudIAM, %s", uResp.ErrMsg)
	}

	resultLink := fmt.Sprintf(resultLinkFormat, uResp.Data.Id)
	log.V2.Info().Str("[updateCloudIAMGroup] custom group updated in cloudIAM").
		KV("resultLink", resultLink).Emit()

	return resultLink, nil
}

func (pp *PermissionProcessor) buildCreateLabels() map[string]string {
	return map[string]string{
		"app":      "blueprint",
		"location": fmt.Sprintf("%s/%s/%s", pp.Namespace, pp.RepoName, pp.Folder),
	}
}

func (pp *PermissionProcessor) buildQueryLabels() map[string]string {
	return map[string]string{
		"labels": fmt.Sprintf("app:blueprint,location:%s/%s/%s", pp.Namespace, pp.RepoName, pp.Folder),
	}
}

func getEndpoint(panel permission.Permission_ControlPanel) string {
	if panel == permission.Permission_CN {
		return cloudIAMCNURI
	}

	return cloudIAMI18NURI
}

func getHeaders(panel permission.Permission_ControlPanel) map[string]string {
	var svcAccount string
	if panel == permission.Permission_CN {
		svcAccount = "7830211c27c2faac6cfa0fbdf4edd704"
	} else {
		svcAccount = "711efb60e285a621573376c9937fa16c"
	}
	return map[string]string{
		"Domain":        "Cloud-IAM",
		"Authorization": fmt.Sprintf("Bearer %s", svcAccount),
	}

}

func buildEmployeesFromAdmins(admins []string) []*meta.Employee {
	employees := []*meta.Employee{}
	for _, member := range admins {
		employees = append(employees, &meta.Employee{
			Handle: member,
		})
	}

	return employees
}

func buildEmployeesFromMembers(members model.MemberList) []*meta.Employee {
	employees := []*meta.Employee{}
	for _, user := range members.Users {
		employees = append(employees, &meta.Employee{
			Handle: user,
		})
	}

	return employees
}

func getAdminsFromGroup(group *meta.Group) []string {
	admins := []string{}
	for _, leader := range group.Leaders {
		admins = append(admins, leader.Handle)
	}

	return admins
}

func getMembersFromGroup(group *meta.Group) []string {
	members := []string{}
	for _, member := range group.Members {
		members = append(members, member.Handle)
	}

	return members
}
