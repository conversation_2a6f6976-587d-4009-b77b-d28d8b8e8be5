package validators

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/strategist/biz/stubs/capacity_platform"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/strategist/biz/validator"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/strategist/model"
	resources "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rollout/resourcespb"
	rollout "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutpb"
	strategist "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/strategistpb"
	"code.byted.org/tiktok/wac/sdk/go/wac"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"
)

type RDSCapacityChecker struct{}

// RDSDBMapping Mapping rollout.DB_name to rds openapi
var RDSDBMapping = map[string]string{
	"NDB":     "NDB",
	"INNODB":  "InnoDB",
	"ROCKSDB": "RocksDB",
}

// getRDSEngineName convert resource engine name to openapi rds engine name
func getRDSEngineName(resourceEngineName string) string {
	if v, ok := RDSDBMapping[resourceEngineName]; ok {
		return v
	}
	return resourceEngineName
}

func (v *RDSCapacityChecker) Validate(r *rollout.Resource) *strategist.ValidatorResult {
	errorResult := &strategist.ValidatorResult{
		ValidatorName: v.GetName(),
		Msg:           "failed, without validate",
		IsSuccess:     false,
	}
	successResult := &strategist.ValidatorResult{
		ValidatorName: v.GetName(),
		Msg:           "success, without validate",
		IsSuccess:     true,
	}

	temp := r.GetSpec().GetTemplate()
	db := new(resources.RDSDB)
	err := anypb.UnmarshalTo(temp, db, proto.UnmarshalOptions{})
	if err != nil {
		errorResult.Msg = "unMarshal template error"
		return errorResult
	}

	req := new(model.RDSCapacityReq)
	req.VRegion = wac.ToVRegion(wac.VDCFromString(db.GetVdc())).Name()
	req.Vdc = db.GetVdc()
	req.IsSensitive = false
	req.Creator = db.Creator
	req.Dbname = db.GetDbName()
	req.DbConfig = model.DbConfig{
		GalaxyParentID:  db.GetDbConfig().GalaxyParentId,
		ImportanceLevel: db.GetDbConfig().ImportanceLevel.String(),
		SlowLogKill:     nil,
		SecurityLevel:   int(db.GetDbConfig().GetSecurityLevel().Number()),
	}
	req.DbMeta = model.DbMeta{
		Engine:              getRDSEngineName(db.GetDbMeta().GetEngine().String()),
		SyncSemi:            db.GetDbMeta().GetSyncSemi(),
		IsSharding:          db.GetDbMeta().GetIsSharding(),
		ShardingInstanceCnt: db.GetDbMeta().GetShardingInstanceCnt(),
		ShardingCnt:         db.GetDbMeta().GetShardingCnt(),
		Capacity:            db.GetDbMeta().GetCapacity(),
		IsShared:            db.GetDbMeta().GetIsShared(),
		WQps:                db.GetDbMeta().GetWqps(),
		RQps:                db.GetDbMeta().GetRqps(),
		Rto:                 0,
		DiffDataOutputType:  "",
	}

	rs, err := capacity_platform.CheckRdsCapacity(req)

	if err != nil {
		errorResult.Msg = "request capacity platform failed"
		return errorResult
	}

	if rs.Code != 0 {
		errorResult.Msg = fmt.Sprintf("got capacity platform statusCode error: %s", rs.Message)
		return errorResult
	}

	if !rs.Data.Ok {
		errorResult.Msg = fmt.Sprintf("capacity not enough: %s", rs.Data.Msg)
		return errorResult
	}
	successResult.Msg = "success, rds resource enough"

	return successResult
}

func (v *RDSCapacityChecker) Compatible(r *rollout.Resource) bool {
	return r.Metadata.Type == rollout.ResourceType_RDS_DB
}

func (v *RDSCapacityChecker) GetName() string {
	return "rds_capacity_checker"
}

func init() {
	validator.GetValidatorManager().RegisterValidator(&RDSCapacityChecker{})
}
