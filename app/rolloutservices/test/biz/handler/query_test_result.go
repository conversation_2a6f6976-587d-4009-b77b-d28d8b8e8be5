// Code generated by hertztool.

package handler

import (
	"context"
	"net/http"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/test/biz/testexec/db"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error/hertz_server"
	"code.byted.org/devinfra/hagrid/libs/errors"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/hertz/test"
	"code.byted.org/middleware/hertz/pkg/app"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/test/biz/testexec"
)

// QueryTestResult .
// @router /internal/test/runs/:id [GET]
func QueryTestResult(ctx context.Context, c *app.RequestContext) {
	req := new(test.QueryTestResultInput)
	id := c.Param("id")
	req.TestRunId = id

	testRunID := req.GetTestRunId()
	testMgr, exist := testexec.TestMgrMap[testRunID]
	var resp *test.QueryTestResultOutput
	if !exist {
		r, err := db.GetTestResult(ctx, id)
		if err != nil {
			hertz_server.ReturnError(c, err, http.StatusInternalServerError)
			return
		}
		if r == nil {
			err := errors.StatusNotFound.Newf("QueryTestResult(id=%s) not found", testRunID)
			hertz_server.ReturnError(c, err, http.StatusNotFound)
			return
		}
		resp = r
	} else {
		resp = testMgr.GetResultOutput()
	}

	hertz_server.WriteResp(c, resp)
}
