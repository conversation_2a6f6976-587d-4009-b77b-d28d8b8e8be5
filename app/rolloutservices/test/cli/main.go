package main

import (
	"context"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/test/biz/testexec"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/constvar"
	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error"
	"code.byted.org/devinfra/hagrid/libs/errors"
)

func outputError(str string) {
	_, _ = fmt.Fprintln(os.Stderr, "\033[31mERROR:", str, "\033[0m")
}

func main() {
	var env string
	var testName string
	var rootCmd = &cobra.Command{
		Use:   "test-cli",
		Short: "run test case locally",
		Run: func(cmd *cobra.Command, args []string) {
			if testName == "" {
				outputError("test name is required")
				os.Exit(1)
			}
			errors.SetCurrentService(errors.ServiceID(std_error.RolloutTest))
			testMgr, err := testexec.NewTestMgr(testName, nil)
			if err != nil {
				outputError(fmt.Sprintf("%+v", err))
				os.Exit(1)
			}
			ctx := context.Background()
			if env != "" {
				ctx = context.WithValue(ctx, constvar.KeyUsePpe, "1")
				ctx = context.WithValue(ctx, constvar.KeyLane, env)
			}
			testMgr.ExecuteAllTests(ctx)
		},
	}
	rootCmd.Flags().StringVarP(&env, "env", "e", "", "ppe name")
	rootCmd.Flags().StringVarP(&testName, "test", "t", "", "test name")
	cobra.CheckErr(rootCmd.Execute())
}
