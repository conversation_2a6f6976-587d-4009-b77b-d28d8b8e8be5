deploymentInputs:
  - deploymentName: org.devinfra.rollout.sot_change_owner_operator
    org: devinfra
    resourceInputs:
      - name: myengine1
        useDefault:
          defaultTceGen:
            tceSvcSot:
              repoNamespace: devinfra
              repoName: hagrid
              filePath: app/rolloutservices/test/conf/sot_change_tests/tce/svc/owner_operator/sot/tcesvc_before.yaml
              branch: main
            tceClsSots:
              - repoNamespace: devinfra
                repoName: hagrid
                filePath: app/rolloutservices/test/conf/sot_change_tests/tce/svc/owner_operator/sot/tcecls.yaml
                branch: main
    customStrategy:
      name: TestDeletion 
      deletionStrategy: DELETE