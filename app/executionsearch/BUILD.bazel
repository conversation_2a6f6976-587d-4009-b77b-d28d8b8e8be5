load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")
load("@io_bazel_rules_go//byted:def.bzl", "go_app")

go_library(
    name = "executionsearch_lib",
    srcs = [
        "config.go",
        "main.go",
    ],
    embedsrcs = [
        "conf/boe.yaml",
        "conf/prod.yaml",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/executionsearch",
    visibility = ["//visibility:private"],
    deps = [
        "//app/executionsearch/pkg/dal/mysql",
        "//app/executionsearch/pkg/handler",
        "//app/executionsearch/pkg/rpc",
        "//app/executionsearch/pkg/workspace",
        "@com_github_pkg_errors//:errors",
        "@in_gopkg_yaml_v3//:yaml_v3",
        "@org_byted_code_bytecloud_search_center_sdk//syncer",
        "@org_byted_code_bytefaas_faas_go//bytefaas",
        "@org_byted_code_gopkg_env//:env",
    ],
)

go_binary(
    name = "executionsearch",
    basename = "main",
    embed = [":executionsearch_lib"],
    gc_linkopts = [
        "-linkmode",
        "external",
        "-extldflags",
        "-static",
    ],
    visibility = ["//visibility:public"],
)

go_app(
    name = "pkg",
    bin = [":executionsearch"],
)
