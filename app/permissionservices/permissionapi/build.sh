#!/bin/bash

mkdir -p output

if [[ "${BUILD_PATH}" == "" ]]; then
    # This must be a local build.
    export BAZELISK_SKIP_UPDATE_CHECK=1
    HAGRID_ROOT=$( cd "$( dirname "${BASH_SOURCE[0]}" )"/../../.. && pwd )
    BUILD_PATH="${HAGRID_ROOT}"
    bash ${HAGRID_ROOT}/scripts/kitex_gen_rule.sh copy
    bazel run //:gazelle -- app/permissionservices/permissionapi
else

  ( cd app/permissionservices/permissionapi; go install code.byted.org/kite/kitex/tool/cmd/kitex@v1.17.2; go install github.com/cloudwego/thriftgo@latest; bash kitex-all.sh )

# Install Bytedance Bazelisk, Start
bash ${BUILD_PATH}/scripts/install_bazel.sh
# Install Bytedance Bazelisk, End
bazel run //:gazelle-update-go-repos
bazel run //:gazelle -- app/permissionservices/permissionapi
bazel run //:gazelle -- $(bazel query 'deps(//app/permissionservices/permissionapi:permissionapi)' | grep "^//" | sed 's|//||' | sed 's|:.*||' | sort | uniq)
fi

if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
fi

mkdir -p output/bin output/conf
cp script/bootstrap.sh output 2>/dev/null
chmod +x output/bootstrap.sh
cp script/bootstrap.sh output/bootstrap_staging.sh
chmod +x output/bootstrap_staging.sh
find conf/ -type f ! -name "*_local.*" | xargs -I{} cp {} output/conf/

RUN_NAME=bits.permission.api
bazel build //app/permissionservices/permissionapi:permissionapi
cp ${BUILD_PATH}/bazel-bin/app/permissionservices/permissionapi/permissionapi_/${RUN_NAME} output/bin/${RUN_NAME}

if [[ $OLD_PWD != "" ]]; then
  cp -r output/* $OLD_PWD/output/ || true
  bash ${BUILD_PATH}/scripts/post-build.scm.sh || true
fi
bash -c "$(curl -fsL https://tosv.byted.org/obj/uitesting/tos_upload_blame.sh)" || echo ""
