package service

import (
	"code.byted.org/bytecycle/go-middlewares/infra/initializer"
)

var (
	Group              *PermissionGroupService
	Role               *RoleService
	Principal          *PrincipalService
	Space              *SpaceService
	AuthWorkflowRecord *AuthWorkflowRecordService
	Authorization      *AuthorizationService
	CloudIam           *CloudIamService
)

var Module = initializer.MustRegisterPkg(mustInit)

func mustInit() {
	Group = &PermissionGroupService{}
	Role = &RoleService{}
	Principal = &PrincipalService{}
	Space = &SpaceService{}
	AuthWorkflowRecord = &AuthWorkflowRecordService{}
	Authorization = &AuthorizationService{}
	CloudIam = &CloudIamService{}
}
