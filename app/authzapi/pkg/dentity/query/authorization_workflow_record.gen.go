// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/authzapi/pkg/dentity/model"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"
	"gorm.io/plugin/dbresolver"
)

func newAuthorizationWorkflowRecord(db *gorm.DB, opts ...gen.DOOption) authorizationWorkflowRecord {
	_authorizationWorkflowRecord := authorizationWorkflowRecord{}

	_authorizationWorkflowRecord.authorizationWorkflowRecordDo.UseDB(db, opts...)
	_authorizationWorkflowRecord.authorizationWorkflowRecordDo.UseModel(&model.AuthorizationWorkflowRecord{})

	tableName := _authorizationWorkflowRecord.authorizationWorkflowRecordDo.TableName()
	_authorizationWorkflowRecord.ALL = field.NewAsterisk(tableName)
	_authorizationWorkflowRecord.ID = field.NewInt64(tableName, "id")
	_authorizationWorkflowRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_authorizationWorkflowRecord.UpdatedAt = field.NewTime(tableName, "updated_at")
	_authorizationWorkflowRecord.DeletedAt = field.NewField(tableName, "deleted_at")
	_authorizationWorkflowRecord.ResourceBrn = field.NewString(tableName, "resource_brn")
	_authorizationWorkflowRecord.Creator = field.NewString(tableName, "creator")
	_authorizationWorkflowRecord.UserType = field.NewString(tableName, "user_type")
	_authorizationWorkflowRecord.Username = field.NewString(tableName, "username")
	_authorizationWorkflowRecord.Roles = field.NewString(tableName, "roles")
	_authorizationWorkflowRecord.BpmID = field.NewInt64(tableName, "bpm_id")
	_authorizationWorkflowRecord.Status = field.NewString(tableName, "status")
	_authorizationWorkflowRecord.Reason = field.NewString(tableName, "reason")

	_authorizationWorkflowRecord.fillFieldMap()

	return _authorizationWorkflowRecord
}

type authorizationWorkflowRecord struct {
	authorizationWorkflowRecordDo authorizationWorkflowRecordDo

	ALL         field.Asterisk
	ID          field.Int64  // id
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 更新时间
	DeletedAt   field.Field  // 删除时间
	ResourceBrn field.String // 资源brn
	Creator     field.String // 工单发起人
	UserType    field.String // 用户类型
	Username    field.String // 用户名
	Roles       field.String // 角色名
	BpmID       field.Int64  // bpm工单ID
	Status      field.String // 状态,pending/approved/rejected/canceled
	Reason      field.String // 申请原因

	fieldMap map[string]field.Expr
}

func (a authorizationWorkflowRecord) Table(newTableName string) *authorizationWorkflowRecord {
	a.authorizationWorkflowRecordDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a authorizationWorkflowRecord) As(alias string) *authorizationWorkflowRecord {
	a.authorizationWorkflowRecordDo.DO = *(a.authorizationWorkflowRecordDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *authorizationWorkflowRecord) updateTableName(table string) *authorizationWorkflowRecord {
	a.ALL = field.NewAsterisk(table)
	a.ID = field.NewInt64(table, "id")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.DeletedAt = field.NewField(table, "deleted_at")
	a.ResourceBrn = field.NewString(table, "resource_brn")
	a.Creator = field.NewString(table, "creator")
	a.UserType = field.NewString(table, "user_type")
	a.Username = field.NewString(table, "username")
	a.Roles = field.NewString(table, "roles")
	a.BpmID = field.NewInt64(table, "bpm_id")
	a.Status = field.NewString(table, "status")
	a.Reason = field.NewString(table, "reason")

	a.fillFieldMap()

	return a
}

func (a *authorizationWorkflowRecord) WithContext(ctx context.Context) *authorizationWorkflowRecordDo {
	return a.authorizationWorkflowRecordDo.WithContext(ctx)
}

func (a authorizationWorkflowRecord) TableName() string {
	return a.authorizationWorkflowRecordDo.TableName()
}

func (a authorizationWorkflowRecord) Alias() string { return a.authorizationWorkflowRecordDo.Alias() }

func (a *authorizationWorkflowRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *authorizationWorkflowRecord) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 12)
	a.fieldMap["id"] = a.ID
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["deleted_at"] = a.DeletedAt
	a.fieldMap["resource_brn"] = a.ResourceBrn
	a.fieldMap["creator"] = a.Creator
	a.fieldMap["user_type"] = a.UserType
	a.fieldMap["username"] = a.Username
	a.fieldMap["roles"] = a.Roles
	a.fieldMap["bpm_id"] = a.BpmID
	a.fieldMap["status"] = a.Status
	a.fieldMap["reason"] = a.Reason
}

func (a authorizationWorkflowRecord) clone(db *gorm.DB) authorizationWorkflowRecord {
	a.authorizationWorkflowRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a authorizationWorkflowRecord) replaceDB(db *gorm.DB) authorizationWorkflowRecord {
	a.authorizationWorkflowRecordDo.ReplaceDB(db)
	return a
}

type authorizationWorkflowRecordDo struct{ gen.DO }

func (a authorizationWorkflowRecordDo) Debug() *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Debug())
}

func (a authorizationWorkflowRecordDo) WithContext(ctx context.Context) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a authorizationWorkflowRecordDo) ReadDB() *authorizationWorkflowRecordDo {
	return a.Clauses(dbresolver.Read)
}

func (a authorizationWorkflowRecordDo) WriteDB() *authorizationWorkflowRecordDo {
	return a.Clauses(dbresolver.Write)
}

func (a authorizationWorkflowRecordDo) Session(config *gorm.Session) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Session(config))
}

func (a authorizationWorkflowRecordDo) Clauses(conds ...clause.Expression) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a authorizationWorkflowRecordDo) Returning(value interface{}, columns ...string) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a authorizationWorkflowRecordDo) Not(conds ...gen.Condition) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a authorizationWorkflowRecordDo) Or(conds ...gen.Condition) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a authorizationWorkflowRecordDo) Select(conds ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a authorizationWorkflowRecordDo) Where(conds ...gen.Condition) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a authorizationWorkflowRecordDo) Exists(subquery interface{ UnderlyingDB() *gorm.DB }) *authorizationWorkflowRecordDo {
	return a.Where(field.CompareSubQuery(field.ExistsOp, nil, subquery.UnderlyingDB()))
}

func (a authorizationWorkflowRecordDo) Order(conds ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a authorizationWorkflowRecordDo) Distinct(cols ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a authorizationWorkflowRecordDo) Omit(cols ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a authorizationWorkflowRecordDo) Join(table schema.Tabler, on ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a authorizationWorkflowRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a authorizationWorkflowRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a authorizationWorkflowRecordDo) Group(cols ...field.Expr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a authorizationWorkflowRecordDo) Having(conds ...gen.Condition) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a authorizationWorkflowRecordDo) Limit(limit int) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a authorizationWorkflowRecordDo) Offset(offset int) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a authorizationWorkflowRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a authorizationWorkflowRecordDo) Unscoped() *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Unscoped())
}

func (a authorizationWorkflowRecordDo) Create(values ...*model.AuthorizationWorkflowRecord) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a authorizationWorkflowRecordDo) CreateInBatches(values []*model.AuthorizationWorkflowRecord, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a authorizationWorkflowRecordDo) Save(values ...*model.AuthorizationWorkflowRecord) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a authorizationWorkflowRecordDo) First() (*model.AuthorizationWorkflowRecord, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AuthorizationWorkflowRecord), nil
	}
}

func (a authorizationWorkflowRecordDo) Take() (*model.AuthorizationWorkflowRecord, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AuthorizationWorkflowRecord), nil
	}
}

func (a authorizationWorkflowRecordDo) Last() (*model.AuthorizationWorkflowRecord, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AuthorizationWorkflowRecord), nil
	}
}

func (a authorizationWorkflowRecordDo) Find() ([]*model.AuthorizationWorkflowRecord, error) {
	result, err := a.DO.Find()
	return result.([]*model.AuthorizationWorkflowRecord), err
}

func (a authorizationWorkflowRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AuthorizationWorkflowRecord, err error) {
	buf := make([]*model.AuthorizationWorkflowRecord, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a authorizationWorkflowRecordDo) FindInBatches(result *[]*model.AuthorizationWorkflowRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a authorizationWorkflowRecordDo) Attrs(attrs ...field.AssignExpr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a authorizationWorkflowRecordDo) Assign(attrs ...field.AssignExpr) *authorizationWorkflowRecordDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a authorizationWorkflowRecordDo) Joins(fields ...field.RelationField) *authorizationWorkflowRecordDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a authorizationWorkflowRecordDo) Preload(fields ...field.RelationField) *authorizationWorkflowRecordDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a authorizationWorkflowRecordDo) FirstOrInit() (*model.AuthorizationWorkflowRecord, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AuthorizationWorkflowRecord), nil
	}
}

func (a authorizationWorkflowRecordDo) FirstOrCreate() (*model.AuthorizationWorkflowRecord, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AuthorizationWorkflowRecord), nil
	}
}

func (a authorizationWorkflowRecordDo) FindByPage(offset int, limit int) (result []*model.AuthorizationWorkflowRecord, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a authorizationWorkflowRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a authorizationWorkflowRecordDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a authorizationWorkflowRecordDo) Delete(models ...*model.AuthorizationWorkflowRecord) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *authorizationWorkflowRecordDo) withDO(do gen.Dao) *authorizationWorkflowRecordDo {
	a.DO = *do.(*gen.DO)
	return a
}
