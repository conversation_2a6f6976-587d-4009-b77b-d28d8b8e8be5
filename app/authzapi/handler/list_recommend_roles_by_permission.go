package handler

import (
	"code.byted.org/devinfra/hagrid/app/authzapi/pkg/dal/tcc"
	"code.byted.org/gopkg/lang/slices"
	"context"
	"fmt"
	"sort"
	"strings"

	"code.byted.org/devinfra/hagrid/app/authzapi/biz/middleware"
	"code.byted.org/devinfra/hagrid/app/authzapi/biz/utils"
	consts "code.byted.org/devinfra/hagrid/app/authzapi/pkg/const"
	"code.byted.org/devinfra/hagrid/app/authzapi/pkg/handler"
	"code.byted.org/devinfra/hagrid/app/authzapi/pkg/service"
	"code.byted.org/devinfra/hagrid/pkg/brn"
	pb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
	"code.byted.org/middleware/hertz/pkg/app"
	"github.com/ahmetb/go-linq/v3"
)

func ListRecommendRolesByPermission(ctx context.Context, c *app.RequestContext, req *pb.ListRecommendRolesByPermissionRequest, bpmWorkflowPrefix string) (resp *pb.ListRecommendRolesByPermissionResponse, err error) {
	// Check if the request is invalid
	if IsInvalidListRecommendRolesByPermissionRequest(req) {
		return nil, fmt.Errorf("invalid request")
	}
	// Get the accept language from the context
	lang := utils.GetAcceptLanguage(c)
	// Initialize the platform variable
	platform := ""
	// Check if the CommonParams field is not nil
	if req.CommonParams != nil {
		platform = req.CommonParams.Platform
	}
	// Check if the permission ends with ".acl.write"
	if strings.HasSuffix(req.Permission, ".acl.write") {
		platform = req.CommonParams.AclPlatform
	}
	appCenterRoleConfigs, err := tcc.QueryAppCenterRoleConfigs(ctx)
	if err != nil {
		return nil, err
	}
	// Check if the platform is not empty and not equal to AppCenterPlatforms
	if len(platform) != 0 && !slices.ContainsString(appCenterRoleConfigs.SupportedPlatforms, platform) {
		// Get the roles by platform
		roles, err := handler.CloudIAM.GetRolesByPlatform(ctx, platform, req.CommonParams.Env, lang, req.Permission)
		if err != nil {
			return nil, err
		}
		var res []*pb.RecommendRole
		resp = &pb.ListRecommendRolesByPermissionResponse{}
		// Select the RoleMeta field from each Role object and create a RecommendRole object
		linq.From(roles).Select(func(i interface{}) interface{} {
			if r, ok := i.(*pb.Role); ok {
				return &pb.RecommendRole{RoleMeta: r.RoleMeta}
			}
			return &pb.RecommendRole{}
		}).ToSlice(&res)
		resp.RecommendedRoles = res
		return resp, nil
	}
	// Get the resource info from the header
	resourceBrn, err := utils.GetResourceInfoFromHeader(c)
	if err != nil {
		return
	}
	resourceBrnInfo, err := brn.ParseBRN(resourceBrn)
	if err != nil {
		return
	}
	var workspaceType string
	_, workspaceType, err = handler.Space.GetResourceSpaceInfo(ctx, resourceBrn)
	if err != nil {
		return
	}
	// Get the user from the context
	user := middleware.GetUser(c)
	if user == nil {
		err = fmt.Errorf("user not found")
		return
	}
	workspaceBrn := resourceBrn
	// Check if the resource is not a space
	if resourceBrnInfo.ResourceType != consts.ResourceSpaceType {
		// Get the ancestor space BRN of the resource
		var spaceBrn string
		_, spaceBrn, err = handler.Space.GetAncestorSpaceBrnOfResource(ctx, resourceBrn)
		if err != nil {
			return
		}
		if len(spaceBrn) != 0 {
			workspaceBrn = spaceBrn
		}
	}
	resp, err = handler.Role.ListRecommendRolesByPermission(ctx, service.ListRecommendRoleParam{
		Permission:             req.GetPermission(),
		CurUser:                user.Username,
		ResourceBrn:            resourceBrn,
		WorkspaceType:          workspaceType,
		PrincipalList:          req.GetPrincipalBrns(),
		RoleBrn:                req.GetRoleBrn(),
		NeedRecommendRoleByTag: req.GetNeedRecommendRoleByTag(),
		Lang:                   lang,
		NeedAncestorRole:       req.GetNeedAncestorRole(),
		WorkspaceBrn:           workspaceBrn,
	})
	if err != nil {
		return
	}
	var roleBrns []string
	linq.From(resp.RecommendedRoles).Select(func(i interface{}) interface{} {
		role := i.(*pb.RecommendRole)
		return role.RoleMeta.RoleBrn
	}).ToSlice(&roleBrns)
	principalBrn := brn.GenPrincipalBRN(brn.TypeUserAccount, user.Username).String()
	if len(req.GetPrincipalBrns()) != 0 {
		principalBrn = req.GetPrincipalBrns()[0]
	}
	workflowRecords, err := handler.AuthWorkflowRecord.QueryPendingAuthWorkflowRecords(ctx, resourceBrn, roleBrns, principalBrn)
	if err != nil {
		return nil, err
	}
	roleWorkflowUrls := make(map[string]string)
	for _, record := range workflowRecords {
		roleWorkflowUrls[record.Roles[0]] = fmt.Sprintf("%s%d", bpmWorkflowPrefix, *record.BPMID)
	}
	// 调整推荐角色禁用原因
	if req.GetScene() != pb.RoleRecommendScene_ROLE_RECOMMEND_SCENE_UNSPECIFIED {
		fillDisabledReason(resp, roleWorkflowUrls, req, lang)
	}
	return resp, nil
}

func fillDisabledReason(resp *pb.ListRecommendRolesByPermissionResponse, roleWorkflowUrls map[string]string, req *pb.ListRecommendRolesByPermissionRequest, lang string) {
	for _, role := range resp.RecommendedRoles {
		workflowUrl, exists := roleWorkflowUrls[role.RoleMeta.RoleBrn]
		if exists {
			if req.Scene != pb.RoleRecommendScene_ROLE_RECOMMEND_SCENE_GRANT {
				role.DisabledReason = pb.RoleDisabledReason_ROLE_DISABLED_REASON_PENDING_AUTH_WORKFLOW
			}
		}
		if !role.HasAuthPermission && req.Scene != pb.RoleRecommendScene_ROLE_RECOMMEND_SCENE_APPLY {
			role.DisabledReason = pb.RoleDisabledReason_ROLE_DISABLED_REASON_NO_AUTH_PERMISSION
		}
		role.WorkflowUrl = workflowUrl
		// 已授权角色不禁用
		if role.Granted {
			role.DisabledReason = pb.RoleDisabledReason_ROLE_DISABLED_REASON_UNSPECIFIED
		}
		if role.DisabledReason != pb.RoleDisabledReason_ROLE_DISABLED_REASON_UNSPECIFIED {
			role.SystemRecommended = false
		}
		role.RoleDisabledMsgFmt = consts.GetRoleDisableMsgFmtByScene(req.Scene, role.DisabledReason, lang)
	}
	// 不可选角色排序规则：推荐的 -> 已有工单的 -> 无授权权限的 -> 授权主体限制的 -> 数量限制的
	sort.SliceStable(resp.RecommendedRoles, func(i, j int) bool {
		iRole, jRole := resp.RecommendedRoles[i], resp.RecommendedRoles[j]
		if iRole.SystemRecommended && !jRole.SystemRecommended && iRole.DisabledReason == pb.RoleDisabledReason_ROLE_DISABLED_REASON_UNSPECIFIED {
			return true
		}
		if !iRole.SystemRecommended && jRole.SystemRecommended && jRole.DisabledReason == pb.RoleDisabledReason_ROLE_DISABLED_REASON_UNSPECIFIED {
			return false
		}
		order := map[pb.RoleDisabledReason]int{
			pb.RoleDisabledReason_ROLE_DISABLED_REASON_UNSPECIFIED:                0,
			pb.RoleDisabledReason_ROLE_DISABLED_REASON_PENDING_AUTH_WORKFLOW:      1,
			pb.RoleDisabledReason_ROLE_DISABLED_REASON_NO_AUTH_PERMISSION:         2,
			pb.RoleDisabledReason_ROLE_DISABLED_REASON_PRINCIPAL_TYPE_NOT_ALLOWED: 3,
			pb.RoleDisabledReason_ROLE_DISABLED_REASON_GRANT_REACH_LIMIT:          4,
		}

		iOrder, iExists := order[iRole.DisabledReason]
		jOrder, jExists := order[jRole.DisabledReason]
		if !iExists && !jExists {
			return false // Keep the order unchanged if both are unspecified.
		}
		if !iExists {
			return false // Put unspecified before specified reasons.
		}
		if !jExists {
			return true // Put specified reasons before unspecified.
		}
		if iOrder != jOrder {
			return iOrder < jOrder
		}
		return true
	})
}

func IsInvalidListRecommendRolesByPermissionRequest(*pb.ListRecommendRolesByPermissionRequest) bool {
	return false
}
