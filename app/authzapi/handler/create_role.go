package handler

import (
	"context"

	pb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
	"code.byted.org/middleware/hertz/pkg/app"
)

type CreateRoleRequest pb.CreateRoleRequest

func CreateRole(ctx context.Context, c *app.RequestContext, req *pb.CreateRoleRequest) (resp *pb.CreateRoleResponse, err error) {
	// TODO (linyuhang):
	return nil, nil
}

func (r *CreateRoleRequest) IsValidRequest() bool {
	return r.Role != nil && r.Role.RoleMeta != nil
}
