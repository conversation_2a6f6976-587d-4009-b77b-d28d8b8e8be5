#!/usr/bin/env bash
RUN_NAME="bits.trigger.rpc"

if [[  "$BUILD_FILE" != ""  ]]; then
  OLD_PWD=$PWD
  cd $(dirname $BUILD_FILE)
fi

mkdir -p output/bin output/conf
cp script/* output/
cp conf/* output/conf/
chmod +x output/bootstrap.sh

if [ "$IS_SYSTEM_TEST_ENV" != "1" ]; then
    # Install Bytedance Bazelisk, Start
    bash ${BUILD_PATH}/scripts/install_bazel.sh
    # Install Bytedance Bazelisk, End
    if [ "$BUILD_TYPE" = "offline" -o "$BUILD_TYPE" = "test" ]; then
      bazel build -c dbg //app/triggerrpc:triggerrpc --@io_bazel_rules_go//go/config:gc_goopts="-l,-N" --strip=never
    else
      bazel build //app/triggerrpc:triggerrpc
    fi
    cp ${BUILD_PATH}/bazel-bin/app/triggerrpc/triggerrpc_/${RUN_NAME} output/bin/${RUN_NAME}
    # go build -o output/bin/${RUN_NAME}
else
    go test -c -covermode=set -o output/bin/${RUN_NAME} -coverpkg=./...
fi

if [[ $OLD_PWD != "" ]]; then
  cp -r output $OLD_PWD
  bash ${BUILD_PATH}/scripts/post-build.scm.sh
fi
bash -c "$(curl -fsL https://tosv.byted.org/obj/uitesting/tos_upload_blame.sh)" || echo ""
