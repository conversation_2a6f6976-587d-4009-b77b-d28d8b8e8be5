package cron

import (
	"fmt"
	"time"

	"github.com/panjf2000/ants/v2"
)

type Option func(cs *scheduler)

func WithPoolSize(size int) Option {
	return func(cs *scheduler) {
		var pool *ants.Pool
		if size > 0 {
			opts := ants.Options{
				ExpiryDuration: 3 * time.Minute,
				Nonblocking:    true,
			}
			var err error
			pool, err = ants.NewPool(size, ants.WithOptions(opts))
			if err != nil {
				panic(fmt.Sprintf("init goroutine pool failed, error: %s", err))
			}
		}
		cs.pool = pool
	}
}
