package redis

import (
	"code.byted.org/devinfra/hagrid/pkg/redis"
	"code.byted.org/kv/goredis"
	"github.com/alicebob/miniredis"
	"strconv"
)

var Redis *goredis.Client

// MustInitialize initialize redis connection
func MustInitialize(config *Config) {
	goredis.UseMiddleWare(&redis.CmderLoggerMiddleware{})
	if config.PSM != "" {
		Redis = redis.MustConnect(redis.NewPSMEndpoint(config.PSM))
	} else {
		Redis = redis.MustConnect(redis.NewIPEndpoint(config.Host, config.Port, nil, nil))
	}
}

func InitTestRedis() {
	redisServer, err := miniredis.Run()
	if err != nil {
		panic(err)
	}
	port, _ := strconv.Atoi(redisServer.Port())
	Redis = redis.MustConnect(redis.NewIPEndpoint(redisServer.Host(), port, nil, nil))
}
