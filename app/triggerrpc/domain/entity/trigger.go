package entity

import "context"

type ITrigger interface {
	// GetTriggerUser 获得触发器触发事件的触发人
	// huxin.0804: 用户可以设置成自定义的中文和英文，导致下游使用username失败。不要用username，用email前缀
	GetTriggerUser(ctx context.Context) (string, error)

	// SetTriggerUser 设置触发器触发事件的触发人
	SetTriggerUser(username string) error

	// GetTriggerType 获得触发事件类型
	GetTriggerType() TriggerType

	// GetPipelineId 获得触发事件的流水线
	GetPipelineId() uint64

	// GetTriggerName 获得触发器名称
	GetTriggerName() string

	GetExecutorType() ExecutorType
}
