package repository

import (
	"code.byted.org/devinfra/hagrid/app/pipelinedelete/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"context"
	"errors"
	"gorm.io/gorm"
)

type IApiServerBuildRepo interface {
	GetApiServerBuildList(ctx context.Context, pipelineIDs []uint64) ([]*entity.DBApiServerBuild, error)
}

type apiServerBuildRepo struct {
	db *gorm.DB
}

func newApiServerBuildRepo(db *gorm.DB) IApiServerBuildRepo {
	return &apiServerBuildRepo{
		db: db,
	}
}

func (p *apiServerBuildRepo) GetApiServerBuildList(ctx context.Context, ids []uint64) ([]*entity.DBApiServerBuild, error) {
	apiServerBuildList := make([]*entity.DBApiServerBuild, 0)
	if err := p.db.WithContext(ctx).
		Where("id in (?)", ids).
		Find(&apiServerBuildList).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, bits_err.PIPELINECOMMON.ErrRecordNotFound
		}
		return nil, bits_err.PIPELINECOMMON.ErrDBErr.AddOrPass(ctx, err)
	}
	return apiServerBuildList, nil
}
