load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = [
        "array.go",
        "backoff.go",
        "const.go",
        "error.go",
        "i18n.go",
        "string.go",
        "url.go",
        "util.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/atommanagerpc/pkg/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/atommanage:atommanage_go_proto",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_bytedance_sonic//ast",
        "@com_github_cenkalti_backoff_v4//:backoff",
        "@org_byted_code_gopkg_env//:env",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_test(
    name = "utils_test",
    srcs = [
        "array_test.go",
        "backoff_test.go",
        "string_test.go",
    ],
    embed = [":utils"],
    deps = [
        "@com_github_pkg_errors//:errors",
        "@com_github_stretchr_testify//assert",
        "@tools_gotest_v3//assert",
    ],
)
