package middleware

import (
	"encoding/json"

	"github.com/cloudwego/kitex/pkg/kerrors"
	"google.golang.org/genproto/googleapis/rpc/code"
)

const (
	ClientError  = 1
	ServiceError = 2

	marshalErr = string(`{"code": 2, "message": "internal err, json marshal failed"}`)
)

var _ kerrors.BizStatusErrorIface = &ErrResp{}

type ErrResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (e ErrResp) Error() string {
	bt, err := json.Marshal(e)
	if err != nil {
		return marshalErr
	}
	return string(bt)
}

// BizExtra implements kerrors.BizStatusErrorIface.
func (ErrResp) BizExtra() map[string]string {
	return nil
}

// BizMessage implements kerrors.BizStatusErrorIface.
func (e ErrResp) BizMessage() string {
	return e.Message
}

// BizStatusCode implements kerrors.BizStatusErrorIface.
func (e ErrResp) BizStatusCode() int32 {
	return int32(e.Code)
}

func (e ErrResp) As(err any) bool {
	if target, ok := err.(*ErrResp); ok {
		target.Code = e.Code
		target.Message = e.Message
		return true
	}
	return false
}

func WrapServiceError(message string) error {
	return &ErrResp{
		Code:    int(code.Code_INTERNAL),
		Message: message,
	}
}

func WrapClientError(message string) error {
	return &ErrResp{
		Code:    int(code.Code_INVALID_ARGUMENT),
		Message: message,
	}
}
