package rpc

import (
	"context"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"code.byted.org/gopkg/logs"
)

func (s *AtomMarketServiceImpl) CreateCloudIDE(ctx context.Context, req *atommanagepb.CreateCloudIDERequest) (r *atommanagepb.CreateCloudIDEResponse, err error) {
	r, err = s.AtomMarketUseCase.CreateCloudIDE(ctx, req)
	if err != nil {
		logs.CtxError(ctx, "CreateCloudIDE failed : %v", err)
		return nil, err
	}
	return r, nil
}
