package atom_create

import (
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/pkg/utils"
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/app/atommanagerpc/conf"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/entity"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/pkg/tcc"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/gopkg/logs"
)

type AtomCreateRelatedApi struct {
	JobAtomCreateUrl string
	pplSdk           PipelineInterface
}

func NewAtomCreateRelatedApi(cfg conf.AtomCreateRelated) *AtomCreateRelatedApi {
	return &AtomCreateRelatedApi{
		JobAtomCreateUrl: cfg.AtomCallBackUrl,
		pplSdk:           newPipelineSdk(),
	}
}

// SendCreationRequest : send create job atom service request
func (f *AtomCreateRelatedApi) SendCreationRequest(ctx context.Context, uniqId string, creator string, entity *entity.ServiceAtomMeta) (*platformpb.PipelineRun, error) {

	uniqueId := uniqId
	uniqueId = strings.ToLower(uniqueId)
	uniqueId = strings.ReplaceAll(uniqueId, "-", "_")

	psm := fmt.Sprintf("toutiao.devops.%s", uniqueId)
	gitRepo := fmt.Sprintf("devops/%s", uniqueId)
	scmRepo := fmt.Sprintf("toutiao/devops/%s", uniqueId)

	if entity.Region == atommanagepb.RegionEnum_REGION_ENUM_UNSPECIFIED {
		entity.Region = atommanagepb.RegionEnum_REGION_ENUM_CN
	}
	atomRegion := utils.RegionMap(entity.Region)

	createVariables := map[string]interface{}{
		"service_name":  uniqId,
		"atom_type":     entity.ServiceType,
		"atom_language": entity.ServiceLanguage,
		"atom_region":   atomRegion,
		"atom_id":       entity.JobAtomId,
		"atom_psm":      psm,
		"git_repo_name": gitRepo,
		"scm_repo_name": scmRepo,
	}
	if entity.GalaxyId > 0 {
		createVariables["override_settings"] = map[string]interface{}{
			"AtomGalaxyId": entity.GalaxyId,
		}
	}
	options, optionsErr := tcc.GetJobAtomCreateDefaultOptions(ctx)
	if optionsErr != nil {
		return nil, optionsErr
	}
	for _, option := range options {
		if option.Region == atomRegion &&
			option.Language == entity.ServiceLanguage.String() &&
			option.SdkIaasType == entity.ServiceType.String() {
			//	perfect matched type
			for k, v := range option.Variables {
				// append default variables
				createVariables[k] = v
			}
			v := url.Values{}
			v.Add("atom_uid", uniqId)
			v.Add("create_user", creator)
			v.Add("region", fmt.Sprintf("%v", int32(entity.Region)))
			build, buildErr := f.pplSdk.TriggerBuild(ctx, option.CreatePipelineId, createVariables,
				creator, fmt.Sprintf("%s/create_service_callback?%s", f.JobAtomCreateUrl, v.Encode()))
			if buildErr != nil {
				return nil, buildErr
			}
			logs.CtxInfo(ctx, "trigger build for service %s create get build id %d", uniqId, build)
			return build, nil
		}
	}
	return nil, errors.Errorf("applied request region %v & language %v & iaas %v exceed definitions", atomRegion, entity.ServiceLanguage.String(), entity.ServiceType.String())
}

// SendCreateTestEnvReq : invoke pipeline to create ppe / boe env
func (f *AtomCreateRelatedApi) SendCreateTestEnvReq(ctx context.Context, user string,
	serviceMeta *entity.ServiceAtomMeta,
	createEnv *entity.AtomTestEnv,
	atomMeta *entity.AtomMeta) (*platformpb.PipelineRun, error) {

	options, getTccErr := tcc.GetJobAtomCreateTestEnvOptions(ctx)
	if getTccErr != nil {
		return nil, getTccErr
	}

	region := createEnv.Region
	if serviceMeta.Region == atommanagepb.RegionEnum_REGION_ENUM_CN && serviceMeta.IsI18n {
		region = atommanagepb.RegionEnum_REGION_ENUM_SG
	}

	for _, option := range options {
		//if option.SdkIaasType == serviceMeta.ServiceType.String() {
		if option.SdkIaasType == serviceMeta.ServiceType.String() && option.Region ==
			utils.RegionToString(region) {

			createVariables := map[string]interface{}{
				"scm_repo":     serviceMeta.ScmRepoName,
				"branch":       createEnv.Branch,
				"env_name":     createEnv.Name,
				"psm":          serviceMeta.Psm,
				"atom_uid":     atomMeta.UniqueId,
				"code_repo":    serviceMeta.GitRepo,
				"service_iaas": serviceMeta.ServiceType.String(),
				"atom_gen_id":  atomMeta.GenId,
				"env_id":       createEnv.GenId,
			}
			for k, v := range option.Variables {
				createVariables[k] = v
			}
			v := url.Values{}
			v.Add("atom_gen_id", strconv.FormatInt(atomMeta.GenId, 10))
			v.Add("env_name", createEnv.Name)
			v.Add("create_user", user)
			build, triggerBuildErr := f.pplSdk.TriggerBuild(ctx, option.CreatePipelineId, createVariables,
				user, fmt.Sprintf("%s/create_env_callback?%s", f.JobAtomCreateUrl, v.Encode()))
			if triggerBuildErr != nil {
				return nil, triggerBuildErr
			}
			logs.CtxInfo(ctx, "trigger build %d", build)
			return build, nil
		}
	}

	return nil, bits_err.ATOMMARKET.ErrCopier.AddError(errors.New("no matching create options found"))
}

// SendDeleteTestEnvReq : invoke pipeline to delete ppe / boe env
func (f *AtomCreateRelatedApi) SendDeleteTestEnvReq(ctx context.Context, user string,
	serviceMeta *entity.ServiceAtomMeta,
	testEnv *entity.AtomTestEnv) (*platformpb.PipelineRun, error) {

	options, getTccErr := tcc.GetJobAtomCreateTestEnvOptions(ctx)
	if getTccErr != nil {
		return nil, getTccErr
	}
	logs.CtxInfo(ctx, "user %s delete test env gen id = %d name = %s for atom id = %d", user, testEnv.GenId, testEnv.Name, testEnv.JobAtomId)

	region := serviceMeta.Region
	if serviceMeta.Region == atommanagepb.RegionEnum_REGION_ENUM_CN && serviceMeta.IsI18n {
		region = atommanagepb.RegionEnum_REGION_ENUM_SG
	}

	for _, option := range options {
		if option.Region == utils.RegionToString(region) &&
			option.SdkIaasType == serviceMeta.ServiceType.String() {
			varMap := map[string]interface{}{
				"ppe_name":  testEnv.Name,
				"atom_psm":  serviceMeta.Psm,
				"atom_type": utils.SdkIaasTypeToString(serviceMeta.ServiceType),
			}
			pplRun, triggerErr := f.pplSdk.TriggerBuild(ctx, option.DeletePipelineId, varMap, user, "")
			if triggerErr != nil {
				logs.CtxError(ctx, "failed trigger delete pipeline %d get err %s", option.DeletePipelineId, triggerErr.Error())
				return nil, triggerErr
			}
			return pplRun, nil
		}
	}
	return nil, errors.New("no matching service option found!")
}

// CancelPipelineRun: cancel pipeline run
func (f *AtomCreateRelatedApi) CancelPipelineRun(ctx context.Context, user string, pplRunID uint64) error {
	if pplRunID != 0 {
		return f.pplSdk.CancelPipelineRun(ctx, pplRunID, user)
	}
	return nil
}

// SendFECreateReq : send create fe goofy job atom service request
func (f *AtomCreateRelatedApi) SendFECreateReq(ctx context.Context, uniqId string, creator string) (*platformpb.PipelineRun, int64, error) {
	createVariables := map[string]interface{}{
		"service_name": uniqId,
	}
	option, optionsErr := tcc.GetFEJobAtomCreateOption(ctx)
	if optionsErr != nil {
		return nil, 0, optionsErr
	}
	var spaceId int64
	var err error
	for k, v := range option.Variables {
		// append default variables
		createVariables[k] = v
		if k == "space_id" {
			if i, ok := v.(json.Number); ok {
				if spaceId, err = i.Int64(); err != nil {
					return nil, 0, err
				}
			}
		}
	}
	build, buildErr := f.pplSdk.TriggerBuild(ctx, option.CreatePipelineId, createVariables,
		creator, "")
	if buildErr != nil {
		return nil, spaceId, buildErr
	}
	logs.CtxInfo(ctx, "trigger build for service %s create get build id %d", uniqId, build)
	return build, spaceId, nil
}

// SendExtendAtomCreationRequest : send create job atom service request
// 当前先和 SendCreationRequest 里面公用一个逻辑，但为一个函数，是为了以后如果用户仓库不是 devops前缀的，
// 则不能直接使用 cn\eu等原子创建的流水线，得新配置流水线 兼容比如 cn gitlab 已存在直接 创建eu faas的逻辑
func (f *AtomCreateRelatedApi) SendExtendAtomCreationRequest(ctx context.Context, uniqId string, creator string,
	entity *entity.ServiceAtomMeta, extendType int) (*platformpb.PipelineRun, error) {

	atomRegion := utils.RegionMap(entity.Region)
	//uniqueId := strings.ToLower(uniqId)
	//uniqueId = strings.ReplaceAll(uniqueId, "-", "_")

	uniqueId := uniqId
	uniqueId = strings.ToLower(uniqueId)
	uniqueId = strings.ReplaceAll(uniqueId, "-", "_")

	if extendType == utils.ExtendTypeCreate {
		if strings.HasSuffix(uniqueId, fmt.Sprintf("_%s", utils.REGIONCN)) {
			uniqueId = strings.TrimSuffix(uniqueId, fmt.Sprintf("_%s", utils.REGIONCN))
		} else if strings.HasSuffix(uniqueId, strings.ReplaceAll(fmt.Sprintf("_%s", utils.RegionEUTtp), "-", "_")) {
			uniqueId = strings.TrimSuffix(uniqueId, strings.ReplaceAll(fmt.Sprintf("_%s", utils.RegionEUTtp), "-", "_"))
		} else if strings.HasSuffix(uniqueId, strings.ReplaceAll(fmt.Sprintf("_%s", utils.REGIONUSTtp), "-", "_")) {
			uniqueId = strings.TrimSuffix(uniqueId, strings.ReplaceAll(fmt.Sprintf("_%s", utils.REGIONUSTtp), "-", "_"))
		}
		uniqueId = fmt.Sprintf("%s_%s", uniqueId, strings.ReplaceAll(utils.RegionToString(entity.Region), "-", "_"))
	}

	psm := fmt.Sprintf("toutiao.devops.%s", uniqueId)
	gitRepo := fmt.Sprintf("devops/%s", uniqueId)
	scmRepo := fmt.Sprintf("toutiao/devops/%s", uniqueId)

	createVariables := map[string]interface{}{
		"service_name":  uniqId,
		"atom_type":     entity.ServiceType,
		"atom_language": entity.ServiceLanguage,
		"atom_region":   atomRegion,
		"atom_id":       entity.JobAtomId,
		"atom_psm":      psm,
		"git_repo_name": gitRepo,
		"scm_repo_name": scmRepo,
	}
	if entity.GalaxyId > 0 {
		createVariables["override_settings"] = map[string]interface{}{
			"AtomGalaxyId": entity.GalaxyId,
		}
	}
	options, optionsErr := tcc.GetJobAtomCreateDefaultOptions(ctx)
	if optionsErr != nil {
		return nil, optionsErr
	}
	for _, option := range options {
		if option.Region == atomRegion &&
			option.Language == entity.ServiceLanguage.String() &&
			option.SdkIaasType == entity.ServiceType.String() {
			//	perfect matched type
			for k, v := range option.Variables {
				// append default variables
				createVariables[k] = v
			}
			v := url.Values{}
			v.Add("atom_uid", uniqId)
			v.Add("create_user", creator)
			v.Add("region", fmt.Sprintf("%v", int32(entity.Region)))
			build, buildErr := f.pplSdk.TriggerBuild(ctx, option.CreatePipelineId, createVariables,
				creator, fmt.Sprintf("%s/create_service_callback?%s", f.JobAtomCreateUrl, v.Encode()))
			if buildErr != nil {
				return nil, buildErr
			}
			logs.CtxInfo(ctx, "trigger build for service %s create get build id %d", uniqId, build)
			return build, nil
		}
	}
	return nil, errors.Errorf("applied request region %v & language %v & iaas %v exceed definitions", atomRegion, entity.ServiceLanguage.String(), entity.ServiceType.String())
}
