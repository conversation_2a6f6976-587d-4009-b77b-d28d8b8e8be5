package repo

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devinfra/hagrid/app/atommanagerpc/pkg/db"
	"github.com/stretchr/testify/assert"
)

func TestDeleteStepAtomParameter(t *testing.T) {
	db<PERSON><PERSON><PERSON>, err := db.<PERSON>(db.ConnectionStyle("psm"), db.<PERSON>("bits"), db.PSM("toutiao.mysql.bits"))
	assert.NoError(t, err)

	err = NewAtomParameterRepo().DeleteByEntityIdAndEntityIid(context.Background(), db<PERSON><PERSON><PERSON>, 0, 2537, 1)
	assert.NoError(t, err)
}

func TestGetStepAtomParameter(t *testing.T) {
	dbHand<PERSON>, err := db.New(db.ConnectionStyle("psm"), db.<PERSON><PERSON><PERSON>("bits"), db.PSM("toutiao.mysql.bits"))
	assert.NoError(t, err)

	values, err := NewAtomParameterRepo().GetAtomParameters(context.Background(), db<PERSON><PERSON><PERSON>, 0, 2537, 1, 0)
	assert.NoError(t, err)
	fmt.Println(err)
	fmt.Println(values)
}
