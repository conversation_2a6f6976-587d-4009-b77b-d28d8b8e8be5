package usecase

import (
	"context"
	"math/rand"
	"time"

	"code.byted.org/devinfra/hagrid/app/atommanagerpc/conf"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/entity"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/usecase/interfacecoll"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/usecase/webapi/codebase"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/usecase/webapi/translate"
	"code.byted.org/devinfra/hagrid/app/atommanagerpc/pkg/tcc"
	hclient "code.byted.org/devinfra/hagrid/pkg/net/httpclient"
	"code.byted.org/gopkg/logs"
)

var (
	AtomTypeFlowMap      = map[entity.AtomType]interfacecoll.AtomTypeFlow{}
	CodebaseClientApi    interfacecoll.CodebaseApi
	I18nSetManageHandler interfacecoll.I18nSetHandler
	PipelineApi          interfacecoll.PipelineWebApi
)

// Init ...
// todo 未来支持更多的底层
func Init(cfg *conf.Config) {
	rand.Seed(time.Now().UnixNano())

	PipelineApi = NewPipelineHandler(
		hclient.New(),
	)

	var (
		err           error
		codebaseToken string
	)
	tokens, err := tcc.GetHTTPClientToken(context.Background())
	if err != nil {
		logs.Warnf("failed to get http client token: %+v", err)
	}
	if tokens != nil {
		codebaseToken = tokens.CodebaseToken
	}
	CodebaseClientApi, err = codebase.NewCodebaseHandler(cfg.CodebaseClient.BaseURL, codebaseToken)
	if err != nil {
		logs.Fatalf("failed to get codebase handler: %+v", err)
	}
	I18nSetManageHandler = translate.NewTranslateWebApi()
}
