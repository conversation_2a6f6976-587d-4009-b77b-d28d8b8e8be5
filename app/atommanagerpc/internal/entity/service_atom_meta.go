package entity

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
)

type ServiceAtomMeta struct {
	Cancellable           bool                              `json:"cancellable"`          // 是否可终止
	CheckBeforeExecute    bool                              `json:"check_before_execute"` // 运行前是否检查
	ParamsCheckMethod     string                            `json:"params_check_method"`  // 要检查的函数名
	DestroySwitch         bool                              `json:"destroy_switch"`
	DestroyMethod         string                            `json:"destroy_method"`
	DestroyTrigger        []dslpb.PipelineRunStatus         `json:"destroy_trigger"`
	AtomDestroySwitch     bool                              `json:"atom_destroy_switch"`
	AtomDestroyMethod     string                            `json:"atom_destroy_method"`
	AtomDestroyTrigger    []dslpb.JobRunStatus              `json:"atom_destroy_trigger"`
	Rerunnable            bool                              `json:"rerunnable"`                // 是否可重试
	Rollbackable          bool                              `json:"rollbackable"`              // 是否可回滚
	Skippable             bool                              `json:"skippable"`                 // 是否可跳过
	ServiceLanguage       atommanagepb.ServiceLanguageEvent `json:"service_language"`          // 服务语言
	ServiceType           atommanagepb.ServiceTypeEvent     `json:"service_type"`              // 服务类型
	Id                    int64                             `json:"id"`                        // 主键id
	JobAtomId             int64                             `json:"job_atom_id"`               // 对应的job atom id
	CustomizedForm        string                            `json:"customized_form"`           // 自定义表单
	GitRepo               string                            `json:"git_repo"`                  // 仓库地址
	ScmRepoName           string                            `json:"scm_repo_name"`             // scm仓库地址
	GalaxyId              int64                             `json:"galaxy_id"`                 // galaxy_id
	Domain                string                            `json:"domain"`                    // 调用域名
	Outputs               string                            `json:"outputs"`                   // 输出配置
	Psm                   string                            `json:"psm"`                       // psm
	Reason                string                            `json:"reason"`                    // 申请原因
	PipelineID            uint64                            `json:"pipeline_id"`               // 升级流水线id
	GoofyID               string                            `json:"goofy_id"`                  // 前端原子 goofy id
	FERepo                string                            `json:"fe_repo"`                   // 前端原子 repo name
	FEUpgradePipelineID   uint64                            `json:"fe_upgrade_pipeline_id"`    // 前端原子 升级 goofy 流水线 pipeline id
	FECreatePipelineRunID uint64                            `json:"fe_create_pipeline_run_id"` // 前端原子 创建 goofy 流水线 pipeline run id
	FEDetailDevType       int32                             `json:"fe_detail_dev_type"`        // 前端原子 fe_detail_dev_type
	FeIframeAction        int32                             `json:"fe_iframe_action"`          // 前端原子 fe_iframe_action
	FEMfEntry             string                            `json:"fe_mf_entry"`               // 前端原子 fe_mf_entry
	Disabled              bool                              `json:"disabled"`                  // 是否被禁用
	OverrideJobContext    bool                              `json:"override_job_context"`      // 是否覆盖写job context
	DefaultTimeout        uint32                            `json:"default_timeout"`           // 原子服务调用接口时长
	ChangeActor           bool                              `json:"change_actor"`              // 原子服务是否可以改写操作人
	RollbackAtomUniqueID  string                            `json:"rollback_atom"`             // 处理回滚逻辑绑定的原子服务
	HasCustomizedForm     bool                              `json:"has_customized_form"`
	Callbackable          bool                              `json:"callbackable"`
	CallbackPrincipals    []string                          `json:"callback_principals"`
	IsAtomCustomForm      bool                              `json:"is_atom_custom_form"` // 是否原子自定义入参
	ArtifactOutputs       ArtifactOutputs                   `json:"artifact_outpus"`
	CreatePipelineRunID   uint64                            `json:"create_pipeline_run_id"` // 创建流水线 pipeline run id
	Region                atommanagepb.RegionEnum           `json:"region"`                 // 部署机房
	IsI18n                bool                              `json:"is_i18n"`                // 是否部署在 i18n（仅对 region 为 1 的数据有效，region 为 1 的数据包括 CN 控制面和 i18n 控制面）
	Status                atommanagepb.AtomRegionInitStatus `json:"status"`                 // status
	SubRegion             atommanagepb.SubRegion            `json:"sub_region"`             //应对i18n控制面拆分，目前初步确定i18n拆分为i18n-tt和i18n-bd，仅档region为1时有效，后面完成控制面拆分后，考虑下掉 is_i18n字段
}

type ArtifactType string

const (
	ArtifactType_Unknown ArtifactType = "unknown"
	ArtifactType_Scm     ArtifactType = "scm"
)

type ArtifactOutputs []ArtifactOutput

type ArtifactOutput struct {
	ArtifactType ArtifactType `json:"artifact_type"`
	Description  string       `json:"description"`
}

func (t ArtifactType) IDL() atommanagepb.ArtifactType {
	switch t {
	case ArtifactType_Scm:
		return atommanagepb.ArtifactType_ARTIFACT_TYPE_SCM
	default:
		return atommanagepb.ArtifactType_ARTIFACT_TYPE_UNSPECIFIED
	}
}

func ArtifactTypeIDLToEntity(t atommanagepb.ArtifactType) ArtifactType {
	switch t {
	case atommanagepb.ArtifactType_ARTIFACT_TYPE_SCM:
		return ArtifactType_Scm
	default:
		return ArtifactType_Unknown
	}
}

func MetaStatusToServiceStatus(s atommanagepb.StatusEvent) atommanagepb.AtomRegionInitStatus {
	switch s {
	case atommanagepb.StatusEvent_STATUS_EVENT_DOING:
		return atommanagepb.AtomRegionInitStatus_ATOM_REGION_INIT_STATUS_INPROGRESS
	case atommanagepb.StatusEvent_STATUS_EVENT_FAILED:
		return atommanagepb.AtomRegionInitStatus_ATOM_REGION_INIT_STATUS_FAIL
	case atommanagepb.StatusEvent_STATUS_EVENT_SUCCESS:
		return atommanagepb.AtomRegionInitStatus_ATOM_REGION_INIT_STATUS_SUCCESS
	default:
		return atommanagepb.AtomRegionInitStatus_ATOM_REGION_INIT_STATUS_UNSPECIFIED
	}
}
