package handler

import "code.byted.org/devinfra/hagrid/app/atommanagerpc/internal/usecase/interfacecoll"

type AtomESbuildUseCase struct {
	metaRepo         interfacecoll.AtomMetaRepo
	serviceMetaRepo  interfacecoll.ServiceAtomMetaRepo
	StepAtomMetaRepo interfacecoll.StepAtomMetaRepo
	versionRepo      interfacecoll.AtomVersionRepo
	authorRepo       interfacecoll.AuthorRepo
	appRelationRepo  interfacecoll.AtomAppRelationRepo
	atomInfoRepo     interfacecoll.AtomInfoRepo
	stepAtomInfoRepo interfacecoll.StepAtomInfoRepo
}

func NewAtomSearchUseCase(metaRepo interfacecoll.AtomMetaRepo, serviceMetaRepo interfacecoll.ServiceAtomMetaRepo, StepAtomMetaRepo interfacecoll.StepAtomMetaRepo, versionRepo interfacecoll.AtomVersionRepo, authorRepo interfacecoll.AuthorRepo, appRelationRepo interfacecoll.AtomAppRelationRepo, atomInfoRepo interfacecoll.AtomInfoRepo, stepAtomInfoRepo interfacecoll.StepAtomInfoRepo) *AtomESbuildUseCase {
	return &AtomESbuildUseCase{
		metaRepo:         metaRepo,
		serviceMetaRepo:  serviceMetaRepo,
		StepAtomMetaRepo: StepAtomMetaRepo,
		versionRepo:      versionRepo,
		authorRepo:       authorRepo,
		appRelationRepo:  appRelationRepo,
		atomInfoRepo:     atomInfoRepo,
		stepAtomInfoRepo: stepAtomInfoRepo,
	}
}
