package main

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/handler"
	"code.byted.org/devinfra/hagrid/internal/pipeline/utils/ctxutil"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	platformpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pkg/utils"
	"code.byted.org/devinfra/pbgen/byted/devinfra/pipeline/templatepb/templateservice"
	"code.byted.org/gopkg/logs"
)

// TemplateServiceImpl implements the last service interface defined in the IDL.
type TemplateServiceImpl struct {
	//templateservice.UnimplementedTemplateServiceServer
	templateHandler *handler.TemplateHandler
	// templateVarsHandler *handler.TemplateVarHandler
	templateservice.UnimplementedTemplateServiceServer
}

func NewTemplateServiceImpl() *TemplateServiceImpl {
	return &TemplateServiceImpl{
		templateHandler: handler.GetTemplateHandler(),
		// templateVarsHandler: handler.NewTemplateVarHandler(),
	}
}

// GetTemplateDetail implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateDetail(ctx context.Context, req *platformpb.GetTemplateDetailRequest) (resp *platformpb.GetTemplateDetailResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplateDetail req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplateDetail(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplateDetail.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateDetail resp: %s", utils.JSONToString(ctx, resp))
	return
}

// CreateTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) CreateTemplate(ctx context.Context, req *platformpb.CreateTemplateRequest) (resp *platformpb.CreateTemplateResponse, err error) {
	logs.CtxInfo(ctx, "CreateTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreateTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreateTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreateTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// DuplicateTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) DuplicateTemplate(ctx context.Context, req *platformpb.DuplicateTemplateRequest) (resp *platformpb.DuplicateTemplateResponse, err error) {
	logs.CtxInfo(ctx, "DuplicateTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.DuplicateTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateDuplicateTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "DuplicateTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// DeleteTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) DeleteTemplate(ctx context.Context, req *platformpb.DeleteTemplateRequest) (resp *platformpb.DeleteTemplateResponse, err error) {
	logs.CtxInfo(ctx, "DeleteTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.DeleteTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateDeleteTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "DeleteTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UpdateTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) UpdateTemplate(ctx context.Context, req *platformpb.UpdateTemplateRequest) (resp *platformpb.UpdateTemplateResponse, err error) {
	logs.CtxInfo(ctx, "UpdateTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UpdateTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// ListTemplates implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) ListTemplates(ctx context.Context, req *platformpb.ListTemplatesRequest) (resp *platformpb.ListTemplatesResponse, err error) {
	logs.CtxInfo(ctx, "ListTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.ListTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateListTemplates.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "ListTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// EditFavoriteTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) EditFavoriteTemplate(ctx context.Context, req *platformpb.EditFavoriteTemplateRequest) (resp *platformpb.EditFavoriteTemplateResponse, err error) {
	// TODO: check 这里到底是 create 还是 edit 逻辑
	logs.CtxInfo(ctx, "CreateTemplateFavor req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreateTemplateFavor(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateEditFavoriteTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreateTemplateFavor resp: %s", utils.JSONToString(ctx, resp))
	return
}

// CreatePipelinesFromTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) CreatePipelinesFromTemplate(ctx context.Context, req *platformpb.CreatePipelinesFromTemplateRequest) (resp *platformpb.CreatePipelinesFromTemplateResponse, err error) {
	logs.CtxInfo(ctx, "CreatePipelinesFromTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreatePipelinesFromTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreatePipelinesFromTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreatePipelinesFromTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// PatchPipelinesFromTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) PatchPipelinesFromTemplate(ctx context.Context, req *platformpb.PatchPipelinesFromTemplateRequest) (resp *platformpb.PatchPipelinesFromTemplateResponse, err error) {
	logs.CtxInfo(ctx, "PatchPipelinesFromTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.PatchPipelinesFromTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplatePatchPipelinesFromTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "PatchPipelinesFromTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplatePipelineTasks implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplatePipelineTasks(ctx context.Context, req *platformpb.GetTemplatePipelineTasksRequest) (resp *platformpb.GetTemplatePipelineTasksResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplatePipelineTasks req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplatePipelineTasks(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplatePipelineTasks.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplatePipelineTasks resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplatePipelineTask implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplatePipelineTask(ctx context.Context, req *platformpb.GetTemplatePipelineTaskRequest) (resp *platformpb.GetTemplatePipelineTaskResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplatePipelineTask req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplatePipelineTask(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplatePipelineTask.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplatePipelineTask resp: %s", utils.JSONToString(ctx, resp))
	return
}

func (s *TemplateServiceImpl) GetTemplatePipelineTaskLite(ctx context.Context, req *platformpb.GetTemplatePipelineTaskRequest) (resp *platformpb.GetTemplatePipelineTaskResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplatePipelineTaskLite req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplatePipelineTaskLite(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplatePipelineTask.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplatePipelineTaskLite resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplateVars implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateVars(ctx context.Context, req *platformpb.GetTemplateVarsRequest) (resp *platformpb.GetTemplateVarsResponse, err error) {
	return
}

// GetTemplateSysVars implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateSysVars(ctx context.Context, req *platformpb.GetTemplateSysVarsRequest) (resp *platformpb.GetTemplateSysVarsResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplateSysVarGroup req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplateSysVarGroup(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplateSysVars.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateSysVarGroup resp: %s", utils.JSONToString(ctx, resp))
	return
}

// PreviewPipelinesFromTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) PreviewPipelinesFromTemplate(ctx context.Context, req *platformpb.PreviewPipelineFromTemplateRequest) (resp *platformpb.PreviewPipelineFromTemplateResponse, err error) {
	logs.CtxInfo(ctx, "PreviewPipelinesFromTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.PreviewPipelinesFromTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplatePreviewPipelinesFromTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "PreviewPipelinesFromTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplateBoundPipelines implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateBoundPipelines(ctx context.Context, req *platformpb.GetTemplateBoundPipelinesRequest) (resp *platformpb.GetTemplateBoundPipelinesResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplateBoundPipelines req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplateBoundPipelines(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplateBoundPipelines.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateBoundPipelines resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplateBoundPipelineByPipelineId implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateBoundPipelineByPipelineId(ctx context.Context, req *platformpb.GetTemplateBoundPipelineByPipelineIdRequest) (resp *platformpb.GetTemplateBoundPipelinesResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplateBoundPipelineByPipelineId req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplateBoundPipelineByPipelineId(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplateBoundPipelines.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateBoundPipelineByPipelineId resp: %s", utils.JSONToString(ctx, resp))
	return
}

// PreviewTemplateDiff implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) PreviewTemplateDiff(ctx context.Context, req *platformpb.PreviewTemplateDiffRequest) (resp *platformpb.PreviewTemplateDiffResponse, err error) {
	logs.CtxInfo(ctx, "PreviewTemplateDiff req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.PreviewTemplateDiff(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplatePreviewTemplateDiff.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "PreviewTemplateDiff resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UnbindPipelines implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) UnbindPipelines(ctx context.Context, req *platformpb.UnbindPipelinesRequest) (resp *platformpb.UnbindPipelinesResponse, err error) {
	logs.CtxInfo(ctx, "UnbindPipelines req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UnbindPipelines(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUnbindPipelines.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UnbindPipelines resp: %s", utils.JSONToString(ctx, resp))
	return
}

// ListArchivedTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) ListArchivedTemplate(ctx context.Context, req *platformpb.ListArchivedTemplateRequest) (resp *platformpb.ListArchivedTemplateResponse, err error) {
	logs.CtxInfo(ctx, "ListArchivedTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.ListArchivedTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateListArchivedTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "ListArchivedTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// RecoverArchivedTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) RecoverArchivedTemplate(ctx context.Context, req *platformpb.RecoverArchivedTemplateRequest) (resp *platformpb.RecoverArchivedTemplateResponse, err error) {
	logs.CtxInfo(ctx, "RecoverArchivedTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.RecoverArchivedTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateRecoverArchivedTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "RecoverArchivedTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// MigrateTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) MigrateTemplate(ctx context.Context, req *platformpb.MigrateTemplateRequest) (resp *platformpb.MigrateTemplateResponse, err error) {
	logs.CtxInfo(ctx, "MigrateTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.MigrateTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateMigrateTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "MigrateTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// CreateMarketTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) CreateMarketTemplate(ctx context.Context, req *platformpb.CreateMarketTemplateRequest) (resp *platformpb.CreateMarketTemplateResponse, err error) {
	logs.CtxInfo(ctx, "CreateMarketTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreateMarketTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreateMarketTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreateMarketTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetMarketTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetMarketTemplate(ctx context.Context, req *platformpb.GetMarketTemplateRequest) (resp *platformpb.GetMarketTemplateResponse, err error) {
	logs.CtxInfo(ctx, "GetMarketTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetMarketTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetMarketTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetMarketTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// ListMarketTemplates implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) ListMarketTemplates(ctx context.Context, req *platformpb.ListMarketTemplatesRequest) (resp *platformpb.ListMarketTemplatesResponse, err error) {
	logs.CtxInfo(ctx, "ListMarketTemplates req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.ListMarketTemplates(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateListMarketTemplates.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "ListMarketTemplates resp: %s", utils.JSONToString(ctx, resp))
	return
}

// ListManageTemplates implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) ListManageTemplates(ctx context.Context, req *platformpb.ListManageTemplatesRequest) (resp *platformpb.ListManageTemplatesResponse, err error) {
	logs.CtxInfo(ctx, "ListManageTemplates req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.ListManageTemplates(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateListManageTemplates.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "ListManageTemplates resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetManageTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetManageTemplate(ctx context.Context, req *platformpb.GetManageTemplateRequest) (resp *platformpb.GetManageTemplateResponse, err error) {
	logs.CtxInfo(ctx, "GetManageTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetManageTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetManageTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetManageTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetMarketTemplateGroups implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetMarketTemplateGroups(ctx context.Context, req *platformpb.GetMarketTemplateGroupsRequest) (resp *platformpb.GetMarketTemplateGroupsResponse, err error) {
	logs.CtxInfo(ctx, "GetMarketTemplateGroups req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetMarketTemplateGroups(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetMarketTemplateGroups.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetMarketTemplateGroups resp: %s", utils.JSONToString(ctx, resp))
	return

}

// CreateManageTemplateSpaces implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) CreateManageTemplateSpaces(ctx context.Context, req *platformpb.CreateManageTemplateSpacesRequest) (resp *platformpb.CreateManageTemplateSpacesResponse, err error) {
	logs.CtxInfo(ctx, "CreateManageTemplateSpaces req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreateManageTemplateSpaces(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreateManageTemplateSpaces.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreateManageTemplateSpaces resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UpdateManageTemplateSpaces implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) UpdateManageTemplateSpaces(ctx context.Context, req *platformpb.UpdateManageTemplateSpacesRequest) (resp *platformpb.UpdateManageTemplateSpacesResponse, err error) {
	logs.CtxInfo(ctx, "UpdateManageTemplateSpaces req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UpdateManageTemplateSpaces(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateManageTemplateSpaces.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateManageTemplateSpaces resp: %s", utils.JSONToString(ctx, resp))
	return
}

// PublishMarketTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) PublishMarketTemplate(ctx context.Context, req *platformpb.PublishMarketTemplateRequest) (resp *platformpb.PublishMarketTemplateResponse, err error) {
	logs.CtxInfo(ctx, "PublishMarketTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = handler.NewTemplatePublishHandler().PublishMarketTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplatePublishMarketTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "PublishMarketTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// CheckTemplateAtoms implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) CheckTemplateAtoms(ctx context.Context, req *platformpb.CheckTemplateAtomsRequest) (resp *platformpb.CheckTemplateAtomsResponse, err error) {
	logs.CtxInfo(ctx, "CheckTemplateAtoms req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CheckTemplateAtoms(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCheckTemplateAtoms.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CheckTemplateAtoms resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplatePublishApprovalProcess implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplatePublishApprovalProcess(ctx context.Context, req *platformpb.GetTemplatePublishApprovalProcessRequest) (resp *platformpb.GetTemplatePublishApprovalProcessResponse, err error) {
	logs.CtxInfo(ctx, "GetPublishTemplateApprovalProcess req: %s", utils.JSONToString(ctx, req))
	resp, err = handler.NewTemplatePublishHandler().GetPublishTemplateApprovalProcess(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplatePublishApprovalProcess.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetPublishTemplateApprovalProcess resp: %s", utils.JSONToString(ctx, resp))
	return
}

// OperatePublishApprovalProcess implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) OperatePublishApprovalProcess(ctx context.Context, req *platformpb.OperatePublishApprovalProcessRequest) (resp *platformpb.OperatePublishApprovalProcessResponse, err error) {
	logs.CtxInfo(ctx, "OperateMarketTemplateApprovalProcess req: %s", utils.JSONToString(ctx, req))
	resp, err = handler.NewTemplatePublishHandler().OperateMarketTemplateApprovalProcess(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateOperatePublishApprovalProcess.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "OperateMarketTemplateApprovalProcess resp: %s", utils.JSONToString(ctx, resp))
	return
}

// ListMarketTemplatePipelines implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) ListMarketTemplatePipelines(ctx context.Context, req *platformpb.ListMarketTemplatePipelinesRequest) (resp *platformpb.ListMarketTemplatePipelinesResponse, err error) {
	// TODO: Your code here...
	return
}

// ListTemplateVersion implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) ListTemplateVersion(ctx context.Context, req *platformpb.ListTemplateVersionRequest) (resp *platformpb.ListTemplateVersionResponse, err error) {
	logs.CtxInfo(ctx, "ListTemplateVersion req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.ListTemplateVersion(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateListTemplateVersion.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "ListTemplateVersion resp: %s", utils.JSONToString(ctx, resp))
	return
}

// RollbackTemplateVersion implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) RollbackTemplateVersion(ctx context.Context, req *platformpb.RollbackTemplateVersionRequest) (resp *platformpb.RollbackTemplateVersionResponse, err error) {
	logs.CtxInfo(ctx, "RollbackTemplateVersion req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.RollbackTemplateVersion(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateRollbackTemplateVersion.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "RollbackTemplateVersion resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UpdateOreoTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) UpdateOreoTemplate(ctx context.Context, req *platformpb.UpdateOreoTemplateRequest) (resp *platformpb.UpdateOreoTemplateResponse, err error) {
	logs.CtxInfo(ctx, "UpdateOreoTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UpdateOreoTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateOreoTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateOreoTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// SyncCreatePipelinesFromTemplate implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) SyncCreatePipelinesFromTemplate(ctx context.Context, req *platformpb.SyncCreatePipelinesFromTemplateRequest) (resp *platformpb.SyncCreatePipelinesFromTemplateResponse, err error) {
	logs.CtxInfo(ctx, "SyncCreatePipelinesFromTemplate req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.SyncCreatePipelinesFromTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateSyncCreatePipelinesFromTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "SyncCreatePipelinesFromTemplate resp: %s", utils.JSONToString(ctx, resp))
	return
}

// CreatePipelinesFromTemplateSync implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) CreatePipelinesFromTemplateSync(ctx context.Context, req *platformpb.CreatePipelinesFromTemplateRequest) (resp *platformpb.SyncCreatePipelinesFromTemplateResponse, err error) {
	logs.CtxInfo(ctx, "CreatePipelinesFromTemplateSync req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreatePipelinesFromTemplateSync(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreatePipelinesFromTemplateSync.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreatePipelinesFromTemplateSync resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UpdateTemplateSceneType implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) UpdateTemplateSceneType(ctx context.Context, req *platformpb.UpdateTemplateSceneTypeRequest) (resp *platformpb.UpdateTemplateSceneTypeResponse, err error) {
	logs.CtxInfo(ctx, "UpdateTemplateSceneType req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UpdateTemplateSceneType(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplateSceneType.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateTemplateSceneType resp: %s", utils.JSONToString(ctx, resp))
	return
}

// CreateTemplateDraft implements the CreateTemplateDraft interface.
func (s *TemplateServiceImpl) CreateTemplateDraft(ctx context.Context, req *platformpb.CreateTemplateDraftRequest) (resp *platformpb.CreateTemplateDraftResponse, err error) {
	logs.CtxInfo(ctx, "CreateTemplateDraft req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.CreateTemplateDraft(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreateTemplateDraft.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "CreateTemplateDraft resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplateDraft implements the GetTemplateDraft interface.
func (s *TemplateServiceImpl) GetTemplateDraft(ctx context.Context, req *platformpb.GetTemplateDraftRequest) (resp *platformpb.GetTemplateDraftResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplateDraft req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplateDraft(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetTemplateDraft.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateDraft resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UpdateTemplateDraft implements the UpdateTemplateDraft interface.
func (s *TemplateServiceImpl) UpdateTemplateDraft(ctx context.Context, req *platformpb.UpdateTemplateDraftRequest) (resp *platformpb.UpdateTemplateDraftResponse, err error) {
	logs.CtxInfo(ctx, "UpdateTemplateDraft req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UpdateTemplateDraft(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplateDraft.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateTemplateDraft resp: %s", utils.JSONToString(ctx, resp))
	return
}

// UpdateTemplateDraftAndPatchPipelines implements the UpdateTemplateDraftAndPatchPipelines interface.
func (s *TemplateServiceImpl) UpdateTemplateDraftAndPatchPipelines(ctx context.Context, req *platformpb.UpdateTemplateDraftAndPatchPipelinesRequest) (resp *platformpb.UpdateTemplateDraftAndPatchPipelinesResponse, err error) {
	logs.CtxInfo(ctx, "UpdateTemplateDraftAndPatchPipelines req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.UpdateTemplateDraftAndPatchPipelines(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplateDraftAndPatchPipelines.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateTemplateDraftAndPatchPipelines resp: %s", utils.JSONToString(ctx, resp))
	return
}

// DeleteTemplateDraft implements the DeleteTemplateDraft interface.
func (s *TemplateServiceImpl) DeleteTemplateDraft(ctx context.Context, req *platformpb.DeleteTemplateDraftRequest) (resp *platformpb.DeleteTemplateDraftResponse, err error) {
	logs.CtxInfo(ctx, "DeleteTemplateDraft req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.DeleteTemplateDraft(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateDeleteTemplateDraft.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "DeleteTemplateDraft resp: %s", utils.JSONToString(ctx, resp))
	return
}

// DeployTemplateDraft implements the DeployTemplateDraft interface.
func (s *TemplateServiceImpl) DeployTemplateDraft(ctx context.Context, req *platformpb.DeployTemplateDraftRequest) (resp *platformpb.DeployTemplateDraftResponse, err error) {
	logs.CtxInfo(ctx, "DeployTemplateDraft req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.DeployTemplateDraft(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateDeployTemplateDraft.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "DeployTemplateDraft resp: %s", utils.JSONToString(ctx, resp))
	return
}

// DeployTemplateDraftAndPatchPipelines implements the DeployTemplateDraftAndPatchPipelines interface.
func (s *TemplateServiceImpl) DeployTemplateDraftAndPatchPipelines(ctx context.Context, req *platformpb.DeployTemplateDraftAndPatchPipelinesRequest) (resp *platformpb.DeployTemplateDraftAndPatchPipelinesResponse, err error) {
	logs.CtxInfo(ctx, "DeployTemplateDraftAndPatchPipelines req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.DeployTemplateDraftAndPatchPipelines(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateDeployTemplateDraftAndPatchPipelines.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "DeployTemplateDraftAndPatchPipelines resp: %s", utils.JSONToString(ctx, resp))
	return
}

// BatchMigrateBitsTemplateRequest implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) BatchMigrateBitsTemplateRequest(ctx context.Context, req *platformpb.BatchMigrateBitsTemplateRequest) (resp *platformpb.BatchMigrateBitsTemplateResponse, err error) {
	logs.CtxInfo(ctx, "BatchMigrateBitsTemplateRequest req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.MigrateBitsTemplate(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateBatchMigrateBitsTemplate.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "BatchMigrateBitsTemplateRequest resp: %s", utils.JSONToString(ctx, resp))
	return
}

// GetTemplateVersionDetail implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateVersionDetail(ctx context.Context, req *platformpb.GetTemplateVersionDetailRequest) (resp *platformpb.GetTemplateVersionDetailResponse, err error) {
	logs.CtxInfo(ctx, "GetTemplateVersionDetail req: %s", utils.JSONToString(ctx, req))
	resp, err = s.templateHandler.GetTemplateVersionDetail(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateGetVersionDetail.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateVersionDetail req: %v", utils.JSONToString(ctx, req))
	return
}

func (s *TemplateServiceImpl) CreateTemplateFile(ctx context.Context, req *platformpb.CreateTemplateFileReq) (resp *platformpb.CreateTemplateFileResp, err error) {
	logs.CtxInfo(ctx, "SendCreateTemplateFileEvent req: %s", utils.JSONToString(ctx, req))
	if req.GetUsername() == "" {
		req.Username = ctxutil.GetUsername(ctx)
	}
	resp, err = s.templateHandler.SendCreateTemplateFileEvent(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateCreateTemplateFile.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "SendCreateTemplateFileEvent resp: %s", utils.JSONToString(ctx, resp))
	return
}

func (s *TemplateServiceImpl) UpdateTemplatePipelineWithFile(ctx context.Context, req *platformpb.UpdateTemplatePipelineWithFileReq) (resp *platformpb.UpdateTemplatePipelineWithFileResp, err error) {
	logs.CtxInfo(ctx, "UpdateTemplatePipelineWithFile req: %s", utils.JSONToString(ctx, req))
	if req.GetUsername() == "" {
		req.Username = ctxutil.GetUsername(ctx)
	}
	resp, err = s.templateHandler.SendTemplateFileUpdatePipelinesEvent(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplatePipelineWithFile.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpdateTemplatePipelineWithFile resp: %s", utils.JSONToString(ctx, resp))
	return
}

// BindPipeline implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) BindPipeline(ctx context.Context, req *platformpb.BindPipelineReq) (resp *platformpb.BindPipelineResp, err error) {
	logs.CtxInfo(ctx, "BindPipeline req: %s", utils.JSONToString(ctx, req))

	resp, err = s.templateHandler.BindPipeline(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplatePipelineWithFile.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "BindPipeline resp: %s", utils.JSONToString(ctx, resp))

	return
}

// GetTemplateByPipelineID implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) GetTemplateByPipelineID(ctx context.Context, req *platformpb.GetTemplateByPipelineIDReq) (resp *platformpb.GetTemplateByPipelineIDResp, err error) {
	logs.CtxInfo(ctx, "GetTemplateByPipelineID req: %s", utils.JSONToString(ctx, req))

	resp, err = s.templateHandler.GetTemplateByPipelineID(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplatePipelineWithFile.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "GetTemplateByPipelineID resp: %s", utils.JSONToString(ctx, resp))

	return
}

// RetryTemplatePipelineTask implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) RetryTemplatePipelineTask(ctx context.Context, req *platformpb.RetryTemplatePipelineTaskRequest) (resp *platformpb.RetryTemplatePipelineTaskResponse, err error) {
	logs.CtxInfo(ctx, "RetryTemplatePipelineTask req: %s", utils.JSONToString(ctx, req))

	resp, err = s.templateHandler.RetryTemplatePipelineTask(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrTemplateUpdateTemplatePipelineWithFile.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "RetryTemplatePipelineTask resp: %s", utils.JSONToString(ctx, resp))

	return
}

// UpgradeTemplateToGlobal implements the TemplateServiceImpl interface.
func (s *TemplateServiceImpl) UpgradeTemplateToGlobal(ctx context.Context, req *platformpb.UpgradeTemplateToGlobalReq) (resp *platformpb.UpgradeTemplateToGlobalResp, err error) {
	logs.CtxInfo(ctx, "UpgradeTemplateToGlobal req: %s", utils.JSONToString(ctx, req))
	if req.GetUsername() == "" {
		req.Username = ctxutil.GetUsername(ctx)
	}
	resp, err = s.templateHandler.UpgradeTemplateToGlobal(ctx, req)
	if err != nil {
		return nil, bits_err.TEMPLATE.ErrUpgradeTemplateToGlobal.AddOrPass(ctx, err)
	}
	logs.CtxInfo(ctx, "UpgradeTemplateToGlobal resp: %s", utils.JSONToString(ctx, resp))

	return
}
