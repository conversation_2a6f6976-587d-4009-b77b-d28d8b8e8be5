package main

import (
	"os"

	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/tos"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/atom"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/authz"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/pipeline"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/pmarshaller"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/trigger"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/varstore"
	"code.byted.org/kite/kitex/byted/transmeta"

	"code.byted.org/canal/provider/bytefaas"
	"code.byted.org/canal/provider/bytetree"
	"code.byted.org/canal/provider/cronjob"
	"code.byted.org/canal/provider/kms_v2"
	redisBC "code.byted.org/canal/provider/redis"
	"code.byted.org/canal/provider/scm"
	"code.byted.org/canal/provider/tcc_config"
	"code.byted.org/canal/provider/tce"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/pkms"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/rmq"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/event/consumer"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/event/producer"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/idgen"
	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/lark"
	"code.byted.org/devinfra/hagrid/app/templaterpc/config"
)

func Init() {
	transmeta.SetReadBizStatusErr(true)
	cfg := config.MustInitializeConfig()
	InitDependency(cfg)

	// consumer need dependency
	producer.Init(cfg)
	consumer.Init(cfg)
}

func InitDependency(config *config.Config) {
	mysql.MustInitialize(config.Mysql)
	idgen.MustInitialize(config.IdGen)
	redis.MustInitialize(config.Redis)
	// es.MustInitialize(config.ES)
	tcc.MustInitialize(config.Tcc)
	if os.Getenv("LOCAL_MOD") != "true" {
		pkms.MustInit()
		initBCProvider()
		lark.MustInit()
	}
	trigger.GetClient()
	rmq.MustInitializeProducer(config.Rmq)
	authz.GetRpcClient()
	pipeline.GetClient()
	varstore.GetClient()
	// 初始化 tos 配置
	tos.MustInitialize(config.Tos)
	atomCli := atom.GetRpcClient()
	pmarshaller.NewPipelineMarshaller(atomCli)
}

func initBCProvider() {
	kms_v2.Init()
	tcc_config.InitClient()
	redisBC.InitClient()
	bytetree.InitSDK()
	tce.InitSDK()
	cronjob.InitClient()
	bytefaas.InitClient()
	scm.InitService()
}
