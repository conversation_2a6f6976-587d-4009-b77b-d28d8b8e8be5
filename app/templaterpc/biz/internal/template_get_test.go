package internal

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/templaterpc/biz/pkg/space"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb/spacerpcapi_mock"
)

func Test_getSpaceName(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	mockSpaceClient := spacerpcapi_mock.NewMockClient(ctrl)
	space.InitMockClient(mockSpaceClient)
	t.Run("success", func(t *testing.T) {
		defer mockey.Mock(mockey.GetMethod(mockSpaceClient, "GetSpaceInfoByID")).Return(&rpcpb.SpaceDetail{
			Id:   123,
			Name: "mock space",
		}, nil).Build().UnPatch()
		mockCtx := &TemplateGetCtx{
			ctx: ctx,
			RespTemplate: &platformpb.Template{
				TemplateId: 111,
				SpaceId:    123,
				SpaceName:  "",
			},
			Locker: &sync.Mutex{},
		}
		err := mockCtx.getSpaceName(mockCtx)
		assert.NoError(t, err)
		assert.Equal(t, "mock space", mockCtx.RespTemplate.SpaceName)

		zeroSpaceIDCtx := &TemplateGetCtx{
			ctx: ctx,
			RespTemplate: &platformpb.Template{
				TemplateId: 111,
				SpaceId:    0,
				SpaceName:  "",
			},
			Locker: &sync.Mutex{},
		}
		err = zeroSpaceIDCtx.getSpaceName(zeroSpaceIDCtx)
		assert.NoError(t, err)
		assert.Equal(t, "", zeroSpaceIDCtx.RespTemplate.SpaceName)
	})

	t.Run("failed", func(t *testing.T) {
		defer mockey.Mock(mockey.GetMethod(mockSpaceClient, "GetSpaceInfoByID")).Return(nil, fmt.Errorf("mock err")).Build().UnPatch()
		mockCtx := &TemplateGetCtx{
			ctx: ctx,
			RespTemplate: &platformpb.Template{
				TemplateId: 111,
				SpaceId:    123,
				SpaceName:  "",
			},
			Locker: &sync.Mutex{},
		}
		err := mockCtx.getSpaceName(mockCtx)
		assert.ErrorContains(t, err, "mock err")
	})
}
