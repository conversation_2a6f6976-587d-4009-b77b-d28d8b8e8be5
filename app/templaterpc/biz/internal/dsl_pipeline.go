package internal

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"github.com/mohae/deepcopy"
)

// 通过模版的dslpb.Pipeline构造用于创建流水线的dslpb.Pipeline
func BuildPipelineDsl(tmplDsl *dslpb.Pipeline, isMarketTemplate bool) *dslpb.Pipeline {
	if tmplDsl == nil {
		return nil
	}
	newDsl := deepcopy.Copy(tmplDsl).(*dslpb.Pipeline)
	buildNewTriggers(newDsl.Triggers)
	buildNewTriggerGroup(newDsl.TriggerGroup)
	buildNewNotification(newDsl.Notifications)
	buildNewNotificationGroup(newDsl.NotificationGroup)
	buildNewVarGroup(newDsl.VarGroup)

	if isMarketTemplate {
		newDsl.Authorizations = nil
	}
	return newDsl
}

func buildNewNotificationGroup(notifyGroup *dslpb.NotificationGroup) {
	if notifyGroup == nil {
		return
	}
	buildNewNotification(notifyGroup.Notifications)
}

func buildNewNotification(notify []*dslpb.Notification) {
	for _, data := range notify {
		data.Id = 0

	}
}

func buildNewTriggerGroup(group *dslpb.TriggerGroup) {
	if group == nil {
		return
	}
	buildNewTriggers(group.Triggers)
}

func buildNewTriggers(oldTriggers []*dslpb.Trigger) {
	for i, trigger := range oldTriggers {
		if trigger == nil {
			continue
		}
		switch trigger.GetTrigger().(type) {
		case *dslpb.Trigger_GitPush:
			pushTrigger := oldTriggers[i].GetGitPush()
			pushTrigger.Id = 0
			trigger.Trigger = &dslpb.Trigger_GitPush{
				GitPush: pushTrigger,
			}
		case *dslpb.Trigger_Mr:
			mrTrigger := oldTriggers[i].GetMr()
			mrTrigger.Id = 0
			trigger.Trigger = &dslpb.Trigger_Mr{
				Mr: mrTrigger,
			}
		case *dslpb.Trigger_Cron:
			cronTrigger := oldTriggers[i].GetCron()
			cronTrigger.Id = 0
			trigger.Trigger = &dslpb.Trigger_Cron{
				Cron: cronTrigger,
			}
		default:
			continue
		}
		buildNewVarGroup(trigger.GetVarGroup())
	}
}

func buildNewVarGroup(group *varstorepb.VarGroup) {
	if group == nil {
		return
	}
	group.GroupId = 0
	group.Version = 0
}
