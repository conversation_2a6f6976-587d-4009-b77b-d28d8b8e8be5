package util

import (
	"testing"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/sharedpb"
	"github.com/stretchr/testify/assert"
)

func TestPaginator_Result(t *testing.T) {
	type testCase[T any] struct {
		name  string
		p     *Paginator[T]
		want  []T
		want1 *sharedpb.Pagination
	}
	tests := []testCase[string]{
		{
			name:  "empty",
			p:     NewPaginator([]string{}, 0, 0),
			want:  []string{},
			want1: &sharedpb.Pagination{},
		},
		{
			name:  "more than total",
			p:     NewPaginator([]string{"0", "1", "2"}, 2, 4),
			want:  []string{},
			want1: &sharedpb.Pagination{Total: 3, PageNum: 2, PageSize: 4},
		},
		{
			name:  "normal1",
			p:     NewPaginator([]string{"0", "1", "2"}, 1, 1),
			want:  []string{"0"},
			want1: &sharedpb.Pagination{Total: 3, PageNum: 1, PageSize: 1},
		},
		{
			name:  "normal2",
			p:     NewPaginator([]string{"0", "1", "2"}, 2, 2),
			want:  []string{"2"},
			want1: &sharedpb.Pagination{Total: 3, PageNum: 2, PageSize: 2},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := tt.p.Result()
			assert.Equalf(t, tt.want, got, "Result()")
			assert.Equalf(t, tt.want1, got1, "Result()")
		})
	}
}
