load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["service_storage_version_manager_mock.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/version_manager/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//app/storagerpc/pkg/dal/mysql/entity",
        "//app/storagerpc/pkg/service/version_manager",
        "//idls/byted/devinfra/storage/shared:shared_go_proto",
        "//idls/byted/devinfra/storage/storage_version:storage_version_go_proto",
        "@com_github_golang_mock//gomock",
    ],
)
