// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/tcc_config (interfaces: TccConfigSvc)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	change_itempb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/change_itempb"
	platform_configpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	gomock "github.com/golang/mock/gomock"
)

// MockTccConfigSvc is a mock of TccConfigSvc interface.
type MockTccConfigSvc struct {
	ctrl     *gomock.Controller
	recorder *MockTccConfigSvcMockRecorder
}

// MockTccConfigSvcMockRecorder is the mock recorder for MockTccConfigSvc.
type MockTccConfigSvcMockRecorder struct {
	mock *MockTccConfigSvc
}

// NewMockTccConfigSvc creates a new mock instance.
func NewMockTccConfigSvc(ctrl *gomock.Controller) *MockTccConfigSvc {
	mock := &MockTccConfigSvc{ctrl: ctrl}
	mock.recorder = &MockTccConfigSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTccConfigSvc) EXPECT() *MockTccConfigSvcMockRecorder {
	return m.recorder
}

// CreateTccConfig mocks base method.
func (m *MockTccConfigSvc) CreateTccConfig(arg0 context.Context, arg1 *platform_configpb.CreateTccConfigReq) (*platform_configpb.CreateTccConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTccConfig", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.CreateTccConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTccConfig indicates an expected call of CreateTccConfig.
func (mr *MockTccConfigSvcMockRecorder) CreateTccConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTccConfig", reflect.TypeOf((*MockTccConfigSvc)(nil).CreateTccConfig), arg0, arg1)
}

// GetTccConfigDiffByControlPlaneDeployTarget mocks base method.
func (m *MockTccConfigSvc) GetTccConfigDiffByControlPlaneDeployTarget(arg0 context.Context, arg1 *platform_configpb.GetTccConfigDiffByControlPlaneDeployTargetReq) (*platform_configpb.GetTccConfigDiffByControlPlaneDeployTargetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccConfigDiffByControlPlaneDeployTarget", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.GetTccConfigDiffByControlPlaneDeployTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccConfigDiffByControlPlaneDeployTarget indicates an expected call of GetTccConfigDiffByControlPlaneDeployTarget.
func (mr *MockTccConfigSvcMockRecorder) GetTccConfigDiffByControlPlaneDeployTarget(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccConfigDiffByControlPlaneDeployTarget", reflect.TypeOf((*MockTccConfigSvc)(nil).GetTccConfigDiffByControlPlaneDeployTarget), arg0, arg1)
}

// GetTccConfigDiffByDeployTarget mocks base method.
func (m *MockTccConfigSvc) GetTccConfigDiffByDeployTarget(arg0 context.Context, arg1 *platform_configpb.GetTccConfigDiffByDeployTargetReq, arg2 bool) (*platform_configpb.GetTccConfigDiffByDeployTargetResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccConfigDiffByDeployTarget", arg0, arg1, arg2)
	ret0, _ := ret[0].(*platform_configpb.GetTccConfigDiffByDeployTargetResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccConfigDiffByDeployTarget indicates an expected call of GetTccConfigDiffByDeployTarget.
func (mr *MockTccConfigSvcMockRecorder) GetTccConfigDiffByDeployTarget(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccConfigDiffByDeployTarget", reflect.TypeOf((*MockTccConfigSvc)(nil).GetTccConfigDiffByDeployTarget), arg0, arg1, arg2)
}

// GetTccConfigDiffByVersion mocks base method.
func (m *MockTccConfigSvc) GetTccConfigDiffByVersion(arg0 context.Context, arg1 *platform_configpb.GetTccConfigDiffByVersionReq) (*platform_configpb.GetTccConfigDiffByVersionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccConfigDiffByVersion", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.GetTccConfigDiffByVersionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccConfigDiffByVersion indicates an expected call of GetTccConfigDiffByVersion.
func (mr *MockTccConfigSvcMockRecorder) GetTccConfigDiffByVersion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccConfigDiffByVersion", reflect.TypeOf((*MockTccConfigSvc)(nil).GetTccConfigDiffByVersion), arg0, arg1)
}

// GetTccConfigList mocks base method.
func (m *MockTccConfigSvc) GetTccConfigList(arg0 context.Context, arg1 *platform_configpb.GetTccConfigListReq) (*platform_configpb.GetTccConfigListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccConfigList", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.GetTccConfigListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccConfigList indicates an expected call of GetTccConfigList.
func (mr *MockTccConfigSvcMockRecorder) GetTccConfigList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccConfigList", reflect.TypeOf((*MockTccConfigSvc)(nil).GetTccConfigList), arg0, arg1)
}

// GetTccOnlineConfigInfo mocks base method.
func (m *MockTccConfigSvc) GetTccOnlineConfigInfo(arg0 context.Context, arg1 *platform_configpb.GetTccOnlineConfigInfoReq) (*platform_configpb.GetTccOnlineConfigInfoResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccOnlineConfigInfo", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.GetTccOnlineConfigInfoResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccOnlineConfigInfo indicates an expected call of GetTccOnlineConfigInfo.
func (mr *MockTccConfigSvcMockRecorder) GetTccOnlineConfigInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccOnlineConfigInfo", reflect.TypeOf((*MockTccConfigSvc)(nil).GetTccOnlineConfigInfo), arg0, arg1)
}

// ImportMultiTccConfigs mocks base method.
func (m *MockTccConfigSvc) ImportMultiTccConfigs(arg0 context.Context, arg1 *platform_configpb.ImportMultiTccConfigsReq) (*platform_configpb.ImportMultiTccConfigsResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImportMultiTccConfigs", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.ImportMultiTccConfigsResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImportMultiTccConfigs indicates an expected call of ImportMultiTccConfigs.
func (mr *MockTccConfigSvcMockRecorder) ImportMultiTccConfigs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImportMultiTccConfigs", reflect.TypeOf((*MockTccConfigSvc)(nil).ImportMultiTccConfigs), arg0, arg1)
}

// ImportTccConfig mocks base method.
func (m *MockTccConfigSvc) ImportTccConfig(arg0 context.Context, arg1 *platform_configpb.ImportTccConfigReq, arg2 bool) (*platform_configpb.ImportTccConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImportTccConfig", arg0, arg1, arg2)
	ret0, _ := ret[0].(*platform_configpb.ImportTccConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImportTccConfig indicates an expected call of ImportTccConfig.
func (mr *MockTccConfigSvcMockRecorder) ImportTccConfig(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImportTccConfig", reflect.TypeOf((*MockTccConfigSvc)(nil).ImportTccConfig), arg0, arg1, arg2)
}

// ImportTccConfigToTargetRegion mocks base method.
func (m *MockTccConfigSvc) ImportTccConfigToTargetRegion(arg0 context.Context, arg1 *change_itempb.ImportTccConfigToTargetRegionReq) (*change_itempb.ImportTccConfigToTargetRegionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ImportTccConfigToTargetRegion", arg0, arg1)
	ret0, _ := ret[0].(*change_itempb.ImportTccConfigToTargetRegionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ImportTccConfigToTargetRegion indicates an expected call of ImportTccConfigToTargetRegion.
func (mr *MockTccConfigSvcMockRecorder) ImportTccConfigToTargetRegion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ImportTccConfigToTargetRegion", reflect.TypeOf((*MockTccConfigSvc)(nil).ImportTccConfigToTargetRegion), arg0, arg1)
}

// UpdateTccConfig mocks base method.
func (m *MockTccConfigSvc) UpdateTccConfig(arg0 context.Context, arg1 *platform_configpb.UpdateTccConfigReq) (*platform_configpb.UpdateTccConfigResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTccConfig", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.UpdateTccConfigResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTccConfig indicates an expected call of UpdateTccConfig.
func (mr *MockTccConfigSvcMockRecorder) UpdateTccConfig(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTccConfig", reflect.TypeOf((*MockTccConfigSvc)(nil).UpdateTccConfig), arg0, arg1)
}

// UpdateTccConfigStatus mocks base method.
func (m *MockTccConfigSvc) UpdateTccConfigStatus(arg0 context.Context, arg1 *change_itempb.UpdateTccConfigStatusReq) (*change_itempb.UpdateTccConfigStatusResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTccConfigStatus", arg0, arg1)
	ret0, _ := ret[0].(*change_itempb.UpdateTccConfigStatusResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTccConfigStatus indicates an expected call of UpdateTccConfigStatus.
func (mr *MockTccConfigSvcMockRecorder) UpdateTccConfigStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTccConfigStatus", reflect.TypeOf((*MockTccConfigSvc)(nil).UpdateTccConfigStatus), arg0, arg1)
}
