// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/storagerpc/pkg/service/tcc_region (interfaces: TccRegionSvc)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	authpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/authpb"
	platform_configpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	gomock "github.com/golang/mock/gomock"
)

// MockTccRegionSvc is a mock of TccRegionSvc interface.
type MockTccRegionSvc struct {
	ctrl     *gomock.Controller
	recorder *MockTccRegionSvcMockRecorder
}

// MockTccRegionSvcMockRecorder is the mock recorder for MockTccRegionSvc.
type MockTccRegionSvcMockRecorder struct {
	mock *MockTccRegionSvc
}

// NewMockTccRegionSvc creates a new mock instance.
func NewMockTccRegionSvc(ctrl *gomock.Controller) *MockTccRegionSvc {
	mock := &MockTccRegionSvc{ctrl: ctrl}
	mock.recorder = &MockTccRegionSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTccRegionSvc) EXPECT() *MockTccRegionSvcMockRecorder {
	return m.recorder
}

// CheckTccDeveloperPermission mocks base method.
func (m *MockTccRegionSvc) CheckTccDeveloperPermission(arg0 context.Context, arg1 *authpb.CheckTccDeveloperPermissionReq) (*authpb.CheckTccDeveloperPermissionResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckTccDeveloperPermission", arg0, arg1)
	ret0, _ := ret[0].(*authpb.CheckTccDeveloperPermissionResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckTccDeveloperPermission indicates an expected call of CheckTccDeveloperPermission.
func (mr *MockTccRegionSvcMockRecorder) CheckTccDeveloperPermission(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckTccDeveloperPermission", reflect.TypeOf((*MockTccRegionSvc)(nil).CheckTccDeveloperPermission), arg0, arg1)
}

// GetTccDirList mocks base method.
func (m *MockTccRegionSvc) GetTccDirList(arg0 context.Context, arg1 *platform_configpb.GetTccDirListReq) (*platform_configpb.GetTccDirListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccDirList", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.GetTccDirListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccDirList indicates an expected call of GetTccDirList.
func (mr *MockTccRegionSvcMockRecorder) GetTccDirList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccDirList", reflect.TypeOf((*MockTccRegionSvc)(nil).GetTccDirList), arg0, arg1)
}

// GetTccRegionList mocks base method.
func (m *MockTccRegionSvc) GetTccRegionList(arg0 context.Context, arg1 *platform_configpb.GetTccRegionListReq) (*platform_configpb.GetTccRegionListResp, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTccRegionList", arg0, arg1)
	ret0, _ := ret[0].(*platform_configpb.GetTccRegionListResp)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTccRegionList indicates an expected call of GetTccRegionList.
func (mr *MockTccRegionSvcMockRecorder) GetTccRegionList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTccRegionList", reflect.TypeOf((*MockTccRegionSvc)(nil).GetTccRegionList), arg0, arg1)
}
