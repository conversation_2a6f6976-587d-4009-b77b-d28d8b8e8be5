package tcc_region

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/storagerpc/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/authpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/platform_configpb"
	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"github.com/bytedance/mockey"
	"github.com/smartystreets/goconvey/convey"
)

func Test_tccRegionSvc_IsTccRegionValidAutoGen(t *testing.T) {
	// Verify the IsTccRegionValid function behavior.
	t.Run("testTccRegionSvc_IsTccRegionValid", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var cNBOERegionsMock []string
			mockey.MockValue(&apis.CNBOERegions).To(cNBOERegionsMock)

			var i18NBOERegionsMock []string
			mockey.MockValue(&apis.I18NBOERegions).To(i18NBOERegionsMock)

			// prepare parameters
			var receiverPtrValue tccRegionSvc
			receiver := &receiverPtrValue
			var region string
			var rtControlPlane []sharedpb.ControlPlane

			// run target function and assert
			got1 := receiver.IsTccRegionValid(region, rtControlPlane)
			convey.So(got1, convey.ShouldEqual, false)
		})
	})

}

func Test_tccRegionSvc_GetTccRegionListAutoGen(t *testing.T) {
	// Verify the function behavior with valid input parameters.
	t.Run("testTccRegionSvc_GetTccRegionList_ValidInput", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var userJwtRet1Mock string
			var userJwtRet2Mock error
			mockey.Mock(utils.UserJwt).Return(userJwtRet1Mock, userJwtRet2Mock).Build()

			var apiMockImpl apisApiImplForTestAutoGen
			apiMock := &apiMockImpl
			mockey.Mock(apis.TccClientWrapper.Get).Return(apiMock).Build()

			var dirListRespMock apis.DirListResp
			var getDirListByNameSpaceRet2Mock error
			mockey.Mock((*apisApiImplForTestAutoGen).GetDirListByNameSpace, mockey.OptUnsafe).Return(dirListRespMock, getDirListByNameSpaceRet2Mock).Build()

			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			var nameSpaceRespMock apis.NameSpaceResp
			var getNameSpaceRet2Mock error
			mockey.Mock((*apisApiImplForTestAutoGen).GetNameSpace, mockey.OptUnsafe).Return(nameSpaceRespMock, getNameSpaceRet2Mock).Build()

			var getByReleaseTicketIDAndPsmRet1Mock []*entity.ReleaseTicketChangeItem
			var getByReleaseTicketIDAndPsmRet2Mock error
			mockey.Mock((*mysqlReleaseTicketChangeItemDaoSvcImplForTestAutoGen).GetByReleaseTicketIDAndPsm, mockey.OptUnsafe).Return(getByReleaseTicketIDAndPsmRet1Mock, getByReleaseTicketIDAndPsmRet2Mock).Build()

			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			var isTccRegionValidRet1Mock bool
			mockey.Mock((*tccRegionSvc).IsTccRegionValid).Return(isTccRegionValidRet1Mock).Build()

			var errorfRet1Mock error
			mockey.Mock(erri.Errorf).Return(errorfRet1Mock).Build()

			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue platform_configpb.GetTccRegionListReq
			req := &reqPtrValue
			req.Psm = "not "
			var receiverPtrValue tccRegionSvc
			receiver := &receiverPtrValue
			var receiverTccClientPtrValue apis.TccClientWrapper
			receiver.TccClient = &receiverTccClientPtrValue

			// run target function and assert
			got1, got2 := receiver.GetTccRegionList(ctx, req)
			convey.So(got2 == nil, convey.ShouldBeTrue)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).TccRegionItems), convey.ShouldEqual, 0)
		})
	})

	// Verify the function behavior under default conditions.
	t.Run("testTccRegionSvc_GetTccRegionList_DefaultCondition", func(t *testing.T) {
		mockey.PatchConvey("case_1", t, func() {
			// mock function returns or global values
			var userJwtRet1Mock string
			var userJwtRet2Mock error
			mockey.Mock(utils.UserJwt).Return(userJwtRet1Mock, userJwtRet2Mock).Build()

			var errorfRet1Mock error
			mockey.Mock(erri.Errorf).Return(errorfRet1Mock).Build()

			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			var nameSpaceRespMock apis.NameSpaceResp
			var getNameSpaceRet2Mock error
			mockey.Mock((*apisApiImplForTestAutoGen).GetNameSpace, mockey.OptUnsafe).Return(nameSpaceRespMock, getNameSpaceRet2Mock).Build()

			var getRegionNameRet1Mock string
			mockey.Mock(apis.GetRegionName).Return(getRegionNameRet1Mock).Build()

			var getByReleaseTicketIDAndPsmRet1Mock []*entity.ReleaseTicketChangeItem
			var getByReleaseTicketIDAndPsmRet2Mock error
			mockey.Mock((*mysqlReleaseTicketChangeItemDaoSvcImplForTestAutoGen).GetByReleaseTicketIDAndPsm, mockey.OptUnsafe).Return(getByReleaseTicketIDAndPsmRet1Mock, getByReleaseTicketIDAndPsmRet2Mock).Build()

			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			var isTccRegionValidRet1Mock bool
			mockey.Mock((*tccRegionSvc).IsTccRegionValid).Return(isTccRegionValidRet1Mock).Build()

			var dirListRespMock apis.DirListResp
			var getDirListByNameSpaceRet2Mock error
			mockey.Mock((*apisApiImplForTestAutoGen).GetDirListByNameSpace, mockey.OptUnsafe).Return(dirListRespMock, getDirListByNameSpaceRet2Mock).Build()

			var apiMockValueImpl apisApiImplForTestAutoGen
			apiMockValue := &apiMockValueImpl
			apiMock := apiMockValue
			mockey.Mock(apis.TccClientWrapper.Get).Return(apiMock).Build()

			// prepare parameters
			var receiverPtrValue tccRegionSvc
			receiver := &receiverPtrValue
			ctx := context.Background()
			var reqPtrValue platform_configpb.GetTccRegionListReq
			req := &reqPtrValue

			// run target function and assert
			got1, got2 := receiver.GetTccRegionList(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).TccRegionItems), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}

func Test_tccRegionSvc_CheckTccDeveloperPermissionAutoGen(t *testing.T) {
	// Verify the permission check functionality of the tccRegionSvc.CheckTccDeveloperPermission method.
	t.Run("testTccRegionSvc_CheckTccDeveloperPermission", func(t *testing.T) {
		mockey.PatchConvey("case_0", t, func() {
			// mock function returns or global values
			var bitsErrMockPtrValue bits_err.BitsErr
			bitsErrMock := &bitsErrMockPtrValue
			mockey.Mock(bits_err.ToBizError).Return(bitsErrMock).Build()

			var apiMockImpl apisApiImplForTestAutoGen
			apiMock := &apiMockImpl
			mockey.Mock(apis.TccClientWrapper.Get).Return(apiMock).Build()

			var userJwtRet1Mock string
			var userJwtRet2Mock error
			mockey.Mock(utils.UserJwt).Return(userJwtRet1Mock, userJwtRet2Mock).Build()

			var nameSpaceRespMock apis.NameSpaceResp
			getNameSpaceRet2Mock := fmt.Errorf("error")
			mockey.Mock((*apisApiImplForTestAutoGen).GetNameSpace, mockey.OptUnsafe).Return(nameSpaceRespMock, getNameSpaceRet2Mock).Build()

			var codeRet1Mock int64
			mockey.Mock((*bits_err.BitsErr).Code, mockey.OptUnsafe).Return(codeRet1Mock).Build()

			// prepare parameters
			ctx := context.Background()
			var reqPtrValue authpb.CheckTccDeveloperPermissionReq
			req := &reqPtrValue
			var receiverPtrValue tccRegionSvc
			receiver := &receiverPtrValue
			var receiverTccClientPtrValue apis.TccClientWrapper
			receiver.TccClient = &receiverTccClientPtrValue

			// run target function and assert
			got1, got2 := receiver.CheckTccDeveloperPermission(ctx, req)
			convey.So(got1 == nil, convey.ShouldBeFalse)
			convey.So(len((*got1).Items), convey.ShouldEqual, 0)
			convey.So(got2 == nil, convey.ShouldBeFalse)
		})
	})

}
