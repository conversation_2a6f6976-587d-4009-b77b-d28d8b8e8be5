package entity

import (
	"code.byted.org/devinfra/hagrid/pkg/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type TccChange struct {
	gorm.Model
	Id                  int64                                 `json:"id" gorm:"primaryKey"`
	StorageChangeItemId int64                                 `json:"storage_change_item_id" gorm:"column:storage_change_item_id"`
	ChangeUniqKey       string                                `json:"change_uniq_key" gorm:"column:change_uniq_key;uniqueIndex:uniq_key"` //唯一Id，draft_name+psm+source_control_plane+source_region+source_dir
	WorkflowType        string                                `json:"workflow_type" gorm:"column:workflow_type"`
	WorkflowUniqueId    int64                                 `json:"workflow_unique_id" gorm:"column:workflow_unique_id"`
	Psm                 string                                `json:"psm" gorm:"column:psm"`
	SourceControlPlane  string                                `json:"source_control_plane" gorm:"source_control_plane"`
	SourceRegion        string                                `json:"source_region" gorm:"column:source_region"`
	SourceDir           string                                `json:"source_dir" gorm:"column:source_dir"`
	DraftName           string                                `json:"draft_name" gorm:"column:draft_name"`
	TargetConfigList    datatypes.JSONType[DBTccRegionConfig] `json:"target_config_list" gorm:"column:target_config_list"`
	Creator             string                                `json:"creator" gorm:"column:creator"`
}

type DBTccRegionConfigItem struct {
	ControlPlane string `json:"control_plane"`
	Region       string `json:"region"`
	Dir          string `json:"dir"`
}

type DBTccRegionConfig struct {
	BoeTargetRegion  []DBTccRegionConfigItem `json:"boe_target_region"`
	PpeTargetRegion  []DBTccRegionConfigItem `json:"ppe_target_region"`
	ProdTargetRegion []DBTccRegionConfigItem `json:"prod_target_region"`
}

// 带psm版本的 change item
type DBTccChangeItemInfo struct {
	TccChange
	PsmVersion        int64                                   `json:"version" gorm:"column:version"`
	TCCNamespaceIDMap datatypes.JSONType[DBTCCNamespaceIDMap] `json:"ns_id_map" gorm:"column:ns_id_map"`
}

func (TccChange) TableName() string {
	return "hagrid_tcc_storage_change_item"
}

func (i *TccChange) BeforeCreate(tx *gorm.DB) (err error) {
	if i.StorageChangeItemId != 0 {
		return nil
	}
	var id uint64
	if id, err = uuid.UniqueID(); err != nil {
		return err
	}
	i.StorageChangeItemId = int64(id)
	return nil
}
