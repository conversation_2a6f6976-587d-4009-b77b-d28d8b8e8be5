// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql (interfaces: StoragePsmVersionDaoSvc)

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/storagerpc/pkg/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockStoragePsmVersionDaoSvc is a mock of StoragePsmVersionDaoSvc interface.
type MockStoragePsmVersionDaoSvc struct {
	ctrl     *gomock.Controller
	recorder *MockStoragePsmVersionDaoSvcMockRecorder
}

// MockStoragePsmVersionDaoSvcMockRecorder is the mock recorder for MockStoragePsmVersionDaoSvc.
type MockStoragePsmVersionDaoSvcMockRecorder struct {
	mock *MockStoragePsmVersionDaoSvc
}

// NewMockStoragePsmVersionDaoSvc creates a new mock instance.
func NewMockStoragePsmVersionDaoSvc(ctrl *gomock.Controller) *MockStoragePsmVersionDaoSvc {
	mock := &MockStoragePsmVersionDaoSvc{ctrl: ctrl}
	mock.recorder = &MockStoragePsmVersionDaoSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStoragePsmVersionDaoSvc) EXPECT() *MockStoragePsmVersionDaoSvcMockRecorder {
	return m.recorder
}

// BatchUpsert mocks base method.
func (m *MockStoragePsmVersionDaoSvc) BatchUpsert(arg0 context.Context, arg1 []*entity.StoragePsmVersion) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpsert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpsert indicates an expected call of BatchUpsert.
func (mr *MockStoragePsmVersionDaoSvcMockRecorder) BatchUpsert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpsert", reflect.TypeOf((*MockStoragePsmVersionDaoSvc)(nil).BatchUpsert), arg0, arg1)
}

// GetStoragePsmVersionByWorkflowUniqueId mocks base method.
func (m *MockStoragePsmVersionDaoSvc) GetStoragePsmVersionByWorkflowUniqueId(arg0 context.Context, arg1 string, arg2 uint64, arg3 string) (*entity.StoragePsmVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStoragePsmVersionByWorkflowUniqueId", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*entity.StoragePsmVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoragePsmVersionByWorkflowUniqueId indicates an expected call of GetStoragePsmVersionByWorkflowUniqueId.
func (mr *MockStoragePsmVersionDaoSvcMockRecorder) GetStoragePsmVersionByWorkflowUniqueId(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoragePsmVersionByWorkflowUniqueId", reflect.TypeOf((*MockStoragePsmVersionDaoSvc)(nil).GetStoragePsmVersionByWorkflowUniqueId), arg0, arg1, arg2, arg3)
}

// GetStoragePsmVersionListByWorkflowUniqueId mocks base method.
func (m *MockStoragePsmVersionDaoSvc) GetStoragePsmVersionListByWorkflowUniqueId(arg0 context.Context, arg1 string, arg2 uint64) ([]*entity.StoragePsmVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStoragePsmVersionListByWorkflowUniqueId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.StoragePsmVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoragePsmVersionListByWorkflowUniqueId indicates an expected call of GetStoragePsmVersionListByWorkflowUniqueId.
func (mr *MockStoragePsmVersionDaoSvcMockRecorder) GetStoragePsmVersionListByWorkflowUniqueId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoragePsmVersionListByWorkflowUniqueId", reflect.TypeOf((*MockStoragePsmVersionDaoSvc)(nil).GetStoragePsmVersionListByWorkflowUniqueId), arg0, arg1, arg2)
}

// GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList mocks base method.
func (m *MockStoragePsmVersionDaoSvc) GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList(arg0 context.Context, arg1 string, arg2 uint64, arg3 []string) ([]*entity.StoragePsmVersion, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*entity.StoragePsmVersion)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList indicates an expected call of GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList.
func (mr *MockStoragePsmVersionDaoSvcMockRecorder) GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList", reflect.TypeOf((*MockStoragePsmVersionDaoSvc)(nil).GetStoragePsmVersionListByWorkflowUniqueIdAndPsmList), arg0, arg1, arg2, arg3)
}

// Upsert mocks base method.
func (m *MockStoragePsmVersionDaoSvc) Upsert(arg0 context.Context, arg1 *entity.StoragePsmVersion, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockStoragePsmVersionDaoSvcMockRecorder) Upsert(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockStoragePsmVersionDaoSvc)(nil).Upsert), arg0, arg1, arg2)
}
