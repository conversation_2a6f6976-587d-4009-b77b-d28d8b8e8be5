package idgen

import (
	"context"

	"code.byted.org/gopkg/idgenerator/v2"
	"code.byted.org/gopkg/logs/v2"
)

var (
	Client idgenerator.NtIdGenerator
)

func MustInitialize() {
	var err error
	Client, err = idgenerator.NewNtIdGeneratorBuilder().
		WithNamespace("bits_hagrid").
		Build()
	if err != nil {
		logs.Error("id generator client init fail, err: %v", err)
		panic(err)
	}
}

// GetID ...
func GetID(ctx context.Context) (uint64, error) {
	return Client.Get(ctx)
}
