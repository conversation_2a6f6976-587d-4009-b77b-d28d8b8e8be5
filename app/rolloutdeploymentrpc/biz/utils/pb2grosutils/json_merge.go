package pb2grosutils

import (
	"encoding/json"
	"strconv"
	"strings"

	"code.byted.org/devinfra/hagrid/app/rolloutservices/utils/std_error"
)

func recursiveDissectUpdate(msg JsonObject, fm []string, val json.RawMessage) error {
	opField := fm[0]
	fieldName, index, isSlice := isFieldSlice(opField)
	if isSlice {
		var s []json.RawMessage
		fieldVal, ok := msg[fieldName]
		if !ok {
			s = make([]json.RawMessage, 0)
		} else {
			if err := json.Unmarshal(fieldVal, &s); err != nil {
				return std_error.StatusJsonUnmarshal.Wrap(err, "recursiveDissectUpdate")
			}
		}

		if index >= len(s) {
			for i := len(s); i <= index; i++ {
				s = append(s, nil)
			}
		}

		if len(fm) == 1 {
			// last item
			s[index] = val
		} else {
			m := JsonObject{}
			if s[index] != nil && string(s[index]) != "null" {
				if err := json.Unmarshal(s[index], &m); err != nil {
					return std_error.StatusJsonUnmarshal.Wrapf(err, "recursiveDissectUpdate failed for target slice item not a json object: %s", string(fieldVal))
				}
			}
			if err := recursiveDissectUpdate(m, fm[1:], val); err != nil {
				return err
			}
			newObjVal, err := json.Marshal(m)
			if err != nil {
				return std_error.StatusJsonMarshal.Wrapf(err, "marshal JsonObject")
			}
			s[index] = newObjVal
		}
		newSliceVal, err := json.Marshal(s)
		if err != nil {
			return std_error.StatusJsonMarshal.Wrapf(err, "marshal []json.RawMessage")
		}
		msg[fieldName] = newSliceVal
		return nil
	}

	// 不是数组

	if len(fm) == 1 {
		// last item
		msg[opField] = val
		return nil
	}

	// not last item, so msg[opField] must be a json object
	m := JsonObject{}
	if fieldVal, ok := msg[opField]; ok {
		// unmarshal current value into the json object
		if err := json.Unmarshal(fieldVal, &m); err != nil {
			return std_error.StatusJsonUnmarshal.Wrapf(err, "recursiveDissectUpdate failed for target position not a json object: %s", fieldVal)
		}
	}

	// update val in m
	if err := recursiveDissectUpdate(m, fm[1:], val); err != nil {
		return err
	}

	bytes, err := json.Marshal(m)
	if err != nil {
		return std_error.StatusJsonMarshal.Wrapf(err, "marshal JsonObject")
	}
	msg[opField] = bytes
	return nil
}

func isFieldSlice(v string) (fieldName string, index int, isSlice bool) {
	if !strings.HasSuffix(v, "]") {
		return "", 0, false
	}
	bytes := []byte(v)
	start := 0
	for i := len(bytes) - 1; i >= 0; i-- {
		if bytes[i] == '[' {
			start = i
		}
	}
	if start == 0 {
		return "", 0, false
	}

	index, err := strconv.Atoi(string(bytes[start+1 : len(bytes)-1]))
	if err != nil {
		return "", 0, false
	}
	return string(bytes[:start]), index, true
}

// updateByFieldMask does a simple fieldmask replacement and return a marshalled json string after the replacement
func updateByFieldMask(msg JsonObject, fieldMask string, val json.RawMessage) (JsonObject, error) {
	fieldSlice := strings.Split(fieldMask, ".")
	err := recursiveDissectUpdate(msg, fieldSlice, val)
	if err != nil {
		return nil, err
	}

	return msg, nil
}
