load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "utils",
    srcs = ["utils.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/rolloutdeploymentrpc/biz/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//app/rolloutdeploymentrpc/biz/utils/pb2grosutils",
        "//app/rolloutdeploymentrpc/pkg/errutil",
        "//app/rolloutdeploymentrpc/pkg/jsonutil",
        "//app/rolloutdeploymentrpc/pkg/pbutil",
        "//app/rolloutdeploymentrpc/pkg/resourceregistry",
        "//idls/byted/devinfra/rollout:rollout_go_proto",
        "//idls/byted/devinfra/rolloutapp/deployment:deployment_go_proto",
        "//libs/errors",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_gopkg_logs_v2//log",
        "@org_byted_code_ros_ros_sdk_go//model",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "utils_test",
    srcs = ["utils_test.go"],
    embed = [":utils"],
    deps = [
        "//app/rolloutdeploymentrpc/pkg/parser",
        "//idls/byted/devinfra/rollout:rollout_go_proto",
        "//idls/byted/devinfra/rollout/resources:resources_go_proto",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_golang_google_protobuf//proto",
        "@org_golang_google_protobuf//types/known/anypb",
        "@org_golang_google_protobuf//types/known/structpb",
    ],
)
