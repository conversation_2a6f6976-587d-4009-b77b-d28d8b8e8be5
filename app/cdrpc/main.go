package main

import (
	"code.byted.org/devinfra/hagrid/libs/middleware/kitex1701"
	bits_kitexmw "code.byted.org/devinfra/hagrid/libs/middleware/kitexmw"
	_ "code.byted.org/devinfra/hagrid/libs/prelude"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cdpb/cdrpc"
	"code.byted.org/devinfra/hagrid/pkg/middlewares/kitexmw"
	"code.byted.org/gopkg/logs/v2/log"
	"code.byted.org/iesarch/paas_sdk/util/trace/regionmark"
	"code.byted.org/kite/kitex/server"
)

func main() {
	Init()
	mws := kitexmw.NewServerSuite()
	// 先在cdrpc中试用MarkRequestRegionKitexMiddleware，验证过后再加入到kitexmw.NewServerSuite中
	mws = append(mws, server.WithMiddleware(regionmark.MarkRequestRegionKitexMiddleware))
	mws = append(mws, server.WithMiddleware(bits_kitexmw.WarpError))
	mws = append(mws, server.WithMiddleware(kitexmw.BitsErrTracerMW))
	mws = append(mws, server.WithRecvMiddleware(bits_kitexmw.LogServerSideRequestResponseRecv))
	mws = append(mws, server.WithMiddleware(bits_kitexmw.LogRequestResponse))
	mws = append(mws, server.WithMiddleware(kitex1701.ResponseAvoid1701Error))
	mws = append(mws, server.WithMiddleware(bits_kitexmw.WithJwt))
	mws = append(mws, server.WithMiddleware(bits_kitexmw.WithUserName))
	if err := cdrpc.NewServer(NewCDRPC(), mws...).Run(); err != nil {
		log.V2.Fatal().Str("failed to run server").Error(err).Emit()
	}
}
