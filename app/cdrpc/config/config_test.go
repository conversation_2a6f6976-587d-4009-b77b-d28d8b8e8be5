package config

import (
	"context"
	"fmt"
	"os"
	"path"
	"testing"

	"code.byted.org/gopkg/env"
	"github.com/bytedance/mockey"
	"github.com/cloudwego/kitex/pkg/utils"
	"github.com/stretchr/testify/suite"
)

type ConfigTestSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
}

func (t *ConfigTestSuite) setupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
}

func (t *ConfigTestSuite) tearDownTest() {
	t.cancel()
}

func TestConfig(t *testing.T) {
	__dirname, _ := os.Getwd()
	os.Setenv(utils.EnvConfDir, path.Join(__dirname, "../conf"))
	suite.Run(t, new(ConfigTestSuite))
}

func (t *ConfigTestSuite) TestMustInitializeConfigCase1() {
	fmt.Println(os.Getwd())
	mockey.PatchConvey("prod", &testing.T{}, func() {
		mockey.Mock(env.IsProduct).Return(true).Build()
		mockey.Mock(env.IsBoe).Return(false).Build()
		config := MustInitializeConfig()
		t.Equal("prod", config.ConfName)
		t.Equal("toutiao.mysql.canal_delivery", config.Mysql.PSM)
		t.Equal("https://code.byted.org", config.Codebase.WebHost)
	})
}

func (t *ConfigTestSuite) TestMustInitializeConfigCase2() {
	mockey.PatchConvey("boe", &testing.T{}, func() {
		mockey.Mock(env.IsProduct).Return(false).Build()
		mockey.Mock(env.IsBoe).Return(true).Build()
		config := MustInitializeConfig()
		t.Equal("boe", config.ConfName)
		t.Equal("toutiao.mysql.canal_delivery", config.Mysql.PSM)
	})
}
