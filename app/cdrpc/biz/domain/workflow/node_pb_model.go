package workflowdomain

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
)

type NodePBModel struct {
	*workflowpb.Node
}

func NewNodePBModel(node *workflowpb.Node) *NodePBModel {
	return &NodePBModel{node}
}

func (m *NodePBModel) NodeConfigEnv() (*workflowpb.Env, error) {
	nodeConfig := m.GetNodeConfig()

	if m.NodeType == workflowpb.NodeType_NODE_TYPE_PIPELINE_STAGE && nodeConfig.GetPipelineNodeConfig() != nil {
		return nodeConfig.GetPipelineNodeConfig().GetEnv(), nil
	}

	if m.NodeType == workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE && nodeConfig.GetDeployNodeConfig() != nil {
		return nodeConfig.GetDeployNodeConfig().GetEnv(), nil
	}

	return nil, fmt.Errorf("no valid environment configuration found in node/%d", m.GetId())
}

// MergeEnv 将新的环境配置合入到当前环境配置的拷贝中，返回一个新的环境配置，不会
// 篡改当前数据
func (m *NodePBModel) MergeEnv(incomingEnv *workflowpb.Env) (*workflowpb.Env, error) {
	currentEnv, err := m.NodeConfigEnv()
	if err != nil {
		return nil, err
	}

	mergedEnv := NewEnvPBModel(currentEnv).Merge(incomingEnv)

	return mergedEnv, nil
}

func (m *NodePBModel) SetNodeConfigEnv(env *workflowpb.Env) {
	nodeConfig := m.GetNodeConfig()

	if m.NodeType == workflowpb.NodeType_NODE_TYPE_PIPELINE_STAGE && nodeConfig.GetPipelineNodeConfig() != nil {
		nodeConfig.GetPipelineNodeConfig().Env = env
	}

	if m.NodeType == workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE && nodeConfig.GetDeployNodeConfig() != nil {
		nodeConfig.GetDeployNodeConfig().Env = env
	}
}
