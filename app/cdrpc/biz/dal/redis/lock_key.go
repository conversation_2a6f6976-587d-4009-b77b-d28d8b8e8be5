package redis

import (
	"fmt"

	"code.byted.org/iesarch/cdaas_utils/erri"
)

const (
	ReleaseTicketPostTaskLockKeyPrefix                = "ReleaseTicketPostTask"
	ReleaseTicketPostTaskCreationLockKeyPrefix        = "ReleaseTicketPostTaskCreation"
	ReleaseTicketRollbackLockKeyPrefix                = "ReleaseTicketRollback"
	ReleaseTicketUpdateLockKeyPrefix                  = "ReleaseTicketInfoUpdate"
	InitCDWorkflowKeyPrefix                           = "InitCDWorkflow"
	ChangeItemUpdateLockKeyPrefix                     = "bitscdrpc/UpdateChangeItem"
	MiniTaskLockKeyPrefix                             = "bitscdrpc/minitask"
	EnvGCPostTaskCreationLockKeyPrefix                = "EnvGCPostTaskCreation"
	DelBranchPostTaskCreationLockKeyPrefix            = "DelBranchPostTaskCreation"
	ReleaseTicketStartIntegrationLockKeyPrefix        = "ReleaseTicketStartIntegration"
	ReleaseTicketFinishIntegrationLockKeyPrefix       = "ReleaseTicketFinishIntegration"
	SingleRtMRStateUpdateLockKeyPrefix                = "SingleRtMRStateUpdate"
	StageStartLockKeyPrefix                           = "StageStart"
	StageCompleteLockKeyPrefix                        = "StageComplete"
	UpdateGraySpacesTccKeyPrefix                      = "UpdateGraySpacesTcc"
	UpdateGraySpacesTccRtFlowExecutionKeyPrefix       = "UpdateGraySpacesTccRtFlowExecution"
	ReleaseTicketIntegrationVersionKeyPrefix          = "ReleaseTicketIntegrationVersion"
	ReleaseTicketInitTaskKeyPrefix                    = "ReleaseTicketInitTask"
	ReleaseTicketDeployStageTaskKeyPrefix             = "ReleaseTicketDeployStageTask"
	ReleaseTicketResetConcurrencyCounterLockKeyPrefix = "ReleaseTicketResetConcurrencyCounter"
	ReleaseIntegrationExitTaskKeyPrefix               = "ReleaseIntegrationExitTask"
	ReleaseIntegrationAccessTaskKeyPrefix             = "ReleaseIntegrationAccessTask"
	ReleaseTicketRaceDeploymentLock                   = "ReleaseTicketRaceDeploymentLock"
	ReleaseTicketSyncLock                             = "ReleaseTicketSyncLock"
)

func BuildReleaseTicketPostTaskSubmitLockKey(rtID, taskID uint64, projectType, projectUniqueID, boeBaseEnv string) string {
	return fmt.Sprintf("%s/%d/%d/%s/%s/%s/submit",
		ReleaseTicketPostTaskLockKeyPrefix,
		rtID,
		taskID,
		projectType,
		projectUniqueID,
		boeBaseEnv)
}

func BuildReleaseTicketPostTaskPollLockKey(rtID, taskID uint64, projectType, projectUniqueID, boeBaseEnv string) string {
	return fmt.Sprintf("%s/%d/%d/%s/%s/%s/poll",
		ReleaseTicketPostTaskLockKeyPrefix,
		rtID,
		taskID,
		projectType,
		projectUniqueID,
		boeBaseEnv)
}

func BuildReleasePostTaskCreationLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/release/%d", ReleaseTicketPostTaskCreationLockKeyPrefix, rtID)
}

func BuildRollbackPostTaskCreationLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/rollback/%d", ReleaseTicketPostTaskCreationLockKeyPrefix, rtID)
}

func BuildReleaseTicketStagePipelineUpdateLockKey(stageId uint64, cp string) (string, error) {
	if stageId == 0 {
		return "", erri.Errorf("invalid getLockKey param: stageId is 0")
	}

	if cp == "" {
		return "", erri.Errorf("invalid getLockKey param: controlPlane is empty")
	}

	return fmt.Sprintf("StagePipelineUpdate:%d:%s", stageId, cp), nil
}

func BuildInitWorkflowKey(workspaceID uint64) string {
	return fmt.Sprintf("%s/%d/init",
		InitCDWorkflowKeyPrefix,
		workspaceID)
}

func BuildReleaseTicketChangeItemUpdateLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ChangeItemUpdateLockKeyPrefix, rtID)
}

func BuildReleaseTicketRollbackLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketRollbackLockKeyPrefix, rtID)
}

func BuildMiniTaskGroupKey(groupKey, bitsEnv string) string {
	return fmt.Sprintf("%s/%s/%s", MiniTaskLockKeyPrefix, groupKey, bitsEnv)
}

func BuildSyncRepoKey(rtId uint64, repoId int64) string {
	return fmt.Sprintf("%s/%d/%d", ReleaseTicketSyncLock, rtId, repoId)
}

func BuildEnvGCPostTaskCreationLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/env_gc/%d", EnvGCPostTaskCreationLockKeyPrefix, rtID)
}

func BuildEnvGCPostTaskSubmitLockKey(rtID, taskID uint64, envName, standardEnv, serviceType string) string {
	return fmt.Sprintf("%s/%d/%d/%s/%s/%s/submit",
		EnvGCPostTaskCreationLockKeyPrefix,
		rtID,
		taskID,
		envName,
		standardEnv,
		serviceType)
}

func BuildUpdateReleaseTicketKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketUpdateLockKeyPrefix, rtID)
}

func BuildReleaseTicketStartIntegrationKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketStartIntegrationLockKeyPrefix, rtID)
}

func BuildReleaseTicketFinishIntegrationKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketFinishIntegrationLockKeyPrefix, rtID)
}

func BuildDelBranchPostTaskCreationLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/branch_del/%d", DelBranchPostTaskCreationLockKeyPrefix, rtID)
}

func BuildDelBranchPostTaskSubmitLockKey(rtID, taskID uint64, branchName string) string {
	return fmt.Sprintf("%s/%d/%d/%s/submit",
		DelBranchPostTaskCreationLockKeyPrefix,
		rtID,
		taskID,
		branchName)
}

func BuildSingleRtMRStateUpdateLockKey(iid int64, repoName string) string {
	return fmt.Sprintf("%s/%s/%d", SingleRtMRStateUpdateLockKeyPrefix, repoName, iid)
}

func BuildUpdateGraySpacesTccKey() string {
	return UpdateGraySpacesTccKeyPrefix
}

func BuildUpdateGraySpacesTccRtFlowExecutionKey() string {
	return fmt.Sprintf("%s", UpdateGraySpacesTccRtFlowExecutionKeyPrefix)
}

func BuildStageStartLockKey(stageID int64) string {
	return fmt.Sprintf("%s/%d", StageStartLockKeyPrefix, stageID)
}

func BuildStageCompleteLockKey(stageID uint64) string {
	return fmt.Sprintf("%s/%d", StageCompleteLockKeyPrefix, stageID)
}

func BuildReleaseTicketIntegrationVersionKey(integrationID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketIntegrationVersionKeyPrefix, integrationID)
}

func BuildReleaseTicketInitTaskKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketInitTaskKeyPrefix, rtID)
}

func BuildReleaseTicketDeployStageTaskLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketDeployStageTaskKeyPrefix, rtID)
}

func BuildReleaseTicketResetConcurrencyCounterLockKeyByTaskID(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketResetConcurrencyCounterLockKeyPrefix, rtID)
}

func BuildReleaseIntegrationExitTaskLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseIntegrationExitTaskKeyPrefix, rtID)
}

func BuildReleaseIntegrationAccessTaskLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseIntegrationAccessTaskKeyPrefix, rtID)
}

func BuildReleaseTicketRaceDeploymentLockKey(rtID uint64) string {
	return fmt.Sprintf("%s/%d", ReleaseTicketRaceDeploymentLock, rtID)
}
