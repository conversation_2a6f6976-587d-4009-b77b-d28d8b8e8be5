package repository

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"github.com/stretchr/testify/assert"
)

func TestGetByWorkspaceId(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkspaceChangeItemDependency{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewWorkspaceChangeItemDependencyDao()
	db.Create(&entity.DBWorkspaceChangeItemDependency{
		WorkspaceID:     1,
		ProjectType:     "tce",
		ProjectUniqueID: "p.s.m",
		ControlPlane:    "-",
	})
	db.Create(&entity.DBWorkspaceChangeItemDependency{
		WorkspaceID:     1,
		ProjectType:     "tcc",
		ProjectUniqueID: "p.s.m",
		ControlPlane:    "-",
	})

	workspaceChangeItems, _ := dao.GetByWorkspaceID(ctx, 1)
	assert.Len(t, workspaceChangeItems, 2)
	assert.Equal(t, "tce", workspaceChangeItems[0].ProjectType)
}
