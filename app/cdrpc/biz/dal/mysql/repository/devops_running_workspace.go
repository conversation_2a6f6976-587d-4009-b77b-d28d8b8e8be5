package repository

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/iesarch/cdaas_utils/erri"
)

//go:generate mockgen -destination mock/devops_running_workspace_mock.go -package repositorymock code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository DevopsRunningWorkspaceDao

type devopsRunningWorkspaceDao struct{}

type DevopsRunningWorkspaceDao interface {
	BatchGetByIDs(ctx context.Context, ids []uint64) ([]*entity.DBDevopsRunningWorkspace, error)
}

func NewDevopsRunningWorkspaceDao() DevopsRunningWorkspaceDao {
	return &devopsRunningWorkspaceDao{}
}

func (d devopsRunningWorkspaceDao) BatchGetByIDs(ctx context.Context, ids []uint64) ([]*entity.DBDevopsRunningWorkspace, error) {
	records := make([]*entity.DBDevopsRunningWorkspace, 0)
	if err := mysql.DevOpsRunningDB.WithContext(ctx).Model(&entity.DBDevopsRunningWorkspace{}).
		Where("id in (?)", ids).Find(&records).Error; err != nil {
		return nil, erri.Error(err)
	}
	return records, nil
}
