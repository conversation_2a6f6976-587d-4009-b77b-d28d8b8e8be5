// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository (interfaces: ReleaseApproverDao)

// Package repositorymock is a generated GoMock package.
package repositorymock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockReleaseApproverDao is a mock of ReleaseApproverDao interface.
type MockReleaseApproverDao struct {
	ctrl     *gomock.Controller
	recorder *MockReleaseApproverDaoMockRecorder
}

// MockReleaseApproverDaoMockRecorder is the mock recorder for MockReleaseApproverDao.
type MockReleaseApproverDaoMockRecorder struct {
	mock *MockReleaseApproverDao
}

// NewMockReleaseApproverDao creates a new mock instance.
func NewMockReleaseApproverDao(ctrl *gomock.Controller) *MockReleaseApproverDao {
	mock := &MockReleaseApproverDao{ctrl: ctrl}
	mock.recorder = &MockReleaseApproverDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReleaseApproverDao) EXPECT() *MockReleaseApproverDaoMockRecorder {
	return m.recorder
}

// DeleteApprovers mocks base method.
func (m *MockReleaseApproverDao) DeleteApprovers(arg0 context.Context, arg1 []uint64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteApprovers", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteApprovers indicates an expected call of DeleteApprovers.
func (mr *MockReleaseApproverDaoMockRecorder) DeleteApprovers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteApprovers", reflect.TypeOf((*MockReleaseApproverDao)(nil).DeleteApprovers), arg0, arg1)
}

// DeleteByReleaseTicketIDAndRole mocks base method.
func (m *MockReleaseApproverDao) DeleteByReleaseTicketIDAndRole(arg0 context.Context, arg1 uint64, arg2, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByReleaseTicketIDAndRole", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByReleaseTicketIDAndRole indicates an expected call of DeleteByReleaseTicketIDAndRole.
func (mr *MockReleaseApproverDaoMockRecorder) DeleteByReleaseTicketIDAndRole(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByReleaseTicketIDAndRole", reflect.TypeOf((*MockReleaseApproverDao)(nil).DeleteByReleaseTicketIDAndRole), arg0, arg1, arg2, arg3)
}

// GetAllRoleByReleaseTicketID mocks base method.
func (m *MockReleaseApproverDao) GetAllRoleByReleaseTicketID(arg0 context.Context, arg1 uint64) ([]*entity.DBReleaseTicketReleaseApprover, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRoleByReleaseTicketID", arg0, arg1)
	ret0, _ := ret[0].([]*entity.DBReleaseTicketReleaseApprover)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRoleByReleaseTicketID indicates an expected call of GetAllRoleByReleaseTicketID.
func (mr *MockReleaseApproverDaoMockRecorder) GetAllRoleByReleaseTicketID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRoleByReleaseTicketID", reflect.TypeOf((*MockReleaseApproverDao)(nil).GetAllRoleByReleaseTicketID), arg0, arg1)
}

// GetByReleaseTicketID mocks base method.
func (m *MockReleaseApproverDao) GetByReleaseTicketID(arg0 context.Context, arg1 uint64, arg2 string) ([]*entity.DBReleaseTicketReleaseApprover, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByReleaseTicketID", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*entity.DBReleaseTicketReleaseApprover)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByReleaseTicketID indicates an expected call of GetByReleaseTicketID.
func (mr *MockReleaseApproverDaoMockRecorder) GetByReleaseTicketID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByReleaseTicketID", reflect.TypeOf((*MockReleaseApproverDao)(nil).GetByReleaseTicketID), arg0, arg1, arg2)
}

// GetByReleaseTicketIDList mocks base method.
func (m *MockReleaseApproverDao) GetByReleaseTicketIDList(arg0 context.Context, arg1 []uint64, arg2 string) (map[uint64][]*entity.DBReleaseTicketReleaseApprover, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByReleaseTicketIDList", arg0, arg1, arg2)
	ret0, _ := ret[0].(map[uint64][]*entity.DBReleaseTicketReleaseApprover)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByReleaseTicketIDList indicates an expected call of GetByReleaseTicketIDList.
func (mr *MockReleaseApproverDaoMockRecorder) GetByReleaseTicketIDList(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByReleaseTicketIDList", reflect.TypeOf((*MockReleaseApproverDao)(nil).GetByReleaseTicketIDList), arg0, arg1, arg2)
}

// Insert mocks base method.
func (m *MockReleaseApproverDao) Insert(arg0 context.Context, arg1 *entity.DBReleaseTicketReleaseApprover) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockReleaseApproverDaoMockRecorder) Insert(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockReleaseApproverDao)(nil).Insert), arg0, arg1)
}

// InsertApproversInBatch mocks base method.
func (m *MockReleaseApproverDao) InsertApproversInBatch(arg0 context.Context, arg1 uint64, arg2 []string, arg3 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertApproversInBatch", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertApproversInBatch indicates an expected call of InsertApproversInBatch.
func (mr *MockReleaseApproverDaoMockRecorder) InsertApproversInBatch(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertApproversInBatch", reflect.TypeOf((*MockReleaseApproverDao)(nil).InsertApproversInBatch), arg0, arg1, arg2, arg3)
}

// InsertInBatches mocks base method.
func (m *MockReleaseApproverDao) InsertInBatches(arg0 context.Context, arg1 []*entity.DBReleaseTicketReleaseApprover) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InsertInBatches", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertInBatches indicates an expected call of InsertInBatches.
func (mr *MockReleaseApproverDaoMockRecorder) InsertInBatches(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertInBatches", reflect.TypeOf((*MockReleaseApproverDao)(nil).InsertInBatches), arg0, arg1)
}
