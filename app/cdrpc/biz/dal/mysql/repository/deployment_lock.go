package repository

import (
	"context"

	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"
)

//go:generate mockgen -destination mock/deployment_lock_mock.go -package repositorymock code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository DeploymentLockDao

type deploymentLockDao struct {
}

type DeploymentLockDao interface {
	GetRtListByLockID(ctx context.Context, lockID uint64) ([]*entity.DBReleaseTicket, error)
	InsertDeploymentLock(ctx context.Context, lock *entity.DBDeploymentLock) error
	DeleteDeploymentLockByRtID(ctx context.Context, rtID uint64) error
	DeleteDeploymentLockByChangeItemID(ctx context.Context, changeItemID uint64) error
}

func NewDeploymentLockDao() DeploymentLockDao {
	return &deploymentLockDao{}
}

func (m *deploymentLockDao) GetRtListByLockID(ctx context.Context, lockID uint64) ([]*entity.DBReleaseTicket, error) {
	db, err := mysql.GetMasterConn(ctx)
	if err != nil {
		return nil, err
	}
	rts := make([]*entity.DBReleaseTicket, 0)
	if err = db.WithContext(ctx).Table("hagrid_deployment_lock").
		Select("hagrid_release_ticket.*").
		Joins("LEFT JOIN hagrid_release_ticket ON hagrid_release_ticket.release_ticket_id = hagrid_deployment_lock.release_ticket_id").
		Where("hagrid_deployment_lock.lock_id = ?", lockID).Find(&rts).Error; err != nil {
		if utils.ErrIsRecordNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return rts, nil
}

func (m *deploymentLockDao) InsertDeploymentLock(ctx context.Context, lock *entity.DBDeploymentLock) error {
	db, err := mysql.GetMasterConn(ctx)
	if err != nil {
		return err
	}

	var oldLock *entity.DBDeploymentLock
	// 查找未删除的记录
	err = db.WithContext(ctx).Where("change_item_id = ? AND lock_id = ?", lock.ChangeItemID, lock.LockID).First(&oldLock).Error
	// 找到后直接返回
	if err == nil {
		return nil
	}

	if !utils.ErrIsRecordNotFound(err) {
		return err
	}
	// 插入新记录
	if err = db.WithContext(ctx).Create(&lock).Error; err != nil {
		return err
	}
	return nil
}

func (m *deploymentLockDao) DeleteDeploymentLockByRtID(ctx context.Context, rtID uint64) error {
	db, err := mysql.GetDefaultConn(ctx)
	if err != nil {
		return err
	}

	locks := make([]*entity.DBDeploymentLock, 0)
	result := db.WithContext(ctx).Where("release_ticket_id = ?", rtID).Find(&locks)
	if result.Error != nil {
		if utils.ErrIsRecordNotFound(result.Error) {
			return nil
		}
		return result.Error
	}

	if err = result.Delete(&entity.DBDeploymentLock{}).Error; err != nil {
		return err
	}
	lockIDs := make([]uint64, 0)
	for _, lock := range locks {
		lockIDs = append(lockIDs, lock.LockID)
	}
	lockIDs = gslice.Uniq(lockIDs)
	// 是否有其他发布单占锁，如果没有可以删除锁资源
	for _, lockID := range lockIDs {
		var count int64
		if err = db.WithContext(ctx).Model(&entity.DBDeploymentLock{}).Where("lock_id = ?", lockID).Count(&count).Error; err != nil {
			logs.CtxWarn(ctx, "DBDeploymentLock lock_id=%d not found", lockID)
			continue
		}
		if count == 0 {
			mutexLock := entity.DBMutexLock{}
			// 删除时进行版本校验
			if err = db.WithContext(ctx).Where("id = ?", lockID).Find(&mutexLock).Error; err != nil {
				continue
			}
			_ = deleteDeleteMutexLock(ctx, db, mutexLock, lockID)
		}
	}
	return nil
}

func (m *deploymentLockDao) DeleteDeploymentLockByChangeItemID(ctx context.Context, changeItemID uint64) error {
	db, err := mysql.GetDefaultConn(ctx)
	if err != nil {
		return err
	}

	locks := make([]*entity.DBDeploymentLock, 0)
	result := db.WithContext(ctx).Where("change_item_id = ?", changeItemID).Find(&locks)
	if result.Error != nil {
		if utils.ErrIsRecordNotFound(result.Error) {
			return nil
		}
		return result.Error
	}

	if err = result.Delete(&entity.DBDeploymentLock{}).Error; err != nil {
		return err
	}
	lockIDs := make([]uint64, 0)
	for _, lock := range locks {
		lockIDs = append(lockIDs, lock.LockID)
	}
	lockIDs = gslice.Uniq(lockIDs)
	// 是否有其他发布单占锁，如果没有可以删除锁资源
	for _, lockID := range lockIDs {
		var count int64
		if err = db.WithContext(ctx).Model(&entity.DBDeploymentLock{}).Where("lock_id = ?", lockID).Count(&count).Error; err != nil {
			return err
		}
		if count == 0 {
			mutexLock := entity.DBMutexLock{}
			// 删除时进行版本校验
			if errT := db.WithContext(ctx).Where("id = ?", lockID).Find(&mutexLock).Error; errT != nil {
				continue
			}
			_ = deleteDeleteMutexLock(ctx, db, mutexLock, lockID)
		}
	}
	return nil
}

func deleteDeleteMutexLock(ctx context.Context, db *gorm.DB, mutexLock entity.DBMutexLock, lockID uint64) error {
	spinLock := redis.NewDeploymentMutexLock(ctx, mutexLock.Resource, mutexLock.ScopeHash)
	err := spinLock.Lock(ctx)
	if err != nil {
		logs.CtxError(ctx, "get retry lock failed, err:%s", err.Error())
		return err
	}
	defer func() {
		_ = spinLock.UnLock(ctx)
	}()
	resultD := db.WithContext(ctx).Where("id = ? and version = ?", lockID, mutexLock.Version).Delete(&entity.DBMutexLock{})
	if resultD.Error != nil {
		return resultD.Error
	}
	// 版本号不匹配，有可能有新的发布单项目关联锁；匹配会删除锁
	return nil
}
