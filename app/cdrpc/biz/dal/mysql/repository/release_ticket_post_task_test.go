package repository

import (
	"context"
	"testing"

	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
)

func Test_releaseTicketPostTaskDao_BatchCreate(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.BatchCreate(ctx, []*entity.DBReleaseTicketPostTask{
		{
			ReleaseTicketID: 1,
			TaskID:          12134,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		},
		{
			ReleaseTicketID: 1,
			TaskID:          4234,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		},
	})
	assert.NoError(t, err)
}

func Test_releaseTicketPostTaskDao_Create(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.Create(ctx, &entity.DBReleaseTicketPostTask{
		ReleaseTicketID: 1,
		TaskID:          12134,
		TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
	})
	assert.NoError(t, err)
}

func Test_releaseTicketPostTaskDao_FindByReleaseTicketID(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.BatchCreate(ctx, []*entity.DBReleaseTicketPostTask{
		{
			ReleaseTicketID: 12134,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		},
		{
			ReleaseTicketID: 4234,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		},
	})
	assert.NoError(t, err)

	tasks, err := dao.FindByReleaseTicketID(ctx, 12134)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(tasks))
	assert.Equal(t, uint64(12134), tasks[0].ReleaseTicketID)
}

func Test_releaseTicketPostTaskDao_FindByStatus(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.BatchCreate(ctx, []*entity.DBReleaseTicketPostTask{
		{
			ReleaseTicketID: 1,
			TaskID:          12134,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
			TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_RUNNING.String(),
		},
		{
			ReleaseTicketID: 1,
			TaskID:          4234,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
			TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String(),
		},
	})
	assert.NoError(t, err)

	tasks, err := dao.FindByStatus(ctx, []string{release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String()})
	assert.NoError(t, err)
	assert.Equal(t, 1, len(tasks))
	assert.Equal(t, uint64(4234), tasks[0].TaskID)
}

func Test_releaseTicketPostTaskDao_Update(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.Create(ctx, &entity.DBReleaseTicketPostTask{
		TaskID:          12134,
		ReleaseTicketID: 123456,
		TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_RUNNING.String(),
	})
	assert.NoError(t, err)

	err = dao.Update(ctx, 12134, entity.DBReleaseTicketPostTask{
		TaskStatus: release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String(),
	})
	assert.NoError(t, err)

	tasks, err := dao.FindByReleaseTicketID(ctx, 123456)
	assert.NoError(t, err)
	assert.Equal(t, 1, len(tasks))
	assert.Equal(t, release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String(), tasks[0].TaskStatus)
}

func Test_releaseTicketPostTaskDao_FindByProjectAndStatus(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.BatchCreate(ctx, []*entity.DBReleaseTicketPostTask{
		{
			TaskID:          12134,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
			TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_RUNNING.String(),
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bits.cd.rpc",
			BoeBaseEnv:      "prod_va",
			ReleaseTicketID: 1,
		},
		{
			TaskID:          4234,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
			TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String(),
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bits.cd.rpc",
			BoeBaseEnv:      "prod",
			ReleaseTicketID: 1,
		},
	})
	assert.NoError(t, err)

	tasks, err := dao.FindByProjectAndStatus(ctx, sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
		"bits.cd.rpc", "prod",
		[]string{release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String()})
	assert.NoError(t, err)
	assert.Equal(t, 1, len(tasks))
	assert.Equal(t, uint64(4234), tasks[0].TaskID)
}

func Test_releaseTicketPostTaskDao_FindByPostTaskID(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicketPostTask{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)
	dao := NewReleaseTicketPostTaskDao()

	err := dao.BatchCreate(ctx, []*entity.DBReleaseTicketPostTask{
		{
			TaskID:          12134,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
			TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_RUNNING.String(),
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bits.cd.rpc",
			BoeBaseEnv:      "prod_va",
			ReleaseTicketID: 1,
		},
		{
			TaskID:          4234,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
			TaskStatus:      release_ticket_sharedpb.CheckStatus_CHECK_STATUS_SUCCEEDED.String(),
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bits.cd.rpc",
			BoeBaseEnv:      "prod",
			ReleaseTicketID: 1,
		},
	})
	assert.NoError(t, err)

	task, err := dao.FindByPostTaskID(ctx, 4234)
	assert.NoError(t, err)
	assert.Equal(t, uint64(4234), task.TaskID)
}
