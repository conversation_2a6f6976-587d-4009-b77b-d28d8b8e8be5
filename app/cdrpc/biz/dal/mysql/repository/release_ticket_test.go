package repository

import (
	"context"
	"testing"
	"time"

	json "github.com/bytedance/sonic"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
)

type ReleaseTicketTestSuite struct {
	suite.Suite

	ctx context.Context
	db  *gorm.DB
}

func TestReleaseTicketRepository(t *testing.T) {
	suite.Run(t, new(ReleaseTicketTestSuite))
}

func (t *ReleaseTicketTestSuite) SetupTest() {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicket{}, &entity.DBReleaseTicketChangeItem{})

	t.ctx = mysql.ContextWithUnitTestDB(ctx, db)
	t.db = db
}

func (t *ReleaseTicketTestSuite) TestGetReleaseTicketByReleaseTicketID() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root",
	})
	releaseTicket, _ := dao.GetByReleaseTicketID(ctx, 1)

	t.Equal("root", releaseTicket.Name)
}

func (t *ReleaseTicketTestSuite) TestGetBasicReleaseTicketByReleaseTicketID() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root",
		WorkItems:       "[]",
	})
	releaseTicket, _ := dao.GetBasicByReleaseTicketID(ctx, 1)

	t.Equal("", releaseTicket.WorkItems)
}

func (t *ReleaseTicketTestSuite) TestInsertReleaseTicket() {
	ctx := t.ctx

	dao := NewReleaseTicketDao()
	rt := entity.DBReleaseTicket{
		ReleaseTicketID:    1,
		Name:               "root",
		IntegrationVersion: "233",
	}
	err := dao.Insert(ctx, &rt)

	t.Equal(nil, err)

	releaseTicket, _ := dao.GetByReleaseTicketID(ctx, 1)

	t.Equal("root", releaseTicket.Name)
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_UpdateStatus() {
	ctx := t.ctx
	db := t.db

	releaseTicket := entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Status:          "RELEASE_TICKET_STATUS_BEFORE_INTEGRATION",
	}
	db.Create(&releaseTicket)

	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx             context.Context
		releaseTicketID uint64
		status          string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "should update status properly",
			fields: fields{db},
			args: args{
				ctx:             ctx,
				releaseTicketID: releaseTicket.ReleaseTicketID,
				status:          "RELEASE_TICKET_STATUS_INTEGRATING",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func() {
			d := &releaseTicketDao{}
			if err := d.UpdateStatus(tt.args.ctx, tt.args.releaseTicketID, tt.args.status); (err != nil) != tt.wantErr {
				t.T().Errorf("releaseTicketDao.UpdateStatus() error = %v, wantErr %v", err, tt.wantErr)
			}

			var updatedReleaseTicket entity.DBReleaseTicket
			err := db.First(&updatedReleaseTicket).Error
			if err != nil {
				t.T().Errorf("releaseTicketDao.UpdateStatus() error = %v, wantErr %v", err, tt.wantErr)
			}

			if updatedReleaseTicket.Status != tt.args.status {
				t.T().Errorf(`releaseTicketDao.UpdateStatus() expects to change status from "%s" to "%s", but got "%s"`, releaseTicket.Status, tt.args.status, updatedReleaseTicket.Status)
			}
		})
	}
}

func (t *ReleaseTicketTestSuite) TestUpdateRollbackStatus() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		RollbackStatus:  "hehe",
	})

	err := dao.UpdateRollbackStatus(ctx, 1, "haha")
	t.NoError(err)

	rt, err := dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)

	t.Equal("haha", rt.RollbackStatus)
}

func (t *ReleaseTicketTestSuite) TestUpdateByField() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	id := uint64(1)
	err := db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID:  id,
		Name:             "root1",
		VarsAssignmentID: uint64(1),
		Version:          1,
	}).Error
	if !t.NoError(err) {
		return
	}

	version := int64(1)

	larkGroupIDs := []int{1, 2, 3}
	err = dao.UpdateByField(ctx, id, version, ReleaseTicketUpdateField{
		Name:   gptr.Of("123"),
		Notice: gptr.Of("notice"),
		LarkGroupIDs: gptr.Of(func() string {
			toString, _ := json.MarshalString(larkGroupIDs)
			return toString
		}()),
		VarsAssignmentID: gptr.Of(uint64(2)),
	})
	if !t.NoError(err) {
		return
	}
	rt, err := dao.GetByReleaseTicketID(ctx, id)
	if !t.NoError(err) {
		return
	}
	t.Equal("123", rt.Name)
	t.Equal("notice", rt.Notice)
	t.Equal(uint64(2), rt.VarsAssignmentID)
	t.Equal(gslice.ContainsAll(func() []int {
		var g []int
		json.UnmarshalString(rt.LarkGroupIDs, &g)
		return g
	}(), larkGroupIDs...), true)
	t.Equal(uint64(version), rt.Version)
	err = dao.UpdateByField(ctx, id, version, ReleaseTicketUpdateField{
		Version: gptr.Of(uint64(version + 1)),
	})
	if !t.NoError(err) {
		return
	}
	err = dao.UpdateByField(ctx, id, version, ReleaseTicketUpdateField{
		Version: gptr.Of(uint64(version)),
	})
	t.ErrorContains(err, "Others have updated the data. Please refresh the page to obtain the new data and try again.")
	rt, err = dao.GetByReleaseTicketID(ctx, id)
	if !t.NoError(err) {
		return
	}
	t.Equal(uint64(version+1), rt.Version)
	logs.CtxInfo(ctx, "[TestUpdateByField] done")

	logs.Flush()
}

func (t *ReleaseTicketTestSuite) TestUpdateRollbackReason() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		RollbackReason:  "hehe",
	})

	err := dao.UpdateRollbackReason(ctx, 1, "haha")
	t.NoError(err)

	rt, err := dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)

	t.Equal("haha", rt.RollbackReason)
}

func (t *ReleaseTicketTestSuite) TestListReleaseTicket() {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicket{}, &entity.DBReleaseTicketChangeItem{})
	ctx = mysql.ContextWithUnitTestDB(ctx, db)

	dao := NewReleaseTicketDao()

	releaseTicket1 := &entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "yangchengzhi",
	}
	db.Create(releaseTicket1)

	releaseTicket2 := &entity.DBReleaseTicket{
		ReleaseTicketID: 2,
		Name:            "root2",
		Creator:         "jasper",
	}
	db.Create(releaseTicket2)

	releaseTicket3 := &entity.DBReleaseTicket{
		ReleaseTicketID: 3,
		Name:            "root3",
		Creator:         "jasper",
	}
	db.Create(releaseTicket3)

	changeItem1 := &entity.DBReleaseTicketChangeItem{
		ID:              1,
		ReleaseTicketID: releaseTicket1.ReleaseTicketID,
		ProjectType:     "PROJECT_TYPE_TCE",
		ProjectUniqueID: "p.s.m1",
	}
	db.Create(changeItem1)

	changeItem2 := &entity.DBReleaseTicketChangeItem{
		ID:              2,
		ReleaseTicketID: releaseTicket2.ReleaseTicketID,
		ProjectType:     "PROJECT_TYPE_TCE",
		ProjectUniqueID: "p.s.m1",
	}
	db.Create(changeItem2)

	changeItem3 := &entity.DBReleaseTicketChangeItem{
		ID:              3,
		ReleaseTicketID: releaseTicket2.ReleaseTicketID,
		ProjectType:     "PROJECT_TYPE_WEB",
		ProjectUniqueID: "1",
	}
	db.Create(changeItem3)

	changeItem4 := &entity.DBReleaseTicketChangeItem{
		ID:              4,
		ReleaseTicketID: releaseTicket3.ReleaseTicketID,
		ProjectType:     "PROJECT_TYPE_WEB",
		ProjectUniqueID: "2",
	}
	db.Create(changeItem4)

	t.Run("should work with Where correctly", func() {
		list, i, err := dao.List(ctx, &ListConditions{
			Page:     1,
			PageSize: 5,
			Where: map[string]interface{}{
				"creator": "jasper",
			},
		})
		t.NoError(err)

		t.Equal(int64(2), i)
		t.Equal(int(2), len(list))
	})

	t.Run("should work with ProjectType correctly", func() {
		list, total, err := dao.List(ctx, &ListConditions{
			SearchProjects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectType: sharedpb.ProjectType_PROJECT_TYPE_TCE,
				},
			},
		})
		t.NoError(err)

		t.Equal(int64(2), total)
		t.Equal(2, len(list))

		expected := []*entity.DBReleaseTicket{}
		t.NoError(
			db.Model(&entity.DBReleaseTicket{}).
				Find(&expected, "release_ticket_id in (?)", []uint64{releaseTicket1.ReleaseTicketID, releaseTicket2.ReleaseTicketID}).
				Error,
		)

		t.ElementsMatch(expected, list)
	})

	t.Run("should work with multi ProjectType and ProjectUniqueID together correctly", func() {
		list, total, err := dao.List(ctx, &ListConditions{
			SearchProjects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
					ProjectUniqueId: "1",
				},
			},
		})
		t.NoError(err)

		t.Equal(int64(1), total)
		t.Equal(1, len(list))

		expected := []*entity.DBReleaseTicket{}
		t.NoError(
			db.Model(&entity.DBReleaseTicket{}).
				Find(&expected, "release_ticket_id in (?)", []uint64{releaseTicket2.ReleaseTicketID}).
				Error,
		)

		t.ElementsMatch(expected, list)
	})

	t.Run("should work with ProjectType and ProjectUniqueID together correctly", func() {
		list, total, err := dao.List(ctx, &ListConditions{
			SearchProjects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
					ProjectUniqueId: "p.s.m1",
				},
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
					ProjectUniqueId: "1",
				},
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
					ProjectUniqueId: "2",
				},
			},
		})
		t.NoError(err)

		t.Equal(int64(3), total)
		t.Equal(3, len(list))

		expected := []*entity.DBReleaseTicket{}
		t.NoError(
			db.Model(&entity.DBReleaseTicket{}).
				Find(&expected, "release_ticket_id in (?)", []uint64{releaseTicket1.ReleaseTicketID, releaseTicket2.ReleaseTicketID, releaseTicket3.ReleaseTicketID}).
				Error,
		)

		t.ElementsMatch(expected, list)
	})
}

func (t *ReleaseTicketTestSuite) TestGetByIntegrationID() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root",
		IntegrationID:   1,
	})
	releaseTicket, _ := dao.GetByIntegrationID(ctx, 1)

	t.Equal("root", releaseTicket.Name)
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_SetStatusAndActualReleaseTime() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
	})

	timeNow := time.Now()
	err := dao.SetStatusAndActualReleaseTime(ctx, 1, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String(), timeNow)
	t.NoError(err)
	rt, err := dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.Status, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String())
	t.Equal(rt.ActualReleaseTime.Unix(), timeNow.Unix())
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_UpdateIntegrationId() {
	ctx := t.ctx

	dao := NewReleaseTicketDao()
	rt := &entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
	}
	err := dao.Insert(ctx, rt)
	t.NoError(err)
	err = dao.UpdateIntegrationID(ctx, rt, 22222)
	t.NoError(err)
	rt, err = dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.IntegrationID, uint64(22222))
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_UpdateExecutionID() {
	ctx := t.ctx

	dao := NewReleaseTicketDao()
	rt := &entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
	}
	err := dao.Insert(ctx, rt)
	t.NoError(err)
	err = dao.UpdateExecutionID(ctx, rt.ReleaseTicketID, 22222)
	t.NoError(err)
	rt, err = dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.ExecutionID, uint64(22222))
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_UpdateIntegrationVersion() {
	ctx := t.ctx

	dao := NewReleaseTicketDao()
	rt := &entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
	}
	err := dao.Insert(ctx, rt)
	t.NoError(err)
	err = dao.UpdateIntegrationVersion(ctx, rt.ReleaseTicketID, "22222")
	t.NoError(err)
	rt, err = dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.IntegrationVersion, "22222")
}

func (t *ReleaseTicketTestSuite) TestGetActiveReleaseTicketByProject() {
	ctx := t.ctx
	db := t.db

	rtDao := NewReleaseTicketDao()
	db.Create([]*entity.DBReleaseTicket{
		{
			ReleaseTicketID: 1,
			Name:            "1",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String(),
			DeletedAt:       gorm.DeletedAt{},
		},
		{
			ReleaseTicketID: 2,
			Name:            "2",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASED.String(),
		},
		// hit
		{
			ReleaseTicketID: 3,
			Name:            "3",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String(),
		},
		{
			ReleaseTicketID: 4,
			Name:            "4",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
		},
	})
	db.Create([]*entity.DBReleaseTicketChangeItem{
		{
			ReleaseTicketID: 1,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
			DeployResource:  `{"type": 1, "scm_artifacts": [{"scm_id": 50629, "is_main": true, "pub_base": 2, "revision": "master", "scm_name": "ies/cdaas/testdemo7", "git_repo_name": "iesarch/ies.cdaas.testdemo7"}, {"scm_id": 667, "version": "1.0.2.499", "pub_base": 1, "scm_name": "toutiao/load"}, {"scm_id": 631, "version": "1.0.1.607", "pub_base": 1, "scm_name": "toutiao/runtime"}]}`,
		},
		{
			ReleaseTicketID: 2,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
			DeployResource:  `{"type": 1, "scm_artifacts": [{"scm_id": 50629, "is_main": true, "pub_base": 2, "revision": "master", "scm_name": "ies/cdaas/testdemo7", "git_repo_name": "iesarch/ies.cdaas.testdemo7"}, {"scm_id": 667, "version": "1.0.2.499", "pub_base": 1, "scm_name": "toutiao/load"}, {"scm_id": 631, "version": "1.0.1.607", "pub_base": 1, "scm_name": "toutiao/runtime"}]}`,
		},
		{
			ReleaseTicketID: 3,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
			DeployResource:  `{"type": 1, "scm_artifacts": [{"scm_id": 50629, "is_main": true, "pub_base": 2, "revision": "master", "scm_name": "ies/cdaas/testdemo7", "git_repo_name": "iesarch/ies.cdaas.testdemo7"}, {"scm_id": 667, "version": "1.0.2.499", "pub_base": 1, "scm_name": "toutiao/load"}, {"scm_id": 631, "version": "1.0.1.607", "pub_base": 1, "scm_name": "toutiao/runtime"}]}`,
		},
		{
			ReleaseTicketID: 3,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
			DeployResource:  `{"type": 1, "scm_artifacts": [{"scm_id": 50629, "is_main": true, "pub_base": 2, "revision": "master", "scm_name": "ies/cdaas/testdemo7", "git_repo_name": "iesarch/ies.cdaas.testdemo7"}, {"scm_id": 667, "version": "1.0.2.499", "pub_base": 1, "scm_name": "toutiao/load"}, {"scm_id": 631, "version": "1.0.1.607", "pub_base": 1, "scm_name": "toutiao/runtime"}]}`,
		},
		{
			ReleaseTicketID: 4,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "pp.ss.mm",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
			DeployResource:  `{"type": 1, "scm_artifacts": [{"scm_id": 50629, "is_main": true, "pub_base": 2, "revision": "feat-xxx", "scm_name": "ies/cdaas/testdemo7", "git_repo_name": "iesarch/ies.cdaas.testdemo7"}, {"scm_id": 667, "version": "1.0.2.499", "pub_base": 1, "scm_name": "toutiao/load"}, {"scm_id": 631, "version": "1.0.1.607", "pub_base": 1, "scm_name": "toutiao/runtime"}]}`,
		},
	})
	_, err := rtDao.GetActiveReleaseTicketByProject(ctx, &release_ticketpb.AuditProjectItem{
		ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
		ProjectUniqueId: "ies.cdaas.testdemo7",
		ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
		GitInfo: &release_ticketpb.AuditProjectGitInfo{
			GitRepoName: "iesarch/ies.cdaas.testdemo7",
			BranchName:  "master",
		},
	})
	// TODO: 只有在 ut 中查询语法报错
	t.Error(err)
}

func (t *ReleaseTicketTestSuite) TestGetInHotfixReleaseTicketByProject() {
	ctx := t.ctx
	db := t.db

	rtDao := NewReleaseTicketDao()
	db.Create([]*entity.DBReleaseTicket{
		{
			ReleaseTicketID: 1,
			Name:            "发布单集成中",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String(),
			// TODO: 排查 deleteAt 未成功
			DeletedAt:         gorm.DeletedAt{Time: time.Now()},
			ReleaseTicketType: release_ticket_sharedpb.ReleaseTicketType_RELEASE_TICKET_TYPE_HOTFIX.String(),
		},
		{
			ReleaseTicketID:   2,
			Name:              "发布单已发布",
			Status:            release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASED.String(),
			ReleaseTicketType: release_ticket_sharedpb.ReleaseTicketType_RELEASE_TICKET_TYPE_HOTFIX.String(),
		},
		// hit
		{
			ReleaseTicketID:   3,
			Name:              "发布单发布中",
			Status:            release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String(),
			ReleaseTicketType: release_ticket_sharedpb.ReleaseTicketType_RELEASE_TICKET_TYPE_HOTFIX.String(),
		},
		{
			ReleaseTicketID: 4,
			Name:            "发布单非hotfix",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
		},
	})

	db.Create([]*entity.DBReleaseTicketChangeItem{
		{
			ReleaseTicketID: 1,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		},
		{
			ReleaseTicketID: 2,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		},
		{
			ReleaseTicketID: 3,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		},
		{
			ReleaseTicketID: 3,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
		},
		{
			ReleaseTicketID: 4,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "pp.ss.mm",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
		},
	})
	db.Where("release_ticket_id = ?", 1).Delete(&entity.DBReleaseTicket{})

	hotfixItem, err := rtDao.GetInHotfixReleaseTicketByProject(ctx, &entity.DBReleaseTicketChangeItem{
		ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		ProjectUniqueID: "ies.cdaas.testdemo7",
		ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
	})
	// TODO: 只有在 ut 中查询语法报错
	t.NoError(err)
	t.Equal(hotfixItem[0].ReleaseTicketID, uint64(3))
}

func (t *ReleaseTicketTestSuite) TestGetInHotfixReleaseTicketByProjects() {
	ctx := t.ctx
	db := t.db

	rtDao := NewReleaseTicketDao()
	db.Create([]*entity.DBReleaseTicket{
		{
			ReleaseTicketID:   1,
			Name:              "发布单待集成",
			Status:            release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String(),
			ReleaseTicketType: release_ticket_sharedpb.ReleaseTicketType_RELEASE_TICKET_TYPE_HOTFIX.String(),
		},
		{
			ReleaseTicketID:   2,
			Name:              "发布单发布中",
			Status:            release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String(),
			ReleaseTicketType: release_ticket_sharedpb.ReleaseTicketType_RELEASE_TICKET_TYPE_HOTFIX.String(),
		},
	})

	db.Create([]*entity.DBReleaseTicketChangeItem{
		{
			ReleaseTicketID: 1,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		},
		{
			ReleaseTicketID: 2,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
		},
	})

	hotfixItem, err := rtDao.GetInHotfixReleaseTicketByProjects(ctx, []*entity.DBReleaseTicketChangeItem{
		{
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
		},
		{
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
			ProjectUniqueID: "ies.cdaas.testdemo7",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
		},
	})
	t.NoError(err)
	t.Equal(len(hotfixItem), 2)
}

func (t *ReleaseTicketTestSuite) TestGetReleaseTicketBasicInfoByIDs() {
	ctx := t.ctx
	db := t.db

	rtDao := NewReleaseTicketDao()
	db.Create([]*entity.DBReleaseTicket{
		{
			ReleaseTicketID: 1,
			Name:            "1",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String(),
		},
		{
			ReleaseTicketID: 2,
			Name:            "2",
			Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASED.String(),
		},
	})
	rts, err := rtDao.GetReleaseTicketBasicInfoByIDs(ctx, []uint64{1, 2, 3})
	t.NoError(err)
	t.Equal(len(rts), 2)
}

func (t *ReleaseTicketTestSuite) TestBatchGetByIntegrationIDs() {
	ctx := t.ctx
	db := t.db

	rtDao := NewReleaseTicketDao()
	db.Create([]*entity.DBReleaseTicket{
		{
			ReleaseTicketID: 1,
			IntegrationID:   1,
		},
		{
			ReleaseTicketID: 2,
			IntegrationID:   2,
		},
	})
	rts, err := rtDao.BatchGetByIntegrationIDs(ctx, []uint64{1, 2, 3})
	t.NoError(err)
	t.Equal(len(rts), 2)
}

func (t *ReleaseTicketTestSuite) TestUpdateRuntimeInfo() {
	ctx := t.ctx
	db := t.db

	rtDao := NewReleaseTicketDao()
	db.Create([]*entity.DBReleaseTicket{
		{
			ReleaseTicketID: 1,
		},
	})
	err := rtDao.UpdateRuntimeInfo(ctx, 1, "hehe")
	t.NoError(err)
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_UpdateForceCompleteStatus() {
	ctx := t.ctx
	db := t.db

	releaseTicket := entity.DBReleaseTicket{
		ReleaseTicketID:     1,
		ForceCompleteStatus: "FORCE_COMPLETE_STATUS_UNSPECIFIED",
	}
	db.Create(&releaseTicket)

	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx                 context.Context
		releaseTicketID     uint64
		forceCompleteStatus string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "should update forceCompleteStatus properly",
			fields: fields{db},
			args: args{
				ctx:                 ctx,
				releaseTicketID:     releaseTicket.ReleaseTicketID,
				forceCompleteStatus: "FORCE_COMPLETE_STATUS_RELEASED",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func() {
			d := &releaseTicketDao{}
			if err := d.UpdateForceCompleteStatus(tt.args.ctx, tt.args.releaseTicketID, tt.args.forceCompleteStatus); (err != nil) != tt.wantErr {
				t.T().Errorf("releaseTicketDao.UpdateForceCompleteStatus() error = %v, wantErr %v", err, tt.wantErr)
			}

			var updatedReleaseTicket entity.DBReleaseTicket
			err := db.First(&updatedReleaseTicket).Error
			if err != nil {
				t.T().Errorf("releaseTicketDao.UpdateForceCompleteStatus() error = %v, wantErr %v", err, tt.wantErr)
			}

			if updatedReleaseTicket.ForceCompleteStatus != tt.args.forceCompleteStatus {
				t.T().Errorf(`releaseTicketDao.UpdateForceCompleteStatus() expects to change forceCompleteStatus from "%s" to "%s", but got "%s"`, releaseTicket.Status, tt.args.forceCompleteStatus, updatedReleaseTicket.Status)
			}
		})
	}
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_SetStatusAndIntegratingAt() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_INTEGRATION.String(),
	})

	timeNow := time.Now()
	err := dao.SetStatusAndIntegratingAt(ctx, 1, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String(), timeNow)
	t.NoError(err)
	rt, err := dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.Status, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String())
	t.Equal(rt.IntegratingAt.Unix(), timeNow.Unix())
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_SetStatusAndBeforeReleaseAt() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_INTEGRATING.String(),
	})

	timeNow := time.Now()
	err := dao.SetStatusAndBeforeReleaseAt(ctx, 1, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(), timeNow)
	t.NoError(err)
	rt, err := dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.Status, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String())
	t.Equal(rt.BeforeReleaseAt.Unix(), timeNow.Unix())
}

func (t *ReleaseTicketTestSuite) Test_GetRTIDsByWorkspaceID() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		WorkspaceID:     123,
	})
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 2,
		WorkspaceID:     123,
	})
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 3,
		WorkspaceID:     234,
	})

	ids, err := dao.GetRTIDsByWorkspaceID(ctx, 123)
	t.NoError(err)
	t.Equal(2, len(ids))
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_UpdatePlanTime() {
	ctx := t.ctx

	dao := NewReleaseTicketDao()
	rt := &entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE.String(),
	}

	err := dao.Insert(ctx, rt)
	t.NoError(err)

	planT := time.Now()
	err = dao.UpdatePlanTime(ctx, 1, planT, planT, planT, planT)
	t.NoError(err)

	rt, err = dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)

	t.True(planT.Equal(rt.StartIntegrationAt))
	t.True(planT.Equal(rt.EndIntegrationAt))
	t.True(planT.Equal(rt.TestAt))
	t.True(planT.Equal(rt.ReleaseAt))
}

func (t *ReleaseTicketTestSuite) Test_Delete() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		WorkspaceID:     123,
	})
	err := dao.Delete(ctx, 1)
	t.NoError(err)
}

func (t *ReleaseTicketTestSuite) Test_releaseTicketDao_SetStatusAndCancelReason() {
	ctx := t.ctx
	db := t.db

	dao := NewReleaseTicketDao()
	db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		Name:            "root1",
		Creator:         "xiaxiangshun.xxs",
		Status:          release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_INTEGRATION.String(),
	})

	reason := &release_ticketpb.CancelRtReason{
		CancelReasonType: 1,
		CancelReason:     "test",
	}
	reasonStr, _ := json.MarshalString(reason)
	err := dao.SetStatusAndCancelReason(ctx, 1, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_CANCELED.String(), reasonStr)
	t.NoError(err)
	rt, err := dao.GetByReleaseTicketID(ctx, 1)
	t.NoError(err)
	t.Equal(rt.Status, release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_CANCELED.String())
	t.Equal(rt.CancelReason, "{\"cancel_reason_type\":1,\"cancel_reason\":\"test\"}")
}
