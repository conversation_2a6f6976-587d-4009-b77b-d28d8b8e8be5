package repository

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"github.com/stretchr/testify/assert"
)

func TestBatchCreateAssociateWorkflow(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkflowAssociation{})
	mysql.DefaultDB = db
	dao := NewAssociateWorkflowDao()
	dbWorkflowAssociations := make([]*entity.DBWorkflowAssociation, 0)
	dbWorkflowAssociations = append(dbWorkflowAssociations, &entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	}, &entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 789,
		Creator:             "Li Hua",
	})

	err := dao.BatchCreate(ctx, dbWorkflowAssociations)
	assert.NoError(t, err)
	dbWorkflowAssociations, err = dao.GetByWorkflowID(ctx, 123)
	assert.NoError(t, err)
	assert.Len(t, dbWorkflowAssociations, 2)
}

func TestGetByWorkflowID(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkflowAssociation{})
	mysql.DefaultDB = db
	dao := NewAssociateWorkflowDao()
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 789,
		Creator:             "Li Hua",
	})

	dbWorkflowAssociations, err := dao.GetByWorkflowID(ctx, 123)
	assert.NoError(t, err)
	assert.Len(t, dbWorkflowAssociations, 2)
}

func TestGetByAssociateWorkflowID(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkflowAssociation{})
	mysql.DefaultDB = db
	dao := NewAssociateWorkflowDao()
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          235,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})

	dbWorkflowAssociations, err := dao.GetByAssociateWorkflowID(ctx, 567)
	assert.NoError(t, err)
	assert.Len(t, dbWorkflowAssociations, 2)
}

func TestGetByAssociateWorkflowIDs(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkflowAssociation{})
	mysql.DefaultDB = db
	dao := NewAssociateWorkflowDao()
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          235,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          247,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})

	dbWorkflowAssociations, err := dao.GetByWorkflowIDs(ctx, []uint64{123, 11222, 247})
	assert.NoError(t, err)
	assert.Len(t, dbWorkflowAssociations, 2)
}

func TestDeleteByWorkflowID(t *testing.T) {
	ctx := context.Background()
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkflowAssociation{})
	mysql.DefaultDB = db
	dao := NewAssociateWorkflowDao()
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 567,
		Creator:             "Li Hua",
	})
	db.Create(&entity.DBWorkflowAssociation{
		WorkflowID:          123,
		AssociateWorkflowID: 789,
		Creator:             "Li Hua",
	})

	err := dao.Delete(ctx, 123)
	assert.NoError(t, err)
	dbWorkflowAssociations, err := dao.GetByWorkflowID(ctx, 123)
	assert.NoError(t, err)
	assert.Len(t, dbWorkflowAssociations, 0)
}
