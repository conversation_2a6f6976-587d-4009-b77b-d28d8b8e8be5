package entity

import (
	"time"

	"gorm.io/gorm"
)

type DBWorkspaceChangeItemDependency struct {
	ID              uint64         `json:"id" gorm:"primaryKey"`
	WorkspaceID     uint64         `json:"workspace_id" gorm:"column:workspace_id"`
	ProjectType     string         `json:"project_type" gorm:"column:project_type"`
	ProjectUniqueID string         `json:"project_unique_id" gorm:"column:project_unique_id"`
	DependencyItems []byte         `json:"dependency_items" gorm:"column:dependency_items"`
	ControlPlane    string         `json:"control_plane" gorm:"column:control_plane"`
	CreatedAt       time.Time      `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt       time.Time      `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt       gorm.DeletedAt `sql:"index" gorm:"column:deleted_at"`       // 删除时间
}

func (t *DBWorkspaceChangeItemDependency) TableName() string {
	return "hagrid_workspace_change_item_dependency"
}
