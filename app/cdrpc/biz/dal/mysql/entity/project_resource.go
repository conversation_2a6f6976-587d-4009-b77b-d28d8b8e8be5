package entity

import "time"

type DBProjectResource struct {
	ID          uint64    `json:"id" gorm:"primaryKey"`
	Project<PERSON>ey  string    `json:"project_key" gorm:"column:project_key"`
	Environment string    `json:"environment" gorm:"column:environment"`
	CreatedAt   time.Time `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt   time.Time `json:"updated_at" gorm:"column:updated_at"` // 更新时间
}

func (t *DBProjectResource) TableName() string {
	return "hagrid_project_resource"
}
