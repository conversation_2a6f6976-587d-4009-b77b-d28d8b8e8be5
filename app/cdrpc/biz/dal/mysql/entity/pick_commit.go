package entity

import (
	"time"

	"gorm.io/gorm"
)

type DBPickCommit struct {
	ID         uint64         `json:"id" gorm:"primaryKey"`
	GitName    string         `json:"git_name" gorm:"column:git_name"`
	FromCommit string         `json:"from_commit" gorm:"column:from_commit"`
	NewCommit  string         `json:"new_commit" gorm:"column:new_commit"`
	ActionType string         `json:"action_type" gorm:"column:action_type"`
	Creator    string         `json:"creator" gorm:"column:creator"`
	Updater    string         `json:"updater" gorm:"column:updater"`
	CreatedAt  time.Time      `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt  time.Time      `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt  gorm.DeletedAt `sql:"index" gorm:"column:deleted_at"`       // 删除时间
}

func (t *DBPickCommit) TableName() string {
	return "hagrid_pick_commit"
}
