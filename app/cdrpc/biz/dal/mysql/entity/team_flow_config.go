package entity

import (
	"time"

	"gorm.io/gorm"
)

type DBTeamFlowConfig struct {
	ID                               uint64         `json:"id" gorm:"primaryKey"`
	TeamFlowID                       uint64         `json:"team_flow_id" gorm:"column:team_flow_id"`
	TeamFlowType                     string         `json:"team_flow_type" gorm:"column:team_flow_type"`
	Name                             string         `json:"name" gorm:"column:name"`
	NameI18N                         string         `json:"name_i18n" gorm:"column:name_i18n"`
	Description                      string         `json:"description" gorm:"column:description"`
	DescriptionI18N                  string         `json:"description_i18n" gorm:"column:description_i18n"`
	WorkspaceID                      uint64         `json:"workspace_id" gorm:"column:workspace_id"`
	RtWorkflowID                     uint64         `json:"rt_workflow_id" gorm:"column:rt_workflow_id"` //废弃，勿用
	RtWorkflowIDs                    string         `json:"rt_workflow_ids" gorm:"column:rt_workflow_ids"`
	RtWorkflowUniqKey                string         `json:"rt_workflow_uniq_key" gorm:"column:rt_workflow_uniq_key"`
	DevWorkflowIDs                   string         `json:"dev_workflow_ids" gorm:"column:dev_workflow_ids"`
	DevWorkflowUniqKey               string         `json:"dev_workflow_uniq_key" gorm:"column:dev_workflow_uniq_key"`
	FlowConfig                       string         `json:"flow_config" gorm:"column:flow_config"`
	BranchingModelConfigInheritSpace int            `json:"branching_model_config_inherit_space" gorm:"branching_model_config_inherit_space"`
	BranchingModelConfig             string         `json:"branching_model_config" gorm:"column:branching_model_config"`
	IncrementalDeliveryRT            string         `json:"incremental_delivery_rt" gorm:"column:incremental_delivery_rt"`
	IncrementalDeliveryDevTask       string         `json:"incremental_delivery_dev_task" gorm:"column:incremental_delivery_dev_task"`
	Enabled                          bool           `json:"enabled" gorm:"column:enabled"`
	Version                          uint64         `json:"version" gorm:"column:version"`
	Creator                          string         `json:"creator" gorm:"column:creator"`
	Updater                          string         `json:"updater" gorm:"column:updater"`
	CreatedAt                        time.Time      `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt                        time.Time      `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt                        gorm.DeletedAt `sql:"index" gorm:"column:deleted_at"`       // 删除时间
}

func (t *DBTeamFlowConfig) TableName() string {
	return "hagrid_team_flow_config"
}

func (t *DBTeamFlowConfig) BeforeCreate(tx *gorm.DB) (err error) {
	if t.DevWorkflowIDs == "" {
		t.DevWorkflowIDs = "[]"
	}

	if t.RtWorkflowIDs == "" {
		t.RtWorkflowIDs = "[]"
	}

	if t.FlowConfig == "" {
		t.FlowConfig = "{}"
	}

	if t.BranchingModelConfig == "" {
		t.BranchingModelConfig = "{}"
	}

	if t.IncrementalDeliveryDevTask == "" {
		t.IncrementalDeliveryDevTask = "{}"
	}

	if t.IncrementalDeliveryRT == "" {
		t.IncrementalDeliveryRT = "{}"
	}

	return nil
}

func (t *DBTeamFlowConfig) AfterFind(tx *gorm.DB) (err error) {
	if t.IncrementalDeliveryDevTask == "" {
		t.IncrementalDeliveryDevTask = "{}"
	}

	if t.IncrementalDeliveryRT == "" {
		t.IncrementalDeliveryRT = "{}"
	}

	return nil
}
