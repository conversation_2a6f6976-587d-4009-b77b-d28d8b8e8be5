package entity

import (
	"time"

	"gorm.io/gorm"
)

type DBStagePipelineRunRecord struct {
	ID                   uint64         `json:"id" gorm:"primaryKey"`
	PipelineID           uint64         `json:"pipeline_id" gorm:"column:pipeline_id"`
	BuildID              uint64         `json:"build_id" gorm:"column:build_id"`
	SelectedControlPlane string         `json:"selected_control_plane" gorm:"selected_control_plane"`
	SelectedProject      string         `json:"selected_project" gorm:"selected_project"`
	RunParams            string         `json:"run_params" gorm:"run_params"`
	CreatedAt            time.Time      `json:"created_at" gorm:"column:created_at"` // 创建时间
	UpdatedAt            time.Time      `json:"updated_at" gorm:"column:updated_at"` // 更新时间
	DeletedAt            gorm.DeletedAt `sql:"index" gorm:"column:deleted_at"`       // 删除时间
}

func (t *DBStagePipelineRunRecord) TableName() string {
	return "hagrid_stage_pipeline_run_record"
}
