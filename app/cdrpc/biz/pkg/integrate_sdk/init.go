package integrate_sdk

import (
	"code.byted.org/kite/kitex/byted/transmeta"
	"code.byted.org/kite/kitex/client"
	"code.byted.org/overpass/bits_integration_multi/rpc/bits_integration_multi"
	"code.byted.org/overpass/bytedance_bits_dev/rpc/bytedance_bits_dev"
	"code.byted.org/overpass/bytedance_bits_git_server/rpc/bytedance_bits_git_server"

	codeNextCode "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/git_server/nextcodeservice"
)

var codebaseNextCodeClient codeNextCode.Client

func MustInitialize() {
	transmeta.SetReadBizStatusErr(true)
	bits_integration_multi.DefaultClient().Conf().EnableErrHandler = false

	codebaseNextCodeClient = codeNextCode.MustNewClient("bytedance.bits.git_server", newRPCOptions()...)

	bytedance_bits_git_server.DefaultClient().Conf().EnableErrHandler = false

	bytedance_bits_dev.DefaultClient().Conf().EnableErrHandler = false
}

func newRPCOptions() []client.Option {
	var options []client.Option
	return options
}
