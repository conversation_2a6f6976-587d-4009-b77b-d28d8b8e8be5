package pipeline_rpc

import (
	"sync"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb/pipelineservice"
	"code.byted.org/devinfra/hagrid/pbgen/rpc/bits/pipelinerpc"
)

var cli pipelineservice.Client
var once sync.Once

func Init() {
	once.Do(func() {
		cli = pipelineservice.MustNewClient(pipelinerpc.PSM)
	})
}

func GetClient() pipelineservice.Client {
	if cli == nil {
		Init()
	}
	return cli
}
