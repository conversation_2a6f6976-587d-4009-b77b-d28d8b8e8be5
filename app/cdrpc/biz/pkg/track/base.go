package track

// psm
const (
	CDRPCPSM = "bits.cd.rpc"
)

// 异常重试机制 API 名
const ()

// 场景
const (
	// 接口
	RtInterfaceSubmitReleaseTicket = "rt_interface_SubmitReleaseTicket" // SubmitReleaseTicket 接口
	RtInterfacePrefix              = "rt_interface"                     //接口 前缀

	// 功能链路
	RtFinishPipelineStage = "rt_finish_pipeline_stage" // 完成非发布阶段
	RtFinishDeployStage   = "rt_finish_deploy_stage"   // 完成发布阶段
	RtFinishArchiveStage  = "rt_finish_archive_stage"  // 完成归档阶段
	RtChangeProjects      = "rt_change_projects"       // 更新项目

	RtStageCreatePipeline = "rt_stage_create_pipeline" // 创建流水线
	RtStageRunPipeline    = "rt_stage_run_pipeline"    // 运行流水线

	RtDeployStageAccessTask = "rt_deploy_stage_access_task" // 发布阶段准入任务

	// tea才用的
	RtUpdateDeployStrategy = "rt_update_deploy_strategy" // 更新部署参数
	RtPreCheckPplRun       = "rt_pre_check_ppl_run"      // 流水线运行前校验
	RtReloadPpl            = "rt_reload_ppl"             // 更新流水线
	RtCancelPpl            = "rt_cancel_ppl"             // 取消流水线
	RtFinishIntegration    = "rt_finish_integration"     // 结束集成
)

// 独特 id 类型
const (
	RELEASE_TICKET                        = "RT"             // 发布单
	RELEASE_TICKET_STAGE                  = "RT_STAGE"       // 发布单阶段, 用于功能链路下钻数据
	RELEASE_TICKET_STAGE_BY_CONTROL_PLANE = "RT_STAGE_BY_CP" // 发布单控制面阶段
	RELEASE_TICKET_WORKSPACE              = "RT_WORKSPACE"   // 发布单工作空间，仅用在创建发布单和初始化空间

	RELEASE_TICKET_WORKFLOW  = "RT_WORKFLOW"  // 仅有发布单模版，仅用在 curd workflow
	RELEASE_TICKET_TEAM_FLOW = "RT_TEAM_FLOW" // 仅有研发流程模版，仅用在 curd team flow

	DEVTASK = "DEVTASK" // 开发任务
)

type UpdatePathFrom struct {
	PathFrom uint64 `json:"path_from"` // 1: 发布单创建；2: 发布单编辑；3: 开发任务合入
}

type TriggerType struct {
	IsAuto bool `json:"is_auto"` // 是否自动触发
}

type ComputeType int

const (
	Success ComputeType = iota + 1
	Fail
)

type TeaTriggerType int

const (
	Auto TeaTriggerType = iota
	Manual
)
