// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/goofy_deploy/base (interfaces: GoofyDeployControlPlaneSvc)

// Package mock_base is a generated GoMock package.
package mock_base

import (
	context "context"
	reflect "reflect"

	paaspb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	release_ticketpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	goofy_deploy "code.byted.org/iesarch/paas_sdk/goofy_deploy"
	gomock "github.com/golang/mock/gomock"
)

// MockGoofyDeployControlPlaneSvc is a mock of GoofyDeployControlPlaneSvc interface.
type MockGoofyDeployControlPlaneSvc struct {
	ctrl     *gomock.Controller
	recorder *MockGoofyDeployControlPlaneSvcMockRecorder
}

// MockGoofyDeployControlPlaneSvcMockRecorder is the mock recorder for MockGoofyDeployControlPlaneSvc.
type MockGoofyDeployControlPlaneSvcMockRecorder struct {
	mock *MockGoofyDeployControlPlaneSvc
}

// NewMockGoofyDeployControlPlaneSvc creates a new mock instance.
func NewMockGoofyDeployControlPlaneSvc(ctrl *gomock.Controller) *MockGoofyDeployControlPlaneSvc {
	mock := &MockGoofyDeployControlPlaneSvc{ctrl: ctrl}
	mock.recorder = &MockGoofyDeployControlPlaneSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGoofyDeployControlPlaneSvc) EXPECT() *MockGoofyDeployControlPlaneSvcMockRecorder {
	return m.recorder
}

// GetWebChannels mocks base method.
func (m *MockGoofyDeployControlPlaneSvc) GetWebChannels(arg0 context.Context, arg1 *release_ticketpb.GetWebChannelsReq, arg2 string) ([]*paaspb.ChannelBasicInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWebChannels", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*paaspb.ChannelBasicInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWebChannels indicates an expected call of GetWebChannels.
func (mr *MockGoofyDeployControlPlaneSvcMockRecorder) GetWebChannels(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWebChannels", reflect.TypeOf((*MockGoofyDeployControlPlaneSvc)(nil).GetWebChannels), arg0, arg1, arg2)
}

// GetWebRegions mocks base method.
func (m *MockGoofyDeployControlPlaneSvc) GetWebRegions(arg0 context.Context, arg1, arg2 string) ([]*goofy_deploy.DeployUnitInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWebRegions", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*goofy_deploy.DeployUnitInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetWebRegions indicates an expected call of GetWebRegions.
func (mr *MockGoofyDeployControlPlaneSvcMockRecorder) GetWebRegions(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWebRegions", reflect.TypeOf((*MockGoofyDeployControlPlaneSvc)(nil).GetWebRegions), arg0, arg1, arg2)
}
