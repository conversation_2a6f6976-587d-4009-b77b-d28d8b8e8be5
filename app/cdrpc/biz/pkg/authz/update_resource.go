package authz

import (
	"context"
	"strconv"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pkg/brn"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/authz"
	"code.byted.org/gopkg/logs"
)

type UpdateResourceReq struct {
	// 待创建资源对应的实体ID
	InstanceID uint64
	// 待创建资源对应的实体名称
	InstanceName string
	// 待创建资源类型
	ResourceType string
	// 待创建资源的父资源ID（父资源存在时填写，空间等一级资源无需制定）
	ParentResourceID uint64
	// 待创建资源的父资源类型（父资源存在时填写，空间等一级资源无需制定）
	ParentResourceType string
	// 创建人
	Creator string
}

// 对接 IAM 更新资源接口 https://cloud.bytedance.net/bam/rd/bits.authz.rpc/api_doc/show_doc?endpoint_id=1022725&cluster=default
func (m *authzManager) UpdateResource(ctx context.Context, req *UpdateResourceReq) error {
	logs.CtxInfo(ctx, "begin to update resource:%v, instanceID:%v for auth.", req.ResourceType, req.InstanceID)

	var parentBrn string
	if req.ParentResourceType != "" && req.ParentResourceID > 0 {
		parentBrn = brn.GenResourceBRN(req.ParentResourceType, strconv.FormatUint(req.ParentResourceID, 10)).String()
	}

	_, err := m.iamAuthzClient.UpdateResource(ctx, &authz.UpdateResourceRequest{
		Resource: &authz.Resource{
			Brn:       brn.GenResourceBRN(req.ResourceType, strconv.FormatUint(req.InstanceID, 10)).String(),
			Name:      req.InstanceName,
			Creator:   brn.GenPrincipalBRN(brn.TypeServiceAccount, "bytecycle").String(),
			ParentBrn: parentBrn,
		},
	})
	if err != nil {
		logs.CtxError(ctx, "update resource:%v, instanceID:%v for auth failed, err:%v", req.ResourceType, req.InstanceID, err)
		return bits_err.THIRDPARTYIAM.ErrCallIAMAPI.PassThrough(err)
	}

	return nil
}
