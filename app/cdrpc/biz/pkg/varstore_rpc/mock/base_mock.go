// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/varstore_rpc (interfaces: VarStoreSvc)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	gomock "github.com/golang/mock/gomock"
)

// MockVarStoreSvc is a mock of VarStoreSvc interface.
type MockVarStoreSvc struct {
	ctrl     *gomock.Controller
	recorder *MockVarStoreSvcMockRecorder
}

// MockVarStoreSvcMockRecorder is the mock recorder for MockVarStoreSvc.
type MockVarStoreSvcMockRecorder struct {
	mock *MockVarStoreSvc
}

// NewMockVarStoreSvc creates a new mock instance.
func NewMockVarStoreSvc(ctrl *gomock.Controller) *MockVarStoreSvc {
	mock := &MockVarStoreSvc{ctrl: ctrl}
	mock.recorder = &MockVarStoreSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVarStoreSvc) EXPECT() *MockVarStoreSvcMockRecorder {
	return m.recorder
}

// CreateVarsAssignment mocks base method.
func (m *MockVarStoreSvc) CreateVarsAssignment(arg0 context.Context, arg1 []*sharedpb.Variable, arg2 uint64, arg3 string) (uint64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateVarsAssignment", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(uint64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVarsAssignment indicates an expected call of CreateVarsAssignment.
func (mr *MockVarStoreSvcMockRecorder) CreateVarsAssignment(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVarsAssignment", reflect.TypeOf((*MockVarStoreSvc)(nil).CreateVarsAssignment), arg0, arg1, arg2, arg3)
}
