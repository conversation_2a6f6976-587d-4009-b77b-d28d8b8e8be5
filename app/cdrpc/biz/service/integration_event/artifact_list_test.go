package integration_event

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"code.byted.org/bits/monkey"
	scmmock "code.byted.org/canal/provider/scm/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	repositorymock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	mock_authz "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/authz/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/execution_engine"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tce"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/track"
	mock_branch_manager "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/branch_manager/mock"
	mock_branching_model "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/branching_model/mock"
	cd_fgmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/cd_fg/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/cd_gray"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	changeitemmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item/mock"
	mock_config "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/config/mock"
	intgrationmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/integration_event/mock"
	releaseticketmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket/mock"
	trigger_mgr_mock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket_trigger_manager/mock"
	rt_change_item_managermock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_change_item_manager/mock"
	rt_notification_mock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_notification/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_operation_reporter"
	mock_rt_stage "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_stage/mock"
	rt_stage_pipeline_manager_mock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_stage_pipeline_manager/mock"
	stageMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/stage/mock"
	stagecheckmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/stage_check/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/projectpb"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	scmModel "code.byted.org/iesarch/paas_sdk/scm"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bits/integration/multi"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bytedance/bits/dev"
)

type ArtifactListSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
	db     *gorm.DB
	ctrl   *gomock.Controller
}

func (t *ArtifactListSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	t.ctrl = gomock.NewController(t.T())
	db := testfactory.NewUnitTestDB(context.Background(), &entity.DBReleaseTicket{}, &entity.DBReleaseTicketChangeItem{}, &entity.DBProject{}, &entity.DBStage{}, &entity.DBCheckRecord{})

	t.db = db
	mysql.DefaultDB = db
	tce.TCESDK = testfactory.NewMockTceSdk()
	execution_engine.MustInit()
}

func (t *ArtifactListSuite) TearDownTest() {
	t.cancel()
	monkey.UnpatchAll()
}

func TestArtifactList(t *testing.T) {
	suite.Run(t, new(ArtifactListSuite))
}

func (t *ArtifactListSuite) TestOnArtifactListUpdateHandler() {
	t.ctx = cdauth.WithUsername(t.ctx, "a")
	ctrl := gomock.NewController(t.T())

	releaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctrl)
	releaseApproverDao := repositorymock.NewMockReleaseApproverDao(ctrl)
	rtChangeItemManager := rt_change_item_managermock.NewMockRtChangeItemManager(ctrl)
	releaseticketSvc := releaseticketmock.NewMockReleaseTicketSvc(ctrl)
	stageSvc := stageMock.NewMockStageSvc(ctrl)
	mockAuthzManager := mock_authz.NewMockAuthzManager(ctrl)
	stageDao := repositorymock.NewMockStageDao(ctrl)
	stagecheckSvc := stagecheckmock.NewMockStageCheckSvc(ctrl)
	scmSvc := scmmock.NewMockService(ctrl)
	workflowConfigHelper := mock_config.NewMockWorkflowConfigHelper(ctrl)
	workflowConfigHelper.EXPECT().DbToPb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	baseStageManager := mock_rt_stage.NewMockBaseStageManager(ctrl)
	triggerMgr := trigger_mgr_mock.NewMockReleaseTicketPipelineTriggerManager(t.ctrl)
	triggerMgr.EXPECT().RegisterTrigger(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	releaseticketSvc.EXPECT().SaveRTEngineTaskOperator(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	t.Run("payload failed", func() {
		svc := &ArtifactListEventService{
			trackApi:   track.New(),
			triggerMgr: triggerMgr,
			cdGray:     cd_gray.NewCdGraySvc(),
		}
		defer monkey.PatchInstanceMethod(reflect.TypeOf(svc.trackApi), "EmitExceptionRequest", func(ctx context.Context, downstreamPsm, downstreamApi, requestBody, scenario, businessId, errorMessage string, errorCode int64) {
			return
		}).UnPatch()
		err := svc.OnArtifactListUpdateHandler(t.ctx, &IntegrationEventPayload{})
		t.Error(err)
	})

	t.Run("no release ticket", func() {
		releaseTicketDao.EXPECT().GetByIntegrationID(gomock.Any(), gomock.Any()).Return(nil, errors.New("mock not found"))
		eventMsg := &IntegrationEventPayload{
			IntegrationId: 1,
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
			},
			EventTimestamp: 10,
		}
		svc := &ArtifactListEventService{
			releaseTicketDao:     releaseTicketDao,
			workflowConfigHelper: workflowConfigHelper,
			trackApi:             track.New(),
			cdGray:               cd_gray.NewCdGraySvc(),
		}
		defer monkey.PatchInstanceMethod(reflect.TypeOf(svc.trackApi), "EmitExceptionRequest", func(ctx context.Context, downstreamPsm, downstreamApi, requestBody, scenario, businessId, errorMessage string, errorCode int64) {
			return
		}).UnPatch()
		err := svc.OnArtifactListUpdateHandler(t.ctx, eventMsg)
		t.Error(err)
	})

	t.Run("是集成区锁定事件，且判定无需处理", func() {
		releaseTicketDao.EXPECT().GetByIntegrationID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		releaseApproverDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketReleaseApprover{
			{
				Username: "liu",
			},
		}, nil)
		rtChangeItemManager.EXPECT().SetIntegrationZoneStagedStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "IsMsgNeedHandle", func(ctx context.Context, eventMsg *IntegrationEventPayload, rt *entity.DBReleaseTicket) (bool, error) {
			return false, nil
		}).UnPatch()
		eventMsg := &IntegrationEventPayload{
			IntegrationId: 1,
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
				Version:     "1",
			},
			EventTimestamp: 10,
		}
		svc := &ArtifactListEventService{
			releaseTicketDao:     releaseTicketDao,
			releaseApproverDao:   releaseApproverDao,
			rtChangeItemManager:  rtChangeItemManager,
			workflowConfigHelper: workflowConfigHelper,
			releaseTicketSvc:     releaseticketSvc,
			cdGray:               cd_gray.NewCdGraySvc(),
		}
		err := svc.OnArtifactListUpdateHandler(t.ctx, eventMsg)
		t.NoError(err)
	})

	t.Run("是普通合入事件，需处理，按最大处理任务走", func() {
		t.T().SkipNow()

		releaseTicketDao.EXPECT().GetByIntegrationID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		releaseApproverDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketReleaseApprover{
			{
				Username: "liu",
			},
		}, nil)
		scmSvc.EXPECT().GetMultiRepoMergeBuildMeta(gomock.Any(), gomock.Any()).Return(map[int64]*scmModel.MergeBuildMeta{}, nil)
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "IsMsgNeedHandle", func(ctx context.Context, eventMsg *IntegrationEventPayload, rt *entity.DBReleaseTicket) (bool, error) {
			return true, nil
		}).UnPatch()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "UpdateItemsByArtifacts", func(ctx context.Context, rt *entity.DBReleaseTicket, eventMsg *IntegrationEventPayload, time int64) (
			hasItemAdded bool, hasItemRemoved bool, hasItemDependencyChanged bool, batchChangeItems *change_item.BatchChangeItems, err error) {
			return true, true, true, &change_item.BatchChangeItems{}, nil
		}).UnPatch()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "ReloadPipelinesWhenProjectUpdate", func(
			ctx context.Context,
			rt *entity.DBReleaseTicket,
			username string,
			batchChangeItems *change_item.BatchChangeItems,
			hasItemDependencyChanged bool,
			rebuildStages []*entity.DBStage) (err error) {
			return nil
		}).UnPatch()
		stageSvc.EXPECT().GetCanRebuildPipelineStagesByRtID(gomock.Any(), gomock.Any()).Return([]*entity.DBStage{{}}, nil)
		releaseTicketStagePipelineManager := rt_stage_pipeline_manager_mock.NewMockReleaseTicketStagePipelineManager(ctrl)
		releaseTicketStagePipelineManager.EXPECT().ProduceArtifactListUpdateChanges(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "RefreshRTProjectOwnerAuthorization", func(ctx context.Context, rt *entity.DBReleaseTicket) error {
			return nil
		}).UnPatch()
		mockAuthzManager.EXPECT().SyncPipelineRole(gomock.Any(), gomock.Any()).Return()
		monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "CacheProjectDefaultBranchByRedis", func(ctx context.Context, eventMsg *IntegrationEventPayload, rt *entity.DBReleaseTicket) error {
			return nil
		})
		stageDao.EXPECT().GetStagesByReleaseTicketID(gomock.Any(), gomock.Any()).Return([]*entity.DBStage{
			{
				NodeType:    workflowpb.NodeType_NODE_TYPE_PIPELINE_STAGE.String(),
				StageStatus: release_ticketpb.StageStatus_STAGE_STATUS_RUNNING.String(),
			},
		}, nil)
		stagecheckSvc.EXPECT().ShouldRetryStageCheckPointWhenArtifactChange(gomock.Any()).Return(true)
		baseStageManager.EXPECT().CreateGatekeeperCheck(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		eventMsg := &IntegrationEventPayload{
			IntegrationId: 1,
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
			},
			EventTimestamp: 10,
		}
		svc := &ArtifactListEventService{
			releaseTicketDao:                  releaseTicketDao,
			rtChangeItemManager:               rtChangeItemManager,
			stageSvc:                          stageSvc,
			releaseTicketSvc:                  releaseticketSvc,
			stageDao:                          stageDao,
			stageCheckSvc:                     stagecheckSvc,
			scmSvc:                            scmSvc,
			authzManager:                      mockAuthzManager,
			workflowConfigHelper:              workflowConfigHelper,
			baseStageManager:                  baseStageManager,
			releaseApproverDao:                releaseApproverDao,
			triggerMgr:                        triggerMgr,
			releaseTicketStagePipelineManager: releaseTicketStagePipelineManager,
			cdGray:                            cd_gray.NewCdGraySvc(),
		}
		err := svc.OnArtifactListUpdateHandler(t.ctx, eventMsg)
		t.NoError(err)
	})
}

func (t *ArtifactListSuite) TestUpdateItemsByArtifacts() {
	t.ctx = cdauth.WithUsername(t.ctx, "a")
	ctrl := gomock.NewController(t.T())

	artifactLock := intgrationmock.NewMockArtifactTimestampLock(ctrl)
	releaseTicketChangeItemDao := repositorymock.NewMockReleaseTicketChangeItemDao(ctrl)
	releaseticketSvc := releaseticketmock.NewMockReleaseTicketSvc(ctrl)
	rtChangeItemManager := rt_change_item_managermock.NewMockRtChangeItemManager(ctrl)
	branchManager := mock_branch_manager.NewMockBranchManager(ctrl)
	rtNotification := rt_notification_mock.NewMockRtNotification(ctrl)

	t.Run("no dev task", func() {
		eventMsg := &IntegrationEventPayload{
			IntegrationId: 1,
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
			},
			EventTimestamp: 10,
		}
		svc := &ArtifactListEventService{}
		hasItemAdded, hasItemRemoved, hasItemDependencyChanged, batchChangeItems, err := svc.UpdateItemsByArtifacts(t.ctx, &entity.DBReleaseTicket{}, eventMsg, 0)
		t.Equal(hasItemAdded, false)
		t.Equal(hasItemRemoved, false)
		t.Equal(hasItemDependencyChanged, false)
		t.Nil(batchChangeItems)
		t.NoError(err)
	})

	t.Run("has dev task", func() {
		rt := &entity.DBReleaseTicket{
			ControlPlanes: `["CONTROL_PLANE_US_TTP"]`,
		}
		eventMsg := &IntegrationEventPayload{
			IntegrationId: 1,
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
				DevList:     []*multi.DevInfo{{}},
			},
			EventTimestamp: 10,
		}
		artifactLock.EXPECT().TryHoldTheLock(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
		defer monkey.Patch(mysql.WrapCtxWithTransaction, func(ctx context.Context) (context.Context, error) {
			return t.ctx, nil
		}).UnPatch()
		releaseTicketChangeItemDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(nil, nil)
		rtChangeItemManager.EXPECT().UpdateRtChangItems(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&change_item.ChangeItemDiffs{HasItemAdded: true, HasItemRemoved: true, HasItemDependencyChanged: true}, &change_item.BatchChangeItems{}, nil)
		defer monkey.Patch(mysql.CommitTransaction, func(ctx context.Context) error {
			return nil
		}).UnPatch()
		releaseTicketChangeItemDao.EXPECT().RemoveDuplicateChangeItems(gomock.Any(), gomock.Any()).Return(nil)
		monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "SendDeleteChangeItemMsg", func(
			ctx context.Context, rt *entity.DBReleaseTicket, rtChangeItems []*entity.DBReleaseTicketChangeItem, eventMsg *IntegrationEventPayload) {
			return
		})
		rtNotification.EXPECT().JoinOwnersToRTGroups(gomock.Any(), gomock.Any()).Return().AnyTimes()
		workflowConfigHelper := mock_config.NewMockWorkflowConfigHelper(ctrl)
		workflowConfigHelper.EXPECT().DbToPb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

		branchManager.EXPECT().BatchRefreshRtReposInDB(gomock.Any(), gomock.Any()).Return(nil)

		svc := &ArtifactListEventService{
			artifactTimestampLock:      artifactLock,
			releaseTicketChangeItemDao: releaseTicketChangeItemDao,
			releaseTicketSvc:           releaseticketSvc,
			workflowConfigHelper:       workflowConfigHelper,
			branchManager:              branchManager,
			rtNotification:             rtNotification,
			rtChangeItemManager:        rtChangeItemManager,
		}
		hasItemAdded, hasItemRemoved, hasItemDependencyChanged, batchChangeItems, err := svc.UpdateItemsByArtifacts(t.ctx, rt, eventMsg, 0)
		t.Equal(hasItemAdded, true)
		t.Equal(hasItemRemoved, true)
		t.Equal(hasItemDependencyChanged, true)
		t.NotNil(batchChangeItems)
		t.NoError(err)
	})
}

func (t *ArtifactListSuite) TestArtifactListEventService_updateWorkItem() {
	svc := NewArtifactListEventService()

	ctx := context.Background()
	t.db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID: 1,
		IntegrationID:   1,
		ControlPlanes:   "[\"CONTROL_PLANE_CN\", \"CONTROL_PLANE_I18N\"]",
	})

	t.Run("update work item success", func() {
		eventMsg := &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				DevList: []*multi.DevInfo{
					{
						WorkItems: []*dev.WorkItem{
							{
								Id:       "1",
								Platform: 1,
								SpaceKey: "1",
								Type:     "1",
								SpaceId:  "1",
								Status:   "1",
								Url:      "www.google.com",
								Name:     "123",
							},
						},
					},
				},
			},
		}
		monkey.PatchInstanceMethod(reflect.TypeOf(svc.releaseTicketDao), "UpdateByField", func(ctx context.Context, releaseTicketID uint64, version int64, fields repository.ReleaseTicketUpdateField) error {
			return nil
		})
		//monkey.Patch(rt_stage..GetCurrentStageByRtID, func(ctx context.Context, rtID uint64) (currentStage *entity.DBStage, exist bool, err error) {
		//	return &entity.DBStage{StageID: 1}, true, nil
		//})

		monkey.PatchInstanceMethod(reflect.TypeOf(svc.baseStageManager), "CreateGatekeeperCheck", func(ctx context.Context, stage *entity.DBStage, bizScene release_ticket_sharedpb.BizScene, useCheckSet bool, projects []*projectpb.ProjectBasicInfoItem) (err error) {
			return nil
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(svc.releaseTicketSvc), "GetDeletedWorkItems", func(ctx context.Context, rtWorkItems []*release_ticketpb.WorkItem, workItems []*release_ticketpb.WorkItem) []*release_ticketpb.WorkItem {
			return []*release_ticketpb.WorkItem{
				{
					Id:       "1",
					SpaceKey: "1",
					Type:     "1",
					SpaceId:  "1",
					Status:   "1",
					Url:      "www.google.com",
					Name:     "123",
				},
			}
		})
		monkey.PatchInstanceMethod(reflect.TypeOf(rt_operation_reporter.NewRtOperationReporter()), "SendReleaseTicketDeleteWorkItemsOperationRecord", func(ctx context.Context, req *release_ticketpb.UpdateReleaseTicketReq, oldReleaseTicket *entity.DBReleaseTicket, changeFrom string) {
		})
		_, err := svc.updateWorkItem(ctx, &entity.DBReleaseTicket{ReleaseTicketID: 1, Version: 1}, eventMsg)
		t.NoError(err)
	})

	t.Run("update work item failed", func() {
		eventMsg := &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				DevList: []*multi.DevInfo{
					{
						WorkItems: []*dev.WorkItem{
							{
								Id:       "1",
								Platform: 1,
								SpaceKey: "1",
								Type:     "1",
								SpaceId:  "1",
								Status:   "1",
								Url:      "www.google.com",
								Name:     "123",
							},
						},
					},
				},
			},
		}
		monkey.PatchInstanceMethod(reflect.TypeOf(svc.releaseTicketDao), "UpdateByField", func(ctx context.Context, releaseTicketID uint64, version int64, fields repository.ReleaseTicketUpdateField) error {
			return errors.New("update failed")
		})
		_, err := svc.updateWorkItem(ctx, &entity.DBReleaseTicket{ReleaseTicketID: 1, Version: 1}, eventMsg)
		t.Error(err)
	})
}

func (t *ArtifactListSuite) TestOnArtifactListUpdateHandlerHasVersion() {
	eventMsg := &IntegrationEventPayload{
		IntegrationId: 1,
		ArtifactList: &multi.ArtifactList{
			Version:     "12",
			ProjectList: []*multi.ProjectInfo{},
		},
		EventTimestamp: 10,
	}
	ctl := gomock.NewController(t.T())
	defer ctl.Finish()
	defer monkey.PatchInstanceMethod(reflect.TypeOf(&ArtifactListEventService{}), "IsMsgNeedHandle", func(ctx context.Context, eventMsg *IntegrationEventPayload, rt *entity.DBReleaseTicket) (bool, error) {
		return false, nil
	}).UnPatch()
	defer monkey.Patch(redis.SetNxWithResult).Return(nil, nil).UnPatch()

	rtChangeItemManager := rt_change_item_managermock.NewMockRtChangeItemManager(ctl)
	releaseTicketDaoMock := repositorymock.NewMockReleaseTicketDao(ctl)
	releaseTicketDaoMock.EXPECT().GetByIntegrationID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{ReleaseTicketID: 1, IntegrationVersion: "13"}, nil)
	rtChangeItemManager.EXPECT().SetIntegrationZoneStagedStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	releaseApproverDao := repositorymock.NewMockReleaseApproverDao(ctl)
	releaseApproverDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketReleaseApprover{
		{
			Username: "liu",
		},
	}, nil)

	releaseticketSvc := releaseticketmock.NewMockReleaseTicketSvc(ctl)
	releaseticketSvc.EXPECT().SaveRTEngineTaskOperator(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	svc := &ArtifactListEventService{
		releaseTicketDao:    releaseTicketDaoMock,
		rtChangeItemManager: rtChangeItemManager,
		releaseApproverDao:  releaseApproverDao,
		releaseTicketSvc:    releaseticketSvc,
		cdGray:              cd_gray.NewCdGraySvc(),
	}

	t.Run("update integration version", func() {
		err := svc.OnArtifactListUpdateHandler(t.ctx, eventMsg)
		t.NoError(err)
	})
}

func (t *ArtifactListSuite) TestRetryReleaseTicketArtifactEvent() {
	ctl := gomock.NewController(t.T())
	defer ctl.Finish()

	t.Run("happy path", func() {
		releaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctl)
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{
			IntegrationID: 1,
		}, nil)
		integrationSvc := mock.NewMockIntegrationSvc(ctl)
		integrationSvc.EXPECT().GetArtifactList(gomock.Any(), gomock.Any(), gomock.Any()).Return(&multi.GetArtifactListResp{
			ArtifactList: &multi.ArtifactList{},
		}, nil)
		svc := &ArtifactListEventService{
			releaseTicketDao: releaseTicketDao,
			integrationSvc:   integrationSvc,
			cdGray:           cd_gray.NewCdGraySvc(),
		}

		p := monkey.PatchInstanceMethod(reflect.TypeOf(svc), "OnArtifactListUpdateHandler").Return(nil)
		defer p.UnPatch()

		_, err := svc.RetryReleaseTicketArtifactEvent(t.ctx, &release_ticketpb.RetryReleaseTicketArtifactEventReq{
			ReleaseTicketId: 1,
		})
		t.NoError(err)
	})

	t.Run("get rt fail", func() {
		releaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctl)
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(nil, errors.New("aa"))
		svc := &ArtifactListEventService{
			releaseTicketDao: releaseTicketDao,
			cdGray:           cd_gray.NewCdGraySvc(),
		}

		p := monkey.PatchInstanceMethod(reflect.TypeOf(svc), "OnArtifactListUpdateHandler").Return(nil)
		defer p.UnPatch()

		_, err := svc.RetryReleaseTicketArtifactEvent(t.ctx, &release_ticketpb.RetryReleaseTicketArtifactEventReq{
			ReleaseTicketId: 1,
		})
		t.Error(err)
	})

	t.Run("GetArtifactList failed", func() {
		releaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctl)
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{
			IntegrationID: 1,
		}, nil)
		integrationSvc := mock.NewMockIntegrationSvc(ctl)
		integrationSvc.EXPECT().GetArtifactList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("aa"))
		svc := &ArtifactListEventService{
			releaseTicketDao: releaseTicketDao,
			integrationSvc:   integrationSvc,
			cdGray:           cd_gray.NewCdGraySvc(),
		}

		p := monkey.PatchInstanceMethod(reflect.TypeOf(svc), "OnArtifactListUpdateHandler").Return(nil)
		defer p.UnPatch()

		_, err := svc.RetryReleaseTicketArtifactEvent(t.ctx, &release_ticketpb.RetryReleaseTicketArtifactEventReq{
			ReleaseTicketId: 1,
		})
		t.Error(err)
	})
}

func (t *ArtifactListSuite) Test_SendArtifactReadyAfterChangeItemUpdated() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	changeItemSvc := changeitemmock.NewMockChangeItemSvc(ctrl)
	rtChangeItemManager := rt_change_item_managermock.NewMockRtChangeItemManager(ctrl)

	t.Run("满足发送事件要求", func() {
		changeItemSvc.EXPECT().SendChangeItemArtifactReadyRecord(gomock.Any(), gomock.Any()).Return()
		rtChangeItemManager.EXPECT().IsReleaseTicketArtifactReadyConditionSatisfied(gomock.Any(), gomock.Any()).Return(true, &entity.DBReleaseTicket{}, nil)
		svc := &ArtifactListEventService{
			changeItemSvc:       changeItemSvc,
			rtChangeItemManager: rtChangeItemManager,
		}
		svc.SendArtifactReadyAfterChangeItemUpdated(t.ctx, 1)
		t.Equal("h", "h")
	})
}

func (t *ArtifactListSuite) Test_IsMsgNeedHandle() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	fgSvc := cd_fgmock.NewMockCdFeatureGateSvc(ctrl)
	rtChangeItemManager := rt_change_item_managermock.NewMockRtChangeItemManager(ctrl)
	branchingModelConfigSvc := mock_branching_model.NewMockCDBranchingModelConfigSvc(ctrl)
	branchingModelConfigSvc.EXPECT().GetBranchingModelTypeFromRT(gomock.Any(), gomock.Any()).Return(
		branching_model_configpb.BranchingModelType_BRANCHING_MODEL_TYPE_BR_DEV_MA_DEPLOY, nil).AnyTimes()
	t.Run("兼容旧逻辑-有版本", func() {
		fgSvc.EXPECT().IsReleaseTicketArtifactReadyEventEnabled(gomock.Any(), gomock.Any()).Return(false, nil)

		svc := &ArtifactListEventService{
			fgSvc:                   fgSvc,
			branchingModelConfigSvc: branchingModelConfigSvc,
		}

		need, err := svc.IsMsgNeedHandle(t.ctx, &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
				Version:     "abc",
			},
			ChangeFrom:     ChangeFromDevTaskFinished,
			EventTimestamp: 10,
		}, &entity.DBReleaseTicket{})
		t.NoError(err)
		t.Equal(need, false)
	})
	t.Run("兼容旧逻辑-无版本", func() {
		fgSvc.EXPECT().IsReleaseTicketArtifactReadyEventEnabled(gomock.Any(), gomock.Any()).Return(false, nil)

		svc := &ArtifactListEventService{
			fgSvc:                   fgSvc,
			branchingModelConfigSvc: branchingModelConfigSvc,
		}

		need, err := svc.IsMsgNeedHandle(t.ctx, &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
			},
			EventTimestamp: 10,
		}, &entity.DBReleaseTicket{})
		t.NoError(err)
		t.Equal(need, true)
	})

	t.Run("有版本，始终需要", func() {
		fgSvc.EXPECT().IsReleaseTicketArtifactReadyEventEnabled(gomock.Any(), gomock.Any()).Return(true, nil)

		svc := &ArtifactListEventService{
			fgSvc:                   fgSvc,
			branchingModelConfigSvc: branchingModelConfigSvc,
		}

		need, err := svc.IsMsgNeedHandle(t.ctx, &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
				Version:     "AA",
			},
			EventTimestamp: 10,
		}, &entity.DBReleaseTicket{})
		t.NoError(err)
		t.Equal(need, true)
	})

	t.Run("无版本，集成区未完成，需要", func() {
		fgSvc.EXPECT().IsReleaseTicketArtifactReadyEventEnabled(gomock.Any(), gomock.Any()).Return(true, nil)
		rtChangeItemManager.EXPECT().IsIntegrationZoneDone(gomock.Any(), gomock.Any()).Return(false, nil)
		svc := &ArtifactListEventService{
			fgSvc:                   fgSvc,
			rtChangeItemManager:     rtChangeItemManager,
			branchingModelConfigSvc: branchingModelConfigSvc,
		}
		need, err := svc.IsMsgNeedHandle(t.ctx, &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
			},
			EventTimestamp: 10,
		}, &entity.DBReleaseTicket{})
		t.NoError(err)
		t.Equal(need, true)
	})
	t.Run("无版本，集成区已完成，不需要", func() {
		fgSvc.EXPECT().IsReleaseTicketArtifactReadyEventEnabled(gomock.Any(), gomock.Any()).Return(true, nil)
		rtChangeItemManager.EXPECT().IsIntegrationZoneDone(gomock.Any(), gomock.Any()).Return(true, nil)
		svc := &ArtifactListEventService{
			fgSvc:                   fgSvc,
			rtChangeItemManager:     rtChangeItemManager,
			branchingModelConfigSvc: branchingModelConfigSvc,
		}
		need, err := svc.IsMsgNeedHandle(t.ctx, &IntegrationEventPayload{
			ArtifactList: &multi.ArtifactList{
				ProjectList: []*multi.ProjectInfo{},
			},
			EventTimestamp: 10,
		}, &entity.DBReleaseTicket{})
		t.NoError(err)
		t.Equal(need, false)
	})
}
