package workflow

import (
	"context"
	"testing"
	"time"

	"github.com/bytedance/sonic"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb/spacerpcapi_mock"

	. "github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"github.com/stretchr/testify/suite"
	"gorm.io/gorm"

	"code.byted.org/canal/bytecycle_sdk/template"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	authzmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/authz/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/spacerpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tcc"
	wfconfig "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/config"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/devinfra/hagrid/pbgen/rpc/bc/varstorerpc"
)

type ProjectTemplateTestSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
	wfSvc  CDWorkflowSvc
}

func (t *ProjectTemplateTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	tcc.MustInit()

	mockAuthManager := authzmock.NewMockAuthzManager(gomock.NewController(t.T()))
	mockAuthManager.EXPECT().CheckSpacePermission(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(true, nil).AnyTimes()

	t.wfSvc = &WorkflowSvc{
		WorkflowDao:          repository.NewWorkflowDao(),
		NodeDao:              repository.NewNodeDao(),
		AssociateWorkflowDao: repository.NewAssociateWorkflowDao(),
		PplTemplateDao:       repository.NewPplTemplateConfigDao(),

		workflowConfigHelper: wfconfig.NewWorkflowConfigHelper(),
		nodeConfigHelper:     wfconfig.NewNodeConfigHelper(),
		VarstoreCli:          varstorerpc.Client(),
		authzManager:         mockAuthManager,
	}

	spacerpc.InitSpaceClient()
	mockSpaceClient := spacerpcapi_mock.NewMockClient(gomock.NewController(t.T()))
	mockSpaceClient.EXPECT().GetBizIDBySpaceID(gomock.Any(), gomock.Any()).
		Return(&rpcpb.GetBizIDBySpaceIDResp{
			BizId: int64(111653),
		}, nil).AnyTimes()
	spacerpc.SpaceClient = mockSpaceClient
}

func (t *ProjectTemplateTestSuite) TearDownTest() {
	t.cancel()
}

func TestTemplateConfig(t *testing.T) {
	suite.Run(t, new(ProjectTemplateTestSuite))
}

func setupDB(ctx context.Context) *gorm.DB {
	// setup DB
	nodes := make([]*workflowpb.Node, 0)
	nodes = append(nodes, &workflowpb.Node{
		Id:           679442432,
		Name:         "发布",
		NameI18N:     "Deploy",
		NodeType:     2,
		PrevNodeIds:  make([]uint64, 0),
		NextNodeIds:  make([]uint64, 0),
		Enabled:      true,
		EditDisabled: true,
		NodeConfig: &workflowpb.NodeConfig{
			NodeConfig: &workflowpb.NodeConfig_DeployNodeConfig{
				DeployNodeConfig: &workflowpb.DeployNodeConfig{},
			},
		},
	})

	nodeIDs := []uint64{679442432, 690268416}
	orchestrationRawMsg, _ := sonic.Marshal(nodeIDs)
	db := testfactory.NewUnitTestDB(ctx, &entity.DBWorkflow{}, &entity.DBNode{}, &entity.DBPplTemplateConfig{}, &entity.DBCheckMeta{}, &entity.DBWorkflowAssociation{},
		&entity.DBWorkflowDefaultConfig{}, &entity.DBNodeDefaultConfig{}, &entity.DBBranchingModelConfig{})
	mysql.DefaultDB = db

	db.Create(&entity.DBWorkflow{
		WorkflowID:      639608832,
		Name:            "workflow 名称",
		NameI18N:        "workflow name",
		Description:     "模板",
		DescriptionI18N: "template",
		WorkspaceID:     339727360,
		IsDefault:       true,
		Orchestration:   string(orchestrationRawMsg),
		WorkflowConfig:  "{}",
	})

	dbNodes, err := ToDBNodes(ctx, 639608832, nodes, "Li Hua")
	if err != nil {
		panic(err)
	}
	db.Create(dbNodes)

	return db
}

func (t *ProjectTemplateTestSuite) TestCreateCustomTemplate() {
	svc := t.wfSvc
	ctx := context.Background()
	setupDB(ctx)

	defer Mock(GetMethod(svc, "SendTemplateEvent")).Return().Build().UnPatch()

	t.Run("create success", func() {
		PatchConvey("CreateCustomTemplate", t.T(), func(c convey.C) {
			Mock((*template.Template).CopySingleTemplate).Return(1328849, nil).Build()
			Mock((*template.Template).UpdateTemplate).Return(1328849, nil).Build()
			Mock((*WorkflowSvc).CheckConfigAuth).Return(uint64(339727360), nil).Build()
			Mock((*template.Template).AppendTemplateTag).Return(nil).Build()

			mockReq := &workflowpb.CreateCustomTemplateReq{
				WorkflowId:       639608832,
				NodeId:           679442432,
				ProjectType:      sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED,
				SourceTemplateId: 1200970,
				Name:             "测试模版",
				NameI18N:         "testing template",
				Regions: []sharedpb.ControlPlane{
					sharedpb.ControlPlane_CONTROL_PLANE_CN,
				},
				Projects: []*sharedpb.ProjectUniqueKey{
					{
						ProjectUniqueId: "111",
						ProjectName:     "test project",
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
					},
					{
						ProjectUniqueId: "bc.song.mono",
						ProjectName:     "bc.song.mono",
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
					},
				},
				WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
				Username:     "lusong.chn",
			}

			res, err := svc.CreateCustomTemplate(t.ctx, mockReq)

			t.NoError(err)
			t.NotNil(res)
		})
	})
}

func (t *ProjectTemplateTestSuite) TestUpdateTemplate() {
	svc := t.wfSvc
	ctx := context.Background()
	db := setupDB(ctx)

	t.Run("update success", func() {
		mockRecord := &entity.DBPplTemplateConfig{
			ID:               1,
			WorkflowID:       639608832,
			NodeID:           679442432,
			WorkflowType:     int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
			ProjectType:      sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID:  "bc.march.rpc",
			ProjectName:      "bc.march.rpc",
			Region:           sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
			Scene:            0,
			ManageType:       int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
			TemplateID:       123,
			TemplateVersion:  1,
			TemplateName:     "测测名字",
			TemplateNameI18N: "testing name",
			IsDefault:        0,
			IsRollback:       0,
			Creator:          "lusong.chn",
			CreatedAt:        time.Time{},
			UpdatedAt:        time.Time{},
			DeletedAt:        gorm.DeletedAt{},
			UpdatedBy:        "",
			DeletedBy:        "",
		}
		db.Create(mockRecord)
		mockReq := &workflowpb.UpdateCustomTemplateReq{
			WorkflowId:   639608832,
			NodeId:       679442432,
			WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
			TemplateId:   123,
			Regions: []sharedpb.ControlPlane{
				sharedpb.ControlPlane_CONTROL_PLANE_I18N,
				sharedpb.ControlPlane_CONTROL_PLANE_CN,
			},
			Projects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectUniqueId: "111",
					ProjectName:     "test project",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
				},
				{
					ProjectUniqueId: "bc.song.mono",
					ProjectName:     "bc.song.mono",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
				},
			},
			Username: "liujia.ldspirit",
		}

		res, err := svc.UpdateCustomTemplate(t.ctx, mockReq)
		t.NoError(err)
		t.NotNil(res)

		queryItems := make([]*entity.DBPplTemplateConfig, 0)

		err = db.Model(&entity.DBPplTemplateConfig{}).Where("template_id = ?", mockReq.TemplateId).Find(&queryItems).Error
		t.NoError(err)
		t.Equal(len(queryItems), len(mockReq.Regions)*len(mockReq.Projects))
		for _, item := range queryItems {
			t.Equal(mockReq.TemplateId, item.TemplateID)
			t.Equal(mockReq.Username, item.UpdatedBy)
			t.NotEqual(mockRecord.UpdatedAt, item.UpdatedAt)
			t.Equal("测测名字", item.TemplateName)
			t.Equal("testing name", item.TemplateNameI18N)
		}
	})
}

func (t *ProjectTemplateTestSuite) TestCheckDuplicateTemplate() {
	t.Run("success", func() {
		mockRecords := []*entity.DBPplTemplateConfig{
			{
				TemplateID: 2333,
				IsDefault:  1,
			},
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS.String(),
				ProjectUniqueID: "bc.faas.demo",
				ProjectName:     "bc.faas.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
				TemplateID:      111,
				IsDefault:       0,
				Creator:         "lusong.chn",
			},
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ProjectUniqueID: "bc.tce.demo",
				ProjectName:     "bc.tce.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
				TemplateID:      111,
				IsDefault:       0,
				Creator:         "lusong.chn",
			},
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS.String(),
				ProjectUniqueID: "bc.faas.demo",
				ProjectName:     "bc.faas.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
				TemplateID:      111,
				IsDefault:       0,
				Creator:         "lusong.chn",
			},
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ProjectUniqueID: "bc.tce.demo",
				ProjectName:     "bc.tce.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
				TemplateID:      111,
				IsDefault:       0,
				Creator:         "lusong.chn",
			},
		}

		regions := []sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_CN, sharedpb.ControlPlane_CONTROL_PLANE_I18N}

		projects := []*sharedpb.ProjectUniqueKey{
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
				ProjectName:     "bc.faas.demo",
				ProjectUniqueId: "bc.faas.demo",
			},
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ProjectName:     "bc.tce.demo",
				ProjectUniqueId: "bc.tce.demo",
			},
		}

		res := checkDuplicateTemplate(mockRecords, 111, regions, projects)
		t.Empty(res)

		mockRecords = append(mockRecords, &entity.DBPplTemplateConfig{
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB.String(),
			ProjectUniqueID: "bc.web.demo",
			ProjectName:     "bc.web.demo",
			Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
			TemplateID:      111,
			IsDefault:       0,
			Creator:         "lusong.chn",
		})

		res = checkDuplicateTemplate(mockRecords, 111, regions, projects)
		t.Empty(res)

		mockRecords = append(mockRecords, &entity.DBPplTemplateConfig{
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bc.tce.demo",
			ProjectName:     "bc.tce.demo",
			Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
			TemplateID:      123,
			IsDefault:       0,
			Creator:         "lusong.chn",
		})
		res = checkDuplicateTemplate(mockRecords, 111, regions, projects)
		t.Equal(len(res), 1)
		r := res[0]
		t.Equal(r.Project.ProjectType, sharedpb.ProjectType_PROJECT_TYPE_TCE)
		t.Equal(r.Project.ProjectUniqueId, "bc.tce.demo")
		t.Equal(r.Region, sharedpb.ControlPlane_CONTROL_PLANE_I18N)
	})
}

func (t *ProjectTemplateTestSuite) TestDeleteTemplate() {
	ctx := context.Background()
	db := setupDB(ctx)
	svc := t.wfSvc

	defer Mock(GetMethod(svc, "SendTemplateEvent")).Return().Build().UnPatch()

	t.Run("delete success", func() {
		mockRecord := &entity.DBPplTemplateConfig{
			ID:              1,
			WorkflowID:      639608832,
			NodeID:          679442432,
			WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
			ProjectUniqueID: "bc.tce.demo",
			ProjectName:     "bc.tce.demo",
			Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
			Scene:           0,
			ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
			TemplateID:      123,
			IsDefault:       0,
			Creator:         "lusong.chn",
		}
		db.Create(mockRecord)

		mockReq := &workflowpb.DeleteCustomTemplateReq{
			WorkflowId:   639608832,
			NodeId:       679442432,
			WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
			TemplateId:   123,
			Username:     "lusong.chn",
		}

		res, err := svc.DeleteCustomTemplate(ctx, mockReq)
		t.NoError(err)
		t.NotNil(res.TemplateId, mockReq.TemplateId)
	})
}

func (t *ProjectTemplateTestSuite) TestCheckDuplicateService() {
	svc := t.wfSvc
	ctx := context.Background()
	setupDB(ctx)

	t.Run("success", func() {
		_, err := svc.CheckDuplicateCustomTemplate(ctx, &workflowpb.CheckDuplicateCustomTemplateReq{
			WorkflowId:   111,
			NodeId:       679442432,
			TemplateId:   223,
			WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
			Regions:      []sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_CN, sharedpb.ControlPlane_CONTROL_PLANE_I18N},
			Projects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
					ProjectName:     "bc.faas.demo",
					ProjectUniqueId: "bc.faas.demo",
				},
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
					ProjectName:     "bc.tce.demo",
					ProjectUniqueId: "bc.tce.demo",
				},
			},
			Username: "lusong.chn",
		})
		t.NoError(err)
	})
}

func (t *ProjectTemplateTestSuite) TestDeduplicateProjectUniqueKey() {
	mockList := []*sharedpb.ProjectUniqueKey{
		{
			ProjectUniqueId: "1",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "bc.tce.demo",
		},
		{
			ProjectUniqueId: "1",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "bc.tce.demo",
		},
		{
			ProjectUniqueId: "2",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "bc.tce.demo",
		},
	}

	res := DeduplicateProjectUniqueKey(mockList)
	t.Equal(len(res), 2)
}

func (t *ProjectTemplateTestSuite) TestDeduplicateRegion() {
	mockList := []sharedpb.ControlPlane{
		sharedpb.ControlPlane_CONTROL_PLANE_CN,
		sharedpb.ControlPlane_CONTROL_PLANE_I18N,
		sharedpb.ControlPlane_CONTROL_PLANE_I18N,
		sharedpb.ControlPlane_CONTROL_PLANE_I18N,
	}

	res := DeduplicateRegion(mockList)
	t.Equal(len(res), 2)
}

func (t *ProjectTemplateTestSuite) TestGetNodeProjectTemplateConfig() {
	svc := t.wfSvc
	ctx := context.Background()
	db := setupDB(ctx)

	t.Run("success", func() {
		mockRecords := []*entity.DBPplTemplateConfig{
			{
				TemplateID: 2333,
				IsDefault:  1,
			},
			{
				WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
				WorkflowID:      1234,
				NodeID:          1133,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS.String(),
				ProjectUniqueID: "bc.faas.demo",
				ProjectName:     "bc.faas.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
				TemplateID:      997969,
				IsDefault:       0,
				ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_BYTETREE),
				Scene:           int32(workflowpb.TemplateScene_TEMPLATE_SCENE_FEAT_DEV),
				Creator:         "lusong.chn",
				CreatedAt:       time.Now(),
			},
			{
				WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
				WorkflowID:      1234,
				NodeID:          1133,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ProjectUniqueID: "bc.tce.demo",
				ProjectName:     "bc.tce.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
				TemplateID:      997969,
				IsDefault:       0,
				ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
				Creator:         "lusong.chn",
				CreatedAt:       time.Now().Add(1 * time.Second),
			},
			{
				WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
				WorkflowID:      1234,
				NodeID:          1133,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS.String(),
				ProjectUniqueID: "bc.faas.demo",
				ProjectName:     "bc.faas.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
				TemplateID:      111,
				IsDefault:       0,
				ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_BYTETREE),
				Scene:           int32(workflowpb.TemplateScene_TEMPLATE_SCENE_FEAT_DEV),
				Creator:         "lusong.chn",
				CreatedAt:       time.Now().Add(2 * time.Second),
			},
			{
				WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
				WorkflowID:      1234,
				NodeID:          1133,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ProjectUniqueID: "bc.tce.demo",
				ProjectName:     "bc.tce.demo",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
				TemplateID:      111,
				IsDefault:       0,
				ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
				Creator:         "lusong.chn",
				CreatedAt:       time.Now().Add(3 * time.Second),
			},
			{
				WorkflowType:    int32(workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET),
				WorkflowID:      1234,
				NodeID:          1133,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED.String(),
				ProjectUniqueID: "",
				ProjectName:     "",
				Region:          sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED.String(),
				TemplateID:      6666,
				IsDefault:       1,
				ManageType:      int32(workflowpb.ManageType_MANAGE_TYPE_SPACE),
				Creator:         "lusong.chn",
			},
		}

		db.Create(mockRecords)

		res, err := svc.GetNodeProjectTemplateConfig(ctx, &workflowpb.GetNodeProjectTemplateConfigReq{
			WorkflowId:   1234,
			NodeId:       1133,
			WorkflowType: workflowpb.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
		})
		t.NoError(err)

		for _, r := range res.ProjectConfigs {
			if r.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_TCE {
				t.Equal(r.ProjectType, sharedpb.ProjectType_PROJECT_TYPE_TCE)
				t.Equal(r.ManageType, workflowpb.ManageType_MANAGE_TYPE_SPACE)
			}
			if r.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_FAAS {
				t.Equal(r.ProjectType, sharedpb.ProjectType_PROJECT_TYPE_FAAS)
				t.Equal(r.ManageType, workflowpb.ManageType_MANAGE_TYPE_BYTETREE)
				t.Equal(r.Scene, workflowpb.TemplateScene_TEMPLATE_SCENE_FEAT_DEV)

			}
		}
		t.NotNil(res)
	})
}
