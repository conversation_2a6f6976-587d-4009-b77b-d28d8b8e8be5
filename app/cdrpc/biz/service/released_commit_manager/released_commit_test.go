package released_commit_manager

import (
	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"code.byted.org/bits/monkey"
	git_server_gen "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/overpass/bytedance_bits_git_server/kitex_gen/bytedance/bits/git_server"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	repositorymock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	integrationsdkmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	changeitemmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item/mock"
	mock_config "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/config/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
)

func Test_GetChangeItemReleaseBranchName(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	releaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctrl)
	workflowConfigHelper := mock_config.NewMockWorkflowConfigHelper(ctrl)
	changeItemDao := repositorymock.NewMockReleaseTicketChangeItemDao(ctrl)
	changeItemSvc := changeitemmock.NewMockChangeItemSvc(ctrl)
	gitServerSvc := integrationsdkmock.NewMockGitServerSvc(ctrl)

	t.Run("not default branch, no change item", func(t *testing.T) {
		cfg := &workflowpb.WorkflowConfig{
			InstanceConfig: &workflowpb.WorkflowConfig_ReleaseTicketConfig{
				ReleaseTicketConfig: &workflowpb.ReleaseTicketConfig{
					BranchingModelConfig: &branching_model_configpb.BranchingModelConfig{
						BranchingModelType: branching_model_configpb.BranchingModelType_BRANCHING_MODEL_TYPE_BR_DEV_BR_DEPLOY,
						ReleaseBranch: &branching_model_configpb.BranchNaming{
							Name:             "integration_test",
							BranchNamingType: branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_DYNAMIC,
						},
					},
				},
			},
		}
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		workflowConfigHelper.EXPECT().DbToPb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cfg, nil)
		svc := &releasedCommitManager{
			releaseTicketDao:     releaseTicketDao,
			workflowConfigHelper: workflowConfigHelper,
		}
		branchName, _, err := svc.GetChangeItemReleaseBranchName(ctx, 1, nil)
		assert.NoError(t, err)
		assert.Equal(t, branchName, "integration_test")
	})

	t.Run("default branch, but no change item", func(t *testing.T) {
		cfg := &workflowpb.WorkflowConfig{
			InstanceConfig: &workflowpb.WorkflowConfig_ReleaseTicketConfig{
				ReleaseTicketConfig: &workflowpb.ReleaseTicketConfig{
					BranchingModelConfig: &branching_model_configpb.BranchingModelConfig{
						BranchingModelType: branching_model_configpb.BranchingModelType_BRANCHING_MODEL_TYPE_BR_DEV_BR_DEPLOY,
						ReleaseBranch: &branching_model_configpb.BranchNaming{
							BranchNamingType: branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_GIT_DEFAULT,
						},
					},
				},
			},
		}
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		workflowConfigHelper.EXPECT().DbToPb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cfg, nil)
		svc := &releasedCommitManager{
			releaseTicketDao:     releaseTicketDao,
			workflowConfigHelper: workflowConfigHelper,
		}
		branchName, _, err := svc.GetChangeItemReleaseBranchName(ctx, 1, nil)
		assert.NoError(t, err)
		assert.Equal(t, branchName, "")
	})

	t.Run("not default branch, has change item", func(t *testing.T) {
		cfg := &workflowpb.WorkflowConfig{
			InstanceConfig: &workflowpb.WorkflowConfig_ReleaseTicketConfig{
				ReleaseTicketConfig: &workflowpb.ReleaseTicketConfig{
					BranchingModelConfig: &branching_model_configpb.BranchingModelConfig{
						BranchingModelType: branching_model_configpb.BranchingModelType_BRANCHING_MODEL_TYPE_BR_DEV_BR_DEPLOY,
						ReleaseBranch: &branching_model_configpb.BranchNaming{
							Name:             "integration_test",
							BranchNamingType: branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_FIXED,
						},
					},
				},
			},
		}
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		workflowConfigHelper.EXPECT().DbToPb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cfg, nil)
		changeItemSvc.EXPECT().GetChangeItemGitRepo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Project{
			Id: 1,
		}, nil)
		gitServerSvc.EXPECT().GetBranchV2(gomock.Any(), gomock.Any()).Return(&git_server_gen.GetBranchV2Response{
			Branch: &git_server_gen.GitBranch{
				Name: "integration_test",
				Commit: &git_server_gen.GitCommit{
					Id: "sha",
				},
			},
		}, nil)
		svc := &releasedCommitManager{
			releaseTicketDao:     releaseTicketDao,
			workflowConfigHelper: workflowConfigHelper,
			changeItemSvc:        changeItemSvc,
			gitServerSvc:         gitServerSvc,
		}
		branchName, c, err := svc.GetChangeItemReleaseBranchName(ctx, 1, &release_ticketpb.ReleaseTicketChangeItem{})
		assert.NoError(t, err)
		assert.Equal(t, branchName, "integration_test")
		assert.Equal(t, c.GetSha(), "sha")
	})

	t.Run("default branch, has change item, but branch not found", func(t *testing.T) {
		cfg := &workflowpb.WorkflowConfig{
			InstanceConfig: &workflowpb.WorkflowConfig_ReleaseTicketConfig{
				ReleaseTicketConfig: &workflowpb.ReleaseTicketConfig{
					BranchingModelConfig: &branching_model_configpb.BranchingModelConfig{
						BranchingModelType: branching_model_configpb.BranchingModelType_BRANCHING_MODEL_TYPE_BR_DEV_BR_DEPLOY,
						ReleaseBranch: &branching_model_configpb.BranchNaming{
							BranchNamingType: branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_GIT_DEFAULT,
						},
					},
				},
			},
		}
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		workflowConfigHelper.EXPECT().DbToPb(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(cfg, nil)
		changeItemSvc.EXPECT().GetChangeItemGitRepo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Project{
			Id: 1,
		}, nil)
		gitServerSvc.EXPECT().GetBranchV2(gomock.Any(), gomock.Any()).Return(nil, bits_err.GITSERVER.ErrBranchNotFound)
		changeItemDao.EXPECT().GetSingleChangeItem(gomock.Any(), gomock.Any()).Return(testfactory.NewMockDBReleaseTicketChangeItem(1), nil)
		defer monkey.Patch(change_item.GetChangeItemDefaultBranch, func(ctx context.Context, rtId uint64, scmArtifacts []*change_itempb.SCMArtifact) string {
			return "master"
		}).UnPatch()
		svc := &releasedCommitManager{
			releaseTicketDao:     releaseTicketDao,
			workflowConfigHelper: workflowConfigHelper,
			changeItemDao:        changeItemDao,
			gitServerSvc:         gitServerSvc,
			changeItemSvc:        changeItemSvc,
		}
		branchName, c, err := svc.GetChangeItemReleaseBranchName(ctx, 1, &release_ticketpb.ReleaseTicketChangeItem{})
		assert.NoError(t, err)
		assert.Equal(t, branchName, "master")
		assert.Equal(t, c.GetSha(), "")
	})
}

func Test_GetChangeItemReleaseCommit(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	releaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctrl)
	changeItemDao := repositorymock.NewMockReleaseTicketChangeItemDao(ctrl)
	gitServiceSvc := integrationsdkmock.NewMockGitServerSvc(ctrl)
	changeItemSvc := changeitemmock.NewMockChangeItemSvc(ctrl)

	t.Run("from artifact", func(t *testing.T) {
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&releasedCommitManager{}), "ReadCommitFrom", func(ctx context.Context, rt *entity.DBReleaseTicket, changeItem *entity.DBReleaseTicketChangeItem) (ReadCommitFromType, error) {
			return READ_COMMIT_FROM_ARTIFACT, nil
		}).UnPatch()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&releasedCommitManager{}), "ReadCommitFromArtifact", func(ctx context.Context, gitRepoId int32, rt *entity.DBReleaseTicket, changeItem *entity.DBReleaseTicketChangeItem) (string, error) {
			return "sha", nil
		}).UnPatch()
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		changeItemDao.EXPECT().GetSingleChangeItem(gomock.Any(), gomock.Any()).Return(testfactory.NewMockDBReleaseTicketChangeItem(1), nil)
		changeItemSvc.EXPECT().GetChangeItemGitRepo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Project{Id: 1}, nil)
		//gitServiceSvc.EXPECT().GetCommit(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Commit{Id: "sss"}, nil)
		svc := &releasedCommitManager{
			releaseTicketDao: releaseTicketDao,
			changeItemDao:    changeItemDao,
			gitServerSvc:     gitServiceSvc,
			changeItemSvc:    changeItemSvc,
		}
		from, commit, err := svc.GetChangeItemReleaseCommit(ctx, 1, &release_ticketpb.ReleaseTicketChangeItem{})
		assert.Equal(t, from, READ_COMMIT_FROM_ARTIFACT)
		assert.NoError(t, err)
		assert.Equal(t, commit.GetId(), "sha")
	})

	t.Run("from released", func(t *testing.T) {
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&releasedCommitManager{}), "ReadCommitFrom", func(ctx context.Context, rt *entity.DBReleaseTicket, changeItem *entity.DBReleaseTicketChangeItem) (ReadCommitFromType, error) {
			return READ_COMMIT_FROM_RELEASED, nil
		}).UnPatch()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&releasedCommitManager{}), "ReadCommitFromReleased", func(ctx context.Context, gitRepoId int32, rt *entity.DBReleaseTicket, changeItem *entity.DBReleaseTicketChangeItem) (string, error) {
			return "sha", nil
		}).UnPatch()
		releaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{}, nil)
		changeItemDao.EXPECT().GetSingleChangeItem(gomock.Any(), gomock.Any()).Return(testfactory.NewMockDBReleaseTicketChangeItem(1), nil)
		//gitServiceSvc.EXPECT().GetCommit(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Commit{Id: "sss"}, nil)
		changeItemSvc.EXPECT().GetChangeItemGitRepo(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Project{Id: 1}, nil)
		svc := &releasedCommitManager{
			releaseTicketDao: releaseTicketDao,
			changeItemDao:    changeItemDao,
			gitServerSvc:     gitServiceSvc,
			changeItemSvc:    changeItemSvc,
		}
		from, commit, err := svc.GetChangeItemReleaseCommit(ctx, 1, &release_ticketpb.ReleaseTicketChangeItem{})
		assert.Equal(t, from, READ_COMMIT_FROM_RELEASED)
		assert.NoError(t, err)
		assert.Equal(t, commit.GetId(), "sha")
	})
}

func Test_ReadCommitFrom(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	t.Run("err", func(t *testing.T) {
		svc := &releasedCommitManager{}
		_, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{}, &entity.DBReleaseTicketChangeItem{
			Reviewers: "abc",
		})
		assert.Error(t, err)
	})
	t.Run("is before releasing", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_INTEGRATION.String(),
		}, &entity.DBReleaseTicketChangeItem{})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_ARTIFACT)
	})
	t.Run("is releasing and checked", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String(),
		}, &entity.DBReleaseTicketChangeItem{
			Reviewers: `["liujia.ldspirit"]`,
		})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_DEPLOY_STRATEGY)
	})
	t.Run("is releasing and no checked", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASING.String(),
		}, &entity.DBReleaseTicketChangeItem{})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_ARTIFACT)
	})
	t.Run("canceled", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_CANCELED.String(),
		}, &entity.DBReleaseTicketChangeItem{})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_ARTIFACT)
	})
	t.Run("released no rollback", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_CANCELED.String(),
		}, &entity.DBReleaseTicketChangeItem{
			ReleasedAt: time.Now(),
		})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_RELEASED)
	})
	t.Run("released and rollback", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_CANCELED.String(),
		}, &entity.DBReleaseTicketChangeItem{
			ReleasedAt: time.Now().Add(-1000),
			RollbackAt: time.Now(),
		})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_ROLLBACK)
	})
	t.Run("released and rollback, then released", func(t *testing.T) {
		svc := &releasedCommitManager{}
		from, err := svc.ReadCommitFrom(ctx, &entity.DBReleaseTicket{
			Status: release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_CANCELED.String(),
		}, &entity.DBReleaseTicketChangeItem{
			ReleasedAt: time.Now().Add(1000),
			RollbackAt: time.Now(),
		})
		assert.NoError(t, err)
		assert.Equal(t, from, READ_COMMIT_FROM_RELEASED)
	})
}

func Test_ReadCommitFromArtifact(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	gitServiceSvc := integrationsdkmock.NewMockGitServerSvc(ctrl)

	t.Run("artifact is based branch", func(t *testing.T) {
		gitServiceSvc.EXPECT().GetBranchV2(gomock.Any(), gomock.Any()).Return(&git_server_gen.GetBranchV2Response{
			Branch: &git_server_gen.GitBranch{
				Commit: &git_server_gen.GitCommit{Id: "sha"},
			},
		}, nil)
		svc := &releasedCommitManager{
			gitServerSvc: gitServiceSvc,
		}
		sha, err := svc.ReadCommitFromArtifact(ctx, 1, &entity.DBReleaseTicket{}, &entity.DBReleaseTicketChangeItem{
			DeployResource: testfactory.NewMockDBReleaseTicketChangeItem(1).DeployResource,
		})
		assert.NoError(t, err)
		assert.Equal(t, sha, "sha")
	})

	t.Run("artifact is based commit", func(t *testing.T) {
		t.SkipNow()
	})

	t.Run("artifact is based version", func(t *testing.T) {
		t.SkipNow()
	})
}

func Test_ReadCommitFromReleased(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	releaseCommitDao := repositorymock.NewMockReleaseCommitRecordDao(ctrl)

	t.Run("found", func(t *testing.T) {
		releaseCommitDao.EXPECT().GetByChangeItem(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseCommitRecord{
			CommitID: "sha",
		}, nil)
		svc := &releasedCommitManager{
			releaseCommitDao: releaseCommitDao,
		}
		sha, err := svc.ReadCommitFromReleased(ctx, 1, &entity.DBReleaseTicket{}, &entity.DBReleaseTicketChangeItem{})
		assert.NoError(t, err)
		assert.Equal(t, sha, "sha")
	})
}
