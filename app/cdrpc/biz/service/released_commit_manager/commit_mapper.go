package released_commit_manager

import (
	git_server_gen "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/overpass/bytedance_bits_git_server/kitex_gen/bytedance/bits/git_server"
)

// kitex_gen 转 overpass 兼容，逐步全部改成 kitex_gen 调用
func commitMapper(commit *git_server_gen.GitCommit) *git_server.CodeBaseCommit {
	baseCommit := &git_server.CodeBaseCommit{
		Sha:     commit.GetId(),
		Message: commit.GetMessage(),
	}
	if commit.GetAuthor() != nil {
		baseCommit.Author = &git_server.CodeBaseCommitAuthor{
			Name:  commit.GetAuthor().GetName(),
			Email: commit.GetAuthor().GetEmail(),
		}
	}
	if commit.GetCommitter() != nil {
		baseCommit.Committer = &git_server.CodeBaseCommiiter{
			Name:  commit.GetCommitter().GetName(),
			Email: commit.GetCommitter().GetEmail(),
		}
	}
	if len(commit.GetParents()) > 0 {
		baseCommit.Parents = make([]*git_server.CodeBaseCommitSHA, 0)
		for _, parent := range commit.Parents {
			baseCommit.Parents = append(baseCommit.Parents, &git_server.CodeBaseCommitSHA{
				Sha: parent,
			})
		}
	}

	return baseCommit
}
