package release_ticket

import (
	"context"
	"fmt"
	"strconv"

	"golang.org/x/sync/errgroup"

	redisrepository "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis/repository"
	rtdomain "code.byted.org/devinfra/hagrid/app/cdrpc/biz/domain/release_ticket"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/scm"
	release_ticket_helper "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket/helper"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/appcenter"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/crossborderpb"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/paas_sdk/tce_config"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bits/integration/multi"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bytedance/bits/dev"
)

func (h *releaseTicketSvc) GetReleaseTicketProjectDetail(ctx context.Context, rtID uint64) (resp *release_ticketpb.GetReleaseTicketProjectDetailResp, err error) {
	rt, err := h.releaseTicketDao.GetByReleaseTicketID(ctx, rtID)
	if err != nil {
		err = fmt.Errorf("failed to find release ticket by release_ticket_id/%d: %w", rtID, err)
		logs.CtxError(ctx, err.Error())

		return nil, err
	}

	if rtdomain.IsRtWithoutAssociateDevTask(rt) {
		logs.CtxInfo(ctx, "单发布单不用这个接口获取详情")
		return &release_ticketpb.GetReleaseTicketProjectDetailResp{}, nil
	}

	// 极端情况下，集成区没有change item，因此通过config拿集成分支
	rtConfig, err := h.workflowConfigHelper.DbToPb(ctx, rt.WorkflowConfig, false, 0)
	if err != nil {
		return nil, bits_err.RELEASETICKET.ErrParamConversion.PassThrough(err)
	}
	integrationBranch := rtConfig.GetReleaseTicketConfig().GetBranchingModelConfig().GetIntegrationBranch().GetName()

	// 集成区信息，用来获取scm制品、合并编译等
	changeItems, err := h.changeItemSvc.GetReleaseTicketChangeItems(ctx, rtID)
	if err != nil {
		return nil, bits_err.RELEASETICKET.ErrProject.AddErrMsg("get release ticket all projects failed").AddOrPass(ctx, err)
	}

	// 发布单关联的全部开发任务，获取任务名
	rowDevTasks, err := h.integrationSvc.GetIntegrationAllDevTaskList(ctx, rt.IntegrationID, true)
	if err != nil {
		return nil, bits_err.RELEASETICKET.ErrCallIntegration.AddErrMsg("get all dev tasks failed").AddError(err) //该错误会偶现超时报错
	}

	// 获取制品，目的是为了过滤掉revert的开发任务
	artifactList := &multi.GetArtifactListResp{}
	if rt.IntegrationID > 0 {
		artifactList, err = h.integrationSvc.GetArtifactList(ctx, rt.IntegrationID, "")
		if err != nil {
			if err != nil {
				return nil, bits_err.RELEASETICKET.ErrCallIntegration.AddErrMsg("get artifacts in integration zone failed").AddError(err)
			}
		}
	}

	devTaskNameMap := make(map[int64]string, 0)
	for _, task := range rowDevTasks {
		if task.GetDevTask().GetStatus() == dev.DevTaskStatus_finished {
			devTaskNameMap[task.GetDevTask().GetId()] = task.GetDevTask().GetName()
		}
	}

	// 获取项目清单时需要生成全量控制面的项目信息
	fullControlPlanes := []sharedpb.ControlPlane{
		sharedpb.ControlPlane_CONTROL_PLANE_CN,
		sharedpb.ControlPlane_CONTROL_PLANE_I18N,
		sharedpb.ControlPlane_CONTROL_PLANE_TTP,
		sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP,
	}

	// pure US-TTP
	isUseNewUSTTP, err := h.cdFeatureGateSvc.IsPureUSTTPControlPlaneEnabled(ctx, int64(rt.WorkspaceID))
	if err != nil {
		logs.CtxError(ctx, "failed to get feature gate: %s", err.Error())
	}

	if isUseNewUSTTP {
		logs.CtxInfo(ctx, "发布单自身没有旧 usttp 控制面，且命中灰度空间，那么返回新 usttp 的项目内容")
		fullControlPlanes = []sharedpb.ControlPlane{
			sharedpb.ControlPlane_CONTROL_PLANE_CN,
			sharedpb.ControlPlane_CONTROL_PLANE_I18N,
			sharedpb.ControlPlane_CONTROL_PLANE_TTP,
			sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP,
			sharedpb.ControlPlane_CONTROL_PLANE_US_TTP,
		}
	}

	devtaskCps := make(map[dev.ControlPlane]dev.ControlPlane, 0)
	projectControlPlanesMap := make(map[string]*release_ticketpb.ControlPlaneProject, 0)
	for _, p := range artifactList.GetArtifactList().GetProjectList() {
		name := p.GetProjectName()
		projectType, err := utils.TransDevProjectTypeToPB(p.GetProjectType())
		if err != nil {
			return nil, bits_err.RELEASETICKET.ErrParamConversion.AddErrMsg("transfer dev project type to pb failed")
		}

		controlPlaneProject := &release_ticketpb.ControlPlaneProject{
			Project: &change_itempb.ChangeItemDeployTarget{
				ProjectType:     projectType,
				ProjectUniqueId: p.GetProjectUniqueId(),
				ProjectName:     p.GetProjectName(),
			},
		}

		taskScmInfoMap := make(map[string][]*dev.SCMInfo, 0)
		for _, artifact := range p.GetArtifact() {
			taskScmInfoMap[artifact.GetControlPanel().String()] = artifact.GetScmList()

			if _, ok := devtaskCps[artifact.GetControlPanel()]; !ok {
				devtaskCps[artifact.GetControlPanel()] = artifact.GetControlPanel()
			}
		}
		var defaultMergeBuildInfo *sharedpb.ScmMergeBuildInfo
		scmMergeInfosCPMap := make(map[string]*sharedpb.ScmMergeBuildInfo)
		scmArtifactsCPMap := make(map[string][]*change_itempb.SCMArtifact, 0)
		for _, item := range changeItems {
			if item.GetProjectUniqueId() == p.GetProjectUniqueId() {
				scmArtifactsCPMap[item.GetControlPlane().String()] = item.GetDeployResource().GetScmArtifacts()
				scmMergeInfosCPMap[item.GetControlPlane().String()] = item.GetDeployResource().GetScmMergeBuildInfo()
				if defaultMergeBuildInfo == nil {
					defaultMergeBuildInfo = item.GetDeployResource().GetScmMergeBuildInfo()
				}
			}
		}
		if defaultMergeBuildInfo == nil && p.ProjectType == dev.ProjectType_PROJECT_TYPE_TCE {
			if mainID := rtdomain.GetMergeBuildRepoID(ctx, p); mainID != 0 {
				meta, err := h.scmSvc.GetRepoMergeBuildMeta(ctx, mainID)
				if err != nil {
					return nil, bits_err.THIRDPARTYSCM.ErrCallScmAPI.AddErrMsg("get merge build info of repo failed").AddError(err)
				}
				defaultMergeBuildInfo = rtdomain.MergeBuildApiToCD(ctx, meta)
			}
		}

		devTasks := gslice.Map(p.GetDevIds(), func(id int64) *release_ticketpb.DevTaskOverview {
			return &release_ticketpb.DevTaskOverview{
				DevTaskId:   uint64(id),
				DevTaskName: devTaskNameMap[id],
			}
		})

		// 如果不存在，则将任务信息和全量的控制面下项目信息生成放入map中
		if projectControlPlanesMap[name] == nil {
			for _, cp := range fullControlPlanes {
				info := &release_ticketpb.ControlPlaneProjectInfo{
					ControlPlane:               cp,
					GitChangeInfo:              &release_ticketpb.GitChangeInfo{},
					ScmArtifacts:               make([]*change_itempb.SCMArtifact, 0),
					ControlPlanePipelineStatus: release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_PENDING,
				}

				_, itemHasCP := scmArtifactsCPMap[cp.String()]
				_, taskHasCP := taskScmInfoMap[cp.String()]
				var ok bool
				info.ScmMergeBuildInfo, ok = scmMergeInfosCPMap[cp.String()]
				if !ok && cp != sharedpb.ControlPlane_CONTROL_PLANE_US_TTP && cp != sharedpb.ControlPlane_CONTROL_PLANE_TTP {
					info.ScmMergeBuildInfo = defaultMergeBuildInfo
				}

				if taskHasCP {
					info.DevTasks = devTasks
				}

				// 发布区项目有这个控制面，说明已生成流水线
				if itemHasCP {
					info.ScmArtifacts = scmArtifactsCPMap[cp.String()]
					info.ControlPlanePipelineStatus = release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_EXIST
				} else {
					// 如果发布区的项目没有这个控制面，但开发任务有 说明发布单没选这个控制面，待生成
					taskCPSCMInfo := make([]*dev.SCMInfo, 0)
					if taskHasCP {
						taskCPSCMInfo = taskScmInfoMap[cp.String()]
					} else {
						// 如果开发任务和发布区都没有这个控制面，则从开发任务其他控制面拿一个，用来后续判断
						for _, scmInfo := range taskScmInfoMap {
							taskCPSCMInfo = scmInfo
							break
						}
					}
					info.ScmArtifacts = slicex.Map(taskCPSCMInfo, func(scm *dev.SCMInfo) *change_itempb.SCMArtifact {
						version := ""
						reversion := ""
						if scm.PubBase == dev.SCMPubBase_SCM_PUB_BASE_VERSION {
							version = scm.Revision
						} else if scm.PubBase == dev.SCMPubBase_SCM_PUB_BASE_BRANCH && scm.GetIsMain() {
							if integrationBranch == branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_GIT_DEFAULT.String() {
								gitDBMap, err := release_ticket_helper.GetDefaultBranchMapByRt(ctx, rtID, []redisrepository.ProjectGitItem{
									{
										GitName: scm.GetGitRepoName(),
									},
								})
								if err != nil {
									logs.CtxError(ctx, "get git default branch failed, git_repo_name: %s, err: %s", scm.GetGitRepoName(), err.Error())
									return nil
								}
								reversion = gitDBMap[scm.GetGitRepoName()]
							} else {
								reversion = integrationBranch
							}
						}

						// 注意这里没有填充gitRepoID， 因为开发任务那边没存，需要后续调app center接口后填充
						return &change_itempb.SCMArtifact{
							IsMain:      scm.GetIsMain(),
							PubBase:     sharedpb.ScmPubBase(scm.PubBase),
							ScmId:       int64(scm.Id),
							ScmName:     scm.Name,
							Version:     version,
							Revision:    reversion,
							GitRepoName: scm.GetGitRepoName(),
						}
					})
				}

				controlPlaneProject.ProjectInfos = append(controlPlaneProject.ProjectInfos, info)
			}

			projectControlPlanesMap[name] = controlPlaneProject
			continue
		}
		// 如果存在，则只更新开发任务和ScmArtifacts
		for _, info := range projectControlPlanesMap[name].GetProjectInfos() {
			// 集成区change item有这个控制面，说明已生成流水线
			if _, itemHasCP := scmArtifactsCPMap[info.GetControlPlane().String()]; itemHasCP {
				info.ScmArtifacts = scmArtifactsCPMap[info.GetControlPlane().String()]
				info.ControlPlanePipelineStatus = release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_EXIST
			}

			if _, taskHasCP := taskScmInfoMap[info.GetControlPlane().String()]; taskHasCP {
				info.DevTasks = devTasks
			}
		}
	}

	g := new(errgroup.Group)
	// 遍历map，获取git info，判断是否可以生成流水线
	projects := make([]*release_ticketpb.ControlPlaneProject, 0)
	for _, p := range projectControlPlanesMap {
		project := &release_ticketpb.ControlPlaneProject{
			Project:      p.GetProject(),
			ProjectInfos: p.GetProjectInfos(),
		}

		g.Go(func() error {
			// 待生成流水线的项目
			pendingPipelineProjectMap := make(map[sharedpb.ControlPlane]*release_ticketpb.ControlPlaneProjectInfo, 0)
			for _, info := range project.GetProjectInfos() {
				// 没生成，则需要对比git仓和依赖仓等信息
				if info.GetControlPlanePipelineStatus() != release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_EXIST {
					pendingPipelineProjectMap[info.GetControlPlane()] = info
					continue
				}

				// 如果已生成，则直接填充git info等信息
				gitChangeInfo, err := h.GetMainRepoGitChangeInfo(ctx, &change_itempb.ChangeItemArtifact{
					ScmArtifacts: info.GetScmArtifacts(),
				})
				// 单个项目的git info获取失败不阻塞整个获取
				if err != nil {
					logs.CtxError(ctx, "GetMainRepoGitChangeInfo err=%s", err.Error())
					continue
				}

				info.GitChangeInfo = gitChangeInfo
			}

			appCenterProjectInfo, err := h.changeItemSvc.GetAppCenterProjectInfo(ctx,
				fullControlPlanes,
				project.GetProject().GetProjectType(),
				project.GetProject().GetProjectUniqueId())
			if err != nil {
				return bits_err.RELEASETICKET.ErrCallAppCenterComponent.AddErrMsg("get app center project info failed").AddOrPass(ctx, err)
			}

			if appCenterProjectInfo == nil {
				return nil
			}

			for cp, info := range pendingPipelineProjectMap {
				controlPlaneDetail := &appcenter.ProjectControlPlaneDetail{}
				for _, detail := range appCenterProjectInfo.GetControlPlaneDetail() {
					if detail.GetControlPlane().String() == cp.String() {
						controlPlaneDetail = detail
						break
					}
				}

				if len(controlPlaneDetail.GetScmInfo()) != 0 {
					h.updateProjectByAppCenter(ctx, controlPlaneDetail, info, project.GetProject())
				} else {
					// 如果为nil，说明项目不支持该控制面
					info.GitChangeInfo = nil
					info.ScmArtifacts = []*change_itempb.SCMArtifact{}
					info.ScmMergeBuildInfo = nil
					info.ControlPlanePipelineStatus = release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_DISABLE
				}
			}

			projects = append(projects, project)
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		return nil, err
	}

	devTaskControlPlaneInfos, rtControlPlaneInfos := genControlPlaneFittedCheckInfo(devtaskCps, fullControlPlanes, projects)
	return &release_ticketpb.GetReleaseTicketProjectDetailResp{
		DevTaskControlPlaneInfos: devTaskControlPlaneInfos,
		RtControlPlaneInfos:      rtControlPlaneInfos,
		Projects:                 projects,
	}, nil
}

// updateProjectInfoByAppCenter 判断待生成的project info的git和依赖是否和线上的一致并更新
func (h *releaseTicketSvc) updateProjectByAppCenter(ctx context.Context, detail *appcenter.ProjectControlPlaneDetail, info *release_ticketpb.ControlPlaneProjectInfo, project *change_itempb.ChangeItemDeployTarget) {
	projectMainSCM, _ := slicex.Find(info.GetScmArtifacts(), func(artifact *change_itempb.SCMArtifact) bool {
		return artifact.GetIsMain()
	})

	appCenterMainSCM, _ := slicex.Find(detail.GetScmInfo(), func(scm *appcenter.SCMInfo) bool {
		return scm.GetIsMain()
	})

	scmId, err := strconv.ParseInt(appCenterMainSCM.GetId(), 10, 64)
	if err != nil {
		logs.CtxError(ctx, "ParseInt failed, error: %v，scmId id: %s", err, appCenterMainSCM.GetId())
		return
	}
	gitRepoId, err := strconv.ParseInt(appCenterMainSCM.GetGitRepoId(), 10, 64)
	if err != nil {
		logs.CtxError(ctx, "ParseInt failed, error: %v，repo id: %s", err, appCenterMainSCM.GetGitRepoId())
		return
	}

	// 主仓不一致，则无法生成流水线
	if appCenterMainSCM.GetGitRepoName() != projectMainSCM.GetGitRepoName() {
		info.ControlPlanePipelineStatus = release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_DISABLE
		projectMainSCM = &change_itempb.SCMArtifact{
			IsMain:      true,
			ScmId:       scmId,
			ScmName:     appCenterMainSCM.GetName(),
			GitRepoName: appCenterMainSCM.GetGitRepoName(),
			GitRepoId:   gitRepoId,
		}
		return
	}

	// 一致，则填充git info
	info.ControlPlanePipelineStatus = release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_PENDING
	if !utils.IsTTPControlPlane(info.GetControlPlane()) {
		// usttp 不处理，因为
		projectMainSCM.GitRepoId = gitRepoId
	}
	gitChangeInfo, err := h.GetMainRepoGitChangeInfo(ctx, &change_itempb.ChangeItemArtifact{
		ScmArtifacts: info.GetScmArtifacts(),
	})
	if err != nil {
		logs.CtxError(ctx, "GetMainRepoGitChangeInfo failed, error: %s", err.Error())
		return
	}
	info.GitChangeInfo = gitChangeInfo

	// 只有tce用校验依赖
	if project.GetProjectType() != sharedpb.ProjectType_PROJECT_TYPE_TCE {
		return
	}

	// 检查依赖版本
	// 如果是ttp控制面，并且集成区有依赖，直接用集成区的
	if info.GetControlPlane() == sharedpb.ControlPlane_CONTROL_PLANE_TTP && len(info.GetDevTasks()) > 0 {
		return
	}

	dependency, err := h.GenChangeItemDependency(ctx, projectMainSCM.GetGitRepoName(), detail, info, project)
	if err != nil {
		logs.CtxError(ctx, "genChangeItemDependency failed, error: %s", err.Error())
		return
	}

	info.ScmArtifacts = make([]*change_itempb.SCMArtifact, 0)
	info.ScmArtifacts = append(info.ScmArtifacts, projectMainSCM)
	info.ScmArtifacts = append(info.ScmArtifacts, dependency...)

	return
}

func (h *releaseTicketSvc) GenChangeItemDependency(ctx context.Context,
	mainGitRepoName string,
	detail *appcenter.ProjectControlPlaneDetail,
	info *release_ticketpb.ControlPlaneProjectInfo,
	project *change_itempb.ChangeItemDeployTarget) (dependency []*change_itempb.SCMArtifact, err error) {
	dependency = make([]*change_itempb.SCMArtifact, 0)

	if info.GetControlPlane() == sharedpb.ControlPlane_CONTROL_PLANE_TTP {
		scmInfo, err := h.crossBorderSDK.GetScmTceServiceDependentRepoVersion(ctx, &crossborderpb.GetScmTceServiceDependentRepoVersionReq{
			Psm:    project.GetProjectUniqueId(),
			AppEnv: tce_config.APP_ENV_PROD,
			Idc:    tce_config.IDC_TTP,
			TceEnv: tce_config.ENV_PROD,
		})
		if err != nil {
			return dependency, err
		}

		dependency = slicex.MapWithFilter(detail.GetScmInfo(), func(appSCM *appcenter.SCMInfo) (*change_itempb.SCMArtifact, bool) {
			if mainGitRepoName == appSCM.GetGitRepoName() {
				return nil, false
			}

			scmID, err := strconv.ParseInt(appSCM.GetId(), 10, 64)
			if err != nil {
				return nil, false
			}

			ttpScmItemOpt, index := slicex.Find(scmInfo.Data, func(scm *crossborderpb.ScmItemInfo) bool {
				return scmID == int64(scm.GetRepos())
			})
			if index == -1 {
				return nil, false
			}

			return &change_itempb.SCMArtifact{
				IsMain:      false,
				PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
				ScmId:       scmID,
				ScmName:     appSCM.GetName(),
				Version:     ttpScmItemOpt.GetVersion(),
				GitRepoName: appSCM.GetGitRepoName(),
			}, true
		})

		return dependency, nil
	}

	dependency = slicex.MapWithFilter(detail.GetScmInfo(), func(appSCM *appcenter.SCMInfo) (*change_itempb.SCMArtifact, bool) {
		if mainGitRepoName == appSCM.GetGitRepoName() {
			return nil, false
		}

		scmID, err := strconv.ParseInt(appSCM.GetId(), 10, 64)
		if err != nil {
			return nil, false
		}

		// 对比集成区依赖版本
		integrationSCM, index := slicex.Find(info.GetScmArtifacts(), func(scm *change_itempb.SCMArtifact) bool {
			return scmID == scm.GetScmId()
		})

		// 一致则直接返回集成区的依赖
		if index > -1 {
			return integrationSCM, true
		}

		latestVersion, has, err := scm.CurrentIdcScmSvc.GetLatestVersionStringBySCMName(ctx, appSCM.GetGitRepoName())
		if err != nil || !has {
			return nil, false
		}

		return &change_itempb.SCMArtifact{
			IsMain:      false,
			PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
			ScmId:       scmID,
			ScmName:     appSCM.GetName(),
			Version:     latestVersion,
			GitRepoName: appSCM.GetGitRepoName(),
		}, true
	})

	return dependency, nil
}

func genControlPlaneFittedCheckInfo(devtaskCps map[dev.ControlPlane]dev.ControlPlane, fullControlPlanes []sharedpb.ControlPlane, projects []*release_ticketpb.ControlPlaneProject) (
	devTaskControlPlaneInfos, rtControlPlaneInfos []*release_ticketpb.ControlPlaneFittedCheckInfo) {
	ControlPlaneInfosMap := make(map[sharedpb.ControlPlane]*release_ticketpb.ControlPlaneFittedCheckInfo, 0)
	for _, cp := range fullControlPlanes {
		ControlPlaneInfosMap[cp] = &release_ticketpb.ControlPlaneFittedCheckInfo{
			ControlPlane: cp,
		}
	}

	devTaskControlPlaneInfos = make([]*release_ticketpb.ControlPlaneFittedCheckInfo, 0)
	rtControlPlaneInfos = make([]*release_ticketpb.ControlPlaneFittedCheckInfo, 0)

	for _, p := range projects {
		for _, projectInfo := range p.GetProjectInfos() {
			cp := projectInfo.GetControlPlane()
			if projectInfo.GetControlPlanePipelineStatus() != release_ticketpb.ControlPlanePipelineStatus_CONTROL_PLANE_PIPELINE_STATUS_EXIST {
				ControlPlaneInfosMap[cp].AbnormalProjectNum++
			} else {
				ControlPlaneInfosMap[cp].NormalProjectNum++
			}
		}
	}

	for cp, info := range ControlPlaneInfosMap {
		rtControlPlaneInfos = append(rtControlPlaneInfos, info)

		for devtaskCP := range devtaskCps {
			if devtaskCP.String() == cp.String() {
				devTaskControlPlaneInfos = append(devTaskControlPlaneInfos, info)
				break
			}
		}
	}

	gslice.SortBy(devTaskControlPlaneInfos, func(a, b *release_ticketpb.ControlPlaneFittedCheckInfo) bool {
		return a.ControlPlane < b.ControlPlane
	})
	gslice.SortBy(rtControlPlaneInfos, func(a, b *release_ticketpb.ControlPlaneFittedCheckInfo) bool {
		return a.ControlPlane < b.ControlPlane
	})

	return devTaskControlPlaneInfos, rtControlPlaneInfos
}
