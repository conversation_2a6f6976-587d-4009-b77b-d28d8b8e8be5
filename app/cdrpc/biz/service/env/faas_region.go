package env

import (
	"context"

	rtprojectdomain "code.byted.org/devinfra/hagrid/app/cdrpc/biz/domain/rt_project"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/envpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pkg/env"
	"code.byted.org/devinfra/hagrid/pkg/envplatform"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
)

func (e envService) GetSupportedFaaSRegions(ctx context.Context, req *envpb.GetSupportFaaSRegionsReq) (*envpb.GetSupportFaaSRegionsResp, error) {
	resp := new(envpb.GetSupportFaaSRegionsResp)
	regionList, err := e.envPlatformSDK.GetFAASSupportRegionList(ctx, envplatform.GetFAASSupportRegionRequest{
		EnvName:     req.GetEnvName(),
		StandardEnv: req.GetStandardEnv(),
		Service:     req.GetServicePsm(),
		EnvType:     env.EnvType(req.EnvType),
	}, cdauth.JWT(ctx))
	if err != nil {
		logs.CtxInfo(ctx, err.Error())
		return nil, bits_err.THIRDPARTYENV.ErrCallEnvAPI.PassThrough(err)
	}
	resp.Regions = slicex.Map(regionList, func(from envplatform.FaaSRegion) *envpb.FaaSRegion {
		return &envpb.FaaSRegion{
			Name:                     from.Name,
			RegionDisplay:            from.RegionDisplay,
			RegionClusterDefaultName: rtprojectdomain.GetFaaSDefaultClusterNameForRegion(from.Name),
		}
	})
	resp.Regions = filterBitsSupportRegions(resp.Regions, req.GetControlPanel(), env.EnvType(req.EnvType))
	return resp, nil
}

// filterBitsSupportRegions 根据 bits 场景过滤掉不支持的 region
func filterBitsSupportRegions(regions []*envpb.FaaSRegion, controlPlane sharedpb.ControlPlane, envType env.EnvType) []*envpb.FaaSRegion {
	if controlPlane == sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED {
		return regions
	}

	// boe环境，多环境的接口会返回海内外的region，再基于研发流程控制面做一次过滤
	if envType == env.EnvTypeBOEFeature || envType == env.EnvTypeBOEBase || envType == env.EnvTypeBOELane {
		if controlPlane == sharedpb.ControlPlane_CONTROL_PLANE_CN {
			return slicex.Filter(regions, func(from *envpb.FaaSRegion) bool {
				return from.RegionDisplay == "BOE"
			})
		} else {
			return slicex.Filter(regions, func(from *envpb.FaaSRegion) bool {
				return from.RegionDisplay == "BOEI18n"
			})
		}
	}

	// 目前us-ttp仅支持部署该region，单选，如果要下掉和@liuchenxing确认
	if envType == env.EnvTypePPE && controlPlane == sharedpb.ControlPlane_CONTROL_PLANE_US_TTP {
		return slicex.Filter(regions, func(from *envpb.FaaSRegion) bool {
			return from.Name == "us-ttp"
		})
	}
	return regions
}
