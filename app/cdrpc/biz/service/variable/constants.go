package variable

const (
	TemplateTagTypeActivityPhase = "activity_phase"
)

// 新旧通用的系统变量Key
const (
	VariablesKeyWorkItems                             = "work_items"
	VariablesKeyFeatureIDs                            = "feature_ids"
	VariablesKeyTrainRegion                           = "train_region"
	VariablesKeyRegion                                = "region"
	VariablesKeySdlcInfoList                          = "sdlc_info_list"
	VariablesKeySdlcInfo                              = "sdlc_info"
	VariablesKeyPSM                                   = "psm"
	VariablesKeyChangePSM                             = "change_psm"
	VariablesKeyBranch                                = "branch"
	VariablesKeyInstanceID                            = "instance_id"
	VariablesKeyTrainID                               = "train_id"
	VariablesKeyTicketID                              = "ticket_id"
	VariablesKeyInstanceType                          = "instance_type"
	VariablesKeyTrainIssueIDList                      = "train_issue_id_list"
	VariablesKeyTrainFeatureIDList                    = "train_feature_id_list"
	VariablesKeyTrainName                             = "train_name"
	VariablesKeyBOEEnvName                            = "boe_env_name"
	VariablesKeyPPEEnvName                            = "ppe_env_name"
	VariablesKeyTicketName                            = "ticket_name"
	VariablesKeyQaRole                                = "qa_role"
	VariablesKeyReleaseTrainURL                       = "release_train_url"
	VariablesKeyReleaseTicketID                       = "release_ticket_id"
	VariablesKeyReleaseTicketName                     = "release_ticket_name"
	VariablesKeyDevelopmentTaskID                     = "development_task_id"
	VariablesKeyDevelopmentTaskName                   = "development_task_name"
	VariablesKeyDevelopmentTaskListOfIntegrationStage = "development_task_list_of_integration_stage"
	VariablesKeyMainPipelineID                        = "main_pipeline_id"
	VariablesKeyIntegrationBranch                     = "integration_branch" // 集成分支 暂不注册为系统变量
)

// 变量前缀
const (
	SysVarPrefixReleaseTicket   = "sys.release_ticket."
	SysVarPrefixDevelopmentTask = "sys.development_task."
	SysVarPrefixMessage         = "sys.message."
	SysVarPrefixWorkflow        = "sys.workflow."
	SysVarPrefixWorkspace       = "sys.workspace."
	SysVarPrefixBuild           = "sys.build."
	SysVarPrefix                = "sys."
	CustomVarPrefix             = "custom."
)

// 开发任务系统变量key
const (
	SysVarKeyDevelopmentTaskTitle           = SysVarPrefixDevelopmentTask + "title"
	SysVarKeyDevelopmentTaskTestApprover    = SysVarPrefixDevelopmentTask + "test_approver"
	SysVarKeyDevelopmentTaskTestApproverStr = SysVarPrefixDevelopmentTask + "test_approver_str"
	SysVarKeyDevelopmentTaskDeveloper       = SysVarPrefixDevelopmentTask + "developer"
)

// 发布单系统变量key
const (
	SysVarKeyReleaseTicketTitle                                 = SysVarPrefixReleaseTicket + "title"
	SysVarKeyReleaseTicketTestApprover                          = SysVarPrefixReleaseTicket + "test_approver"
	SysVarKeyReleaseTicketTestApproverStr                       = SysVarPrefixReleaseTicket + "test_approver_str"
	SysVarKeyReleaseTicketReleaseApprover                       = SysVarPrefixReleaseTicket + "release_approver"
	SysVarKeyReleaseTicketDeveloper                             = SysVarPrefixReleaseTicket + "developer"
	SysVarKeyReleaseTicketControlPlane                          = SysVarPrefixReleaseTicket + "control_plane"
	SysVarKeyReleaseTicketURL                                   = SysVarPrefixReleaseTicket + "url"
	SysVarKeyReleaseTicketCreationDate                          = SysVarPrefixReleaseTicket + "creation_date"
	SysVarKeyReleaseTicketScheduledFreezeDate                   = SysVarPrefixReleaseTicket + "scheduled_freeze_date"
	SysVarKeyReleaseTicketScheduledReleaseDate                  = SysVarPrefixReleaseTicket + "scheduled_release_date"
	SysVarKeyReleaseTicketDevelopmentTaskListOfIntegrationStage = SysVarPrefixReleaseTicket + "development_task_list_of_integration_stage"
	SysVarKeyReleaseTicketKeyIntegrationBranch                  = SysVarPrefixReleaseTicket + VariablesKeyIntegrationBranch
)

// 空间系统变量key
const (
	SysVarKeyWorkspaceName = SysVarPrefixWorkspace + "name"
)

// 构建系统变量key
const (
	SysVarKeyBuildMainPipeline = SysVarPrefixBuild + VariablesKeyMainPipelineID
)

// 通知消息模版系统变量key
const (
	SysVarKeyMessageReleaseTicketCreate      = SysVarPrefixMessage + "release_ticket_create"
	SysVarKeyMessageReleaseTicketIntegration = SysVarPrefixMessage + "release_ticket_integration"
	SysVarKeyMessageReleaseList              = SysVarPrefixMessage + "release_list"
	SysVarKeyMessageReleaseComplete          = SysVarPrefixMessage + "release_complete"
)

// NotificationVarList 通知消息支持的变量列表
var NotificationVarList = []string{
	SysVarKeyReleaseTicketTitle,
	SysVarKeyReleaseTicketTestApprover,
	SysVarKeyReleaseTicketReleaseApprover,
	SysVarKeyReleaseTicketDeveloper,
	SysVarKeyReleaseTicketControlPlane,
	SysVarKeyReleaseTicketURL,
	SysVarKeyReleaseTicketCreationDate,
	SysVarKeyReleaseTicketScheduledFreezeDate,
	SysVarKeyReleaseTicketScheduledReleaseDate,
	SysVarKeyWorkspaceName,
}

// NotificationVarMap 通知消息支持的变量Map
var NotificationVarMap = map[string]bool{
	// 发布单系统变量
	SysVarKeyReleaseTicketTitle:                true,
	SysVarKeyReleaseTicketTestApprover:         true,
	SysVarKeyReleaseTicketReleaseApprover:      true,
	SysVarKeyReleaseTicketDeveloper:            true,
	SysVarKeyReleaseTicketControlPlane:         true,
	SysVarKeyReleaseTicketURL:                  true,
	SysVarKeyReleaseTicketCreationDate:         true,
	SysVarKeyReleaseTicketScheduledFreezeDate:  true,
	SysVarKeyReleaseTicketScheduledReleaseDate: true,
	// 空间系统变量
	SysVarKeyWorkspaceName: true,
	// 消息模版变量
	SysVarKeyMessageReleaseTicketIntegration: true,
	SysVarKeyMessageReleaseList:              true,
}

// BranchVarMap 分支支持的变量Map
var BranchVarMap = map[string]bool{
	// 发布单系统变量
	SysVarKeyReleaseTicketTitle:                true,
	SysVarKeyReleaseTicketTestApprover:         true,
	SysVarKeyReleaseTicketReleaseApprover:      true,
	SysVarKeyReleaseTicketDeveloper:            true,
	SysVarKeyReleaseTicketControlPlane:         true,
	SysVarKeyReleaseTicketURL:                  true,
	SysVarKeyReleaseTicketCreationDate:         true,
	SysVarKeyReleaseTicketScheduledFreezeDate:  true,
	SysVarKeyReleaseTicketScheduledReleaseDate: true,
	// 空间系统变量
	SysVarKeyWorkspaceName: true,
	// 开发任务系统变量
	SysVarKeyDevelopmentTaskTitle:        true,
	SysVarKeyDevelopmentTaskTestApprover: true,
	SysVarKeyDevelopmentTaskDeveloper:    true,
}

//
//// PipelineSupportedSystemProviderList 流水线支持的系统变量域
//var PipelineSupportedSystemProviderList = []varstorepb.SysProvider{
//	varstorepb.SysProvider_SYS_PROVIDER_BUILD,
//	varstorepb.SysProvider_SYS_PROVIDER_WORKFLOW,
//	varstorepb.SysProvider_SYS_PROVIDER_RELEASE_TICKET,
//	varstorepb.SysProvider_SYS_PROVIDER_DEVELOPMENT_TASK,
//}

// 构建操作
const (
	BuildActionCancel = "cancel"
)

const (
	maxNumOfQueryVarGroupAssociationKey = 10
)

const (
	spaceExtraKeyUseByteTreeVars = "useByteTreeVars"
)

const ByteTreeVarGroupWorkspaceID uint64 = 10
