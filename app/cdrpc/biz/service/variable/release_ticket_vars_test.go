package variable

import (
	"context"
	"reflect"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	"code.byted.org/bits/monkey"
	bytetreeProvider "code.byted.org/canal/provider/bytetree"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb/varstoreservice_mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/app/apprpc_mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/appcenter"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	spacerpcpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/space/rpcpb/spacerpcapi_mock"
	"code.byted.org/sre/bytetree_go_sdk/schema"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	repositorymock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	changItemMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
)

type ReleaseTicketVarSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
	db     *gorm.DB
}

func (t *ReleaseTicketVarSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	db := testfactory.NewUnitTestDB(context.Background(), &entity.DBReleaseTicket{}, &entity.DBStagePipeline{})
	t.ctx = mysql.ContextWithUnitTestDB(t.ctx, db)
	t.db = db
	mysql.DefaultDB = db
}

func (t *ReleaseTicketVarSuite) TearDownTest() {
	t.cancel()
	monkey.UnpatchAll()
}

func TestCreate(t *testing.T) {
	suite.Run(t, new(ReleaseTicketVarSuite))
}

func (t *ReleaseTicketVarSuite) Test_releaseTicketVarSVC_GetSystemVars() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	mockVarstoreCli.EXPECT().
		QueryVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupResp{Groups: []*varstorepb.VarGroup{{
			GroupId:        1,
			Version:        2,
			VarDefinitions: []*varstorepb.VarDefinition{{Name: "foo"}},
		}}}, nil).AnyTimes()
	t.Run("success", func() {
		r := &releaseTicketVarSVC{
			varstoreCli: mockVarstoreCli,
		}
		got, err := r.GetSystemVars(t.ctx)
		t.NoError(err)
		t.True(reflect.DeepEqual(got, []*release_ticket_sharedpb.Variable{{
			Definition: &varstorepb.VarDefinition{Name: "foo"},
			GroupId:    1,
			Version:    2,
		}}))
	})

}

func (t *ReleaseTicketVarSuite) Test_getReleaseTicketCustomVars() {
	t.db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID:  1,
		VarsAssignmentID: 999,
		WorkflowID:       10000,
	})
	t.Run("no diff", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()

		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{{
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
						GroupId: 1,
						Version: 1},
				}},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 1,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{{
			Definition: &varstorepb.VarDefinition{
				Name:     "var1",
				FullName: "custom.var1",
			},
			GroupId:          1,
			Version:          1,
			AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
		}})
	})

	t.Run("vars added", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{{
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
						GroupId: 1,
						Version: 1},
				}},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 2,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
					},
					{
						GroupId: 1,
						Version: 2,
						Definition: &varstorepb.VarDefinition{
							Name:     "var2",
							FullName: "custom.var2",
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var1",
					FullName: "custom.var1",
				},
				GroupId:          1,
				Version:          2,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var2",
					FullName: "custom.var2",
				},
				GroupId:          1,
				Version:          2,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
		})
	})

	t.Run("vars kind change", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{{
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
							Kind:     varstorepb.VarKind_VAR_KIND_BOOL,
						},
						GroupId: 1,
						Version: 1},
				}},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 2,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
							Kind:     varstorepb.VarKind_VAR_KIND_ARRAY,
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var1",
					FullName: "custom.var1",
					Kind:     varstorepb.VarKind_VAR_KIND_ARRAY,
				},
				GroupId:          1,
				Version:          2,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
		})
	})

	t.Run("vars UI kind change", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{{
					DefSnapshot: &varstorepb.VarDefSnapshot{
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
							UiOption: &varstorepb.VarUIOption{
								InputType: varstorepb.UIInputType_UI_INPUT_TYPE_MEEGO_TASK,
							},
						},
						GroupId: 1,
						Version: 1},
				}},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 2,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
							UiOption: &varstorepb.VarUIOption{
								InputType: varstorepb.UIInputType_UI_INPUT_TYPE_TEXT,
							},
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var1",
					FullName: "custom.var1",
					UiOption: &varstorepb.VarUIOption{
						InputType: varstorepb.UIInputType_UI_INPUT_TYPE_TEXT,
					},
				},
				GroupId:          1,
				Version:          2,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
		})
	})

	t.Run("var group change", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{
					{
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Name:     "var1",
								FullName: "custom.var1",
							},
							GroupId: 1,
							Version: 1},
					},
					{
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Name:     "var2",
								FullName: "custom.var2",
							},
							GroupId: 2,
							Version: 1},
					},
				},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 1,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
					},
					{
						GroupId: 3,
						Version: 1,
						Definition: &varstorepb.VarDefinition{
							Name:     "var3",
							FullName: "custom.var3",
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var1",
					FullName: "custom.var1",
				},
				GroupId:          1,
				Version:          1,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var2",
					FullName: "custom.var2",
				},
				GroupId:          2,
				Version:          1,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
		})
	})

	t.Run("var group deleted", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()

		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{
					{
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Name:     "var1",
								FullName: "custom.var1",
							},
							GroupId: 1,
							Version: 1},
					},
					{
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Name:     "var2",
								FullName: "custom.var2",
							},
							GroupId: 2,
							Version: 1},
					},
				},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 1,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var1",
					FullName: "custom.var1",
				},
				GroupId:          1,
				Version:          1,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var2",
					FullName: "custom.var2",
				},
				GroupId:          2,
				Version:          1,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
		})
	})

	t.Run("new var group", func() {
		ctrl := gomock.NewController(&testing.T{})
		defer ctrl.Finish()
		mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
		mockVarstoreCli.EXPECT().
			GetAssignment(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.GetAssignmentResp{Assignment: &varstorepb.Assignment{
				AssignmentId: 1,
				Assignments: []*varstorepb.VarAssignment{
					{
						DefSnapshot: &varstorepb.VarDefSnapshot{
							Definition: &varstorepb.VarDefinition{
								Name:     "var1",
								FullName: "custom.var1",
							},
							GroupId: 1,
							Version: 1},
					},
				},
			}}, nil).Times(1)
		mockVarstoreCli.EXPECT().
			QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&varstorepb.QueryVarGroupByAssociationResp{
				FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
					{
						GroupId: 1,
						Version: 1,
						Definition: &varstorepb.VarDefinition{
							Name:     "var1",
							FullName: "custom.var1",
						},
					},
					{
						GroupId: 2,
						Version: 1,
						Definition: &varstorepb.VarDefinition{
							Name:     "var2",
							FullName: "custom.var2",
						},
					},
				},
			}, nil).Times(1)

		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}
		got, err := r.getReleaseTicketCustomVars(t.ctx, 1)
		t.NoError(err)
		DeepEqualIgnoreOrder(got, []*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var1",
					FullName: "custom.var1",
				},
				GroupId:          1,
				Version:          1,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "var2",
					FullName: "custom.var2",
				},
				GroupId:          2,
				Version:          1,
				AssociationItems: []*release_ticket_sharedpb.AssociationItem{newReleaseTicketAssociationItem(10000)},
			},
		})
	})
}

func (t *ReleaseTicketVarSuite) Test_getWorkflowCustomVars() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	mockVarstoreCli.EXPECT().
		QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupByAssociationResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId:     1,
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_RELEASE_TICKET,
				},
			},
			FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
				{
					GroupId: 1,
					Version: 2,
					Definition: &varstorepb.VarDefinition{
						Name: "key1",
					},
				},
				{
					GroupId: 2,
					Version: 2,
					Definition: &varstorepb.VarDefinition{
						Name: "key1",
					},
				},
			},
		}, nil).AnyTimes()
	t.db.Create(&entity.DBReleaseTicket{
		ReleaseTicketID:  1,
		VarsAssignmentID: 999,
		WorkflowID:       10000,
	})

	t.Run("success", func() {
		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			releaseTicketDao: repository.NewReleaseTicketDao(),
		}

		got, err := r.getWorkflowCustomVars(t.ctx, 1, 23)
		t.NoError(err)
		t.T().Log(got)
	})
}

func (t *ReleaseTicketVarSuite) Test_getProjectCustomVars() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	mockVarstoreCli.EXPECT().
		QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupByAssociationResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId: 1,
					Name: &varstorepb.StringInMultiLang{
						Value: "name1",
						Cn:    "名称1",
						En:    "name1",
						Lang:  "cn",
					},
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key1",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_PROJECT,
				},
				{
					GroupId: 2,
					Name: &varstorepb.StringInMultiLang{
						Value: "name2",
						Cn:    "名称2",
						En:    "name2",
						Lang:  "cn",
					},
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key2",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_PROJECT,
				},
			},
			FlattenVarSnapshots: []*varstorepb.VarDefSnapshot{
				{
					GroupId: 1,
					Version: 2,
					Definition: &varstorepb.VarDefinition{
						Name: "key1",
					},
				},
				{
					GroupId: 2,
					Version: 1,
					Definition: &varstorepb.VarDefinition{
						Name: "key2",
					},
				},
			},
			Associations: []*varstorepb.VarGroupAssociation{
				{
					GroupId:        1,
					AssociationKey: "project:PROJECT_TYPE_TCE+p.s.m1",
				},
				{
					GroupId:        1,
					AssociationKey: "project:PROJECT_TYPE_TCE+p.s.m2",
				},
				{
					GroupId:        2,
					AssociationKey: "project:PROJECT_TYPE_WEB+p.s.m3",
				},
			},
		}, nil).AnyTimes()
	mockChangeItemSvc := changItemMock.NewMockChangeItemSvc(ctrl)
	mockChangeItemSvc.EXPECT().GetReleaseTicketChangeItems(gomock.Any(), gomock.Any()).
		Return([]*release_ticketpb.ReleaseTicketChangeItem{
			{
				ProjectUniqueId: "p.s.m1",
				ProjectType:     1,
				ProjectName:     "p.s.m1",
				ControlPlane:    1,
			},
			{
				ProjectUniqueId: "p.s.m1",
				ProjectType:     1,
				ProjectName:     "p.s.m1",
				ControlPlane:    2,
			},
			{
				ProjectUniqueId: "p.s.m2",
				ProjectType:     1,
				ProjectName:     "p.s.m2",
			},
			{
				ProjectUniqueId: "p.s.m3",
				ProjectType:     4,
				ProjectName:     "p.s.m3",
			},
		}, nil).AnyTimes()
	mockReleaseTicketDao := repositorymock.NewMockReleaseTicketDao(ctrl)
	mockReleaseTicketDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).
		Return(&entity.DBReleaseTicket{
			ReleaseTicketID: 1,
			WorkspaceID:     1,
		}, nil).AnyTimes()

	t.Run("success without projects", func() {
		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			changeItemSvc:    mockChangeItemSvc,
			releaseTicketDao: mockReleaseTicketDao,
		}

		got, err := r.getProjectCustomVars(t.ctx, &GetProjectCustomVarsReq{
			releaseTicketID: 1,
			scene:           GetVarsSceneTypeReleaseTicket,
		})
		t.NoError(err)
		t.T().Log(got)
		t.Equal(len(got) > 0, true)
	})

	t.Run("success with projects", func() {
		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			changeItemSvc:    mockChangeItemSvc,
			releaseTicketDao: mockReleaseTicketDao,
		}

		got, err := r.getProjectCustomVars(t.ctx, &GetProjectCustomVarsReq{
			releaseTicketID: 1,
			scene:           GetVarsSceneTypePipelineRun,
			projects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectUniqueId: "p.s.m1",
					ProjectType:     1,
					ProjectName:     "p.s.m1",
				},
				{
					ProjectUniqueId: "p.s.m1",
					ProjectType:     1,
					ProjectName:     "p.s.m1",
				},
				{
					ProjectUniqueId: "p.s.m2",
					ProjectType:     1,
					ProjectName:     "p.s.m2",
				},
				{
					ProjectUniqueId: "p.s.m3",
					ProjectType:     4,
					ProjectName:     "p.s.m3",
				},
			},
		})
		t.NoError(err)
		t.T().Log(got)
		t.Equal(len(got) > 0, true)
	})

	t.Run("pipeline run vars without projects", func() {
		r := &releaseTicketVarSVC{
			varstoreCli:      mockVarstoreCli,
			changeItemSvc:    mockChangeItemSvc,
			releaseTicketDao: mockReleaseTicketDao,
		}

		got, err := r.getProjectCustomVars(t.ctx, &GetProjectCustomVarsReq{
			releaseTicketID: 1,
			scene:           GetVarsSceneTypePipelineRun,
		})
		t.NoError(err)
		t.Len(got, 0)
	})
}

func (t *ReleaseTicketVarSuite) Test_GetReleaseTicketVars() {
	r := &releaseTicketVarSVC{}
	t.Run("failed", func() {
		ctrl := gomock.NewController(t.T())
		defer ctrl.Finish()
		spaceCliMock := spacerpcapi_mock.NewMockClient(ctrl)
		r.spaceCli = spaceCliMock
		spaceCliMock.EXPECT().GetSpaceInfoByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&spacerpcpb.SpaceDetail{
				Id:    1,
				Extra: "{\"useByteTreeVars\": false}",
			}, nil)
		got, err := r.GetReleaseTicketVars(t.ctx, &release_ticket_sharedpb.GetReleaseTicketVarsReq{
			WorkspaceId:     1,
			WorkflowId:      0,
			ReleaseTicketId: 0,
			IncludeSysVars:  true,
		})
		t.Error(err)
		t.Nil(got)
	})

	t.Run("byteTreeVars", func() {
		ctrl := gomock.NewController(t.T())
		defer ctrl.Finish()
		spaceCliMock := spacerpcapi_mock.NewMockClient(ctrl)
		r.spaceCli = spaceCliMock
		spaceCliMock.EXPECT().GetSpaceInfoByID(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&spacerpcpb.SpaceDetail{
				Id:    1,
				Extra: "{\"useByteTreeVars\": true}",
			}, nil)
		p2 := mockey.Mock(r.getReleaseTicketCustomVars).Return([]*release_ticket_sharedpb.Variable{
			&release_ticket_sharedpb.Variable{
				Definition: &varstorepb.VarDefinition{
					Name:     "ticket1",
					FullName: "custom.ticket1",
				},
			},
		}, nil).Build()
		defer p2.UnPatch()
		p3 := mockey.Mock(r.getByteTreeCustomVars).Return([]*release_ticket_sharedpb.Variable{
			{
				Definition: &varstorepb.VarDefinition{
					Name:     "byteTreeVar1",
					FullName: "custom.byteTreeVar1",
				},
			},
		}, nil).Build()
		defer p3.UnPatch()

		got, err := r.GetReleaseTicketVars(t.ctx, &release_ticket_sharedpb.GetReleaseTicketVarsReq{
			WorkspaceId:     1,
			WorkflowId:      0,
			ReleaseTicketId: 1,
		})
		t.NoError(err)
		t.NotNil(got)
	})
}

// DeepEqualIgnoreOrder 比较两个protobuf结构体数组是否深度相等，忽略数组顺序
func DeepEqualIgnoreOrder[T proto.Message](a, b []T) bool {
	if len(a) != len(b) {
		return false
	}

	visited := make([]bool, len(b))
	for _, itemA := range a {
		found := false
		for j, itemB := range b {
			if visited[j] {
				continue
			}
			if proto.Equal(itemA, itemB) {
				visited[j] = true
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

func (t *ReleaseTicketVarSuite) Test_getByteTreeCustomVars() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	t.ctx = cdauth.WithJWT(t.ctx, "jwt")
	monkey.Patch(bytetreeProvider.GetNodeByPSM, func(ctx context.Context, psm string, userJwt *string) (*schema.ServiceTreeNodeDetail, bool, error) {
		return &schema.ServiceTreeNodeDetail{
			ServiceTreeNode: schema.ServiceTreeNode{ID: 1, Name: "name"},
		}, true, nil
	})
	monkey.Patch(bytetreeProvider.GetAllParentNodeInfoByID, func(ctx context.Context, nodeID int64) ([]schema.ServiceTreeNodeDetail, bool, error) {
		return []schema.ServiceTreeNodeDetail{
			schema.ServiceTreeNodeDetail{
				ServiceTreeNode: schema.ServiceTreeNode{ID: 0, Name: "根节点"},
			},
		}, true, nil
	})
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)
	mockVarstoreCli.EXPECT().
		QueryVarGroupByAssociation(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(&varstorepb.QueryVarGroupByAssociationResp{
			Groups: []*varstorepb.VarGroup{
				{
					GroupId: 1,
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key1",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_BYTE_TREE,
				},
				{
					GroupId: 2,
					VarDefinitions: []*varstorepb.VarDefinition{
						{
							Name: "key2",
						},
					},
					CustomScope: varstorepb.CustomScope_CUSTOM_SCOPE_BYTE_TREE,
				},
			},
			Associations: []*varstorepb.VarGroupAssociation{
				{
					GroupId:        1,
					AssociationKey: "byte_tree:PROJECT_TYPE_TCE+1",
				},
				{
					GroupId:        2,
					AssociationKey: "byte_tree:PROJECT_TYPE_WEB+10",
				},
			},
		}, nil).AnyTimes()
	mockChangeItemSvc := changItemMock.NewMockChangeItemSvc(ctrl)
	mockChangeItemSvc.EXPECT().GetReleaseTicketChangeItems(gomock.Any(), gomock.Any()).
		Return([]*release_ticketpb.ReleaseTicketChangeItem{
			{
				ProjectUniqueId: "p.s.m1",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ProjectName:     "p.s.m1",
				ControlPlane:    1,
			},
			{
				ProjectUniqueId: "p.s.m1",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ProjectName:     "p.s.m1",
				ControlPlane:    2,
			},
			{
				ProjectUniqueId: "p.s.m2",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
				ProjectName:     "p.s.m2",
			},
			{
				ProjectUniqueId: "p.s.m3",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
				ProjectName:     "p.s.m3",
			},
		}, nil).AnyTimes()

	appcenterCliMock := apprpc_mock.NewMockClient(ctrl)
	appcenterCliMock.EXPECT().GetComponentBytetreeNodeId(gomock.Any(), gomock.Any(), gomock.Any()).Return(
		&app.GetComponentBytetreeNodeIdResp{
			Data: []*app.GetComponentBytetreeNodeIdRespItem{
				&app.GetComponentBytetreeNodeIdRespItem{
					ProjectUniqueId: "p.s.m2",
					ProjectType:     appcenter.ProjectType_PROJECT_TYPE_WEB,
					NodeId:          10,
				},
				&app.GetComponentBytetreeNodeIdRespItem{
					ProjectUniqueId: "p.s.m3",
					ProjectType:     appcenter.ProjectType_PROJECT_TYPE_WEB,
					NodeId:          10,
				},
			},
			Code:    0,
			Message: "",
		}, nil).AnyTimes()

	t.Run("success without projects", func() {
		r := &releaseTicketVarSVC{
			changeItemSvc: mockChangeItemSvc,
			appCenterCli:  appcenterCliMock,
			varstoreCli:   mockVarstoreCli,
		}

		got, err := r.getByteTreeCustomVars(t.ctx, &GetProjectCustomVarsReq{
			releaseTicketID: 1,
			scene:           GetVarsSceneTypeReleaseTicket,
		})
		t.NoError(err)
		t.T().Log(got)
		t.Equal(len(got) > 0, true)
	})

	t.Run("success with projects", func() {
		r := &releaseTicketVarSVC{
			appCenterCli: appcenterCliMock,
			varstoreCli:  mockVarstoreCli,
		}

		got, err := r.getByteTreeCustomVars(t.ctx, &GetProjectCustomVarsReq{
			releaseTicketID: 1,
			scene:           GetVarsSceneTypePipelineRun,
			projects: []*sharedpb.ProjectUniqueKey{
				{
					ProjectUniqueId: "p.s.m1",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
					ProjectName:     "p.s.m1",
				},
				{
					ProjectUniqueId: "p.s.m1",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
					ProjectName:     "p.s.m1",
				},
				{
					ProjectUniqueId: "p.s.m2",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
					ProjectName:     "p.s.m2",
				},
				{
					ProjectUniqueId: "p.s.m3",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
					ProjectName:     "p.s.m3",
				},
			},
		})
		t.NoError(err)
		t.T().Log(got)
		t.Equal(len(got) > 0, true)
	})

	t.Run("pipeline run vars without projects", func() {
		r := &releaseTicketVarSVC{
			changeItemSvc: mockChangeItemSvc,
			appCenterCli:  appcenterCliMock,
			varstoreCli:   mockVarstoreCli,
		}

		got, err := r.getByteTreeCustomVars(t.ctx, &GetProjectCustomVarsReq{
			releaseTicketID: 1,
			scene:           GetVarsSceneTypePipelineRun,
		})
		t.NoError(err)
		t.Len(got, 0)
	})
}
