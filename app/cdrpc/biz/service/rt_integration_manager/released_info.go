package rt_integration_manager

import (
	"context"
	"fmt"
	"strconv"
	"sync"

	"golang.org/x/sync/errgroup"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	git_server_gen "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/appcenter"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/mono_managerpb"
	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/rt_integration_managerpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/gitpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bytedance_bits_git_server/kitex_gen/bytedance/bits/git_server"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	cdutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
)

type CompareRef struct {
	CodeBaseId int64
	Source     string
	Target     string
}
type MonoProject struct {
	ProjectUniqueId string
	ProjectType     sharedpb.ProjectType
}

// 获取 changeItems 最近发布成功的发布单和发布 commit
func (r *rtIntegrationManager) GetChangeItemsLatestReleasedInfo(ctx context.Context, req *release_ticketpb.GetChangeItemsLatestReleasedInfoReq) (resp *release_ticketpb.GetChangeItemsLatestReleasedInfoResp, err error) {
	// 批量获取
	mu := sync.Mutex{}
	g := errgroup.Group{}

	latestReleasedInfo := make([]*release_ticketpb.ChangeItemLatestReleasedInfo, 0)
	for _, c := range req.GetChangeItems() {
		changeItem := c
		g.Go(func() (err error) {
			defer utils.PanicGuard(ctx)

			changeItemLatestReleasedInfo, err := r.GetChangeItemLatestReleasedInfo(ctx, &release_ticketpb.ReleaseTicketChangeItem{
				ProjectUniqueId: changeItem.GetProjectUniqueId(),
				ProjectType:     changeItem.GetProjectType(),
				ControlPlane:    changeItem.GetControlPlane(),
			})

			if err != nil {
				logs.CtxWarn(ctx, "[GetChangeItemsLatestReleasedInfo] GetChangeItemLatestReleasedInfo: ProjectUniqueID %v, err %v", changeItem.GetProjectUniqueId(), err)
				return nil
			}

			mu.Lock()
			latestReleasedInfo = append(latestReleasedInfo, changeItemLatestReleasedInfo)
			mu.Unlock()

			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// 补全 git commit info 信息，因为 限流，改成 for 串行获取
	for _, info := range latestReleasedInfo {
		if info.GetReleasedCommit().GetId() != "" {
			commitInfo, err := r.gitServerSvc.GetCommit(ctx, info.GetReleasedCommit().GetProjectId(), info.GetReleasedCommit().GetId())
			if err != nil {
				logs.CtxError(ctx, "[GetChangeItemsLatestReleasedInfo] GetCommit %v, err %v", info.GetReleasedCommit().GetId(), err)
			}
			info.ReleasedCommit = CommitToPB(commitInfo)
		}
	}

	return &release_ticketpb.GetChangeItemsLatestReleasedInfoResp{LatestReleasedInfo: latestReleasedInfo}, nil
}

// 获取单个 changeItem ReleasedInfo, 不需要 current rt
func (r *rtIntegrationManager) GetChangeItemLatestReleasedInfo(ctx context.Context, changeItem *release_ticketpb.ReleaseTicketChangeItem) (chanItemLatestReleasedInfo *release_ticketpb.ChangeItemLatestReleasedInfo, err error) {
	projectUniqueId := changeItem.GetProjectUniqueId()
	changeItemLatestReleasedInfo := &release_ticketpb.ChangeItemLatestReleasedInfo{
		ProjectUniqueId:  projectUniqueId,
		ProjectType:      changeItem.GetProjectType(),
		ControlPlane:     changeItem.GetControlPlane(),
		ReleasedCommit:   nil,
		LatestReleasedRt: nil,
	}
	// 1.获取最近发布单
	latestRt, err := r.rtChangeItemManager.GetChangeItemLatestReleasedRt(ctx, changeItem)
	if err != nil {
		// 先不返回错误
		logs.CtxWarn(ctx, "[GetChangeItemsLatestReleasedInfo] projectUniqueID %v get release ticket is err: %v", projectUniqueId, err)
		return changeItemLatestReleasedInfo, nil
	}
	if latestRt == nil {
		logs.CtxInfo(ctx, "[GetChangeItemsLatestReleasedInfo] projectUniqueID %v get release ticket is nil")
		return changeItemLatestReleasedInfo, nil
	}
	changeItemLatestReleasedInfo.LatestReleasedRt = &release_ticketpb.ReleaseTicketDetail{ReleaseTicketId: latestRt.ReleaseTicketID, Name: latestRt.Name, WorkspaceId: latestRt.WorkspaceID}

	// 2.获取最近发布单 commit
	_, prevCommit, err := r.releasedCommitManager.GetChangeItemReleaseCommit(ctx, latestRt.ReleaseTicketID, changeItem)
	if err != nil {
		logs.CtxWarn(ctx, "[GetChangeItemsLatestReleasedInfo] projectUniqueID %v GetChangeItemReleaseCommit err, %v", projectUniqueId, err)
		return changeItemLatestReleasedInfo, nil
	}

	if prevCommit != nil {
		changeItemLatestReleasedInfo.ReleasedCommit = CommitToPB(prevCommit)
		logs.CtxInfo(ctx, "[GetChangeItemsLatestReleasedInfo] projectUniqueID %v, releasedCommit %v", projectUniqueId, prevCommit.GetId())
	}

	return changeItemLatestReleasedInfo, nil
}

// 获取【指定发布单】上的 changeItems 上一次发布成功的发布单和发布 commit
func (r *rtIntegrationManager) GetChangeItemsLatestReleasedInfoInRt(ctx context.Context, releaseTicketId uint64) (resp *release_ticketpb.GetChangeItemsLatestReleasedInfoResp, err error) {
	// 1.获取发布单上 changeItems
	changeItems, err := r.RtChangeItemDao.GetByReleaseTicketID(ctx, releaseTicketId)
	if err != nil {
		logs.CtxError(ctx, "[GetChangeItemsLatestReleasedInfoInRt] GetByReleaseTicketID err: %s", err.Error())
		return nil, err
	}

	// 批量获取
	mu := sync.Mutex{}
	g := errgroup.Group{}
	g.SetLimit(10)

	latestReleasedInfo := make([]*release_ticketpb.ChangeItemLatestReleasedInfo, 0)
	for _, c := range changeItems {
		changeItem := c
		g.Go(func() (err error) {
			defer utils.PanicGuard(ctx)

			changeItemLatestReleasedInfo, err := r.GetChangeItemLatestReleasedInfoInRt(ctx, releaseTicketId, &release_ticketpb.ReleaseTicketChangeItem{
				ProjectUniqueId: changeItem.ProjectUniqueID,
				ProjectType:     sharedpb.ProjectType(sharedpb.ProjectType_value[changeItem.ProjectType]),
				ControlPlane:    sharedpb.ControlPlane(sharedpb.ControlPlane_value[changeItem.ControlPlane]),
			})

			if err != nil {
				logs.CtxWarn(ctx, "[GetChangeItemsLatestReleasedInfo] GetChangeItemLatestReleasedInfo: ProjectUniqueID %v, err %v", changeItem.ProjectUniqueID, err)
				return nil
			}

			mu.Lock()
			latestReleasedInfo = append(latestReleasedInfo, changeItemLatestReleasedInfo)
			mu.Unlock()

			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, err
	}

	// 补全 git commit info 信息，因为 限流，改成 for 串行获取
	for _, info := range latestReleasedInfo {
		if info.GetReleasedCommit().GetId() != "" {
			commitInfo, err := r.gitServerSvc.GetCommit(ctx, info.GetReleasedCommit().GetProjectId(), info.GetReleasedCommit().GetId())
			if err != nil {
				logs.CtxError(ctx, "[GetChangeItemsLatestReleasedInfo] GetCommit %v, err %v", info.GetReleasedCommit().GetId(), err)
			}
			info.ReleasedCommit = CommitToPB(commitInfo)
		}
	}

	return &release_ticketpb.GetChangeItemsLatestReleasedInfoResp{LatestReleasedInfo: latestReleasedInfo}, nil
}

// 获取指定 RT 单个changeItem ReleasedInfo, 需要 RT
func (r *rtIntegrationManager) GetChangeItemLatestReleasedInfoInRt(ctx context.Context, releaseTicketId uint64, changeItem *release_ticketpb.ReleaseTicketChangeItem) (chanItemLatestReleasedInfo *release_ticketpb.ChangeItemLatestReleasedInfo, err error) {
	projectUniqueId := changeItem.GetProjectUniqueId()
	changeItemLatestReleasedInfo := &release_ticketpb.ChangeItemLatestReleasedInfo{
		ProjectUniqueId:  projectUniqueId,
		ProjectType:      changeItem.GetProjectType(),
		ControlPlane:     changeItem.GetControlPlane(),
		ReleasedCommit:   nil,
		LatestReleasedRt: nil,
	}
	// 1.获取最近发布单
	latestRt, err := r.rtChangeItemManager.GetChangeItemPrevReleasedRt(ctx, releaseTicketId, changeItem)
	if err != nil {
		// 先不返回错误
		logs.CtxWarn(ctx, "[GetChangeItemLatestReleasedInfoInRt] projectUniqueID %v get release ticket is err: %v", projectUniqueId, err)
		return changeItemLatestReleasedInfo, nil
	}
	if latestRt == nil {
		logs.CtxInfo(ctx, "[GetChangeItemLatestReleasedInfoInRt] projectUniqueID %v get release ticket is nil")
		return changeItemLatestReleasedInfo, nil
	}
	changeItemLatestReleasedInfo.LatestReleasedRt = &release_ticketpb.ReleaseTicketDetail{
		ReleaseTicketId: latestRt.ReleaseTicketID,
		Name:            latestRt.Name,
		WorkspaceId:     latestRt.WorkspaceID,
		Status:          release_ticket_sharedpb.ReleaseTicketStatus(release_ticket_sharedpb.ReleaseTicketStatus_value[latestRt.Status]),
	}

	// 2.获取最近发布单 commit
	_, prevCommit, err := r.releasedCommitManager.GetChangeItemReleaseCommit(ctx, latestRt.ReleaseTicketID, changeItem)
	if err != nil {
		logs.CtxWarn(ctx, "[GetChangeItemsLatestReleasedInfo] projectUniqueID %v GetChangeItemReleaseCommit err, %v", projectUniqueId, err)
		return changeItemLatestReleasedInfo, nil
	}

	if prevCommit != nil {
		changeItemLatestReleasedInfo.ReleasedCommit = CommitToPB(prevCommit)
		logs.CtxInfo(ctx, "[GetChangeItemsLatestReleasedInfo] projectUniqueID %v, releasedCommit %v", projectUniqueId, prevCommit.GetId())
	}
	return changeItemLatestReleasedInfo, nil
}

func (r *rtIntegrationManager) GetCandidateReleasedProjectsForTBD(ctx context.Context, req *rt_integration_managerpb.GetCandidateReleasedProjectsForTBDReq) (*rt_integration_managerpb.GetCandidateReleasedProjectsForTBDResp, error) {
	candidateReleasedProjects := make([]*appcenter.ProjectInfo, 0)
	// 1.获取最近发布commit
	// 如果有发布单id， 优先取发布单中的changeItem release commit
	// 如果有仓库，优先取仓库下发布过的release commit
	var releasedCommit *gitpb.Commit
	var err error
	if req.GetReleaseTicketId() > 0 {
		releasedCommit, err = r.GetReleasedCommitByRt(ctx, req.GetReleaseTicketId())
		if err != nil {
			logs.CtxWarn(ctx, "[GetCandidateReleasedProjectsForTBD] GetReleasedCommitByRt err:%s", err.Error())
			return nil, err
		}
		logs.CtxInfo(ctx, "[GetCandidateReleasedProjectsForTBD] GetReleasedCommitByRt releasedCommit %v", releasedCommit)
	} else if req.GetGitRepoName() != "" {
		releasedCommit, err = r.GetReleasedCommitByRepo(ctx, req.GetGitRepoName())
		if err != nil {
			logs.CtxWarn(ctx, "[GetCandidateReleasedProjectsForTBD] GetReleasedCommitByRepo err:%s", err.Error())
			return nil, err
		}
		logs.CtxInfo(ctx, "[GetCandidateReleasedProjectsForTBD] GetReleasedCommitByRepo releasedCommit %v", releasedCommit)
	} else {
		// 可以补充其他策略
	}

	if releasedCommit.GetId() == "" {
		logs.CtxWarn(ctx, "[GetCandidateReleasedProjectsForTBD] released commit %s", releasedCommit.GetId())
		return &rt_integration_managerpb.GetCandidateReleasedProjectsForTBDResp{Projects: candidateReleasedProjects}, nil
	}

	// 2.获取当前要发布的commit
	toBeReleaseRef := req.GetToBeReleasedRef()
	toBeReleaseBranch := ""
	if toBeReleaseRef == "" && req.GetReleaseTicketId() > 0 {
		changeItems, err := r.RtChangeItemDao.GetByReleaseTicketID(ctx, req.GetReleaseTicketId())
		if err != nil {
			logs.CtxError(ctx, "[GetCandidateReleasedProjectsForTBD] GetByReleaseTicketID err: %s", err.Error())
			return nil, err
		}
		if len(changeItems) == 0 {
			logs.CtxError(ctx, "[GetCandidateReleasedProjectsForTBD] GetByReleaseTicketID changeitems len is 0")
			return nil, err
		}
		// 获取 branch
		branch, branchCommitIfExist, err := r.releasedCommitManager.GetChangeItemReleaseBranchName(ctx, req.GetReleaseTicketId(), &release_ticketpb.ReleaseTicketChangeItem{
			ProjectUniqueId: changeItems[0].ProjectUniqueID,
			ProjectType:     sharedpb.ProjectType(sharedpb.ProjectType_value[changeItems[0].ProjectType]),
			ControlPlane:    sharedpb.ControlPlane(sharedpb.ControlPlane_value[changeItems[0].ControlPlane]),
		})
		if err != nil || branchCommitIfExist.GetSha() == "" {
			logs.CtxError(ctx, "[GetCandidateReleasedProjectsForTBD] GetChangeItemReleaseBranchName err: %v or commit is null", err)
			return nil, bits_err.RELEASETICKET.ErrDBErr.AddErrMsg(fmt.Sprintf("get rt changeitems branch name is err: %s", err.Error()))
		}
		// 必须得转化成 commitId, 🌶GetRepoDiffByCommit
		toBeReleaseRef = branchCommitIfExist.GetSha()
		// 记录 branch
		toBeReleaseBranch = branch
		logs.CtxInfo(ctx, "[GetCandidateReleasedProjectsForTBD] to be release branch %v", toBeReleaseRef)
	} else {
		return nil, bits_err.RELEASETICKET.ErrParamConversion.AddErrMsg("get to be release commit params is null")
	}

	// 3.计算可被发布的项目
	candidateReleasedProjects, err = r.ComputeCandidateReleasedProjects(ctx, &RepoInfo{GitName: req.GetGitRepoName(), ProjectId: req.GetProjectId()}, releasedCommit.GetId(), toBeReleaseRef)
	if err != nil {
		return nil, err
	}

	return &rt_integration_managerpb.GetCandidateReleasedProjectsForTBDResp{Projects: candidateReleasedProjects, ReleasedCommit: releasedCommit.GetId(), ReleaseBranch: toBeReleaseBranch, ReleaseCommit: toBeReleaseRef}, nil
}

func (r *rtIntegrationManager) GetReleasedCommitByRt(ctx context.Context, releaseTicketId uint64) (*gitpb.Commit, error) {
	releasedInfo, err := r.GetChangeItemsLatestReleasedInfoInRt(ctx, releaseTicketId)
	if err != nil {
		logs.CtxError(ctx, "[GetReleasedCommitByRt] rtID: %s, err: %v", releaseTicketId, err)
		return nil, bits_err.RELEASETICKET.ErrRtIntegrationReleaseDiff.AddErrMsg(fmt.Sprintf("get rt changeItem released commit, %s", err.Error()))
	}
	if len(releasedInfo.GetLatestReleasedInfo()) == 0 {
		logs.CtxWarn(ctx, "[GetReleasedCommitByRt] rtID: %s, release info len is 0", releaseTicketId)
		return nil, nil
	}

	var latestCommit *gitpb.Commit
	for _, info := range releasedInfo.GetLatestReleasedInfo() {
		if info.GetReleasedCommit().GetId() != "" {
			// 初始复制
			if latestCommit.GetId() == "" {
				latestCommit = info.GetReleasedCommit()
			}
			// 提交时间最近，则赋值
			if info.GetReleasedCommit().GetCommittedDate() > latestCommit.GetCommittedDate() {
				latestCommit = info.GetReleasedCommit()
			}
		}
	}
	return latestCommit, nil
}

func (r *rtIntegrationManager) GetReleasedCommitByRepo(ctx context.Context, gitRepoName string) (*gitpb.Commit, error) {
	releasedCommits, err := r.ReleaseCommitRecordDao.GetByRepoName(ctx, gitRepoName)
	if err != nil {
		logs.CtxError(ctx, "[GetReleasedCommitByRepo] gitRepoName: %s, err: %v", gitRepoName, err)
		return nil, err
	}
	if len(releasedCommits) == 0 {
		logs.CtxWarn(ctx, "[GetReleasedCommitByRepo] gitRepoName: %s, release commit len is 0", gitRepoName)
		return nil, nil
	}
	// 过滤and去重
	commits := gslice.UniqBy(gslice.Filter(releasedCommits, func(c *entity.DBReleaseCommitRecord) bool {
		return c.CommitID != ""
	}), func(t *entity.DBReleaseCommitRecord) string {
		return t.CommitID
	})

	// get ProjectId
	repoInfo, err := r.GetRepoInfo(ctx, &RepoInfo{GitName: commits[0].GitRepoName})
	if err != nil {
		// err was handled
		return nil, err
	}

	var latestCommit = &git_server.Commit{}
	for _, c := range commits {
		if c.CommitID != "" {
			commitInfo, err := r.gitServerSvc.GetCommit(ctx, repoInfo.ProjectId, c.CommitID)
			if err != nil {
				logs.CtxError(ctx, "[GetReleasedCommitByRepo] GetCommit %v, err %v", c.CommitID, err)
			}

			// 初始复制
			if latestCommit.GetId() == "" {
				latestCommit = commitInfo
			}
			// 提交时间最近，则赋值
			if commitInfo.GetCommittedDate() > latestCommit.GetCommittedDate() {
				latestCommit = commitInfo
			}
		}
	}

	return CommitToPB(latestCommit), nil
}

func (r *rtIntegrationManager) ComputeCandidateReleasedProjects(ctx context.Context, info *RepoInfo, releasedRef string, toBeReleasedRef string) ([]*appcenter.ProjectInfo, error) {
	candidateReleasedProjects := make([]*appcenter.ProjectInfo, 0)
	repoInfo, err := r.GetRepoInfo(ctx, info)
	if err != nil {
		// err was handled
		return nil, err
	}

	// 需要转换成 codebaseId
	codebaseInfo, err := r.gitServerSvc.GetCodebaseRepoInfoByProjectID(ctx, int64(repoInfo.ProjectId))
	if err != nil {
		logs.CtxError(ctx, "[ComputeCandidateReleasedProjects] GetCodebaseRepoInfoByProjectID err %s", err.Error())
		return nil, bits_err.GITSERVER.ErrGitlabProjectNotFound.PassThrough(err)
	}

	repoID, _ := strconv.ParseInt(codebaseInfo.GetId(), 10, 64)
	commitDiffResp, err := r.gitServerSvc.GetRepoDiffByCommit(ctx, &git_server.GetRepoDiffByCommitRequest{RepoId: repoID, SourceCommit: toBeReleasedRef, TargetCommit: releasedRef})
	if err != nil {
		logs.CtxError(ctx, "[ComputeCandidateReleasedProjects] CompareRef sourceCommitsResp err %s", err.Error())
		return nil, bits_err.GITSERVER.ErrParams.AddErrMsg(fmt.Sprintf("git sever CompareRef sourceCommitsResp err: %s", err.Error()))
	}

	if len(commitDiffResp.GetFiles()) == 0 {
		logs.CtxError(ctx, "[ComputeCandidateReleasedProjects] CompareRef diff files is 0")
		return candidateReleasedProjects, nil
	}
	logs.CtxInfo(ctx, "[ComputeCandidateReleasedProjects] files is %v", commitDiffResp.GetFiles())

	projects, err := r.monoManager.GetMonoProjects(ctx, &mono_managerpb.GetMonoProjectsReq{
		GitRepoName: repoInfo.GitName,
		WithSubPath: true,
	})

	if err != nil {
		logs.CtxError(ctx, "[ComputeCandidateReleasedProjects] GetMonoProjects err %s", err.Error())
		return nil, err
	}

	if len(projects.GetProjects()) == 0 {
		logs.CtxInfo(ctx, "[ComputeCandidateReleasedProjects] iac projects is null")
		return candidateReleasedProjects, nil
	}

	for _, changePath := range commitDiffResp.GetFiles() {
		for _, p := range projects.GetProjects() {
			isSubPath, err := cdutils.IsSubPath(p.GetSubPath(), changePath.GetPath())
			if err != nil {
				logs.CtxWarn(ctx, "[ComputeCandidateReleasedProjects] isSubPath err %s", err.Error())
				continue
			}
			if isSubPath {
				candidateReleasedProjects = append(candidateReleasedProjects, p)
			}
		}
	}

	// 去重
	candidateReleasedProjects = gslice.UniqBy(candidateReleasedProjects, func(elem *appcenter.ProjectInfo) string {
		return fmt.Sprintf("%d_%s", elem.ProjectType, elem.ProjectUniqueId)
	})

	// 获取下应用中心详情
	queryProjects := gslice.Map(candidateReleasedProjects, func(p *appcenter.ProjectInfo) *appcenter.QueryCondition {
		return &appcenter.QueryCondition{ProjectType: p.GetProjectType(), ProjectUniqueId: p.GetProjectUniqueId()}
	})
	componentDetail, err := r.appCenter.BatchGetComponentListV2(ctx, queryProjects, []appcenter.ControlPlane{})
	if err != nil {
		logs.CtxError(ctx, "[ComputeCandidateReleasedProjects] BatchGetComponentListV2 err %s", err.Error())
		return nil, bits_err.APPCENTER.ErrGetComponentsFailure.PassThrough(err)
	}

	return componentDetail, nil
}

func (r *rtIntegrationManager) CompareRefHasDiffForMonoProject(ctx context.Context, monoProject *MonoProject, compareRef *CompareRef) (bool, error) {
	logs.CtxInfo(ctx, "[CompareRefHasDiffForMonoProject] CompareRef %+v, %+v", monoProject, compareRef)

	// TOOD: 需要缓存diff
	// 1.计算 source and target commit diff
	if compareRef.Source == "" || compareRef.Target == "" {
		logs.CtxInfo(ctx, "[CompareRefHasDiffForMonoProject] CompareRef source and target is null, no need to compute")
		return false, bits_err.RELEASETICKET.ErrParamConversion.AddErrMsg("[CompareRefHasDiffForMonoProject] CompareRef source and target is null")
	}
	// 必须是两个 commit + codebaseId
	commitDiffResp, err := r.gitServerSvc.GetRepoDiffByCommit(ctx, &git_server.GetRepoDiffByCommitRequest{RepoId: compareRef.CodeBaseId, SourceCommit: compareRef.Source, TargetCommit: compareRef.Target})
	if err != nil {
		logs.CtxError(ctx, "[CompareHasDiffForMonoProject] CompareRef sourceCommitsResp err %s", err.Error())
		return false, bits_err.GITSERVER.ErrParams.AddErrMsg(fmt.Sprintf("git sever CompareRef sourceCommitsResp err: %s", err.Error()))
	}

	if len(commitDiffResp.GetFiles()) == 0 {
		logs.CtxError(ctx, "[CompareHasDiffForMonoProject] CompareRef diff files is 0")
		return false, nil
	}
	logs.CtxInfo(ctx, "[CompareHasDiffForMonoProject] files is %v", commitDiffResp.GetFiles())
	// 获取项目路径
	project, err := r.GetProjectValidPathInfo(ctx, monoProject.ProjectType, monoProject.ProjectUniqueId, false)
	if err != nil {
		logs.CtxError(ctx, "[CompareHasDiffForMonoProject] GetProjectValidPathInfo err %s", err.Error())
		return false, bits_err.RELEASETICKET.ErrRtIntegrationReleaseDiff.PassThrough(err)
	}
	hasDiff := false
	for _, changePath := range commitDiffResp.GetFiles() {
		isSubPath, err := cdutils.IsSubPath(project.GetSubPath(), changePath.GetPath())
		if err != nil {
			logs.CtxWarn(ctx, "[CompareHasDiffForMonoProject] isSubPath err %s", err.Error())
			continue
		}
		if isSubPath {
			hasDiff = true
			logs.CtxInfo(ctx, "[CompareHasDiffForMonoProject] project subPath %s [has diff]", project.GetSubPath())
			break
		}
	}
	return hasDiff, nil
}

func (r *rtIntegrationManager) FilterRtProjectsIfHasNoDiff(ctx context.Context, rtProjects []*change_item.ProjectWithBranchModel) ([]*change_item.ProjectWithBranchModel, error) {
	// 计算最近一次发布成功的 commit

	// 1.prepare 参数
	releaseRepoBranch, err := r.GetToBeReleaseInfo(ctx, rtProjects)
	if err != nil {
		// 获取信息失败，不过滤项目
		logs.CtxWarn(ctx, "[FilterRtProjectsIfHasNoDiff] GetToBeReleaseInfo err %s", err.Error())
		return rtProjects, nil
	}

	newProjects := make([]*change_item.ProjectWithBranchModel, 0)
	for _, rtProject := range rtProjects {
		// get
		controlPlanes := make([]sharedpb.ControlPlane, 0)

		for _, a := range rtProject.Project.Artifact {
			controlPlanes = append(controlPlanes, sharedpb.ControlPlane(a.ControlPanel))
		}
		cp := cdutils.GetBaselineControlPlane(controlPlanes)

		currentProject := &MonoProject{ProjectUniqueId: rtProject.Project.GetProjectUniqueId(), ProjectType: sharedpb.ProjectType(sharedpb.ProjectType_value[rtProject.Project.GetProjectType().String()])}

		changeItemLatestReleasedInfo, err := r.GetChangeItemLatestReleasedInfo(ctx, &release_ticketpb.ReleaseTicketChangeItem{
			ProjectUniqueId: currentProject.ProjectUniqueId,
			ProjectType:     currentProject.ProjectType,
			ControlPlane:    cp,
		})

		if err != nil {
			logs.CtxWarn(ctx, "[FilterRtProjectsIfHasNoDiff] GetChangeItemLatestReleasedInfo: ProjectUniqueID %s, err %s", rtProject.Project.GetProjectUniqueId(), err.Error())
			// 出错了，还是要创建 projects
			newProjects = append(newProjects, rtProject)
			continue
		}

		hasDiff, err := r.CompareRefHasDiffForMonoProject(ctx, &MonoProject{ProjectUniqueId: currentProject.ProjectUniqueId, ProjectType: currentProject.ProjectType}, &CompareRef{CodeBaseId: releaseRepoBranch.CodeBaseId, Source: releaseRepoBranch.Source, Target: changeItemLatestReleasedInfo.GetReleasedCommit().GetId()})
		if err != nil {
			logs.CtxWarn(ctx, "[FilterRtProjectsIfHasNoDiff] CompareRefHasDiffForMonoProject: ProjectUniqueID %s, err %s", rtProject.Project.GetProjectUniqueId(), err.Error())
			// 出错了，发布单创建还是继续添加 project
			newProjects = append(newProjects, rtProject)
			continue
		}
		if !hasDiff {
			// 只有这种case，没有diff, 创建发布单不需要新增project
			logs.CtxInfo(ctx, "[FilterRtProjectsIfHasNoDiff] 是的，因为我没有变更，被无情的被业务方要求忽略了。%s")
			continue
		} else {
			newProjects = append(newProjects, rtProject)
		}
	}
	logs.CtxInfo(ctx, "[FilterRtProjectsIfHasNoDiff] newProjects %s", cdutils.JSONToString(ctx, newProjects))
	return newProjects, nil
}

// 仅内部使用, 获取发布分支和仓库信息
func (r *rtIntegrationManager) GetToBeReleaseInfo(ctx context.Context, rtProjects []*change_item.ProjectWithBranchModel) (*CompareRef, error) {
	// 1.prepare 参数
	if len(rtProjects) == 0 {
		logs.CtxWarn(ctx, "[FilterRtProjectsIfHasNoDiff] no projects found")
		return nil, bits_err.RELEASETICKET.ErrParamConversion.AddErrMsg(`[FilterRtProjectsIfHasNoDiff] no projects found`)
	}
	gitRepoId := rtProjects[0].Project.RepoId
	toBeReleaseBranch := rtProjects[0].Project.Branch
	for _, branchModel := range rtProjects[0].ControlPlaneBranchModel {
		if toBeReleaseBranch == "" {
			toBeReleaseBranch = branchModel.ReleaseBranch.GetName()
			logs.CtxInfo(ctx, "[FilterRtProjectsIfHasNoDiff] ReleaseBranch by project branch Model %s", toBeReleaseBranch)
			break
		}
	}
	if gitRepoId == 0 || toBeReleaseBranch == "" {
		// 参数不合法，不过滤
		logs.CtxWarn(ctx, "[FilterRtProjectsIfHasNoDiff] params is null")
		return nil, bits_err.RELEASETICKET.ErrParamConversion.AddErrMsg(`[FilterRtProjectsIfHasNoDiff] params is null`)
	}
	// 需要转换成 codebaseId
	codebaseInfo, err := r.gitServerSvc.GetCodebaseRepoInfoByProjectID(ctx, gitRepoId)
	if err != nil {
		logs.CtxError(ctx, "[FilterRtProjectsIfHasNoDiff] GetCodebaseRepoInfoByProjectID err %s", err.Error())
		return nil, bits_err.GITSERVER.ErrGitlabProjectNotFound.PassThrough(err)
	}
	repoID, _ := strconv.ParseInt(codebaseInfo.GetId(), 10, 64)
	// 发布分支commit
	branchInfo, err := r.gitServerSvc.GetBranchV2(ctx, &git_server_gen.GetBranchV2Request{
		RepositoryId: gptr.Of(repoID),
		Name:         toBeReleaseBranch,
	})
	if err != nil {
		logs.CtxError(ctx, "[FilterRtProjectsIfHasNoDiff] GetCodebaseBranch err %s", err.Error())
		return nil, bits_err.GITSERVER.ErrGitlabBranchNotFound.PassThrough(err)
	}
	// Target 先占位
	return &CompareRef{CodeBaseId: repoID, Source: branchInfo.GetBranch().GetCommit().GetId(), Target: ""}, nil
}
