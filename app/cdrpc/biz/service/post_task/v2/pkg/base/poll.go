package base

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	workflowdomain "code.byted.org/devinfra/hagrid/pkg/envplatform"

	"github.com/pkg/errors"
	"github.com/zeebo/errs"
	"gorm.io/gorm"

	pcronjob "code.byted.org/canal/provider/cronjob"
	envprovider "code.byted.org/canal/provider/env_platform"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/constants/project"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	envSDK "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/env"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/post_task/v2/pkg/model"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	commonUtils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/iesarch/paas_sdk/cronjob"
	"code.byted.org/iesarch/paas_sdk/env_platform"
	"code.byted.org/iesarch/paas_sdk/tce_config"
	"code.byted.org/lang/gg/choose"
)

func (s *postTaskBaseSvc) PollEnvPlatform(ctx context.Context,
	task *entity.DBReleaseTicketPostTask, lock redis.DistributedLock) error {
	ctx = logs.CtxAddKVs(ctx, "PollEnvPlatformRtId", task.ReleaseTicketID, "PollEnvPlatformTaskId", task.TaskID)

	logs.CtxInfo(ctx, "[PollEnvPlatform]start, task id: %d", task.TaskID)

	// 上锁
	if err := lock.TryLock(ctx, time.Second*10); err != nil {
		return erri.ErrorWrap(err, "get distributed lock failed")
	}
	defer lock.Unlock(ctx)

	if task.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_CRONJOB.String() {
		return s.pollCronJobTaskStatusHandler(ctx, task)
	} else {
		// 兼容历史数据，没 projectType 的填走 env 平台轮询逻辑
		if !envSDK.IsEnvSupportServiceType(task.ServiceType) {
			logs.CtxInfo(ctx, fmt.Sprintf("no such type: %s", task.ProjectType))
		}
		return s.pollEnvPlatformTaskStatusHandler(ctx, task)
	}
}

func (s *postTaskBaseSvc) pollCronJobTaskStatusHandler(ctx context.Context, task *entity.DBReleaseTicketPostTask) error {
	logs.CtxAddKVs(ctx, "task.id", task.TaskID, "task.DeploymentID", task.DeploymentID)
	cronjobBoeSdk, _ := pcronjob.GetClient(cronjob.RegionBOE)
	deploymentID, err := strconv.ParseInt(task.DeploymentID, 10, 64)
	jwtToken, _, err := s.GetUserToken(ctx, task.Operator, task.ReleaseTicketID, workflowdomain.StandardEnvModel{})
	if err != nil {
		logs.CtxError(ctx, "err: %s", err.Error())
		return err
	}
	ticket, err := cronjobBoeSdk.GetTicket(ctx, jwtToken, deploymentID)
	if err != nil {
		logs.CtxError(ctx, "err: %s", err.Error())
		return err
	}
	if ticket == nil {
		err = fmt.Errorf("not found cronjob ticket")
		logs.CtxError(ctx, "err: %s", err.Error())
		return err
	}
	logs.CtxInfo(ctx, "ticket: %s", utils.JSONToString(ctx, ticket))
	status, ok := project.CronJobDpTicketStatus[ticket.Status]
	if !ok {
		msg := fmt.Sprintf("no sush CronJobDpTicketStatus: %s", ticket.Status)
		logs.CtxError(ctx, msg)
		return errs.New(msg)
	}
	switch status {
	case change_itempb.CronJobTicketStatus_CREATE,
		change_itempb.CronJobTicketStatus_BUILD_IMAGE,
		change_itempb.CronJobTicketStatus_WAIT_APPROVE:
		logs.CtxInfo(ctx, "task is running.")
	case change_itempb.CronJobTicketStatus_BUILD_FAILED, change_itempb.CronJobTicketStatus_CANCEL:
		logs.CtxError(ctx, fmt.Sprintf("task status is %s", status.String()))
		errCode := choose.If[release_ticketpb.PostTaskErrCode](status.String() == "Failed", release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED, release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_CANCELLED)
		_ = s.RecordPtFailedReason(ctx, task.TaskID, errCode)
	case change_itempb.CronJobTicketStatus_FINISH:
		logs.CtxInfo(ctx, "task is succeed")
		err := s.postTaskDao.Update(ctx, task.TaskID, entity.DBReleaseTicketPostTask{
			TaskStatus: release_ticketpb.PostTaskStatus_POST_TASK_STATUS_SUCCEED.String(),
			EndAt:      time.Now(),
		})
		if err != nil {
			return erri.ErrorWrap(err, "update post task failed, rtID %d, taskID %d",
				task.ReleaseTicketID, task.TaskID)
		}
	default:
		logs.CtxError(ctx, fmt.Sprintf("not support status: %s", ticket.Status))
	}
	return nil
}

func (s *postTaskBaseSvc) pollEnvPlatformTaskStatusHandler(ctx context.Context, task *entity.DBReleaseTicketPostTask) error { // 查询多环境平台
	var (
		tceEnv string
		idc    string
	)
	switch task.TaskType {
	case release_ticketpb.PostTaskType_POST_TASK_TYPE_GC_ENV.String():
		standardEnvModel := workflowdomain.StandardEnvModel{
			StandardEnv: workflowdomain.StandardEnvType(task.StandardEnv),
		}
		tceEnv = choose.If[string](standardEnvModel.IsPPE(), tce_config.ENV_PPE, tce_config.ENV_BOE)
		idc = standardEnvModel.GetTceIDC()
	default:
		tceEnv = tce_config.ENV_BOE
		idc = tce_config.IDC_CN
	}

	deploymentID, err := strconv.ParseInt(task.DeploymentID, 10, 64)
	if err != nil {
		logs.CtxError(ctx, "Parse deploymentID err: %s", err.Error())
		return err
	}

	if deploymentID == 0 {
		logs.CtxError(ctx, "deploymentID is 0, post task id: %d", task.TaskID)
		return errors.New("deploymentID is 0")
	}

	ticketInfo := &env_platform.DeploymentInfoV4{}
	if tceEnv == tce_config.ENV_PPE && idc == tce_config.IDC_TTP {
		// ttp走单独的接口
		status, errCode, message, err := s.QueryTTPTicket(ctx, deploymentID)
		if err != nil {
			logs.CtxError(ctx, "QueryTTPTicket err: %s", err.Error())
			return err
		}
		ticketInfo.Status = status
		ticketInfo.ErrCode = errCode
		ticketInfo.Message = message
	} else if idc == tce_config.IDC_EU_TTP {
		status, errCode, message, err := s.QueryTTPTicketNew(ctx, deploymentID, idc, tceEnv)
		if err != nil {
			logs.CtxError(ctx, "QueryTTPTicketNew err: %s", err.Error())
			return err
		}
		ticketInfo.Status = status
		ticketInfo.ErrCode = errCode
		ticketInfo.Message = message
	} else {
		envPlatformSDK, err := envprovider.GetSDKByIDCAndTceEnv(idc, tceEnv)
		if err != nil {
			logs.CtxError(ctx, "envPlatformSDKProvider get err: %s", err.Error())
			return err
		}

		ticketInfo, err = envPlatformSDK.DeploymentInfo(ctx, deploymentID)
		if err != nil {
			logs.CtxError(ctx, "envPlatformSDK.DeploymentInfo err: %s", err.Error())
			return err
		}
	}

	logs.CtxInfo(ctx, "taskID: %d, psm: %s, env platform get deployment status: %s",
		task.TaskID, task.ProjectUniqueID, ticketInfo.Status)

	switch ticketInfo.Status {
	case model.Succeed:
		err := s.postTaskDao.Update(ctx, task.TaskID, entity.DBReleaseTicketPostTask{
			TaskStatus: release_ticketpb.PostTaskStatus_POST_TASK_STATUS_SUCCEED.String(),
			EndAt:      time.Now(),
		})
		if err != nil {
			return erri.ErrorWrap(err, "update post task failed, rtID %d, taskID %d",
				task.ReleaseTicketID, task.TaskID)
		}
	case model.Failed, model.Cancelled:
		errCode := genPostTaskErrCodeByEnvTicket(ticketInfo)
		_ = s.RecordPtFailedReason(ctx, task.TaskID, errCode)
		// 如果之前的状态不是失败状态，再发消息，避免重复发送消息
		if task.TaskStatus != release_ticketpb.PostTaskStatus_POST_TASK_STATUS_FAILED.String() &&
			(task.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String() || task.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK.String()) {
			commonUtils.SafeGoV3(ctx, func(innerCtx context.Context) {
				_ = s.rtOperationReporter.SendReleaseTicketPostTaskStatus(innerCtx, task.ReleaseTicketID, task.TaskID, task.DeploymentID, errCode.String(), task.ProjectType, task.ProjectUniqueID, task.BoeBaseEnv)
			})
		}
	default:
		logs.CtxInfo(ctx, "env platform ticket is still running, rtID %d, taskID %d, deploymentID %s",
			task.ReleaseTicketID, task.TaskID, task.DeploymentID)
	}

	return nil
}

// genPostTaskErrCodeByEnvTicket 根据环境工单报错信息细化错误码
func genPostTaskErrCodeByEnvTicket(ticketInfo *env_platform.DeploymentInfoV4) release_ticketpb.PostTaskErrCode {
	if ticketInfo.Status == model.Cancelled {
		return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_CANCELLED
	}

	switch ticketInfo.ErrCode {
	case "TCE_ERROR":
		// 检测未完成工单
		if strings.Contains(ticketInfo.Message, "unfinished tickets") ||
			strings.Contains(ticketInfo.Message, "service still has running tickets") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_UNFINISHED_TICKETS
		}
		// 没有删除集群权限
		if strings.Contains(ticketInfo.Message, "does not have cluster.delete permission") ||
			strings.Contains(ticketInfo.Message, "does not have service.delete permission") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_TCE_NO_PERMISSION
		}
		// 检测tce权限管控
		if strings.Contains(ticketInfo.Message, "字节云服务管控策略限制") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_DEPLOY_POLICY_NOT_ALLOW
		}
		if strings.Contains(ticketInfo.Message, "context deadline exceeded") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_TCE_DOWNSTREAM_TIMEOUT
		}
		return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_TCE_DEFAULT
	case "FAAS_ERROR":
		// 没有删除faas权限
		if strings.Contains(ticketInfo.Message, "permission denied") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_FAAS_NO_PERMISSION
		}
		// faas服务还有trigger enable，不允许删除
		if strings.Contains(ticketInfo.Message, "has enabled MQ triggers") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_FAAS_MQ_TRIGGER_ENABLED
		}
		if strings.Contains(ticketInfo.Message, "ticket is unfinished") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_FAAS_UNFINISHED_TICKETS
		}
		if strings.Contains(ticketInfo.Message, "context deadline exceeded") {
			return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_FAAS_DOWNSTREAM_TIMEOUT
		}
		return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_FAAS_DEFAULT
	case "TIMEOUT_ERROR":
		return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_DEPLOY_TIMEOUT
	case "SERVICE_UNKNOWN":
		return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED_SERVICE_UNKNOWN
	case "USER_CANCEL":
		return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_CANCELLED
	}
	return release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_DEPLOYMENT_EXECUTE_FAILED
}

func (s *postTaskBaseSvc) PollEnvPlatformByTaskID(ctx context.Context, taskID uint64) (*release_ticketpb.RTPostTask, error) {
	task, err := s.postTaskDao.FindByPostTaskID(ctx, taskID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.CtxError(ctx, "find post task error: %s, task id: %v", err.Error(), taskID)
		return nil, err
	}
	if task == nil {
		msg := fmt.Sprintf("not found task: %d", taskID)
		logs.CtxError(ctx, msg)
		return nil, errs.New(msg)
	}
	lockKey := redis.BuildReleaseTicketPostTaskPollLockKey(task.ReleaseTicketID, task.TaskID,
		task.ProjectType, task.ProjectUniqueID, task.BoeBaseEnv)
	lock := redis.NewDistributedLock(lockKey)
	if err = s.PollEnvPlatform(ctx, task, lock); err != nil {
		if !strings.Contains(err.Error(), "Locked by another session") {
			logs.CtxError(ctx, "poll env platform error %s", err.Error())
		}
		return nil, err
	}
	updatedTask, err := s.postTaskDao.FindByPostTaskID(ctx, taskID)
	if err != nil || updatedTask == nil {
		logs.CtxInfo(ctx, "not found task %d", taskID)
		return nil, err
	}
	// 查询 changeItem
	changeItems, err := s.releaseTicketChangeItemDao.GetByReleaseTicketID(ctx, updatedTask.ReleaseTicketID)
	if err != nil {
		logs.CtxError(ctx, "find changeItems err, rtID: %d", updatedTask.ReleaseTicketID)
		return nil, err
	}
	pbTask := s.BuildPostTask(ctx, []*entity.DBReleaseTicketPostTask{updatedTask}, changeItems)
	if len(pbTask) == 0 {
		err = fmt.Errorf("buildPostTask failed, task: %d", taskID)
		logs.CtxError(ctx, "err: %s", err.Error())
		return nil, err
	}
	return pbTask[0], err
}
