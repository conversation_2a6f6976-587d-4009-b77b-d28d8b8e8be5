package base

import (
	"context"
	"testing"

	"code.byted.org/bits/monkey"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/post_task/v2/pkg/model"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type PostTaskBaseTestSuite struct {
	suite.Suite
	ctx      context.Context
	cancel   context.CancelFunc
	mockCtrl *gomock.Controller
}

func (t *PostTaskBaseTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	t.mockCtrl = gomock.NewController(t.T())
}

func (t *PostTaskBaseTestSuite) TearDownTest() {
	t.cancel()
	monkey.UnpatchAll()
}

func TestPostTaskBase(t *testing.T) {
	suite.Run(t, new(PostTaskBaseTestSuite))
}

func (t *PostTaskBaseTestSuite) Test_getGeckoProjectBoeBase() {
	t.Run("rollback type, return empty", func() {
		projects := &model.ProjectInfo{
			RawChangeItem: &release_ticketpb.ReleaseTicketChangeItem{
				DeployTarget: &change_itempb.ChangeItemDeployTarget{
					ChannelItems: []*paaspb.ChannelItem{
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_CN,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_PROD,
						},
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_BOE_CN,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_TEST,
						},
					},
				},
			},
		}
		svc := &postTaskBaseSvc{}
		res := svc.getGeckoProjectBoeBase(t.ctx, projects, release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK)
		t.Len(res, 0)
	})

	t.Run("IES", func() {
		projects := &model.ProjectInfo{
			RawChangeItem: &release_ticketpb.ReleaseTicketChangeItem{
				DeployTarget: &change_itempb.ChangeItemDeployTarget{
					ChannelItems: []*paaspb.ChannelItem{
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_CN,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_PROD,
						},
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_BOE_CN,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_TEST,
						},
					},
				},
			},
		}
		svc := &postTaskBaseSvc{}
		res := svc.getGeckoProjectBoeBase(t.ctx, projects, release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE)
		t.Len(res, 1)
		t.Equal(res[0], model.GeckoIESBOE)
	})

	t.Run("TT", func() {
		projects := &model.ProjectInfo{
			RawChangeItem: &release_ticketpb.ReleaseTicketChangeItem{
				DeployTarget: &change_itempb.ChangeItemDeployTarget{
					ChannelItems: []*paaspb.ChannelItem{
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_TT_ROW,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_PROD,
						},
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_TT_ROW,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_TEST,
						},
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_EU_TTP,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_TEST,
						},
						{
							Region:       paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_US_TTP,
							GeckoEnvType: paaspb.GeckoEnvType_GECKO_ENV_TYPE_PROD,
						},
					},
				},
			},
		}
		svc := &postTaskBaseSvc{}
		res := svc.getGeckoProjectBoeBase(t.ctx, projects, release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE)
		t.Len(res, 2)
	})
}
