package base

//go:generate mockgen -destination mock/post_task_base_mock.go code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/post_task/v2/pkg/base PostTaskBaseSvc

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	json "github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"golang.org/x/sync/errgroup"

	"code.byted.org/bits/hephaestus/pkg/jsons"
	envprovider "code.byted.org/canal/provider/env_platform"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	envsdk "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/env"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/idgen"
	integratesdk "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/scm"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tce"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/branch_manager"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/config"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/post_task/v2/pkg/model"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_operation_reporter"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtproject"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	cdrpcutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils/enumutil"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils/timeutil"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	CommonUtils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"
	"code.byted.org/devinfra/hagrid/pkg/envplatform"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	cdutils "code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/iesarch/paas_sdk/tce_config"
	"code.byted.org/kite/kitex/byted/kitexutil"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gslice"
	tceopensdk "code.byted.org/tce/tce_golang_sdk"
)

type PostTaskBaseSvc interface {
	Create(ctx context.Context, param *model.PostTaskCreateParam) error
	GetPostTaskConfig(ctx context.Context, rtId uint64) (*workflowpb.PostTaskConfig, error)
	FindCurrentChangeItems(ctx context.Context, task *entity.DBReleaseTicketPostTask, changeItems []*entity.DBReleaseTicketChangeItem) []*entity.DBReleaseTicketChangeItem
	GetWorkflowId(ctx context.Context, rtId uint64) (uint64, error)
	RegionToDeployMapping(projectType sharedpb.ProjectType, source sharedpb.SyncSourceRegion) (deployTarget []sharedpb.SyncTargetRegion, err error)
	RegionFormat(boeBaseEnv string) sharedpb.SyncSourceRegion
	RegionToControlPlane(boeBaseEnv string) []sharedpb.ControlPlane
	BoeBaseFormat(boeBaseEnv string) sharedpb.SyncTargetRegion
	FindRtRepoInfo(ctx context.Context, task *entity.DBReleaseTicketPostTask) ([]model.ProjectRepoInfo, error)
	BuildPostTask(ctx context.Context, tasks []*entity.DBReleaseTicketPostTask, changeItems []*entity.DBReleaseTicketChangeItem) []*release_ticketpb.RTPostTask
	RecordPtFailedReason(ctx context.Context, taskID uint64, errCode release_ticketpb.PostTaskErrCode) error
	BoeBaseRegion(controlPlane sharedpb.ControlPlane, boeBaseEnv string) string
	// PollEnvPlatform 轮询多环境平台工单
	PollEnvPlatform(ctx context.Context, task *entity.DBReleaseTicketPostTask, lock redis.DistributedLock) error
	PollEnvPlatformByTaskID(ctx context.Context, taskID uint64) (*release_ticketpb.RTPostTask, error)
	GetUserToken(ctx context.Context, username string, releaseTicketID uint64, standardEnvModel envplatform.StandardEnvModel) (jwtToken, resUsername string, err error)
	QueryTTPTicket(ctx context.Context, deploymentID int64) (string, string, string, error)
	QueryTTPTicketNew(ctx context.Context, deploymentID int64, idc string, env string) (string, string, string, error)
	TransStringToControlPlan(ctx context.Context, planes string) []sharedpb.ControlPlane
	DelPostTask(ctx context.Context, taskID uint64) error
	BoeZoneSpecialFilter(ctx context.Context, controlPlane sharedpb.ControlPlane, clusters []*tceopensdk.ClusterInfo) []*tceopensdk.ClusterInfo
}

type postTaskBaseSvc struct {
	postTaskDao                repository.ReleaseTicketPostTaskDao
	releaseTicketChangeItemDao repository.ReleaseTicketChangeItemDao
	rtDao                      repository.ReleaseTicketDao
	releaseApproverDao         repository.ReleaseApproverDao
	proRollbackInfoDao         repository.ProjectRollbackInfoDao
	tceSvc                     tce.TceSdkSvc
	branchSyncTaskDao          repository.BranchSyncTaskDao
	workflowConfigHelper       config.WorkflowConfigHelper
	workflowDao                repository.WorkflowDao
	dlFactory                  redis.DistributedLockFactory
	stageDao                   repository.StageDao
	nodeConfig                 config.NodeConfigHelper
	integrationSvc             integratesdk.IntegrationSvc
	envSDK                     envplatform.SDK
	devTaskSvc                 integratesdk.DevSvc
	changeItemSvc              change_item.ChangeItemSvc
	gitSvc                     integratesdk.GitServerSvc
	branchManager              branch_manager.BranchManager
	crossborderSDK             crossborder.ICrossborderSDK
	envApi                     envsdk.CDEnvApiSvc
	rtProjectSvc               rtproject.RtProjectService
	stagePipelineDao           repository.StagePipelineDao
	rtOperationReporter        rt_operation_reporter.RtOperationReporter
}

func NewPostTaskBaseSvc() PostTaskBaseSvc {
	return &postTaskBaseSvc{
		postTaskDao:                repository.NewReleaseTicketPostTaskDao(),
		releaseTicketChangeItemDao: repository.NewReleaseTicketChangeItemDao(),
		rtDao:                      repository.NewReleaseTicketDao(),
		releaseApproverDao:         repository.NewReleaseApproverDao(),
		tceSvc:                     tce.TCESDK,
		workflowConfigHelper:       config.NewWorkflowConfigHelper(),
		branchSyncTaskDao:          repository.NewBranchSyncTaskDao(),
		dlFactory:                  redis.NewDistributedLockFactory(),
		stageDao:                   repository.NewStageDao(),
		integrationSvc:             integratesdk.NewIntegrationSvc(),
		envSDK:                     envplatform.DefaultSDK(),
		devTaskSvc:                 integratesdk.NewDevSvc(),
		nodeConfig:                 config.NewNodeConfigHelper(),
		proRollbackInfoDao:         repository.NewProjectRollbackInfoDao(),
		changeItemSvc:              change_item.NewChangeItemSvc(),
		gitSvc:                     integratesdk.NewGitServerSvc(),
		branchManager:              branch_manager.NewBranchManager(),
		crossborderSDK:             crossborder.NewCrossborderSDK(),
		envApi:                     envsdk.NewEnvPlatformSvc(),
		rtProjectSvc:               rtproject.NewRtProjectSvc(),
		stagePipelineDao:           repository.NewStagePipelineDao(),
		rtOperationReporter:        rt_operation_reporter.NewRtOperationReporter(),
		workflowDao:                repository.NewWorkflowDao(),
	}
}

func (s *postTaskBaseSvc) FindCurrentChangeItems(ctx context.Context, task *entity.DBReleaseTicketPostTask, changeItems []*entity.DBReleaseTicketChangeItem) []*entity.DBReleaseTicketChangeItem {
	cps := s.TransStringToControlPlan(ctx, task.ControlPlanes)
	if len(cps) == 0 {
		logs.CtxError(ctx, "FindCurrentChangeItems.TransStringToControlPlan ControlPlanes is empty, task id: %d", task.TaskID)
		return nil
	}
	resChangeItems := gslice.Filter(changeItems, func(changeItem *entity.DBReleaseTicketChangeItem) bool {
		return changeItem.ProjectUniqueID == task.ProjectUniqueID &&
			changeItem.ProjectType == task.ProjectType &&
			changeItem.ControlPlane == cps[0].String()
	})
	return resChangeItems
}

func (s *postTaskBaseSvc) GetPostTaskConfig(ctx context.Context, rtId uint64) (*workflowpb.PostTaskConfig, error) {
	// 获取模版的后置任务配置
	rt, err := s.rtDao.GetBasicByReleaseTicketID(ctx, rtId)
	if err != nil {
		logs.CtxError(ctx, "GetBasicByReleaseTicketID err: %v", err)
		return nil, err
	}
	workflow, err := s.workflowDao.Get(ctx, rt.WorkflowID)
	if err != nil {
		logs.CtxError(ctx, "get workflow err: %v", err)
		return nil, err
	}
	wfConfig, err := s.workflowConfigHelper.DbToPb(ctx, workflow.WorkflowConfig, false, 0)
	if err != nil {
		logs.CtxError(ctx, "workflow DbToPb err: %v", err)
		return nil, err
	}
	if assertPostTaskConfigIsNull(wfConfig) {
		err = fmt.Errorf("invalid post task config")
		logs.CtxError(ctx, "err: %v", err)
		return nil, err
	}
	return wfConfig.GetReleaseTicketConfig().GetPostTaskConfig(), nil
}

func (s *postTaskBaseSvc) GetWorkflowId(ctx context.Context, rtId uint64) (uint64, error) {
	// 获取模版的后置任务配置
	rt, err := s.rtDao.GetBasicByReleaseTicketID(ctx, rtId)
	if err != nil {
		logs.CtxError(ctx, "GetBasicByReleaseTicketID err: %v", err)
		return 0, err
	}
	workflow, err := s.workflowDao.Get(ctx, rt.WorkflowID)
	if err != nil {
		logs.CtxError(ctx, "get workflow err: %v", err)
		return 0, err
	}
	return workflow.WorkflowID, nil
}

// 无使用
func (s *postTaskBaseSvc) RegionToDeployMapping(projectType sharedpb.ProjectType, source sharedpb.SyncSourceRegion) (deployTarget []sharedpb.SyncTargetRegion, err error) {
	switch projectType {
	case sharedpb.ProjectType_PROJECT_TYPE_TCE, sharedpb.ProjectType_PROJECT_TYPE_FAAS:
		switch source {
		case sharedpb.SyncSourceRegion_SYNC_SOURCE_REGION_CN:
			return []sharedpb.SyncTargetRegion{sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE}, nil
		case sharedpb.SyncSourceRegion_SYNC_SOURCE_REGION_I18N:
			return []sharedpb.SyncTargetRegion{sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE,
				sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE_I18N_SG,
				sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE_I18N_VA}, nil
		default:
			return nil, fmt.Errorf("invalid source region")
		}
	case sharedpb.ProjectType_PROJECT_TYPE_HYBRID:
		return nil, fmt.Errorf("invalid project type")
	default:
		return nil, fmt.Errorf("invalid project type")
	}
}

func (s *postTaskBaseSvc) BoeBaseFormat(boeBaseEnv string) sharedpb.SyncTargetRegion {
	switch boeBaseEnv {
	case model.BoeBaseEnvProd:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE
	case model.BoeBaseEnvProdVA:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE_I18N_VA
	case model.BoeBaseEnvProdSG:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE_I18N_SG
	case model.BoeBaseEnvProdTTP:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_BOE_TTP
	case model.GeckoIESCN:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_GECKO_IES_CN
	case model.GeckoIESBOE:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_GECKO_IES_BOE
	case model.GeckoIESI18N:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_GECKO_IES_I18N
	case model.GeckoTTROW:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_GECKO_TT_ROW
	case model.GeckoTTEU:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_GECKO_TT_EU_TTP
	case model.GeckoTTUS:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_GECKO_TT_US_TTP
	default:
		return sharedpb.SyncTargetRegion_SYNC_TARGET_REGION_UNSPECIFIED
	}
}

// no use
func (s *postTaskBaseSvc) RegionFormat(boeBaseEnv string) sharedpb.SyncSourceRegion {
	switch boeBaseEnv {
	case model.BoeBaseEnvProd:
		return sharedpb.SyncSourceRegion_SYNC_SOURCE_REGION_CN
	case model.BoeBaseEnvProdVA, model.BoeBaseEnvProdSG:
		return sharedpb.SyncSourceRegion_SYNC_SOURCE_REGION_I18N
	case model.BoeBaseEnvProdTTP:
		return sharedpb.SyncSourceRegion_SYNC_SOURCE_REGION_UNSPECIFIED
	}
	return sharedpb.SyncSourceRegion_SYNC_SOURCE_REGION_UNSPECIFIED
}

// no use
func (s *postTaskBaseSvc) RegionToControlPlane(boeBaseEnv string) []sharedpb.ControlPlane {
	switch boeBaseEnv {
	case model.BoeBaseEnvProd:
		return []sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_CN, sharedpb.ControlPlane_CONTROL_PLANE_I18N}
	case model.BoeBaseEnvProdVA, model.BoeBaseEnvProdSG:
		return []sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_I18N}
	case model.BoeBaseEnvProdTTP:
		return []sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_US_TTP}
	}
	return []sharedpb.ControlPlane{}
}

func assertPostTaskConfigIsNull(wfConfig *workflowpb.WorkflowConfig) bool {
	if wfConfig == nil || wfConfig.GetReleaseTicketConfig() == nil || wfConfig.GetReleaseTicketConfig().GetPostTaskConfig() == nil ||
		wfConfig.GetReleaseTicketConfig().GetPostTaskConfig() == nil {
		return true
	}
	ptConfig := wfConfig.GetReleaseTicketConfig().GetPostTaskConfig()
	if ptConfig.GetPublishPostTaskConfig() == nil || ptConfig.GetPublishPostTaskConfig().GetTceConfig() == nil {
		return true
	}
	if ptConfig.GetRollbackPostTaskConfig() == nil || ptConfig.GetRollbackPostTaskConfig().GetTceConfig() == nil {
		return true
	}
	return false
}

// 兼容跨端 TT 的 EU US 聚合在了 I18N 控制面
func TransHybridDeployControlPlane(cp sharedpb.ControlPlane, projectType string) sharedpb.ControlPlane {
	// 跨端非 CN 控制面，说明是 IES I18N 或者 TT，统一返回 I18N 控制面
	if projectType == sharedpb.ProjectType_PROJECT_TYPE_HYBRID.String() && cp != sharedpb.ControlPlane_CONTROL_PLANE_CN {
		return sharedpb.ControlPlane_CONTROL_PLANE_I18N
	}

	// 其他的都不需要处理
	return cp
}

func (s *postTaskBaseSvc) FindRtRepoInfo(ctx context.Context, task *entity.DBReleaseTicketPostTask) ([]model.ProjectRepoInfo, error) {
	ctx = logs.CtxAddKVs(ctx, "findRtRepoInfo.rtID", task.ReleaseTicketID, "findRtRepoInfo.taskID", task.TaskID)
	cps := s.TransStringToControlPlan(ctx, task.ControlPlanes)
	if len(cps) == 0 {
		logs.CtxError(ctx, "invalid control plane")
		return nil, fmt.Errorf("invalid control plane")
	}
	scmRepos, err := s.getScmInfoByDeploymentId(ctx, task, cps[0])
	if err != nil {
		logs.CtxError(ctx, "getScmInfoByDeploymentId err: %s", err.Error())
		return nil, err
	}
	logs.CtxInfo(ctx, "findRtRepoInfo %s", CommonUtils.ToJson(scmRepos))
	return scmRepos, nil
}

func (s *postTaskBaseSvc) findItemDeployPipeline(ctx context.Context, releaseTicketID uint64, projectUniqueID string, controlPlane sharedpb.ControlPlane) (*entity.DBStagePipeline, error) {
	stages, err := s.stageDao.GetStagesByReleaseTicketID(ctx, releaseTicketID)
	if err != nil {
		logs.CtxError(ctx, "GetStagesByReleaseTicketID error: %s, rt id: %d", err.Error(), releaseTicketID)
		return nil, err
	}

	publishStage := gslice.Find(stages, func(stage *entity.DBStage) bool {
		return stage.NodeType == workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE.String()
	})
	if publishStage.IsNil() {
		logs.CtxError(ctx, "stage.IsNil rt id: %v", releaseTicketID)
		return nil, fmt.Errorf("stage.IsNil rt id: %v", releaseTicketID)
	}

	stagePipelines, err := s.stagePipelineDao.GetByStageID(ctx, publishStage.Value().StageID)
	if err != nil {
		logs.CtxError(ctx, "GetByStageID error: %s, rt id: %d", err.Error(), releaseTicketID)
		return nil, err
	}
	targetStagePipeline := gslice.Find(stagePipelines, func(stagePipeline *entity.DBStagePipeline) bool {
		return stagePipeline.ProjectUniqueID == projectUniqueID && stagePipeline.ControlPlane == controlPlane.String()
	})
	if targetStagePipeline.IsNil() {
		logs.CtxError(ctx, "not found targetPipelineId: %v, rt id: %v", err, releaseTicketID)
		return nil, fmt.Errorf("not found targetPipelineId: %v, rt id: %v", err, releaseTicketID)
	}

	return targetStagePipeline.Value(), nil
}

// 原函数中调用抽取的函数
func (s *postTaskBaseSvc) getScmInfoByDeploymentId(ctx context.Context, task *entity.DBReleaseTicketPostTask, pControlPlane sharedpb.ControlPlane) ([]model.ProjectRepoInfo, error) {
	if task.ReleaseTicketID == 0 {
		return nil, fmt.Errorf("ReleaseTicketID required")
	}
	pType := enumutil.Enum(task.ProjectType, sharedpb.ProjectType_value, sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED)
	if !slicex.Contains([]sharedpb.ProjectType{
		sharedpb.ProjectType_PROJECT_TYPE_TCE,
		sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
		sharedpb.ProjectType_PROJECT_TYPE_FAAS,
		sharedpb.ProjectType_PROJECT_TYPE_WEB,
		sharedpb.ProjectType_PROJECT_TYPE_HYBRID,
	}, pType) {
		return nil, fmt.Errorf("not support ptype, pType:%s", pType.String())
	}

	// 仅处理 gecko
	deployControlPlane := TransHybridDeployControlPlane(pControlPlane, task.ProjectType)
	targetStagePipeline, err := s.findItemDeployPipeline(ctx, task.ReleaseTicketID, task.ProjectUniqueID, deployControlPlane)
	if err != nil {
		logs.CtxError(ctx, "findItemDeployPipeline err: %v", err)
		return nil, err
	}

	ticketInfo, err := s.rtProjectSvc.GetRtProjectByProjectType(ctx, pType).GetTicketInfo(ctx, rtproject.TicketQuery{
		PipelineID:      targetStagePipeline.PipelineID,
		ControlPlane:    deployControlPlane,
		ProjectUniqueID: task.ProjectUniqueID,
		ReleaseTicketID: task.ReleaseTicketID,
	})
	if err != nil {
		logs.CtxError(ctx, "GetRtProjectByProjectType err: %v", err)
		return nil, err
	}
	if ticketInfo == nil {
		logs.CtxError(ctx, "tceTicketInfo is nil, projectUniqueID: %s, ControlPlane: %s", task.ProjectUniqueID, deployControlPlane.String())
		return nil, fmt.Errorf("tceTicketInfo nil")
	}

	var scmRepos []model.ProjectRepoInfo
	switch pType {
	case sharedpb.ProjectType_PROJECT_TYPE_TCE:
		scmRepos, err = s.getTCEScmInfoByReleaseInfo(ctx, task, ticketInfo)
	case sharedpb.ProjectType_PROJECT_TYPE_CRONJOB:
		scmRepos, err = s.getCronJobScmInfoByReleaseInfo(ctx, ticketInfo)
	case sharedpb.ProjectType_PROJECT_TYPE_FAAS:
		scmRepos, err = s.getFaaSScmInfoByReleaseInfo(ctx, ticketInfo, task)
	case sharedpb.ProjectType_PROJECT_TYPE_WEB:
		scmRepos, err = s.getWebScmInfoByReleaseInfo(ctx, ticketInfo, task, targetStagePipeline.PipelineID)
	case sharedpb.ProjectType_PROJECT_TYPE_HYBRID:
		scmRepos, err = s.getHybridScmInfoByReleaseInfo(ctx, ticketInfo, pControlPlane)
	}
	if err != nil {
		logs.CtxError(ctx, "getScmInfoByDeploymentId err: %v", err)
		return nil, err
	}

	changeItemMainRepos, err := s.changeItemSvc.GetChangeItemMainSCMArtifact(ctx, task.ReleaseTicketID, task.ProjectUniqueID, pType, deployControlPlane)
	if err != nil {
		logs.CtxError(ctx, "GetChangeItemMainSCMArtifact err: %v", err)
		return nil, err
	}

	// 判断是不是主仓
	deployMainRepos := gslice.Map(scmRepos, func(r model.ProjectRepoInfo) model.ProjectRepoInfo {
		if gslice.Find(changeItemMainRepos, func(c *change_itempb.SCMArtifact) bool { return c.GetScmName() == r.ScmName }).IsOK() {
			r.MainRepo = true
		}
		return r
	})
	logs.CtxInfo(ctx, "psm %s, scm repo to deploy: %s", task.ProjectUniqueID, utils.JSONToString(ctx, deployMainRepos))
	return deployMainRepos, nil
}

func (s *postTaskBaseSvc) getWebScmInfoByReleaseInfo(ctx context.Context, ticketInfo *change_itempb.DeployTicket, task *entity.DBReleaseTicketPostTask, deployPplID uint64) ([]model.ProjectRepoInfo, error) {
	if ticketInfo.GetWebTicket() == nil {
		err := fmt.Errorf("not found ticketInfo.GetWebTicket")
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	controlPlane := s.TransStringToControlPlan(ctx, task.ControlPlanes)[0]
	var scmRepos []model.ProjectRepoInfo
	if task.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String() {
		regionTickets := ticketInfo.GetWebTicket().GetRegionTickets()
		var targetControlPlaneDeployTicket *change_itempb.RegionTicketWeb
		switch controlPlane {
		case sharedpb.ControlPlane_CONTROL_PLANE_CN:
			targetControlPlaneDeployTicket, _ = slicex.Find(regionTickets, func(v *change_itempb.RegionTicketWeb) bool {
				return cdrpcutils.IsCNOnlineRegion(v.GetRegion())
			})
		case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
			targetControlPlaneDeployTicket, _ = slicex.Find(regionTickets, func(v *change_itempb.RegionTicketWeb) bool {
				return cdrpcutils.IsI18nOnlineRegion(v.GetRegion())
			})
		case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP, sharedpb.ControlPlane_CONTROL_PLANE_US_TTP, sharedpb.ControlPlane_CONTROL_PLANE_TTP:
			targetControlPlaneDeployTicket, _ = slicex.Find(regionTickets, func(v *change_itempb.RegionTicketWeb) bool {
				return cdrpcutils.IsTTpOnlineRegion(v.GetRegion())
			})
		}
		if targetControlPlaneDeployTicket != nil {
			scmRepos = append(scmRepos, model.ProjectRepoInfo{
				Version:  targetControlPlaneDeployTicket.GetScmVersion(),
				ScmName:  targetControlPlaneDeployTicket.GetScmName(),
				Type:     "scm",
				RemoteID: int64(targetControlPlaneDeployTicket.GetScmId()),
			})
		}
	}
	if task.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK.String() {
		releaseApprovers, err := s.releaseApproverDao.GetByReleaseTicketID(ctx, task.ReleaseTicketID, release_ticketpb.ReleaseTicketApproverRole_RELEASE_TICKET_APPROVER_ROLE_RELEASE.String())
		if err != nil || len(releaseApprovers) == 0 {
			logs.CtxError(ctx, "err: %v", err)
			return nil, err
		}
		rollbackDeployInfo, err := s.rtProjectSvc.GetRtProjectByProjectType(ctx, sharedpb.ProjectType_PROJECT_TYPE_WEB).GetDeploymentInfoForRollback(ctx, rtproject.GetDeploymentInfoForRollbackReq{
			ForwardDeployPipelineID: deployPplID,
			ControlPlane:            controlPlane,
			ProjectUniqueID:         task.ProjectUniqueID,
		}, releaseApprovers[0].Username)
		if err != nil {
			return nil, err
		}
		var targetControlPlaneRollbackTicket *paaspb.RegionInfoForRollback
		regionRollbackTickets := rollbackDeployInfo.GetWebTicketInfo().GetRegions()
		switch controlPlane {
		case sharedpb.ControlPlane_CONTROL_PLANE_CN:
			targetControlPlaneRollbackTicket, _ = slicex.Find(regionRollbackTickets, func(v *paaspb.RegionInfoForRollback) bool {
				return cdrpcutils.IsCNOnlineRegion(v.GetRegion())
			})
		case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
			targetControlPlaneRollbackTicket, _ = slicex.Find(regionRollbackTickets, func(v *paaspb.RegionInfoForRollback) bool {
				return cdrpcutils.IsI18nOnlineRegion(v.GetRegion())
			})
		case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP, sharedpb.ControlPlane_CONTROL_PLANE_US_TTP, sharedpb.ControlPlane_CONTROL_PLANE_TTP:
			targetControlPlaneRollbackTicket, _ = slicex.Find(regionRollbackTickets, func(v *paaspb.RegionInfoForRollback) bool {
				return cdrpcutils.IsTTpOnlineRegion(v.GetRegion())
			})
		}
		if targetControlPlaneRollbackTicket != nil {
			scmRepos = append(scmRepos, model.ProjectRepoInfo{
				Version:  targetControlPlaneRollbackTicket.GetCurrentVersion(),
				ScmName:  targetControlPlaneRollbackTicket.GetScmName(),
				Type:     "scm",
				RemoteID: int64(targetControlPlaneRollbackTicket.GetScmId()),
			})
		}
	}
	return scmRepos, nil
}

func (s *postTaskBaseSvc) getFaaSScmInfoByReleaseInfo(ctx context.Context, ticketInfo *change_itempb.DeployTicket, task *entity.DBReleaseTicketPostTask) ([]model.ProjectRepoInfo, error) {
	if ticketInfo.GetFaasTicket() == nil {
		err := fmt.Errorf("not found ticketInfo.GetFaasTicket")
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	var scmRepos []model.ProjectRepoInfo
	revisions := choose.If(task.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		ticketInfo.GetFaasTicket().GetUpdateData().GetClusterReleaseMeta().GetRevisions(),
		ticketInfo.GetFaasTicket().GetOriginData().GetClusterReleaseMeta().GetRevisions())
	for _, revision := range revisions {
		scmName, scmVersion, err := getFAASSCMInfoFromSource(revision.GetSource())
		if err != nil {
			logs.CtxError(ctx, "getFAASSCMInfoFromSource err: %v", err)
			return nil, err
		}
		resScm, exist, err := scm.CurrentIdcScmSvc.GetSCMRepoByName(ctx, scmName)
		if err != nil {
			logs.CtxError(ctx, "scm.CurrentIdcScmSvc.GetSCMRepoByName: err: %v, scmRepoName: %d", err, scmName)
			return nil, err
		}
		if !exist {
			err = fmt.Errorf("not found scm repo")
			logs.CtxError(ctx, err.Error())
			return nil, err
		}
		scmRepo := model.ProjectRepoInfo{
			RemoteID: resScm.ID,
			Type:     "scm",
			Version:  scmVersion,
			ScmName:  scmName,
		}
		scmRepos = append(scmRepos, scmRepo)
	}
	return scmRepos, nil
}

func (s *postTaskBaseSvc) getCronJobScmInfoByReleaseInfo(ctx context.Context, ticketInfo *change_itempb.DeployTicket) ([]model.ProjectRepoInfo, error) {
	if ticketInfo.GetCronjobTicket() == nil {
		err := fmt.Errorf("not found ticketInfo.GetCronjobTicket")
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	var scmRepos []model.ProjectRepoInfo
	for _, repo := range ticketInfo.GetCronjobTicket().GetDiff() {
		scmRepos = append(scmRepos, model.ProjectRepoInfo{
			Version: repo.NewVersion,
			ScmName: repo.Name,
		})
	}
	return scmRepos, nil
}

func (s *postTaskBaseSvc) getHybridScmInfoByReleaseInfo(ctx context.Context, ticketInfo *change_itempb.DeployTicket, controlPlane sharedpb.ControlPlane) ([]model.ProjectRepoInfo, error) {
	if ticketInfo.GetGeckoTicket() == nil {
		err := fmt.Errorf("not found ticketInfo.GetGeckoTicket")
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	logs.CtxInfo(ctx, "getHybridScmInfoByReleaseInfo ticketInfo: %s", CommonUtils.ToJson(ticketInfo.GetGeckoTicket()))
	var scmRepos []model.ProjectRepoInfo
	// usttp 的 scm 版本不同
	if controlPlane == sharedpb.ControlPlane_CONTROL_PLANE_US_TTP {
		if ttpScmInfo := ticketInfo.GetGeckoTicket().GetTtpScmVersion(); ttpScmInfo != nil {
			scmRepos = append(scmRepos, model.ProjectRepoInfo{
				Version:  ttpScmInfo.GetCurrentVersion(),
				ScmName:  ttpScmInfo.GetScmName(),
				RemoteID: int64(ttpScmInfo.GetScmId()),
				Type:     "scm",
			})
		}
	} else {
		if scmInfo := ticketInfo.GetGeckoTicket().GetScmVersion(); scmInfo != nil {
			scmRepos = append(scmRepos, model.ProjectRepoInfo{
				Version:  scmInfo.GetCurrentVersion(),
				ScmName:  scmInfo.GetScmName(),
				RemoteID: int64(scmInfo.GetScmId()),
				Type:     "scm",
			})
		}
	}

	return scmRepos, nil
}

func (s *postTaskBaseSvc) getTCEScmInfoByReleaseInfo(ctx context.Context, task *entity.DBReleaseTicketPostTask, ticketInfo *change_itempb.DeployTicket) ([]model.ProjectRepoInfo, error) {
	if ticketInfo.GetTceTicket() == nil {
		err := fmt.Errorf("not found ticketInfo.GetTceTicket")
		logs.CtxError(ctx, err.Error())
		return nil, err
	}
	ticketId := ticketInfo.GetTceTicket().GetId()
	if ticketId == 0 {
		logs.CtxError(ctx, "ticketId is 0")
		return nil, fmt.Errorf("tceTicketInfo.GetTceTicket().GetId() is 0, tceTicketInfo:%v", CommonUtils.ToJson(ticketInfo.GetTceTicket()))
	}

	logs.CtxInfo(ctx, "ticket info: %v", utils.JSONToString(ctx, ticketInfo))
	releaseApprovers, err := s.releaseApproverDao.GetByReleaseTicketID(ctx, task.ReleaseTicketID, release_ticketpb.ReleaseTicketApproverRole_RELEASE_TICKET_APPROVER_ROLE_RELEASE.String())
	if err != nil || len(releaseApprovers) == 0 {
		logs.CtxError(ctx, "err: %v", err)
		return nil, err
	}

	cps := s.TransStringToControlPlan(ctx, task.ControlPlanes)
	idc := tce_config.IDC_CN
	switch cps[0] {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		idc = tce_config.IDC_CN
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		idc = tce_config.IDC_I18N
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD:
		idc = tce_config.IDC_I18NBD
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		idc = tce_config.IDC_TTP
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		idc = tce_config.IDC_EU_TTP
	default:
		logs.CtxError(ctx, "err： %s", "not support control plane")
		return nil, fmt.Errorf("not support control plane")
	}
	resp, err := s.rtProjectSvc.GetRtProjectByProjectType(ctx, sharedpb.ProjectType_PROJECT_TYPE_TCE).GetDeploymentInfo(ctx, &rtproject.GetDeploymentInfoReq{
		ProjectUniqueID: task.ProjectUniqueID,
		ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
		Idc:             idc,
		TceEnv:          tce_config.ENV_PROD,
		TceTicketID:     ticketId,
		UserName:        releaseApprovers[0].Username,
	})
	if err != nil {
		return nil, bits_err.RELEASETICKET.ErrRollback.AddOrPass(ctx, err)
	}

	scmRepos := make([]model.ProjectRepoInfo, 0)
	for _, cluster := range resp.TceDeploymentInfo.RuntimeInfo.ClusterList {
		for _, r := range cluster.RepoInfo {
			scmRepoID, err := strconv.ParseInt(r.ScmRepoId, 10, 64)
			if err != nil {
				return nil, erri.ErrorWrap(err, "[SubmitReleaseTicketPostTaskError] "+
					"parse scm repo id failed, rtID %d, taskID %d", task.ReleaseTicketID, task.TaskID)
			}
			if exist := gslice.Find(scmRepos, func(scmRepo model.ProjectRepoInfo) bool {
				return scmRepo.RemoteID == scmRepoID
			}); exist.IsOK() {
				logs.CtxInfo(ctx, "exist repo: %v", utils.JSONToString(ctx, exist.Value()))
				continue
			}

			version := r.GetVersion()
			if task.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK.String() {
				version = r.GetOriginVersion()
			}
			scmRepo := model.ProjectRepoInfo{
				RemoteID: scmRepoID,
				Type:     "scm",
				Version:  version,
				MainRepo: r.MainRepo,
			}
			resScm, err := scm.CurrentIdcScmSvc.GetSCMRepoByID(ctx, scmRepoID)
			if err != nil {
				logs.CtxError(ctx, "scm.CurrentIdcScmSvc.GetSCMRepoByID: err: %v, scmRepoID: %d", err, scmRepoID)
				return nil, err
			}
			scmRepo.ScmName = resScm.Name
			scmRepos = append(scmRepos, scmRepo)
		}
	}
	return scmRepos, nil
}

func getFAASSCMInfoFromSource(source string) (scmRepoName string, scmVersion string, err error) {
	// 解析 source 字段
	parts := strings.Split(source, ":")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid source format")
	}
	// 获取 SCM 名称和版本号
	scmRepoName = parts[0]
	scmVersion = parts[1]
	return scmRepoName, scmVersion, nil
}

func (s *postTaskBaseSvc) BuildPostTask(ctx context.Context, tasks []*entity.DBReleaseTicketPostTask, changeItems []*entity.DBReleaseTicketChangeItem) []*release_ticketpb.RTPostTask {
	postTaskPb := gslice.Map(
		tasks,
		func(t *entity.DBReleaseTicketPostTask) *release_ticketpb.RTPostTask {
			var psms []string
			err := json.Unmarshal([]byte(t.PSMs), &psms)
			if err != nil {
				logs.CtxError(ctx, "List post task error, can not unmarshal psms, err: %v, psms: %v", err, t.PSMs)
			}

			delBranchResults := make([]*release_ticketpb.DelBranchResult, 0)
			err = json.UnmarshalString(t.DelBranchResult, &delBranchResults)
			if err != nil {
				logs.CtxError(ctx, "List post task error, can not unmarshal delBranchResults, err: %v, delBranchResults: %v", err, t.DelBranchResult)
			}

			var ptMode workflowpb.PostTaskMode
			switch t.PostTaskMode {
			case "", workflowpb.PostTaskMode_POST_TASK_MODE_TICKET.String():
				ptMode = workflowpb.PostTaskMode_POST_TASK_MODE_TICKET
			case workflowpb.PostTaskMode_POST_TASK_MODE_PIPELINE.String():
				ptMode = workflowpb.PostTaskMode_POST_TASK_MODE_PIPELINE
			default:
				ptMode = workflowpb.PostTaskMode_POST_TASK_MODE_UNSPECIFIED
			}
			if t.PostTaskMode == "" || t.PostTaskMode == workflowpb.PostTaskMode_POST_TASK_MODE_TICKET.String() {
				ptMode = workflowpb.PostTaskMode_POST_TASK_MODE_TICKET
			}

			controlPlanes := s.TransStringToControlPlan(ctx, t.ControlPlanes)
			if len(controlPlanes) == 0 {
				logs.CtxInfo(ctx, "not found controlPlanes: %d", t.TaskID)
			}
			return &release_ticketpb.RTPostTask{
				TaskId:          t.TaskID,
				TaskName:        "",
				TaskDescription: "",
				ReleaseTicketId: t.ReleaseTicketID,
				TaskType: enumutil.Enum(t.TaskType, release_ticketpb.PostTaskType_value,
					release_ticketpb.PostTaskType_POST_TASK_TYPE_UNSPECIFIED),
				TaskStatus: enumutil.Enum(t.TaskStatus, release_ticketpb.PostTaskStatus_value,
					release_ticketpb.PostTaskStatus_POST_TASK_STATUS_UNSPECIFIED),
				ProjectType: enumutil.Enum(t.ProjectType, sharedpb.ProjectType_value,
					sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED),
				ProjectUniqueId:  t.ProjectUniqueID,
				BoeBaseEnv:       t.BoeBaseEnv,
				DeploymentId:     t.DeploymentID,
				Operator:         t.Operator,
				ModifyType:       getModifyType(t.TaskType),
				StartAt:          lo.Ternary(t.StarAt.IsZero(), "", timeutil.TimeToString(t.StarAt)),
				EndAt:            lo.Ternary(t.EndAt.IsZero(), "", timeutil.TimeToString(t.EndAt)),
				CreatedAt:        timeutil.TimeToString(t.CreatedAt),
				UpdatedAt:        lo.Ternary(t.UpdatedAt.IsZero(), "", timeutil.TimeToString(t.UpdatedAt)),
				EnvName:          t.EnvName,
				StandardEnv:      t.StandardEnv,
				ServiceType:      t.ServiceType,
				Psms:             psms,
				ErrCode:          release_ticketpb.PostTaskErrCode(release_ticketpb.PostTaskErrCode_value[t.ErrCode]),
				ControlPlanes:    controlPlanes,
				DelBranch:        t.DelBranch,
				DelBranchResults: delBranchResults,
				ProjectExtra:     t.ProjectExtra,
				ProjectName:      t.ProjectName,
				GitRepoName:      t.GitRepoName,
				CommitId:         t.CommitID,
				PostTaskMode:     ptMode,
			}
		},
	)
	return postTaskPb
}

func getModifyType(taskType string) string {
	switch taskType {
	case release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK.String():
		return "upgrade"
	case release_ticketpb.PostTaskType_POST_TASK_TYPE_GC_ENV.String():
		return "delete"
	default:
		return ""
	}
}

func (s *postTaskBaseSvc) RecordPtFailedReason(ctx context.Context, taskID uint64, errCode release_ticketpb.PostTaskErrCode) error {
	task, err := s.postTaskDao.FindByPostTaskID(ctx, taskID)
	if err != nil {
		logs.CtxError(ctx, "RecordPtFailedReason error, err: %v", err)
		return err
	}
	task.TaskStatus = release_ticketpb.PostTaskStatus_POST_TASK_STATUS_FAILED.String()
	logID, _ := kitexutil.GetLogID(ctx)
	task.ErrCodeLog = logID
	task.ErrCode = errCode.String()
	task.StarAt = time.Now()
	err = s.postTaskDao.Update(ctx, taskID, *task)
	if err != nil {
		logs.CtxError(ctx, "RecordPtFailedReason error, err: %v", err)
		return err
	}
	return nil
}

func (s *postTaskBaseSvc) BoeBaseRegion(controlPlane sharedpb.ControlPlane, boeBaseEnv string) string {
	boeBaseRegion := ""
	switch controlPlane {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		boeBaseRegion = "china"
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N, sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD:
		switch boeBaseEnv {
		case model.BoeBaseEnvProd:
			boeBaseRegion = "i18n"
		case model.BoeBaseEnvProdSG:
			boeBaseRegion = "i18n"
		case model.BoeBaseEnvProdVA:
			boeBaseRegion = "i18n"
		default:
			boeBaseRegion = ""
		}
	default:
		boeBaseRegion = ""
	}
	return boeBaseRegion
}

func (s *postTaskBaseSvc) convertPostTaskControlPlane(project *model.ProjectInfo, boeBaseEnv string) string {
	// 只有 gecko 要特殊处理
	if project.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_HYBRID {
		switch boeBaseEnv {
		case model.GeckoTTROW:
			return jsons.Stringify([]string{sharedpb.ControlPlane_CONTROL_PLANE_I18N.String()})
		case model.GeckoTTUS:
			return jsons.Stringify([]string{sharedpb.ControlPlane_CONTROL_PLANE_US_TTP.String()})
		case model.GeckoTTEU:
			return jsons.Stringify([]string{sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP.String()})
		default:
			return jsons.Stringify([]string{project.ControlPlane.String()})
		}
	}

	return jsons.Stringify([]string{project.ControlPlane.String()})
}

// Create 创建发布单后置任务，会根据传入的项目信息，结合基准环境，可能创建多条后置任务。
// 此处做原子操作，要么都创建成功，要么一条也不创建
func (s *postTaskBaseSvc) Create(ctx context.Context, param *model.PostTaskCreateParam) error {
	ctx = logs.CtxAddKVs(ctx, "CreateReleasePostTasksRtId", param.ReleaseTicketID)

	postTaskConfig, err := s.GetPostTaskConfig(ctx, param.ReleaseTicketID)
	if err != nil {
		logs.CtxError(ctx, "GetPostTaskConfig: %v", err)
		return err
	}

	getPanicErrors, guard := utils.GetPanicGuard()
	eg := errgroup.Group{}
	var tasks []*entity.DBReleaseTicketPostTask
	changeItemsIfBoeProdNotExist := make([]*model.ProjectInfo, 0)
	for _, item := range param.ItemList {
		// 规避闭包引用for循环局部变量
		project := item
		eg.Go(func() error {
			defer guard(ctx)
			boeBaseEnvs, err := s.getProjectBoeBase(ctx, project, param.TaskType)
			if err != nil {
				logs.CtxError(ctx, "calEnvsToSync err %s", err.Error())
				return err
			}
			// 基准环境均不存在
			if len(boeBaseEnvs) == 0 {
				changeItemsIfBoeProdNotExist = append(changeItemsIfBoeProdNotExist, project)
				return nil
			}
			// 配置关闭
			projectConfig := s.getPostTaskConfigByProject(postTaskConfig, param.TaskType, project.ProjectType)
			if projectConfig == nil {
				logs.CtxInfo(ctx, "project config is nil for item %s", CommonUtils.ToJson(item))
				return nil
			}
			if !projectConfig.Enabled {
				logs.CtxInfo(ctx, "project config not enable for item %s", CommonUtils.ToJson(item))
				return nil
			}
			// 发布后同步基准，校验能否解析出部署工单，如果不能就不创建同步任务
			if param.TaskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE && !s.checkItemForwardDeploymentTicketValidate(ctx, param.ReleaseTicketID, item) {
				logs.CtxInfo(ctx, "forward deployment ticket not validate for item %s", CommonUtils.ToJson(item))
				return nil
			}
			for _, boeBaseEnv := range boeBaseEnvs {
				logs.CtxInfo(ctx, "CreateSyncBoeBaseReleasePostTasks boeBaseEnvs %s", boeBaseEnv)
				for _, postTaskMode := range projectConfig.PostTaskModes {
					if postTaskMode == workflowpb.PostTaskMode_POST_TASK_MODE_UNSPECIFIED {
						continue
					}
					taskID, err := idgen.GetID(ctx)
					if err != nil {
						logs.CtxError(ctx, "get id err %s", err.Error())
						return err
					}
					tasks = append(tasks, &entity.DBReleaseTicketPostTask{
						TaskID:          taskID,
						ReleaseTicketID: param.ReleaseTicketID,
						TaskType:        param.TaskType.String(),
						TaskStatus:      release_ticketpb.PostTaskStatus_POST_TASK_STATUS_PENDING.String(),
						ProjectType:     project.ProjectType.String(),
						ProjectUniqueID: project.ProjectUniqueID,
						ProjectName:     project.ProjectName,
						BoeBaseEnv:      boeBaseEnv,
						ControlPlanes:   s.convertPostTaskControlPlane(project, boeBaseEnv),
						// 使用bytecycle服务账号操作多环境平台，变更人始终为服务账号
						Operator:     "bytecycle",
						CreatedAt:    time.Now(),
						PostTaskMode: postTaskMode.String(),
					})
				}
			}
			return nil
		})
	}
	if err := eg.Wait(); err != nil {
		logs.CtxError(ctx, "[CreateReleaseTicketPostTaskError] %s", err.Error())
	}

	if err := getPanicErrors(); err != nil {
		return fmt.Errorf("[CreateReleaseTicketPostTaskError] %s", err.Error())

	}

	// task=0 的时候，更会出现 基准不存在的case
	// ProjectUniqueID + projectType 不存在，则更新 boe_prod_not_exist
	if len(changeItemsIfBoeProdNotExist) > 0 {
		if err := s.updateRTChangeItemsIfBoeProdNotExist(ctx, param.ReleaseTicketID, changeItemsIfBoeProdNotExist); err != nil {
			logs.CtxInfo(ctx, "update rt changeitems if boeprodnotexist, releaseTicketID = %d", param.ReleaseTicketID)
			return nil
		}
	}

	if len(tasks) == 0 {
		logs.CtxInfo(ctx, "no boe base env to sync, releaseTicketID = %d", param.ReleaseTicketID)
		return nil
	}

	if err := s.postTaskDao.BatchCreate(ctx, tasks); err != nil {
		return fmt.Errorf("[CreateReleaseTicketPostTaskError] %s", err.Error())
	}
	return nil
}

func needAddGeckoRegionEnvTag(channels []*paaspb.ChannelItem, geckoRegion paaspb.GeckoControlPlane) bool {
	return gslice.Any(channels, func(item *paaspb.ChannelItem) bool {
		return item.GetRegion() == geckoRegion && item.GetGeckoEnvType() == paaspb.GeckoEnvType_GECKO_ENV_TYPE_TEST
	})
}

func (s *postTaskBaseSvc) getGeckoProjectBoeBase(ctx context.Context, project *model.ProjectInfo, taskType release_ticketpb.PostTaskType) (envTags []string) {
	// gecko 暂不支持回滚后同步基准
	if taskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK {
		return envTags
	}
	// 研发流程绑定的所有内测channel在DeployTarget
	channelItems := project.RawChangeItem.GetDeployTarget().GetChannelItems()
	// 存在gecko region 的内测 channel，需要同步基准
	if needAddGeckoRegionEnvTag(channelItems, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_CN) {
		envTags = append(envTags, model.GeckoIESCN)
	}
	if needAddGeckoRegionEnvTag(channelItems, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_BOE_CN) {
		envTags = append(envTags, model.GeckoIESBOE)
	}
	if needAddGeckoRegionEnvTag(channelItems, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_I18N) {
		envTags = append(envTags, model.GeckoIESI18N)
	}
	if needAddGeckoRegionEnvTag(channelItems, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_TT_ROW) {
		envTags = append(envTags, model.GeckoTTROW)
	}
	if needAddGeckoRegionEnvTag(channelItems, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_EU_TTP) {
		envTags = append(envTags, model.GeckoTTEU)
	}
	if needAddGeckoRegionEnvTag(channelItems, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_US_TTP) {
		envTags = append(envTags, model.GeckoTTUS)
	}

	logs.CtxInfo(ctx, "getGeckoProjectBoeBase res: %s", utils.JSONToString(ctx, envTags))
	return envTags
}

// checkItemForwardDeploymentTicketValidate 检测某个item的正向工单是否能解析出来，兜底一些业务部署流水线不规范的情况
func (s *postTaskBaseSvc) checkItemForwardDeploymentTicketValidate(ctx context.Context, releaseTicketID uint64, projectInfo *model.ProjectInfo) bool {
	targetStagePipeline, err := s.findItemDeployPipeline(ctx, releaseTicketID, projectInfo.ProjectUniqueID, projectInfo.ControlPlane)
	if err != nil {
		logs.CtxError(ctx, "findItemDeployPipeline err: %v", err)
		return false
	}

	ticketInfo, err := s.rtProjectSvc.GetRtProjectByProjectType(ctx, projectInfo.ProjectType).GetTicketInfo(ctx, rtproject.TicketQuery{
		PipelineID:              targetStagePipeline.PipelineID,
		ControlPlane:            projectInfo.ControlPlane,
		ProjectUniqueID:         projectInfo.ProjectUniqueID,
		SolveNoDeploymentIdCase: true,
	})
	if err != nil {
		logs.CtxError(ctx, "GetTicketInfo err: %v", err)
		return false
	}

	if ticketInfo == nil {
		logs.CtxError(ctx, "ticketInfo is nil, projectUniqueID: %s, ControlPlane: %s", projectInfo.ProjectUniqueID, projectInfo.ControlPlane.String())
		return false
	}

	return true
}

// 检查对应基准下的服务是否存在
func (s *postTaskBaseSvc) getProjectBoeBase(ctx context.Context, project *model.ProjectInfo, taskType release_ticketpb.PostTaskType) ([]string, error) {
	switch project.ProjectType {
	case sharedpb.ProjectType_PROJECT_TYPE_CRONJOB:
		// cronjob 只支持 BoeBaseEnvProd 基准环境
		return []string{model.BoeBaseEnvProd}, nil
	case sharedpb.ProjectType_PROJECT_TYPE_TCE:
		if project.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_CN {
			if exist, _ := s.boeBaseEnvAndRegionExist(ctx, "tce", model.BoeBaseEnvProd, project.ProjectUniqueID, project.ControlPlane); exist {
				return []string{model.BoeBaseEnvProd}, nil
			}
			logs.CtxInfo(ctx, "cannot found psm: %s, cp: %s boe base, ignore", project.ProjectUniqueID, project.ControlPlane.String())
			return []string{}, nil
		}
		if gslice.Contains([]sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_I18N, sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD}, project.ControlPlane) {
			var res []string
			for _, boeBaseEnv := range model.I18nBoeBaseEnvs {
				if exist, _ := s.boeBaseEnvAndRegionExist(ctx, "tce", boeBaseEnv, project.ProjectUniqueID, project.ControlPlane); exist {
					res = append(res, boeBaseEnv)
				}
			}
			if len(res) == 0 {
				logs.CtxInfo(ctx, "cannot found psm: %s, cp: %s boe base, ignore", project.ProjectUniqueID, project.ControlPlane.String())
			}
			return res, nil
		}
		return []string{}, fmt.Errorf(fmt.Sprintf("unsupport control plane: %s", project.ControlPlane.String()))
	case sharedpb.ProjectType_PROJECT_TYPE_FAAS:
		if project.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_CN {
			return []string{model.BoeBaseEnvProd}, nil
		}
		// 仅用来获取后置任务的流水线模版, prod_va 才会获取 i18n的模版 见代码 post_task/v2/biz/boe_base_sync/pipeline.go:557
		if project.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_I18N {
			return []string{model.BoeBaseEnvProdVA}, nil
		}
		if project.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD {
			return []string{model.BoeBaseEnvProdVA}, nil
		}
		return []string{}, fmt.Errorf(fmt.Sprintf("unsupport control plane: %s", project.ControlPlane.String()))
	case sharedpb.ProjectType_PROJECT_TYPE_WEB:
		if project.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_CN {
			return []string{model.BoeBaseEnvProd}, nil
		}
		if project.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_I18N {
			return []string{model.BoeBaseEnvProdVA}, nil
		}
		return []string{}, fmt.Errorf(fmt.Sprintf("unsupport control plane: %s", project.ControlPlane.String()))
	case sharedpb.ProjectType_PROJECT_TYPE_HYBRID:
		return s.getGeckoProjectBoeBase(ctx, project, taskType), nil
	default:
		return []string{}, fmt.Errorf(fmt.Sprintf("unsupport project type: %s", project.ProjectType.String()))
	}
}

func (s *postTaskBaseSvc) calEnvsToSync(ctx context.Context, project *model.ProjectInfo, boeBaseEnvs map[string]struct{}) error {
	if project.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_CRONJOB {
		boeBaseEnvs[model.BoeBaseEnvProd] = struct{}{}
		return nil
	}
	f := func(envs []string) error {
		for _, env := range envs {
			if _, ok := boeBaseEnvs[env]; ok {
				continue
			}
			exist, err := s.boeBaseEnvExist(ctx, model.ServiceTypeMap[project.ProjectType], env, project.ProjectUniqueID)
			if err != nil {
				return err
			}
			if exist {
				boeBaseEnvs[env] = struct{}{}
			}
		}
		return nil
	}
	switch project.ControlPlane {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return f([]string{model.BoeBaseEnvProd})
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N, sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD:
		return f([]string{model.BoeBaseEnvProd, model.BoeBaseEnvProdSG, model.BoeBaseEnvProdVA})
	case sharedpb.ControlPlane_CONTROL_PLANE_TTP, sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return f([]string{model.BoeBaseEnvProdTTP})
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return f([]string{model.BoeBaseEnvProd})

	default:
		logs.CtxError(ctx, "unexpected control plane %d", project.ControlPlane)
		return nil
	}
}

func (s *postTaskBaseSvc) boeBaseEnvExist(ctx context.Context, serviceType string, env string, psm string) (bool, error) {
	envPlatformSDK, err := envprovider.GetSDKByIDCAndTceEnv(tce_config.IDC_CN, tce_config.ENV_BOE)
	if err != nil {
		logs.CtxError(ctx, "get env platform sdk from provider failed, %v", err)
		return false, errors.WithMessage(err, "get env platform sdk from provider failed")
	}
	exist, _, err := envPlatformSDK.CheckIfServiceExistInAppEnv(ctx, env, serviceType, psm)
	if err != nil {
		logs.CtxError(ctx, "CheckIfServiceExistInAppEnv failed, %v", err)
		return false, err
	}
	return exist, nil
}

func (s *postTaskBaseSvc) boeBaseEnvAndRegionExist(ctx context.Context, serviceType string, env string, psm string, controlPlane sharedpb.ControlPlane) (bool, error) {
	envPlatformSDK, err := envprovider.GetSDKByIDCAndTceEnv(tce_config.IDC_CN, tce_config.ENV_BOE)
	if err != nil {
		logs.CtxError(ctx, "get env platform sdk from provider failed, %s", err.Error())
		return false, errors.WithMessage(err, "get env platform sdk from provider failed")
	}
	serviceExist, item, err := envPlatformSDK.CheckIfServiceExistInAppEnvWithClusterInfo(ctx, env, serviceType, psm)
	if err != nil {
		logs.CtxError(ctx, "CheckIfServiceExistInAppEnv failed, %s", err.Error())
		return false, err
	}
	logs.CtxInfo(ctx, "[boeBaseEnvAndRegionExist] serviceExist %s, item: %s", serviceExist, CommonUtils.ToJson(item))
	if !serviceExist {
		return false, nil
	}

	if item == nil && len(item.Instances) == 0 {
		return false, nil
	}
	var region string
	switch env {
	case model.BoeBaseEnvProd:
		region = "BOE-Arbutus"
		if gslice.Contains([]sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_I18N, sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD}, controlPlane) {
			region = "BOE-i18n"
		}
	case model.BoeBaseEnvProdSG:
		region = "BOE-i18n"
	case model.BoeBaseEnvProdVA:
		region = "BOE-i18n"
	default:
		logs.CtxWarn(ctx, "not supported boe base: %s", env)
		return false, nil
	}

	regionExist := false
	for _, instance := range item.Instances {
		if instance.Zone == region {
			regionExist = true
			break
		}
	}
	return regionExist, nil
}

// 发布单基准环境都不存在的changeItems，更新 boe_prod_not_exist
func (s *postTaskBaseSvc) updateRTChangeItemsIfBoeProdNotExist(ctx context.Context, rtId uint64, projects []*model.ProjectInfo) error {
	logs.CtxInfo(ctx, "CreateSyncBoeBaseReleasePostTasks ChangeItemsIfBoeProdNotExist %s", utils.JSONToString(ctx, projects))
	// 开启事务
	transactionCtx, err := mysql.WrapCtxWithTransaction(ctx)
	if err != nil {
		return erri.Error(err)
	}

	defer cdutils.PanicGuard(transactionCtx)
	defer func() {
		if err != nil {
			e := mysql.RollbackTransaction(transactionCtx)
			if e != nil {
				logs.CtxError(transactionCtx, "transaction rollback err", e)
			}
		}
	}()

	for _, p := range projects {
		err = s.releaseTicketChangeItemDao.UpdateBoeProdNotExist(transactionCtx, &repository.ChangeItemQuery{
			ReleaseTicketID: rtId,
			ControlPlane:    p.ControlPlane.String(),
			ProjectUniqueID: p.ProjectUniqueID,
			ProjectType:     p.ProjectType.String(),
		})
		if err != nil {
			return erri.Error(err)
		}
	}
	// 提交事务
	if err = mysql.CommitTransaction(transactionCtx); err != nil {
		return erri.Error(err)
	}
	return nil
}

func (s *postTaskBaseSvc) getPostTaskConfigByProject(config *workflowpb.PostTaskConfig, taskType release_ticketpb.PostTaskType, projectType sharedpb.ProjectType) *workflowpb.PostTaskProjectConfig {
	switch projectType {
	case sharedpb.ProjectType_PROJECT_TYPE_TCE:
		return choose.If(taskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE, config.GetPublishPostTaskConfig().GetTceConfig(), config.GetRollbackPostTaskConfig().GetTceConfig())
	case sharedpb.ProjectType_PROJECT_TYPE_FAAS:
		return choose.If(taskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE, config.GetPublishPostTaskConfig().GetFaasConfig(), config.GetRollbackPostTaskConfig().GetFaasConfig())
	case sharedpb.ProjectType_PROJECT_TYPE_WEB:
		return choose.If(taskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE, config.GetPublishPostTaskConfig().GetWebConfig(), config.GetRollbackPostTaskConfig().GetWebConfig())
	case sharedpb.ProjectType_PROJECT_TYPE_HYBRID:
		return choose.If(taskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE, config.GetPublishPostTaskConfig().GetGeckoConfig(), config.GetRollbackPostTaskConfig().GetGeckoConfig())
	case sharedpb.ProjectType_PROJECT_TYPE_CRONJOB:
		return choose.If(taskType == release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE, config.GetPublishPostTaskConfig().GetCronjobConfig(), config.GetRollbackPostTaskConfig().GetCronjobConfig())
	default:
		return nil
	}
}

func (s *postTaskBaseSvc) TransStringToControlPlan(ctx context.Context, controlPlans string) []sharedpb.ControlPlane {
	var controlPlanes []sharedpb.ControlPlane
	var strControlPlanes []string
	err := json.UnmarshalString(controlPlans, &strControlPlanes)
	if err != nil {
		logs.CtxError(ctx, "TransStringToControlPlan unmarshal controlPlanes err: %s", err.Error())
		return controlPlanes
	}
	for _, strControlPlane := range strControlPlanes {
		controlPlanes = append(controlPlanes, enumutil.Enum(strControlPlane,
			sharedpb.ControlPlane_value, sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED))
	}
	return controlPlanes
}

func (s *postTaskBaseSvc) DelPostTask(ctx context.Context, taskID uint64) error {
	s.postTaskDao.DeleteByID(ctx, taskID)
	return nil
}

func (s *postTaskBaseSvc) BoeZoneSpecialFilter(ctx context.Context, controlPlane sharedpb.ControlPlane, clusters []*tceopensdk.ClusterInfo) []*tceopensdk.ClusterInfo {
	switch controlPlane {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		clusters = gslice.Filter(clusters, func(c *tceopensdk.ClusterInfo) bool {
			return c.Resource.Zone == "BOE-Arbutus"
		})
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N, sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD:
		clusters = gslice.Filter(clusters, func(c *tceopensdk.ClusterInfo) bool {
			return c.Resource.Zone == "BOE-i18n"
		})
	default:
		// 暂时没有处理的case
	}
	return clusters
}
