package branch_del

//go:generate mockgen -destination mock/branch.go code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/post_task/v2/biz/branch_del PostTaskBranchSvc

import (
	"context"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	envsdk "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/env"
	integratesdk "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tce"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/branch_manager"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/config"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_operation_reporter"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtproject"
	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"
	"code.byted.org/devinfra/hagrid/pkg/envplatform"
)

type PostTaskBranchSvc interface {
	// CreateDelBranchPostTasks 创建<分支删除任务>
	CreateDelBranchPostTasks(ctx context.Context, rt *entity.DBReleaseTicket, lock redis.DistributedLock) error
	// SubmitDelBranchTPostTaskStatus 发布完成后自动删除分支
	SubmitDelBranchTPostTaskStatus(ctx context.Context, task *entity.DBReleaseTicketPostTask, lock redis.DistributedLock) error
}

type postTaskBranchSvc struct {
	postTaskDao                repository.ReleaseTicketPostTaskDao
	releaseTicketChangeItemDao repository.ReleaseTicketChangeItemDao
	rtDao                      repository.ReleaseTicketDao
	releaseApproverDao         repository.ReleaseApproverDao
	proRollbackInfoDao         repository.ProjectRollbackInfoDao
	tceSvc                     tce.TceSdkSvc
	branchSyncTaskDao          repository.BranchSyncTaskDao
	workflowConfigHelper       config.WorkflowConfigHelper
	dlFactory                  redis.DistributedLockFactory
	stageDao                   repository.StageDao
	nodeConfig                 config.NodeConfigHelper
	integrationSvc             integratesdk.IntegrationSvc
	envSDK                     envplatform.SDK
	devTaskSvc                 integratesdk.DevSvc
	changeItemSvc              change_item.ChangeItemSvc
	gitSvc                     integratesdk.GitServerSvc
	branchManager              branch_manager.BranchManager
	crossborderSDK             crossborder.ICrossborderSDK
	envApi                     envsdk.CDEnvApiSvc
	rtProjectSvc               rtproject.RtProjectService
	stagePipelineDao           repository.StagePipelineDao
	rtOperationReporter        rt_operation_reporter.RtOperationReporter
}

func NewPostTaskBranchSvc() PostTaskBranchSvc {
	return &postTaskBranchSvc{
		postTaskDao:                repository.NewReleaseTicketPostTaskDao(),
		releaseTicketChangeItemDao: repository.NewReleaseTicketChangeItemDao(),
		rtDao:                      repository.NewReleaseTicketDao(),
		releaseApproverDao:         repository.NewReleaseApproverDao(),
		tceSvc:                     tce.TCESDK,
		workflowConfigHelper:       config.NewWorkflowConfigHelper(),
		branchSyncTaskDao:          repository.NewBranchSyncTaskDao(),
		dlFactory:                  redis.NewDistributedLockFactory(),
		stageDao:                   repository.NewStageDao(),
		integrationSvc:             integratesdk.NewIntegrationSvc(),
		envSDK:                     envplatform.DefaultSDK(),
		devTaskSvc:                 integratesdk.NewDevSvc(),
		nodeConfig:                 config.NewNodeConfigHelper(),
		proRollbackInfoDao:         repository.NewProjectRollbackInfoDao(),
		changeItemSvc:              change_item.NewChangeItemSvc(),
		gitSvc:                     integratesdk.NewGitServerSvc(),
		branchManager:              branch_manager.NewBranchManager(),
		crossborderSDK:             crossborder.NewCrossborderSDK(),
		envApi:                     envsdk.NewEnvPlatformSvc(),
		rtProjectSvc:               rtproject.NewRtProjectSvc(),
		stagePipelineDao:           repository.NewStagePipelineDao(),
		rtOperationReporter:        rt_operation_reporter.NewRtOperationReporter(),
	}
}
