package query

import (
	"context"
	"fmt"
	"os"

	"github.com/pkg/errors"
	"gorm.io/gorm"

	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/gopkg/lang/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	cdI18n "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/i18n"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils/enumutil"
)

func (s *postTaskQuerySvc) List(ctx context.Context, rtID uint64) (
	*release_ticketpb.ListReleaseTicketPostTaskResp, error) {
	tasks, err := s.postTaskDao.FindByReleaseTicketID(ctx, rtID)
	if err != nil {
		return nil, err
	}
	// 查询 changeItem
	changeItems, err := s.releaseTicketChangeItemDao.GetByReleaseTicketID(ctx, rtID)
	if err != nil {
		return nil, err
	}

	info, err := s.rtDao.GetByReleaseTicketID(ctx, rtID)
	if err != nil {
		return nil, err
	}
	workflowConfigPb, err := s.workflowConfigHelper.DbToPb(ctx, info.WorkflowConfig, false, 0)
	if err != nil {
		return nil, err
	}
	cnf, ok := workflowConfigPb.InstanceConfig.(*workflowpb.WorkflowConfig_ReleaseTicketConfig)
	if !ok {
		return nil, erri.Errorf("invalid workflow config")
	}

	unexpectedPostTasks := make([]*entity.DBReleaseTicketChangeItem, 0)
	if info.Status == release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASED.String() && cnf.ReleaseTicketConfig.AfterCheckConfig.SyncBoeProdEnvAfterDeploy {
		unexpectedPostTasks = findUnexpectedPostTasks(ctx, changeItems, tasks)
	}

	// 查询发布单后置任务
	pbTasks := s.baseSvc.BuildPostTask(ctx, tasks, changeItems)
	// 查询不符合预期的 changeItems
	unexpectedPostTasksPb := gslice.Map(unexpectedPostTasks, func(t *entity.DBReleaseTicketChangeItem) *release_ticketpb.RTPostTask {
		return &release_ticketpb.RTPostTask{
			TaskId:          0,
			TaskName:        "",
			TaskDescription: "",
			ReleaseTicketId: t.ReleaseTicketID,
			TaskType:        release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE,
			TaskStatus:      release_ticketpb.PostTaskStatus_POST_TASK_STATUS_UNSPECIFIED,
			ProjectType: enumutil.Enum(t.ProjectType, sharedpb.ProjectType_value,
				sharedpb.ProjectType_PROJECT_TYPE_UNSPECIFIED),
			ProjectUniqueId: t.ProjectUniqueID,
			BoeBaseEnv:      "",
			DeploymentId:    "0",
			Operator:        "",
			ModifyType:      "",
			ErrCode:         release_ticketpb.PostTaskErrCode(release_ticketpb.PostTaskErrCode_value[choose.If[string](t.BoeProdNotExist == 1, release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_BOE_PROD_NOT_EXIST.String(), release_ticketpb.PostTaskErrCode_POST_TASK_ERR_CODE_NOT_DEPLOYED.String())]),
			ControlPlanes:   []sharedpb.ControlPlane{sharedpb.ControlPlane(sharedpb.ControlPlane_value[t.ControlPlane])},
		}
	})

	// 查询发布阶段后置任务
	var releaseStagePostTasks []*release_ticketpb.StagePostTask
	branchSyncTask, err := s.branchSyncTaskDao.GetByRtidScene(ctx, rtID, entity.SceneReleaseToArchive)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if branchSyncTask != nil {
		var detail = release_ticket_sharedpb.ReleaseBranchBackwardCheckDetail{}
		if branchSyncTask.Detail != "" {
			err = utils.PBUnmarshaler.Unmarshal(conv.UnsafeStrToBytes(branchSyncTask.Detail), &detail)
			if err != nil {
				return nil, err
			}
		}
		releaseStagePostTasks = gslice.Map(detail.GetItems(),
			func(d *release_ticket_sharedpb.ReleaseBranchBackwardCheckItem) *release_ticketpb.StagePostTask {
				return &release_ticketpb.StagePostTask{
					ProjectType:       d.GetProjectType(),
					Psm:               d.GetPsm(),
					GitRepoName:       d.GetGitRepoName(),
					GitRepoUrl:        d.GetGitRepoUrl(),
					ReleaseBranch:     d.GetReleaseBranch().GetName(),
					ArchiveBranch:     d.GetArchiveBranch().GetName(),
					BranchMergeResult: d.GetBranchBackwardCheckResult(),
				}
			})
		if err != nil {
			return nil, err
		}
	}

	list := append(pbTasks, unexpectedPostTasksPb...)
	s.SetPostTaskName(ctx, list, workflowConfigPb, choose.If(cdauth.Lang(ctx) == "", "cn", cdauth.Lang(ctx)))

	return &release_ticketpb.ListReleaseTicketPostTaskResp{
		PostTasks:             pbTasks,
		ReleaseStagePostTasks: releaseStagePostTasks,
		UnexpectedPostTasks:   unexpectedPostTasksPb,
	}, nil
}

func (s *postTaskQuerySvc) SetPostTaskName(ctx context.Context, tasks []*release_ticketpb.RTPostTask, wf *workflowpb.WorkflowConfig, lang string) {
	releasePostTaskName := cdI18n.GetMessage(ctx, lang, cdI18n.ReleasePostTaskName)
	releasePostTaskDescription := cdI18n.GetMessage(ctx, lang, cdI18n.ReleasePostTaskDescription)

	rollbackPostTaskName := cdI18n.GetMessage(ctx, lang, cdI18n.RollbackPostTaskName)
	rollbackPostTaskDescription := cdI18n.GetMessage(ctx, lang, cdI18n.RollbackPostTaskDescription)

	gcPostTaskName := cdI18n.GetMessage(ctx, lang, cdI18n.GCServicePostTaskName)
	gcPostTaskDescription := cdI18n.GetMessage(ctx, lang, cdI18n.GCServicePostTaskDescription)

	delBranchPostTaskName := cdI18n.GetMessage(ctx, lang, cdI18n.DelBranchPostTaskName)
	delBranchPostTaskDescription := cdI18n.GetMessage(ctx, lang, cdI18n.DelBranchPostTaskDescription)

	if wf.GetReleaseTicketConfig().GetIsHotfix() {
		delBranchPostTaskName = cdI18n.GetMessage(ctx, lang, cdI18n.DelHotfixBranchPostTaskName, delBranchPostTaskName)
		delBranchPostTaskDescription = cdI18n.GetMessage(ctx, lang, cdI18n.DelHotfixBranchPostTaskDescription, delBranchPostTaskName)
	}

	for _, t := range tasks {
		switch t.TaskType {
		case release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE:
			t.TaskName = releasePostTaskName
			t.TaskDescription = releasePostTaskDescription
		case release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK:
			t.TaskName = rollbackPostTaskName
			t.TaskDescription = rollbackPostTaskDescription
		case release_ticketpb.PostTaskType_POST_TASK_TYPE_GC_ENV:
			t.TaskName = gcPostTaskName
			t.TaskDescription = gcPostTaskDescription
		case release_ticketpb.PostTaskType_POST_TASK_TYPE_DEL_BRANCH:
			t.TaskName = delBranchPostTaskName
			t.TaskDescription = delBranchPostTaskDescription
		default:
		}
	}
}

func (s *postTaskQuerySvc) getModifyType(taskType string) string {
	switch taskType {
	case release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String(),
		release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK.String():
		return "upgrade"
	case release_ticketpb.PostTaskType_POST_TASK_TYPE_GC_ENV.String():
		return "delete"
	default:
		return ""
	}
}

func (s *postTaskQuerySvc) FindUnfinished(ctx context.Context) ([]*entity.DBReleaseTicketPostTask, error) {
	return s.postTaskDao.FindByStatus(ctx, []string{
		release_ticketpb.PostTaskStatus_POST_TASK_STATUS_PENDING.String(),
		release_ticketpb.PostTaskStatus_POST_TASK_STATUS_RUNNING.String(),
	})
}

func (s *postTaskQuerySvc) FindUnfinishedByInstanceTceEnv(ctx context.Context) ([]*entity.DBReleaseTicketPostTask, error) {
	return s.postTaskDao.FindByStatusAndInstanceTceEnv(ctx, []string{
		release_ticketpb.PostTaskStatus_POST_TASK_STATUS_PENDING.String(),
		release_ticketpb.PostTaskStatus_POST_TASK_STATUS_RUNNING.String(),
	}, os.Getenv("TCE_ENV"))
}

func findUnexpectedPostTasks(ctx context.Context, rtChangeItems []*entity.DBReleaseTicketChangeItem, rtPostTasks []*entity.DBReleaseTicketPostTask) []*entity.DBReleaseTicketChangeItem {
	unexpectedPostTasks := make([]*entity.DBReleaseTicketChangeItem, 0)

	tasksMap := gslice.GroupBy(gslice.Filter(rtPostTasks, func(task *entity.DBReleaseTicketPostTask) bool {
		return gslice.Contains([]string{release_ticketpb.PostTaskType_POST_TASK_TYPE_ROLLBACK.String(), release_ticketpb.PostTaskType_POST_TASK_TYPE_RELEASE.String()}, task.TaskType)
	}), func(t *entity.DBReleaseTicketPostTask) string {
		return fmt.Sprintf("%s:%s", t.ProjectType, t.ProjectUniqueID)
	})

	// 过滤 属于 TCE 的项目
	tceChangeItems := gslice.Filter(rtChangeItems, func(item *entity.DBReleaseTicketChangeItem) bool {
		return item.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_TCE.String()
	})

	for _, changeItem := range tceChangeItems {
		key := fmt.Sprintf("%s:%s", changeItem.ProjectType, changeItem.ProjectUniqueID)
		// cn 和 i18n 基准只能同存在
		existTasks := tasksMap[key]
		if len(existTasks) == 0 {
			unexpectedPostTasks = append(unexpectedPostTasks, changeItem)
		}
	}
	return unexpectedPostTasks
}

func (s *postTaskQuerySvc) GetPostTaskByID(ctx context.Context, TaskID uint64) (*entity.DBReleaseTicketPostTask, error) {
	task, err := s.postTaskDao.FindByPostTaskID(ctx, TaskID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logs.CtxError(ctx, "find post task error: %v, task id: %v", err, TaskID)
		return nil, err
	}
	return task, nil
}
