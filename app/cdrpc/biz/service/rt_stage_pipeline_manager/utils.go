package rt_stage_pipeline_manager

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	"code.byted.org/devinfra/hagrid/pkg/cicd_utils"
	"code.byted.org/devinfra/hagrid/pkg/devops_pipeline_sdk"
)

const PipelineStatusSuccess string = "SUCCESS"

const PipelineStatusFailed = "FAILED"

const PipelineStatusPending = "PENDING"

type GetProjectKeyRequest struct {
	ProjectType     string
	ProjectUniqueID string
	ControlPlane    string
}

func getProjectKey(req *GetProjectKeyRequest) string {
	return fmt.Sprintf("%s-%s-%s", req.ProjectType, req.ProjectUniqueID, req.ControlPlane)
}

func getDependencies(ctx context.Context, dependencyItems string) []*devops_pipeline_sdk.Dependencies {
	items, err := change_item.UnmarshalToDependencyItems([]byte(dependencyItems))

	var deps []*devops_pipeline_sdk.Dependencies

	if err == nil {
		logs.CtxInfo(ctx, "UnmarshalToDependencyItems Result: %#v, Param: %s", items, dependencyItems)

		if len(items) > 0 {
			deps = gslice.Map(items, func(item *change_itempb.DependencyItem) *devops_pipeline_sdk.Dependencies {
				return &devops_pipeline_sdk.Dependencies{
					ProjectType:     item.GetProjectType(),
					ProjectUniqueID: item.GetProjectUniqueId(),
				}
			})
		}
	} else {
		logs.CtxError(ctx, "UnmarshalToDependencyItems Error: %#v, Param: %s", err, dependencyItems)
	}

	logs.CtxInfo(ctx, "deps: %#v", deps)

	return deps
}

func transDependencyItemsForPipelineSDK(ctx context.Context, items []*change_itempb.DependencyItem) []*devops_pipeline_sdk.Dependencies {
	if items == nil {
		return nil
	}

	deps := gslice.Map(items, func(item *change_itempb.DependencyItem) *devops_pipeline_sdk.Dependencies {
		return &devops_pipeline_sdk.Dependencies{
			ProjectType:     item.GetProjectType(),
			ProjectUniqueID: item.GetProjectUniqueId(),
		}
	})

	logs.CtxInfo(ctx, "deps: %#v", deps)

	return deps
}

// 传入更新后的 deps 信息，返回馈当前 changeItem 正确的依赖信息。
// 策略是：targetDeps 里有，取 targetDeps 命中的，否则取 changeItem 自带的
func getProjectDependencies(ctx context.Context, changeItem *entity.DBReleaseTicketChangeItem, targetDeps []*change_itempb.Dependency) []*devops_pipeline_sdk.Dependencies {
	if changeItem == nil {
		return nil
	}
	curKey := cicd_utils.ProjectUniqKey{ProjectUniqueId: changeItem.ProjectUniqueID, ProjectType: sharedpb.ProjectType(sharedpb.ProjectType_value[changeItem.ProjectType])}
	targetDep := gslice.Find(targetDeps, func(dependency *change_itempb.Dependency) bool {
		itemKey := cicd_utils.ProjectUniqKey{ProjectUniqueId: dependency.ProjectUniqueId, ProjectType: dependency.ProjectType}
		return curKey == itemKey
	})

	var dependencies []*devops_pipeline_sdk.Dependencies
	if targetDep.IsOK() {
		logs.CtxInfo(ctx, "[getProjectDependencies]依赖关系从目标依赖获得, curKey=%v, deps=%v", curKey, targetDep.Value().GetDependencyItems())
		dependencies = transDependencyItemsForPipelineSDK(ctx, targetDep.Value().GetDependencyItems())
	} else {
		logs.CtxInfo(ctx, "[getProjectDependencies]依赖关系从change item获得, %v", changeItem.DependencyItems)
		dependencies = getDependencies(ctx, changeItem.DependencyItems)
	}

	return dependencies
}

// 获取更新后的项目依赖信息，currentChangeItems 里的个数可能小于 BaselineDeps
func getTargetDeps(ctx context.Context, oneControlPlaneChangeItems []*entity.DBReleaseTicketChangeItem, baselineDeps []*change_itempb.Dependency) ([]*change_itempb.Dependency, error) {
	curControlPlane := sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED.String()
	for _, cp := range oneControlPlaneChangeItems {
		if curControlPlane != sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED.String() && cp.ControlPlane != curControlPlane {
			return nil, errors.New("change items must be in one control plane")
		}
		curControlPlane = cp.ControlPlane
	}

	currentDeps, err := change_item.DependencyConvertToPB(ctx, oneControlPlaneChangeItems)
	if err != nil {
		return nil, errors.WithMessage(err, "get change item dependency failed")
	}

	targetDeps := currentDeps

	// 如果 BaselineDeps > 0 则需要尝试更新当前控制面的依赖项了
	if len(baselineDeps) > 0 {
		logs.CtxInfo(ctx, "[getTargetDeps]按基线依赖重新计算依赖关系")
		manager := cicd_utils.NewDepManger()
		removedDeps := gslice.Filter(baselineDeps, func(dependency *change_itempb.Dependency) bool {
			itemKey := cicd_utils.ProjectUniqKey{ProjectUniqueId: dependency.ProjectUniqueId, ProjectType: dependency.ProjectType}
			matchedOne := gslice.Find(currentDeps, func(cur *change_itempb.Dependency) bool {
				curKey := cicd_utils.ProjectUniqKey{ProjectUniqueId: cur.ProjectUniqueId, ProjectType: cur.ProjectType}
				return itemKey == curKey
			})

			// 如果 currentDeps 里没有找到，则说明是要 removed 的
			return !matchedOne.IsOK()
		})
		logs.CtxInfo(ctx, "[getTargetDeps]按基线依赖重新计算依赖关系, 移除个数 %d", len(removedDeps))

		for _, baselineDep := range baselineDeps {
			logs.CtxInfo(ctx, "[getTargetDeps]枚举baseline Region=%s,base dep=%v", curControlPlane, baselineDep)
		}
		for _, removedDep := range removedDeps {
			logs.CtxInfo(ctx, "[getTargetDeps]枚举removed Region=%s,base dep=%v", curControlPlane, removedDep)
		}

		targetDeps, err = manager.RebuildAfterRemoveNode(baselineDeps, removedDeps)

		if err != nil {
			logs.CtxError(ctx, "[getTargetDeps]移除处理过程失败 %s", err.Error())
			return nil, errors.WithMessage(err, "remove dependency error")
		}

		for _, targetDep := range targetDeps {
			logs.CtxInfo(ctx, "[getTargetDeps]枚举targetDep Region=%s,target dep=%v", curControlPlane, targetDep)
		}
	}

	return targetDeps, nil
}
