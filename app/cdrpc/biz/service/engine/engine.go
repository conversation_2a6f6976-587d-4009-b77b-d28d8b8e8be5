package engine

import (
	"context"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/gopkg/logs"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_flow_task/dispatcher"
)

type Engine interface {
	DispatchTask(ctx context.Context, req *release_ticketpb.DispatchTaskReq) (*release_ticketpb.DispatchTaskResp, error) // 流程引擎调用此接口
}

func New() Engine {
	return &engineImpl{
		dispatcherSvc: dispatcher.NewDispatcherSvc(),
	}
}

type engineImpl struct {
	dispatcherSvc dispatcher.DispatcherSvc
}

func (impl *engineImpl) DispatchTask(ctx context.Context, req *release_ticketpb.DispatchTaskReq) (*release_ticketpb.DispatchTaskResp, error) {
	logs.CtxInfo(ctx, "[DispatchTask] engine DispatchTask")
	resp, err := impl.dispatcherSvc.DispatchTask(ctx, req)
	return resp, err
}
