package change_item

import (
	"context"
	"testing"

	"code.byted.org/bits/monkey"
	"code.byted.org/canal/provider/tce"
	redisrepository "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis/repository"
	release_ticket_helper "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket/helper"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"
	"code.byted.org/iesarch/paas_sdk/tce_config"
	tceopensdk "code.byted.org/tce/tce_golang_sdk"
	"github.com/bytedance/mockey"
)

func (t *ChangeItemTestSuite) TestClusterInfoDataValidate() {
	t.T().Skip()
	mockey.PatchConvey("mock no error get cluster info", &testing.T{}, func() {
		defer mockey.Mock(tce.GetClusterInfoListByClusterIDListAndServiceID).Return(nil, nil).Build().UnPatch()
		err := ClusterInfoDataValidate(t.ctx, nil)
		t.Nil(err)

		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{})
		t.NotNil(err)

		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{},
		})
		t.NotNil(err)

		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{
				ServiceId: 1,
			},
		})
		t.NotNil(err)

		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{
				ServiceId:        1,
				SelectedClusters: []int64{1},
			},
			RolloutStrategy: "aa",
			SurgeConfig:     &change_itempb.SurgeConfig{},
		})
		t.Nil(err)

		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{
				ServiceId:        1,
				SelectedClusters: []int64{1},
				UpgradeMode:      "custom",
			},
			RolloutStrategy: "aa",
			SurgeConfig:     &change_itempb.SurgeConfig{},
		})
		t.Nil(err)

		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{
				ServiceId:        1,
				SelectedClusters: []int64{1},
				UpgradeMode:      "custom",
				StageSet: &change_itempb.StageSet{
					SingleDc: []*change_itempb.SingleDc{
						{},
					},
				},
			},
			RolloutStrategy: "aa",
			SurgeConfig:     &change_itempb.SurgeConfig{},
		})
		t.NotNil(err)
		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{
				ServiceId:        1,
				SelectedClusters: []int64{1},
				UpgradeMode:      "custom",
				StageSet: &change_itempb.StageSet{
					AllDc: []*change_itempb.AllDc{
						{},
					},
				},
			},
			RolloutStrategy: "aa",
			SurgeConfig:     &change_itempb.SurgeConfig{},
		})
		t.NotNil(err)
		err = ClusterInfoDataValidate(t.ctx, &change_itempb.ClusterInfo{
			UpgradeClusterSet: &change_itempb.UpgradeClusterSet{
				ServiceId:        1,
				SelectedClusters: []int64{1},
				UpgradeMode:      "custom",
				StageSet: &change_itempb.StageSet{
					Canary: []*change_itempb.CanaryDc{
						{},
					},
				},
			},
			RolloutStrategy: "aa",
			SurgeConfig:     &change_itempb.SurgeConfig{},
		})
		t.NotNil(err)
	})
	ctx := context.Background()
	isTrue := true
	isFalse := false
	independentCluster1 := &tceopensdk.ClusterInfo{
		Meta: &tceopensdk.ClusterInfoMeta{
			Id: 110,
		},
		Upgrade: &tceopensdk.ClusterInfoUpgrade{
			IsStandaloneRelease: &isTrue,
		},
	}
	independentCluster2 := &tceopensdk.ClusterInfo{
		Meta: &tceopensdk.ClusterInfoMeta{
			Id: 111,
		},
		Upgrade: &tceopensdk.ClusterInfoUpgrade{
			IsStandaloneRelease: &isTrue,
		},
	}
	commonCluster1 := &tceopensdk.ClusterInfo{
		Meta: &tceopensdk.ClusterInfoMeta{
			Id: 112,
		},
		Upgrade: &tceopensdk.ClusterInfoUpgrade{
			IsStandaloneRelease: &isFalse,
		},
	}
	commonCluster3 := &tceopensdk.ClusterInfo{
		Meta: &tceopensdk.ClusterInfoMeta{
			Id: 113,
		},
		Upgrade: &tceopensdk.ClusterInfoUpgrade{
			IsStandaloneRelease: &isFalse,
		},
	}
	mockey.PatchConvey("test ttp clusters check", &testing.T{}, func() {
		defer mockey.Mock(tce.GetClusterInfoListByClusterIDListAndServiceID).Return([]*tceopensdk.ClusterInfo{
			independentCluster1, independentCluster2, commonCluster1, commonCluster3,
		}, nil).Build().UnPatch()
		clusterSet := &change_itempb.UpgradeClusterSet{
			SelectedClusters: []int64{110, 111, 112, 113},
			ServiceId:        1,
			Idc:              tce_config.IDC_TTP,
		}
		err := ClusterTypeValidate(ctx, clusterSet)
		t.NoError(err)
	})
	mockey.PatchConvey("test independent clusters and common clusters", &testing.T{}, func() {
		defer mockey.Mock(tce.GetClusterInfoListByClusterIDListAndServiceID).Return([]*tceopensdk.ClusterInfo{
			independentCluster1, independentCluster2, commonCluster1, commonCluster3,
		}, nil).Build().UnPatch()
		clusterSet := &change_itempb.UpgradeClusterSet{
			SelectedClusters: []int64{110, 111, 112, 113},
			ServiceId:        1,
			Idc:              tce_config.IDC_CN,
		}
		err := ClusterTypeValidate(ctx, clusterSet)
		t.Error(err)
	})
	mockey.PatchConvey("more than one independent cluster", &testing.T{}, func() {
		defer mockey.Mock(tce.GetClusterInfoListByClusterIDListAndServiceID).Return([]*tceopensdk.ClusterInfo{
			independentCluster1, independentCluster2,
		}, nil).Build().UnPatch()
		clusterSet := &change_itempb.UpgradeClusterSet{
			SelectedClusters: []int64{110, 111},
			ServiceId:        1,
			Idc:              tce_config.IDC_CN,
		}
		err := ClusterTypeValidate(ctx, clusterSet)
		t.NoError(err)
	})
	mockey.PatchConvey("no err", &testing.T{}, func() {
		defer mockey.Mock(tce.GetClusterInfoListByClusterIDListAndServiceID).Return([]*tceopensdk.ClusterInfo{
			commonCluster1, commonCluster3,
		}, nil).Build().UnPatch()
		clusterSet := &change_itempb.UpgradeClusterSet{
			SelectedClusters: []int64{112, 113},
			ServiceId:        1,
			Idc:              tce_config.IDC_CN,
		}
		err := ClusterTypeValidate(ctx, clusterSet)
		t.NoError(err)
	})
	mockey.PatchConvey("empty cluster err", &testing.T{}, func() {
		defer mockey.Mock(tce.GetClusterInfoListByClusterIDListAndServiceID).Return([]*tceopensdk.ClusterInfo{
			independentCluster1, independentCluster2, commonCluster1, commonCluster3,
		}, nil).Build().UnPatch()
		clusterSet := &change_itempb.UpgradeClusterSet{
			SelectedClusters: []int64{},
			ServiceId:        1,
			Idc:              tce_config.IDC_CN,
		}
		err := ClusterTypeValidate(ctx, clusterSet)
		t.Error(err)
	})
	mockey.PatchConvey("euttp cluster", &testing.T{}, func() {
		t.T().Skip()
		defer mockey.Mock(crossborder.NewCrossborderSDK().EUGetAllClusterListByServiceID).Return([]*tceopensdk.ClusterInfo{
			independentCluster1, independentCluster2, commonCluster1, commonCluster3,
		}, nil).Build().UnPatch()
		clusterSet := &change_itempb.UpgradeClusterSet{
			SelectedClusters: []int64{110, 111, 112, 113},
			ServiceId:        1,
			Idc:              tce_config.IDC_EU_TTP,
		}
		err := ClusterTypeValidate(ctx, clusterSet)
		t.NoError(err)
	})

}

func (t *ChangeItemTestSuite) TestGetSCMMainArtifact() {
	item := change_itempb.ChangeItemContent{
		ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
		DeployTarget: &change_itempb.ChangeItemDeployTarget{
			ProjectUniqueId: "ies.cdaas.testdemo7",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "ies.cdaas.testdemo7",
		},
		Strategy: &change_itempb.ChangeItemDeployStrategy{
			TceBaseScmArtifacts: []*change_itempb.SCMArtifact{
				{
					PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
					ScmId:   631,
					ScmName: "toutiao/runtime",
					Version: "1.0.1.1122",
				},
				{
					PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
					ScmId:   667,
					ScmName: "toutiao/load",
					Version: "1.0.2.499",
				},
			},
		},
		Artifact: &change_itempb.ChangeItemArtifact{
			Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
			ScmArtifacts: []*change_itempb.SCMArtifact{
				{
					IsMain:           true,
					PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
					ScmId:            50629,
					ScmName:          "ies/cdaas/testdemo7",
					Revision:         "feature/feature-4306316",
					TargetCommitHash: "fce837ad",
					GitRepoName:      "iesarch/ies.cdaas.testdemo7",
				},
				{
					PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
					ScmId:   631,
					ScmName: "toutiao/runtime",
					Version: "1.0.1.548",
				},
			},
		},
	}

	mainArtifact := &change_itempb.SCMArtifact{
		IsMain:           true,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
		ScmId:            50629,
		ScmName:          "ies/cdaas/testdemo7",
		Revision:         "feature/feature-4306316",
		TargetCommitHash: "fce837ad",
		GitRepoName:      "iesarch/ies.cdaas.testdemo7",
	}
	artifact, found := GetSCMMainArtifact(item.GetArtifact().GetScmArtifacts())
	t.Equal(found, true)
	t.Equal(artifact, mainArtifact)
}

func (t *ChangeItemTestSuite) Test_PubBaseIsGitRelated() {
	artifact := &change_itempb.SCMArtifact{}
	t.Equal(false, PubBaseIsGitRelated(artifact.GetPubBase()))

	artifact = &change_itempb.SCMArtifact{
		PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
	}
	t.Equal(true, PubBaseIsGitRelated(artifact.GetPubBase()))

	artifact = &change_itempb.SCMArtifact{
		PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
	}
	t.Equal(false, PubBaseIsGitRelated(artifact.GetPubBase()))

	artifact = &change_itempb.SCMArtifact{
		PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_COMMIT,
	}
	t.Equal(true, PubBaseIsGitRelated(artifact.GetPubBase()))
}

func (t *ChangeItemTestSuite) TestGetChangeItemDefaultBranch() {
	artifacts := &change_itempb.ChangeItemArtifact{
		Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
		ScmArtifacts: []*change_itempb.SCMArtifact{
			{
				IsMain:           true,
				PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
				ScmId:            50629,
				ScmName:          "aaa",
				Revision:         "bbb",
				TargetCommitHash: "xxx",
				GitRepoName:      "aaa",
			},
			{
				PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
				ScmId:   631,
				ScmName: "toutiao/runtime",
				Version: "1.0.1.548",
			},
		},
	}

	p := monkey.Patch(release_ticket_helper.GetDefaultBranchMapByRt, func(ctx context.Context, rtId uint64, gitRepoList []redisrepository.ProjectGitItem) (map[string]string, error) {
		return map[string]string{
			"aaa": "test",
		}, nil
	})
	defer p.UnPatch()

	defaultBranch := GetChangeItemDefaultBranch(context.Background(), 0, artifacts.GetScmArtifacts())
	t.Equal(defaultBranch, "test")
}
