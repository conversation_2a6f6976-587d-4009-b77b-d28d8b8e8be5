package change_item

import (
	"context"
	"fmt"
	"reflect"

	"github.com/bytedance/mockey"
	json "github.com/bytedance/sonic"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/zeebo/errs"
	"golang.org/x/exp/slices"

	"code.byted.org/bits/monkey"
	scm_provider "code.byted.org/canal/provider/scm"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	mockrepository "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	redisrepository "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis/repository"
	appcenterapi "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/appcenter"
	integrate_sdk_mock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/resourceapi"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/scm"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/cd_gray"
	release_ticket_helper "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket/helper"
	"code.byted.org/devinfra/hagrid/app/cdrpc/config"
	change_item_ "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bits/cd/change_item"
	"code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/config_service"
	git_server_gen "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/git_server"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	commonUtils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/appcenter"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bits/integration/multi"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bytedance/bits/dev"
	"code.byted.org/overpass/bytedance_bits_git_server/kitex_gen/bytedance/bits/git_server"
)

func (t *ChangeItemTestSuite) Test_GetDevTaskRevision() {
	artifacts := []*change_itempb.SCMArtifact{
		{
			IsMain:   true,
			PubBase:  sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
			Revision: "master",
		},
		{
			PubBase:  sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
			Revision: "master",
			Version:  "1.0.0.1",
		},
	}

	revision := GetDevTaskRevision(artifacts[0])
	t.Equal("master", revision)
	revision = GetDevTaskRevision(artifacts[1])
	t.Equal("1.0.0.1", revision)
}

func (t *ChangeItemTestSuite) TestChangeItemDeployTargetMarshal() {
	svc := NewChangeItemSvc()
	item1 := &release_ticketpb.ReleaseTicketChangeItem{
		DeployTarget: &change_itempb.ChangeItemDeployTarget{
			ProjectUniqueId: "ies.cdaas.testdemo7",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
			ServiceMetaWeb: &paaspb.ServiceMetaWeb{
				AppId: "1",
			},
		},
	}

	dbItem, err := svc.ChangeItemConvertToDB(t.ctx, item1, 1)
	t.NoError(err)
	t.Contains(dbItem.DeployTarget, "project_unique_id")
	t.Contains(dbItem.DeployTarget, `"project_type":4`)
	t.Contains(dbItem.DeployTarget, "service_meta_web")
}

func (t *ChangeItemTestSuite) TestChangeItemDeployTargetUnMarshal() {
	svc := NewChangeItemSvc()
	item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)

	pbItem, err := svc.ChangeItemConvertToPB(t.ctx, item1)
	t.NoError(err)
	t.Equal("toutiao.canal.testzhangjh", pbItem.DependencyItems[0].ProjectUniqueId)
	t.Equal(change_itempb.ArtifactType_ARTIFACT_TYPE_SCM, pbItem.DeployResource.Type)
	t.Equal("ies.cdaas.testdemo7", pbItem.DeployTarget.ProjectUniqueId)
	t.Equal(change_itempb.ControlledBy_CONTROLLED_BY_RELEASE_TICKET, pbItem.ControlledBy)
	t.Equal(pbItem.GetDeployTarget().GetServiceMetaWeb().GetAppId(), "1")
	t.NotNil(pbItem.DeployStrategy.ClusterInfo)

	targetStr := `{"project_name": "ies.cdaas.testdemo7", "project_type": 4, "project_unique_id": "ies.cdaas.testdemo7"}`
	p, err := UnMarshalDeployTarget(targetStr)
	t.NoError(err)
	t.Nil(p.GetServiceMetaWeb())

}

func (t *ChangeItemTestSuite) TestChangeItemConvertToDB() {
	svc := NewChangeItemSvc()
	item1 := testfactory.NewMockPBReleaseTicketChangeItem()

	dbItem, err := svc.ChangeItemConvertToDB(t.ctx, item1, 1)
	t.NoError(err)
	t.Equal(dbItem.ControlPlane, sharedpb.ControlPlane_CONTROL_PLANE_CN.String())
	t.Equal(dbItem.ProjectType, sharedpb.ProjectType_PROJECT_TYPE_TCE.String())
	t.Equal(dbItem.ProjectType, "PROJECT_TYPE_TCE")
	t.Equal(dbItem.ProjectOwners, `["liujia.ldspirit"]`)
	t.Equal(dbItem.Reviewers, `["liujia.ldspirit"]`)
}

func (t *ChangeItemTestSuite) TestChangeItemConvertToPB() {
	svc := NewChangeItemSvc()
	item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)

	pbItem, err := svc.ChangeItemConvertToPB(t.ctx, item1)
	t.NoError(err)
	t.Equal("toutiao.canal.testzhangjh", pbItem.DependencyItems[0].ProjectUniqueId)
	t.Equal(change_itempb.ArtifactType_ARTIFACT_TYPE_SCM, pbItem.DeployResource.Type)
	t.Equal("ies.cdaas.testdemo7", pbItem.DeployTarget.ProjectUniqueId)
	t.Equal(change_itempb.ControlledBy_CONTROLLED_BY_RELEASE_TICKET, pbItem.ControlledBy)
	t.NotNil(pbItem.DeployStrategy.ClusterInfo)
}

func (t *ChangeItemTestSuite) TestDependencyConvertToPB() {
	item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)
	pbItems, err := DependencyConvertToPB(t.ctx, []*entity.DBReleaseTicketChangeItem{item1})
	t.NoError(err)
	t.Len(pbItems, 1)
	pbItem := pbItems[0]
	t.Equal("toutiao.canal.testzhangjh", pbItem.DependencyItems[0].ProjectUniqueId)
}

func (t *ChangeItemTestSuite) TestDependencyHagridToOnesite() {
	items := testfactory.NewMockWorkspaceDependency()
	deps := DependencyHagridToOnesite(items)
	t.Len(deps, 2)
	t.Equal(deps[0].ProjectUniqueId, "ies.cdaas.fake1")
	t.Equal(deps[0].ProjectType, config_service.OnesiteProjectType_TCE)
}

func (t *ChangeItemTestSuite) TestDependencyOnesiteToHagrid() {
	items := testfactory.NewMockDependencyFromWorkspace()
	deps := DependencyOnesiteToHagrid(items)
	t.Len(deps, 2)
	t.Equal(deps[0].ProjectUniqueId, "ies.cdaas.fake1")
	t.Equal(deps[0].ProjectType, sharedpb.ProjectType_PROJECT_TYPE_TCE)
}

func (t *ChangeItemTestSuite) TestGetNeedSaveDependencies() {
	item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)
	dependencies := testfactory.NewMockEditDependency()
	t.Run("hard 1", func() {
		saveList, err := GetNeedSaveDependencies(dependencies, []*entity.DBReleaseTicketChangeItem{item1})
		t.NoError(err)
		t.Equal(len(saveList), 1)
	})
	t.Run("hard 2", func() {
		dependencies = testfactory.NewMockInvalidEditDependency()
		saveList, err := GetNeedSaveDependencies(dependencies, []*entity.DBReleaseTicketChangeItem{item1})
		t.NoError(err)
		t.Equal(len(saveList), 0)
	})

	t.Run("A->B 改成 B->A", func() {
		ditems, _ := MarshalToDependencyItems([]*change_itempb.DependencyItem{
			{
				ProjectUniqueId: "toutiao.canal.fake1",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
				ProjectName:     "toutiao.canal.fake1",
			},
		})
		origin := []*entity.DBReleaseTicketChangeItem{
			{
				ID:              1,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ProjectUniqueID: "toutiao.canal.fake1",
			},
			{
				ID:              2,
				ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ProjectUniqueID: "toutiao.canal.fake2",
				DependencyItems: string(ditems),
			},
			{
				ID:              3,
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE.String(),
				ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
				ProjectUniqueID: "toutiao.canal.fake2",
				DependencyItems: string(ditems),
			},
		}
		target := []*change_itempb.Dependency{
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ProjectUniqueId: "toutiao.canal.fake1",
				DependencyItems: []*change_itempb.DependencyItem{
					{
						ProjectUniqueId: "toutiao.canal.fake2",
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
						ProjectName:     "toutiao.canal.fake2",
					},
				},
			},
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ProjectUniqueId: "toutiao.canal.fake2",
			},
		}

		saveList, err := GetNeedSaveDependencies(target, origin)
		t.NoError(err)
		t.Equal(len(saveList), 3)
	})
}

func (t *ChangeItemTestSuite) TestGetReleaseTicketChangeItems() {
	ctl := gomock.NewController(t.T())
	defer ctl.Finish()

	item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)

	mockDao := mockrepository.NewMockReleaseTicketChangeItemDao(ctl)
	mockDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketChangeItem{
		item1,
	}, nil)

	svc := &changeItemSvc{releaseTicketChangeItemDao: mockDao,
		cdGray: cd_gray.NewCdGraySvc(),
	}

	items, err := svc.GetReleaseTicketChangeItems(t.ctx, item1.ReleaseTicketID)
	t.NoError(err)
	t.Len(items, 1)
	t.Equal("toutiao.canal.testzhangjh", items[0].DependencyItems[0].ProjectUniqueId)
	t.Equal(change_itempb.ArtifactType_ARTIFACT_TYPE_SCM, items[0].DeployResource.Type)
	t.Equal("ies.cdaas.testdemo7", items[0].DeployTarget.ProjectUniqueId)
}

func (t *ChangeItemTestSuite) TestGetReleaseTicketChangeItemsByControlPlane() {
	ctl := gomock.NewController(t.T())
	defer ctl.Finish()

	item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)

	mockDao := mockrepository.NewMockReleaseTicketChangeItemDao(ctl)
	mockDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketChangeItem{
		item1,
		item1,
	}, nil).AnyTimes()
	mockDao.EXPECT().GetByReleaseTicketIDAndControlPlane(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketChangeItem{
		item1,
	}, nil).AnyTimes()

	svc := &changeItemSvc{releaseTicketChangeItemDao: mockDao}

	t.Run("no control plane", func() {
		items, err := svc.GetReleaseTicketChangeItemsByControlPlane(t.ctx, item1.ReleaseTicketID, sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED)
		t.NoError(err)
		t.Len(items, 2)
		t.Equal("toutiao.canal.testzhangjh", items[0].DependencyItems[0].ProjectUniqueId)
		t.Equal(change_itempb.ArtifactType_ARTIFACT_TYPE_SCM, items[0].DeployResource.Type)
		t.Equal("ies.cdaas.testdemo7", items[0].DeployTarget.ProjectUniqueId)
	})

	t.Run("control plane", func() {
		items, err := svc.GetReleaseTicketChangeItemsByControlPlane(t.ctx, item1.ReleaseTicketID, sharedpb.ControlPlane_CONTROL_PLANE_CN)
		t.NoError(err)
		t.Len(items, 1)
	})
}

func (t *ChangeItemTestSuite) TestSaveReleaseTicketChangeItems() {
	svc := NewChangeItemSvc()

	_, err := svc.SaveReleaseTicketChangeItems(t.ctx, nil)
	t.NoError(err)
}

func (t *ChangeItemTestSuite) TestReplaceChangeItemsToWorkBranch() {
	svc := NewChangeItemSvc()

	scmArtifacts := make([]*change_itempb.SCMArtifact, 0)
	scmArtifacts = append(scmArtifacts, &change_itempb.SCMArtifact{
		IsMain:           true,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
		ScmId:            226033,
		ScmName:          "iesarch/demo/wangjian525_demod_hertz",
		Version:          "",
		Revision:         "master",
		TargetCommitHash: "e5e23fd5301f118951c5adf66d3281a7b977c43a",
		GitRepoName:      "wangjian.525/demod_hertz",
	}, &change_itempb.SCMArtifact{
		IsMain:           false,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
		ScmId:            667,
		ScmName:          "toutiao/load",
		Version:          "1.0.2.499",
		Revision:         "",
		TargetCommitHash: "",
		GitRepoName:      "",
	}, &change_itempb.SCMArtifact{
		IsMain:           false,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
		ScmId:            631,
		ScmName:          "toutiao/runtime",
		Version:          "1.0.1.527",
		Revision:         "",
		TargetCommitHash: "",
		GitRepoName:      "",
	})

	scmArtifacts2 := make([]*change_itempb.SCMArtifact, 0)
	scmArtifacts2 = append(scmArtifacts2, &change_itempb.SCMArtifact{
		IsMain:           true,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
		ScmId:            226138,
		ScmName:          "iesarch/demo/wangjian525_demod_kitex",
		Version:          "",
		Revision:         "master",
		TargetCommitHash: "8eb5dd568dee3dfa5a6d20fea91e053053eee5e7",
		GitRepoName:      "wangjian.525/demod_kitex",
	}, &change_itempb.SCMArtifact{
		IsMain:           false,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
		ScmId:            667,
		ScmName:          "toutiao/load",
		Version:          "1.0.2.499",
		Revision:         "",
		TargetCommitHash: "",
		GitRepoName:      "",
	}, &change_itempb.SCMArtifact{
		IsMain:           false,
		PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
		ScmId:            631,
		ScmName:          "toutiao/runtime",
		Version:          "1.0.1.527",
		Revision:         "",
		TargetCommitHash: "",
		GitRepoName:      "",
	})

	changeItems := make([]*release_ticketpb.ReleaseTicketChangeItem, 0)
	changeItems = append(changeItems, &release_ticketpb.ReleaseTicketChangeItem{
		ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
		DeployTarget: &change_itempb.ChangeItemDeployTarget{
			ProjectUniqueId: "iesarch.demo.wangjian525_demod_hertz",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "iesarch.demo.wangjian525_demod_hertz",
		},
		DeployResource: &change_itempb.ChangeItemArtifact{
			Type:         change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
			ScmArtifacts: scmArtifacts,
		},
	}, &release_ticketpb.ReleaseTicketChangeItem{
		DeployTarget: &change_itempb.ChangeItemDeployTarget{
			ProjectUniqueId: "iesarch.demo.wangjian525_demod_kitex",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "iesarch.demo.wangjian525_demod_kitex",
		},
		DeployResource: &change_itempb.ChangeItemArtifact{
			Type:         change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
			ScmArtifacts: scmArtifacts2,
		},
		ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
	})

	nodeConfig := &workflowpb.NodeConfig{
		NodeConfig: &workflowpb.NodeConfig_DeployNodeConfig{
			DeployNodeConfig: &workflowpb.DeployNodeConfig{
				WorkBranch: &branching_model_configpb.BranchNaming{
					Name: "BRANCH_NAMING_TYPE_GIT_DEFAULT",
				},
			},
		},
	}

	p := monkey.Patch(release_ticket_helper.GetDefaultBranchMapByRt, func(ctx context.Context, rtId uint64, gitRepoList []redisrepository.ProjectGitItem) (map[string]string, error) {
		return map[string]string{
			"wangjian.525/demod_hertz": "master",
			"wangjian.525/demod_kitex": "main",
		}, nil
	})
	defer p.Unpatch()
	err := svc.ReplaceChangeItemsToWorkBranch(0, changeItems, nodeConfig, workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE)
	t.NoError(err)

	for _, sa := range changeItems[0].GetDeployResource().GetScmArtifacts() {
		if sa.IsMain {
			t.Equal("master", sa.Revision)
		}
	}
}

func (t *ChangeItemTestSuite) TestGetReleaseTicketChangeItemProjects() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	c := NewChangeItemSvc()

	patch := monkey.PatchInstanceMethod(reflect.TypeOf(c), "GetReleaseTicketChangeItems", func(ctx context.Context, releaseTicketID uint64) ([]*release_ticketpb.ReleaseTicketChangeItem, error) {
		return []*release_ticketpb.ReleaseTicketChangeItem{
			{
				ProjectUniqueId: "iesarch.demo.wangjian525_demod_hertz",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
				DeployResource: &change_itempb.ChangeItemArtifact{
					// 非 SCM
					Type: change_itempb.ArtifactType_ARTIFACT_TYPE_UNSPECIFIED,
				},
			},
			{
				ProjectUniqueId: "iesarch.demo.wangjian525_demod_hertz",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
				DeployResource: &change_itempb.ChangeItemArtifact{
					Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
					ScmArtifacts: []*change_itempb.SCMArtifact{
						// 无主仓
						{
							IsMain: false,
						},
						{
							IsMain: false,
						},
					},
				},
			},
			{
				ProjectUniqueId: "xxx",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
				DeployResource: &change_itempb.ChangeItemArtifact{
					Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
					ScmArtifacts: []*change_itempb.SCMArtifact{
						{
							IsMain:      true,
							GitRepoName: "wangjian.525/demod_hertz",
							GitRepoId:   226033,
						},
					},
				},
			},
			{
				ProjectUniqueId: "xxx",
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_I18N,
				DeployResource: &change_itempb.ChangeItemArtifact{
					Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
					ScmArtifacts: []*change_itempb.SCMArtifact{
						{
							IsMain:      true,
							GitRepoName: "xxx",
							GitRepoId:   631,
						},
					},
				},
			},
		}, nil
	})
	defer patch.Unpatch()

	projects, err := c.GetReleaseTicketChangeItemProjects(t.ctx, 1)
	t.NoError(err)
	t.Equal(2, len(projects))
	t.T().Log(projects)
}

func (t *ChangeItemTestSuite) Test_GetCodebaseCommitRaw() {
	scm.CurrentIdcScmSvc = testfactory.NewMockScmService()

	config.Conf = &config.Config{
		OnesiteApi: &resourceapi.Config{
			Host: "https://bits.bytedance.net/",
		},
	}
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	mockGitServerSvc := integrate_sdk_mock.NewMockGitServerSvc(ctrl)
	s := &changeItemSvc{
		gitSvc: mockGitServerSvc,
	}
	mockGitServerSvc.EXPECT().GetProjectByRepoInfo(gomock.Any(), gomock.Any()).Return(&git_server.GetProjectByRepoInfoResponse{
		Project: &git_server.Project{
			Id: 1,
		},
	}, nil).AnyTimes()
	mockGitServerSvc.EXPECT().GetBranchV2(gomock.Any(), gomock.Any()).Return(&git_server_gen.GetBranchV2Response{
		Branch: &git_server_gen.GitBranch{
			Commit: &git_server_gen.GitCommit{
				Id:      "123",
				Message: "",
				Committer: &git_server_gen.GitUser{
					Name:  "name",
					Email: "email",
				},
				Author: &git_server_gen.GitUser{
					Name:  "name",
					Email: "email",
				},
			},
			Name: "test",
		}}, nil).AnyTimes()
	mockGitServerSvc.EXPECT().GetCommit(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Commit{
		Id:             "123",
		ShortId:        "123",
		Title:          "test message",
		AuthorName:     "name",
		AuthorEmail:    "email",
		CommitterName:  "name",
		CommitterEmail: "enail",
	}, nil).AnyTimes()
	t.Run("commit raw yes no repo id", func() {
		c, err := s.GetCodebaseCommitRaw(t.ctx, &change_itempb.SCMArtifact{
			PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
			Revision:    "a",
			GitRepoName: "a/b",
			ScmId:       10,
			ScmName:     "b",
		})

		t.NoError(err)
		t.Equal("123", c.Branch.Commit.Sha)
		t.Equal("test", c.Branch.Name)
	})

	t.Run("commit raw yes", func() {
		patch := monkey.PatchInstanceMethod(reflect.TypeOf(scm.CurrentIdcScmSvc), "GetRepoVersionInfo", func(ctx context.Context, repoID int64, version string) (*scm_provider.RepoVersion, bool, error) {
			return &scm_provider.RepoVersion{
				BranchName: "hahah",
			}, true, nil
		})
		defer patch.UnPatch()

		c, err := s.GetCodebaseCommitRaw(t.ctx, &change_itempb.SCMArtifact{
			PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
			Revision:    "a",
			GitRepoName: "a/b",
			GitRepoId:   1,
			ScmId:       10,
			ScmName:     "b",
		})

		t.NoError(err)
		t.Equal("123", c.Branch.Commit.Sha)
		t.Equal("test", c.Branch.Name)
	})

	t.Run("commit raw no", func() {
		patch := monkey.PatchInstanceMethod(reflect.TypeOf(scm.CurrentIdcScmSvc), "GetRepoVersionInfo", func(ctx context.Context, repoID int64, version string) (*scm_provider.RepoVersion, bool, error) {
			return nil, false, errors.New("err")
		})
		defer patch.UnPatch()

		_, err := s.GetCodebaseCommitRaw(t.ctx, &change_itempb.SCMArtifact{
			PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
			Revision:    "a",
			GitRepoName: "a/b",
			GitRepoId:   1,
			ScmId:       10,
			ScmName:     "b",
		})

		t.Error(err)
	})

	t.Run("commit raw yes but no record", func() {
		patch := monkey.PatchInstanceMethod(reflect.TypeOf(scm.CurrentIdcScmSvc), "GetRepoVersionInfo", func(ctx context.Context, repoID int64, version string) (*scm_provider.RepoVersion, bool, error) {
			return &scm_provider.RepoVersion{
				BranchName: "hahah",
			}, true, nil
		})
		defer patch.UnPatch()

		mockGitServerSvc.EXPECT().GetCommit(gomock.Any(), gomock.Any(), gomock.Any()).Return(&git_server.Commit{
			Id:             "123",
			ShortId:        "123",
			Title:          "test message",
			AuthorName:     "name",
			AuthorEmail:    "email",
			CommitterName:  "name",
			CommitterEmail: "enail",
		}, errs.New("")).AnyTimes()

		c, err := s.GetCodebaseCommitRaw(t.ctx, &change_itempb.SCMArtifact{
			PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
			Revision:    "a",
			GitRepoName: "a/b",
			GitRepoId:   1,
			ScmId:       10,
			ScmName:     "b",
		})

		t.NoError(err)
		t.Equal("123", c.Branch.Commit.Sha)
	})
}

func (t *ChangeItemTestSuite) TestGetProjectName() {
	ctl := gomock.NewController(t.T())
	defer ctl.Finish()
	t.Run("IncludeDeleteProject false", func() {
		item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)

		mockDao := mockrepository.NewMockReleaseTicketChangeItemDao(ctl)
		mockDao.EXPECT().GetSingleChangeItem(gomock.Any(), gomock.Any()).Return(item1, nil)

		mockStageDao := mockrepository.NewMockStageDao(ctl)
		mockStageDao.EXPECT().GetStageByStageID(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			ReleaseTicketID: 123,
		}, nil)

		svc := &changeItemSvc{
			releaseTicketChangeItemDao: mockDao,
			stageDao:                   mockStageDao,
		}

		name := svc.GetProjectName(t.ctx, ProjectNameQuery{
			ProjectUniqueId: "xxx",
		})
		t.Equal("ies.cdaas.testdemo7", name)
	})

	t.Run("IncludeDeleteProject true", func() {
		item1 := testfactory.NewMockDBReleaseTicketChangeItem(1)
		item2 := testfactory.NewMockDeletedDBReleaseTicketChangeItem(1)

		mockDao := mockrepository.NewMockReleaseTicketChangeItemDao(ctl)
		mockDao.EXPECT().GetByReleaseTicketIDWithUnscoped(gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketChangeItem{item1, item2}, nil)

		mockStageDao := mockrepository.NewMockStageDao(ctl)
		mockStageDao.EXPECT().GetStageByStageID(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			ReleaseTicketID: 1,
		}, nil)

		svc := &changeItemSvc{
			releaseTicketChangeItemDao: mockDao,
			stageDao:                   mockStageDao,
		}

		name := svc.GetProjectName(t.ctx, ProjectNameQuery{
			ProjectUniqueId:       "7",
			ControlPlane:          sharedpb.ControlPlane_CONTROL_PLANE_CN,
			ProjectType:           sharedpb.ProjectType_PROJECT_TYPE_WEB,
			IncludeDeletedProject: true,
		})
		t.Equal("ies.cdaas.testdemo7", name)
	})
}

func (t *ChangeItemTestSuite) TestGetTtpProjectName() {
	svc := &changeItemSvc{}
	t.Run("not ttp", func() {
		ttpProjName := svc.GetTtpProjectName(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		})
		t.Equal("", ttpProjName)
	})

	t.Run("invalid deployTarget", func() {
		ttpProjName := svc.GetTtpProjectName(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_TTP.String(),
			DeployTarget: "invalid content",
		})
		t.Equal("", ttpProjName)
	})

	t.Run("web project name", func() {
		ttpProjName := svc.GetTtpProjectName(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_TTP.String(),
			DeployTarget: "{\"project_name\": \"bc测试web项目_RoW\", \"project_type\": 4, \"ttp_web_name\": \"bc测试web项目_TTP\", \"project_unique_id\": \"64758\"}",
		})
		t.Equal("bc测试web项目_TTP", ttpProjName)
	})
}

func (t *ChangeItemTestSuite) TestGetTtpProjectType() {
	svc := &changeItemSvc{}
	t.Run("not ttp", func() {
		ttpProjType := svc.GetTtpProjectType(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		})
		t.Equal("", ttpProjType)
	})

	t.Run("invalid deployTarget", func() {
		ttpProjType := svc.GetTtpProjectType(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_TTP.String(),
			DeployTarget: "invalid content",
		})
		t.Equal("", ttpProjType)
	})

	t.Run("web project name", func() {
		ttpProjType := svc.GetTtpProjectType(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_TTP.String(),
			DeployTarget: "{\"project_name\": \"bc测试web项目_RoW\", \"project_type\": 4, \"ttp_web_name\": \"bc测试web项目_TTP\", \"project_unique_id\": \"64758\"}",
		})
		t.Equal("web", ttpProjType)
	})

	t.Run("unknown project type", func() {
		ttpProjType := svc.GetTtpProjectType(t.ctx, &entity.DBReleaseTicketChangeItem{
			ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_TTP.String(),
			DeployTarget: "{\"project_name\": \"bc测试web项目_RoW\", \"project_type\": 404, \"ttp_web_name\": \"bc测试web项目_TTP\", \"project_unique_id\": \"64758\"}",
		})
		t.Equal("", ttpProjType)
	})
}

func (t *ChangeItemTestSuite) TestGetChangeItemGitRepo() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	changeItemDao := mockrepository.NewMockReleaseTicketChangeItemDao(ctrl)
	gitServiceSvc := integrate_sdk_mock.NewMockGitServerSvc(ctrl)

	t.Run("git repo id", func() {
		changeItemDao.EXPECT().GetSingleChangeItem(gomock.Any(), gomock.Any()).Return(testfactory.NewMockDBReleaseTicketChangeItem(1), nil)
		gitServiceSvc.EXPECT().GetProjectByRepoName(gomock.Any(), gomock.Any()).Return(&git_server.Project{Id: 1}, nil)

		svc := &changeItemSvc{
			releaseTicketChangeItemDao: changeItemDao,
			gitSvc:                     gitServiceSvc,
		}
		repo, err := svc.GetChangeItemGitRepo(t.ctx, 1, &release_ticketpb.ReleaseTicketChangeItem{})
		t.NoError(err)
		t.Equal(repo.GetId(), int32(1))
	})

}

func (t *ChangeItemTestSuite) TestGetDeployFinalArtifact() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	item := change_itempb.ChangeItemContent{
		ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
		DeployTarget: &change_itempb.ChangeItemDeployTarget{
			ProjectUniqueId: "ies.cdaas.testdemo7",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "ies.cdaas.testdemo7",
		},
		Strategy: &change_itempb.ChangeItemDeployStrategy{
			TceBaseScmArtifacts: []*change_itempb.SCMArtifact{
				{
					PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
					Revision:    "feature/feature-4306316",
					ScmId:       631,
					ScmName:     "toutiao/runtime",
					GitRepoName: "toutiao/runtime",
					Version:     "1.0.1.548",
				},
				{
					PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
					ScmId:   667,
					ScmName: "toutiao/load",
					Version: "1.0.2.499",
				},
			},
		},
		Artifact: &change_itempb.ChangeItemArtifact{
			Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
			ScmArtifacts: []*change_itempb.SCMArtifact{
				{
					IsMain:           true,
					PubBase:          sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
					ScmId:            50629,
					ScmName:          "ies/cdaas/testdemo7",
					Revision:         "feature/feature-4306316",
					TargetCommitHash: "fce837ad",
					GitRepoName:      "iesarch/ies.cdaas.testdemo7",
				},
				{
					PubBase:     sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
					Revision:    "feature/feature-4306316",
					ScmId:       631,
					ScmName:     "toutiao/runtime",
					GitRepoName: "toutiao/runtime",
					Version:     "1.0.1.548",
				},
				{
					PubBase: sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
					ScmId:   667,
					ScmName: "toutiao/load",
					Version: "1.0.2.502",
				},
			},
		},
	}

	nodeConfig := &workflowpb.NodeConfig{
		NodeConfig: &workflowpb.NodeConfig_DeployNodeConfig{
			DeployNodeConfig: &workflowpb.DeployNodeConfig{
				WorkBranch: &branching_model_configpb.BranchNaming{
					Name: branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_GIT_DEFAULT.String(),
				},
			},
		},
	}

	p := monkey.Patch(release_ticket_helper.GetDefaultBranchMapByRt, func(ctx context.Context, rtId uint64, gitRepoList []redisrepository.ProjectGitItem) (map[string]string, error) {
		return map[string]string{
			"iesarch/ies.cdaas.testdemo7": "test",
			"toutiao/runtime":             "test",
		}, nil
	})
	defer p.UnPatch()

	svc := &changeItemSvc{}
	monkey.PatchInstanceMethod(reflect.TypeOf(svc), "GetCodebaseCommitRaw").Return(nil, nil)
	monkey.PatchInstanceMethod(reflect.TypeOf(svc), "GetRepoVersionInfo").Return(nil, nil)
	changeItem := &release_ticketpb.ReleaseTicketChangeItem{
		BranchingModelConfig: &branching_model_configpb.BranchingModelConfig{
			IntegrationBranch: &branching_model_configpb.BranchNaming{
				Name: "a",
			},
		},
	}

	artifact, err := svc.GetDeployFinalArtifact(context.Background(), 0, changeItem, &item, nodeConfig, workflowpb.NodeType_NODE_TYPE_DEPLOY_STAGE)
	t.NoError(err)
	t.Len(gslice.Filter(artifact.ScmArtifacts, func(item *change_itempb.SCMArtifact) bool {
		return item.GetRevision() == "test"
	}), 2)
	t.Len(artifact.ScmArtifacts, 3)
}

func (t *ChangeItemTestSuite) TestGetReleaseTicketCustomizeProjectsBranch() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()

	releaseTicketChangeItemDao := mockrepository.NewMockReleaseTicketChangeItemDao(ctrl)
	releaseTicketChangeItemDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return([]*entity.DBReleaseTicketChangeItem{{}}, nil).AnyTimes()

	svc := &changeItemSvc{releaseTicketChangeItemDao: releaseTicketChangeItemDao}
	defer monkey.PatchInstanceMethod(reflect.TypeOf(svc), "ChangeItemConvertToPB", func(ctx context.Context, dbChangeItem *entity.DBReleaseTicketChangeItem) (*release_ticketpb.ReleaseTicketChangeItem, error) {
		return &release_ticketpb.ReleaseTicketChangeItem{
			ProjectUniqueId: "1",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectName:     "a",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
			DeployResource: &change_itempb.ChangeItemArtifact{
				Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
				ScmArtifacts: []*change_itempb.SCMArtifact{
					{
						IsMain:    true,
						GitRepoId: 1,
						PubBase:   sharedpb.ScmPubBase_SCM_PUB_BASE_COMMIT,
					},
				},
			},
			BranchingModelConfig: &branching_model_configpb.BranchingModelConfig{
				ReleaseBranch: &branching_model_configpb.BranchNaming{Name: "a"},
			},
		}, nil
	}).UnPatch()
	defer mockey.Mock((*changeItemSvc).GetReposInChangeItem).Return([]redisrepository.ProjectGitItem{
		{
			GitName:   "a",
			ProjectID: 1,
		},
	}).Build().UnPatch()

	res, err := svc.GetReleaseTicketCustomizeProjectsBranch(t.ctx, 1)
	t.NoError(err)
	t.Equal(res[1].ReleaseBranch.Name, "a")
}

func (t *ChangeItemTestSuite) Test_GetReposInChangeItem() {
	svc := &changeItemSvc{}

	t.Run("no scm", func() {
		changeItem := &release_ticketpb.ReleaseTicketChangeItem{
			DeployResource: &change_itempb.ChangeItemArtifact{
				Type: change_itempb.ArtifactType_ARTIFACT_TYPE_TCC,
			},
		}

		repos := svc.GetReposInChangeItem(t.ctx, changeItem)
		t.Len(repos, 0)
	})

	t.Run("has scm", func() {
		changeItem := &release_ticketpb.ReleaseTicketChangeItem{
			DeployResource: &change_itempb.ChangeItemArtifact{
				Type: change_itempb.ArtifactType_ARTIFACT_TYPE_SCM,
				ScmArtifacts: []*change_itempb.SCMArtifact{
					{
						PubBase:   sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION,
						GitRepoId: 1,
					},
					{
						PubBase:   sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH,
						GitRepoId: 2,
					},
					{
						PubBase:   sharedpb.ScmPubBase_SCM_PUB_BASE_COMMIT,
						GitRepoId: 3,
					},
				},
			},
		}

		repos := svc.GetReposInChangeItem(t.ctx, changeItem)
		t.Len(repos, 1)
	})
}

func (t *ChangeItemTestSuite) TestGetBaselineChangeItemDependency() {
	ctl := gomock.NewController(t.T())
	defer ctl.Finish()

	svc := &changeItemSvc{}

	i18n, cn := testfactory.NewDependRTChangeItems()

	dbItems := append(cn, i18n...)
	pbItems := gslice.Map(dbItems, func(f *entity.DBReleaseTicketChangeItem) *release_ticketpb.ReleaseTicketChangeItem {
		pbb, _ := svc.ChangeItemConvertToPB(t.ctx, f)
		return pbb
	})
	defer mockey.Mock(mockey.GetMethod(svc, "GetReleaseTicketChangeItems")).Return(pbItems, nil).Build().UnPatch()

	mockey.PatchConvey("GET RELEASE TICKET CHANGE ITEM DEPENDENCY", t.T(), func() {
		dependency, err := svc.GetBaselineChangeItemDependencies(t.ctx, 1)

		slices.SortFunc(dependency, func(a *change_itempb.Dependency, b *change_itempb.Dependency) int {
			if a.ProjectUniqueId > b.ProjectUniqueId {
				return 1
			}
			return -1
		})

		t.NoError(err)
		t.Equal(4, len(dependency))
		t.Equal("toutiao.canal.joker02", dependency[0].ProjectUniqueId)
		t.Equal("toutiao.mf_test.i18n", dependency[0].DependencyItems[0].ProjectUniqueId)

		t.Equal("toutiao.canal.liangzai", dependency[1].ProjectUniqueId)
		t.Equal("toutiao.canal.joker02", dependency[1].DependencyItems[0].ProjectUniqueId)

		t.Equal("toutiao.mf_test.i18n", dependency[2].ProjectUniqueId)
		t.Equal(0, len(dependency[2].DependencyItems))

		t.Equal("toutiao.zhangzeyu.ces1", dependency[3].ProjectUniqueId)
		t.Equal("toutiao.canal.liangzai", dependency[3].DependencyItems[0].ProjectUniqueId)

	})
}

func (t *ChangeItemTestSuite) TestMergeDependencyItems() {
	controlPlanes := []string{
		sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
		sharedpb.ControlPlane_CONTROL_PLANE_I18N.String(),
	}

	depItems1, _ := MarshalToDependencyItems([]*change_itempb.DependencyItem{
		{
			ProjectUniqueId: "B",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
		},
		{
			ProjectUniqueId: "C",
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB,
		},
	})

	// A->B,C B
	rtChangeItems := []*entity.DBReleaseTicketChangeItem{
		{
			ReleaseTicketID: 1,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB.String(),
			ProjectUniqueID: "A",
			DependencyItems: string(depItems1),
			DeployTarget:    "{}",
			DeployStrategy:  "{}",
			DeployResource:  "{}",
			ControlPlane:    "CONTROL_PLANE_CN",
		},
		{
			ReleaseTicketID: 1,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB.String(),
			ProjectUniqueID: "B",
			DependencyItems: "[]",
			DeployTarget:    "{}",
			DeployStrategy:  "{}",
			DeployResource:  "{}",
			ControlPlane:    "CONTROL_PLANE_CN",
		},
		// 计算依赖会忽略它，因为 baselineControlPlane 计算出为 cn
		{
			ReleaseTicketID: 1,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_WEB.String(),
			ProjectUniqueID: "B",
			DependencyItems: "[]",
			DeployTarget:    "{}",
			DeployStrategy:  "{}",
			DeployResource:  "{}",
			ControlPlane:    "CONTROL_PLANE_I18N",
		},
	}

	// A->B,C B
	projectList1 := []*multi.ProjectInfo{
		{
			ProjectType:     dev.ProjectType_PROJECT_TYPE_WEB,
			ProjectUniqueId: "A",
			Artifact: []*multi.Content{
				{
					ContentType:  multi.ContentType_CONTENT_TYPE_CODE_CHANGE,
					ControlPanel: dev.ControlPlane_CONTROL_PLANE_I18N,
					ScmList: []*dev.SCMInfo{
						{
							IsMain:      new(bool),
							Name:        "toutiao/load",
							PubBase:     dev.SCMPubBase_SCM_PUB_BASE_VERSION,
							Revision:    "1.0.2.499",
							GitRepoName: new(string),
							Id:          667,
						},
					},
					DeployDependencies: []*dev.DeployProjectInfo{
						{
							ProjectUniqueId: "B",
							Type:            dev.ProjectType_PROJECT_TYPE_WEB,
						},
						{
							ProjectUniqueId: "C",
							Type:            dev.ProjectType_PROJECT_TYPE_WEB,
						},
					},
				},
			},
			ProjectOwnerList: []string{"yyy"},
		},
		{
			ProjectType:     dev.ProjectType_PROJECT_TYPE_WEB,
			ProjectUniqueId: "B",
			Artifact: []*multi.Content{
				{
					ContentType:  multi.ContentType_CONTENT_TYPE_CODE_CHANGE,
					ControlPanel: dev.ControlPlane_CONTROL_PLANE_I18N,
					ScmList: []*dev.SCMInfo{
						{
							IsMain:      new(bool),
							Name:        "toutiao/load",
							PubBase:     dev.SCMPubBase_SCM_PUB_BASE_VERSION,
							Revision:    "1.0.2.499",
							GitRepoName: new(string),
							Id:          667,
						},
					},
					DeployDependencies: []*dev.DeployProjectInfo{},
				},
			},
			ProjectOwnerList: []string{"yyy"},
		},
	}

	// A->B C->A 此时没有 B 这个项目，但计算时，dependencyItems 里依旧会保留
	projectList2 := []*multi.ProjectInfo{
		{
			ProjectType:     dev.ProjectType_PROJECT_TYPE_WEB,
			ProjectUniqueId: "A",
			Artifact: []*multi.Content{
				{
					ContentType:  multi.ContentType_CONTENT_TYPE_CODE_CHANGE,
					ControlPanel: dev.ControlPlane_CONTROL_PLANE_I18N,
					ScmList: []*dev.SCMInfo{
						{
							IsMain:      new(bool),
							Name:        "toutiao/load",
							PubBase:     dev.SCMPubBase_SCM_PUB_BASE_VERSION,
							Revision:    "1.0.2.499",
							GitRepoName: new(string),
							Id:          667,
						},
					},
					DeployDependencies: []*dev.DeployProjectInfo{
						{
							ProjectUniqueId: "B",
							Type:            dev.ProjectType_PROJECT_TYPE_WEB,
						},
					},
				},
			},
			ProjectOwnerList: []string{"yyy"},
		},
		{
			ProjectType:     dev.ProjectType_PROJECT_TYPE_WEB,
			ProjectUniqueId: "C",
			Artifact: []*multi.Content{
				{
					ContentType:  multi.ContentType_CONTENT_TYPE_CODE_CHANGE,
					ControlPanel: dev.ControlPlane_CONTROL_PLANE_I18N,
					ScmList: []*dev.SCMInfo{
						{
							IsMain:      new(bool),
							Name:        "toutiao/load",
							PubBase:     dev.SCMPubBase_SCM_PUB_BASE_VERSION,
							Revision:    "1.0.2.499",
							GitRepoName: new(string),
							Id:          667,
						},
					},
					DeployDependencies: []*dev.DeployProjectInfo{
						{
							ProjectUniqueId: "A",
							Type:            dev.ProjectType_PROJECT_TYPE_WEB,
						},
					},
				},
			},
			ProjectOwnerList: []string{"yyy"},
		},
	}

	projectsWithBranchModel1 := gslice.Map(projectList1, func(p *multi.ProjectInfo) *ProjectWithBranchModel {
		return &ProjectWithBranchModel{
			Project:                 p,
			ControlPlaneBranchModel: make(map[sharedpb.ControlPlane]*branching_model_configpb.BranchingModelConfig),
		}
	})

	projectsWithBranchModel2 := gslice.Map(projectList2, func(p *multi.ProjectInfo) *ProjectWithBranchModel {
		return &ProjectWithBranchModel{
			Project:                 p,
			ControlPlaneBranchModel: make(map[sharedpb.ControlPlane]*branching_model_configpb.BranchingModelConfig),
		}
	})

	svc := &changeItemSvc{}
	t.Run("projectList1计算结果预期：A->B,C B    无diff", func() {
		d, err := svc.MergeDependencyItems(t.ctx, controlPlanes, nil, rtChangeItems, projectsWithBranchModel1, false)
		t.NoError(err)
		t.True(len(d.FromWorkspaceDiffs) == 0)
	})
	t.Run("// projectList2计算结果预期：A->B,C B C 有diff, 移除了C的依赖因为已经存在A->C", func() {
		d, err := svc.MergeDependencyItems(t.ctx, controlPlanes, nil, rtChangeItems, projectsWithBranchModel2, false)
		t.NoError(err)
		t.True(len(d.FromWorkspaceDiffs) > 0)

		slices.SortFunc(d.Dependencies, func(a *change_itempb.Dependency, b *change_itempb.Dependency) int {
			if a.ProjectUniqueId > b.ProjectUniqueId {
				return 1
			}
			return -1
		})

		t.Equal("A", d.Dependencies[0].ProjectUniqueId)
		t.Equal(2, len(d.Dependencies[0].DependencyItems))
		t.Equal("B", d.Dependencies[0].DependencyItems[0].ProjectUniqueId)

		t.Equal("B", d.Dependencies[1].ProjectUniqueId)

		t.Equal("C", d.Dependencies[2].ProjectUniqueId)
		t.Equal(0, len(d.Dependencies[2].DependencyItems))
	})

}

func (t *ChangeItemTestSuite) TestGetAppCenterProjectInfo() {
	mockey.PatchConvey("mock success", t.T(), func() {
		resStr := "{\"projectType\": 1,\"projectUniqueId\": \"aaa.aaa.aaa\",\"projectName\": \"aaa.aaa.aaa\",\"controlPlaneDetail\": [{\"controlPlane\": 1,\"scmInfo\": [{\"gitRepoId\": \"1\",\"id\": \"1\",\"isMain\": true,\"gitRepoName\": \"aaa\"},{\"gitRepoId\": \"2\",\"id\": \"2\",\"name\": \"toutiao/runtime\",\"gitRepoName\": \"toutiao/runtime\"}]},{\"controlPlane\": 3,\"scmInfo\": [{\"gitRepoId\": \"1\",\"id\": \"1\",\"isMain\": true,\"gitRepoName\": \"aaa\"},{\"gitRepoId\": \"2\",\"id\": \"2\",\"name\": \"toutiao/runtime\",\"gitRepoName\": \"toutiao/runtime\"}]}]}"
		projectInfos := &appcenter.ProjectInfo{}
		_ = json.UnmarshalString(resStr, projectInfos)
		mockey.Mock((*appcenterapi.AppCenterSvc).BatchGetComponentListV2).Return([]*appcenter.ProjectInfo{projectInfos}, nil).Build()

		svc := &changeItemSvc{
			appCenterSvc: appcenterapi.NewAppCenterSvc(),
		}

		res, err := svc.GetAppCenterProjectInfo(t.ctx,
			[]sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED},
			sharedpb.ProjectType_PROJECT_TYPE_TCE, "1")
		t.NoError(err)
		t.Equal(res.ProjectUniqueId, "aaa.aaa.aaa")
	})

	mockey.PatchConvey("get project nil", t.T(), func() {
		mockey.Mock((*appcenterapi.AppCenterSvc).BatchGetComponentListV2).Return(nil, nil).Build()
		svc := &changeItemSvc{
			appCenterSvc: appcenterapi.NewAppCenterSvc(),
		}

		res, err := svc.GetAppCenterProjectInfo(t.ctx,
			[]sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED},
			sharedpb.ProjectType_PROJECT_TYPE_TCE, "1")
		t.NoError(err)
		t.Nil(res)
	})

	mockey.PatchConvey("get project failed", t.T(), func() {
		mockey.Mock((*appcenterapi.AppCenterSvc).BatchGetComponentListV2).Return(nil, errors.New("t")).Build()
		svc := &changeItemSvc{
			appCenterSvc: appcenterapi.NewAppCenterSvc(),
		}

		res, err := svc.GetAppCenterProjectInfo(t.ctx,
			[]sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_UNSPECIFIED},
			sharedpb.ProjectType_PROJECT_TYPE_TCE, "1")
		t.Error(err)
		t.Nil(res)
	})
}

func (t *ChangeItemTestSuite) TestSlimDependencies() {
	deps := []*change_itempb.Dependency{
		{
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectUniqueId: "toutiao.canal.fake1",
			DependencyItems: []*change_itempb.DependencyItem{
				{
					ProjectUniqueId: "toutiao.canal.fake2",
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
					ProjectName:     "toutiao.canal.fake2",
				},
			},
		},
		{
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectUniqueId: "toutiao.canal.fake2",
		},
		{
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
			ProjectUniqueId: "toutiao.canal.fake3",
		},
	}
	t.Run("no scope", func() {
		targetD := SlimDependencies(t.ctx, deps, nil)
		t.Equal(0, len(targetD))
	})

	t.Run("scope limit", func() {
		scopedDeps := []*change_itempb.Dependency{
			{
				ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
				ProjectUniqueId: "toutiao.canal.fake1",
				DependencyItems: []*change_itempb.DependencyItem{
					{
						ProjectUniqueId: "toutiao.canal.fake3",
						ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_FAAS,
						ProjectName:     "toutiao.canal.fake3",
					},
				},
			},
		}
		targetD := SlimDependencies(t.ctx, deps, scopedDeps)
		t.Equal(1, len(targetD))
		t.Equal(0, len(targetD[0].DependencyItems))
	})

}

func (t *ChangeItemTestSuite) Test_MapChangeItems2pb() {
	itemsStr := `[{
				"projectUniqueId": "ies.cdaas.testdemo7",
				"projectType": 1,
				"projectName": "ies.cdaas.testdemo7",
				"controlPlane": 1,
				"deployTarget": "{\"project_type\":1,\"project_unique_id\":\"ies.cdaas.testdemo7\",\"project_name\":\"ies.cdaas.testdemo7\"}",
				"deployResource": "{\"type\":1,\"scm_artifacts\":[{\"is_main\":true,\"pub_base\":2,\"scm_id\":50629,\"scm_name\":\"ies/cdaas/testdemo7\",\"revision\":\"release_2025-05-12_901335966722\",\"target_commit_hash\":\"88e6a81bb76ef4c5b0ebbf6ceff2b7edead1100e\",\"git_repo_name\":\"iesarch/ies.cdaas.testdemo7\",\"git_repo_id\":92679,\"dev_mode\":2}],\"scm_merge_build_info\":{}}",
				"deployStrategy": "{\"tceBaseScmArtifacts\":[],\"clusterInfo\":null,\"scmArtifacts\":[],\"scmMergeBuildInfo\":null}",
				"controlledBy": 2,
				"releasedAt": "0001-01-01T00:00:00Z",
				"rollbackAt": "0001-01-01T00:00:00Z",
				"branchingModelConfig": "null",
				"activeStageId": 0,
				"scmVersionChoice": 0,
				"version": 3,
				"needDelete": false
			}]`
	changeItems := make([]*change_item_.ReleaseTicketChangeItem, 0)
	_ = json.UnmarshalString(itemsStr, &changeItems)
	ctx := context.Background()
	t.Run("map change item to pb", func() {
		items := MapChangeItems2pb(ctx, changeItems)
		t.Equal(1, len(items))
		fmt.Print(commonUtils.ToJson(items))
	})
}
