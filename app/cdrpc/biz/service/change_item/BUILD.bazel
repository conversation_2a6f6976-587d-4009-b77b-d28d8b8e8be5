load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "change_item",
    srcs = [
        "change_item_event.go",
        "deploy_escape.go",
        "helper.go",
        "model.go",
        "release_ticket_change_item.go",
        "release_ticket_change_item_stage.go",
        "release_ticket_pre_change_item.go",
        "service.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cdrpc/biz/dal/mysql",
        "//app/cdrpc/biz/dal/mysql/entity",
        "//app/cdrpc/biz/dal/mysql/repository",
        "//app/cdrpc/biz/dal/redis/repository",
        "//app/cdrpc/biz/dal/rmq",
        "//app/cdrpc/biz/domain/release_ticket",
        "//app/cdrpc/biz/domain/stage",
        "//app/cdrpc/biz/pkg/appcenter",
        "//app/cdrpc/biz/pkg/auth",
        "//app/cdrpc/biz/pkg/change_item_rpc",
        "//app/cdrpc/biz/pkg/integrate_sdk",
        "//app/cdrpc/biz/pkg/scm",
        "//app/cdrpc/biz/service/cd_gray",
        "//app/cdrpc/biz/service/release_ticket/helper",
        "//app/cdrpc/biz/service/rt_orchestration",
        "//app/cdrpc/config",
        "//app/cdrpc/kitex_gen/bits/cd/change_item",
        "//app/cdrpc/kitex_gen/bytedance/bits/config_service",
        "//app/cdrpc/kitex_gen/bytedance/bits/git_server",
        "//app/cdrpc/utils",
        "//app/cdrpc/utils/timeutil",
        "//idls/byted/devinfra/appcenter:appcenter_go_proto",
        "//idls/byted/devinfra/cd/branching_model_config:branching_model_config_go_proto",
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/paas:paas_go_proto",
        "//idls/byted/devinfra/cd/project:project_go_proto",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/cd/workflow:workflow_go_proto",
        "//idls/byted/devinfra/crossborder:crossborder_go_proto",
        "//idls/byted/devinfra/storage/storage_version:storage_version_go_proto",
        "//libs/bits_err",
        "//libs/common_lib/utils",
        "//libs/events",
        "//pkg/canal_provider/crossborder",
        "//pkg/cicd_utils",
        "@com_github_avast_retry_go//:retry-go",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@com_github_samber_lo//:lo",
        "@org_byted_code_canal_bytecycle_sdk//resource",
        "@org_byted_code_canal_provider//scm",
        "@org_byted_code_canal_provider//tce",
        "@org_byted_code_gopkg_lang//slices",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_byted_code_gopkg_lang_v2//operatorx",
        "@org_byted_code_gopkg_lang_v2//slicex",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_iesarch_cdaas_utils//erri",
        "@org_byted_code_iesarch_cdaas_utils//utils",
        "@org_byted_code_iesarch_paas_sdk//tce_config",
        "@org_byted_code_lang_gg//gptr",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_overpass_bits_integration_multi//kitex_gen/bits/integration/multi",
        "@org_byted_code_overpass_bits_integration_multi//kitex_gen/bytedance/bits/dev",
        "@org_byted_code_overpass_bytedance_bits_git_server//kitex_gen/bytedance/bits/git_server",
        "@org_byted_code_tce_tce_golang_sdk//:tce_golang_sdk",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "change_item_test",
    srcs = [
        "change_item_event_test.go",
        "deploy_escape_test.go",
        "helper_test.go",
        "release_ticket_change_item_deploy_test.go",
        "release_ticket_change_item_stage_test.go",
        "release_ticket_change_item_test.go",
        "service_test.go",
    ],
    embed = [":change_item"],
    deps = [
        "//app/cdrpc/biz/dal/mysql",
        "//app/cdrpc/biz/dal/mysql/entity",
        "//app/cdrpc/biz/dal/mysql/repository/mock",
        "//app/cdrpc/biz/dal/redis/repository",
        "//app/cdrpc/biz/dal/rmq",
        "//app/cdrpc/biz/domain/stage",
        "//app/cdrpc/biz/pkg/appcenter",
        "//app/cdrpc/biz/pkg/auth",
        "//app/cdrpc/biz/pkg/idgen",
        "//app/cdrpc/biz/pkg/integrate_sdk/mock",
        "//app/cdrpc/biz/pkg/resourceapi",
        "//app/cdrpc/biz/pkg/scm",
        "//app/cdrpc/biz/service/cd_gray",
        "//app/cdrpc/biz/service/release_ticket/helper",
        "//app/cdrpc/config",
        "//app/cdrpc/kitex_gen/bits/cd/change_item",
        "//app/cdrpc/kitex_gen/bytedance/bits/config_service",
        "//app/cdrpc/kitex_gen/bytedance/bits/git_server",
        "//app/cdrpc/testfactory",
        "//idls/byted/devinfra/appcenter:appcenter_go_proto",
        "//idls/byted/devinfra/cd/branching_model_config:branching_model_config_go_proto",
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/paas:paas_go_proto",
        "//idls/byted/devinfra/cd/release_ticket:release_ticket_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//idls/byted/devinfra/cd/workflow:workflow_go_proto",
        "//libs/bits_err",
        "//libs/common_lib/utils",
        "//pkg/canal_provider/crossborder",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_golang_mock//gomock",
        "@com_github_pkg_errors//:errors",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//suite",
        "@com_github_zeebo_errs//:errs",
        "@io_gorm_gorm//:gorm",
        "@org_byted_code_bits_monkey//:monkey",
        "@org_byted_code_canal_delivery_model//mutex_execution",
        "@org_byted_code_canal_provider//scm",
        "@org_byted_code_canal_provider//tce",
        "@org_byted_code_iesarch_paas_sdk//tce_config",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_overpass_bits_integration_multi//kitex_gen/bits/integration/multi",
        "@org_byted_code_overpass_bits_integration_multi//kitex_gen/bytedance/bits/dev",
        "@org_byted_code_overpass_bytedance_bits_git_server//kitex_gen/bytedance/bits/git_server",
        "@org_byted_code_tce_tce_golang_sdk//:tce_golang_sdk",
        "@org_golang_x_exp//slices",
    ],
)
