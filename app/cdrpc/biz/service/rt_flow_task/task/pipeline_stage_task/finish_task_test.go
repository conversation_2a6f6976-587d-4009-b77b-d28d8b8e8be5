package pipeline_stage_task

import (
	"context"
	"reflect"
	"testing"

	json "github.com/bytedance/sonic"

	rtflowconstants "code.byted.org/devinfra/hagrid/app/cdrpc/biz/constants/rt_flow"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/track"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	"code.byted.org/bits/monkey"
	rt_flow_task_servicemock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_flow_task/service/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_flow_task/task/base"
)

type PipelineStageFinishTaskTestSuite struct {
	suite.Suite
	ctx      context.Context
	cancel   context.CancelFunc
	mockCtrl *gomock.Controller
}

func (t *PipelineStageFinishTaskTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	t.mockCtrl = gomock.NewController(t.T())
}

func (t *PipelineStageFinishTaskTestSuite) TearDownTest() {
	t.cancel()
	monkey.UnpatchAll()
}

func TestPipelineStageFinishTask(t *testing.T) {
	suite.Run(t, new(PipelineStageFinishTaskTestSuite))
}

func (t *PipelineStageFinishTaskTestSuite) TestPipelineStageFinishTaskStart() {
	PipelineStageTaskSvc := rt_flow_task_servicemock.NewMockPipelineStageTaskSvc(t.mockCtrl)
	svc := &releasePipelineStageFinishTask{
		PipelineStageTaskSvc: PipelineStageTaskSvc,
		trackApi:             track.New(),
	}
	defer monkey.PatchInstanceMethod(reflect.TypeOf(svc.trackApi), "EmitExceptionRequest", func(ctx context.Context, downstreamPsm, downstreamApi, requestBody, scenario, businessId, errorMessage string, errorCode int64) {
		return
	}).UnPatch()
	t.Run("not pass", func() {
		params := &rtflowconstants.CommonParallelContext{
			Input: []map[string]interface{}{
				{},
				{"trigger_type": 1, "is_active_by_trigger": false},
			},
		}
		prevContext, err := json.Marshal(params)

		PipelineStageTaskSvc.EXPECT().FinishTaskStart(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
		pass, output, err := svc.Start(t.ctx, &base.TaskRequest{
			ReleaseTicketID: 1,
			PrevTaskContext: prevContext,
		})

		t.False(pass)
		t.Nil(output)
		t.NoError(err)
	})

	t.Run("invalid payload", func() {
		PipelineStageTaskSvc.EXPECT().FinishTaskStart(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
		pass, output, err := svc.Start(t.ctx, &base.TaskRequest{
			ReleaseTicketID: 1,
			PrevTaskContext: []byte{},
		})

		t.False(pass)
		t.Nil(output)
		t.Error(err)
	})
}
