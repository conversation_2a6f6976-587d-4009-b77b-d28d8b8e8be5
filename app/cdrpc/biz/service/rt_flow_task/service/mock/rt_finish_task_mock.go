// Code generated by MockGen. DO NOT EDIT.
// Source: code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_flow_task/service (interfaces: RtFinishTaskSvc)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	entity "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	gomock "github.com/golang/mock/gomock"
)

// MockRtFinishTaskSvc is a mock of RtFinishTaskSvc interface.
type MockRtFinishTaskSvc struct {
	ctrl     *gomock.Controller
	recorder *MockRtFinishTaskSvcMockRecorder
}

// MockRtFinishTaskSvcMockRecorder is the mock recorder for MockRtFinishTaskSvc.
type MockRtFinishTaskSvcMockRecorder struct {
	mock *MockRtFinishTaskSvc
}

// NewMockRtFinishTaskSvc creates a new mock instance.
func NewMockRtFinishTaskSvc(ctrl *gomock.Controller) *MockRtFinishTaskSvc {
	mock := &MockRtFinishTaskSvc{ctrl: ctrl}
	mock.recorder = &MockRtFinishTaskSvcMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRtFinishTaskSvc) EXPECT() *MockRtFinishTaskSvcMockRecorder {
	return m.recorder
}

// CancelStagePipelineRun mocks base method.
func (m *MockRtFinishTaskSvc) CancelStagePipelineRun(arg0 context.Context, arg1 *entity.DBReleaseTicket, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelStagePipelineRun", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelStagePipelineRun indicates an expected call of CancelStagePipelineRun.
func (mr *MockRtFinishTaskSvcMockRecorder) CancelStagePipelineRun(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelStagePipelineRun", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).CancelStagePipelineRun), arg0, arg1, arg2)
}

// CancelStagePipelineTrigger mocks base method.
func (m *MockRtFinishTaskSvc) CancelStagePipelineTrigger(arg0 context.Context, arg1 *entity.DBReleaseTicket, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelStagePipelineTrigger", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelStagePipelineTrigger indicates an expected call of CancelStagePipelineTrigger.
func (mr *MockRtFinishTaskSvcMockRecorder) CancelStagePipelineTrigger(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelStagePipelineTrigger", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).CancelStagePipelineTrigger), arg0, arg1, arg2)
}

// CloseIntegrationZone mocks base method.
func (m *MockRtFinishTaskSvc) CloseIntegrationZone(arg0 context.Context, arg1 *entity.DBReleaseTicket) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloseIntegrationZone", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloseIntegrationZone indicates an expected call of CloseIntegrationZone.
func (mr *MockRtFinishTaskSvcMockRecorder) CloseIntegrationZone(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloseIntegrationZone", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).CloseIntegrationZone), arg0, arg1)
}

// CompleteReleaseTicket mocks base method.
func (m *MockRtFinishTaskSvc) CompleteReleaseTicket(arg0 context.Context, arg1 *entity.DBReleaseTicket) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CompleteReleaseTicket", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CompleteReleaseTicket indicates an expected call of CompleteReleaseTicket.
func (mr *MockRtFinishTaskSvcMockRecorder) CompleteReleaseTicket(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompleteReleaseTicket", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).CompleteReleaseTicket), arg0, arg1)
}

// CreateRtPostTasks mocks base method.
func (m *MockRtFinishTaskSvc) CreateRtPostTasks(arg0 context.Context, arg1 *entity.DBReleaseTicket) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRtPostTasks", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRtPostTasks indicates an expected call of CreateRtPostTasks.
func (mr *MockRtFinishTaskSvcMockRecorder) CreateRtPostTasks(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRtPostTasks", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).CreateRtPostTasks), arg0, arg1)
}

// GetRt mocks base method.
func (m *MockRtFinishTaskSvc) GetRt(arg0 context.Context, arg1 uint64) (*entity.DBReleaseTicket, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRt", arg0, arg1)
	ret0, _ := ret[0].(*entity.DBReleaseTicket)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRt indicates an expected call of GetRt.
func (mr *MockRtFinishTaskSvcMockRecorder) GetRt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRt", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).GetRt), arg0, arg1)
}

// StoreTBDReleasedInfo mocks base method.
func (m *MockRtFinishTaskSvc) StoreTBDReleasedInfo(arg0 context.Context, arg1 *entity.DBReleaseTicket, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoreTBDReleasedInfo", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// StoreTBDReleasedInfo indicates an expected call of StoreTBDReleasedInfo.
func (mr *MockRtFinishTaskSvcMockRecorder) StoreTBDReleasedInfo(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoreTBDReleasedInfo", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).StoreTBDReleasedInfo), arg0, arg1, arg2)
}

// UnprotectTBDReleaseBranch mocks base method.
func (m *MockRtFinishTaskSvc) UnprotectTBDReleaseBranch(arg0 context.Context, arg1 *entity.DBReleaseTicket, arg2 bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnprotectTBDReleaseBranch", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnprotectTBDReleaseBranch indicates an expected call of UnprotectTBDReleaseBranch.
func (mr *MockRtFinishTaskSvcMockRecorder) UnprotectTBDReleaseBranch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnprotectTBDReleaseBranch", reflect.TypeOf((*MockRtFinishTaskSvc)(nil).UnprotectTBDReleaseBranch), arg0, arg1, arg2)
}
