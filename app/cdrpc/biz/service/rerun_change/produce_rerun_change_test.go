package rerun_change

import (
	"context"
	"fmt"
	"testing"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	mock2 "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_operation_reporter/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/pipelinepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type ProduceRerunChangeTestSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
}

func (t *ProduceRerunChangeTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
}

func (t *ProduceRerunChangeTestSuite) TearDownTest() {
	t.cancel()
}

func TestProduce(t *testing.T) {
	suite.Run(t, new(ProduceRerunChangeTestSuite))
}

func (t *ProduceRerunChangeTestSuite) TestProduceRerunChange() {
	ctrl := gomock.NewController(t.T())

	rtOperationReporter := mock2.NewMockRtOperationReporter(ctrl)
	rtOperationReporter.EXPECT().SendReleaseTicketPipelineRerunChange(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	t.Run("filter has not run main ppl rerun changes", func() {
		ctx := context.Background()
		svc := &rerunChangeSvc{
			stagePipelineDao:    repository.NewStagePipelineDao(),
			rerunChangeDao:      repository.NewRerunChangeDao(),
			rtOperationReporter: rtOperationReporter,
		}
		dao := repository.NewRerunChangeDao()

		db := testfactory.NewUnitTestDB(ctx, &entity.DBStagePipeline{}, &entity.DBRerunChange{})
		mysql.DefaultDB = db

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_CN",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   1,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_CN",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      2,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_I18N",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   3,
			HasRun:       false,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_I18N",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      4,
		})

		err := svc.ProduceRerunChange(ctx, &ProduceRerunChangeParams{
			StageIDs: []uint64{1},
			RerunChanges: []*RerunChange{
				{
					RerunChangeType: pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_DEPENDENCY,
					AffectPipelines: []*PipelineCore{
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN,
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN,
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
					},
				},
			},
		})
		t.NoError(err)

		dbRerunChanges, err := dao.BatchGet(ctx, 1, []uint64{1, 2, 3, 4})
		t.NoError(err)
		t.Len(dbRerunChanges, 2)
	})

	t.Run("produce default rerun changes", func() {
		ctx := context.Background()
		svc := &rerunChangeSvc{
			stagePipelineDao:    repository.NewStagePipelineDao(),
			rerunChangeDao:      repository.NewRerunChangeDao(),
			rtOperationReporter: rtOperationReporter,
		}
		dao := repository.NewRerunChangeDao()

		db := testfactory.NewUnitTestDB(ctx, &entity.DBStagePipeline{}, &entity.DBRerunChange{})
		mysql.DefaultDB = db

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_CN",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   1,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_CN",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      2,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_I18N",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   3,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_I18N",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      4,
		})

		err := svc.ProduceRerunChange(ctx, &ProduceRerunChangeParams{
			StageIDs: []uint64{1},
			RerunChanges: []*RerunChange{
				{
					RerunChangeType: pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_DEPENDENCY,
					AffectPipelines: []*PipelineCore{
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN,
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN,
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
					},
				},
			},
		})
		t.NoError(err)

		dbRerunChanges, err := dao.BatchGet(ctx, 1, []uint64{1, 2, 3, 4})
		t.NoError(err)
		t.Len(dbRerunChanges, 4)
	})

	t.Run("produce delete rerun changes", func() {
		ctx := context.Background()
		svc := &rerunChangeSvc{
			stagePipelineDao:    repository.NewStagePipelineDao(),
			rerunChangeDao:      repository.NewRerunChangeDao(),
			rtOperationReporter: rtOperationReporter,
		}
		dao := repository.NewRerunChangeDao()

		db := testfactory.NewUnitTestDB(ctx, &entity.DBStagePipeline{}, &entity.DBRerunChange{})
		mysql.DefaultDB = db

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_CN",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   1,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_CN",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      2,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_I18N",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   3,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_I18N",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      4,
		})

		db.Create(&entity.DBRerunChange{
			StageID:    1,
			PipelineID: 1,
			ChangeType: "CHANGE_ITEM_TYPE_DEPENDENCY",
		})

		db.Create(&entity.DBRerunChange{
			StageID:    1,
			PipelineID: 2,
			ChangeType: "CHANGE_ITEM_TYPE_DEPENDENCY",
		})

		db.Create(&entity.DBRerunChange{
			StageID:    1,
			PipelineID: 2,
			ChangeType: "CHANGE_ITEM_TYPE_PIPELINE_UPDATE",
		})

		db.Delete(&entity.DBStagePipeline{PipelineID: 2})
		db.Delete(&entity.DBStagePipeline{PipelineID: 4})

		err := svc.ProduceRerunChange(ctx, &ProduceRerunChangeParams{
			StageIDs: []uint64{1},
			RerunChanges: []*RerunChange{
				{
					RerunChangeType: pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_PROJECT_DELETED,
					AffectPipelines: []*PipelineCore{
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
					},
				},
			},
		})
		t.NoError(err)

		dbRerunChanges, err := dao.BatchGet(ctx, 1, []uint64{1, 2, 3, 4})
		t.NoError(err)
		t.Len(dbRerunChanges, 3)
		for _, c := range dbRerunChanges {
			fmt.Printf("%+v\n", c)
		}

		dbRerunChanges2, err := dao.BatchGet(ctx, 1, []uint64{2})
		t.NoError(err)
		t.Len(dbRerunChanges2, 1)
		t.Equal(pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_PROJECT_DELETED.String(), dbRerunChanges2[0].ChangeType)
	})

	t.Run("produce add rerun changes", func() {
		ctx := context.Background()
		svc := &rerunChangeSvc{
			stagePipelineDao:    repository.NewStagePipelineDao(),
			rerunChangeDao:      repository.NewRerunChangeDao(),
			rtOperationReporter: rtOperationReporter,
		}
		dao := repository.NewRerunChangeDao()

		db := testfactory.NewUnitTestDB(ctx, &entity.DBStagePipeline{}, &entity.DBRerunChange{})
		mysql.DefaultDB = db

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_CN",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   1,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_CN",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      2,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:      1,
			ControlPlane: "CONTROL_PLANE_I18N",
			PipelineType: "PIPELINE_TYPE_MAIN",
			PipelineID:   3,
			HasRun:       true,
		})

		db.Create(&entity.DBStagePipeline{
			StageID:         1,
			ControlPlane:    "CONTROL_PLANE_I18N",
			ProjectType:     "PROJECT_TYPE_TCE",
			ProjectUniqueID: "p.s.m",
			PipelineType:    "PIPELINE_TYPE_PROJECT",
			PipelineID:      4,
		})

		db.Create(&entity.DBRerunChange{
			StageID:    1,
			PipelineID: 1,
			ChangeType: "CHANGE_ITEM_TYPE_DEPENDENCY",
		})

		db.Create(&entity.DBRerunChange{
			StageID:    1,
			PipelineID: 2,
			ChangeType: "CHANGE_ITEM_TYPE_PROJECT_DELETED",
		})

		db.Create(&entity.DBRerunChange{
			StageID:    1,
			PipelineID: 4,
			ChangeType: "CHANGE_ITEM_TYPE_PROJECT_DELETED",
		})

		db.Delete(&entity.DBStagePipeline{PipelineID: 2})
		db.Delete(&entity.DBStagePipeline{PipelineID: 4})

		err := svc.ProduceRerunChange(ctx, &ProduceRerunChangeParams{
			StageIDs: []uint64{1},
			RerunChanges: []*RerunChange{
				{
					RerunChangeType: pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_PROJECT_ADD,
					AffectPipelines: []*PipelineCore{
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
						{
							ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_I18N,
							PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
							Project: &sharedpb.ProjectUniqueKey{
								ProjectUniqueId: "p.s.m",
								ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
								ProjectName:     "p.s.m",
							},
						},
					},
				},
			},
		})
		t.NoError(err)

		dbRerunChanges, err := dao.BatchGet(ctx, 1, []uint64{1, 2, 3, 4})
		t.NoError(err)
		dao.Delete(ctx, &entity.DBRerunChange{
			StageID:    1,
			PipelineID: 2,
			ChangeType: pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_PROJECT_DELETED.String(),
		})
		t.Len(dbRerunChanges, 3)

		dbRerunChanges2, err := dao.BatchGet(ctx, 1, []uint64{2})
		t.NoError(err)
		t.Len(dbRerunChanges2, 1)
		t.Equal(pipelinepb.ChangeItemType_CHANGE_ITEM_TYPE_PROJECT_ADD.String(), dbRerunChanges2[0].ChangeType)
	})
}
