package rt_change_item_manager

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	json "github.com/bytedance/sonic"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
	"github.com/samber/lo"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	redisrepository "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis/repository"
	domain "code.byted.org/devinfra/hagrid/app/cdrpc/biz/domain/release_ticket"
	releaseticketdomain "code.byted.org/devinfra/hagrid/app/cdrpc/biz/domain/release_ticket"
	rtprojectdomain "code.byted.org/devinfra/hagrid/app/cdrpc/biz/domain/rt_project"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/storage_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	release_ticket_helper "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket/helper"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/released_commit_manager"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtproject"
	change_item_ "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bits/cd/change_item"
	dev_ "code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/dev"
	cdutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	common_utils "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/branching_model_configpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/projectpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/workflowpb"
	sharedpb2 "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/storage/storage_versionpb"
	"code.byted.org/devinfra/hagrid/pkg/cicd_utils"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/facility/set"
	"code.byted.org/gopkg/gopool"
	"code.byted.org/gopkg/lang/v2/operatorx"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs/v2"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gptr"
	"code.byted.org/lang/gg/gslice"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bits/integration/multi"
	"code.byted.org/overpass/bits_integration_multi/kitex_gen/bytedance/bits/dev"
)

type ComputeHybridChannelListParams struct {
	prevChangeItem *release_ticketpb.ReleaseTicketChangeItem // 之前的 changeitem
	isNewProject   bool                                      // 是否为新增项目
	newChannelList []*paaspb.ChannelItem                     // 本次变更的 channelList
	isUpdateRt     bool                                      // 是否为更新发布单
}

func (a *rtChangeItemManager) MapChangeItems2Thrift(ctx context.Context, projects []*release_ticketpb.Project, rtConfig *workflowpb.ReleaseTicketConfig) (items []*change_item_.ReleaseTicketChangeItem, err error) {
	for _, p := range projects {
		if p.GetProjectInfo() == nil {
			return nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg("项目信息不存在")
		}

		deployTarget := &change_itempb.ChangeItemDeployTarget{
			ProjectType:     sharedpb.ProjectType(sharedpb.ProjectType_value[p.GetProjectInfo().GetType()]),
			ProjectUniqueId: p.GetProjectInfo().GetProjectUniqueId(),
			ProjectName:     p.GetProjectInfo().GetName(),
			TtpWebName:      p.GetProjectInfo().GetTtpWebName(),
			ServiceMetaWeb:  nil,
			ChannelItems:    p.GetChannelItems(),
		}
		DeployDependencies := slicex.Map(p.DeployDependencies, func(from *release_ticketpb.ProjectInfo) *change_item_.DependencyItem {
			pt, _ := dev_.ProjectTypeFromString(p.GetProjectInfo().GetType())
			return &change_item_.DependencyItem{
				ProjectUniqueId: from.ProjectUniqueId,
				ProjectName:     from.GetName(),
				ProjectType:     pt,
			}
		})

		deployTargetStr, err := change_item.MarshalDeployTarget(deployTarget)
		if err != nil {
			return nil, erri.Error(err)
		}

		projectType, _ := dev_.ProjectTypeFromString(p.GetProjectInfo().GetType())
		ScmVersionChoice, _ := change_item_.ScmVersionChoiceTypeFromString(p.ProjectInfo.ScmVersionChoice.String())

		// 计算制品信息
		for _, buildConfig := range p.BuildConfigs {
			// 这里遵循空间分支模型的单发布单，以及hotfix的单发布单都是要遵循发布单纬度分支模型
			projectBranchModel := a.changeItemSvc.ComputeBranchModelByMainRepoInfoAndSpaceBranchModel(buildConfig.GetMainRepoInfo(), rtConfig.GetBranchingModelConfig(),
				rtConfig.GetBranchingModelConfigInheritSpace() || rtConfig.IsHotfix)

			projectBranchModelStr, _ := json.MarshalString(projectBranchModel)
			controlPanel, _ := dev_.ControlPlaneFromString(buildConfig.ControlPanel)

			var item = &change_item_.ReleaseTicketChangeItem{
				ProjectUniqueId:      p.ProjectInfo.ProjectUniqueId,
				ProjectType:          projectType,
				ProjectName:          gptr.Of(p.ProjectInfo.GetName()),
				DependencyItems:      DeployDependencies,
				ControlPlane:         controlPanel,
				DeployTarget:         deployTargetStr,
				DeployResource:       "",
				DeployStrategy:       "",
				ProjectOwners:        p.ProjectInfo.GetProjectOwners(),
				ControlledBy:         nil,
				Reviewers:            nil,
				IsDeleted:            nil,
				ReleasedAt:           nil,
				RollbackAt:           nil,
				BranchingModelConfig: projectBranchModelStr,
				WebUsttpProjectId:    nil,
				ActiveStageId:        nil,
				ScmVersionChoice:     gptr.Of(ScmVersionChoice),
				ProjectLockState:     nil,
				Version:              0,
			}

			deployResource := change_itempb.ChangeItemArtifact{}
			deployResource.Type = operatorx.IfThen(p.ProjectInfo.Type == dev_.ProjectType_PROJECT_TYPE_TCC.String(),
				change_itempb.ArtifactType_ARTIFACT_TYPE_TCC, change_itempb.ArtifactType_ARTIFACT_TYPE_SCM)

			if p.ProjectInfo.Type != dev_.ProjectType_PROJECT_TYPE_TCC.String() {
				mainScm, err := a.GetSCMInfoByMainRepoInfo(buildConfig.GetMainRepoInfo())
				if err != nil {
					return nil, bits_err.RELEASETICKET.ErrInvalidInput.PassThrough(err)
				}
				codebaseProject, err := a.gitSvc.GetProjectByRepoName(ctx, mainScm.GetGitRepoName())
				if err != nil {
					logs.CtxError(ctx, "[MapChangeItems2Thrift] GetProject error: %s", err.Error())
					return nil, err
				}
				mainScm.GitRepoId = int64(codebaseProject.Id)

				deployResource.ScmArtifacts = append(deployResource.ScmArtifacts, mainScm)

				for _, dep := range buildConfig.ScmDependencies {
					artifact := &change_itempb.SCMArtifact{
						ScmId:       dep.Id,
						ScmName:     dep.Name,
						IsMain:      dep.GetIsMain(),
						GitRepoName: dep.GitRepoName,
						PubBase:     sharedpb.ScmPubBase(sharedpb.ScmPubBase_value[dep.PubBase]),
						UseLatest:   dep.UseLatest,
						GitRepoId:   dep.GitRepoId,
					}

					if dep.GetPubBase() == sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION.String() {
						artifact.Version = dep.Revision
					} else {
						artifact.Revision = dep.Revision
					}
					deployResource.ScmArtifacts = append(deployResource.ScmArtifacts, artifact)
				}
			}

			deployResourceStr, err := change_item.MarshalDeployResource(&deployResource)
			if err != nil {
				return nil, erri.Error(err)
			}

			item.DeployResource = deployResourceStr
			items = append(items, item)
		}

	}

	return items, nil
}

func (a *rtChangeItemManager) GetSCMInfoByMainRepoInfo(mainRepoInfo *release_ticketpb.MainRepoInfo) (*change_itempb.SCMArtifact, error) {
	pubBase := sharedpb.ScmPubBase(sharedpb.ScmPubBase_value[mainRepoInfo.GetPubBase()])
	info := &change_itempb.SCMArtifact{
		IsMain:      true,
		PubBase:     pubBase,
		ScmId:       mainRepoInfo.GetScmId(),
		ScmName:     mainRepoInfo.GetScmName(),
		GitRepoName: mainRepoInfo.GetGitRepoName(),
	}

	switch pubBase {
	case sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH:
		info.Revision = operatorx.IfThen(mainRepoInfo.GetIntegrationBranch() != "", mainRepoInfo.GetIntegrationBranch(), mainRepoInfo.GetReleaseBranch())
	case sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION:
		info.Version = mainRepoInfo.GetVersion()
	case sharedpb.ScmPubBase_SCM_PUB_BASE_COMMIT, sharedpb.ScmPubBase_SCM_PUB_BASE_TAG:
		info.Revision = mainRepoInfo.GetReleaseRef()
	}
	return info, nil
}

// HandleCdProjectInfo 把cd的项目信息转换成ci项目的结构，因为cd前端传项目信息只是一个临时的解决方案，还是以ci结构为主，且逻辑保持一致。
func (a *rtChangeItemManager) HandleCdProjectInfo(ctx context.Context, rtId uint64, workSpaceId uint64, operator string, ps []*release_ticketpb.Project, wc *workflowpb.WorkflowConfig, refCommit string) ([]*change_item.ProjectWithBranchModel, map[string]*change_item.ChangeItemBranchSlotValue, error) {
	// 目前基本所有的场景都会走到这个if里，这个函数下面大部分的逻辑都不会走到
	if shouldHandleCDProjectByMainRepoInfo(ps) {
		projects, err := a.HandleCDProjectByMainRepoInfo(ctx, workSpaceId, rtId, operator, ps, wc.GetReleaseTicketConfig())
		if err != nil {
			return nil, nil, err
		}
		return projects, nil, nil
	}
	res, changeItemSlotValueMap, err := a.HandleCdProjectInfoByDefault(ctx, rtId, workSpaceId, operator, ps, wc, refCommit)
	if err != nil {
		return nil, nil, err
	}
	return res, changeItemSlotValueMap, nil
}

func shouldHandleCDProjectByMainRepoInfo(ps []*release_ticketpb.Project) bool {
	return gslice.Any(ps, func(project *release_ticketpb.Project) bool {
		if len(project.GetBuildConfigs()) == 0 {
			return false
		}

		return project.GetBuildConfigs()[0].GetMainRepoInfo().GetGitRepoName() != ""
	})
}

func (a *rtChangeItemManager) HandleCDProjectByMainRepoInfo(ctx context.Context, workSpaceId, rtId uint64, operator string, projects []*release_ticketpb.Project, releaseTicketConfig *workflowpb.ReleaseTicketConfig) ([]*change_item.ProjectWithBranchModel, error) {
	var (
		res []*change_item.ProjectWithBranchModel
		err error
	)
	isTBD := releaseTicketConfig.GetTrunkConfig().GetGitRepoName() != ""

	for _, p := range projects {
		if p.GetProjectInfo() == nil {
			return nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg("项目信息不存在")
		}

		var ciP = &multi.ProjectInfo{
			ProjectUniqueId:  p.ProjectInfo.ProjectUniqueId,
			UpdateTime:       time.Now().Unix(),
			ProjectName:      p.ProjectInfo.GetName(),
			ProjectOwnerList: p.ProjectInfo.GetProjectOwners(), // todo 权限校验
			Operator:         &operator,
			Extra: &multi.ProjectItemExtra{
				TtpWebName: p.GetProjectInfo().GetTtpWebName(),
			},
			ChannelItems: gslice.Map(p.GetChannelItems(), func(item *paaspb.ChannelItem) *dev.ChannelItem {
				return &dev.ChannelItem{
					Region:              dev.GeckoControlPlane(item.GetRegion()),
					GeckoAppId:          int64(item.GetGeckoAppId()),
					GeckoEnvType:        dev.GeckoEnvType(item.GetGeckoEnvType()),
					GeckoDeploymentId:   int64(item.GetGeckoDeploymentId()),
					GeckoChannelId:      int64(item.GetGeckoChannelId()),
					Platform:            dev.GeckoPlatformType(item.GetPlatform()),
					GeckoAppName:        item.GetGeckoAppName(),
					GeckoDeploymentName: item.GetGeckoDeploymentName(),
					GeckoChannelName:    item.GetGeckoChannelName(),
				}
			}),
		}
		ciP.ProjectType, err = dev.ProjectTypeFromString(p.ProjectInfo.Type)
		if err != nil {
			logs.CtxError(ctx, "[HandleCDProjectByMainRepoInfo] wrong type: %s error: %s", p.ProjectInfo.Type, err.Error())
			return nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg(fmt.Sprintf("项目 %s 类型 %s 不合法", p.ProjectInfo.GetName(), p.ProjectInfo.GetType()))
		}
		ciP.Extra.ScmVersionChoiceType, _ = multi.ScmVersionChoiceTypeFromString(p.ProjectInfo.ScmVersionChoice.String())

		// 部署依赖
		var deployProjectInfos []*dev.DeployProjectInfo
		for _, d := range p.DeployDependencies {
			projectType, err := dev.ProjectTypeFromString(d.Type)
			if err != nil {
				logs.CtxError(ctx, "[HandleCDProjectByMainRepoInfo] wrong type: %s error: %s", p.ProjectInfo.Type, err.Error())
				return nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg(fmt.Sprintf("项目 %s 类型 %s 不合法", p.ProjectInfo.GetName(), p.ProjectInfo.GetType()))
			}
			deployProjectInfos = append(deployProjectInfos, &dev.DeployProjectInfo{
				Name:            d.Name,
				Psm:             d.Psm,
				ProjectUniqueId: d.ProjectUniqueId,
				Type:            projectType,
			})
		}

		// 计算制品信息和分支模型
		var cpBranchModelMap = make(map[sharedpb.ControlPlane]*branching_model_configpb.BranchingModelConfig)
		var mainRepoName string
		var mainRepoID int64
		for _, buildConfig := range p.BuildConfigs {
			var content = &multi.Content{
				// TCC 产物/制品类型为暂存区，其他为 dep
				ContentType: operatorx.IfThen(p.ProjectInfo.Type == dev.ProjectType_PROJECT_TYPE_TCC.String(),
					multi.ContentType_CONTENT_TYPE_STORAGE_TCC_CHANGE, multi.ContentType_CONTENT_TYPE_CODE_CHANGE),
			}
			content.ControlPanel, err = dev.ControlPlaneFromString(buildConfig.ControlPanel)
			if err != nil {
				logs.CtxError(ctx, "[HandleCDProjectByMainRepoInfo] wrong control plane type %s, error: %s", buildConfig.ControlPanel, err.Error())
				return nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg(fmt.Sprint("控制面信息不合法", common_utils.ToJson(buildConfig.ControlPanel)))
			}

			if content.ContentType == multi.ContentType_CONTENT_TYPE_CODE_CHANGE {
				// 这里遵循空间分支模型的单发布单，以及hotfix的单发布单都是要遵循发布单纬度分支模型
				projectBranchModel := a.changeItemSvc.ComputeBranchModelByMainRepoInfoAndSpaceBranchModel(buildConfig.GetMainRepoInfo(), releaseTicketConfig.GetBranchingModelConfig(),
					releaseTicketConfig.GetBranchingModelConfigInheritSpace() || releaseTicketConfig.IsHotfix)
				cpBranchModelMap[sharedpb.ControlPlane(content.ControlPanel)] = projectBranchModel
				mainRepo, err := a.changeItemSvc.FillSCMInfoByMainRepoInfo(ctx, buildConfig.GetMainRepoInfo())
				if err != nil {
					return nil, bits_err.RELEASETICKET.ErrInvalidInput.PassThrough(err)
				}

				if mainRepo.GetPubBase() == dev.SCMPubBase_SCM_PUB_BASE_BRANCH {
					// 如果是tbd 且前端没设置release ref， 则使用trunk branch，校验发布分支是否存在 或 切出发布分支
					if isTBD && buildConfig.GetMainRepoInfo().GetReleaseRef() == "" {
						if projectBranchModel.GetTrunkBranch().GetName() == branching_model_configpb.BranchNamingType_BRANCH_NAMING_TYPE_GIT_DEFAULT.String() {
							gitDefaultBranchMap, err := release_ticket_helper.GetDefaultBranchMapByRt(ctx, rtId, []redisrepository.ProjectGitItem{
								{
									GitName: mainRepo.GetGitRepoName(),
								},
							})
							if err != nil {
								return nil, err
							}
							buildConfig.GetMainRepoInfo().ReleaseRef = gitDefaultBranchMap[mainRepo.GetGitRepoName()]
						} else {
							buildConfig.GetMainRepoInfo().ReleaseRef = projectBranchModel.GetTrunkBranch().GetName()

						}
					}
					err = a.branchManager.CheckOrCreateRelatedBranch(ctx, workSpaceId, buildConfig.GetMainRepoInfo(), projectBranchModel, isTBD)
					if err != nil {
						return nil, bits_err.RELEASETICKET.ErrCallCodebase.SetTip("创建分支失败")
					}
				}

				codebaseProject, err := a.gitSvc.GetProjectByRepoName(ctx, mainRepo.GetGitRepoName())
				if err != nil {
					logs.CtxError(ctx, "[HandleCDProjectByMainRepoInfo] GetProject error: %s", err.Error())
					return nil, bits_err.RELEASETICKET.ErrCallCodebaseRepoInfo.PassThrough(err).SetTip("调用 Codebase 接口获取仓库 %s 信息失败", mainRepo.GetGitRepoName())
				}
				if projectBranchModel.GetIntegrationBranch().GetDeleteAfterComplete() && projectBranchModel.GetIntegrationBranch().GetName() == codebaseProject.DefaultBranch {
					return nil, bits_err.RELEASETICKET.ErrInvalidInput.PassThrough(fmt.Errorf("can not delete repo default branch"))
				}

				content.RepoId = int64(codebaseProject.GetId())
				content.ScmList = append(content.ScmList, mainRepo)

				mainRepoName = mainRepo.GetGitRepoName()
				mainRepoID = content.RepoId
			}
			// 填充依赖
			for _, dep := range buildConfig.ScmDependencies {
				pubBase, err := dev.SCMPubBaseFromString(dep.PubBase)
				if err != nil {
					logs.CtxError(ctx, "[HandleCdProjectInfo] SCMPubBaseFromString error: %s", err.Error())
					return nil, bits_err.RELEASETICKET.ErrInvalidInput.SetTip("SCM 信息 %v 不合法", dep.PubBase)
				}
				content.ScmList = append(content.ScmList, &dev.SCMInfo{
					Id:          int32(dep.Id),
					Name:        dep.Name,
					IsMain:      dep.IsMain,
					Revision:    dep.Revision,
					GitRepoName: gptr.Of(dep.GitRepoName),
					PubBase:     pubBase,
					UseLatest:   &dep.UseLatest,
					GitRepoId:   gptr.Of(dep.GitRepoId),
				})
			}
			content.DeployDependencies = deployProjectInfos
			ciP.Artifact = append(ciP.Artifact, content)
		}

		if p.ProjectInfo.Type != dev.ProjectType_PROJECT_TYPE_TCC.String() {
			ciP.RepoId = mainRepoID
		}

		res = append(res, &change_item.ProjectWithBranchModel{
			Project:                 ciP,
			ControlPlaneBranchModel: cpBranchModelMap,
		})

		// 无论是单发布单和还是火车，都主动尝试注册一下仓库app
		go func(repoName string) {
			if repoName == "" {
				return
			}
			a.gitSvc.InstallCodebaseApp(cdutils.GetAsyncContext(ctx), cdutils.GetCodebaseURl(repoName))
		}(mainRepoName)
	}

	logs.CtxInfo(ctx, "[HandleCDProjectByMainRepoInfo] input: %s, output: %s", common_utils.ToJson(projects), common_utils.ToJson(res))
	return res, nil
}

// HandleCdProjectInfoByDefault
// 1. 转换 slot 占位符（包括分支占位、变量占位）得到 slotValue
// 2. 转换数据结构
// 3. 过滤 pre_change_item
// 4. 使用 slotValue 替换 slot
// 5. 创建真实分支（包括找真实的 checkoutRef），可异步
// 改造背景：hotfix 需要按项目找真实 checkoutRef 的功能，需要把本方法按上述步骤重构，以方便新增能力
// isTeamFlowGray 研发流程全量后移除这个字段
// 使用 ComputeBranchModelByMainRepoInfo， 全量后正好移除旧hotfix逻辑、自定义项目逻辑
// Deprecated 尽量不要再添加逻辑了
func (a *rtChangeItemManager) HandleCdProjectInfoByDefault(ctx context.Context, rtId uint64, workSpaceId uint64, operator string, ps []*release_ticketpb.Project, wc *workflowpb.WorkflowConfig, refCommit string) ([]*change_item.ProjectWithBranchModel, map[string]*change_item.ChangeItemBranchSlotValue, error) {
	var (
		res []*change_item.ProjectWithBranchModel
		now = time.Now()
		err error
	)

	isHotfix := wc.GetReleaseTicketConfig().GetIsHotfix()
	isTBD := wc.GetReleaseTicketConfig().GetTrunkConfig().GetGitRepoName() != ""
	changeItemSlotValueMap, err := a.changeItemSvc.ComputeChangeItemSlotValue(ctx, rtId, ps, wc, refCommit, isTBD)
	if err != nil {
		logs.CtxError(ctx, "获取项目分支信息失败，%s", err.Error())
		return nil, nil, err
	}

	for _, p := range ps {
		// 项目基本信息
		if p.GetProjectInfo() == nil {
			return nil, nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg("项目信息不存在")
		}
		isPreChangeItem := false
		var ciP = &multi.ProjectInfo{
			ProjectUniqueId:  p.ProjectInfo.ProjectUniqueId,
			UpdateTime:       now.Unix(),
			ProjectName:      p.ProjectInfo.GetName(),
			ProjectOwnerList: p.ProjectInfo.GetProjectOwners(), // todo 权限校验
			Operator:         &operator,
			Extra: &multi.ProjectItemExtra{
				TtpWebName: p.GetProjectInfo().GetTtpWebName(),
			},
			ChannelItems: gslice.Map(p.GetChannelItems(), func(item *paaspb.ChannelItem) *dev.ChannelItem {
				return &dev.ChannelItem{
					Region:              dev.GeckoControlPlane(item.GetRegion()),
					GeckoAppId:          int64(item.GetGeckoAppId()),
					GeckoEnvType:        dev.GeckoEnvType(item.GetGeckoEnvType()),
					GeckoDeploymentId:   int64(item.GetGeckoDeploymentId()),
					GeckoChannelId:      int64(item.GetGeckoChannelId()),
					Platform:            dev.GeckoPlatformType(item.GetPlatform()),
					GeckoAppName:        item.GetGeckoAppName(),
					GeckoDeploymentName: item.GetGeckoDeploymentName(),
					GeckoChannelName:    item.GetGeckoChannelName(),
				}
			}),
		}
		ciP.ProjectType, err = dev.ProjectTypeFromString(p.ProjectInfo.Type)
		if err != nil {
			logs.CtxError(ctx, "[HandleCdProjectInfo] wrong type: %s error: %s", p.ProjectInfo.Type, err.Error())
			return nil, nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg(fmt.Sprintf("项目 %s 类型 %s 不合法", p.ProjectInfo.GetName(), p.ProjectInfo.GetType()))
		}
		ciP.Extra.ScmVersionChoiceType, _ = multi.ScmVersionChoiceTypeFromString(p.ProjectInfo.ScmVersionChoice.String())
		repoName := ""

		// 依赖
		var dds []*dev.DeployProjectInfo
		for _, d := range p.DeployDependencies {
			di := &dev.DeployProjectInfo{
				Name:            d.Name,
				Psm:             d.Psm,
				ProjectUniqueId: d.ProjectUniqueId,
			}
			di.Type, err = dev.ProjectTypeFromString(d.Type)
			if err != nil {
				logs.CtxError(ctx, "[HandleCdProjectInfo] wrong type: %s error: %s", p.ProjectInfo.Type, err.Error())
				return nil, nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg(fmt.Sprintf("项目 %s 类型 %s 不合法", p.ProjectInfo.GetName(), p.ProjectInfo.GetType()))
			}
			dds = append(dds, di)
		}
		// 部署详情
		for _, bc := range p.BuildConfigs {
			var c = &multi.Content{
				// TCC 产物/制品类型为暂存区，其他为 scm
				ContentType: operatorx.IfThen(p.ProjectInfo.Type == dev.ProjectType_PROJECT_TYPE_TCC.String(), multi.ContentType_CONTENT_TYPE_STORAGE_TCC_CHANGE, multi.ContentType_CONTENT_TYPE_CODE_CHANGE),
			}
			c.ControlPanel, err = dev.ControlPlaneFromString(bc.ControlPanel)
			if err != nil {
				logs.CtxError(ctx, "[HandleCdProjectInfo] wrong control plane type %s, error: %s", bc.ControlPanel, err.Error())
				return nil, nil, bits_err.RELEASETICKET.ErrInvalidInput.AddErrMsg(fmt.Sprintf("控制面信息 %s 不合法", bc.ControlPanel))
			}
			uniqKey := domain.ChangeItemUniqKey(
				sharedpb.ProjectType(sharedpb.ProjectType_value[p.GetProjectInfo().GetType()]),
				p.GetProjectInfo().GetProjectUniqueId(),
				sharedpb.ControlPlane(sharedpb.ControlPlane_value[bc.GetControlPanel()]),
			)

			// 默认项目的所有控制面的repo一致，如果不是，就会有bug，和ci一致
			// 自定义项目使用RepoDependencies
			if len(bc.ScmDependencies) != 0 {
				for _, scm := range bc.ScmDependencies {
					// 如果项目为创建mr，将isPreChangeItem置为true，后面根据isPreChangeItem判断是否需要将项目写入change_item表
					if scm.GetPreChangeItemType() == change_itempb.PreChangeItemType_PRE_CHANGE_ITEM_TYPE_MR {
						isPreChangeItem = true
						break
					}
					info := &dev.SCMInfo{
						Id:          int32(scm.Id),
						Name:        scm.Name,
						IsMain:      scm.IsMain,
						Revision:    scm.Revision,
						GitRepoName: gptr.Of(scm.GitRepoName),
						GitRepoId:   gptr.Of(scm.GitRepoId),
						UseLatest:   &scm.UseLatest,
					}
					info.PubBase, err = dev.SCMPubBaseFromString(scm.PubBase)
					if err != nil {
						logs.CtxError(ctx, "[HandleCdProjectInfo] SCMPubBaseFromString error: %s", err.Error())
						return nil, nil, bits_err.RELEASETICKET.ErrInvalidInput.SetTip("SCM 信息 %s 不合法", scm.PubBase)
					}

					if info.GetIsMain() && info.PubBase == dev.SCMPubBase_SCM_PUB_BASE_BRANCH {
						// 开发任务变更的源分支
						ciP.Branch = scm.Revision
						changeItemSlotValue, ok := changeItemSlotValueMap[uniqKey]
						if ok {
							info.Revision = changeItemSlotValue.IntegrationBranch
							ciP.Branch = changeItemSlotValue.IntegrationBranch
						}
					}
					if scm.GetIsMain() {
						repoName = scm.GitRepoName
					}
					c.ScmList = append(c.ScmList, info)
				}
			} else if p.ProjectInfo.Type == dev.ProjectType_PROJECT_TYPE_CUSTOM.String() {
				for _, rid := range bc.RepoDependencies {
					// 如果项目为创建mr，将isPreChangeItem置为true，后面根据isPreChangeItem判断是否需要将项目写入change_item表
					if rid.GetPreChangeItemType() == change_itempb.PreChangeItemType_PRE_CHANGE_ITEM_TYPE_MR {
						isPreChangeItem = true
						break
					}
					ismain := true
					info := &dev.SCMInfo{
						IsMain:      &ismain,
						GitRepoName: gptr.Of(rid.GitRepoName),
						Revision:    rid.Revision,
					}

					ciP.Branch = rid.Revision
					if ciP.Branch != "" {
						info.PubBase = dev.SCMPubBase_SCM_PUB_BASE_BRANCH
						changeItemSlotValue, ok := changeItemSlotValueMap[uniqKey]
						if ok {
							info.Revision = changeItemSlotValue.IntegrationBranch
							ciP.Branch = changeItemSlotValue.IntegrationBranch
						}
					}
					repoName = rid.GitRepoName
					c.ScmList = append(c.ScmList, info)
				}
			}
			c.DeployDependencies = dds
			if len(bc.ScmDependencies) > 0 {
				c.ArtifactId = bc.ScmDependencies[0].CommitId
			} else if len(bc.RepoDependencies) > 0 {
				c.ArtifactId = bc.RepoDependencies[0].CommitId
			}
			if p.ProjectInfo.Type == dev.ProjectType_PROJECT_TYPE_TCE.String() {
				c.ScmMergeBuildInfo = releaseticketdomain.MergeBuildConfigCdToCi(bc.GetScmMergeBuildInfo())
			}
			ciP.Artifact = append(ciP.Artifact, c)
		}

		if isPreChangeItem {
			continue
		}
		if p.ProjectInfo.Type == dev.ProjectType_PROJECT_TYPE_TCC.String() {
			res = append(res, &change_item.ProjectWithBranchModel{
				Project:                 ciP,
				ControlPlaneBranchModel: make(map[sharedpb.ControlPlane]*branching_model_configpb.BranchingModelConfig),
			})
			continue
		}
		logs.CtxInfo(ctx, "[HandleCdProjectInfo] git GetProject, username: %s, repoName: %s", operator, repoName)
		project, err := a.gitSvc.GetProjectByRepoName(ctx, repoName)
		if err != nil {
			logs.CtxError(ctx, "[HandleCdProjectInfo] GetProject error: %s", err.Error())
			return nil, nil, bits_err.RELEASETICKET.ErrCallCodebaseRepoInfo.PassThrough(err).SetTip("调用 Codebase 接口获取仓库 %s 信息失败", repoName)
		}
		ciP.RepoId = int64(project.GetId())

		res = append(res, &change_item.ProjectWithBranchModel{
			Project:                 ciP,
			ControlPlaneBranchModel: make(map[sharedpb.ControlPlane]*branching_model_configpb.BranchingModelConfig),
		})
	}

	checked := make(map[string]bool)
	for key, creation := range changeItemSlotValueMap {
		uniqBranchKey := fmt.Sprintf("%s/%s", creation.ChangeItem.GetGitRepoName(), creation.IntegrationBranch)
		if checked[uniqBranchKey] {
			continue
		}
		logs.CtxInfo(ctx, "check or create branch, p=%s,payload=%v, refb:= %s, ref:%s", key, creation, creation.IntegrationBranchCheckoutRef, creation.IntegrationBranchCheckoutRefCommit)

		// tbd流程不需要创建保护规则
		creation.NeedProtectBranch = !isTBD
		var checkoutProject *release_ticketpb.Project
		if isHotfix {
			projectT := gslice.Find(ps, func(p *release_ticketpb.Project) bool {
				return p.GetProjectInfo().GetProjectUniqueId() == creation.ChangeItem.GetProjectUniqueId() && p.GetProjectInfo().GetType() == creation.ChangeItem.GetProjectType().String()
			}) //ignore_security_alert SQL_INJECTION
			if projectT.IsOK() {
				logs.CtxInfo(ctx, "命中项目，尝试使用项目的线上最新commit去 checkout, uniq_id=%s, creation=%v", creation.ChangeItem.GetProjectUniqueId(), creation)
				checkoutProject = projectT.Value()
			}
		}
		// hotfix相关功能，将项目的线上分支作为checkout创建分支
		checkoutRef := operatorx.IfThen(creation.IntegrationBranchCheckoutRefCommit == "", creation.IntegrationBranchCheckoutRef, creation.IntegrationBranchCheckoutRefCommit)
		if checkoutProject != nil {
			refs, err := a.releasedCommitManager.GetProjectOnlineBranchCheckoutRef(ctx, &released_commit_manager.CheckoutRefQuery{
				Operator:                   operator,
				ProjectChangeDeployConfigs: []*release_ticketpb.Project{checkoutProject},
			})
			if err != nil {
				logs.CtxError(ctx, "[HandleCdProjectInfo] GetProjectOnlineBranchCheckoutRef error: %w", err)
				return nil, nil, err
			}
			if len(refs) > 0 {
				checkoutRef = refs[0].GetCheckoutRef()
			}
		}
		_, err := a.branchManager.CheckOrCreateBranch(ctx, creation.ChangeItem.GetGitRepoName(), checkoutRef, creation.IntegrationBranch, creation.NeedProtectBranch, nil)
		if err != nil {
			logs.CtxError(ctx, "[HandleCdProjectInfo] CheckOrCreateBranch error: %w", err)
			return nil, nil, err
		}

		//无论是单发布单和还是火车，都主动尝试注册一下仓库app
		go func(creation *change_item.ChangeItemBranchSlotValue) {
			if creation == nil || creation.ChangeItem == nil {
				return
			}
			a.gitSvc.InstallCodebaseApp(ctx, cdutils.GetCodebaseURl(creation.ChangeItem.GetGitRepoName()))
		}(creation)

		checked[uniqBranchKey] = true
	}

	logs.CtxInfo(ctx, "[HandleCdProjectInfo] input: %s, output: %s", common_utils.ToJson(ps), common_utils.ToJson(res))
	return res, changeItemSlotValueMap, nil
}

// UpdateRtChangItems 使用fromRtUpdate字段区分更新来源，true代表发布单侧更新项目，false代表开发任务侧
func (a *rtChangeItemManager) UpdateRtChangItems(ctx context.Context, rt *entity.DBReleaseTicket, controlPanels []string, rtChangeItems []*entity.DBReleaseTicketChangeItem, projectList []*change_item.ProjectWithBranchModel, eventTimestamp uint, fromRtUpdate bool) (*change_item.ChangeItemDiffs, *change_item.BatchChangeItems, error) {
	updateDiffs, batchChangeItems, err := a.ComputeUpdateRtChangItems(ctx, rt, controlPanels, rtChangeItems, projectList, eventTimestamp, fromRtUpdate)
	if err != nil {
		return &change_item.ChangeItemDiffs{}, nil, err
	}
	err = a.BatchRefreshRtChangeItemsInDB(ctx, batchChangeItems)
	if err != nil {
		return &change_item.ChangeItemDiffs{}, nil, err
	}
	if batchChangeItems.NeedUpdateStorageChangeItem {
		err = a.BatchRefreshStorageChangeItems(ctx, rt.ReleaseTicketID, batchChangeItems.RefreshStorageChangeItem)
		if err != nil {
			return &change_item.ChangeItemDiffs{}, nil, err
		}
	}
	return updateDiffs, batchChangeItems, nil
}

func filterGeckoChannelByControlPlane(cp sharedpb.ControlPlane, region paaspb.GeckoControlPlane) bool {
	switch region {
	case paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_CN, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_BOE_CN:
		return cp == sharedpb.ControlPlane_CONTROL_PLANE_CN
	case paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_I18N, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_BOE_I18N, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_TT_ROW, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_US_TTP, paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_EU_TTP:
		return cp == sharedpb.ControlPlane_CONTROL_PLANE_I18N
	default:
		return false
	}
}

func getGeckoDeployTargetChannelItems(channelList []*dev.ChannelItem, cp sharedpb.ControlPlane) []*paaspb.ChannelItem {
	return gslice.FilterMap(channelList, func(item *dev.ChannelItem) (*paaspb.ChannelItem, bool) {
		return &paaspb.ChannelItem{
			Region:              paaspb.GeckoControlPlane(item.GetRegion()),
			GeckoAppId:          uint64(item.GetGeckoAppId()),
			GeckoEnvType:        paaspb.GeckoEnvType(item.GetGeckoEnvType()),
			GeckoDeploymentId:   uint64(item.GetGeckoDeploymentId()),
			GeckoChannelId:      uint64(item.GetGeckoChannelId()),
			Platform:            paaspb.GeckoPlatformType(item.GetPlatform()),
			GeckoAppName:        item.GetGeckoAppName(),
			GeckoDeploymentName: item.GetGeckoDeploymentName(),
			GeckoChannelName:    item.GetGeckoChannelName(),
		}, filterGeckoChannelByControlPlane(cp, paaspb.GeckoControlPlane(item.GetRegion()))
	})
}

func genHybridChannelUniqKey(channel *paaspb.ChannelItem) string {
	return fmt.Sprintf("%d|%d|%d|%d|%d|%d", channel.GetGeckoAppId(), channel.GetGeckoDeploymentId(), channel.GetGeckoChannelId(), channel.GetRegion(), channel.GetGeckoEnvType(), channel.GetPlatform())
}

func mergeHybridChannels(prev, cur []*paaspb.ChannelItem) []*paaspb.ChannelItem {
	res := make([]*paaspb.ChannelItem, 0)
	res = append(res, prev...)
	prevMap := gslice.ToMap(prev, func(item *paaspb.ChannelItem) (string, *paaspb.ChannelItem) {
		return genHybridChannelUniqKey(item), item
	})
	for _, item := range cur {
		if _, ok := prevMap[genHybridChannelUniqKey(item)]; !ok {
			res = append(res, item)
		}
	}

	return res
}

func computeHybridChannelList(ctx context.Context, params ComputeHybridChannelListParams) []*paaspb.ChannelItem {
	var (
		prevChangeItem = params.prevChangeItem
		isNewProject   = params.isNewProject
		newChannelList = params.newChannelList
		isUpdateRt     = params.isUpdateRt
	)
	logs.CtxInfo(ctx, "[computeHybridChannelList] params: %s", common_utils.ToJson(params))

	// 1. 编辑发布单场景，直接以channel入参为准
	if isUpdateRt {
		logs.CtxInfo(ctx, "[computeHybridChannelList] update rt, use new channels.")
		return newChannelList
	}

	// 2. 开发任务合入场景
	// 2.1 新项目，应绑尽绑
	if isNewProject {
		logs.CtxInfo(ctx, "[computeHybridChannelList] isNewProject, should bind channel")
		return newChannelList
	}

	// 2.2 有对应存量 changeitem，且没有绑 channel。
	// 说明是不支持绑channel时的存量数据，这种忽略掉开发任务中的 channel。
	if len(prevChangeItem.GetDeployTarget().GetChannelItems()) == 0 {
		logs.CtxInfo(ctx, "[computeHybridChannelList] prevChangeItem no channel, skip.")
		return []*paaspb.ChannelItem{}
	}

	// 2.3 存量changeitem绑了channel，要把存量 channel 和本次入参的 channel 取并集
	logs.CtxInfo(ctx, "[computeHybridChannelList] dev task, merge prev and current channels.")
	return mergeHybridChannels(prevChangeItem.GetDeployTarget().GetChannelItems(), newChannelList)
}

// todo
// ComputeUpdateRtChangItems 将 change item 更新为最新一次发布清单。最终的 change item 数量为发布清单和控制面的笛卡尔积
func (a *rtChangeItemManager) ComputeUpdateRtChangItems(ctx context.Context, rt *entity.DBReleaseTicket, controlPanels []string, rtChangeItems []*entity.DBReleaseTicketChangeItem, projectList []*change_item.ProjectWithBranchModel, eventTimestamp uint, fromRtUpdate bool) (updateDiffs *change_item.ChangeItemDiffs, batchChangeItems *change_item.BatchChangeItems, err error) {
	logs.CtxInfo(ctx, "[UpdateRtChangItems], rtID: %d, rtChangeItems: %s, projectList: %s, fromRtUpdate: %t", rt.ReleaseTicketID, common_utils.ToJson(rtChangeItems), common_utils.ToJson(projectList), fromRtUpdate)
	defer utils.PanicGuardWithFunc(ctx, func() {
		err = errors.Errorf("panic, log-id: %v", ctxvalues.LogIDDefault(ctx))
	})

	username := cdauth.Username(ctx)
	updateDiffs = &change_item.ChangeItemDiffs{}
	workspaceDeps := make([]*change_itempb.Dependency, 0)

	wf, err := a.workflowConfigHelper.DbToPb(ctx, rt.WorkflowConfig, false, 0)
	if err != nil {
		logs.CtxWarn(ctx, "[UpdateRtChangItems] workflowConfigHelper.DbToPb error: %s", err.Error())
	}
	if wf != nil && wf.GetReleaseTicketConfig().GetDependencyConfig().GetAutoSyncFromSpace() {
		logs.CtxInfo(ctx, "获取空间依赖关系 space %d, sync=%t", rt.WorkspaceID, wf.GetReleaseTicketConfig().GetDependencyConfig().GetAutoSyncFromSpace())
		resp, err := a.configService.ListOnesiteDependency(ctx, int64(rt.WorkspaceID))
		if err != nil {
			return nil, nil, err
		}
		workspaceDeps = change_item.DependencyOnesiteToHagrid(resp.GetDependencies())
	}

	rtDependencies, err := a.changeItemSvc.MergeDependencyItems(ctx, controlPanels, workspaceDeps, rtChangeItems, projectList, fromRtUpdate)

	if err != nil {
		logs.CtxError(ctx, "MergeDependencyItems, but still go on err=%s", err.Error())
	}
	hasDependencyDiff := gslice.Find(rtDependencies.GetFromWorkspaceDiffs(), func(dependency *change_itempb.Dependency) bool {
		if dependency.GetDiffType() == change_itempb.DepDiffType_DEP_DIFF_TYPE_MODIFIED {
			return true
		}
		return false
	})

	// 标记依赖是否发生变更
	updateDiffs.HasItemDependencyChanged = hasDependencyDiff.IsOK()
	rtDependencyMap := gslice.ToMap(rtDependencies.GetDependencies(), func(t *change_itempb.Dependency) (cicd_utils.ProjectUniqKey, *change_itempb.Dependency) {
		return cicd_utils.ProjectUniqKey{
			ProjectUniqueId: t.GetProjectUniqueId(),
			ProjectType:     t.GetProjectType(),
		}, t
	})

	// 要修改的 changItems
	updateChangItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	// 要新增的 changItems
	addChangItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	// Commit 发生变更的 changeItems
	commitUpdateChangeItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	// 依赖仓发生变更的 changeItems
	dependencyRepoVersionUpdateChangeItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	// 合并编译发生变更的 changeItems
	MergeBuildUpdateChangeItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	// 编辑主仓分支/版本，依赖仓版本，项目控制面
	hasUpdatedInfo := false
	updateProName := make([]string, 0)

	workspaceId := rt.WorkspaceID
	supportedComponents, err := a.appCenterSvc.GetBitsReleaseSupportedComponent(ctx, workspaceId)
	if err != nil {
		logs.CtxError(ctx, "GetBitsReleaseSupportedComponent ws=%d, err %w", workspaceId, err)
	}
	logs.CtxInfo(ctx, "GetBitsReleaseSupportedComponent: %s", common_utils.ToJson(supportedComponents))
	rtProjectCpModel := rtprojectdomain.NewProjectCpSupportModel(supportedComponents)
	projectsFromCreateCp := set.NewStringSet() //只有通过扩展控制面创建的项目才会校验是否存在

	//发布清单控制面取开发、发布的并集，移除发布单控制面过滤逻辑
	for _, p := range projectList {
		newControlPlanes := getProjectControlPlanes(p.Project, controlPanels)
		for _, cp := range newControlPlanes {
			controlPlane := sharedpb.ControlPlane(sharedpb.ControlPlane_value[cp])
			// 根据 controlPanel 查找对应的 artifact
			artifact := getArtifactByControlPanel(p.Project, cp) //
			shouldCreateThisCPProject := artifact == nil         //这里根据产物是否问空来判断是否需要补充控制面

			projectSupportThisCp := rtProjectCpModel.IsControlPlaneSupported(sharedpb.ProjectType(p.Project.GetProjectType()), sharedpb.ControlPlane(sharedpb.ControlPlane_value[cp]))
			if shouldCreateThisCPProject && !projectSupportThisCp {
				logs.CtxInfo(ctx, "项目类型 %s 不支持控制面 %s，不生成项目", p.Project.GetProjectType().String(), cp)
				continue
			}

			logs.CtxInfo(ctx, "项目类型 %s 控制面 %s,是否是开发任务传入or编辑输入=%t, projectSupportThisCp=%t",
				p.Project.GetProjectType().String(),
				cp,
				shouldCreateThisCPProject,
				projectSupportThisCp,
			)

			if cp == sharedpb.ControlPlane_CONTROL_PLANE_TTP.String() {
				logs.CtxInfo(ctx, "ttp控制面升级，如果存在在新版控制面，那么老版控制面就不生成")
				hasNewerUSTTP := gslice.Contains(controlPanels, sharedpb.ControlPlane_CONTROL_PLANE_US_TTP.String())
				projectSupportNewUSTTP := rtProjectCpModel.IsControlPlaneSupported(sharedpb.ProjectType(p.Project.GetProjectType()), sharedpb.ControlPlane_CONTROL_PLANE_US_TTP)
				if hasNewerUSTTP && projectSupportNewUSTTP {
					logs.CtxInfo(ctx, "项目类型 %v 支持新版控制面 us-ttp，发布单里包含新版控制面，那么老版控制面不生成", p.Project.GetProjectType().String())
					continue
				}
			}

			var hasCommitDiff, hasDependencyRepoVersionDiff, hasMergeBuildDiff bool
			// 发布清单中已入库的项目
			prevChangeItem, index := slicex.Find(rtChangeItems, func(item *entity.DBReleaseTicketChangeItem) bool {
				return item.ProjectType == p.Project.GetProjectType().String() && item.ProjectUniqueID == p.Project.GetProjectUniqueId() && item.ControlPlane == cp
			}) //ignore_security_alert SQL_INJECTION
			// 9.21 之前的开发任务没有 projectName，用 projectUniqueID 代替
			projectName := p.Project.GetProjectUniqueId()
			if p.Project.GetProjectName() != "" {
				projectName = p.Project.GetProjectName()
			}
			needUpdateChangeItem := &release_ticketpb.ReleaseTicketChangeItem{
				ProjectUniqueId: p.Project.GetProjectUniqueId(),
				ProjectType:     sharedpb.ProjectType(sharedpb.ProjectType_value[p.Project.GetProjectType().String()]),
				ProjectName:     projectName,
				ControlPlane:    sharedpb.ControlPlane(sharedpb.ControlPlane_value[cp]),
				DeployStrategy:  &change_itempb.ChangeItemDeployStrategy{},
				ControlledBy:    change_itempb.ControlledBy_CONTROLLED_BY_DEV_TASK,
				Reviewers:       []string{},
			}
			if index > -1 {
				needUpdateChangeItem, err = a.changeItemSvc.ChangeItemConvertToPB(ctx, prevChangeItem)
				if err != nil {
					return &change_item.ChangeItemDiffs{}, nil, err
				}
			}

			// 更新发布清单携带的一些常规信息
			needUpdateChangeItem.ProjectOwners = gslice.Map(p.Project.ProjectOwnerList, func(owner string) string {
				return strings.Split(owner, "@")[0] //
			})
			needUpdateChangeItem.ArtifactsUpdateTimestamp = uint64(eventTimestamp)

			/*************新增控制面的相关逻辑*****************/
			if shouldCreateThisCPProject {
				// 发布单侧更新项目场景下未找到控制面下的项目，应该删除该项目
				if fromRtUpdate {
					continue
				}

				findArtifactToCopy, copyArtifact := a.changeItemSvc.FindControlPlaneArtifactToCopy(ctx, p.Project, needUpdateChangeItem)
				if !findArtifactToCopy {
					continue
				}

				// 拷贝原对象
				artifact = &multi.Content{}
				_ = copier.Copy(artifact, copyArtifact)
				projectsFromCreateCp.Add(GetProjectUniqueKey(needUpdateChangeItem.GetProjectUniqueId(), needUpdateChangeItem.GetProjectType().String(), needUpdateChangeItem.GetControlPlane().String()))
				if p.Project.GetProjectType().String() == sharedpb.ProjectType_PROJECT_TYPE_TCE.String() {
					devScmList, err := a.ReplaceTCESCMVersion(ctx, artifact, p.Project.GetProjectUniqueId(), sharedpb.ControlPlane(sharedpb.ControlPlane_value[cp]))
					if err != nil {
						logs.CtxError(ctx, "ReplaceTCESCMVersion error，still go on, oncall fix data: %w", err)
					} else {
						logs.CtxInfo(ctx, "ReplaceTCESCMVersion success, devScmList: %s", common_utils.ToJson(devScmList))
						artifact.ScmList = devScmList
					}
					if mergeBuildInfo, err := a.changeItemSvc.GetScmMergeBuildInfo(ctx, artifact); err == nil {
						artifact.ScmMergeBuildInfo = mergeBuildInfo
					} else {
						logs.CtxError(ctx, "GetScmMergeBuildInfo error，still go on, oncall fix data: %w", err)
					}
				} else if p.Project.GetProjectType().String() == sharedpb.ProjectType_PROJECT_TYPE_CRONJOB.String() {
					err = a.ReplaceCronJobSCMList(ctx, artifact, p.Project.GetProjectUniqueId(), sharedpb.ControlPlane(sharedpb.ControlPlane_value[cp]))
					if err != nil {
						logs.CtxError(ctx, "ReplaceCronJobSCMList error，still go on, oncall fix data: %w", err)
					}
				}
			} else {
				// todo: 也可以修正一波 scm 依赖仓数据
			}
			dependencyItems, ok := rtDependencyMap[cicd_utils.ProjectUniqKey{
				ProjectUniqueId: needUpdateChangeItem.ProjectUniqueId,
				ProjectType:     needUpdateChangeItem.ProjectType,
			}]
			if ok {
				needUpdateChangeItem.DependencyItems = dependencyItems.DependencyItems
			}

			/*************计算是否有commit/merge build/dependency diff*****************/
			if index > -1 {
				if needUpdateChangeItem.GetDeployResource().GetType() == change_itempb.ArtifactType_ARTIFACT_TYPE_SCM {
					gslice.ForEach(needUpdateChangeItem.GetDeployResource().GetScmArtifacts(), func(r *change_itempb.SCMArtifact) {
						if r.GetIsMain() {
							if r.PubBase == sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH && r.GetTargetCommitHash() != artifact.GetArtifactId() {
								hasCommitDiff = true
							} else if r.PubBase == sharedpb.ScmPubBase_SCM_PUB_BASE_COMMIT || r.PubBase == sharedpb.ScmPubBase_SCM_PUB_BASE_TAG {
								if len(artifact.ScmList) > 0 {
									hasCommitDiff = !(int64(r.PubBase) == int64(artifact.ScmList[0].PubBase) && r.Revision == artifact.ScmList[0].Revision) //todo 看不懂
								}
							}
						}
					})
					if !releaseticketdomain.MergeBuildIsSameDevRTPb(needUpdateChangeItem, artifact) && fromRtUpdate {
						hasMergeBuildDiff = true
					}

					dbDependencyRepoVersions := gslice.FilterMap(needUpdateChangeItem.GetDeployResource().GetScmArtifacts(), func(sa *change_itempb.SCMArtifact) (string, bool) {
						if !sa.GetIsMain() && sa.GetPubBase() == sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION {
							return fmt.Sprintf("%d", sa.GetScmId()) + sa.GetVersion(), true
						}

						return "", false
					})

					newDependencyRepoVersions := gslice.FilterMap(artifact.ScmList, func(si *dev.SCMInfo) (string, bool) {
						if !si.GetIsMain() && si.GetPubBase() == dev.SCMPubBase_SCM_PUB_BASE_VERSION {
							return fmt.Sprintf("%d", si.GetId()) + si.GetRevision(), true
						}

						return "", false
					})

					if !slicex.ElementsMatch(dbDependencyRepoVersions, newDependencyRepoVersions) {
						hasDependencyRepoVersionDiff = true
					}

				}
			}

			/*************填充deploy target信息*****************/
			// 部署目标更新
			var ttpWebName string
			if p.Project.GetExtra() != nil {
				ttpWebName = p.Project.GetExtra().GetTtpWebName()
			}

			// TODO(jiquan.007): 等"项目名称一致性检测"上线后，去掉这里的 continue 逻辑。
			// 对于创建开发任务时没有勾选 TTP 控制面的 Web 项目，由于没拿到 ttpWebName 信息，暂时过滤掉。注意这是 ttp web 的特殊之处：部署
			// 产物由开发任务的控制面来决定，而不是由发布单的控制面来决定。
			if cp == sharedpb.ControlPlane_CONTROL_PLANE_TTP.String() &&
				p.Project.GetProjectType().String() == sharedpb.ProjectType_PROJECT_TYPE_WEB.String() &&
				ttpWebName == "" {
				continue
			}

			deployTarget := &change_itempb.ChangeItemDeployTarget{
				ProjectUniqueId: p.Project.GetProjectUniqueId(),
				ProjectType:     sharedpb.ProjectType(p.Project.GetProjectType()),
				ProjectName:     projectName,
				TtpWebName:      ttpWebName,
			}
			if p.Project.GetProjectType().String() == sharedpb.ProjectType_PROJECT_TYPE_HYBRID.String() {
				channelItems := getGeckoDeployTargetChannelItems(p.Project.GetChannelItems(), controlPlane)
				logs.CtxInfo(ctx, "current channel list: %s", common_utils.ToJson(channelItems))
				computedChannelItems := computeHybridChannelList(ctx, ComputeHybridChannelListParams{
					prevChangeItem: needUpdateChangeItem,
					isNewProject:   index == -1,
					newChannelList: channelItems,
					isUpdateRt:     fromRtUpdate,
				})
				logs.CtxInfo(ctx, "computed channel list: %s", common_utils.ToJson(computedChannelItems))
				deployTarget.ChannelItems = computedChannelItems
			}
			_, err = a.rtProjectSvc.
				GetRtProjectByProjectType(ctx, sharedpb.ProjectType(p.Project.GetProjectType())).
				FillTargetServiceMeta(ctx, controlPlane, deployTarget, username)
			if err != nil {
				logs.CtxError(ctx, "FillTargetServiceMeta, target=%s, error: %s", common_utils.ToJson(deployTarget), err.Error())
				continue
			}

			needUpdateChangeItem.DeployTarget = deployTarget

			/*************填充deploy resource信息*****************/
			logs.CtxInfo(ctx, "[自定义项目测试]:[project:]%s,[artifact:]%s,index:%d", common_utils.ToJson(p), common_utils.ToJson(artifact), index)
			// 部署制品更新
			deployResource := &change_itempb.ChangeItemArtifact{}
			// Git类型自定义项目，无ScmList字段，用其他字段构造ScmArtifacts
			if p.Project.GetProjectType().String() == sharedpb.ProjectType_PROJECT_TYPE_CUSTOM.String() && len(artifact.RepoList) != 0 {
				deployResource = &change_itempb.ChangeItemArtifact{
					Type:         change_itempb.ArtifactType(artifact.ContentType),
					ScmArtifacts: []*change_itempb.SCMArtifact{},
				}
				for _, repo := range artifact.RepoList {
					scm := &change_itempb.SCMArtifact{
						IsMain:           true,
						Revision:         repo.Branch,
						GitRepoName:      repo.GitRepoName,
						TargetCommitHash: repo.CommitId,
						GitRepoId:        artifact.GetRepoId(),
					}
					if scm.Revision != "" {
						scm.PubBase = sharedpb.ScmPubBase_SCM_PUB_BASE_BRANCH
					}
					// 开发任务传入的repo.Branch是开发分支，这里实际应该是集成分支，所以基于分支模型重新取一遍。
					if msgBranch := choose.If(artifact.GetBranch() != "", artifact.GetBranch(), p.Project.GetBranch()); scm.GetIsMain() && msgBranch != "" {
						scm.Revision = msgBranch
					}
					// 开发任务传入的数据把Commit放到ArtifactId中了
					if scm.TargetCommitHash == "" {
						scm.TargetCommitHash = artifact.GetArtifactId()
					}
					deployResource.ScmArtifacts = append(deployResource.ScmArtifacts, scm)
				}
			} else if p.Project.GetProjectType().String() == sharedpb.ProjectType_PROJECT_TYPE_TCC.String() {
				deployResource = &change_itempb.ChangeItemArtifact{
					Type:         change_itempb.ArtifactType(artifact.ContentType),
					TccArtifacts: operatorx.IfThen(index > -1, needUpdateChangeItem.GetDeployResource().GetTccArtifacts(), &change_itempb.TCCArtifact{}),
				}
			} else {
				deployResource = &change_itempb.ChangeItemArtifact{
					Type:              change_itempb.ArtifactType(artifact.ContentType),
					ScmMergeBuildInfo: choose.If(fromRtUpdate || index == -1 || needUpdateChangeItem.GetDeployResource().GetScmMergeBuildInfo() == nil, releaseticketdomain.MergeBuildConfigCiToCD(artifact.GetScmMergeBuildInfo()), needUpdateChangeItem.GetDeployResource().GetScmMergeBuildInfo()),
					ScmArtifacts: gslice.Map(artifact.ScmList, func(scm *dev.SCMInfo) *change_itempb.SCMArtifact {
						targetCommitHash := artifact.GetArtifactId()
						version := ""
						revision := ""
						switch scm.PubBase {
						case dev.SCMPubBase_SCM_PUB_BASE_VERSION:
							version = scm.Revision
							if gptr.Indirect(scm.UseLatest) && version == "" {
								version = "somebody_never_will_use_this_value_for_version_always_latest"
							}
						case dev.SCMPubBase_SCM_PUB_BASE_BRANCH:
							revision = scm.Revision
							// 12.12线上数据主仓 *dev.SCMInfo.revision是开发分支（不可用）。*multi.Content.branch 是目标分支。
							// 新功能上线后 branch 和 version 方式*multi.Content.branch 为空。
							// 故兼容处理为主仓*multi.Content.branch存在，优先使用；否则用*dev.SCMInfo.revision
							// 主仓的兼容；适用于发布单服务先发布，集成区后发布
							if msgBranch := choose.If(artifact.GetBranch() != "", artifact.GetBranch(), p.Project.GetBranch()); scm.GetIsMain() && msgBranch != "" {
								revision = msgBranch
							}
						case dev.SCMPubBase_SCM_PUB_BASE_COMMIT:
							revision = scm.Revision
						case dev.SCMPubBase_SCM_PUB_BASE_TAG:
							revision = scm.Revision
							targetCommitHash = scm.CommitId
						}
						var gitRepoID int64
						if scm.GetIsMain() || scm.PubBase == dev.SCMPubBase_SCM_PUB_BASE_BRANCH {
							gitRepoID = scm.GetGitRepoId()
							if scm.GetIsMain() && gitRepoID == 0 {
								logs.CtxInfo(ctx, "【兼容】分控制面的仓库 id 若无，则直接读外层的仓库 id")
								// 对于cronjob，各控制面的主仓可能不同，不应该读p.Project.RepoId，但实际内层artifact.RepoId始终能取到
								gitRepoID = operatorx.IfThen(artifact.GetRepoId() != 0, artifact.GetRepoId(), p.Project.GetRepoId())
							}
						}

						return &change_itempb.SCMArtifact{
							IsMain:           scm.GetIsMain(),
							PubBase:          sharedpb.ScmPubBase(scm.PubBase),
							ScmId:            int64(scm.Id),
							ScmName:          scm.Name,
							Version:          version,
							Revision:         revision,
							GitRepoName:      scm.GetGitRepoName(),
							GitRepoId:        gitRepoID,
							TargetCommitHash: targetCommitHash,
							DevMode:          sharedpb.DevMode(scm.GetDevMode()),
							UseLatest:        gptr.Indirect(scm.UseLatest),
						}
					}),
				}
			}
			if index > -1 && (needUpdateChangeItem.GetDeployResource().GetType() != deployResource.GetType() ||
				!isScmArtifactEqual(needUpdateChangeItem.GetDeployResource().GetScmArtifacts(), deployResource.GetScmArtifacts())) ||
				!releaseticketdomain.MergerBuildIsSame(needUpdateChangeItem.GetDeployResource().GetScmMergeBuildInfo(), deployResource.GetScmMergeBuildInfo()) {
				hasUpdatedInfo = true
				updateProName = append(updateProName, projectName)
			}
			needUpdateChangeItem.DeployResource = deployResource
			needUpdateChangeItem.BranchingModelConfig = p.ControlPlaneBranchModel[controlPlane]

			needUpdateChangeItemDB, err := a.changeItemSvc.ChangeItemConvertToDB(ctx, needUpdateChangeItem, rt.ReleaseTicketID)
			if err != nil {
				return &change_item.ChangeItemDiffs{}, nil, err
			}
			/*************信息汇总*****************/
			if index > -1 {
				if hasCommitDiff {
					commitUpdateChangeItems = append(commitUpdateChangeItems, needUpdateChangeItemDB)
				}

				if hasDependencyRepoVersionDiff {
					dependencyRepoVersionUpdateChangeItems = append(dependencyRepoVersionUpdateChangeItems, needUpdateChangeItemDB)
				}
				if hasMergeBuildDiff {
					MergeBuildUpdateChangeItems = append(MergeBuildUpdateChangeItems, needUpdateChangeItemDB)
				}

				needUpdateChangeItemDB.ID = prevChangeItem.ID //
				updateChangItems = append(updateChangItems, needUpdateChangeItemDB)
			} else {
				addChangItems = append(addChangItems, needUpdateChangeItemDB)
			}
		}
	}
	/*************最终的信息汇总*****************/
	// 再次检测服务是否存在，要删除 add modify slice 中的 changItems
	addChangItems, _, err = a.RemoveChangeItemsWhenServiceNotExist(ctx, addChangItems, projectsFromCreateCp)
	if err != nil {
		return &change_item.ChangeItemDiffs{}, nil, err
	}

	removeChangeItems, err := getRemovedChangeItems(ctx, fromRtUpdate, rt.ReleaseTicketID, rtChangeItems, updateChangItems) //
	if err != nil {
		return &change_item.ChangeItemDiffs{}, nil, err
	}

	// 标记发布清单有新增 changeitem
	updateDiffs.HasItemAdded = len(addChangItems) > 0
	// 标记发布清单有移除的 changeitem
	updateDiffs.HasItemRemoved = len(removeChangeItems) > 0

	printItems := slicex.Map(removeChangeItems, func(from *entity.DBReleaseTicketChangeItem) string {
		return from.ProjectType + "|" + from.ProjectUniqueID + "|" + from.ControlPlane
	})
	logs.CtxInfo(ctx, "update rt=%d artifact list delete: %s", rt.ReleaseTicketID, strings.Join(printItems, ","))

	// 新增控制面的情况 todo
	if len(addChangItems) > 0 && len(updateChangItems) > 0 {
		for _, addItem := range addChangItems {
			preCP := gslice.FilterMap(updateChangItems, func(item *entity.DBReleaseTicketChangeItem) (string, bool) {
				return item.ControlPlane, item.ProjectType == addItem.ProjectType && item.ProjectUniqueID == addItem.ProjectUniqueID
			})
			if len(preCP) > 0 && !gslice.Contains(preCP, addItem.ControlPlane) {
				hasUpdatedInfo = true
				var dt change_itempb.ChangeItemDeployTarget
				errT := json.UnmarshalString(addItem.DeployTarget, &dt)
				if errT != nil {
					logs.CtxError(ctx, "Unmarshal deploy target err:%v", errT)
					continue
				}
				updateProName = append(updateProName, dt.ProjectName)
			}
		}
	}
	// 删除控制面的情况 todo
	if len(removeChangeItems) > 0 && len(updateChangItems) > 0 {
		for _, removeItem := range removeChangeItems {
			preCP := gslice.FilterMap(updateChangItems, func(item *entity.DBReleaseTicketChangeItem) (string, bool) {
				return item.ControlPlane, item.ProjectType == removeItem.ProjectType && item.ProjectUniqueID == removeItem.ProjectUniqueID
			})
			if len(preCP) > 0 && !gslice.Contains(preCP, removeItem.ControlPlane) {
				// 不支持删除 TCC 项目控制面
				if removeItem.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_TCC.String() {
					logs.CtxError(ctx, "unsupport delete tcc control plane: %s", common_utils.ToJson(removeItem))
					return &change_item.ChangeItemDiffs{}, nil, bits_err.RELEASETICKET.ErrUnSupportDeleteTccControlPlane
				}

				hasUpdatedInfo = true
				var dt change_itempb.ChangeItemDeployTarget
				errT := json.UnmarshalString(removeItem.DeployTarget, &dt)
				if errT != nil {
					logs.CtxError(ctx, "Unmarshal deploy target err:%w", errT)
					continue
				}
				updateProName = append(updateProName, dt.ProjectName)
			}
		}
	}
	batchChangeItems = &change_item.BatchChangeItems{
		ItemsAdded:                         addChangItems,
		ItemsModified:                      updateChangItems,
		ItemsRemoved:                       removeChangeItems,
		ItemsModifiedCommit:                commitUpdateChangeItems,
		ItemsModifiedDependencyRepoVersion: dependencyRepoVersionUpdateChangeItems,
		ItemsModifiedBuildMerge:            MergeBuildUpdateChangeItems,
		HasUpdatedInfo:                     hasUpdatedInfo,
		UpdateProjectName:                  updateProName,
	}
	// 更新暂存区psm版本.暂时只支持 TCC
	// rpc 调用避免产生长事务
	// 11.22 解决：将计算与更新拆开，更新的时候为保证安全先执行非事务操作，成功后再执行事务
	if rtProjectCpModel.IsProjectTypeSupported(sharedpb.ProjectType_PROJECT_TYPE_TCC) {
		refreshStorageChangeItem, err := a.ComputeBatchRefreshStorageChangeItems(ctx, rt, batchChangeItems)
		if err != nil {
			return &change_item.ChangeItemDiffs{}, nil, err
		}
		batchChangeItems.RefreshStorageChangeItem = refreshStorageChangeItem
		if len(refreshStorageChangeItem) > 0 {
			batchChangeItems.NeedUpdateStorageChangeItem = true
		}
	}

	logs.CtxInfo(ctx, "[自定义项目测试]:[待更新的changeItem:]%s", common_utils.ToJson(batchChangeItems))
	return updateDiffs, batchChangeItems, nil
}

// 控制面取开发任务和发布单控制面并集
func getProjectControlPlanes(p *multi.ProjectInfo, controlPlanes []string) []string {
	newCps := make([]string, 0)
	cpMap := make(map[string]struct{})
	for _, cp := range controlPlanes {
		cpMap[cp] = struct{}{}
		newCps = append(newCps, cp)
	}

	for _, artifact := range p.GetArtifact() {
		key := artifact.GetControlPanel().String()
		if _, ok := cpMap[key]; !ok {
			newCps = append(newCps, key)
		}
	}

	return newCps
}

func getArtifactByControlPanel(p *multi.ProjectInfo, controlPlane string) *multi.Content {
	artifact, _ := slicex.Find(p.Artifact, func(artifact *multi.Content) bool {
		return artifact.ControlPanel.String() == controlPlane
	}) //ignore_security_alert_wait_for_fix SQL_INJECTION
	return artifact
}

func GetProjectUniqueKey(ProjectUniqueId string, projectType, cp string) string {
	return fmt.Sprintf("%s-%s-%s", ProjectUniqueId, projectType, cp)
}

func (a *rtChangeItemManager) ReplaceTCESCMVersion(ctx context.Context, artifact *multi.Content, projectUniqueID string, cp sharedpb.ControlPlane) ([]*dev.SCMInfo, error) {
	mainScmInfo := gslice.Find(artifact.GetScmList(), func(info *dev.SCMInfo) bool {
		return info.GetIsMain()
	}).Value()
	mainScm := mainScmInfo.GetName()
	if mainScm == "" {
		logs.CtxError(ctx, "连主 scm 都解析不出来，比较蛋疼了")
	}

	logs.CtxWarn(ctx, "try to get artifact with control panel=%s, main_scm=%s", cp, mainScm)
	scmList, err := rtproject.NewTceProject().GetScmBuildList(ctx, cp, &change_itempb.ChangeItemDeployTarget{
		ProjectUniqueId: projectUniqueID,
		ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
		ProjectName:     projectUniqueID,
	}, mainScm)
	if err != nil {
		return nil, err
	}

	logs.CtxInfo(ctx, "trans to artifact. control panel=%s, scmList=%s", cp, common_utils.ToJson(scmList))

	devScmList := make([]*dev.SCMInfo, 0)
	var getDevScmRevision = func(fromRemote *change_itempb.SCMArtifact, fromCopy *dev.SCMInfo) string {
		if fromRemote.GetPubBase() != sharedpb.ScmPubBase_SCM_PUB_BASE_VERSION {
			return fromRemote.GetRevision()
		} else {
			if fromRemote.GetVersion() == "" && fromCopy != nil {
				// 如果远端没编译过，那么使用复制的版本。
				// 在发布阶段，用户会再次确认版本是否正确，因此使用复制的版本是安全的。
				return fromCopy.GetRevision()
			}
			return fromRemote.GetVersion()
		}
	}
	for _, remoteScm := range scmList {
		copyFromScmInfo := gslice.Find(artifact.GetScmList(), func(info *dev.SCMInfo) bool {
			return info.GetName() == remoteScm.GetScmName()
		}).Value()

		pubFromRemote, _ := dev.SCMPubBaseFromString(remoteScm.GetPubBase().String())
		revisionFromRemote := getDevScmRevision(remoteScm, copyFromScmInfo)

		// 主仓强制改成发布单配置的
		revision := choose.If(remoteScm.GetIsMain(), mainScmInfo.GetRevision(), revisionFromRemote)
		pubBase := choose.If(remoteScm.GetIsMain(), mainScmInfo.GetPubBase(), pubFromRemote)

		newA := &dev.SCMInfo{
			Id:          int32(remoteScm.GetScmId()),
			Name:        remoteScm.GetScmName(),
			IsMain:      lo.ToPtr(remoteScm.GetIsMain()),
			PubBase:     pubBase,
			Revision:    revision,
			GitRepoName: choose.If(remoteScm.GetIsMain(), mainScmInfo.GitRepoName, lo.ToPtr(remoteScm.GetGitRepoName())),
			CommitId:    choose.If(remoteScm.GetIsMain(), mainScmInfo.GetCommitId(), remoteScm.GetTargetCommitHash()),
		}

		// 如果copy artifact 选择了使用线上最新，扩展时也修复成使用线上最新
		if !remoteScm.GetIsMain() && copyFromScmInfo.GetUseLatest() {
			newA.Revision = "somebody_never_will_use_this_value_for_version_always_latest"
			newA.UseLatest = lo.ToPtr(true)
		}
		devScmList = append(devScmList, newA)
	}

	logs.CtxInfo(ctx, "trans to dev scm list. control panel=%s, scmList=%s", cp, common_utils.ToJson(devScmList))
	return devScmList, nil
}

// ReplaceCronJobSCMList 如果Artifact中没有指明SCM列表，就拉每个SCM仓库的最新线上版本为填充
func (a *rtChangeItemManager) ReplaceCronJobSCMList(ctx context.Context, artifact *multi.Content, projectUniqueID string, cp sharedpb.ControlPlane) (err error) {
	resp, err := a.rtProjectSvc.GetRtProjectByProjectType(ctx, sharedpb.ProjectType_PROJECT_TYPE_CRONJOB).GetProjectOnlineScmVersionInfo(ctx, &projectpb.GetProjectScmVersionInfoReq{
		ProjectUniqueId: projectUniqueID,
		ControlPlane:    cp,
		ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_CRONJOB,
	})
	if err != nil {
		logs.CtxError(ctx, "[FillInCronJOBSCMList] etProjectOnlineScmVersionInfo err: %s", err.Error())
		return
	}
	//存在就覆盖版本
	for i := range artifact.ScmList {
		if scm, index := slicex.Find(resp.ScmVerionInfos, func(v *projectpb.ScmVerionInfo) bool {
			return strconv.FormatInt(int64(artifact.ScmList[i].Id), 10) == v.ScmId
		}); index > -1 && scm != nil {
			logs.CtxInfo(ctx, "[ReplaceCronJobSCMList] rewrite,scm id=%d, version=%s", artifact.ScmList[i].Id, scm.Version)
			artifact.ScmList[i].Revision = scm.Version
		}
	}
	//不存在就添加
	for i, scmInfo := range resp.ScmVerionInfos {
		scmID, _ := strconv.ParseInt(scmInfo.ScmId, 10, 64)
		if scm, index := slicex.Find(artifact.ScmList, func(v *dev.SCMInfo) bool {
			return scmID == int64(v.Id)
		}); index > -1 && scm != nil {
			logs.CtxInfo(ctx, "[ReplaceCronJobSCMList] has exist,scm id=%d, version=%s", scmID, scmInfo.Version)
			continue
		}
		artifact.ScmList = append(artifact.ScmList, &dev.SCMInfo{
			Id:       int32(scmID),
			Name:     scmInfo.Name,
			IsMain:   lo.ToPtr(i == 0), // cronjob有多主仓，这里随机选择第一个仓库为主仓
			PubBase:  dev.SCMPubBase_SCM_PUB_BASE_VERSION,
			Revision: scmInfo.Version,
		})
	}
	return
}

func isScmArtifactEqual(preItem, newItem []*change_itempb.SCMArtifact) bool {
	for _, item := range preItem {
		newI := gslice.Find(newItem, func(artifact *change_itempb.SCMArtifact) bool {
			return artifact.GetScmId() == item.GetScmId()
		})
		if !newI.IsOK() {
			continue
		}
		updateItem := newI.Value()
		if item.GetPubBase() != updateItem.GetPubBase() {
			return false
		}
		if item.GetVersion() != updateItem.GetVersion() {
			return false
		}
		if item.GetRevision() != updateItem.GetRevision() {
			return false
		}
		if item.GetTargetCommitHash() != updateItem.GetTargetCommitHash() {
			return false
		}
	}
	return true
}

// Todo: 入参使用 pb 结构
func (a *rtChangeItemManager) RemoveChangeItemsWhenServiceNotExist(ctx context.Context,
	changeItems []*entity.DBReleaseTicketChangeItem, projectsFromCreateCp set.StringSet,
) ([]*entity.DBReleaseTicketChangeItem, []*entity.DBReleaseTicketChangeItem, error) {
	username := cdauth.Username(ctx)
	if username != "" {
		_, err := cdutils.GetOrSetUserJWTContext(ctx, username)
		if err != nil {
			// 提前获取 jwt，避免下游并发获取 jwt
			logs.CtxWarn(ctx, "[UpdateRtChangItems] GetOrSetUserJWTContext err: %s", err.Error())
		}
	}
	logs.CtxInfo(ctx, "[RemoveChangeItemsWhenServiceNotExist] projectsFromCreateCp: %s", common_utils.ToJson(projectsFromCreateCp))
	cleanedChangeItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	removedChangeItems := make([]*entity.DBReleaseTicketChangeItem, 0)
	var mutex sync.Mutex
	errorChan := make(chan error, len(changeItems))
	var wg sync.WaitGroup
	for _, tchangeItem := range changeItems {
		if !projectsFromCreateCp.Contains(GetProjectUniqueKey(tchangeItem.ProjectUniqueID, tchangeItem.ProjectType, tchangeItem.ControlPlane)) {
			cleanedChangeItems = append(cleanedChangeItems, tchangeItem)
			continue //非扩展出的控制面的变更项，不做处理默认存在
		}
		wg.Add(1)
		changeItem := tchangeItem
		gopool.Go(func() {
			defer wg.Done()
			if changeItem.DeployTarget == "" {
				removedChangeItems = append(removedChangeItems, changeItem)
				return
			}
			deployTarget := &change_itempb.ChangeItemDeployTarget{}
			err := cdutils.PBUnmarshaler.Unmarshal([]byte(changeItem.DeployTarget), deployTarget)
			if err != nil {
				logs.CtxError(ctx, "failed to unmarshal changeItem.DeployTarget to deployTarget: %s", err.Error())
				errorChan <- err
			}

			exist, err := a.rtProjectSvc.
				GetRtProjectByProjectType(ctx, sharedpb.ProjectType(sharedpb.ProjectType_value[changeItem.ProjectType])).
				IsTargetServiceExist(ctx, sharedpb.ControlPlane(sharedpb.ControlPlane_value[changeItem.ControlPlane]), deployTarget, username)

			if err != nil {
				logs.CtxError(ctx, "control plane=%s, type=%s, id=%s deploy target service detect err %s", changeItem.ControlPlane, changeItem.ProjectType, changeItem.ProjectUniqueID, err.Error())
			}
			mutex.Lock()
			defer mutex.Unlock()
			// 存在，则保留
			if exist {
				cleanedChangeItems = append(cleanedChangeItems, changeItem)
			} else {
				// 不存在，则丢弃
				logs.CtxInfo(ctx, "control plane=%s, type=%s, id=%s, deploy target service not found, will be removed from db", changeItem.ControlPlane, changeItem.ProjectType, changeItem.ProjectUniqueID)
				removedChangeItems = append(removedChangeItems, changeItem)
			}
		})
	}
	wg.Wait()
	close(errorChan)
	for e := range errorChan {
		logs.CtxError(ctx, e.Error())
		return nil, nil, erri.Error(e)
	}
	return cleanedChangeItems, removedChangeItems, nil
}

// getRemovedChangeItems. 发布单产物更新时，需要去标记当前产物全集 rtChangeItems 里，哪些产物是应该被移除的。有两种场景下会标记移除：1.用户编辑发布单时，删除了项目；2.对应项目在 tce 里查不到相应服务
func getRemovedChangeItems(ctx context.Context, fromRtUpdate bool, rtID uint64,
	rtChangeItems []*entity.DBReleaseTicketChangeItem, updateChangeItems []*entity.DBReleaseTicketChangeItem,
) ([]*entity.DBReleaseTicketChangeItem, error) {
	var toBeRemovedChangeItems []*entity.DBReleaseTicketChangeItem

	// changeitem 不存在于新的交付清单
	toBeRemovedChangeItems = slicex.Filter(rtChangeItems, func(item *entity.DBReleaseTicketChangeItem) bool {
		// 过滤 rtChangeItems 中不在 updateChangItems 的
		return slicex.All(updateChangeItems, func(u *entity.DBReleaseTicketChangeItem) bool {
			return !(u.ProjectType == item.ProjectType && u.ProjectUniqueID == item.ProjectUniqueID && u.ControlPlane == item.ControlPlane)
		})
	})
	return toBeRemovedChangeItems, nil
}

func (a *rtChangeItemManager) ComputeBatchRefreshStorageChangeItems(ctx context.Context, rt *entity.DBReleaseTicket, batchChangeItems *change_item.BatchChangeItems) ([]*storage_versionpb.WorkflowPsmItem, error) {
	logs.CtxInfo(ctx, "ComputeBatchRefreshStorageChangeItems")
	// 非单发布单，跳过；如果此时发布单记录没插入，直接跳过
	if rt == nil || !domain.IsRtWithoutAssociateDevTask(rt) {
		return nil, nil
	}
	// 注意保证这里一定要是全量的 changeitem 后面的逻辑才正确
	rtChangeitems := make([]*entity.DBReleaseTicketChangeItem, 0)
	rtChangeitems = append(rtChangeitems, batchChangeItems.ItemsAdded...)
	rtChangeitems = append(rtChangeitems, batchChangeItems.ItemsModified...)

	// 当前全量的 TCC 项目
	l := make([]*storage_versionpb.WorkflowPsmItem, 0)
	tccRtChangeitems := gslice.Filter(rtChangeitems, func(item *entity.DBReleaseTicketChangeItem) bool {
		return item.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_TCC.String()
	})
	tccCiMap := gslice.GroupBy(tccRtChangeitems, func(item *entity.DBReleaseTicketChangeItem) string {
		return item.ProjectUniqueID
	})
	for psm, val := range tccCiMap {
		cps := gslice.Map(val, func(item *entity.DBReleaseTicketChangeItem) sharedpb2.TccControlPlane {
			return cdutils.TransControlPlaneToSStorageTCCControlPlane(sharedpb.ControlPlane(sharedpb.ControlPlane_value[item.ControlPlane]))
		})
		l = append(l, &storage_versionpb.WorkflowPsmItem{
			Psm:           psm,
			ControlPlanes: cps,
		})
	}
	logs.CtxInfo(ctx, "[ComputeBatchRefreshStorageChangeItems] tcc psm items: %s", common_utils.ToJson(l))
	return l, nil
}

func (a *rtChangeItemManager) BatchRefreshStorageChangeItems(ctx context.Context, rtID uint64, items []*storage_versionpb.WorkflowPsmItem) error {
	_, err := storage_rpc.GetClient().UpsertTccPsmVersion(ctx, &storage_versionpb.UpsertTccPsmVersionReq{
		WorkflowType:     sharedpb2.WorkflowType_WORKFLOW_TYPE_RELEASE_TICKET,
		WorkflowUniqueId: rtID,
		Creator:          cdauth.Username(ctx),
		PsmItems:         items,
	})
	if err != nil {
		logs.CtxInfo(ctx, "[BatchRefreshStorageChangeItems] failed to upsert tcc psm version: %v", err)
		return err
	}
	return nil
}

func (a *rtChangeItemManager) BatchRefreshRtChangeItemsInDB(ctx context.Context, batchChangeItems *change_item.BatchChangeItems) error {
	if len(batchChangeItems.ItemsRemoved) > 0 {
		err := a.changeItemDao.BatchDelete(ctx, slicex.Map(batchChangeItems.ItemsRemoved, func(item *entity.DBReleaseTicketChangeItem) uint64 {
			return uint64(item.ID)
		}))
		if err != nil {
			return bits_err.RELEASETICKET.ErrDBErr.PassThrough(err)
		}
	}
	if len(batchChangeItems.ItemsModified) > 0 {
		err := a.changeItemDao.BatchUpdate(ctx, batchChangeItems.ItemsModified)
		if err != nil {
			return bits_err.RELEASETICKET.ErrDBErr.PassThrough(err)
		}
	}
	if len(batchChangeItems.ItemsAdded) > 0 {
		err := a.changeItemDao.BatchInsert(ctx, batchChangeItems.ItemsAdded)
		if err != nil {
			return bits_err.RELEASETICKET.ErrDBErr.PassThrough(err)
		}
	}
	logs.CtxInfo(ctx, "BatchRefreshRtChangeItemsInDB success")
	return nil
}
