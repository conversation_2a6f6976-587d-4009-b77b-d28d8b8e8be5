package rt_update_manager

import (
	"context"
	"time"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_mq_reporter"
	"code.byted.org/devinfra/hagrid/app/cdrpc/kitex_gen/bytedance/bits/config_service"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/gopkg/logs"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gvalue"
)

func (h *rtUpdateManager) PlanTimeSendMqEvent(ctx context.Context, releaseTicket *entity.DBReleaseTicket, req *release_ticketpb.UpdateReleaseTicketReq, updatePlanTimeInfo PlanTimeUpdateInfo) {
	// 发送计划时间更新的mq
	wfconf, err := h.workflowConfigHelper.DbToPb(ctx, releaseTicket.WorkflowConfig, false, releaseTicket.WorkspaceID)
	if err != nil {
		logs.CtxError(ctx, "[PlanTimeSendMqEvent] get workflow config error")
		return
	}
	if updatePlanTimeInfo.StartIntegrationTimeChanged && h.workflowConfigHelper.IsStartIntegrationByPlanTime(wfconf) {
		h.registerReleaseTicketTimer(ctx, releaseTicket.ReleaseTicketID, rt_mq_reporter.MsgType_StartIntegration, choose.If(gvalue.IsZero(req.GetStartIntegrationAt()), time.Time{}, time.Unix(req.StartIntegrationAt, 0)))
	}
	if updatePlanTimeInfo.EndIntegrationTimeChanged {
		notificationConfig, err := h.configServiceSvc.GetOnesiteSpaceNotificationConfig(ctx, int64(releaseTicket.WorkspaceID), config_service.OnesiteSpaceNotificationEventName_urgeToMerge.String())
		if err != nil {
			logs.CtxError(ctx, "GetOnesiteSpaceNotificationConfig err: %s", err.Error())
		}
		// 定时催促通知开启
		if notificationConfig.GetConfig().GetOpen() {
			for _, seconds := range notificationConfig.GetConfig().GetNotify().GetTriggerSeconds() {
				urgeTime := time.Unix(req.EndIntegrationAt, 0).Add(time.Duration(seconds) * time.Second)
				logs.CtxInfo(ctx, "rt.EndIntegrationAt=%d, urgeTime=%v", req.EndIntegrationAt, urgeTime)
				h.registerReleaseTicketTimer(ctx, releaseTicket.ReleaseTicketID, rt_mq_reporter.MsgType_UrgeToMerge, urgeTime)
			}
		}
		if h.workflowConfigHelper.IsFinishIntegrationByPlanTime(wfconf) {
			h.registerReleaseTicketTimer(ctx, releaseTicket.ReleaseTicketID, rt_mq_reporter.MsgType_EndIntegration, choose.If(gvalue.IsZero(req.GetEndIntegrationAt()), time.Time{}, time.Unix(req.EndIntegrationAt, 0)))
		}
	}
}

// 注册自动流转的延迟队列
func (h *rtUpdateManager) registerReleaseTicketTimer(ctx context.Context, rtID uint64, msgType rt_mq_reporter.MsgType, planTime time.Time) {
	logs.CtxInfo(ctx, "async registerReleaseTicketTimer, rtID：%d, timestamp: %d", rtID, planTime.Unix())
	if !planTime.IsZero() {
		h.rtMqReporter.SendPlanTimeMQ(ctx, &rt_mq_reporter.PlanTimeMQMsg{
			MsgType:         msgType,
			ReleaseTicketID: rtID,
			TimeStamp:       planTime.Unix(),
			UserName:        cdauth.Username(ctx),
		})
	}
}
