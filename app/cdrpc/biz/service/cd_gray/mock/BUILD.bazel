load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["service_mock.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/cd_gray/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/cd/gray:gray_go_proto",
        "//idls/byted/devinfra/cd/rt_flow_execution:rt_flow_execution_go_proto",
        "//idls/byted/devinfra/cd/team_flow_config:team_flow_config_go_proto",
        "@com_github_golang_mock//gomock",
    ],
)
