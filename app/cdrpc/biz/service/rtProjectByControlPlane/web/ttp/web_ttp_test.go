package ttp

import (
	"context"
	"strconv"
	"testing"

	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/crossborderpb"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/base_project"
)

func Test_TTP_GetService(t *testing.T) {
	ctx := context.Background()

	t.Run("code not 0", func(t *testing.T) {
		defer mockey.Mock((*crossborder.CrossborderSDK).GetWebProjectInfo).Return(&crossborderpb.ResourceGetWebProjectInfoResp{
			Code: 400001,
		}, nil).Build().UnPatch()
		svc := &WebLagacyTTPProject{
			CrossborderSDK: crossborder.NewCrossborderSDK(),
		}
		_, _, err := svc.GetService(ctx, &base_project.GetServiceReq{
			ProjectUniqueId: "1",
			ProjectName:     "ac",
		})
		assert.Error(t, err)
	})

	t.Run("get service exist", func(t *testing.T) {
		defer mockey.Mock((*crossborder.CrossborderSDK).GetWebProjectInfo).Return(&crossborderpb.ResourceGetWebProjectInfoResp{
			Data: &crossborderpb.WebProjectInfoData{
				Data: []*crossborderpb.GoofyData{
					{
						Region:        strconv.FormatInt(int64(paaspb.GoofyDeployRegion_GOOFY_DEPLOY_REGION_US_TTP), 10),
						GoofyDeployId: 60478,
					},
				},
			},
		}, nil).Build().UnPatch()
		svc := &WebLagacyTTPProject{
			CrossborderSDK: crossborder.NewCrossborderSDK(),
		}
		_, ex, err := svc.GetService(ctx, &base_project.GetServiceReq{
			ProjectUniqueId: "1",
			ProjectName:     "ac",
		})
		assert.NoError(t, err)
		assert.Equal(t, ex, true)
	})
}

func Test_TTP_GetDefaultChangeItemDeployStrategy(t *testing.T) {
	ctx := context.Background()

	t.Run("fixed default", func(t *testing.T) {
		svc := &WebLagacyTTPProject{}
		strategy, err := svc.GetDefaultChangeItemDeployStrategy(ctx, &release_ticketpb.GetChangeItemDeployStrategyReq{
			ProjectUniqueId: "",
		}, "")
		assert.NoError(t, err)
		assert.Equal(t, strategy.GetWebStrategy().GetStrategyRegions()[0].GetRegion(), paaspb.GoofyDeployRegion_GOOFY_DEPLOY_REGION_US_TTP)
	})

}
