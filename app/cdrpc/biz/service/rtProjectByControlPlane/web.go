package rtProjectByControlPlane

import (
	"code.byted.org/devinfra/hagrid/pkg/canal_provider/crossborder"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	goofy_deploy_pkg "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/goofy_deploy"
	webDeployUsTtp "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/pipeline/overview/us_ttp"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/base_project"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/web/cn"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/web/i18n"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/web/ttp"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/web/us_ttp"
)

func NewWebCnProject() base_project.RtProjectControlPlane {
	return &cn.WebCnProject{
		GoofySDK:       goofy_deploy_pkg.GetClient(),
		CrossborderSDK: crossborder.NewCrossborderSDK(),
	}
}

func NewWebI18nProject() base_project.RtProjectControlPlane {
	return &i18n.WebI18nProject{
		GoofySDK:       goofy_deploy_pkg.GetClient(),
		CrossborderSDK: crossborder.NewCrossborderSDK(),
	}
}

func NewWebUSTTPProject() base_project.RtProjectControlPlane {
	return &us_ttp.WebUSTTPProject{
		ReleaseTicketDao:           repository.NewReleaseTicketDao(),
		CrossborderSDK:             crossborder.NewCrossborderSDK(),
		ReleaseTicketChangeItemDao: repository.NewReleaseTicketChangeItemDao(),
		WebDeployUsTtp:             webDeployUsTtp.NewWebDeployUSTTP(),
	}
}

func NewWebLegacyTTPProject() base_project.RtProjectControlPlane {
	return &ttp.WebLagacyTTPProject{
		CrossborderSDK: crossborder.NewCrossborderSDK(),
	}
}
