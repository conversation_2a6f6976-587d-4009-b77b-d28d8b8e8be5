package cronjobi18nbd

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	pkgresource "code.byted.org/devinfra/hagrid/pkg/resource"
	cronjobResource "code.byted.org/devinfra/hagrid/pkg/resource/cronjob"
	"code.byted.org/devinfra/hagrid/pkg/resource/protocol"
	"code.byted.org/gopkg/lang/v2/slicex"
	"code.byted.org/gopkg/logs/v2"
	pcronjob "code.byted.org/iesarch/paas_sdk/cronjob"

	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/base_project"
)

type CronjobI18nBDProject struct{}

// 后续统一走resource api, 本期仅支持 i18nbd 控制面
func (t *CronjobI18nBDProject) GetService(ctx context.Context, req *base_project.GetServiceReq) (resp interface{}, exist bool, err error) {
	logs.CtxInfo(ctx, "[GetService] i18nbd psm: %s", req.ProjectUniqueId)
	service, err := pkgresource.GetDefaultClient().Cronjob.GetServiceByPSM(ctx, &cronjobResource.GetServiceByPSMReq{
		XRegionInfo: protocol.XRegionInfo{
			TargetRegion: protocol.ResourceRegionI18NBD,
		},
		PSM:      req.ProjectUniqueId,
		Username: req.Username,
	})
	if err != nil && strings.Contains(err.Error(), "not found") {
		return nil, false, nil
	}
	if err != nil {
		return nil, false, bits_err.THIRDPARTYCRONJOB.ErrCallCronJobAPI.PassThrough(err)
	}
	if service == nil || service.JobInfo == nil {
		return nil, false, bits_err.THIRDPARTYCRONJOB.ErrCallCronJobAPI.AddErrMsg(fmt.Sprintf("service not found"))
	}
	return service.JobInfo, true, nil
}

func (t *CronjobI18nBDProject) GetClusterList(ctx context.Context, psm string, cp string) (resp interface{}, err error) {
	logs.CtxInfo(ctx, "[GetClusterList] i18nbd psm: %s", psm)
	username := cdauth.Username(ctx)
	service, err := pkgresource.GetDefaultClient().Cronjob.GetServiceByPSM(ctx, &cronjobResource.GetServiceByPSMReq{
		// TODO: 统一封装 cp to XRegionInfo
		XRegionInfo: protocol.XRegionInfo{
			TargetRegion: protocol.ResourceRegionI18NBD,
		},
		PSM:      psm,
		Username: username,
	})
	if err != nil {
		return nil, bits_err.THIRDPARTYCRONJOB.ErrCallCronJobAPI.PassThrough(err)
	}
	if service == nil || service.JobInfo == nil {
		return nil, bits_err.THIRDPARTYCRONJOB.ErrCallCronJobAPI.AddErrMsg(fmt.Sprintf("service not found"))
	}
	return slicex.Map(service.JobInfo.Cluster, func(from pcronjob.ClusterInfo) *pcronjob.ClusterInfo {
		return &from
	}), nil
}

func (t *CronjobI18nBDProject) GetTicket(ctx context.Context, ticketID string, idc string, username string) (resp interface{}, err error) {
	id, err := strconv.Atoi(ticketID)
	if err != nil {
		return nil, bits_err.THIRDPARTYCRONJOB.ErrCallCronJobAPI.PassThrough(err)
	}
	logs.CtxInfo(ctx, "[GetTicket] i18nbd ticketID: %d", id)
	ticket, err := pkgresource.GetDefaultClient().Cronjob.GetTicket(ctx, &cronjobResource.GetTicketReq{
		XRegionInfo: protocol.XRegionInfo{
			TargetRegion: protocol.ResourceRegionI18NBD,
		},
		TicketID: int64(id),
		Username: username,
	})
	if err != nil {
		return nil, bits_err.THIRDPARTYCRONJOB.ErrCallCronJobAPI.PassThrough(err)
	}
	return ticket.Ticket, nil
}

func (p *CronjobI18nBDProject) GetDeploymentTicket(ctx context.Context, query *change_itempb.DeployTicketQuery) (ticket *change_itempb.DeployTicket, err error) {
	return nil, errors.New("unsupported project type or control plane")
}

func (t *CronjobI18nBDProject) TicketControl(ctx context.Context, ticketID string, idc string, action string, username string) error {
	return errors.New(" not implement")
}

func (t *CronjobI18nBDProject) GetDefaultChangeItemDeployStrategy(ctx context.Context, req *release_ticketpb.GetChangeItemDeployStrategyReq, username string) (*release_ticketpb.GetChangeItemDeployStrategyResp, error) {
	return nil, errors.New(" not implement")
}

func (t *CronjobI18nBDProject) SubmitRollbackAndGetVarMap(ctx context.Context, query base_project.TicketQuery, username string) (map[string]interface{}, error) {
	return nil, errors.New(" not implement")
}

func (t *CronjobI18nBDProject) FillServiceMeta(ctx context.Context, cp sharedpb.ControlPlane, deployTarget *change_itempb.ChangeItemDeployTarget, username string) (*change_itempb.ChangeItemDeployTarget, error) {
	return nil, errors.New("not implement")
}
