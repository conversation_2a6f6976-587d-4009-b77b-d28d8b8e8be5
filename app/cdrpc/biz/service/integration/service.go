package integration

// TODO: 需要收敛所有的归档和同步的逻辑

//go:generate mockgen -destination mock/service.go code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/integration Integration

import (
	"context"

	integratesdk "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
)

type Integration interface {
	GetChangeItemsLockStatus(ctx context.Context, integrationID uint64, changeItems []*release_ticketpb.QueryChangeItem) ([]*release_ticketpb.ChangeItemIntegrationLockStatus, error)
}

func NewIntegration() Integration {
	return &integration{
		integrationSvc: integratesdk.NewIntegrationSvc(),
	}
}

type integration struct {
	integrationSvc integratesdk.IntegrationSvc
}
