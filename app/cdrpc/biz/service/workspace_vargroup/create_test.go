package workspace_vargroup

import (
	"context"
	"testing"

	"code.byted.org/devinfra/hagrid/pbgen/byted/bc/varstorepb/varstoreservice_mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/variablepb"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"
)

type WorkspaceVarGroupTestSuite struct {
	suite.Suite
	ctx    context.Context
	cancel context.CancelFunc
}

func (t *WorkspaceVarGroupTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
}

func (t *WorkspaceVarGroupTestSuite) TearDownTest() {
	t.cancel()
}

func TestWorkspaceVarGroup(t *testing.T) {
	suite.Run(t, new(WorkspaceVarGroupTestSuite))
}

func (t *WorkspaceVarGroupTestSuite) TestCreateWorkspaceVarGroup() {
	ctx := context.Background()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	mockVarstoreCli := varstoreservice_mock.NewMockClient(ctrl)

	svc := &workspaceVarGroupSvc{
		VarstoreCli: mockVarstoreCli,
	}

	mockVarstoreCli.EXPECT().
		CreateVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, nil).AnyTimes()

	mockVarstoreCli.EXPECT().
		AssociateVarGroup(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, nil).AnyTimes()

	_, err := svc.CreateWorkspaceVarGroup(ctx, &variablepb.CreateWorkspaceVarGroupReq{
		WorkspaceId: 666,
		Username:    "Li Hua",
	})

	t.NoError(err)
}
