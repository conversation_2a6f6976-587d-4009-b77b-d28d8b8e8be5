load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "helloworld",
    srcs = [
        "hello.go",
        "service.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/helloworld",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cdrpc/biz/pkg/auth",
        "//app/cdrpc/biz/pkg/feature_gate",
        "//app/cdrpc/biz/pkg/iac_metadata",
        "@com_github_bytedance_sonic//:sonic",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_gopkg_metainfo//:metainfo",
        "@org_byted_code_iesarch_cdaas_utils//erri",
    ],
)

go_test(
    name = "helloworld_test",
    srcs = ["hello_test.go"],
    embed = [":helloworld"],
    deps = [
        "//app/cdrpc/biz/pkg/auth",
        "//app/cdrpc/biz/pkg/feature_gate",
        "//app/cdrpc/biz/pkg/iac_metadata",
        "//app/rolloutservices/iac/meta_sdk/model",
        "@com_github_bytedance_mockey//:mockey",
        "@com_github_golang_mock//gomock",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//suite",
        "@org_byted_code_bits_monkey//:monkey",
        "@org_byted_code_devinfra_bytegate_sdk//bytegate_client",
        "@org_byted_code_devinfra_bytegate_sdk//models",
        "@org_byted_code_gopkg_metainfo//:metainfo",
    ],
)
