package pipeline

import (
	"context"
	"sync"

	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/envpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/pipelinepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/lang/gg/gslice"
)

func (p *pipelineSvc) StagePipelineRunPreCheck(ctx context.Context, req *pipelinepb.StagePipelineRunPreCheckReq) (resp *pipelinepb.StagePipelineRunPreCheckResp, err error) {
	ctx = cdauth.WithUsername(ctx, req.GetUsername())
	resp = &pipelinepb.StagePipelineRunPreCheckResp{
		HasPassed: true, // 默认放行
	}

	mu := sync.Mutex{}
	results := make([]*pipelinepb.CheckItemResult, 0)
	for _, pro := range req.GetParams().GetSelectedProjects() {
		if !(pro.GetDeployTarget().GetProjectType() == sharedpb.ProjectType_PROJECT_TYPE_TCC || pro.GetDeployTarget().GetProjectType() == sharedpb.ProjectType_PROJECT_TYPE_TCE) {
			continue
		}

		if !(pro.GetControlPlane() == sharedpb.ControlPlane_CONTROL_PLANE_US_TTP ||
			pro.GetControlPlane() == sharedpb.ControlPlane_CONTROL_PLANE_TTP) &&
			pro.GetDeployTarget().GetProjectType() == sharedpb.ProjectType_PROJECT_TYPE_TCE {
			continue
		}

		projectEnvConfig := gslice.Filter(req.GetParams().GetEnvProjectConfigs(), func(config *envpb.EnvProjectConfig) bool {
			return config.GetProjectType() == pro.GetDeployTarget().GetProjectType() &&
				config.GetControlPlane() == pro.GetControlPlane() &&
				config.GetProjectUniqueId() == pro.GetDeployTarget().GetProjectUniqueId()
		})
		projectSvc := p.rtProjectSvc.GetRtProjectByProjectType(ctx, pro.GetDeployTarget().GetProjectType())
		pass, res, err := projectSvc.PipeLineRunPreCheck(ctx, req.GetParams().GetSelectedControlPlane(), pro, req.GetParams().GetEnv(), projectEnvConfig, int64(req.CheckItemId))
		if err != nil {
			return nil, err
		}

		res.CheckProject = pro.GetDeployTarget()

		mu.Lock()
		if resp.HasPassed && !pass {
			resp.HasPassed = false
		}
		results = append(results, res)
		mu.Unlock()
	}

	resp.CheckItemResults = results
	return resp, nil
}
