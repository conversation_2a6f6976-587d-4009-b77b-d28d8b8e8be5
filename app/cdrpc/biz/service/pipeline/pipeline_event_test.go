package pipeline

import (
	"context"
	"reflect"
	"testing"

	"code.byted.org/bits/monkey"
	mockrepo "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	"code.byted.org/devinfra/hagrid/libs/events"
	"code.byted.org/lang/gg/gresult"

	bcSDK "code.byted.org/canal/bytecycle_sdk/pipeline"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/pipelinepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	oreopb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/rmq"
	integratesdk "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tcc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/varstore_rpc"
	cdfgmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/cd_fg/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/config"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/oreo_manager"
	oreoMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/oreo_manager/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rerun_change"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/variable"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
	"code.byted.org/devinfra/hagrid/pkg/devops_pipeline_sdk"
	"code.byted.org/devinfra/hagrid/pkg/envplatform"
)

type PipelineEventTestSuite struct {
	suite.Suite
	ctx             context.Context
	cancel          context.CancelFunc
	mockCtrl        *gomock.Controller
	mockFeatureGate *cdfgmock.MockCdFeatureGateSvc
}

func (t *PipelineEventTestSuite) SetupTest() {
	t.ctx, t.cancel = context.WithCancel(context.Background())
	tcc.MustInit()
	varstore_rpc.InitVarStoreClient()
	rmq.InitMockedBitsEventsProducer()
	t.mockCtrl = gomock.NewController(t.T())
	t.mockFeatureGate = cdfgmock.NewMockCdFeatureGateSvc(t.mockCtrl)
}

func (t *PipelineEventTestSuite) TearDownTest() {
	t.cancel()
}

func TestReleaseTicketEvent(t *testing.T) {
	suite.Run(t, new(PipelineEventTestSuite))
}

func (t *PipelineEventTestSuite) TestSendRunReleaseTicketPipelineOperationRecord() {
	t.Run("success", func() {
		ctx := context.Background()
		db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicket{}, &entity.DBStage{}, &entity.DBStagePipeline{}, &entity.DBStagePipelineRunRecord{})
		ctx = mysql.ContextWithUnitTestDB(ctx, db)
		svc := &pipelineSvc{
			stagePipelineDao:           repository.NewStagePipelineDao(),
			stageDao:                   repository.NewStageDao(),
			releaseTicketChangeItemDao: repository.NewReleaseTicketChangeItemDao(),
			releaseTicketDao:           repository.NewReleaseTicketDao(),
			releaseApproverDao:         repository.NewReleaseApproverDao(),
			stagePipelineRunRecordDao:  repository.NewStagePipelineRunRecordDao(),
			releaseTicketVarSvc:        variable.NewReleaseTicketVar(),
			pipelineSDK:                bcSDK.NewSDK(),
			changeItemSvc:              change_item.NewChangeItemSvc(),
			integrationSvc:             integratesdk.NewIntegrationSvc(),
			envSDK:                     envplatform.DefaultSDK(),
			rerunChangeDao:             repository.NewRerunChangeDao(),
			rerunChangeSvc:             rerun_change.NewRerunChangeSvc(),
			gitServerSvc:               integratesdk.NewGitServerSvc(),
			nodeConfigHelper:           config.NewNodeConfigHelper(),
			workflowConfigHelper:       config.NewWorkflowConfigHelper(),
			oreoManager:                oreo_manager.NewOreoManager(),
		}
		m := mockey.Mock((*oreo_manager.OreoMgr).GetOnePipeline).Return(&oreopb.Pipeline{
			PipelineId: 1,
			LastRunId:  1,
		}, nil).Build()
		defer m.UnPatch()
		params := &devops_pipeline_sdk.RunParams{
			Username: "xiaozan",
			PipelineOverviews: []*devops_pipeline_sdk.PipelineOverview{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
					PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN,
					MainPipeline: &devops_pipeline_sdk.MainPipeline{
						PipelineId:     1,
						HasPlaceholder: false,
					},
					ProjectPipeline: nil,
				},
			},
			SelectedControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
			SelectedProjects: []*devops_pipeline_sdk.ProjectParams{
				{
					ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCE,
					ProjectUniqueId: "11",
					ProjectName:     "test",
				},
			},
			PipelineIDToAssignmentIDs: nil,
			PipelineIDToVarsMap:       nil,
			AutoTrigger:               false,
		}
		releaseTicket := &entity.DBReleaseTicket{
			ReleaseTicketID: 1,
			WorkspaceID:     1,
			Name:            "tttttttt",
		}
		stage := &entity.DBStage{
			ID:              1,
			StageID:         1,
			Name:            "test",
			NameI18N:        "test",
			ReleaseTicketID: 1,
			WorkflowID:      1,
			NodeID:          1,
			NodeType:        "",
			NodeConfig:      "",
			StageStatus:     "",
		}
		svc.SendRunReleaseTicketPipelineOperationRecord(ctx, releaseTicket, stage, params)
	})
}

func (t *PipelineEventTestSuite) TestSendRunReleaseTicketPipelineLarkNotice() {
	t.Run("success", func() {
		ctx := t.ctx
		db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicket{}, &entity.DBNode{}, &entity.DBStage{}, &entity.DBStagePipeline{})
		ctx = mysql.ContextWithUnitTestDB(ctx, db)

		svc := &pipelineSvc{
			stagePipelineDao:           repository.NewStagePipelineDao(),
			stageDao:                   repository.NewStageDao(),
			nodeDao:                    repository.NewNodeDao(),
			releaseTicketChangeItemDao: repository.NewReleaseTicketChangeItemDao(),
			releaseTicketDao:           repository.NewReleaseTicketDao(),
			releaseApproverDao:         repository.NewReleaseApproverDao(),
			stagePipelineRunRecordDao:  repository.NewStagePipelineRunRecordDao(),
			releaseTicketVarSvc:        variable.NewReleaseTicketVar(),
			pipelineSDK:                bcSDK.NewSDK(),
			changeItemSvc:              change_item.NewChangeItemSvc(),
			integrationSvc:             integratesdk.NewIntegrationSvc(),
			envSDK:                     envplatform.DefaultSDK(),
			rerunChangeDao:             repository.NewRerunChangeDao(),
			rerunChangeSvc:             rerun_change.NewRerunChangeSvc(),
			gitServerSvc:               integratesdk.NewGitServerSvc(),
			nodeConfigHelper:           config.NewNodeConfigHelper(),
			workflowConfigHelper:       config.NewWorkflowConfigHelper(),
			oreoManager:                oreo_manager.NewOreoManager(),
			cdFeatureGateSvc:           t.mockFeatureGate,
		}
		t.mockFeatureGate.EXPECT().IsCDFgSwitchEnabled(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
		m := mockey.Mock((*oreo_manager.OreoMgr).GetOnePipeline).Return(&oreopb.Pipeline{
			PipelineId: 1,
			LastRunId:  1,
		}, nil).Build()
		defer m.UnPatch()
		m1 := mockey.Mock((*oreo_manager.OreoMgr).GetPipelineRun).Return(&oreopb.PipelineRun{
			PipelineId: 1,
			RunId:      1,
		}, nil).Build()
		defer m1.UnPatch()

		stage := &entity.DBStage{
			ID:              1,
			StageID:         1,
			Name:            "test",
			NameI18N:        "test",
			ReleaseTicketID: 1,
			WorkflowID:      1,
			NodeID:          1,
			NodeType:        "",
			NodeConfig:      "",
			StageStatus:     "",
		}
		rt := &entity.DBReleaseTicket{ReleaseTicketID: 1, WorkspaceID: 1, WorkflowID: 1}
		err := svc.releaseTicketDao.Insert(ctx, rt)
		t.NoError(err)
		err = svc.stageDao.Insert(ctx, stage)
		t.NoError(err)
		err = svc.nodeDao.BatchCreate(ctx, []*entity.DBNode{
			{
				ID:           0,
				NodeID:       1,
				WorkflowID:   1,
				Name:         "test",
				NameI18N:     "test",
				NodeType:     1,
				Enabled:      true,
				EditDisabled: true,
				Version:      1,
				NodeConfig:   "",
			},
		})
		t.NoError(err)
		p := mockey.Mock((*pipelineSvc).GetDBStagePipelineOverviews).Return(
			[]*release_ticketpb.PipelineOverview{
				{
					ControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
					PipelineType: sharedpb.PipelineType_PIPELINE_TYPE_MAIN,
					MainPipeline: &release_ticketpb.MainPipeline{PipelineId: 2},
				},
				{
					ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
					PipelineType:    sharedpb.PipelineType_PIPELINE_TYPE_PROJECT,
					ProjectPipeline: &release_ticketpb.ProjectPipeline{PipelineId: 3},
				},
			}, nil,
		).Build()
		defer p.UnPatch()
		err = svc.SendRunReleaseTicketPipelineLarkNotice(ctx, rt.ReleaseTicketID, stage.StageID, []sharedpb.ControlPlane{sharedpb.ControlPlane_CONTROL_PLANE_CN})
		t.NoError(err)
	})
}

func (t *PipelineEventTestSuite) TestSendCancelReleaseTicketPipelineOperationRecord() {
	t.Run("success", func() {
		ctx := context.Background()
		db := testfactory.NewUnitTestDB(ctx, &entity.DBReleaseTicket{}, &entity.DBStage{}, &entity.DBStagePipeline{}, &entity.DBStagePipelineRunRecord{})
		ctx = mysql.ContextWithUnitTestDB(ctx, db)
		svc := &pipelineSvc{
			stagePipelineDao:           repository.NewStagePipelineDao(),
			stageDao:                   repository.NewStageDao(),
			releaseTicketChangeItemDao: repository.NewReleaseTicketChangeItemDao(),
			releaseTicketDao:           repository.NewReleaseTicketDao(),
			releaseApproverDao:         repository.NewReleaseApproverDao(),
			stagePipelineRunRecordDao:  repository.NewStagePipelineRunRecordDao(),
			releaseTicketVarSvc:        variable.NewReleaseTicketVar(),
			pipelineSDK:                bcSDK.NewSDK(),
			changeItemSvc:              change_item.NewChangeItemSvc(),
			integrationSvc:             integratesdk.NewIntegrationSvc(),
			envSDK:                     envplatform.DefaultSDK(),
			rerunChangeDao:             repository.NewRerunChangeDao(),
			rerunChangeSvc:             rerun_change.NewRerunChangeSvc(),
			gitServerSvc:               integratesdk.NewGitServerSvc(),
			nodeConfigHelper:           config.NewNodeConfigHelper(),
			workflowConfigHelper:       config.NewWorkflowConfigHelper(),
		}
		req := &pipelinepb.CancelSingleBuildReq{
			BuildId:              1,
			Username:             "xiaozan",
			SelectedControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN,
			StageId:              2,
			IsMain:               true,
			ReleaseTicketId:      3,
			PipelineCancelReason: sharedpb.PipelineCancelReason_PIPELINE_CANCEL_REASON_USER_CLICK,
		}
		db.Create(&entity.DBReleaseTicket{
			ReleaseTicketID: 3,
			WorkspaceID:     1,
			Name:            "tttttttt",
		})
		db.Create(&entity.DBStage{
			ID:              1,
			StageID:         2,
			Name:            "test",
			NameI18N:        "测试",
			ReleaseTicketID: 3,
		})
		db.Create(&entity.DBStagePipelineRunRecord{
			ID:                   1,
			PipelineID:           2,
			BuildID:              1,
			SelectedControlPlane: sharedpb.ControlPlane_CONTROL_PLANE_CN.String(),
			SelectedProject:      "{}",
		})
		svc.SendCancelReleaseTicketPipelineOperationRecord(ctx, req.GetReleaseTicketId(), req.GetStageId(), req.GetBuildId(), req.GetIsMain(), req.GetSelectedControlPlane(), req.GetPipelineCancelReason())
	})
}

func (t *PipelineEventTestSuite) Test_pipelineSvc_SendRunReleaseTicketRollbackPipelineLarkNotice() {
	rtDao := mockrepo.NewMockReleaseTicketDao(t.mockCtrl)
	mainPPLRollbackInfoDao := mockrepo.NewMockMainPplRollbackInfoDao(t.mockCtrl)
	oreoManager := oreoMock.NewMockOreoManager(t.mockCtrl)
	svc := &pipelineSvc{
		releaseTicketDao:       rtDao,
		mainPPlRollbackInfoDao: mainPPLRollbackInfoDao,
		oreoManager:            oreoManager,
	}
	t.Run("success", func() {
		t.mockFeatureGate.EXPECT().IsCDFgSwitchEnabled(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
		rtDao.EXPECT().GetByReleaseTicketID(gomock.Any(), gomock.Any()).Return(&entity.DBReleaseTicket{
			ReleaseTicketID: 1,
			WorkspaceID:     1,
			Name:            "tttttttt",
		}, nil).AnyTimes()
		mainPPLRollbackInfoDao.EXPECT().FindByPipelineID(gomock.Any(), gomock.Any()).Return(&entity.DBMainPipelineRollbackInfo{
			ID:              1,
			ReleaseTicketID: 1,
			PipelineID:      2,
		}, true, nil).AnyTimes()
		oreoManager.EXPECT().GetPipelineRunId(gomock.Any(), gomock.Any()).Return(uint64(100), nil).AnyTimes()
		oreoManager.EXPECT().GetPipelineRun(gomock.Any(), gomock.Any(), gomock.Any()).Return(&oreopb.PipelineRun{
			PipelineId: 1,
			RunId:      100,
		}, nil).AnyTimes()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(rmq.BitsEventsProducer()), "SendReleaseTicketPipelineRun",
			func(ctx context.Context, event *events.ReleaseTicketPipelineRunEvent) gresult.R[string] {
				return gresult.OK("1")
			}).UnPatch()
		err := svc.SendRunReleaseTicketRollbackPipelineLarkNotice(context.Background(), 1, 1)
		t.NoError(err)
	})
}
