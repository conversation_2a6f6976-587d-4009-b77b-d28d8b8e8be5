package us_ttp

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"code.byted.org/bits/monkey"
	oreoMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/oreo_manager/mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	oreopb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	repositorymock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository/mock"
	goofyDeploySvcMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/goofy_deploy/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/change_item"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/pipeline/overview/base"
	"code.byted.org/devinfra/hagrid/app/cdrpc/testfactory"
)

func TestGetWebTicketInfo(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	oreoManager := oreoMock.NewMockOreoManager(ctrl)

	t.Run("no err", func(t *testing.T) {
		oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(&oreopb.PipelineRun{}, nil)
		svc := &webDeployUSTTP{
			oreoManager: oreoManager,
		}
		_, err := svc.GetWebTicketInfo(ctx, &base.WebTicketQuery{})
		assert.NoError(t, err)
	})

	t.Run("normal", func(t *testing.T) {
		oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(&oreopb.PipelineRun{
			PipelineId: 1,
			RunId:      1,
		}, nil)
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&webDeployUSTTP{}), "GetRtChangeItem", func(ctx context.Context, query *base.WebTicketQuery) (*release_ticketpb.ReleaseTicketChangeItem, error) {
			return &release_ticketpb.ReleaseTicketChangeItem{
				DeployTarget: &change_itempb.ChangeItemDeployTarget{
					TtpWebName: "11",
				},
			}, nil
		}).UnPatch()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&webDeployUSTTP{}), "GetRegionTickets", func(ctx context.Context, ttpWebNameName string, buildContextInfo *BuildContextInfo) ([]*change_itempb.RegionTicketWeb, error) {
			return []*change_itempb.RegionTicketWeb{
				{
					Status: paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED,
				},
			}, nil
		}).UnPatch()
		defer monkey.PatchInstanceMethod(reflect.TypeOf(&webDeployUSTTP{}), "HandleRegionTicketsByPipelineRun", func(ctx context.Context, ttpWebNameName string, regionTickets []*change_itempb.RegionTicketWeb, buildContextInfo *BuildContextInfo) ([]*change_itempb.RegionTicketWeb, error) {
			return []*change_itempb.RegionTicketWeb{
				{
					Status: paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED,
				},
			}, nil
		}).UnPatch()
		svc := &webDeployUSTTP{
			oreoManager: oreoManager,
		}
		info, err := svc.GetWebTicketInfo(ctx, &base.WebTicketQuery{PipelineID: 1})
		assert.NoError(t, err)
		assert.Equal(t, info.Ticket.GetWebDeployStatus(), change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_COMPLETED)
	})
}

func TestGetRtChangeItem(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	stagePipelineDao := repositorymock.NewMockStagePipelineDao(ctrl)
	stageDao := repositorymock.NewMockStageDao(ctrl)
	releaseTicketChangeItemDao := repositorymock.NewMockReleaseTicketChangeItemDao(ctrl)

	t.Run("normal", func(t *testing.T) {
		stagePipelineDao.EXPECT().FindByPipelineID(gomock.Any(), gomock.Any()).Return(&entity.DBStagePipeline{
			PipelineID: 1,
		}, true, nil)
		stageDao.EXPECT().GetStageByStageID(gomock.Any(), gomock.Any()).Return(&entity.DBStage{
			StageID: 1,
		}, nil)
		releaseTicketChangeItemDao.EXPECT().GetSingleChangeItem(gomock.Any(), gomock.Any()).Return(testfactory.NewMockDBReleaseTicketChangeItem(1), nil)
		svc := &webDeployUSTTP{
			stagePipelineDao:           stagePipelineDao,
			stageDao:                   stageDao,
			releaseTicketChangeItemDao: releaseTicketChangeItemDao,
			changeItemSvc:              change_item.NewChangeItemSvc(),
		}
		item, err := svc.GetRtChangeItem(ctx, &base.WebTicketQuery{PipelineID: 1})
		assert.NoError(t, err)
		assert.Equal(t, item.GetDeployTarget().GetServiceMetaWeb().GetAppId(), "1")
	})
}

func TestGetRegionTickets(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	goofyDeploySvc := goofyDeploySvcMock.NewMockGoofyDeploySvc(ctrl)
	t.Run("normal", func(t *testing.T) {
		buildContextInfo := &BuildContextInfo{
			BuildInfo: &oreopb.PipelineRun{},
			RtChangeItem: &release_ticketpb.ReleaseTicketChangeItem{
				ProjectUniqueId: "1",
			},
		}
		goofyDeploySvc.EXPECT().GetWebDeployUnit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*paaspb.DeployUnit{
			{
				Region: 5001,
			},
		}, nil)
		goofyDeploySvc.EXPECT().GetWebChannels(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*paaspb.ChannelBasicInfo{
			{
				ChannelName: "1",
				ChannelType: 1,
			},
		}, nil)
		svc := &webDeployUSTTP{
			goofyDeploySvc: goofyDeploySvc,
		}
		tickets, err := svc.GetRegionTickets(ctx, "1", buildContextInfo)
		assert.NoError(t, err)
		assert.Equal(t, tickets[0].GetRegion(), paaspb.GoofyDeployRegion_GOOFY_DEPLOY_REGION_US_TTP)
	})

}
