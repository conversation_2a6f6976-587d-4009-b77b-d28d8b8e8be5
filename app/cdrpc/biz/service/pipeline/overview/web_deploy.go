package pipeline

import (
	"context"
	"fmt"
	"strconv"
	"sync"

	"github.com/bytedance/sonic"
	"github.com/pkg/errors"
	"golang.org/x/sync/errgroup"

	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/crossborderpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	oreopb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb"
	"code.byted.org/gopkg/lang/v2/conv"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/erri"
	goofy "code.byted.org/iesarch/paas_sdk/goofy_deploy"
	"code.byted.org/lang/gg/choose"
	"code.byted.org/lang/gg/gslice"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/repository"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/cd_fg"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/pipeline/overview/base"
	"code.byted.org/devinfra/hagrid/app/cdrpc/config"
	cdutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
)

const (
	ChannelStatus_Delete = 4 // 频道被删除
)

//type WebTicketQuery struct {
//	BuildID         int64
//	PipelineID      uint64
//	ProjectUniqueId string
//	// ttp web 查部署工单用此字段，而不是 project_unique_id 字段
//	TtpWebName string
//	sharedpb.ControlPlane
//	BuildInfo *oreopb.PipelineRun
//}

type WebDeploymentOverview struct {
	DeploymentID uint64
	AtomID       string // 用来判断是哪种类型的部署原子
	ChannelID    uint64
}

func (s *atomSvc) GetWebTicketInfo(ctx context.Context, query *base.WebTicketQuery) (*base.WebDeployTicketInfo, error) {
	logs.CtxInfo(ctx, "[GetWebTicketInfo] query %s", utils.ToJson(query))
	if query.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_US_TTP {
		// 直接使用 us-ttp的方法
		return s.webDeployUsTtp.GetWebTicketInfo(ctx, query)
	}
	execID := ""
	buildID := query.BuildID
	pipelineID := query.PipelineID
	// 根据应用中心 Web 调整此处
	appID := query.ProjectUniqueId
	var build = &oreopb.PipelineRun{}
	var appInfo = &goofy.AppFullInfo{}
	logs.CtxInfo(ctx, "[GetWebTicketInfo] buildID %d, pipelineID %d, appID %s", buildID, pipelineID, appID)

	if query.BuildInfo == nil && buildID == 0 && query.PipelineID > 0 {
		//feature开关获取失败或未配置，使用原有逻辑获取流水线RunID
		useNewPplItf, err := s.cdFeatureGateSvc.IsCDFgSwitchEnabled(ctx, cd_fg.FGKEY_USE_NEW_INTERFACE_GET_PIPELINE_RUN_ID)
		logs.CtxInfo(ctx, "[GetWebTicketInfo] get feature gate key[use_new_interface_get_pipeline_run_id] with value:%v,  err: %v", useNewPplItf, err)

		if err != nil || !useNewPplItf {
			pipelineInfo, err := s.oreoManager.GetOnePipeline(ctx, pipelineID)
			if err != nil {
				logs.CtxError(ctx, "[GetWebTicketInfo] GetOnePipeline error", err)
				return nil, err
			}
			logs.CtxInfo(ctx, "[GetWebTicketInfo] get buildID: %v through GetOnePipeline", pipelineInfo.LastRunId)
			buildID = int64(pipelineInfo.LastRunId)
		} else {
			runID, err := s.oreoManager.GetPipelineRunId(ctx, pipelineID)
			if err != nil {
				logs.CtxError(ctx, "[GetWebTicketInfo] new logic: GetPipelineRunId err: %s", err.Error())
				return nil, err
			}
			logs.CtxInfo(ctx, "[GetWebTicketInfo] get buildID: %v through GetPipelineRunId", runID)
			buildID = int64(runID)
		}
		//get buildID finished
	}

	logs.CtxInfo(ctx, "[GetWebTicketInfo] buildID %d", buildID)
	var err error
	if query.BuildInfo == nil && buildID > 0 {
		build, err = s.oreoManager.GetPipelineRun(ctx, uint64(buildID), true)
		if err != nil {
			return nil, erri.Error(err)
		}
	}

	if query.BuildInfo != nil {
		build = query.BuildInfo
	}

	if build.RunId == 0 {
		// 流水线尚未运行
		return &base.WebDeployTicketInfo{
			Ticket: &change_itempb.DeployTicketWeb{
				WebDeployStatus: change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_NOTREADY,
				RegionTickets:   []*change_itempb.RegionTicketWeb{},
			},
			ChangeItem: nil,
		}, nil
	}

	logs.CtxInfo(ctx, "[GetWebTicketInfo] pipelineID: %v", build.PipelineId)
	// 根据 pipelineID 获取 deployStrategy
	dbStagePpl, existed, err := s.stagePipelineDao.FindByPipelineID(ctx, uint64(build.PipelineId))
	if err != nil {
		logs.CtxInfo(ctx, "FindByPipelineID: %s", err.Error())
		return nil, err
	}
	if !existed {
		err = errors.New(fmt.Sprintf("[GetWebTicketInfo]can not find pipeline, id: %v", build.PipelineId))
		return nil, err
	}
	var dbChangeItem *entity.DBReleaseTicketChangeItem
	// BC TTP 的 changeItem 信息需要从主流水线里解析
	if dbStagePpl.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_TTP.String() {
		var dbChangeItemFromSnapshot []*entity.DBReleaseTicketChangeItem
		err = sonic.Unmarshal(conv.UnsafeStringToBytes(dbStagePpl.ChangeItemsSnapshot), &dbChangeItemFromSnapshot)
		if err != nil {
			return nil, err
		}
		// 与 GetSingleChangeItem 逻辑保持一致，只取一个就好
		if len(dbChangeItemFromSnapshot) == 0 {
			return nil, erri.Errorf("empty changeItem from ttp stagePpl")
		}
		targetDbChangeItemFromSnapshot := gslice.Find(dbChangeItemFromSnapshot,
			func(item *entity.DBReleaseTicketChangeItem) bool {
				return item.ProjectUniqueID == query.ProjectUniqueId &&
					item.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_TTP.String() &&
					item.ProjectType == sharedpb.ProjectType_PROJECT_TYPE_WEB.String()
			})
		if !targetDbChangeItemFromSnapshot.IsOK() {
			return nil, erri.Errorf("no valid change item.")
		}
		// 注意 dbStagePpl.ChangeItemsSnapshot 里的 DeployResource 里包含了非主仓信息。需要过滤掉非主仓信息，只保留主仓信息。后文做过滤。
		dbChangeItem = &entity.DBReleaseTicketChangeItem{
			ID:                       targetDbChangeItemFromSnapshot.Value().ID,
			ReleaseTicketID:          targetDbChangeItemFromSnapshot.Value().ReleaseTicketID,
			ProjectType:              targetDbChangeItemFromSnapshot.Value().ProjectType,
			ProjectUniqueID:          targetDbChangeItemFromSnapshot.Value().ProjectUniqueID,
			DependencyItems:          targetDbChangeItemFromSnapshot.Value().DependencyItems,
			ControlPlane:             targetDbChangeItemFromSnapshot.Value().ControlPlane,
			DeployTarget:             targetDbChangeItemFromSnapshot.Value().DeployTarget,
			DeployStrategy:           targetDbChangeItemFromSnapshot.Value().DeployStrategy,
			DeployResource:           targetDbChangeItemFromSnapshot.Value().DeployResource,
			ProjectOwners:            targetDbChangeItemFromSnapshot.Value().ProjectOwners,
			ControlledBy:             targetDbChangeItemFromSnapshot.Value().ControlledBy,
			ArtifactsUpdateTimestamp: targetDbChangeItemFromSnapshot.Value().ArtifactsUpdateTimestamp,
			Reviewers:                targetDbChangeItemFromSnapshot.Value().Reviewers,
		}
	} else {
		dbStage, err := s.stageDao.GetStageByStageID(ctx, dbStagePpl.StageID)
		if err != nil {
			logs.CtxInfo(ctx, "GetStageByStageID: %s", err.Error())
			return nil, err
		}
		dbChangeItem, err = s.releaseTicketChangeItemDao.GetSingleChangeItem(ctx, &repository.ChangeItemQuery{
			ProjectType:     dbStagePpl.ProjectType,
			ProjectUniqueID: appID,
			ControlPlane:    dbStagePpl.ControlPlane,
			ReleaseTicketID: dbStage.ReleaseTicketID,
		})
		if err != nil {
			logs.CtxInfo(ctx, "GetSingleChangeItem: %s", err.Error())
			return nil, err
		}
	}

	if dbChangeItem == nil {
		return nil, errors.New("no change item found")
	}
	rtChangeItem, err := s.changeItemSvc.ChangeItemConvertToPB(ctx, dbChangeItem)
	if err != nil {
		logs.CtxInfo(ctx, "ChangeItemConvertToPB: %s", err.Error())
		return nil, err
	}
	rtChangeItem.GetDeployResource().ScmArtifacts =
		gslice.Filter(rtChangeItem.GetDeployResource().GetScmArtifacts(),
			func(artifact *change_itempb.SCMArtifact) bool {
				return artifact.IsMain
			})
	logs.CtxInfo(ctx, "[GetWebTicketInfo] username %v", build.CreatedBy)
	var regionTickets []*change_itempb.RegionTicketWeb

	// BC TTP 需要获取 ttp execution id
	if query.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_TTP {
		execID, _, _, err = s.ttpService.GetExecutionIDFromPipeline(ctx, int64(build.PipelineId))
		if err != nil {
			logs.CtxError(ctx, "[GetWebTicketInfo] GetExecutionIDFromPipeline, err: %s", err.Error())
			return nil, err
		}
		resp, err := s.crossborderSDK.DeliveryGetWebDeployment(ctx, &crossborderpb.DeliveryGetWebDeploymentReq{
			ExecutionId: execID,
			ProjectName: rtChangeItem.GetDeployTarget().GetTtpWebName(),
		})
		if err != nil {
			logs.CtxError(ctx, "[GetWebTicketInfo] DeliveryGetWebDeployment, err: %s", err.Error())
			return nil, bits_err.THIRDPARTYGOOFYDEPLOY.ErrGetWebDeploymentTTP.PassThrough(err)
		}
		deployment, rollbackDeployment := resp.GetData().GetBaseInfo(), resp.GetData().GetAssociatedRollbackDeployment()
		rollbackStatus := choose.IfLazyL(rollbackDeployment != nil, func() paaspb.PipelineStatus {
			return paaspb.PipelineStatus(rollbackDeployment.GetStatus())
		}, paaspb.PipelineStatus_PIPELINE_STATUS_UNSPECIFIED)
		goofyRollbackLink := choose.IfLazyL(rollbackDeployment != nil, func() string {
			return fmt.Sprintf("%s/channel/%d/deploy?deploymentId=%d", config.Conf.Goofy.TtpHost,
				resp.GetData().GetAssociatedRollbackDeployment().GetChannelId(),
				resp.GetData().GetAssociatedRollbackDeployment().GetId())
		}, "")
		regionTickets = []*change_itempb.RegionTicketWeb{
			{
				RawDeploymentId: uint64(deployment.GetId()),
				Region:          paaspb.GoofyDeployRegion(deployment.GetDeployUnitRegion()),
				Status:          paaspb.PipelineStatus(deployment.GetStatus()),
				DeployUnitId:    uint64(deployment.GetDeployUnitId()),
				DeployUnitName:  deployment.GetDeployUnitName(),
				ChannelId:       uint64(deployment.GetChannelId()),
				ChannelName:     deployment.GetChannelName(),
				ChannelType:     paaspb.ChannelType(deployment.GetChannelType()),
				ScmVersion:      deployment.GetScmVersion(),
				ScmName:         deployment.GetScmName(),
				ScmId:           uint64(deployment.GetScmId()),
				Desc:            deployment.GetDesc(),
				ScmLink:         deployment.GetScmViewLink(),
				GoofyLink: fmt.Sprintf("%s/channel/%d/deploy?deploymentId=%d", config.Conf.Goofy.TtpHost,
					deployment.GetChannelId(), deployment.GetId()),
				GoofyRollbackLink: goofyRollbackLink,
				RollbackStatus:    rollbackStatus,
			},
		}
		appInfo = &goofy.AppFullInfo{
			DeployUnits: []goofy.DeployUnitInfo{
				{
					Id:     resp.GetData().GetBaseInfo().GetDeployUnitId(),
					Name:   resp.GetData().GetBaseInfo().GetDeployUnitName(),
					Region: resp.GetData().GetBaseInfo().GetDeployUnitRegion(),
					MainChannel: &goofy.ChannelInfo{
						ChannelType: resp.GetData().GetBaseInfo().GetChannelType(),
						Name:        resp.GetData().GetBaseInfo().GetChannelName(),
					},
				},
			},
		}
	} else {
		goofyAppID, err := strconv.ParseInt(appID, 10, 64)
		if err != nil {
			return nil, err
		}
		appInfo, err = s.goofySDK.OpenGetAppById(ctx, goofy.GetAppByIdRequest{
			Id: goofyAppID,
		}, build.CreatedBy)
		if err != nil {
			logs.CtxError(ctx, "cannot get app info, appid: %v", appID)
			return nil, bits_err.THIRDPARTYGOOFYDEPLOY.ErrGetWebDeploymentCN.PassThrough(err)
		}
	}
	deployStrategy := rtChangeItem.GetDeployStrategy().GetWebStrategy()
	regions := []paaspb.GoofyDeployRegion{}
	// 默认的部署策略
	if deployStrategy == nil || deployStrategy.GetStrategyRegions() == nil {
		avaiableRegions := gslice.Map(appInfo.DeployUnits, func(d goofy.DeployUnitInfo) paaspb.GoofyDeployRegion {
			return paaspb.GoofyDeployRegion(d.Region)
		})
		switch dbStagePpl.ControlPlane {
		case sharedpb.ControlPlane_CONTROL_PLANE_CN.String():
			regions = cdutils.FilterOnlineCNRegions(avaiableRegions)
		case sharedpb.ControlPlane_CONTROL_PLANE_I18N.String():
			regions = cdutils.FilterOnlineI18NRegions(avaiableRegions)
		case sharedpb.ControlPlane_CONTROL_PLANE_TTP.String(), sharedpb.ControlPlane_CONTROL_PLANE_US_TTP.String():
			regions = gslice.Filter(avaiableRegions, func(r paaspb.GoofyDeployRegion) bool {
				return gslice.Contains([]paaspb.GoofyDeployRegion{
					paaspb.GoofyDeployRegion_GOOFY_DEPLOY_REGION_US_TTP,
				}, r)
			})
		default:
		}
	} else {
		regions = gslice.Map(deployStrategy.GetStrategyRegions(), func(d *paaspb.WebRegionStrategy) paaspb.GoofyDeployRegion {
			return d.GetRegion()
		})
	}

	if dbStagePpl.ControlPlane == sharedpb.ControlPlane_CONTROL_PLANE_TTP.String() {
		validDeployUnits := gslice.Map(regionTickets, func(ticket *change_itempb.RegionTicketWeb) paaspb.GoofyDeployRegion {
			return ticket.GetRegion()
		})
		regionTickets = gslice.Filter(regionTickets, func(ticket *change_itempb.RegionTicketWeb) bool {
			return gslice.Contains(validDeployUnits, ticket.Region)
		})
	} else {
		regionTickets = gslice.Map(regions, func(d paaspb.GoofyDeployRegion) *change_itempb.RegionTicketWeb {
			deployUnit := gslice.Find(appInfo.DeployUnits, func(i goofy.DeployUnitInfo) bool {
				return i.Region == int64(d)
			})
			if deployUnit.IsOK() {
				desc := ""
				scmLink := ""
				if rtChangeItem.GetDeployResource().GetScmArtifacts()[0].GetVersionRaw() != nil {
					desc = rtChangeItem.GetDeployResource().GetScmArtifacts()[0].GetVersionRaw().GetDesc()
					scmLink = rtChangeItem.GetDeployResource().GetScmArtifacts()[0].GetVersionRaw().GetGitUrl()
				}
				return &change_itempb.RegionTicketWeb{
					Region:         d,
					Status:         paaspb.PipelineStatus_PIPELINE_STATUS_QUEUING,
					DeployUnitId:   uint64(deployUnit.Value().Id),
					DeployUnitName: deployUnit.Value().Name,
					ChannelName:    deployUnit.Value().MainChannel.Name,
					ChannelType:    paaspb.ChannelType(deployUnit.Value().MainChannel.ChannelType),
					ScmVersion:     rtChangeItem.GetDeployResource().GetScmArtifacts()[0].GetVersion(),
					ScmName:        rtChangeItem.GetDeployResource().GetScmArtifacts()[0].GetScmName(),
					ScmId:          uint64(rtChangeItem.GetDeployResource().GetScmArtifacts()[0].GetScmId()),
					Desc:           desc,
					ScmLink:        scmLink,
				}
			}
			return &change_itempb.RegionTicketWeb{
				Region: d,
				Status: paaspb.PipelineStatus_PIPELINE_STATUS_UNSPECIFIED,
			}
		})

		// ttp web 流水线不会执行下面的实际逻辑
		// 一个区域在一条流水线中会有多个工单，需要找出最新的那个
		regionDeploymentOverviewsMap, err := s.getRegionDeploymentOverviewsMap(ctx, build, appID, deployStrategy)
		if err != nil {
			logs.CtxError(ctx, "getRegionDeploymentOverviewsMap failed, appid: %v, err: %s", appID, err.Error())
			return nil, err
		}

		regionTickets, err = s.computeWebDeployRegionTickets(ctx, regionDeploymentOverviewsMap, regionTickets, build, query.ForRollback, query.ForRollbackAtomVar)
		if err != nil {
			logs.CtxError(ctx, "computeWebDeployRegionTickets failed, appid: %v, err: %s", appID, err.Error())
			return nil, err
		}
	}

	// 获取正向工单时，需要过滤灰度工单
	// 用于回滚时不需要过滤灰度工单
	webDeployStatus := base.CalculateWebDeployStatus(regionTickets, !query.ForRollback)
	// 流水线运行完了，有区域因未开始而处于发布中，置为部署取消
	if build.RunStatus != dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING && build.RunStatus != dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_WAITING &&
		webDeployStatus == change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_DEPLOYING {
		webDeployStatus = change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_CANCELLED
	}
	// 流水线运行完了，有区域因未开始而处于回滚中，置为已回滚
	if build.RunStatus != dslpb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING && webDeployStatus == change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKING {
		webDeployStatus = change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKED
	}
	return &base.WebDeployTicketInfo{
		Ticket: &change_itempb.DeployTicketWeb{
			WebDeployStatus: webDeployStatus,
			RegionTickets:   regionTickets,
		},
		ChangeItem:  rtChangeItem,
		ExecutionId: execID,
	}, nil
}

func (s *atomSvc) getRegionDeploymentOverviewsMap(ctx context.Context, build *oreopb.PipelineRun, appID string, deployStrategy *paaspb.WebDeployStrategy) (map[uint64][]*WebDeploymentOverview, error) {
	regionDeploymentOverviewsMap := make(map[uint64][]*WebDeploymentOverview)

	for _, job := range build.JobRuns {
		logs.CtxInfo(ctx, "[GetWebTicketInfo] jobID %d", job.JobRunId)
		if !gslice.Contains([]string{ServiceGoofyDeployFullFlow, ServiceGoofyDeployAbCreate, ServiceGoofyDeployAb, ServiceGoofyDeployCanary}, job.JobAtom.UniqueId) {
			continue
		}
		// 🤮，在 boe，atom_param.appId 是 string
		// 🤮，在 cn，atom_param.appId 是 number
		rawFoundAppID, ok := job.JobAtom.Inputs.AsMap()[WebAppID]
		if !ok {
			continue
		}
		foundAppID, err := conv.String(rawFoundAppID)
		if err != nil {
			logs.CtxError(ctx, "failed to convert appID, appID: %v, err: %v", foundAppID, err)
			continue
		}
		logs.CtxInfo(ctx, "[GetWebTicketInfo] appID %v, foundAppID %v", appID, foundAppID)
		if foundAppID == appID {
			output := job.JobAtom.Output.AsMap()
			// 灰度原子从 execute中解析
			if job.JobAtom.UniqueId == ServiceGoofyDeployCanary {
				username := cdauth.Username(ctx)
				// 从原子输出中解析部署工单id
				res, err := s.pipelineCli.GetJobRun(ctx, &servicepb.GetJobRunRequest{
					RunId:         job.JobRunId,
					PipelineRunId: build.RunId,
					Username:      username,
					NeedOutputs:   true,
				})
				if err != nil {
					logs.CtxError(ctx, "GetJobRun, build: %v, err: %v", build.RunId, err)
					continue
				}

				for _, step := range res.GetJobRun().GetStepInfos() {
					if step.GetName() == "Execute" {
						output = step.GetOutputs().AsMap()
						break
					}
				}
			}

			deploymentID := s.getDeploymentID(ctx, output)
			if deploymentID == 0 {
				continue
			}

			// 从原子输出中解析部署原子和部署区域
			hasAtom, unitRegion, atomID, err := getRegionAndAtomID(ctx, output, deployStrategy, job.JobAtom.UniqueId)
			if err != nil {
				logs.CtxError(ctx, "[GetWebTicketInfo] getRegionAndAtomID, appID: %v, err: %v", foundAppID, err)
				return nil, err
			}
			if !hasAtom {
				continue
			}

			// 从原子输出中解析部署区域
			channelID := getChannelID(ctx, output)
			if channelID == 0 {
				logs.CtxWarn(ctx, "[GetWebTicketInfo] getChannelID = 0, appID: %v", foundAppID)
			}

			overview := &WebDeploymentOverview{
				DeploymentID: deploymentID,
				AtomID:       atomID,
				ChannelID:    channelID,
			}

			_, find := regionDeploymentOverviewsMap[unitRegion]
			if !find {
				regionDeploymentOverviewsMap[unitRegion] = make([]*WebDeploymentOverview, 0)
			}
			regionDeploymentOverviewsMap[unitRegion] = append(regionDeploymentOverviewsMap[unitRegion], overview)
		}
	}

	return regionDeploymentOverviewsMap, nil
}

func (s *atomSvc) computeWebDeployRegionTickets(
	ctx context.Context,
	regionDeploymentOverviewsMap map[uint64][]*WebDeploymentOverview,
	regionTickets []*change_itempb.RegionTicketWeb,
	build *oreopb.PipelineRun,
	forRollback, forRollbackAtomVar bool) (tickets []*change_itempb.RegionTicketWeb, err error) {
	tickets = make([]*change_itempb.RegionTicketWeb, 0, 2)

	g := new(errgroup.Group)
	mutex := sync.Mutex{}

	for region_, overviews_ := range regionDeploymentOverviewsMap {
		logs.CtxInfo(ctx, "computeWebDeployRegionTickets region: %s", region_)
		region, overviews := region_, overviews_
		g.Go(func() error {
			regionTicket := gslice.Find(regionTickets, func(r *change_itempb.RegionTicketWeb) bool {
				return r.Region == paaspb.GoofyDeployRegion(region)
			})

			if !regionTicket.IsOK() {
				return nil
			}

			mutex.Lock()
			tickets = append(tickets, *regionTicket.Ptr())
			defer mutex.Unlock()

			overviews = getLatestDeployOverview(overviews)

			// 该区域没有部署信息
			if len(overviews) == 0 {
				return nil
			}

			overview, info, err := s.findTicket(ctx, overviews, build, forRollback, forRollbackAtomVar)
			if err != nil {
				logs.CtxError(ctx, "failed to findTicket, err: %s", err.Error())
				return err
			}

			if overview == nil || info == nil {
				return nil
			}

			logs.CtxInfo(ctx, "[GetWebTicketInfo] deploymentID %d, status %v", overview.DeploymentID, paaspb.PipelineStatus(info.BaseInfo.Status))

			(*regionTicket.Ptr()).RawDeploymentId = overview.DeploymentID
			(*regionTicket.Ptr()).Status = paaspb.PipelineStatus(info.BaseInfo.Status)
			(*regionTicket.Ptr()).Region = paaspb.GoofyDeployRegion(region)
			(*regionTicket.Ptr()).DeployUnitId = uint64(info.BaseInfo.DeployUnitId)
			(*regionTicket.Ptr()).DeployUnitName = info.BaseInfo.DeployUnitName
			(*regionTicket.Ptr()).ChannelId = uint64(info.BaseInfo.ChannelId)
			(*regionTicket.Ptr()).ChannelName = info.BaseInfo.ChannelName
			(*regionTicket.Ptr()).ChannelType = paaspb.ChannelType(info.BaseInfo.ChannelType)
			(*regionTicket.Ptr()).CanaryType = paaspb.CanaryType(info.BaseInfo.ChannelCanaryType)
			(*regionTicket.Ptr()).ScmVersion = info.BaseInfo.ScmVersion
			(*regionTicket.Ptr()).ScmName = info.BaseInfo.ScmName
			(*regionTicket.Ptr()).ScmId = uint64(info.BaseInfo.ScmId)
			(*regionTicket.Ptr()).GoofyLink = fmt.Sprintf("%s/channel/%d/deploy?deploymentId=%d", config.Conf.Goofy.RowHost, info.BaseInfo.ChannelId, overview.DeploymentID)
			(*regionTicket.Ptr()).ScmLink = info.BaseInfo.ScmViewLink
			(*regionTicket.Ptr()).Desc = info.BaseInfo.Desc

			goofyRollbackLink, rollbackStatus, err := s.getRollbackInfo(ctx, overview, info, build.CreatedBy)
			if err != nil {
				logs.CtxError(ctx, "failed to getRollbackInfo, deploymentID: %v, err: %v", overview.DeploymentID, err)
				return err
			}

			(*regionTicket.Ptr()).GoofyRollbackLink = goofyRollbackLink
			(*regionTicket.Ptr()).RollbackStatus = rollbackStatus
			return nil
		})
	}

	if err = g.Wait(); err != nil {
		return nil, err
	}

	return tickets, nil
}

func (s *atomSvc) findTicket(ctx context.Context, overviews []*WebDeploymentOverview, build *pipelinepb.PipelineRun, forRollback, forRollbackAtomVar bool) (*WebDeploymentOverview, *goofy.FullDeploymentData, error) {
	g := new(errgroup.Group)
	mutex := sync.Mutex{}

	idxTicketInfoMap := make(map[int]*goofy.FullDeploymentData)
	ticketIdx := len(overviews) - 1
	hasProdDeploymentFinished := false
	prodDeploymentTicketIdx := -1
	canRollBackGrayTicketIdx := -1
	// 回滚获取正向工单，需要找是否有完成的
	for idx_, overview_ := range overviews {
		logs.CtxInfo(ctx, "findTicket deployment id: %d", overview_.DeploymentID)

		overview := overview_
		idx := idx_
		g.Go(func() error {
			info, err := s.goofySDK.GetDeploymentInfo(ctx, goofy.GetDeploymentInfoReq{
				DeploymentID: overview.DeploymentID,
			}, build.CreatedBy)

			if err != nil {
				logs.CtxError(ctx, "failed to get web deployment info, deploymentID: %v, err: %v", overview.DeploymentID, err)
				return bits_err.THIRDPARTYGOOFYDEPLOY.ErrGetWebDeploymentCN.PassThrough(err)
			}

			// 忽略测试小流量,https://cloud.bytedance.net/bam/rd/goofy.deploy.openapi/api_doc/show_doc?version=1.0.65&endpoint_id=731937
			if gslice.Contains([]int64{int64(paaspb.CanaryType_CANARY_TYPE_TEST)}, info.BaseInfo.ChannelCanaryType) {
				return nil
			}

			mutex.Lock()
			idxTicketInfoMap[idx] = info
			if paaspb.PipelineStatus(info.BaseInfo.Status) == paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED {
				ticketIdx = idx
			}
			if paaspb.PipelineStatus(info.BaseInfo.Status) == paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED &&
				(info.BaseInfo.ChannelCanaryType == int64(paaspb.CanaryType_CANARY_TYPE_PROD) ||
					info.BaseInfo.ChannelCanaryType == int64(paaspb.CanaryType_CANARY_TYPE_PROD_TEST) ||
					paaspb.ChannelType(info.BaseInfo.ChannelType) == paaspb.ChannelType_CHANNEL_TYPE_MAIN) {
				hasProdDeploymentFinished = true
				prodDeploymentTicketIdx = idx
			}

			if (paaspb.PipelineStatus(info.BaseInfo.Status) == paaspb.PipelineStatus_PIPELINE_STATUS_CANCELING ||
				paaspb.PipelineStatus(info.BaseInfo.Status) == paaspb.PipelineStatus_PIPELINE_STATUS_CANCELED ||
				paaspb.PipelineStatus(info.BaseInfo.Status) == paaspb.PipelineStatus_PIPELINE_STATUS_DEPLOYING) &&
				info.BaseInfo.ChannelCanaryType == int64(paaspb.CanaryType_CANARY_TYPE_GRAY) {
				canRollBackGrayTicketIdx = idx
			}
			mutex.Unlock()

			return nil
		})
	}
	if err := g.Wait(); err != nil {
		return nil, nil, err
	}

	if len(overviews) == 0 || len(idxTicketInfoMap) == 0 {
		return nil, nil, nil
	}
	// 非回滚逻辑
	if !forRollback {
		return overviews[len(overviews)-1], idxTicketInfoMap[len(overviews)-1], nil
	}

	// 如果有完成的全流量工单，直接返回
	if hasProdDeploymentFinished {
		return overviews[prodDeploymentTicketIdx], idxTicketInfoMap[prodDeploymentTicketIdx], nil
	}

	// 运行中，已取消，取消中的灰度原子支持回滚，直接返回
	if forRollbackAtomVar && canRollBackGrayTicketIdx > -1 {
		return overviews[canRollBackGrayTicketIdx], idxTicketInfoMap[canRollBackGrayTicketIdx], nil
	}

	if ticketIdx > -1 {
		return overviews[ticketIdx], idxTicketInfoMap[ticketIdx], nil
	}

	return nil, nil, nil
}

func (s *atomSvc) getRollbackInfo(ctx context.Context, overview *WebDeploymentOverview, info *goofy.FullDeploymentData, createdBy string) (
	goofyRollbackLink string, rollbackStatus paaspb.PipelineStatus, err error) {

	// 灰度频道将 频道被删除视为回滚成功
	if info.BaseInfo.ChannelCanaryType == int64(paaspb.CanaryType_CANARY_TYPE_GRAY) {
		if overview.ChannelID == 0 {
			return goofyRollbackLink, rollbackStatus, nil
		}

		channel, err := s.goofySDK.GetChannelInfo(ctx, goofy.GetChannelInfoReq{
			ChannelID: overview.ChannelID,
		}, createdBy)

		if err != nil {
			logs.CtxError(ctx, "failed to get web deployment info, deploymentID: %v, err: %v", overview.DeploymentID, err)
			return goofyRollbackLink, rollbackStatus, bits_err.THIRDPARTYGOOFYDEPLOY.ErrGetWebChannelsCN.PassThrough(err)
		}

		logs.CtxInfo(ctx, "[GetWebTicketInfo] channel status %d", channel.Channel.Status)

		if channel.Channel.Status == ChannelStatus_Delete {
			goofyRollbackLink = fmt.Sprintf("%s/channel/%d/deploy?deploymentId=%d", config.Conf.Goofy.RowHost, overview.ChannelID, overview.DeploymentID)
			rollbackStatus = paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED
		}
		return goofyRollbackLink, rollbackStatus, nil
	}

	if info.AssociatedRollbackDeployment != nil {
		goofyRollbackLink = fmt.Sprintf("%s/channel/%d/deploy?deploymentId=%d", config.Conf.Goofy.RowHost, info.AssociatedRollbackDeployment.ChannelId, info.AssociatedRollbackDeployment.Id)
		rollbackStatus = paaspb.PipelineStatus(info.AssociatedRollbackDeployment.Status)
	}

	return goofyRollbackLink, rollbackStatus, nil
}

func getLatestDeployOverview(overviews []*WebDeploymentOverview) []*WebDeploymentOverview {
	filterOverviews := make([]*WebDeploymentOverview, 0)
	for _, v := range overviews {
		// 小流量原子有且只有一个，直接返回小流量部署状态
		if gslice.Contains([]string{ServiceGoofyDeployAbCreate}, v.AtomID) {
			return []*WebDeploymentOverview{v}
		}

		if gslice.Contains([]string{ServiceGoofyDeployFullFlow, ServiceGoofyDeployCanary}, v.AtomID) {
			filterOverviews = append(filterOverviews, v)
			continue
		}
	}

	return filterOverviews
}

func (s *atomSvc) getDeploymentID(ctx context.Context, output map[string]any) uint64 {
	rawDeploymentID, ok := output[WebDeploymentID]
	// 当 Goofy Deploy 原子服务已运行，但还无 output 时，提供默认值
	logs.CtxInfo(ctx, "[GetWebTicketInfo] rawDeploymentID %v", rawDeploymentID)
	if !ok {
		return 0
	}

	deploymentID, err := conv.Uint64(rawDeploymentID)
	if err != nil {
		logs.CtxError(ctx, "failed to convert deployment, deploymentID: %v, err: %v", deploymentID, err)
		return 0
	}

	return deploymentID
}

func getChannelID(ctx context.Context, output map[string]any) uint64 {
	rawChannelID, ok := output[WebChannelID]
	// 当 Goofy Deploy 原子服务已运行，但还无 output 时，提供默认值
	logs.CtxInfo(ctx, "[GetWebTicketInfo] getChannelID %v", rawChannelID)
	if !ok {
		return 0
	}

	channelID, err := conv.Uint64(rawChannelID)
	if err != nil {
		logs.CtxError(ctx, "failed to convert deployment, getChannelID: %v, err: %v", rawChannelID, err)
		return 0
	}

	return channelID
}

func getRegionAndAtomID(ctx context.Context, output map[string]any, deployStrategy *paaspb.WebDeployStrategy, rawAtomID string) (find bool, unitRegion uint64, atomID string, err error) {
	if unitRegionStr, ok := output[WebDeployUnitRegion]; ok {
		unitRegion, err = conv.Uint64(unitRegionStr)
		if err != nil {
			logs.CtxError(ctx, "failed to convert unitRegion,  unitRegion %v, err: %v", unitRegion, err)
			return find, 0, "", err
		}
	}

	// 频道类型不用 获取到的区域主频道类型，而是直接用部署策略的频道类型
	strategy := gslice.Find(deployStrategy.GetStrategyRegions(), func(s *paaspb.WebRegionStrategy) bool {
		return uint64(s.GetRegion()) == unitRegion
	})
	if strategy.IsNil() {
		return find, 0, "", nil
	}

	// 如果选择了全流量部署，但工单类型不是全流量或灰度部署，则忽略
	if strategy.Value().GetChannelType() == paaspb.ChannelType_CHANNEL_TYPE_MAIN &&
		!gslice.Contains([]string{ServiceGoofyDeployFullFlow, ServiceGoofyDeployCanary}, rawAtomID) {
		return find, 0, "", nil
	}

	// 如果选择了正式小流量部署，但工单类型不是正式小流量，则忽略
	if strategy.Value().GetChannelType() == paaspb.ChannelType_CHANNEL_TYPE_CANARY &&
		!gslice.Contains([]string{ServiceGoofyDeployAbCreate, ServiceGoofyDeployAb}, rawAtomID) {
		return find, 0, "", nil
	}

	return true, unitRegion, rawAtomID, nil
}

//func CalculateWebDeployStatus(infos []*change_itempb.RegionTicketWeb) change_itempb.WebDeployStatus {
//	if len(infos) == 0 {
//		return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_NOTREADY
//	}
//	// 未发起过回滚
//	if gslice.All(infos, func(info *change_itempb.RegionTicketWeb) bool {
//		return info.RollbackStatus == paaspb.PipelineStatus_PIPELINE_STATUS_UNSPECIFIED
//	}) {
//		// 部署完成
//		if gslice.All(infos, func(info *change_itempb.RegionTicketWeb) bool {
//			return info.Status == paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED
//		}) {
//			return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_COMPLETED
//		}
//		// 部署失败
//		if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//			return gslice.Contains([]paaspb.PipelineStatus{
//				paaspb.PipelineStatus_PIPELINE_STATUS_DEPLOYFAILED,
//			}, info.Status)
//		}) {
//			return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_FAILED
//		}
//		// 部署取消中
//		if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//			return info.Status == paaspb.PipelineStatus_PIPELINE_STATUS_CANCELING
//		}) {
//			return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_CANCELLING
//		}
//		// 部署未开始
//		if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//			return info.Status == paaspb.PipelineStatus_PIPELINE_STATUS_QUEUING
//		}) {
//			return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_NOTREADY
//		}
//		// 部署取消
//		if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//			return gslice.Contains([]paaspb.PipelineStatus{
//				paaspb.PipelineStatus_PIPELINE_STATUS_CANCELED,
//				paaspb.PipelineStatus_PIPELINE_STATUS_REJECTED,
//			}, info.Status)
//		}) {
//			return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_CANCELLED
//		}
//		return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_DEPLOYING
//	}
//	// 已回滚
//	if gslice.All(infos, func(info *change_itempb.RegionTicketWeb) bool {
//		return info.RollbackStatus == paaspb.PipelineStatus_PIPELINE_STATUS_COMPLETED
//	}) {
//		return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKED
//	}
//	// 回滚失败
//	if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//		return info.RollbackStatus == paaspb.PipelineStatus_PIPELINE_STATUS_DEPLOYFAILED
//	}) {
//		return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKFAILED
//	}
//	// 回滚取消中
//	if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//		return info.RollbackStatus == paaspb.PipelineStatus_PIPELINE_STATUS_CANCELING
//	}) {
//		return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKCANCELLING
//	}
//	// 回滚取消
//	if gslice.Any(infos, func(info *change_itempb.RegionTicketWeb) bool {
//		return info.RollbackStatus == paaspb.PipelineStatus_PIPELINE_STATUS_CANCELED
//	}) {
//		return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKCANCELLED
//	}
//	// 回滚中
//	return change_itempb.WebDeployStatus_WEB_DEPLOY_STATUS_ROLLBACKING
//}
