package pipeline

import (
	"context"
	"reflect"

	"github.com/bytedance/mockey"
	"github.com/golang/mock/gomock"
	"github.com/nitrictech/protoutils"

	"code.byted.org/bits/monkey"
	cdauth "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/auth"
	oreoMock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/oreo_manager/mock"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane"
	rtprojectmock "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane/mock"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	"code.byted.org/devinfra/hagrid/pkg/tcc/apis"
)

func (t *PipelineOverviewTestSuite) Test_atomSvc_GetTCCTicketInfo() {
	rtProjectByControlPlane1 := rtprojectmock.NewMockRtProjectByControlPlaneService(t.ctrl)
	rtProjectControlPlane := rtprojectmock.NewMockRtProjectControlPlane(t.ctrl)
	defer monkey.Patch(cdauth.Username, func(ctx context.Context) string {
		return "test"
	}).UnPatch()
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	oreoManager := oreoMock.NewMockOreoManager(ctrl)
	t.Run("ticket id not found case", func() {
		output := map[string]interface{}{}
		out, _ := protoutils.NewStruct(output)
		build := &pipelinepb.PipelineRun{
			JobRuns: []*pipelinepb.JobRun{
				{
					JobName: ServiceCreateTCCTicket,
					JobAtom: &pipelinepb.JobAtom{
						UniqueId: ServiceCreateTCCTicket,
						Output:   out,
					},
				},
			},
		}
		oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(build, nil)
		svc := &atomSvc{
			rtProjectByControlPlane: rtProjectByControlPlane1,
			oreoManager:             oreoManager,
		}
		query := &TccTicketQuery{
			PipelineID:      114,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCC,
			ProjectUniqueId: "psm.tcc.fake",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
			TicketType:      TicketType_Forward,
		}
		rtProjectByControlPlane1.EXPECT().GetByProjectTypeAndControlPlane(gomock.Any(), gomock.Any(), gomock.Any()).Return(rtProjectControlPlane)
		rtProjectControlPlane.EXPECT().GetTicket(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apis.DeploymentResp{
			Data: apis.DeploymentInfo{
				Id:     514,
				Env:    "prod",
				Remark: "111111",
				Status: TccStatusFinished,
			},
		}, nil)
		res, err := svc.GetTCCTicketInfo(t.ctx, query)
		t.NoError(err)
		t.Equal(change_itempb.TccTicketStatus_TCC_TICKET_STATUS_NOT_CREATED, res.Status)
	})
	t.Run("deployment normal cn case", func() {
		output := map[string]interface{}{
			"deployment_id": "514",
		}
		out, _ := protoutils.NewStruct(output)
		build := &pipelinepb.PipelineRun{
			JobRuns: []*pipelinepb.JobRun{
				{
					JobName: ServiceCreateTCCTicket,
					JobAtom: &pipelinepb.JobAtom{
						UniqueId: ServiceCreateTCCTicket,
						Output:   out,
					},
				},
			},
		}
		oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(build, nil)
		svc := &atomSvc{
			rtProjectByControlPlane: rtProjectByControlPlane1,
			oreoManager:             oreoManager,
		}
		query := &TccTicketQuery{
			PipelineID:      114,
			ProjectType:     sharedpb.ProjectType_PROJECT_TYPE_TCC,
			ProjectUniqueId: "psm.tcc.fake",
			ControlPlane:    sharedpb.ControlPlane_CONTROL_PLANE_CN,
			TicketType:      TicketType_Forward,
		}

		rtProjectByControlPlane1.EXPECT().GetByProjectTypeAndControlPlane(gomock.Any(), gomock.Any(), gomock.Any()).Return(rtProjectControlPlane)
		rtProjectControlPlane.EXPECT().GetTicket(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apis.DeploymentResp{
			Data: apis.DeploymentInfo{
				Id:     514,
				Env:    "prod",
				Remark: "111111",
				Status: TccStatusFinished,
			},
		}, nil)
		res, err := svc.GetTCCTicketInfo(t.ctx, query)
		t.NoError(err)
		t.Equal(change_itempb.TccTicketStatus_TCC_TICKET_STATUS_FINISHED, res.Status)
		t.Equal("prod", res.Env)
	})

	t.Run("rollback normal cn case", func() {
		oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(&pipelinepb.PipelineRun{}, nil)
		svc := &atomSvc{
			rtProjectByControlPlane: rtProjectByControlPlane1,
			oreoManager:             oreoManager,
		}
		query := &TccTicketQuery{
			PipelineID:          114,
			ProjectType:         sharedpb.ProjectType_PROJECT_TYPE_TCC,
			ProjectUniqueId:     "psm.tcc.fake",
			ControlPlane:        sharedpb.ControlPlane_CONTROL_PLANE_CN,
			TicketType:          TicketType_Rollback,
			ForwardDeploymentId: 111,
		}
		defer monkey.PatchInstanceMethod(reflect.TypeOf(svc), "GetTCCRollbackTicketID", func(ctx context.Context, build *pipelinepb.PipelineRun) (ticketID string, err error) {
			return "mock", nil
		}).UnPatch()
		rtProjectByControlPlane1.EXPECT().GetByProjectTypeAndControlPlane(gomock.Any(), gomock.Any(), gomock.Any()).Return(rtProjectControlPlane).AnyTimes()
		rtProjectControlPlane.EXPECT().GetTicket(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apis.DeploymentResp{
			Data: apis.DeploymentInfo{
				Id:     514,
				Env:    "prod",
				Remark: "111111",
				Status: TccStatusFinished,
			},
		}, nil).AnyTimes()
		res, err := svc.GetTCCTicketInfo(t.ctx, query)
		t.NoError(err)
		t.Equal(change_itempb.TccTicketStatus_TCC_TICKET_STATUS_ROLLBACKED, res.RollbackStatus)
		t.Equal("514", res.RollbackId)
		t.Equal("111", res.Id)
		t.Equal("prod", res.Env)
	})
}

func (t *PipelineOverviewTestSuite) Test_atomSvc_GetTCCTicketID() {
	t.Run("normal cn case", func() {
		svc := &atomSvc{
			oreoManager:             t.mockOreoManager,
			rtProjectByControlPlane: rtProjectByControlPlane.NewRtProjectByControlPlaneSvc(),
		}
		t.mockOreoManager.EXPECT().GetOnePipeline(t.ctx, gomock.Any()).Return(&pipelinepb.Pipeline{
			LastRunId: 1,
		}, nil).AnyTimes()
		output := map[string]interface{}{
			"deployment_id": "514",
		}
		out, _ := protoutils.NewStruct(output)
		build := &pipelinepb.PipelineRun{
			JobRuns: []*pipelinepb.JobRun{
				{
					JobName: ServiceCreateTCCTicket,
					JobAtom: &pipelinepb.JobAtom{
						UniqueId: ServiceCreateTCCTicket,
						Output:   out,
					},
				},
			},
		}
		t.mockOreoManager.EXPECT().GetPipelineRun(t.ctx, gomock.Any(), gomock.Any()).Return(build, nil).AnyTimes()
		res, _, err := svc.GetTCCTicketID(t.ctx, build, 0)
		t.Equal("514", res)
		t.NoError(err)
	})
	t.Run("not created case", func() {
		svc := &atomSvc{
			oreoManager:             t.mockOreoManager,
			rtProjectByControlPlane: rtProjectByControlPlane.NewRtProjectByControlPlaneSvc(),
		}
		t.mockOreoManager.EXPECT().GetOnePipeline(t.ctx, gomock.Any()).Return(&pipelinepb.Pipeline{
			LastRunId: 1,
		}, nil).AnyTimes()
		output := map[string]interface{}{}
		out, _ := protoutils.NewStruct(output)
		build := &pipelinepb.PipelineRun{
			JobRuns: []*pipelinepb.JobRun{
				{
					JobName: ServiceCreateTCCTicket,
					JobAtom: &pipelinepb.JobAtom{
						UniqueId: ServiceCreateTCCTicket,
						Output:   out,
					},
				},
			},
		}
		t.mockOreoManager.EXPECT().GetPipelineRun(t.ctx, gomock.Any(), gomock.Any()).Return(build, nil).AnyTimes()
		res, _, err := svc.GetTCCTicketID(t.ctx, build, 0)
		t.Equal("", res)
		t.NoError(err)
	})
}

func (t *PipelineOverviewTestSuite) Test_GetTCCRollbackTicketID() {

	svc := &atomSvc{}

	t.Run("no build", func() {
		output := map[string]interface{}{}
		out, _ := protoutils.NewStruct(output)
		build := &pipelinepb.PipelineRun{
			JobRuns: []*pipelinepb.JobRun{
				{
					JobName: ServiceCreateTCCTicket,
					JobAtom: &pipelinepb.JobAtom{
						UniqueId: ServiceCreateTCCTicket,
						Output:   out,
					},
				},
			},
		}
		id, err := svc.GetTCCRollbackTicketID(t.ctx, build)
		t.Equal("", id)
		t.NoError(err)
	})

	t.Run("normal case", func() {
		output := map[string]interface{}{
			TccRollbackTicketID: 111,
		}
		out, _ := protoutils.NewStruct(output)
		build := &pipelinepb.PipelineRun{
			JobRuns: []*pipelinepb.JobRun{
				{
					JobAtom: &pipelinepb.JobAtom{
						UniqueId: ServiceTCCRollback,
						Output:   out,
					},
				},
			},
		}
		id, err := svc.GetTCCRollbackTicketID(t.ctx, build)
		t.Equal("111", id)
		t.NoError(err)
	})
}

func (t *PipelineOverviewTestSuite) Test_GetTCCRollbackTicketInfo() {
	ctrl := gomock.NewController(t.T())
	defer ctrl.Finish()
	oreoManager := oreoMock.NewMockOreoManager(ctrl)
	oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(&pipelinepb.PipelineRun{}, nil)

	svc := &atomSvc{}

	t.Run("invalid type", func() {
		_, _, err := svc.GetTCCRollbackTicketInfo(t.ctx, &TccTicketQuery{
			TicketType: TicketType_Forward,
		})
		t.Error(err)
	})

	t.Run("no build", func() {
		_, _, err := svc.GetTCCRollbackTicketInfo(t.ctx, &TccTicketQuery{
			TicketType: TicketType_Rollback,
		})
		t.Error(err)
	})

	rtProjectByControlPlane1 := rtprojectmock.NewMockRtProjectByControlPlaneService(t.ctrl)
	rtProjectControlPlane := rtprojectmock.NewMockRtProjectControlPlane(t.ctrl)
	defer monkey.Patch(cdauth.Username, func(ctx context.Context) string {
		return "test"
	}).UnPatch()
	t.Run("normal case", func() {
		svc := &atomSvc{
			rtProjectByControlPlane: rtProjectByControlPlane1,
			oreoManager:             oreoManager,
		}
		query := &TccTicketQuery{
			PipelineID:          114,
			ProjectType:         sharedpb.ProjectType_PROJECT_TYPE_TCC,
			ProjectUniqueId:     "psm.tcc.fake",
			ControlPlane:        sharedpb.ControlPlane_CONTROL_PLANE_CN,
			TicketType:          TicketType_Rollback,
			ForwardDeploymentId: 111,
		}

		defer monkey.PatchInstanceMethod(reflect.TypeOf(svc), "GetTCCRollbackTicketID", func(ctx context.Context, build *pipelinepb.PipelineRun) (ticketID string, err error) {
			return "222", nil
		}).UnPatch()
		rtProjectByControlPlane1.EXPECT().GetByProjectTypeAndControlPlane(gomock.Any(), gomock.Any(), gomock.Any()).Return(rtProjectControlPlane).AnyTimes()
		rtProjectControlPlane.EXPECT().GetTicket(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apis.DeploymentResp{
			Data: apis.DeploymentInfo{
				Id:     222,
				Env:    "prod",
				Remark: "111111",
				Status: TccStatusFinished,
				ConfigChanges: []apis.ConfigChanges{
					{},
				},
			},
		}, nil).AnyTimes()
		res, status, err := svc.GetTCCRollbackTicketInfo(t.ctx, query)
		t.Equal(int64(222), res.Id)
		t.Len(res.ConfigChanges, 1)
		t.Equal(change_itempb.TccTicketStatus_TCC_TICKET_STATUS_ROLLBACKED, status)
		t.NoError(err)
	})
}

func (t *PipelineOverviewTestSuite) Test_GetTCCTicketToVersion() {
	oreoManager := oreoMock.NewMockOreoManager(t.ctrl)
	oreoManager.EXPECT().GetSpecificOrLatestPipelineRun(gomock.Any(), gomock.Any()).Return(&pipelinepb.PipelineRun{}, nil)
	rtProjectByControlPlane1 := rtprojectmock.NewMockRtProjectByControlPlaneService(t.ctrl)
	rtProjectControlPlane := rtprojectmock.NewMockRtProjectControlPlane(t.ctrl)
	defer mockey.Mock((*atomSvc).GetTCCTicketID).Return("111", "222", nil).Build().UnPatch()
	defer mockey.Mock(cdauth.Username).Return("test").Build().UnPatch()
	rtProjectByControlPlane1.EXPECT().GetByProjectTypeAndControlPlane(gomock.Any(), gomock.Any(), gomock.Any()).Return(rtProjectControlPlane)
	rtProjectControlPlane.EXPECT().GetTicket(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apis.DeploymentResp{
		Data: apis.DeploymentInfo{
			Status: TccStatusFinished,
			ConfigChanges: []apis.ConfigChanges{
				{},
			},
		},
	}, nil)
	svc := &atomSvc{
		rtProjectByControlPlane: rtProjectByControlPlane1,
		oreoManager:             oreoManager,
	}

	changes, status, ticketID, jobID, err := svc.GetTCCTicketToVersion(t.ctx, &TccTicketQuery{
		PipelineID: 1,
	})
	t.Len(changes, 1)
	t.Equal(change_itempb.TccTicketStatus_TCC_TICKET_STATUS_FINISHED, status)
	t.Equal("111", ticketID)
	t.Equal("222", jobID)
	t.NoError(err)
}
