load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "model",
    srcs = [
        "gecko_common_mapper.go",
        "gecko_ies_mapper.go",
        "gecko_model.go",
        "gecko_tt_mapper.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/pipeline/overview/model",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/cd/change_item:change_item_go_proto",
        "//idls/byted/devinfra/cd/paas:paas_go_proto",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_lang_gg//gslice",
    ],
)
