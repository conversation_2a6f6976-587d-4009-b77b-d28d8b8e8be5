package model

import (
	"fmt"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
)

func GeckoTicketItemMapper(p GeckoPackageInfo) *change_itempb.TicketGeckoItem {
	geckoResourceType := paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_UNSPECIFIED
	if p.IsPackageOnline == 1 {
		geckoResourceType = paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_ONLINE
	} else if p.IsPackageOnline == 0 {
		geckoResourceType = paaspb.GeckoResourceType_GECKO_RESOURCE_TYPE_OFFLINE
	}
	return &change_itempb.TicketGeckoItem{
		GeckoAppId:          uint64(p.AppID),
		GeckoAppName:        p.AppName,
		GeckoChannelId:      uint64(p.ChannelID),
		GeckoChannelName:    p.ChannelName,
		GeckoDeploymentId:   uint64(p.DeploymentID),
		GeckoDeploymentName: p.DeploymentName,
		PackageId:           fmt.Sprintf("%d", p.PackageID),
		Region:              p.Region,
		ResourceType:        geckoResourceType,
		GeckoLink:           p.GeckoLink,
		Platform:            p.Platform,
		GeckoEnvType:        paaspb.GeckoEnvType_GECKO_ENV_TYPE_PROD,
		JobId:               p.JobID,
		JobIndex:            p.JobIndex,
	}
}
