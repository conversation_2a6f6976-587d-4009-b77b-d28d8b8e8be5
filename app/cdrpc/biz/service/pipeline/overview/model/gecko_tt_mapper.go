package model

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/lang/gg/gslice"
)

func TTGeckoPackageInfoMapper(gecko []JobTTGeckoPackageInfoItem, jobID uint64, jobIndex string) []GeckoPackageInfo {
	return gslice.Map(gecko, func(item JobTTGeckoPackageInfoItem) GeckoPackageInfo {
		return GeckoPackageInfo{
			AppID:           item.AppID,
			AppName:         item.AppName,
			ChannelName:     item.ChannelName,
			ChannelID:       item.ChannelID,
			DeploymentID:    item.DeploymentID,
			DeploymentName:  item.DeploymentName,
			PackageID:       item.PackageID,
			IsPackageOnline: item.IsPackageOnline,
			GeckoLink:       item.GeckoLink,
			Region:          TransformTTGeckoRegion(item.Region),
			Platform:        paaspb.GeckoPlatformType_GECKO_PLATFORM_TYPE_TT,
			JobID:           jobID,
			JobIndex:        jobIndex,
		}
	})
}

func TransformTTGeckoRegion(region string) paaspb.GeckoControlPlane {
	switch region {
	case "row":
		return paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_TT_ROW
	case "eu-ttp":
		return paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_EU_TTP
	case "us-ttp":
		return paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_US_TTP
	default:
		return paaspb.GeckoControlPlane_GECKO_CONTROL_PLANE_UNSPECIFIED
	}
}
