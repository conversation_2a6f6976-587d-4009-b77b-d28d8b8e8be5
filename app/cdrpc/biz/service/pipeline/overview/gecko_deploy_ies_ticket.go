package pipeline

import (
	"context"
	"strconv"

	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql/entity"
	cdutils "code.byted.org/devinfra/hagrid/app/cdrpc/utils"
	commonUtil "code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/oreo/pipelinepb"
	geckoSDK "code.byted.org/devinfra/hagrid/pkg/gecko"
	"code.byted.org/gopkg/logs"
	"code.byted.org/iesarch/cdaas_utils/utils"
	"code.byted.org/lang/gg/gslice"
	"golang.org/x/sync/errgroup"
)

type IESCreatePackageJobInfo struct {
	ChannelID       uint64
	IsPackageOnline bool
	PackageID       string
	WorkflowID      string
	JobID           string
	JobRunID        uint64
	TicketStatus    geckoSDK.WorkflowStatusEnum
}

type IESTicketStatusMap map[string]geckoSDK.WorkflowStatusEnum

const (
	GeckoIsPackageOnlineKey = "GECKO_isPackageOnline"
	GeckoPackageIDKey       = "packageId"
	GeckoWorkflowIDKey      = "workflowId"
	GeckoChannelIDKey       = "channelId"
)

func (s *atomSvc) FillIESTicket(ctx context.Context, tickets []*change_itempb.TicketGeckoItem, jobs []*pipelinepb.JobRun, rollbackInfo *entity.DBProjectRollbackInfo, rollbackPackages []*paaspb.GeckoRollbackPayloadItem) error {
	g := errgroup.Group{}

	// 1. get ticket status
	ticketInfo := make([]IESCreatePackageJobInfo, 0)
	g.Go(func() (err error) {
		ticketInfo, err = getIESTicketStatus(ctx, jobs)
		if err != nil {
			logs.CtxError(ctx, "[FillIESTicket] getIESTicketStatus error: %s", err.Error())
			return err
		}

		return nil
	})

	// 2. get ies rollback ticket info
	rollbackTicketInfo := make([]IESRollbackTicketInfo, 0)
	g.Go(func() (err error) {
		if rollbackInfo == nil {
			return nil
		}
		rollbackTicketInfo, err = s.getIESRollbackTicketStatus(ctx, rollbackInfo, rollbackPackages)
		if err != nil {
			logs.CtxError(ctx, "[FillIESTicket] getIESRollbackTicketStatus error: %s", err.Error())
			return err
		}

		return nil
	})

	if err := g.Wait(); err != nil {
		return err
	}
	logs.CtxInfo(ctx, "[FillIESTicket] ticketInfo: %s, rollbackTicketInfo: %s", commonUtil.ToJson(ticketInfo), commonUtil.ToJson(rollbackTicketInfo))

	// 3. fill ticket info
	for _, ticket := range tickets {
		curRawTicketInfo := gslice.Find(ticketInfo, func(item IESCreatePackageJobInfo) bool {
			return isPackageMatch(IsPackageMatchParams[uint64, bool]{
				id1:             item.ChannelID,
				id2:             ticket.GeckoChannelId,
				resourceType:    ticket.ResourceType,
				isPackageOnline: item.IsPackageOnline,
				// ies 暂时先不拿 region 匹配了
				region1: ticket.Region,
				region2: ticket.Region,
			})
		})
		if !curRawTicketInfo.IsOK() {
			logs.CtxWarn(ctx, "[FillIESTicket] ticket not found, channelId: %v, resourceType: %v", ticket.GeckoChannelId, ticket.ResourceType.String())
			continue
		}
		curRawRollbackTicketInfo := gslice.Find(rollbackTicketInfo, func(item IESRollbackTicketInfo) bool {
			return item.ChannelID == ticket.GeckoChannelId && item.ResourceType == ticket.ResourceType
		})
		ticketStatus := convertIESTicketStatus(curRawTicketInfo.Value().TicketStatus, curRawRollbackTicketInfo.IsOK(), curRawRollbackTicketInfo.Value().RollbackStatus)
		ticket.Status = change_itempb.GeckoTicketStatus(ticketStatus)
		ticket.TicketStatus = ticketStatus
		ticket.JobIndex = curRawTicketInfo.Value().JobID
		ticket.JobId = curRawTicketInfo.Value().JobRunID
		ticket.PackageId = curRawTicketInfo.Value().PackageID
	}

	return nil
}

func getIESTicketStatus(ctx context.Context, jobs []*pipelinepb.JobRun) ([]IESCreatePackageJobInfo, error) {
	// 1. get ies ticket create package jobs
	matchedJobs := filterMapIESTicketCreatePackageJobs(jobs)
	logs.CtxInfo(ctx, "[getIESTicketStatus] matchedJobs %s", commonUtil.ToJson(matchedJobs))

	// 2. get ies ticket status
	ticketStatusMap, err := getIESTicketStatusByWorkflowID(ctx, gslice.Map(matchedJobs, func(item IESCreatePackageJobInfo) string {
		return item.WorkflowID
	}))
	if err != nil {
		logs.CtxError(ctx, "[getIESTicketStatus] getIESTicketStatusByWorkflowID error: %s", err.Error())
		return nil, err
	}
	logs.CtxInfo(ctx, "[getIESTicketStatus] ticketStatusMap %s", commonUtil.ToJson(ticketStatusMap))

	// 3. fill ticket status
	return gslice.Map(matchedJobs, func(item IESCreatePackageJobInfo) IESCreatePackageJobInfo {
		if status, ok := ticketStatusMap[item.WorkflowID]; ok {
			item.TicketStatus = status
		} else {
			item.TicketStatus = geckoSDK.GeckoIESWorkflowStatusUnspecified
		}
		return item
	}), nil
}

func getIESTicketStatusByWorkflowID(ctx context.Context, workflowIDs []string) (IESTicketStatusMap, error) {
	g := errgroup.Group{}
	statusChan := make(chan IESTicketStatusMap, len(workflowIDs))
	jwt, err := cdutils.GetUserJWT(ctx)
	if err != nil {
		logs.CtxError(ctx, "[getIESTicketStatus] GetUserJWT error: %s", err.Error())
		return nil, err
	}

	for _, w := range workflowIDs {
		id := w
		g.Go(func() error {
			defer utils.PanicGuard(ctx)

			status, err := geckoSDK.GetGeckoSDK().IESSDK.GetTicketInfo(ctx, jwt, id)
			if err != nil {
				logs.CtxError(ctx, "[getIESTicketStatus] get ticket info error: %s", err.Error())
				return err
			}

			statusChan <- IESTicketStatusMap{
				id: status,
			}
			return nil
		})
	}

	err = g.Wait()
	close(statusChan)
	if err != nil {
		return nil, err
	}

	res := IESTicketStatusMap{}
	for status := range statusChan {
		for k, v := range status {
			res[k] = v
		}
	}

	return res, nil
}

func filterMapIESTicketCreatePackageJobs(jobs []*pipelinepb.JobRun) (matchedJobs []IESCreatePackageJobInfo) {
	matchedJobs = make([]IESCreatePackageJobInfo, 0)
	for _, job := range jobs {
		if job.GetJobAtom().GetUniqueId() != ServiceGeckoOfficialCreatePackage {
			continue
		}
		matchedJobItem := IESCreatePackageJobInfo{}
		outputMap := job.GetJobAtom().GetOutput().AsMap()
		if workflowID, ok := outputMap[GeckoWorkflowIDKey].(string); !ok {
			continue
		} else if workflowID != "" {
			matchedJobItem.WorkflowID = workflowID
		}
		if isPackageOnline, ok := outputMap[GeckoIsPackageOnlineKey].(string); !ok {
			continue
		} else {
			matchedJobItem.IsPackageOnline = isPackageOnline == "1"
		}
		if packageID, ok := outputMap[GeckoPackageIDKey].(string); !ok {
			continue
		} else {
			matchedJobItem.PackageID = packageID
		}
		if channelID, ok := outputMap[GeckoChannelIDKey].(string); !ok {
			continue
		} else {
			if id, err := strconv.ParseUint(channelID, 10, 64); err != nil {
				logs.CtxInfo(context.Background(), "failed to convert deployment, getChannelID: %v, err: %v", channelID, err)
				continue
			} else {
				matchedJobItem.ChannelID = id
			}
		}
		matchedJobItem.JobID = job.GetJobId()
		matchedJobItem.JobRunID = job.GetJobRunId()

		matchedJobs = append(matchedJobs, matchedJobItem)
	}

	return matchedJobs
}

// 正向工单状态为回滚时，判断回滚工单状态
func getIESTicketRollbackStatus(hasRollback bool, rollbackTicketStatus geckoSDK.WorkflowStatusEnum) paaspb.GeckoTicketOriginStatus {
	if hasRollback && rollbackTicketStatus == geckoSDK.GeckoIESWorkflowStatusFinished {
		return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_ROLLBACKED
	} else if rollbackTicketStatus == geckoSDK.GeckoIESWorkflowStatusCANCELED {
		return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_CANCELLED
	}
	return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_ROLLBACKING
}

func convertIESTicketStatus(status geckoSDK.WorkflowStatusEnum, hasRollback bool, rollbackTicketStatus geckoSDK.WorkflowStatusEnum) paaspb.GeckoTicketOriginStatus {
	switch status {
	case geckoSDK.GeckoIESWorkflowStatusFinished:
		return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_SUCCESS
	case geckoSDK.GeckoIESWorkflowStatusRUNNING:
		return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_RUNNING
	case geckoSDK.GeckoIESWorkflowStatusCANCELED:
		return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_CANCELLED
	case geckoSDK.GeckoIESWorkflowStatusRollbacked:
		return getIESTicketRollbackStatus(hasRollback, rollbackTicketStatus)
	default:
		return paaspb.GeckoTicketOriginStatus_GECKO_TICKET_ORIGIN_STATUS_UNSPECIFIED
	}
}
