load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "executor",
    srcs = ["executor.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/utils/executor",
    visibility = ["//visibility:public"],
    deps = [
        "@org_byted_code_gopkg_logs//:logs",
        "@org_golang_x_sync//errgroup",
    ],
)

go_test(
    name = "executor_test",
    srcs = ["executor_test.go"],
    deps = [
        ":executor",
        "@com_github_stretchr_testify//assert",
    ],
)
