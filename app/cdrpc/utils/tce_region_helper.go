package utils

import (
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tce"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/change_itempb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/sharedpb"
	"code.byted.org/iesarch/cdaas_utils/erri"
	"code.byted.org/iesarch/paas_sdk/tce_config"
)

const (
	BitsControlPlaneCN     = "CN"
	BitsControlPlaneI18N   = "I18N"
	BitsControlPlaneI18NBD = "I18N-BD"
	BitsControlPlaneEUTTP  = "EU-TTP"
	BitsControlPlaneUSTTP  = "US-TTP"
)

var BitsToTceControlPlaneMap = map[string]TCEAtomServiceEnvValue{
	BitsControlPlaneCN:     TCEServiceEnvTCE,
	BitsControlPlaneI18N:   TCEServiceEnvI18N,
	BitsControlPlaneI18NBD: TCEServiceEnvI18NBD,
	BitsControlPlaneEUTTP:  TCEServiceEnvEUTTP,
	BitsControlPlaneUSTTP:  TCEServiceEnvTTP,
}

// 映射 TCE 升级工单原子服务里设置的 service_env 值
type TCEAtomServiceEnvValue string

const (
	TCEServiceEnvUnknown    TCEAtomServiceEnvValue = ""
	TCEServiceEnvBOE        TCEAtomServiceEnvValue = "boe"
	TCEServiceEnvTCE        TCEAtomServiceEnvValue = "tce"
	TCEServiceEnvI18N       TCEAtomServiceEnvValue = "tce_i18n"
	TCEServiceEnvI18NBD     TCEAtomServiceEnvValue = "i18nbd"
	TCEServiceEnvI18Nmaliva TCEAtomServiceEnvValue = "tce_maliva"
	TCEServiceEnvI18Nus     TCEAtomServiceEnvValue = "tce_us"
	TCEServiceEnvTTP        TCEAtomServiceEnvValue = "tx"
	TCEServiceEnvEUTTP      TCEAtomServiceEnvValue = "eu_ttp"
)

// TODO: 移除重复代码 cms
func GetTCEConfigByServiceEnv(env TCEAtomServiceEnvValue) (tce.TCEIDC, tce.TCEEnv, error) {
	switch env {
	case TCEServiceEnvTCE:
		return tce_config.IDC_CN, tce_config.ENV_PROD, nil
	case TCEServiceEnvBOE:
		return tce_config.IDC_CN, tce_config.ENV_BOE, nil
	case TCEServiceEnvI18N:
		return tce_config.IDC_I18N, tce_config.ENV_PROD, nil
	case TCEServiceEnvI18NBD:
		return tce_config.IDC_I18NBD, tce_config.ENV_PROD, nil
	case TCEServiceEnvTTP:
		return tce_config.IDC_TTP, tce_config.ENV_PROD, nil
	case TCEServiceEnvEUTTP:
		return tce_config.IDC_EU_TTP, tce_config.ENV_PROD, nil
	default:
		return "", "", erri.Errorf("can't find tce idc info by service env %s", env)
	}
}

// TODO: 移除重复代码 cms
func GetTCEServiceEnvByControlPlane(controlPlane sharedpb.ControlPlane) TCEAtomServiceEnvValue {
	switch controlPlane {
	case sharedpb.ControlPlane_CONTROL_PLANE_CN:
		return TCEServiceEnvTCE
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N:
		return TCEServiceEnvI18N
	case sharedpb.ControlPlane_CONTROL_PLANE_I18N_BD:
		return TCEServiceEnvI18NBD
	case sharedpb.ControlPlane_CONTROL_PLANE_TTP:
		return TCEServiceEnvTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_US_TTP:
		return TCEServiceEnvTTP
	case sharedpb.ControlPlane_CONTROL_PLANE_EU_TTP:
		return TCEServiceEnvEUTTP
	default:
		return TCEServiceEnvUnknown
	}
}

// GetPBTceDpStatus 获取 tce proto dp status
func GetPBTceDpStatus(status string) change_itempb.DpStatus {
	switch status {
	case "finished":
		return change_itempb.DpStatus_DP_STATUS_FINISH
	case "cancelled":
		return change_itempb.DpStatus_DP_STATUS_CANCEL
	case "rollbacked":
		return change_itempb.DpStatus_DP_STATUS_ROLLBACK

	case "checking":
		return change_itempb.DpStatus_DP_STATUS_CHECKING
	case "running":
		return change_itempb.DpStatus_DP_STATUS_RUNNING
	case "rollbacking":
		return change_itempb.DpStatus_DP_STATUS_ROLLBACKING
	case "cancelling":
		return change_itempb.DpStatus_DP_STATUS_CANCELLING
	case "pending":
		return change_itempb.DpStatus_DP_STATUS_PENDING

	case "failed":
		return change_itempb.DpStatus_DP_STATUS_FAILED
	default:
		return change_itempb.DpStatus_DP_STATUS_UNSPECIFIED
	}
}
