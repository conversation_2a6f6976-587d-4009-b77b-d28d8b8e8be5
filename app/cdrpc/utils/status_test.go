package utils

import (
	"testing"

	release_ticket_sharedpb "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb"
	"github.com/stretchr/testify/assert"
)

func TestIsReleaseTicketFinalStatus(t *testing.T) {
	is := IsReleaseTicketFinalStatus(release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_BEFORE_RELEASE)
	assert.False(t, is)

	is = IsReleaseTicketFinalStatus(release_ticket_sharedpb.ReleaseTicketStatus_RELEASE_TICKET_STATUS_RELEASED)
	assert.True(t, is)
}

func Test_ConvertTaskTabPipelineStatusToPipelineStatus(t *testing.T) {
	s := ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_UNSPECIFIED)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_UNSPECIFIED)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_NOT_STARTED)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_NOT_STARTED)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_RUNNING)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_RUNNING)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_SUCCEEDED)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_SUCCEEDED)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_PENDING)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_PENDING)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_FAILED)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_FAILED)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_CANCELING)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_CANCELING)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_CANCELED)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_CANCELED)

	s = ConvertTaskTabPipelineStatusToPipelineStatus(release_ticketpb.TaskTabPipelineStatus_TASK_TAB_PIPELINE_STATUS_MULTIPLE)
	assert.Equal(t, s, release_ticket_sharedpb.PipelineStatus_PIPELINE_STATUS_MULTIPLE)

}
