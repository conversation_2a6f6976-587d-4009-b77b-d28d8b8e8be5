package utils

import (
	"path/filepath"
	"strings"

	"code.byted.org/lang/gg/gslice"
)

func ValidateMonoSubPath(path string) bool {
	return !gslice.Contains([]string{"", "."}, path) && !strings.HasPrefix(path, "/")
}

// 检查 cleanB 是否是 cleanA 的子路径
func IsSubPath(a string, b string) (bool, error) {
	// 获取绝对路径
	absA, err := filepath.Abs(a)
	if err != nil {
		return false, err
	}

	absB, err := filepath.Abs(b)
	if err != nil {
		return false, err
	}

	// 使用 filepath.Clean 规范化路径
	cleanA := filepath.Clean(absA)
	cleanB := filepath.Clean(absB)

	// 检查 cleanB 是否是 cleanA 的子路径
	return strings.HasPrefix(cleanB, cleanA), nil
}
