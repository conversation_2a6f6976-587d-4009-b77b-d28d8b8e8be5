CREATE TABLE IF NOT EXISTS `hagrid_cd_node`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `node_id`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'NodeID',
    `workflow_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'WorkflowID',
    `name`          varchar(255)        NOT NULL DEFAULT '' COMMENT '名称',
    `name_i18n`     varchar(255)        NOT NULL DEFAULT '' COMMENT '英文名称',
    `node_type`     tinyint(4)          NOT NULL DEFAULT '0' COMMENT '节点类型',
    `prev_node_ids` json                NOT NULL COMMENT '前置节点ID列表',
    `next_node_ids` json                NOT NULL COMMENT '后置节点ID列表',
    `enabled`       tinyint(4)          NOT NULL DEFAULT '0' COMMENT '是否启用. 0: false 1: true',
    `edit_disabled` tinyint(4)          NOT NULL DEFAULT '0' COMMENT '是否不可编辑. 0: false 1: true',
    `node_config`   json                NOT NULL COMMENT '流程节点配置信息',
    `version`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '版本',
    `created_at`    datetime            NOT NULL COMMENT '创建时间',
    `updated_at`    datetime            NOT NULL COMMENT '更新时间',
    `deleted_at`    datetime                     DEFAULT NULL COMMENT '删除时间',
    `creator`       varchar(255)        NOT NULL DEFAULT '' COMMENT '创建人',
    `updater`       varchar(255)        NOT NULL DEFAULT '' COMMENT '更新人',
    PRIMARY KEY (`id`),
    KEY `idx_workflow_id_node_id` (`workflow_id`, `node_id`)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COMMENT ='发布单流程模板节点表'