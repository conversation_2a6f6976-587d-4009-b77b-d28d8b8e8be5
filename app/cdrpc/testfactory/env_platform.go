package testfactory

import (
	"context"

	"code.byted.org/iesarch/paas_sdk/env_platform"
)

type fakeEnvPlatformSDK struct {
	env_platform.EnvPlatformSDK
}

func NewMockEnvPlatformSDK() env_platform.EnvPlatformSDK {
	return &fakeEnvPlatformSDK{}
}

func (s *fakeEnvPlatformSDK) Upgrade(ctx context.Context, tceEnv string, psm string, appEnv string, scmRepos []env_platform.RepoInfo, clusters []int, comment string) (int64, error) {
	return 1234, nil
}

func (s *fakeEnvPlatformSDK) DeploymentInfo(ctx context.Context, id int64) (*env_platform.DeploymentInfoV4, error) {
	return &env_platform.DeploymentInfoV4{
		ID:             1234,
		Env:            "",
		Channel:        "",
		Comment:        "",
		Status:         "Succeed",
		Message:        "",
		ErrCode:        "",
		DeploymentInfo: nil,
	}, nil
}

func (s *fakeEnvPlatformSDK) CheckIfServiceExistInAppEnv(ctx context.Context, appEnv, serviceType, servicePSM string) (exist bool, serviceInfo *env_platform.Item, err error) {
	return true, nil, nil
}

func (s *fakeEnvPlatformSDK) DeleteMultipleService(ctx context.Context, sense string, psmList []string, env string, jwtToken string) (int64, error) {
	return 1234, nil
}
