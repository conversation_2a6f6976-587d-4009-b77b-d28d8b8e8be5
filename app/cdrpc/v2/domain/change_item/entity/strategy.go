package entity

import "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb"

type ChangeItemDeployStrategy struct {
	TceBaseScmArtifacts []*SCMArtifact     `json:"tce_base_scm_artifacts,omitempty"`
	ClusterInfo         *ClusterInfo       `json:"cluster_info,omitempty"`
	ScmArtifacts        []*SCMArtifact     `json:"scm_artifacts,omitempty"`
	ScmMergeBuildInfo   *ScmMergeBuildInfo `json:"scm_merge_build_info,omitempty"`
	Strategy            ChangeItemDeployStrategyTyped
}
type ClusterInfo struct {
	UpgradeClusterSet *UpgradeClusterSet `json:"upgrade_cluster_set,omitempty"`
	SurgeConfig       *SurgeConfig       `json:"surge_config,omitempty"`
	RolloutStrategy   string             `json:"rollout_strategy,omitempty"`
}

type SurgeConfig struct {
	Switch         bool   `json:"switch,omitempty"`
	SurgePercent   *int64 `json:"surge_percent,omitempty"`
	MinReadySecond *int64 `json:"min_ready_second,omitempty"`
}

type ServiceClusterInfo struct {
	ClusterId    int64 `json:"cluster_id,omitempty"`
	IsNewCluster bool  `json:"is_new_cluster,omitempty"`
}
type UpgradeClusterSet struct {
	Idc                 string                `json:"idc,omitempty"`
	ServiceId           int32                 `json:"service_id,omitempty"`
	SelectAll           bool                  `json:"select_all,omitempty"`
	UpgradeMode         string                `json:"upgrade_mode,omitempty"`
	SelectedClusters    []int64               `json:"selected_clusters,omitempty"`
	StageSet            *StageSet             `json:"stage_set,omitempty"`
	ServiceClusterInfos []*ServiceClusterInfo `json:"service_cluster_infos,omitempty"`
}

type StageSet struct {
	AllDc    []*AllDc    `json:"all_dc,omitempty"`
	Canary   []*CanaryDc `json:"canary,omitempty"`
	SingleDc []*SingleDc `json:"single_dc,omitempty"`
}

type AllDc struct {
	AutoDeploy bool    `json:"auto_deploy,omitempty"`
	Steps      []*Step `json:"steps,omitempty"`
}
type SingleDc struct {
	AutoDeploy bool    `json:"auto_deploy,omitempty"`
	Steps      []*Step `json:"steps,omitempty"`
}
type CanaryDc struct {
	AutoDeploy bool    `json:"auto_deploy,omitempty"`
	Steps      []*Step `json:"steps,omitempty"`
}

type Step struct {
	ClusterId           int64  `json:"cluster_id,omitempty"`
	IsStandaloneRelease bool   `json:"is_standalone_release,omitempty"`
	Disable             bool   `json:"disable,omitempty"`
	PhysicalCluster     string `json:"physical_cluster,omitempty"`
	LogicalCluster      string `json:"logical_cluster,omitempty"`
	Name                string `json:"name,omitempty"`
	Zone                string `json:"zone,omitempty"`
	IsLocked            bool   `json:"is_locked,omitempty"`
	PreMergeRegion      string `json:"pre_merge_region,omitempty"`
	SurgePercent        int64  `json:"surge_percent,omitempty"`
	SurgeNum            int64  `json:"surge_num,omitempty"`
	CanarySurgePercent  int64  `json:"canary_surge_percent,omitempty"`
	Stage               string `json:"stage,omitempty"`
}

type deployStrategyType int32

const (
	DeployStrategyType_TCE     deployStrategyType = 1
	DeployStrategyType_WEB     deployStrategyType = 2
	DeployStrategyType_GECKO   deployStrategyType = 3
	DeployStrategyType_FAAS    deployStrategyType = 4
	DeployStrategyType_TCC     deployStrategyType = 5
	DeployStrategyType_CRONJOB deployStrategyType = 6
)

type ChangeItemDeployStrategyTyped struct {
	Type        deployStrategyType
	TceStrategy *ClusterInfo `protobuf:"bytes,4,opt,name=tce_strategy,json=tceStrategy,proto3,oneof"`

	WebStrategy *paaspb.WebDeployStrategy `protobuf:"bytes,5,opt,name=web_strategy,json=webStrategy,proto3,oneof"`

	GeckoStrategy *paaspb.GeckoDeployStrategy `protobuf:"bytes,6,opt,name=gecko_strategy,json=geckoStrategy,proto3,oneof"`

	FaasStrategy *paaspb.FaasDeployStrategy `protobuf:"bytes,7,opt,name=faas_strategy,json=faasStrategy,proto3,oneof"`

	TccStrategy *paaspb.TccDeployStrategy `protobuf:"bytes,8,opt,name=tcc_strategy,json=tccStrategy,proto3,oneof"`

	CronjobStrategy *paaspb.CronJobDeployStrategy `protobuf:"bytes,10,opt,name=cronjob_strategy,json=cronjobStrategy,proto3,oneof"`
}
