load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")
load("@rules_pkg//pkg:mappings.bzl", "pkg_attributes", "pkg_files", "strip_prefix")

go_library(
    name = "syncauth_lib",
    srcs = ["syncauth.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/cmd/syncauth",
    visibility = ["//visibility:private"],
    deps = [
        "//app/cdrpc/biz/dal/mysql",
        "//app/cdrpc/biz/dal/mysql/entity",
        "//app/cdrpc/biz/dal/mysql/repository",
        "//app/cdrpc/biz/pkg/authz",
        "//app/cdrpc/biz/pkg/gitrpc",
        "//app/cdrpc/biz/pkg/scm",
        "//app/cdrpc/biz/pkg/tce",
        "//app/cdrpc/biz/service/release_ticket",
        "//app/cdrpc/config",
        "@org_byted_code_canal_provider//env_platform",
        "@org_byted_code_canal_provider//kms_v2",
        "@org_byted_code_canal_provider//tce",
        "@org_byted_code_gopkg_logs//:logs",
    ],
)

go_binary(
    name = "syncauth",
    embed = [":syncauth_lib"],
    visibility = ["//visibility:public"],
)

pkg_files(
    name = "bin",
    srcs = [":syncauth"],
    attributes = pkg_attributes(
        mode = "0755",
    ),
    prefix = "bin",
    visibility = ["//visibility:public"],
)
