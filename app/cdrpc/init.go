package main

import (
	"os"

	"code.byted.org/bytecycle/go-middlewares/infra/initializer"
	"code.byted.org/canal/provider/bytetree"
	"code.byted.org/canal/provider/cronjob"
	"code.byted.org/canal/provider/env_platform"
	"code.byted.org/canal/provider/kms_v2"
	scmProvider "code.byted.org/canal/provider/scm"
	"code.byted.org/canal/provider/tcc_config"
	tceProvider "code.byted.org/canal/provider/tce"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/consumer"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/daemontask"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/es"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/eventbus_mq"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mongo"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/redis"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/dal/rmq"
	rtprojectdomain "code.byted.org/devinfra/hagrid/app/cdrpc/biz/domain/rt_project"
	appcenterapi "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/appcenter"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/authz"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/cd_metrics"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/change_item_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/check_set"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/config_service_sdk"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/cron_job_sdk"
	executionEngine "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/execution_engine"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/faas"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/feature_gate"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/gatekeeper_sdk"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/gitrpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/goofy_deploy"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/i18n"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/idgen"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/integrate_sdk"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/meego"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/message_center"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/message_center_sdk"
	mutexexecution "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/mutex_execution"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/new_env"
	oreorpc "code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/oreo_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/pipeline_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/resourceapi"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/scm"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/spacerpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/storage_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tcc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/tce"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/template_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/pkg/varstore_rpc"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/integration_event"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/release_ticket"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtProjectByControlPlane"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rt_stage"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/service/rtproject"
	"code.byted.org/devinfra/hagrid/app/cdrpc/biz/var_store"
	"code.byted.org/devinfra/hagrid/app/cdrpc/config"
	"code.byted.org/devinfra/hagrid/app/cdrpc/utils/httputil"
	"code.byted.org/devinfra/hagrid/libs/bits_err"
	"code.byted.org/devinfra/hagrid/libs/common_lib/utils"
	"code.byted.org/devinfra/hagrid/pbgen/rpc/bc/varstorerpc"
	"code.byted.org/devinfra/hagrid/pkg/devopsvarsdk"
	"code.byted.org/devinfra/hagrid/pkg/gecko"
)

func Init() {
	InitDependency(config.MustInitializeConfig())
}

func InitDependency(config *config.Config) {
	cd_metrics.MustInitialize()
	kms_v2.Init()
	tce.MustInitialize(true)
	gitrpc.InitGitClient()
	env_platform.InitSDK()
	meego.Init()
	scm.MustInitialize()
	es.Init()
	tceProvider.InitSDK()
	scmProvider.InitSDK()
	appcenterapi.MustInitialize()
	executionEngine.MustInit()
	bits_err.Init()

	mysql.MustInitialize(config.Mysql)
	redis.MustInitialize(config.Redis)
	mongo.Init()
	idgen.MustInitialize(config.IdGen)

	// onesite 网关鉴权 rpc
	authz.InitAuthzClient()
	// onesite 空间 rpc
	spacerpc.InitSpaceClient()
	// TODO: 后续重写该模块
	mutexexecution.MustInitialize()
	httputil.MustInitBytedHttpClient()
	// 初始化varstore  RPC
	initializer.MustInitializeWithDeps(varstorerpc.Module)
	varstore_rpc.InitVarStoreClient()
	change_item_rpc.Init()
	// 初始化vars sdk
	devopsvarsdk.InitDevopsVarSDK(varstore_rpc.GetVarStoreClient())
	// 初始化 oreorpc
	oreorpc.InitOreoClient()
	tcc.MustInit()
	feature_gate.MustInit()
	// 初始化暂存区服务
	storage_rpc.Init()
	var_store.Init()
	tcc_config.InitClient()
	cronjob.InitClient()

	// 初始化resource-api配置
	resourceapi.MustInitialize(config.ResourceApi)

	if _, ok := os.LookupEnv("HAGRID_DEBUG"); !ok {
		daemontask.MustInitReleaseTicketPostTaskWorker()
	}

	goofy_deploy.MustInitialize()
	gecko.InitGeckoClient()
	rtprojectdomain.MustInit()
	rtproject.MustInit()
	rtProjectByControlPlane.MustInit()

	integrate_sdk.MustInitialize()
	message_center.MustInitialize()
	gatekeeper_sdk.MustInitialize()
	new_env.MustInitialize()
	config_service_sdk.MustInitialize()
	message_center_sdk.MustInitialize()
	cron_job_sdk.MustInitialize()
	faas.MustInitialize()
	check_set.MustInitialize()

	//// 一切初始化后，再注册消费事件
	if !utils.IsMac() {
		i18n.Init()
		rmq.InitBitsEventsProducer()
		// 如果是本地 debug/dev，不创建 mq handler
		if _, ok := os.LookupEnv("HAGRID_DEBUG"); !ok {
			rmq.MustInitialize(config.Rmq,
				integration_event.NewIntegrationEventHandler(),
				release_ticket.NewPlanTimeMQHandle(),
			)
		}
		// stage初始化一定要在producer后
		rt_stage.MustInit()
		consumer.InitBitsEventsConsumer()
		eventbus_mq.Init(config.Eventbus)
	}

	pipeline_rpc.Init()
	template_rpc.Init()
	bytetree.InitSDK()
}
