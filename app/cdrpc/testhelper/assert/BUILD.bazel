load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "assert",
    srcs = ["expect.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/cdrpc/testhelper/assert",
    visibility = ["//visibility:public"],
    deps = [
        "//app/cdrpc/utils",
        "@com_github_alecthomas_assert//:assert",
        "@com_github_stretchr_testify//assert",
        "@org_byted_code_gopkg_lang_v2//conv",
        "@org_golang_google_protobuf//proto",
    ],
)
