package model

import (
	larkcard "github.com/larksuite/oapi-sdk-go/v3/card"
	"golang.org/x/exp/maps"
	"golang.org/x/exp/slices"
)

type LarkMessageModel struct {
	OpenId            string // 【必填】消息发送方的ID
	ChatId            string // 【必填】群ID，一般用于回复消息
	UserId            string // 【可选】消息发送者
	MessageId         string // 【必填】消息ID
	OriginMessageText string // 【可选】给机器人发送的原始消息
	CardActionToken   string // 【可选】点击卡片消息按钮后，回传的token，仅用于需要延时更新卡片消息的场景
	ParentMessageId   string // 【可选】回复消息的消息ID
	ParentMessageText string // 【可选】回复消息的文本内容
	ThreadID          string // 【可选】话题ID

	Command         string                 // 【必填】运维指令
	Params          []string               // 【可选】给机器人发送的消息中，解析出的文本参数（格式自定义）
	CardActionValue map[string]interface{} // 【可选】点击卡片消息按钮后，回传的数据
	Extra           map[string]string      // 【可选】额外信息, 用于action call tools时需要传递的参数
	ExecuteAction   string                 //当前执行的Action
}

type LarkConfig struct {
	AppId             string `json:"app_id"`
	AppSecret         string `json:"app_secret"`
	EncryptKey        string `json:"encrypt_key"`
	VerificationToken string `json:"verification_token"`
	OpenId            string `json:"open_id"`
}

// See https://open.feishu.cn/document/server-docs/im-v1/message/create
type LarkSendMessageModel struct {
	ReceiveId string         `json:"receive_id"`
	MsgType   string         `json:"msg_type"`
	Content   MessageContent `json:"content"`
}

func (l *LarkMessageModel) Clone() *LarkMessageModel {
	// clone every field
	return &LarkMessageModel{
		OpenId:            l.OpenId,
		ChatId:            l.ChatId,
		MessageId:         l.MessageId,
		OriginMessageText: l.OriginMessageText,
		CardActionToken:   l.CardActionToken,
		ParentMessageId:   l.ParentMessageId,
		ParentMessageText: l.ParentMessageText,
		Command:           l.Command,
		Params:            slices.Clone(l.Params),
		CardActionValue:   maps.Clone(l.CardActionValue),
		Extra:             maps.Clone(l.Extra),
	}
}

type MessageContent interface {
	GetContent() (string, error)
}

type CardContent struct {
	Card larkcard.MessageCard
}

type TextContent struct {
	Text string
}

func (c CardContent) GetContent() (string, error) {
	cardStr, err := c.Card.String()

	if err != nil {
		return "", err
	}
	return cardStr, nil
}

func (t TextContent) GetContent() (string, error) {
	return t.Text, nil
}
