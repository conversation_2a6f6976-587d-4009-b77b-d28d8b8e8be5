/*
*
<AUTHOR>
@date: 1/12/25
*
*/
package copilot

import (
	"code.byted.org/devinfra/hagrid/internal/pipeline/constvar"
	"code.byted.org/devinfra/hagrid/pkg/net/httpclient"
	"code.byted.org/gopkg/metainfo"
	"context"
	"fmt"
	"github.com/bytedance/mockey"
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestCreateApp(t *testing.T) {
	mockey.PatchConvey("run successfully", t, func() {
		// mock copilot agent 服务的返回
		defer mockey.Mock((*httpclient.BytedHttpClient).CtxPost).Return(&httpclient.Response{}, nil).Build().UnPatch()
		defer mockey.Mock((*httpclient.Response).StatusCode).Return(200).Build().UnPatch()
		defer mockey.Mock((*httpclient.Response).Body).To(func(self *httpclient.Response) []byte {
			return []byte(`{
			"detail": {
				"app_id": "fake_app_id",
				"config": {
					"workflow": {
						"dsl": "fake_dsl"
					}
				}
			}
		}`)
		}).Build().UnPatch()
		c := NewCopilotAgentWebApi()
		ctx := metainfo.WithPersistentValue(context.Background(), constvar.ContextKey_JwtToken, "fakejwttoken")
		appId, err := c.CreateApp(ctx, "fake_dsl", "fake_workflow", "fake_creator")
		assert.Nil(t, err)
		assert.Equal(t, "fake_app_id", appId)
	})
}

func TestGetApp(t *testing.T) {
	mockey.PatchConvey("run GetApp successfully", t, func() {
		// mock copilot agent 服务的返回
		defer mockey.Mock((*httpclient.Response).StatusCode).When(func(self *httpclient.Response) bool {
			return true
		}).To(func(self *httpclient.Response) int {
			return 200
		}).Build().UnPatch()
		defer mockey.Mock((*httpclient.Response).Body).When(func(self *httpclient.Response) bool {
			return true
		}).To(func(self *httpclient.Response) []byte {
			return []byte(`{
			"app": {
				"app_id": "fake_app_id",
				"config": {
					"workflow": {
						"dsl": "fake_dsl2"
					}
				}
			}
		}`)
		}).Build().UnPatch()
		defer mockey.Mock((*httpclient.BytedHttpClient).CtxGet).Return(&httpclient.Response{}, nil).Build().UnPatch()

		c := NewCopilotAgentWebApi()
		ctx := metainfo.WithPersistentValue(context.Background(), constvar.ContextKey_JwtToken, "fakejwttoken")
		appDetail, err := c.GetApp(ctx, "fake_app_id")
		assert.Nil(t, err)
		assert.Equal(t, "fake_app_id", appDetail.AppId)
	})
}

func TestUpdateApp(t *testing.T) {
	mockey.PatchConvey("run UpdateApp successfully", t, func() {
		// mock copilot agent 服务的返回
		mockAppId := "fake_app_id"
		mockRespStatusCodeBuilder := mockey.Mock((*httpclient.Response).StatusCode)
		mockRespBodyBuilder := mockey.Mock((*httpclient.Response).Body)

		defer mockey.Mock((*httpclient.BytedHttpClient).CtxGet).Return(&httpclient.Response{
			Request: &httpclient.Request{
				Method: "GET",
				URL:    fmt.Sprintf(getAppPath, mockAppId),
			},
		}, nil).Build().UnPatch()
		mockRespStatusCodeBuilder.When(func(self *httpclient.Response) bool {
			return self.Request.Method == "GET" && self.Request.URL == fmt.Sprintf(getAppPath, mockAppId)
		}).Return(200)
		mockRespBodyBuilder.When(func(self *httpclient.Response) bool {
			return self.Request.Method == "GET"
		}).Return([]byte(fmt.Sprintf(`{
			"app": {
				"app_id": "%s",
				"config": {
					"workflow": {
						"dsl": "fake_dsl"
					}
				}
			}
		}`, mockAppId)))

		defer mockey.Mock((*httpclient.BytedHttpClient).CtxPut).Return(&httpclient.Response{
			Request: &httpclient.Request{
				Method: "PUT",
				URL:    fmt.Sprintf(updateAppPath, mockAppId),
			},
		}, nil).Build().UnPatch()
		mockRespStatusCodeBuilder.When(func(self *httpclient.Response) bool {
			return self.Request.Method == "PUT" && self.Request.URL == fmt.Sprintf(updateAppPath, mockAppId)
		}).Return(200)
		mockRespBodyBuilder.When(func(self *httpclient.Response) bool {
			return self.Request.Method == "PUT"
		}).Return(`{}`)

		defer mockRespStatusCodeBuilder.Build().UnPatch()
		defer mockRespBodyBuilder.Build().UnPatch()

		c := NewCopilotAgentWebApi()
		ctx := metainfo.WithPersistentValue(context.Background(), constvar.ContextKey_JwtToken, "fakejwttoken")
		err := c.UpdateApp(ctx, mockAppId, "fake_dsl")
		assert.Nil(t, err)
	})
}
