load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "troubleshoot_agent",
    srcs = [
        "api.go",
        "model.go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/app/aiapi/biz/external/troubleshoot_agent",
    visibility = ["//visibility:public"],
    deps = [
        "//app/aiapi/biz/dal/tcc",
        "//app/aiapi/biz/external",
        "//app/aiapi/biz/external/tool_agent/model",
        "//app/aiapi/biz/model",
        "//libs/hertz",
        "//libs/stream",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
        "@org_byted_code_gopkg_jsonx//:jsonx",
        "@org_byted_code_gopkg_logs//:logs",
        "@org_byted_code_lang_gg//choose",
        "@org_byted_code_lang_gg//gslice",
        "@org_byted_code_paas_cloud_sdk_go//jwt",
    ],
)
