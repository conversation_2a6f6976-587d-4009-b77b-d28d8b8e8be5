package producer

import (
	"context"
	"fmt"
	"os"
	"time"

	json "github.com/bytedance/sonic"

	"code.byted.org/gopkg/logs"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"

	rmqCfg "code.byted.org/rocketmq/rocketmq-go-proxy/pkg/config"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/producer"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/rmq"
)

var (
	BaccaTaskProducer       producer.Producer
	EvalTaskProducer        producer.Producer
	CIToolTaskProducer      producer.Producer // TODO @isami.akasaka: rename
	CILintFixRecordProducer producer.Producer
	swimlane                string
)

func MustInitialize(configs map[string]*rmq.Config) {
	swimlane = os.Getenv(rmq.SwimlaneEnvKey)
	mqConfig, ok := configs["eval_task"]
	if ok {
		EvalTaskProducer = NewRMQProducer(mqConfig)
	}
	mqConfig, ok = configs["bacca_task"]
	if ok {
		BaccaTaskProducer = NewRMQProducer(mqConfig)
	}

	mqConfig, ok = configs["ci_tool_task"] // TODO @isami.akasaka
	if ok {
		CIToolTaskProducer = NewRMQProducer(mqConfig)
	}

	mqConfig, ok = configs["ci_lint_fix_record"]
	if ok {
		CILintFixRecordProducer = NewRMQProducer(mqConfig)
	}
}

func NewRMQProducer(config *rmq.Config) producer.Producer {
	cfg := rmqCfg.NewProducerConfig(config.Topic, config.Cluster)
	cfg.ProduceTimeout = 5 * time.Second
	p, err := producer.NewProducer(cfg)
	if err != nil {
		panic(fmt.Sprintf("init rocketmq producer failed, error: %s", err.Error()))
	}
	return p
}

type EvalTaskMsg struct {
	TaskID uint64
}

type BaccaTaskMsg struct {
	ChatId string
}

type CiToolTaskMsg struct {
	ExecutionId uint64    `json:"executionId"` // 工具执行ID
	StartedAt   time.Time `json:"startedAt"`   // 工具执行开始时间
	CompletedAt time.Time `json:"completedAt"` // 工具执行完成时间

	RepoName string `json:"repoName"` // 仓库名称
	Username string `json:"username"` // 用户名

	ToolName string `json:"toolName"` // 工具名称
	IsError  bool   `json:"isError"`  // 是否有错误

	JobRunId       uint64 `json:"jobRunId"`
	JobRunSeq      uint64 `json:"jobRunSeq"`
	StepId         uint64 `json:"stepId"`
	ConversationID string `json:"conversationId"`
	IsManual       bool   `json:"isManual"`  // 是否手动执行
	IsOversea      bool   `json:"isOversea"` // 是否海外仓库
}

func SendEvalTaskMsg(ctx context.Context, msg *EvalTaskMsg) error {
	if len(swimlane) > 0 {
		ctx = context.WithValue(ctx, "K_ENV", swimlane)
	}
	rawJSON, err := json.Marshal(msg)
	if err != nil {
		logs.CtxError(ctx, "marshal eval task msg failed, err: %v", err)
		return err
	}
	if len(rawJSON) == 0 {
		return nil
	}
	data := types.NewMessage(EvalTaskProducer.Config().ProducerGroup, rawJSON)
	res, err := EvalTaskProducer.Send(ctx, data)
	if err != nil {
		logs.CtxError(ctx, "send pipeline event failed, err: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "send pipeline event success, send result: %v", res)
	return nil
}

func SendBaccaTaskMsg(ctx context.Context, msg *BaccaTaskMsg) error {
	if len(swimlane) > 0 {
		ctx = context.WithValue(ctx, "K_ENV", swimlane)
	}
	rawJSON, err := json.Marshal(msg)
	if err != nil {
		logs.CtxError(ctx, "marshal bacca task msg failed, err: %v", err)
		return err
	}
	if len(rawJSON) == 0 {
		return nil
	}
	data := types.NewMessage(BaccaTaskProducer.Config().ProducerGroup, rawJSON)
	res, err := BaccaTaskProducer.Send(ctx, data)
	if err != nil {
		logs.CtxError(ctx, "send pipeline event failed, err: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "send pipeline event success, send result: %v", res)
	return nil
}

func SendCIToolTaskMsg(ctx context.Context, msg *CiToolTaskMsg) error {
	if len(swimlane) > 0 {
		ctx = context.WithValue(ctx, "K_ENV", swimlane)
	}
	rawJSON, err := json.Marshal(msg)
	if err != nil {
		logs.CtxError(ctx, "marshal ci tool task msg failed, err: %v", err)
		return err
	}
	if len(rawJSON) == 0 {
		return nil
	}

	data := types.NewDeferMessage(CIToolTaskProducer.Config().ProducerGroup, 1*time.Hour, rawJSON)

	res, err := CIToolTaskProducer.Send(ctx, data)
	if err != nil {
		logs.CtxError(ctx, "send ci tool defer task msg failed, err: %v", err)
		return err
	}
	logs.CtxInfo(ctx, "send pipeline event success, send result: %v", res)
	return nil
}
