package entity

import "time"

type EvalTaskCase struct {
	ID           uint64             `json:"id" gorm:"column:id;primaryKey;autoIncrement"`
	TaskID       uint64             `json:"task_id" gorm:"column:task_id"`
	CaseID       uint64             `json:"case_id" gorm:"column:case_id"`
	Question     string             `json:"question" gorm:"column:question"`
	ActualAnswer string             `json:"actual_answer" gorm:"column:actual_answer"`
	Status       EvalTaskCaseStatus `json:"status" gorm:"column:status"`
	Score        int8               `json:"score" gorm:"column:score"`
	StartedAt    *time.Time         `json:"started_at" gorm:"column:started_at"`
	CompletedAt  *time.Time         `json:"completed_at" gorm:"column:completed_at"`
	CreatedBy    string             `json:"created_by" gorm:"column:created_by"`
	CreatedAt    *time.Time         `json:"created_at" gorm:"column:created_at;autoCreateTime"`
	UpdatedAt    *time.Time         `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"`
}

func (e *EvalTaskCase) TableName() string {
	return "ai_eval_task_case"
}

type EvalTaskCaseStatus int8

const (
	EVAL_TASK_CASE_STATUS_UNSPECIFIED EvalTaskCaseStatus = iota
	EVAL_TASK_CASE_STATUS_CREATED
	EVAL_TASK_CASE_STATUS_STARTED
	EVAL_TASK_CASE_STATUS_FAILED
	EVAL_TASK_CASE_STATUS_SUCCESS
)
