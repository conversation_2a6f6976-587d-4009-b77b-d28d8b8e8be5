package tool

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/aipb"
	"code.byted.org/devinfra/hagrid/pkg/codebase"
)

var testCli codebase.Client

func TestMain(m *testing.M) {
	var err error
	testCli, err = codebase.NewClient(97, "06440634-f811-48dd-9f51-a60e10f1718e")
	if err != nil {
		panic(err)
	}
	m.Run()
}

func TestCodebaseTool_BatchGetRepoFile(t *testing.T) {
	tool := NewCodebaseTool()
	tool.codebaseClient = testCli
	files, err := tool.BatchGetRepoFile(context.Background(), &aipb.BatchGetRepoFilesRequest{
		FilePaths: []string{"go.mod"},
		RepoName:  "ecom/marketing_express",
		Revision:  "889ad15fc19c0df234b68dc374630c7d887acb9a",
	})

	assert.Nil(t, err)
	assert.Equal(t, 1, len(files.Files))
}
