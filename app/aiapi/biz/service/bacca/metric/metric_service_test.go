package metric

import (
	"context"
	"fmt"
	"io"
	"os"
	"testing"
	"time"

	"code.byted.org/argos/measurement-query/model"
	"code.byted.org/gopkg/env"
	json "github.com/bytedance/sonic"
	"github.com/stretchr/testify/assert"
	"github.com/wcharczuk/go-chart"
)

func Test_UpdateChartWithAbnormalPoints(t *testing.T) {
	if env.IsBoe() {
		assert.True(t, env.IsBoe())
		// todo 跑的时候注释掉这段env判断
		return
	}
	ctx := context.Background()
	svc := NewMetricService()

	bs := getDataFromTestDataFile("./test_data/metric_service_test_data")
	testData := &model.GetMeasurementDataResp{}

	json.Unmarshal(bs, testData)

	t.Run("success", func(t *testing.T) {
		c, err := svc.GetChartFromTimeSeries(ctx, testData.Data.TimeSeries[0])
		//c, err = svc.UpdateChartWithAbnormalPoints(ctx, c, []int64{1713778870, 1713779570, 1713779710})

		assert.NoError(t, err)
		assert.NotNil(t, c)

		f, _ := os.Create("./test.png")
		err = c.Chart.Render(chart.PNG, f)
		assert.NoError(t, err)

	})
}

func getDataFromTestDataFile(filePath string) []byte {
	// 读取 JSON 文件内容
	file, err := os.Open(filePath)
	if err != nil {
		fmt.Println("无法打开文件:", err)
		return nil
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		fmt.Println("无法读取文件:", err)
		return nil
	}

	return data
}

func Test_GetChartFromTimeSeries2(t *testing.T) {
	// 示例数据
	xValues := []float64{1, 2, 3, 4, 5}
	yValues1 := []float64{10, 20, 30, 40, 50}
	yValues2 := []float64{5, 10, 15, 20, 25}

	series1 := chart.ContinuousSeries{
		Name:    "Series 1", // 第一条曲线的名称
		XValues: xValues,
		YValues: yValues1,
	}

	series2 := chart.ContinuousSeries{
		Name:    "Series 2", // 第二条曲线的名称
		XValues: xValues,
		YValues: yValues2,
	}

	// 创建图表并添加曲线
	graph := chart.Chart{
		Series: []chart.Series{
			series1,
			series2,
		},
	}

	// 添加图例
	graph.Elements = []chart.Renderable{
		chart.Legend(&graph),
	}

	// 保存图表为 PNG 文件
	f, _ := os.Create("chart.png")
	defer f.Close()
	graph.Render(chart.PNG, f)
	fmt.Println("Chart rendered and saved as chart.png")
}

// TimeFromFloat64 returns a time from a float64.
func TimeFromFloat64(tf float64) time.Time {
	return time.Unix(0, int64(tf))
}

func Test_highlight(t *testing.T) {
	graph := chart.Chart{
		Series: []chart.Series{
			chart.ContinuousSeries{
				Name:    "Sample Series",
				XValues: []float64{1, 2, 3, 4, 5},
				YValues: []float64{1, 2, 3, 4, 5},
			},
		},
	}

	// 自定义绘制函数，用于高亮展示某个坐标点
	highlightPoint := chart.AnnotationSeries{
		Annotations: []chart.Value2{
			{XValue: 3, YValue: 3, Label: "abormal"},
			{XValue: 4, YValue: 4, Label: "abormal"},
		},
		Style: chart.Style{
			Show:        true,
			StrokeColor: chart.ColorRed,
			FillColor:   chart.ColorRed,
			StrokeWidth: 2,
		},
	}

	// 将自定义绘制函数添加到图表中
	//graph.Elements = []chart.Renderable{
	//	highlightPoint,
	//}

	graph.Series = append(graph.Series, highlightPoint)
	// 将图表保存到文件中
	f, _ := os.Create("./output.png")
	defer f.Close()
	graph.Render(chart.PNG, f)
}
