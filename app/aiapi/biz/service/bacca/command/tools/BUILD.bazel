load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "tools",
    srcs = ["log_translate.go"],
    importpath = "code.byted.org/devinfra/hagrid/app/aiapi/biz/service/bacca/command/tools",
    visibility = ["//visibility:public"],
    deps = [
        "//app/aiapi/biz/utils",
        "//app/aiapi/biz/utils/link",
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_pkg_errors//:errors",
    ],
)
