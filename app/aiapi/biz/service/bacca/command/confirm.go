package command

import (
	"context"
	"strings"

	"code.byted.org/gopkg/logs"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/rmq/producer"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/external/lark/utils"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/model"
)

const (
	yes = "yes"
	no  = "no"
)

type ConfirmCommandHandler struct {
	*baseCommandHandler
}

func (a *ConfirmCommandHandler) Handle(ctx context.Context, messageModel *model.LarkMessageModel) (output string, err error) {
	req, ok := messageModel.CardActionValue["req"]
	if len(messageModel.CardActionValue) == 0 || !ok {
		_, err = utils.CreateInteractiveCard(ctx, messageModel, "", "")
		if err != nil {
			logs.CtxError(ctx, "create card failed, err = %v", err)
			return
		}
		return output, nil
	}

	if req.(string) == yes {
		err = producer.SendBaccaTaskMsg(ctx, &producer.BaccaTaskMsg{
			ChatId: messageModel.ChatId,
		})
		if err != nil {
			utils.SendBadContentMsg(ctx, messageModel, "发起执行计划失败："+err.Error())
		}
	}
	return output, err
}

func (a *ConfirmCommandHandler) extractTitle(text string) string {
	lines := strings.Split(text, "\n")

	// 检查行号是否有效
	if len(lines) < 2 {
		return ""
	}

	for _, line := range lines {
		if strings.HasPrefix(line, "对提问者的回复: ") {
			return strings.TrimPrefix(line, "对提问者的回复: ")
		}
	}
	return ""
}

func (a *ConfirmCommandHandler) extractText(text string) string {
	lines := strings.Split(text, "\n")

	// 检查行号是否有效
	if len(lines) < 2 {
		return ""
	}

	res := make([]string, 0)
	for _, line := range lines {
		if strings.HasPrefix(line, "- [ ] Step") {
			res = append(res, line)
		}
	}
	return strings.Join(res, "\n")
}
