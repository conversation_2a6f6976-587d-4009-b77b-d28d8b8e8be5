package diagnose

import (
	"context"
	_ "embed"
	"fmt"
	"strconv"
	"testing"

	"code.byted.org/gopkg/env"

	"github.com/stretchr/testify/assert"

	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/mysql"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/mysql/entity"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/dal/tcc"
	"code.byted.org/devinfra/hagrid/app/aiapi/biz/utils"
	"code.byted.org/devinfra/hagrid/app/aiapi/infra"
)

func Test_diagnoseService_CreateDiagnosticAnalysisResultWithConversationID(t *testing.T) {
	if env.IsBoe() {
		assert.True(t, env.IsBoe())
		// todo 跑的时候注释掉这段env判断
		return
	}
	tcc.MustInitialize(&tcc.Config{Psm: "bits.devopsai.api"})
	mysql.MustInitialize(&mysql.Config{
		PSM: "toutiao.mysql.hagrid_pipeline_boe",
		DB:  "hagrid_pipeline_boe",
	})

	d := NewDiagnoseService()
	repo := infra.ErrorAnalysisResultRepo()

	ctx := context.Background()
	conversationId := "testconversationId"
	var jobRunID uint64 = 123
	var stepID uint64 = 123

	t.Run("success", func(t *testing.T) {
		bizId := "123-123"
		// 存点数据
		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: bizId, Output: "我是测试用例1"})
		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: "abc", Output: "我是测试用例2"})
		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: bizId, Output: "我是测试用例3"})
		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: "abc", Output: "我是测试用例4"})

		// 测试函数对象
		err := d.CreateDiagnosticAnalysisResultWithConversationID(ctx, jobRunID, 0, stepID, conversationId)
		assert.Nil(t, err)

		// expected 第三个测试用例被的数据被复用
		result, _ := repo.GetLatestErrDiagnosis(ctx, bizId)
		assert.NotNil(t, result)
		assert.Equal(t, bizId, result.BizID)
		assert.Equal(t, "我是测试用例3", result.Output)
		assert.Equal(t, conversationId, result.ConversationID)

		repo.DeleteErrDiagnosis(ctx, bizId)
		repo.DeleteErrDiagnosis(ctx, "abc")
	})

	t.Run("无现有数据", func(t *testing.T) {
		// 存点数据
		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: "abc", Output: "我是测试用例1"})
		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: "abc", Output: "我是测试用例2"})

		// 测试函数对象
		err := d.CreateDiagnosticAnalysisResultWithConversationID(ctx, jobRunID, 0, stepID, conversationId)
		assert.Error(t, err)

		repo.DeleteErrDiagnosis(ctx, "abc")
	})
}

// 因为调用gpt所以比较耗时 暂时先注释
// func Test_diagnoseService_CreateDiagnosticAnalysisResult(t *testing.T) {
//	tcc.MustInitialize(&tcc.Config{Psm: "bits.devopsai.api"})
//	mysql.MustInitialize(&mysql.Config{
//		PSM: "toutiao.mysql.hagrid_pipeline_boe",
//		DB:  "hagrid_pipeline_boe",
//	})
//	initializer.MustInitializeWithDeps(pipelinerpc.Module)
//	config.MustInitializeConfig()
//	d := NewDiagnoseService()
//	repo := infra.ErrorAnalysisResultRepo()
//
//	ctx := context.Background()
//	jobRunID := 1000810531
//	seq := 1
//	stepID := 723664
//	output := "\"👀 诊断过程\n任务类型：\n执行单元测试\n错误日志分析：\n日志行 512-516 说明在执行单元测试时，链接器运行失败，原因是/usr/bin/ld: /go/pkg/mod/github.com/valyala/gozstd@v1.17.0/libzstd_linux_amd64.a(cover.o): warning: relocation against 'stderr@@GLIBC_2.2.5' in read-only section '.text'，链接器无法链接github.com/valyala/gozstd库，导致测试失败。\n日志行 748-749 说明单元测试执行失败，退出码为2。\n🎯 错误原因\n[可能性较大] 单元测试执行失败，原因是链接器无法链接github.com/valyala/gozstd库。\n✨ 解决方案\n检查github.com/valyala/gozstd库是否已经被正确安装，如果没有，需要先安装github.com/valyala/gozstd库。\n检查链接器是否能正常工作，如果不能，需要修复或更换链接器。\n检查github.com/valyala/gozstd库是否与其他库存在冲突，如果存在，需要解决冲突。\n调试信息\"\n"
//	diagnosis := &entity.ErrorDiagnosis{
//		AppID:           "1",
//		ChatRecordID:    "1",
//		MessageRecordID: "1",
//		SceneType:       0,
//		CreatedBy:       "isami.akasaka",
//	}
//
//	t.Run("success", func(t *testing.T) {
//		bizId := "1000810531-723664"
//		// 存点数据
//		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: bizId, Output: "我是测试用例1"})
//		repo.CreateErrDiagnosis(ctx, &entity.ErrorDiagnosis{BizID: bizId, Output: "我是测试用例2"})
//
//		// 测试函数对象
//		err := d.CreateDiagnosticAnalysisResult(ctx, uint64(jobRunID), uint64(seq), int64(stepID), output, diagnosis)
//		assert.Nil(t, err)
//
//		// expected 1. 创建了新的数据项 不覆盖之前的数据 2. extra中记录了正确的version
//		result, _ := repo.GetLatestErrDiagnosis(ctx, bizId)
//		assert.NotNil(t, result)
//
//		assert.Equal(t, bizId, result.BizID)
//		assert.Equal(t, output, result.Output)
//		assert.NotEqual(t, 0, len(result.FormattedOutput))
//
//		extra := &entity.DiagnosisExtraDetail{}
//		json.UnmarshalString(result.Extra, &extra)
//		assert.NotNil(t, extra)
//		assert.Equal(t, entity.CiDiagnosisVersion, extra.Version)
//
//		repo.DeleteErrDiagnosis(ctx, bizId)
//	})
//
//	t.Run("没有历史数据", func(t *testing.T) {
//		bizId := "1000810531-723664"
//		// 测试函数对象
//		err := d.CreateDiagnosticAnalysisResult(ctx, uint64(jobRunID), uint64(seq), int64(stepID), output, diagnosis)
//		assert.Nil(t, err)
//
//		// expected 1. 创建了新的数据项 不覆盖之前的数据 2. extra中记录了正确的version
//		result, _ := repo.GetLatestErrDiagnosis(ctx, bizId)
//		assert.NotNil(t, result)
//
//		assert.Equal(t, bizId, result.BizID)
//		assert.Equal(t, output, result.Output)
//		assert.NotEqual(t, 0, len(result.FormattedOutput))
//
//		extra := &entity.DiagnosisExtraDetail{}
//		json.UnmarshalString(result.Extra, &extra)
//		assert.NotNil(t, extra)
//		assert.Equal(t, entity.CiDiagnosisVersion, extra.Version)
//
//		//repo.DeleteErrDiagnosis(ctx, bizId)
//	})
// }

func Test_diagnoseService_formatResult(t *testing.T) {
	testData := "[\n    {\n        \"error_cause\": \"[可能性较大] pnpm 版本过低，无法满足当前流水线的要求，导致流水线执行被取消。\",\n        \"log_analyses\": [\n            {\n                \"error_line\": \"22-29,73\",\n                \"analysis\": \"pnpm 版本过低，需要更新版本，导致流水线执行被取消。\"\n            }\n        ],\n        \n        \"error_solution\": \"1. 升级 node.js 版本\\n使用字节镜像, 镜像版本选择可参考 [CI Docker Images 推荐使用镜像](https://bytedance.larkoffice.com/wiki/wikcnWH7ZsM9sKQCpqfOPf61Rzd), node.js镜像见  [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)\\_nodejs\\_20   [Dockerfile20](https://code.byted.org/codebase/ci_images/blob/master/nodejs_20/Dockerfile) [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)\\_nodejs\\_18     [Dockerfile18](https://code.byted.org/codebase/ci_images/blob/master/nodejs_18/Dockerfile) [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)\\_nodejs\\_16     [Dockerfile16](https://code.byted.org/codebase/ci_images/blob/master/nodejs_16/Dockerfile) [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)\\_nodejs\\_14     [Dockerfile14](https://code.byted.org/codebase/ci_images/blob/master/nodejs_14/Dockerfile)\\n2. 升级 pnpm 版本\\n根据错误日志信息 \\\"Run \\\"pnpm add -g pnpm\\\" to update.\\\" 可知，可以执行 `pnpm add -g pnpm` 命令升级 pnpm 版本\\n3.  增加超时时间\\n根据 [bnpm FAQ | bnpm 问题速查](https://bytedance.feishu.cn/docx/doxcnj9rYkYudLhI13rhxD33Whc)  内容，尝试增加超时时间：`pnpm install --fetch-timeout 1000000000`\"\n    }\n]"
	//
	// if !utf8.ValidString(testData) {
	//	testData = strings.ToValidUTF8(testData, "")
	// }
	// re := regexp2.MustCompile(`[\x00-\x09\x0B-\x1F\x7F]+|\\(?!n)`, 0)
	// testData, _ = re.Replace(testData, "", -1, -1)
	// // 去除空的反义字符
	// re = regexp2.MustCompile(`\p{C}+`, 0)
	// result, _ := re.Replace(testData, "", -1, -1)
	//
	result, _ := utils.RepairJSON(testData)

	expected := "[{\"error_cause\":\"[可能性较大] pnpm 版本过低，无法满足当前流水线的要求，导致流水线执行被取消。\",\"error_solution\":\"1. 升级 node.js 版本\\n使用字节镜像, 镜像版本选择可参考 [CI Docker Images 推荐使用镜像](https://bytedance.larkoffice.com/wiki/wikcnWH7ZsM9sKQCpqfOPf61Rzd), node.js镜像见  [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)_nodejs_20   [Dockerfile20](https://code.byted.org/codebase/ci_images/blob/master/nodejs_20/Dockerfile) [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)_nodejs_18     [Dockerfile18](https://code.byted.org/codebase/ci_images/blob/master/nodejs_18/Dockerfile) [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)_nodejs_16     [Dockerfile16](https://code.byted.org/codebase/ci_images/blob/master/nodejs_16/Dockerfile) [hub.byted.org/codebase/ci](http://hub.byted.org/codebase/ci)_nodejs_14     [Dockerfile14](https://code.byted.org/codebase/ci_images/blob/master/nodejs_14/Dockerfile)\\n2. 升级 pnpm 版本\\n根据错误日志信息 \\\"Run \\\"pnpm add -g pnpm\\\" to update.\\\" 可知，可以执行 `pnpm add -g pnpm` 命令升级 pnpm 版本\\n3.  增加超时时间\\n根据 [bnpm FAQ | bnpm 问题速查](https://bytedance.feishu.cn/docx/doxcnj9rYkYudLhI13rhxD33Whc)  内容，尝试增加超时时间：`pnpm install --fetch-timeout 1000000000`\",\"log_analyses\":[{\"analysis\":\"pnpm 版本过低，需要更新版本，导致流水线执行被取消。\",\"error_line\":\"22-29,73\"}]}]\n"
	fmt.Println(result)
	assert.NotNil(t, result)
	assert.Equal(t, result, expected)
}

var (
	//go:embed test_data/service_test/log_analysis_result.json
	lar string
)

func Test_diagnoseService_fmtLogLines(t *testing.T) {
	causes := []string{"日志行 3412-3415 说明无法找到有效的 Docker 环境，导致单元测试`EsClientSpec`执行失败\n\n", // 范围
		"日志行 57-58、235-236 说明单元测试`TestPacedSender_Dummy`执行失败，原因是未预期调用`pacer.MockPacedStreamInterface.GetPriority`方法\n\n", // 范围组合
		"日志行 259 说明重试失败，原因为`exit status 1`\n\n",                                                                          // 单行
		"日志行 139, 236, 316, 370, 424 等说明 SonarQube 扫描代码时出\n\n",                                                           // 单行组合
		"日志行 902、919、924-926 说明 `mongod` 启动失败，原因为无法找到本地副本集配置文档\n\n",                                                      // 混合组合
		"日志行 6-12、225、319-321、323-325、330、332、340-341、344-348、350-352、360、364-378、443-459、461、463-473、483-491、492-498、511-514、521-522、547-553、554-558、669-673、677-680、890-891 等说明正在初始化单元测试环境并加载配置\n\n1. ", // 超长 case
		"日志行 `5-7`、`8-9` 说明代码静态\n\n",                                // 引号1
		"日志行 `2`-`3` 说明当前环境中的 TensorRT 和 CUDA 版本与 QS 不兼容。 2. \n1. ", // 引号 2
	}
	wants := []string{"日志行 [3412-3415](#3412) 说明无法找到有效的 Docker 环境，导致单元测试`EsClientSpec`执行失败\n\n",
		"日志行 [57-58](#57)、[235-236](#235) 说明单元测试`TestPacedSender_Dummy`执行失败，原因是未预期调用`pacer.MockPacedStreamInterface.GetPriority`方法\n\n",
		"日志行 [259](#259) 说明重试失败，原因为`exit status 1`\n\n",
		"日志行 [139](#139),[236](#236),[316](#316),[370](#370),[424](#424) 等说明 SonarQube 扫描代码时出\n\n",
		"日志行 [902](#902)、[919](#919)、[924-926](#924) 说明 `mongod` 启动失败，原因为无法找到本地副本集配置文档\n\n",
		"日志行 [6-12](#6)、[225](#225)、[319-321](#319)、[323-325](#323)、[330](#330)、[332](#332)、[340-341](#340)、[344-348](#344)、[350-352](#350)、[360](#360)、[364-378](#364)、[443-459](#443)、[461](#461)、[463-473](#463)、[483-491](#483)、[492-498](#492)、[511-514](#511)、[521-522](#521)、[547-553](#547)、[554-558](#554)、[669-673](#669)、[677-680](#677)、[890-891](#890) 等说明正在初始化单元测试环境并加载配置\n\n1. ",
		"日志行 [5-7](#5)、[8-9](#8) 说明代码静态\n\n",
		"日志行 [2-3](#2) 说明当前环境中的 TensorRT 和 CUDA 版本与 QS 不兼容。 2. \n1. ",
	}

	type fields struct {
	}
	type args struct {
		cause string
	}
	tests := make([]struct {
		name   string
		fields fields
		args   args
		want   string
	}, len(causes))

	for i := 0; i < len(causes); i++ {
		tests = append(tests, struct {
			name   string
			fields fields
			args   args
			want   string
		}{
			name: "success" + strconv.Itoa(i+1),
			args: args{
				cause: causes[i],
			},
			want: wants[i],
		})
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			d := &diagnoseService{}
			assert.Equalf(t, tt.want, d.fmtLogLines(tt.args.cause), "fmtLogLines(%v, %v)", tt.args.cause)
		})
	}
}
