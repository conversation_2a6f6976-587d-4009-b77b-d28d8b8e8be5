package atom_gpt

import (
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/atommanagepb"
	"code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/platformpb"
	"errors"
)

var ErrExternalLogNotFound = errors.New("external log not found")
var ErrFailStepNotFound = errors.New("fail step not found")

type AtomContext struct {
	JobRun             *platformpb.JobRun
	AiDiagnoseSettings *atommanagepb.GetJobAtomAiDiagnoseSettingsResponse
}
