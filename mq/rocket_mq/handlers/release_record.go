package handlers

import (
	"context"
	"encoding/json"

	"code.byted.org/clientQA/artifact-manager/kitex_gen/ep/artifact/manager/common"
	"code.byted.org/clientQA/artifact-manager/service/release_record/hook"
	"code.byted.org/clientQA/artifact-manager/service/testflight"
	"code.byted.org/gopkg/ctxvalues"
	"code.byted.org/gopkg/logs"
	"code.byted.org/kite/kitutil"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/types"
)

type TFInfoHandler struct{}

func (TFInfoHandler) HandleMessage(ctx context.Context, msg *types.MessageExt) error {
	var data hook.ReleaseRecordRMQMsg
	err := json.Unmarshal(msg.Msg.MsgBody, &data)
	if err != nil {
		logs.CtxError(ctx, "[TFInfoHandler] Unmarshal error. msgBody:%v, err:%s", string(msg.Msg.MsgBody), err.Error())
		return err
	}

	if _, ok := ctxvalues.LogID(ctx); !ok {
		ctx = kitutil.NewCtxWithLogID(ctx, msg.MsgId)
	}
	if data.Platform != common.Platform_IOS.String() {
		return nil
	}
	return testflight.GenerateTFInfo(ctx, data)
}
