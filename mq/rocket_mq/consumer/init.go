package consumer

import (
	"code.byted.org/gopkg/env"
	"code.byted.org/gopkg/logs"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/config"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/consumer"
	"code.byted.org/rocketmq/rocketmq-go-proxy/pkg/proto"

	"code.byted.org/clientQA/artifact-manager/mq/rocket_mq/handlers"
)

var UnpackTaskConsumer consumer.Consumer
var SerializerTaskConsumer consumer.Consumer
var TFVersionConsumer consumer.Consumer
var ArtifactCheckerConsumer consumer.Consumer

func InitUnpackTaskConsumer(clusterName string, topic string, consumerGroup string) {
	cfg := config.NewDefaultConsumerConfig(consumerGroup, topic, clusterName)
	cfg.WorkerNum = 30
	cfg.MaxInFlight = 60
	cfg.MaxReconsumeTime = 3
	cfg.MessageQueueAllocateMode = proto.SubscribeRequestV2_WEIGHT
	cfg.ClientConfigEpoch = 1 //确保修改分配策略的新consumer的clientEpoch大于正在线上运行的clientEpoch
	newConsumer, err := consumer.NewConsumer(cfg)
	if err != nil {
		logs.Errorf("%v", err)
		panic(err)
	}
	UnpackTaskConsumer = newConsumer
	UnpackTaskConsumer.RegisterHandler(&UnpackTaskHandler{})
	go UnpackTaskConsumer.Start()
}

func InitSerializerTaskConsumer(clusterName, topic, consumerGroup string) {
	cfg := config.NewDefaultConsumerConfig(consumerGroup, topic, clusterName)
	cfg.WorkerNum = 1 // 启动1个worker
	c, err := consumer.NewConsumer(cfg)
	if err != nil {
		logs.Errorf("consumer start error. topic: %v, consumerGroup: %v, err %v", topic, consumerGroup, err)
		panic(err)
	}
	c.RegisterHandler(&handlers.SerializerTaskHandler{})
	go c.Start()
	SerializerTaskConsumer = c
}

func InitTFInfoConsumer(clusterName, topic, consumerGroup string) {
	cfg := config.NewDefaultConsumerConfig(consumerGroup, topic, clusterName)
	cfg.WorkerNum = 1
	c, err := consumer.NewConsumer(cfg)
	if err != nil {
		logs.Errorf("consumer start error. topic: %v, consumerGroup: %v, err %v", topic, consumerGroup, err)
		panic(err)
	}
	c.RegisterHandler(&handlers.TFInfoHandler{})
	go c.Start()
	TFVersionConsumer = c
}

func InitArtifactCheckerConsumer(clusterName string, topic string, consumerGroup string) {
	cfg := config.NewDefaultConsumerConfig(consumerGroup, topic, clusterName)
	cfg.EnablePPE = false
	cfg.WorkerNum = 2
	cfg.MaxInFlight = 20
	cfg.MaxReconsumeTime = 3
	newConsumer, err := consumer.NewConsumer(cfg)
	if err != nil {
		logs.Errorf("%v", err)
		panic(err)
	}
	ArtifactCheckerConsumer = newConsumer
	ArtifactCheckerConsumer.RegisterHandler(&ArtifactCheckerHandler{})
	go ArtifactCheckerConsumer.Start()
}

func StopConsumer() {
	// ppe就别消费了
	if !env.IsPPE() {
		UnpackTaskConsumer.Close()
	}
	_ = SerializerTaskConsumer.Close()
	_ = TFVersionConsumer.Close()
	_ = ArtifactCheckerConsumer.Close()
}
