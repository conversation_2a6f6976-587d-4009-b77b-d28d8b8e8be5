package itc

import (
	"fmt"
	"time"

	"code.byted.org/clientQA/artifact-manager/dal/redis"
	"code.byted.org/clientQA/artifact-manager/framework/util"
	"code.byted.org/kv/goredis"
)

const (
	VersionReportCacheExpireTime = time.Hour
)

type VersionReportTabKVRepo struct {
	client *goredis.Client
}

func NewVersionReportTabKVRepository(client *goredis.Client) *VersionReportTabKVRepo {
	return &VersionReportTabKVRepo{
		client,
	}
}

func (v *VersionReportTabKVRepo) SetVersionReportTabByPkgID(pkgID uint, reportTab interface{}) error {
	key := fmt.Sprintf(redis.KeyPatternVersionReportTabByPkgID, pkgID)
	val := util.ToJsonSimple(reportTab)
	return v.client.Set(key, val, VersionReportCacheExpireTime).Err()
}

func (v *VersionReportTabKVRepo) GetVersionReportTabByPkgID(pkgID uint) (string, error) {
	key := fmt.Sprintf(redis.KeyPatternVersionReportTabByPkgID, pkgID)
	return v.client.Get(key).Result()
}

func (v *VersionReportTabKVRepo) DeleteVersionReportTabByPkgID(pkgID uint) error {
	key := fmt.Sprintf(redis.KeyPatternVersionReportTabByPkgID, pkgID)
	return v.client.Del(key).Err()
}
