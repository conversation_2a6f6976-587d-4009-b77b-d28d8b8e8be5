package plugin_package

import (
	"code.byted.org/appmonitor/orm_tools/dbutil/transaction"
	"context"
)

type PluginPackageRepo struct {
	R PluginPackageRead
	W PluginPackageWrite
}

type PluginPackageRead interface {
	transaction.TransactionSupport
	/*
		sql(value = "select * from plugin_package where id = ?" , mode="placeholder")
	*/
	GetPluginPackageById(ctx context.Context, id int) (*PluginPackage, error)
}

type PluginPackageWrite interface {
	transaction.TransactionSupport
}
