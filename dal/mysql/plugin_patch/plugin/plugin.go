package plugin

import (
	"code.byted.org/appmonitor/orm_tools/dbutil/transaction"
	"context"
)

type PluginRepo struct {
	R PluginRead
	W PluginWrite
}

type PluginRead interface {
	transaction.TransactionSupport
	/*
		sql(value = "select * from plugin where id = ?" , mode="placeholder")
	*/
	GetPlugin(ctx context.Context, id int64) (*Plugin, error)
}

type PluginWrite interface {
	transaction.TransactionSupport

	// updater(v2 = "true", skipZero="true")
	UpdatePlugin(ctx context.Context, rule *Plugin) error
}
