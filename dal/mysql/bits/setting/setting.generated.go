// Code generated by COMMENTS_BUILD_TOOLS 1.1.23. DO NOT EDIT.
package setting

import (
	"database/sql"
	"errors"

	"code.byted.org/clientQA/artifact-manager/domain/bits/setting"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

type _BitsSettingReadRepoStruct struct {
	handler *gorm.DB
}

func NewBitsSettingReadRepo(handler *gorm.DB) BitsSettingReadRepo {
	return &_BitsSettingReadRepoStruct{
		handler: handler,
	}
}

var _CacheKeyBitsSettingReadRepoTemplate = struct {
}{}
var _BitsSettingReadRepoTemplate = struct {
}{}
var GlobalErrBitsSettingReadRepo = struct {
	CacheDemotionErr        error
	DBDemotionErr           error
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
}{
	errors.New("CacheDemotionError"),
	errors.New("DBDemotionError"),
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
}

func (interstruct *_BitsSettingReadRepoStruct) FindBitsSetting(id int64) (*setting.BitsSetting, error) {
	_result, _retErr := func() (*setting.BitsSetting, error) {
		_sqlText := "select * from bits_setting where id = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		var _ret = &setting.BitsSetting{}
		_sdb = _sdb.Scan(_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.Errorf("Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return nil, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsSettingReadRepoStruct) FindBitsSettingByAppIdAndType(appId int64, theType string) (*setting.BitsSetting, error) {
	_result, _retErr := func() (*setting.BitsSetting, error) {
		_sqlText := "select * from bits_setting where app_id = ? and type = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Raw(_sqlText, appId, theType)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		var _ret = &setting.BitsSetting{}
		_sdb = _sdb.Scan(_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.Errorf("Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return nil, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}

type _BitsSettingWriteRepoStruct struct {
	handler *gorm.DB
}

func NewBitsSettingWriteRepo(handler *gorm.DB) BitsSettingWriteRepo {
	return &_BitsSettingWriteRepoStruct{
		handler: handler,
	}
}

var _CacheKeyBitsSettingWriteRepoTemplate = struct {
}{}
var _BitsSettingWriteRepoTemplate = struct {
}{}
var GlobalErrBitsSettingWriteRepo = struct {
	CacheDemotionErr        error
	DBDemotionErr           error
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
}{
	errors.New("CacheDemotionError"),
	errors.New("DBDemotionError"),
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
}

func (interstruct *_BitsSettingWriteRepoStruct) InsertBitsSetting(setting *setting.BitsSetting) (int64, error) {
	if setting == nil {
		return 0, GlobalErrBitsSettingWriteRepo.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler
		_sdb := _db.Create(setting)
		if _sdb.Error != nil {
			logs.Errorf("BitsSettingWriteRepo.InsertBitsSetting occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsSettingWriteRepoStruct) UpdateBitsSettingByAppIdAndType(appId int64, theType string, setting string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update bits_setting set settings = ?, updated_at = CURRENT_TIME() where app_id = ? and type = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Exec(_sqlText, setting, appId, theType)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
