package setting

import (
	"code.byted.org/clientQA/artifact-manager/domain/bits/setting"
)

/*
读库。记得加上条件: deleted_at is null
*/
type BitsSettingReadRepo interface {
	// sql(value="select * from bits_setting where id = #id and deleted_at is null", mode="named_placeholder")
	FindBitsSetting(id int64) (*setting.BitsSetting, error)

	// sql(value="select * from bits_setting where app_id = #appId and type = #theType and deleted_at is null", mode="named_placeholder")
	FindBitsSettingByAppIdAndType(appId int64, theType string) (*setting.BitsSetting, error)
}

/*
写库。记得: 1.加上条件: deleted_at is null;  2.更新<updated_at>: updated_at = CURRENT_TIME()
*/
type BitsSettingWriteRepo interface {
	// inserter()
	InsertBitsSetting(setting *setting.BitsSetting) (int64, error)

	// sql(value = "update bits_setting set settings = #setting, updated_at = CURRENT_TIME() where app_id = #appId and type = #theType and deleted_at is null", mode="named_placeholder")
	UpdateBitsSettingByAppIdAndType(appId int64, theType string, setting string) (int64, error)
}

type BitsSettingRepo interface {
	BitsSettingReadRepo
	BitsSettingWriteRepo
}
