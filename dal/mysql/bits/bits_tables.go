package bits

import (
	"time"
)

func (Integration) TableName() string {
	return "integration"
}

type Integration struct {
	IsFreeze          int8      `gorm:"column:is_freeze" json:"is_freeze"`                   // 是否被冻结 0未冻结，1已冻结
	TechStack         int       `gorm:"column:tech_stack" json:"tech_stack"`                 // 应用类型, 技术栈 1 iOS 2 Android
	UniqueType        int       `gorm:"column:unique_type" json:"unique_type"`               // 关联应用或组件 id
	IntegrationType   int       `gorm:"column:integration_type" json:"integration_type"`     // 1 业务迭代 2 技术独立灰度
	ReleaseType       int       `gorm:"column:release_type" json:"release_type"`             // 0 对外发布 1 不对外发布
	Status            int       `gorm:"column:status" json:"status"`                         // 集成区的状态 0 未开始 1 开启中 2 已冻结 3 灰度中 4 已发布 5 已关闭
	SubStatus         int       `gorm:"column:sub_status" json:"sub_status"`                 // 集成区子状态
	AutoChangeFlag    int       `gorm:"column:auto_change_flag" json:"auto_change_flag"`     // 集成区状态自动变化的状态记录 1 等待开启 2 灰度创建分支或 mr 中
	ReadyToOpen       int       `gorm:"column:ready_to_open" json:"ready_to_open"`           // 集成区处于就绪准备开启状态
	ParseStatus       int       `gorm:"column:parse_status" json:"parse_status"`             // 依赖解析的状态
	Id                int64     `gorm:"column:id;primary_key" json:"id"`                     // 集成区id
	UniqueId          int64     `gorm:"column:unique_id" json:"unique_id"`                   // 关联的是应用还是组件, 1 App 2 组件
	GitlabProjectId   int64     `gorm:"column:gitlab_project_id" json:"gitlab_project_id"`   // gitlab project id
	GitlabRepoId      int64     `gorm:"column:gitlab_repo_id" json:"gitlab_repo_id"`         // gitlab repo id
	BeginTime         int64     `gorm:"column:begin_time" json:"begin_time"`                 // 开始时间戳 毫秒级
	GreyTime          int64     `gorm:"column:grey_time" json:"grey_time"`                   // 灰度时间戳 毫秒级
	FreezeTime        int64     `gorm:"column:freeze_time" json:"freeze_time"`               // 冻结时间戳 毫秒级
	PlanPublishTime   int64     `gorm:"column:plan_publish_time" json:"plan_publish_time"`   // 计划发布时间戳 毫秒级
	PublishTime       int64     `gorm:"column:publish_time" json:"publish_time"`             // 发布时间戳 毫秒级
	GreyDevtaskId     int64     `gorm:"column:grey_devtask_id" json:"grey_devtask_id"`       // 灰度创建时的 merge 到 grey 分支的研发任务 id
	GreyEndTime       int64     `gorm:"column:grey_end_time" json:"grey_end_time"`           // 灰度结束时间戳 毫秒级
	MeegoVersionId    int64     `gorm:"column:meego_version_id" json:"meego_version_id"`     // meego 版本 id
	Ctime             time.Time `gorm:"column:ctime" json:"ctime"`                           // 创建时间
	Mtime             time.Time `gorm:"column:mtime" json:"mtime"`                           // 修改时间
	Name              string    `gorm:"column:name" json:"name"`                             // 集成区名称
	Version           string    `gorm:"column:version" json:"version"`                       // 集成区版本号
	BaseBranch        string    `gorm:"column:base_branch" json:"base_branch"`               // 基线分支
	BaseCommitId      string    `gorm:"column:base_commit_id" json:"base_commit_id"`         // 基线提交id
	MergeBranch       string    `gorm:"column:merge_branch" json:"merge_branch"`             // 合入分支
	CurrentCommitId   string    `gorm:"column:current_commit_id" json:"current_commit_id"`   // 集成区的当前 cid
	Creator           string    `gorm:"column:creator" json:"creator"`                       // 创建人
	SubJob            string    `gorm:"column:sub_job" json:"sub_job"`                       // 灰度阶段
	SubJobStatus      string    `gorm:"column:sub_job_status" json:"sub_job_status"`         // 灰度阶段成功或失败的信息
	RdBm              string    `gorm:"column:rd_bm" json:"rd_bm"`                           // RDBM，可多个
	QaBm              string    `gorm:"column:qa_bm" json:"qa_bm"`                           // QABM，可多个
	CrashBm           string    `gorm:"column:crash_bm" json:"crash_bm"`                     // CrashBM，可多个
	Qa                string    `gorm:"column:qa" json:"qa"`                                 // QA，可多个
	LarkGroup         string    `gorm:"column:lark_group" json:"lark_group"`                 // lark 群 id
	Description       string    `gorm:"column:description" json:"description"`               // 集成区描述
	TargetsBase       string    `gorm:"column:targets_base" json:"targets_base"`             // iOS 的 target 类型或者 Android 的 flavor
	TargetsCurrent    string    `gorm:"column:targets_current" json:"targets_current"`       // 当前 target 类型
	ComponentsBase    string    `gorm:"column:components_base" json:"components_base"`       // 基线组件列表
	ComponentsCurrent string    `gorm:"column:components_current" json:"components_current"` // 当前组件列表
	ComponentsChanged string    `gorm:"column:components_changed" json:"components_changed"` // 组件变更列表，每次提交集成时更新下这个字段的数据
	ExtInfo           string    `gorm:"column:ext_info" json:"ext_info"`                     // 集成区的其他附带信息
	ParseUrl          string    `gorm:"column:parse_url" json:"parse_url"`                   // 依赖解析的地址
	BindChatIds       string    `gorm:"column:bind_chat_ids" json:"bind_chat_ids"`           // 绑定群id列表
	MeegoTimeInfo     string    `gorm:"column:meego_time_info" json:"meego_time_info"`       // meego 时间信息
	WorkflowStatus    int       `gorm:"column:workflow_status" json:"workflow_status"`
	Labels            string    `gorm:"column:labels" json:"labels"`
}

func NewIntegration() *Integration {
	return &Integration{}
}
func (AppInfo) TableName() string {
	return "app_info"
}

type AppInfo struct {
	Id              int64     `gorm:"column:id;primary_key" json:"id"`                 // 主键id,内部app id
	AppCloudId      int64     `gorm:"column:app_cloud_id" json:"app_cloud_id"`         // 应用云的id
	ProductId       int64     `gorm:"column:product_id" json:"product_id"`             // app关联的产品线ID
	RelateAppId     int64     `gorm:"column:relate_app_id" json:"relate_app_id"`       // 关联appId
	LegacyAppId     int64     `gorm:"column:legacy_app_id" json:"legacy_app_id"`       // 老appid
	CreateTime      time.Time `gorm:"column:create_time" json:"create_time"`           // 创建日期
	UpdateTime      time.Time `gorm:"column:update_time" json:"update_time"`           // 更新日期
	ChineseName     string    `gorm:"column:chinese_name" json:"chinese_name"`         // 中文名
	EnglishName     string    `gorm:"column:english_name" json:"english_name"`         // 英文名
	TechnologyStack string    `gorm:"column:technology_stack" json:"technology_stack"` // 技术栈 枚举:Android,iOS
	Description     string    `gorm:"column:description" json:"description"`           // 应用描述
	Region          string    `gorm:"column:region" json:"region"`                     // 地区 枚举值, 国内:cn,海外:overseas
	Logo            string    `gorm:"column:logo" json:"logo"`                         // logo地址
	GitUrl          string    `gorm:"column:git_url" json:"git_url"`                   // git地址
}

func NewAppInfo() *AppInfo {
	return &AppInfo{}
}
func (GrayInfo) TableName() string {
	return "gray_info"
}

type GrayInfo struct {
	StatusClose   int8   `gorm:"column:status_close" json:"status_close"` // 是否关闭
	GrayType      int8   `gorm:"column:gray_type" json:"gray_type"`
	Id            int64  `gorm:"column:id;primary_key" json:"id"`               // 灰度id
	IntegrationId int64  `gorm:"column:integration_id" json:"integration_id"`   // 集成区id
	CreateTime    int64  `gorm:"column:create_time" json:"create_time"`         // create_time
	Round         int64  `gorm:"column:round" json:"round"`                     // 灰度轮次
	PlanPushCount int64  `gorm:"column:plan_push_count" json:"plan_push_count"` // 发布量
	CloseTime     int64  `gorm:"column:close_time" json:"close_time"`           // close_time
	CreateUser    string `gorm:"column:create_user" json:"create_user"`         // 创建人邮箱
	Statuses      string `gorm:"column:statuses" json:"statuses"`               // 状态json list
	Version       string `gorm:"column:version" json:"version"`                 // 灰度版本号
	Name          string `gorm:"column:name" json:"name"`                       // 灰度名字
	GrayStatus    string `gorm:"column:gray_status" json:"gray_status"`         // 灰度整体状态
}

func NewGrayInfo() *GrayInfo {
	return &GrayInfo{}
}
func (GrayBuild) TableName() string {
	return "gray_build"
}

type GrayBuild struct {
	Id          int64     `gorm:"column:id;primary_key" json:"id"`       // 无关id
	GrayId      int64     `gorm:"column:gray_id" json:"gray_id"`         // 灰度id
	Batch       int64     `gorm:"column:batch" json:"batch"`             // 批次
	JobId       int64     `gorm:"column:job_id" json:"job_id"`           // 构建job的id
	SettingsId  int64     `gorm:"column:settings_id" json:"settings_id"` // 所使用的构建配置id
	PipelineId  int64     `gorm:"column:pipeline_id" json:"pipeline_id"` // ci pipeline任务id
	ArtifactId  int64     `gorm:"column:artifact_id" json:"artifact_id"` // 母包产物id
	CreatedAt   time.Time `gorm:"column:created_at" json:"created_at"`   // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at" json:"updated_at"`   // 更新时间
	Channel     string    `gorm:"column:channel" json:"channel"`         // 构建渠道
	Description string    `gorm:"column:description" json:"description"` // 用户自定义描述
	Version     string    `gorm:"column:version" json:"version"`         // 灰度包版本号
	Branch      string    `gorm:"column:branch" json:"branch"`           // 编译时的branch
	CommitId    string    `gorm:"column:commit_id" json:"commit_id"`     // 编译时的commit_id
}

func NewGrayBuild() *GrayBuild {
	return &GrayBuild{}
}

func (OfficialBuild) TableName() string {
	return "official_build"
}

type OfficialBuild struct {
	ArtifactType   int8       `gorm:"column:artifact_type" json:"artifact_type"`                                       // 1:母包且未完全同步子包;2:母包已同步子包;3:子包
	BuildSource    int        `gorm:"column:build_source" json:"build_source"`                                         // 包同步来源
	CiType         int        `gorm:"column:ci_type" json:"ci_type"`                                                   // 编译类型
	ScenarioType   int        `gorm:"column:scenario_type" json:"scenario_type"`                                       // 编译配置类型
	Id             int64      `gorm:"column:id;primary_key" json:"id"`                                                 // 无关id
	IntegrationId  int64      `gorm:"column:integration_id" json:"integration_id"`                                     // 集成区id
	Batch          int64      `gorm:"column:batch" json:"batch"`                                                       // 批次
	JobId          int64      `gorm:"column:job_id" json:"job_id"`                                                     // 构建job的id
	ParentJobId    int64      `gorm:"column:parent_job_id" json:"parent_job_id"`                                       // 母包job_id
	SettingsId     int64      `gorm:"column:settings_id" json:"settings_id"`                                           // 所使用的构建配置id
	ArtifactId     int64      `gorm:"column:artifact_id" json:"artifact_id"`                                           // 母包产物id
	ReviewTicketId int64      `gorm:"column:review_ticket_id" json:"review_ticket_id"`                                 // 预审 id
	ConfigId       int64      `gorm:"column:config_id" json:"config_id"`                                               // 编译配置ID
	TaskId         int64      `gorm:"column:task_id" json:"task_id"`                                                   // 编译任务ID
	PipelineId     int64      `gorm:"column:pipeline_id" json:"pipeline_id"`                                           // ci pipeline任务id
	Ctime          *time.Time `gorm:"column:ctime;default:CURRENT_TIMESTAMP" json:"ctime"`                             // 创建时间 tags:{"gorm":"column:ctime;default:CURRENT_TIMESTAMP"} type:*time.Time
	Mtime          *time.Time `gorm:"column:mtime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP" json:"mtime"` // 修改时间 tags:{"gorm":"column:mtime;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"} type:*time.Time
	Channel        string     `gorm:"column:channel" json:"channel"`                                                   // 构建渠道
	Description    string     `gorm:"column:description" json:"description"`                                           // 用户自定义描述
	Version        string     `gorm:"column:version" json:"version"`                                                   // version
	Branch         string     `gorm:"column:branch" json:"branch"`                                                     // 编译时的branch
	CommitId       string     `gorm:"column:commit_id" json:"commit_id"`                                               // 编译时的commit_id
}

func NewOfficialBuild() *OfficialBuild {
	return &OfficialBuild{}
}

func (RegressionBuild) TableName() string {
	return "regression_build"
}

type RegressionBuild struct {
	CiType        int    `gorm:"column:ci_type" json:"ci_type"`               // 编译类型
	ScenarioType  int    `gorm:"column:scenario_type" json:"scenario_type"`   // 编译配置类型
	Id            int64  `gorm:"column:id;primary_key" json:"id"`             // 无关id
	IntegrationId int64  `gorm:"column:integration_id" json:"integration_id"` // 集成区id
	Batch         int64  `gorm:"column:batch" json:"batch"`                   // 批次
	JobId         int64  `gorm:"column:job_id" json:"job_id"`                 // 构建job的id
	SettingsId    int64  `gorm:"column:settings_id" json:"settings_id"`       // 所使用的构建配置id
	PipelineId    int64  `gorm:"column:pipeline_id" json:"pipeline_id"`       // ci pipeline任务id
	ArtifactId    int64  `gorm:"column:artifact_id" json:"artifact_id"`       // 母包产物id
	ConfigId      int64  `gorm:"column:config_id" json:"config_id"`           // 编译配置ID
	TaskId        int64  `gorm:"column:task_id" json:"task_id"`               // 编译任务ID
	Channel       string `gorm:"column:channel" json:"channel"`               // 构建渠道
	// description 在数据库中是关键词，所以注意此处两个字段名字不一样
	Description string `gorm:"column:characterization" json:"characterization"` // 用户自定义描述
	Version     string `gorm:"column:version" json:"version"`                   // 灰度包版本号
	Branch      string `gorm:"column:branch" json:"branch"`                     // 编译时的branch
	CommitId    string `gorm:"column:commit_id" json:"commit_id"`               // 编译时的commit_id
}

func NewRegressionBuild() *RegressionBuild {
	return &RegressionBuild{}
}
