package mysql

import (
	"code.byted.org/clientQA/artifact-manager/domain"
	"code.byted.org/gopkg/gorm"
)

//go:generate easytags $GOFILE
type FreqReviewConfigRepo struct {
	Common
	domain.FreqReviewConfig
}

func NewFreqReviewConfigRepository(dbR *gorm.DB, dbW *gorm.DB) (*FreqReviewConfigRepo, error) {
	m := &FreqReviewConfigRepo{
		Common: Common{
			Mysql: Mysql{
				dbR: dbR,
				dbW: dbW,
			},
		},
	}
	return m, nil
}

func (repo *FreqReviewConfigRepo) TableName() string {
	return "bits_freq_review_conf"
}

func (repo *FreqReviewConfigRepo) Create(freqReviewConf *domain.FreqReviewConfig) (int64, error) {
	item := &FreqReviewConfigRepo{
		Common{},
		*freqReviewConf,
	}
	err := repo.dbW.Create(item).Error
	if err != nil {
		return 0, err
	}
	return int64(item.ID), nil
}

func (repo *FreqReviewConfigRepo) Query(aid int64, platform string) ([]*domain.FreqReviewConfig, error) {
	var items []FreqReviewConfigRepo
	err := repo.dbR.Model(&FreqReviewConfigRepo{}).Where("aid = ? and platform = ?", aid, platform).
		Where("deleted_at is NULL").
		Find(&items).Error

	freqReviewConfs := make([]*domain.FreqReviewConfig, len(items))
	for idx, item := range items {
		freqReviewConf := item.FreqReviewConfig
		freqReviewConf.Id = int64(item.ID)
		freqReviewConfs[idx] = &freqReviewConf
	}
	if err != nil {
		return freqReviewConfs, err
	}
	return freqReviewConfs, nil
}

func (repo *FreqReviewConfigRepo) UpdateConf(id int32, reviewConf string) error {
	m := make(map[string]interface{})
	m["review_config"] = reviewConf
	err := repo.dbR.Model(&FreqReviewConfigRepo{}).
		Where("id = ? ", id).
		Update(m).Error
	if err != nil {
		return err
	}
	return nil
}
