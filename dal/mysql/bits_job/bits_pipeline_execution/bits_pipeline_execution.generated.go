// Code generated by COMMENTS_BUILD_TOOLS 2.0.41. DO NOT EDIT.
package bits_pipeline_execution

import (
	"database/sql"
	"errors"

	"code.byted.org/clientQA/artifact-manager/domain/bits_job"
	"code.byted.org/gopkg/gorm"
	"code.byted.org/gopkg/logs"
)

var GlobalErrBitsPipelineExecutionReadRepo = struct {
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
	NothingExecute          error
}{
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
	errors.New("NothingExecuteErr"),
}

func NewBitsPipelineExecutionReadRepo(handler *gorm.DB) BitsPipelineExecutionReadRepo {
	return &_BitsPipelineExecutionReadRepoStruct{
		handler: handler,
	}
}

type _BitsPipelineExecutionReadRepoStruct struct {
	handler *gorm.DB
}

func (interstruct *_BitsPipelineExecutionReadRepoStruct) Find(id int64) (*bits_job.BitsPipelineExecution, error) {
	_result, _retErr := func() (*bits_job.BitsPipelineExecution, error) {
		_sqlText := "select * from bits_pipeline_execution where id = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Raw(_sqlText, id)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		var _ret = new(bits_job.BitsPipelineExecution)
		_sdb = _sdb.Scan(_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.Errorf("Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return nil, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsPipelineExecutionReadRepoStruct) FindByRefType(bitsAppID int64, refType string, refKey string) (*bits_job.BitsPipelineExecution, error) {
	_result, _retErr := func() (*bits_job.BitsPipelineExecution, error) {
		_sqlText := "select * from bits_pipeline_execution where bits_app_id = ? and ref_type = ? and ref_key = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Raw(_sqlText, bitsAppID, refType, refKey)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		var _ret = new(bits_job.BitsPipelineExecution)
		_sdb = _sdb.Scan(_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.Errorf("Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return nil, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsPipelineExecutionReadRepoStruct) FindByStateMachineExecutionID(stateMachineExecutionID int64) (*bits_job.BitsPipelineExecution, error) {
	_result, _retErr := func() (*bits_job.BitsPipelineExecution, error) {
		_sqlText := "select * from bits_pipeline_execution where state_machine_execution_id = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Raw(_sqlText, stateMachineExecutionID)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return nil, _sdb.Error
		}
		var _ret = new(bits_job.BitsPipelineExecution)
		_sdb = _sdb.Scan(_ret)
		if _sdb.Error != nil {
			if _sdb.Error != gorm.ErrRecordNotFound && _sdb.Error != sql.ErrNoRows {
				logs.Errorf("Scan: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			}
			return nil, _sdb.Error
		}
		return _ret, nil
	}()
	return _result, _retErr
}

var GlobalErrBitsPipelineExecutionWriteRepo = struct {
	EmptySliceErr           error
	EmptyParameter          error
	AttrSizeInConsistentErr error
	NothingExecute          error
}{
	errors.New("EmptySliceError"),
	errors.New("EmptyParameter"),
	errors.New("AttrSizeInConsistentErr"),
	errors.New("NothingExecuteErr"),
}

func NewBitsPipelineExecutionWriteRepo(handler *gorm.DB) BitsPipelineExecutionWriteRepo {
	return &_BitsPipelineExecutionWriteRepoStruct{
		handler: handler,
	}
}

type _BitsPipelineExecutionWriteRepoStruct struct {
	handler *gorm.DB
}

func (interstruct *_BitsPipelineExecutionWriteRepoStruct) Insert(bitsJobPipelineExecution *bits_job.BitsPipelineExecution) (int64, error) {
	if bitsJobPipelineExecution == nil {
		return 0, GlobalErrBitsPipelineExecutionWriteRepo.EmptyParameter
	}
	_result, _retErr := func() (int64, error) {
		_db := interstruct.handler
		_sdb := _db.Create(bitsJobPipelineExecution)
		if _sdb.Error != nil {
			logs.Errorf("BitsPipelineExecutionWriteRepo.Insert occur error: %s", _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
func (interstruct *_BitsPipelineExecutionWriteRepoStruct) UpdateStatus(id int64, status string) (int64, error) {
	_result, _retErr := func() (int64, error) {
		_sqlText := "update bits_pipeline_execution set status = ?, updated_at = CURRENT_TIME() where id = ? and deleted_at is null"
		_db := interstruct.handler
		_sdb := _db.Exec(_sqlText, status, id)
		if _sdb.Error != nil {
			logs.Errorf("execute: %s, occur error: %s", _sqlText, _sdb.Error.Error())
			return 0, _sdb.Error
		}
		return _sdb.RowsAffected, nil
	}()
	return _result, _retErr
}
