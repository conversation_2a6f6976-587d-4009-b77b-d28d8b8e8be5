package mysql

import (
	"code.byted.org/clientQA/artifact-manager/domain"
	"code.byted.org/gopkg/gorm"
)

//go:generate easytags $GOFILE
type GrayReleaseCntChannelHistoryRepo struct {
	LegacyCommon
	domain.GrayReleaseCntChannelHistory
}

func NewGrayReleaseCntChannelHistoryRepository(dbR *gorm.DB, dbW *gorm.DB) (*GrayReleaseCntChannelHistoryRepo, error) {
	m := &GrayReleaseCntChannelHistoryRepo{
		LegacyCommon: LegacyCommon{
			Mysql: Mysql{
				dbR: dbR,
				dbW: dbW,
			},
		},
	}
	return m, nil
}

func (repo *GrayReleaseCntChannelHistoryRepo) TableName() string {
	return "gray_release_channel_cnt_history"
}

func (repo *GrayReleaseCntChannelHistoryRepo) Create(grayReleaseInfo *domain.GrayReleaseCntChannelHistory) (uint, error) {
	model := &GrayReleaseCntChannelHistoryRepo{
		LegacyCommon{},
		*grayReleaseInfo,
	}
	err := repo.dbW.Create(&model).Error
	if err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (repo *GrayReleaseCntChannelHistoryRepo) Find(id uint) (*domain.GrayReleaseCntChannelHistory, error) {
	item := GrayReleaseCntChannelHistoryRepo{}
	err := repo.dbR.Model(&GrayReleaseRepo{}).Where("id = ? ", id).First(&item).Error
	if err != nil {
		return &domain.GrayReleaseCntChannelHistory{}, err
	}
	model := &item.GrayReleaseCntChannelHistory
	return model, nil
}

func (repo *GrayReleaseCntChannelHistoryRepo) Query(aid int64, version string,platform string,channel string) ([]*domain.GrayReleaseCntHistoryQueryRes, error) {
	var items []GrayReleaseCntChannelHistoryRepo
	err := repo.dbR.Model(&GrayReleaseCntChannelHistoryRepo{}).Where("aid = ? and platform = ? and version = ? and channel =?", aid, platform,version,channel).
		Find(&items).
		Order("add_time asc").Error

	res := make([]*domain.GrayReleaseCntHistoryQueryRes, len(items))
	for idx, item := range items {
		res[idx] = &domain.GrayReleaseCntHistoryQueryRes{
			Cnt:     int64(item.Cnt),
			AddTime: item.AddTime,
		}
	}
	if err != nil {
		return res, err
	}
	return res, nil
}
