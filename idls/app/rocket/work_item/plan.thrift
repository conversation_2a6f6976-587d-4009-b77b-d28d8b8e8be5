include "../../../base.thrift"
include "field.thrift"
include "common.thrift"

namespace go ep.rocket.workitem.plan
namespace py ep.rocket.workitem.plan

//创建计划workitem
struct CreatePlanWIRequest{
    1: list<field.FieldValue> FieldValues,
    3: i64                    ProjectId,
    4: string                 Operator
    5: common.WorkItemGroup   WorkItemGroup,

    255: base.Base Base,
}

struct CreatePlanWIResponse{
    1: i64  PlanId,

    255: base.BaseResp BaseResp,
}
// 编辑计划workitem
struct EditPlanWIRequest{
    1: i64 PlanId,
    2: list<field.FieldValue> FieldValues,
    3: list<string> ClearFields,
    4: i64 ProjectId,
    5: string Operator,

    255: base.Base Base,
}

struct EditPlanWIResponse{
    1: i64 PlanId,
    2: list<field.FieldValue> FieldValues,
    3: i64 ProjectId,
    255: base.BaseResp BaseResp,
}
// 查询计划workitem
struct FindPlanWIRequest{
    1: i64 PlanWIId,

    255: base.Base Base,
}

struct FindPlanWIResponse{
    1: list<field.FieldValue> FieldValues,

    255: base.BaseResp BaseResp,
}
// 删除计划workitem
struct DeletePlanWIRequest{
    1: i64 PlanWIId,
    2: string Operator,

    255: base.Base Base,
}

struct DeletePlanWIResponse{
    1: list<field.FieldValue> FieldValues,

    255: base.BaseResp BaseResp,
}
// 需求规划至活动
struct BindingActivityRequiresRequest {
    1: list<i64>        RequireIds,
    2: i64              ActivityId,
    3: string           Operator,

    255: base.Base      Base,
}
struct BindingActivityRequiresResponse {
    255: base.BaseResp  BaseResp,
}
// 工作项（需求 or 缺陷）批量移出迭代
struct UnBindingActivityRequiresRequest {
    1: list<i64>        RequireIds,
    2: i64              ActivityId,
    3: string           Operator,

    255: base.Base      Base,
}
struct UnBindingActivityRequiresResponse {
    255: base.BaseResp  BaseResp,
}
// 创建计划
struct CreatePlanRequest{
    1: i64 ParentId,
    2: i64 NodeId,
    3: i64 RelationType,

    255: base.Base Base,
}

struct CreatePlanResponse{

    255: base.BaseResp BaseResp,
}

// 获得整个计划树
struct GetPlanTreeRequest{
    1: i64 ProjectId,

    255: base.Base Base,
}

struct GetPlanTreeResponse{
    1: list<Plan> Plans,

    255: base.BaseResp BaseResp,
}

struct Plan{
    1: i64 Id,
    2: i64 ParentId,
    3: string Title,
    4: i64 WorkitemGroup,
    255: list<Plan> Children,

}