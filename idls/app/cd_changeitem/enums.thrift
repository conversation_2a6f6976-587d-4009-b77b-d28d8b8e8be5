namespace go bits.cd.change_item

enum ProjectLockState {
	PROJECT_LOCK_STATE_UNSPECIFIED = 0;
	PROJECT_LOCK_STATE_UNLOCK = 1;
	PROJECT_LOCK_STATE_LOCK = 2
}

enum ConvertorType {
	CONVERTOR_TYPE_UNSPECIFIED = 0;
	CONVERTOR_TYPE_CI = 1;
	CONVERTOR_TYPE_CD = 2;
}

enum ScmPubBase { //todo待讨论
  SCM_PUB_BASE_UNSPECIFIED = 0;
  // 基于版本发布
  SCM_PUB_BASE_VERSION = 1;
  // 基于分支发布
  SCM_PUB_BASE_BRANCH = 2;
  // 基于 commit 发布
  SCM_PUB_BASE_COMMIT = 3;
  // 基于 tag 发布
  SCM_PUB_BASE_TAG = 4;
}

enum SCMVersionType {
    SCMVERSION_TYPE_UNSPECIFIED = 0,
    SCMVERSION_TYPE_ONLINE = 1,
    SCMVERSION_TYPE_TEST = 2,
    SCMVERSION_TYPE_OFFLINE = 3,
    SCMVERSION_TYPE_DISCARD = 4
}

enum MrStatus {
    // 根据实际需要补充状态枚举值
    OPEN = 0,
    MERGED = 1,
    CLOSED = 2
}

enum DevMode {
    DEV_MODE_UNSPECIFIED = 0,
    DEV_MODE_VERSION = 1,
    DEV_MODE_CODE_CHANGE = 2,
    DEV_MODE_BRANCH = 3
}

enum ArtifactType {
	ARTIFACT_TYPE_UNSPECIFIED = 0;

	// 产物/制品类型为 scm
	ARTIFACT_TYPE_SCM = 1;

	ARTIFACT_TYPE_TCC = 2;
}