include "./devops_settings.thrift"

namespace go bytedance.bits.build_pkg

// {lark_host}/docs/doccniwHbWPpwv3wbN9XHZ8slfg
struct BuildMRPkgRequest {
    1: required string name,
    2: optional i64 env_id,
    3: optional i64 type_id,
    4: required bool all_source,                // 默认 false
    5: required string build_script,            // Android 一段 shell 脚本
    6: required list<devops_settings.AppBuildArtifact> artifacts_path, // 产物路径
    7: required string build_mode,              // iOS normal  或 build_for_test
    8: required string scheme,                  // iOS 后期支持自动解析选项，先手动填写
    9: required string configuration,           // iOS 后期支持自动解析选项，先手动填写
    10: required string architecture_mode,       // iOS single
    11: required string xcodebuild_extra,       // iOS 默认为空
    12: required bool is_boe,                   // iOS 默认为 false
    13: required string boe_env,                // iOS 默认 product
}

struct BuildGreyPkgRequest {
    1: required string name,
    2: optional i64 env_id,
    3: optional i64 type_id,
    4: required bool all_source,                // iOS 默认 false，安卓默认 true
    5: optional string build_script,            // Android 一段 shell 脚本
    6: required list<devops_settings.AppBuildArtifact> artifacts_path, // 产物路径
    7: optional string scheme,                  // iOS 后期支持自动解析选项，先手动填写
    8: optional string configuration,           // iOS 后期支持自动解析选项，先手动填写
    9: optional string architecture_mode,       // iOS single or double
    10: optional string xcodebuild_extra,        // iOS 默认为空
}

struct BuildFormalPkgRequest {
    1: required string name,
    2: optional i64 env_id,
    3: optional i64 type_id,
    4: required bool all_source,                // iOS 默认 false，安卓默认 true
    5: optional string build_script,            // Android 一段 shell 脚本
    6: required list<devops_settings.AppBuildArtifact> artifacts_path, // 产物路径
    7: optional string scheme,                  // iOS 后期支持自动解析选项，先手动填写
    8: optional string configuration,           // iOS 后期支持自动解析选项，先手动填写
    9: optional string architecture_mode,       // iOS single or double
    10: optional string xcodebuild_extra,        // iOS 默认为空
}

struct DependentRepository {
    1: required string git,
    2: optional string branch,
    3: optional string commit_id,
}

struct AndroidRepoVersion {
    1: required string flavorName,
    2: required string version,
}

struct AndroidRepos {
    1: required string component_name,
    2: required list<AndroidRepoVersion> versions,
}

struct PodfilePatch {
    1: required i64 repoId,
    2: required string name,
    3: optional string git,
    4: optional string branch,
    5: optional string commitId,
    6: optional string version,
    7: optional map<string, list<string>> app_struct,
}

struct DependentRepo {
    1: required list<DependentRepository> dependent_repo,
    2: required list<AndroidRepos> android_repos,
    3: required string podfile_patch,
}

// 参数定义 {lark_host}/docs/doccniwHbWPpwv3wbN9XHZ8slfg
struct BuildPkgParams {
    1: required string name,
    2: optional i64 env_id,
    3: optional i64 type_id,
    4: required string gitlab_ssh_url,
    5: required string commit_id,
    6: required DependentRepo dependent_repo,           // 辅助仓库信息 参见 https://bytedance.feishu.cn/docs/doccnoBL89tqTTNBCs8KQ3VZ2sg#
    7: required string version,                         // 平台自动提供，通过集成区获取
    8: required bool all_source,                        // 表示使用全源码的方式编译, 目前双端的策略都是：开发/自定义打开，灰度/正式关闭
    9: required string channel,                         // 参数定义 https://bytedance.feishu.cn/docs/doccniwHbWPpwv3wbN9XHZ8slfg
    10: required string script_template,                 // 开发组develop、灰度组beta、正式组normal，自定义 custom
    11: required list<string> android_publish_channel,  // Android 在正式构建时运营同学可以选择, 会在固定的母包渠道(update) 包基础上，产出不同的渠道包
    12: required string build_script,                   // Android 一段 shell 脚本
    13: required list<devops_settings.AppBuildArtifact> artifacts_path, // 产物路径
    14: required string build_mode,                     // iOS normal  或 build_for_test
    15: required string scheme,                         // iOS 后期支持自动解析选项，先手动填写
    16: required string configuration,                  // iOS 后期支持自动解析选项，先手动填写
    17: required string architecture_mode,              // iOS single
    18: required string xcodebuild_extra,               // iOS 默认为空
    19: required bool is_boe,                           // iOS 默认为 false
    20: required string boe_env,                        // iOS 默认 product
    21: required string buildaction,                    // iOS archive or build-for-testing
    22: required bool build_size_ipa,                   // iOS 是否打包大小的包
    23: required bool pod_patch_create,                 // iOS 是否打包的时候打patch
    24: optional string target_branch,                   // target branch
    25: optional bool merge_target,                      // need merge target
    26: optional string xcode_version (go.tag = 'json:\"XCODE_VERSION\"'),
}

struct FlutterDependRepotoryParams {
    1: required string name,        // 子工程名称
    2: optional string git,         // git 仓库地址
    3: optional string ref,         // 目标 ref，可以是 commitId、branch 或者 tag
    4: optional string commit_id,   // commit id
    5: optional string branch,      // 仓库分支
    6: optional string version,     // 版本变更的版本号
}

struct CloudBuildArtifactsPath {
    1: required string path,        // 构建产物路径
    2: required string type,        // 构建产物类型
}

struct FlutterBuildPkgParams {
    1: required string project,        				// 壳工程名称
    2: optional i64 type_id,        				// 构建环境 mac=3
    3: required i64 repo_id,                        // Flutter 
    4: required string repo_name,                   // Flutter 壳工程作为 iOS 组件发布的名称
    5: required string git,         				// git 仓库地址
    6: required string ref,         				// 目标 ref，可以是 commitId、branch 或者 tag
    7: optional string commit_id,   				// commit id
    8: optional string branch,      				// 仓库分支
    9: optional string version,     				// 平台自动提供，通过集成区获取
    10: optional list<FlutterDependRepotoryParams> dependencies 	// 依赖的子仓库
    11: optional list<CloudBuildArtifactsPath> aritfactsPath    // 云构建产物路径
}

service BuildPkgService {
    i64 A(1: required i64 req)
}
