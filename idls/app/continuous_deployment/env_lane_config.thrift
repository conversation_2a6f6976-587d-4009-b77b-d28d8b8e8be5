namespace go bits.devops.continuous_deployment
include "enums.thrift"

enum LaneGcStrategyType {
    LANE_GC_STRATEGY_TYPE_UNSPECIFIED = 0;
    LANE_GC_STRATEGY_TYPE_AFTER_RELEASE = 1;
    LANE_GC_STRATEGY_TYPE_NO_PROCESSING = 2;
}

struct EnvLaneSingleDcDeploymentConfig {
    1: bool isSingleDcDeployment,
    2: string idc,
}

struct LaneConfig {
    1: string laneType,
    2: string laneId,
    3: optional EnvLaneSingleDcDeploymentConfig singleDcDeployment,
    // 额外的泳道标识。当用户指定的情况下，将覆盖 lane_type 作为最终的环境名称前缀
    4: string overwritePrefix,
    5: optional LaneGcStrategyType laneGcStrategy
    6: optional bool disableDeploy, // 是否不需要部署
    7: optional bool enableHotDeploy, // 是否开启热部署
    8: optional bool enablePpeDebug, // 是否开启ppe调试
    9: list<enums.EnvType> envTypes,   //环境类型 用于区分env还是lane. https://bytedance.larkoffice.com/wiki/YMpqw3RvAiPmPXkuVzrcai1DnPc
}

struct EnvNodeLaneConfig {
    1: i64 nodeID,
    2: string nodeName;
    3: string nodeNameI18N;
    4: string nodeFixedName;
    5: EnvBOELaneConfig boe;
    6: EnvPPELaneConfig ppe;
    7: bool display; // 发布单有这个参数，明确是否还需要
}

struct EnvBOELaneConfig {
    1: bool enabled,
    2: list<LaneConfig> lanes,
}

struct EnvPPELaneConfig {
    1: bool enabled,
    2: list<LaneConfig> lanes,
}
