include "../base.thrift"

namespace go bits.devops.metrics

struct Pipeline {
    1: required i64 eventType, //事件
    2: required i64 eventTime, //事件发生时间
    3: required string eventId, //唯一标识事件ID，可使用此ID找到一次完整的事件开始到结束
    4: required i64 appId,
    5: required i64 devId,
    6: required i64 pipelineId,  // pipeline id
    7: required i64 projectId,
    8: required i64 mrId,
    9: required string status, // 状态
    10: optional i64 parentId, // 父 id
    11: required i64 devtaskId, // 开发任务id
    12: required i64 configId, // 配置id
    13: required string triggerType, // 触发类型
    14: required string operator, // 触发人
    15: optional string projectURL, // 仓库url
    16: optional string projectBranch, // 仓库分支
    17: optional string projectCommit, // 仓库commit
    18: optional map<string, string> env, // 环境变量
    19: required i64 sourceType, //触发来源
    20: optional i8 type, // pipeline 类型, 0 普通, 1: group
    21: optional string name, // pipeline 名字
    22: optional bool waitDepends, // 是否等待依赖
    23: optional list<Pipeline> pipelines, // 子pipeline
    24: optional list<string> dependOn, // 依赖的pipeline
    25: optional i64 iid, // MR IID
    26: optional string mrTitle, // mr title
    27: optional string projectName, // 仓库名
    28: required i64 stopped, // 结束时间
    29: optional list<Stage> stages, // 阶段信息
    30: required i64 created, // 创建时间
    31: required i64 updated, // 更新时间
    32: optional bool retried, // 是否重试
    33: optional i64 templateId,
    34: optional i64 templateVersionId,
    35: required i64 duration,
    255: optional base.Base Base
}

struct Job {
    1: required i64 eventType, //事件
    2: required i64 eventTime, //事件发生时间
    3: required string eventId, //唯一标识事件ID，可使用此ID找到一次完整的事件开始到结束
    4: required i64 jobId, // 任务 id
    5: required string type, // 任务类型: 外部服务(service)
    6: required string status, // 状态
    7: optional i64 parentId, // 上次的id
    8: required string jobName, // 任务名
    9: optional i64 pipelineId, // pipeline id
    10: required i64 created, // 创建时间
    11: optional i64 updated, // 更新时间
    12: required i64 started, // 开始时间
    13: required i64 stopped, // 结束时间
    14: optional list<Step> steps, // 步骤
    15: optional list<JobArtifacts> artifacts, // 产物
    16: optional string jobResult, // 结果json
    17: optional string logSummary, // 日志摘要
    18: optional string logURL, // 日志url
    19: optional string jobURL, // 任务url
    20: optional string errorMsg, // 错误信息
    21: optional i32 errorCode, // 错误码
    22: optional i64 estimatedRun, //预计执行时间
    23: optional map<string, string> env, // 结果env
    24: optional bool retried, // 是否重试
    25: optional list<string> dependensOn, // 依赖任务
    26: required string operator, // 触发人
    27: optional string params, // 触发时的参数json
    28: optional bool waitDepends, // 是否等待依赖
    29: optional string jobSettings, // 任务设置(YML)
    30: required i64 projectId, // 仓库id
    31: required string projectURL, // 仓库url
    32: required string projectBranch, // 仓库分支
    33: required string projectCommit, // 仓库commit
    34: optional string serviceName, // 外部服务名
    35: required bool allowFailed, // 允许失败
    36: optional AllowInfo allowInfo, // 允许信息
    37: optional string statusText, // 状态展示中文
    38: optional string displayName, // 展示名字
    39: optional i64 projectMrIid, // mr iid
    40: optional i64 tier, // 层
    41: optional string taskType, // task type
    42: optional i64 taskId, // task id
    43: optional i64 scheduled, // 调度时间
    44: optional bool skip, // 是否跳过
    45: optional string skip_message // 任务失败，提示跳过的信息
    46: optional list<string> owners // 任务负责人(s)邮箱
    47: optional string content // 任务自定义信息
    48: optional string content_level // 错误信息等级
    49: required string sourceType, //触发来源
    50:required i64 duration, // 运行时长
    51: required i64 pendingDuration, //排队时长
    52: optional string triggerType,//触发类型
}

struct Stage {
    1: required i64 id,
    2: required string name,
    3: required string status,
    4: required i32 order, //阶段的次序
}

struct JobArtifacts {
    1: required string name, // 名字
    2: required string url, // url
    3: required string type, // 类型
    4: required i64 size, // 大小
    5: required string md5,
    6: optional string ipa_plist,
    7: optional string qr_code,
    8: optional string pkg_url, // Android 的 apk 的 url iOS 的 ipa plist 的 url
    9: optional string commit_id,  // 产物对应仓库的commit id
    10: optional string bundle_id, // 自定义bundle_id，用于动态生成的ipa安装时plist
    11: optional string extra_info, // 自定义产物信息
}

struct AllowInfo {
    1: optional list<string> approvers, // 邮箱用户名
    2: optional string approveBy, // 被谁跳过
    3: optional string approveMsg, // 联系xxx跳过job, 失败待审批才有
}

struct Step {
    1: required i64 stepId, // 主键id
    2: required i64 jobId, // job id
    3: required string stageName, // stage 名字
    4: required string stepName, // step 名字
    5: required string status, // 状态
    6: optional i64 started, // 开始时间
    7: optional i64 stopped, // 结束时间
    8: optional string errorMsg, // 错误信息
    9: optional i32 errorCode, // 错误码
    10: required string stepDisplayName,  // step展示名
    11: required string stageDisplayName, // stage 展示名
    12: optional bool allowFailed, // 允许失败
    13: optional i32 plugin_id, // 插件id
    14: optional i32 plugin_iid, // 插件iid
}