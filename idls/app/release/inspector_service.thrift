include "inspector.thrift"

namespace go bytedance.artifact.inspector
namespace py bytedance.artifact.inspector

service InspectorService {
    /** 拆包获取信息并且判断是否已检测过 */
    inspector.GetPackageInfoResp ItcGetPackageInfo(1: required inspector.GetPackageInfoReq req) (api.get="/ard/v2/itc/open-api/get_package_info", api.category='ep.artifact.web未迁移部分', api.api_level='1')
    /** 创建任务并且同步上传包触发检测 */
    inspector.CreatePackageTaskResp ItcCreatePackageTask(1: required inspector.CreatePackageTaskReq req) (api.post="/ard/v2/itc/open-api/create_task", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='1')
    /** 创建任务 */
    inspector.CreateVersionTaskResp ItcCreateVersionTask(1: required inspector.CreateVersionTaskReq req)
    /** 上传包到指定任务触发检测 */
    inspector.UploadPackageResp ItcUploadPackage(1: required inspector.UploadPackageReq req) (api.post="/ard/v2/itc/app/version/upload", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 重试包检测 */
    inspector.RetryPackageResp ItcRetryPackage(1: required inspector.RetryPackageReq req) (api.post="/ard/v2/itc/app/version/package/retry", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 删除包 */
    inspector.DeletePackageResp ItcDeletePackage(1: required inspector.DeletePackageReq req) (api.post="/ard/v2/itc/app/version/delete", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 查询包状态 */
    inspector.GetPackageStatusResp ItcGetPackageStatus(1: required inspector.GetPackageStatusReq req) (api.post="/ard/v2/itc/app/status", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 检测工具回调 */
    inspector.ToolCallbackResp ItcToolCallback(1: required inspector.ToolCallbackReq req)
    /** 创建or更新工具配置(内部使用) */
    inspector.UpsertToolConfigResp ItcConfigTool(1: required inspector.UpsertToolConfigReq req) (api.post="/ard/v2/itc/api/tool/config", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='2')
    /** 解析检测结果 */
    inspector.ExtractResultResp ItcExtractResult(1: required inspector.ExtractResultReq req) (api.post="/ard/v2/itc/open-api/pkg/extract", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 创建版本合规报告 */
    inspector.CreateVersionReportResp ItcCreateVersionReport(1: required inspector.CreateVersionReportReq req) (api.post="/ard/v2/itc/version_report", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 查看版本合规报告 */
    inspector.GetVersionReportResp ItcGetVersionReport(1: required inspector.GetVersionReportReq req) (api.get="/ard/v2/itc/version_report", api.category='报告相关', api.api_level='0')
    /** 创建版本问题汇总报告 */
    inspector.CreateVersionReportSummaryResp ItcCreateVersionReportSummary(1: required inspector.CreateVersionReportSummaryReq req) (api.post="/ard/v2/itc/version_report/summary", api.category='报告相关', api.api_level='0')
    /** 查看问题详情 */
    inspector.IssueDetailResp ItcGetIssueDetails(1: required inspector.IssueDetailReq req) (api.post="/ard/v2/itc/app/version/report/detail", api.serializer='json', api.category='调用点详情', api.api_level='0')
    /** 查看权限描述详情 */
    inspector.PlistIssueDetailResp GetPlistIssueDetail(1: required inspector.PlistIssueDetailReq req) (api.post="/ard/v2/itc/app/plist/detail", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 处理审批流 */
    inspector.ProcessApprovalDetailsResp ProcessApprovalDetails(1: required inspector.ProcessApprovalDetailsReq req) (api.post="/ard/v2/itc/approval/details", api.serializer='json', api.category='审批相关', api.api_level='0')
    /** 修改审批流 */
    inspector.ChangeApprovalDetailsResp ChangeApprovalDetails(1: required inspector.ChangeApprovalDetailsReq req) (api.post="/ard/v2/itc/approval/details/change", api.serializer='json', api.category='审批相关', api.api_level='0')
    /** 创建meego缺陷单 */
    inspector.CreateMeegoBugResp CreateMeegoBug(1: required inspector.CreateMeegoBugReq req) (api.post="/ard/v2/itc/meego/bug", api.serializer='json', api.category='meego相关', api.api_level='0')
    /** 审批详情列表 */
    inspector.ApprovalDetailListResp ApprovalDetailList(1: required inspector.ApprovalDetailListReq req) (api.post="/ard/v2/itc/approval/list", api.serializer='json', api.category='审批相关', api.api_level='0')
    /** 获取审批人信息 */
    inspector.GetApproverListResp GetApproverList(1: required inspector.GetApproverListReq req) (api.get="/ard/v2/itc/approver", api.category='审批人相关', api.api_level='0')
    /** 初始化approval_detail表接口 */
    inspector.InitApprovalDetailsResp InitApprovalDetails(1: required inspector.InitApprovalDetailsReq req)
    /** 指定app空间匹配版本任务 */
    inspector.MatchTaskWithVersionResp MatchTaskWithVersion(1: required inspector.MatchTaskWithVersionReq req)
    /** 查询可选择的所有基准包信息 */
    inspector.GetAvailablePackagesResp GetAvailablePackages(1: required inspector.GetAvailablePackagesReq req) (api.get="/ard/v2/itc/app/version/package/compare", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 查询已选择的基准包信息 */
    inspector.GetBenchmarkPackageResp ItcGetBenchmarkPackage(1: required inspector.GetBenchmarkPackageReq req) (api.get="/ard/v2/itc/app/version/package/info", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 主动超时 */
    inspector.ActiveTerminationResp ItcActiveTermination(1: required inspector.ActiveTerminationReq req) (api.post="/ard/v2/itc/open-api/task/termination", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='1')
    /** 主动关闭审批流 */
    inspector.PassApprovalDetailsResp PassApprovalDetails(1: required inspector.PassApprovalDetailsReq req) (api.post="/ard/v2/itc/open-api/approval/pass", api.serializer='json', api.category='批量通过审批', api.api_level='0')
    /** 主动删除redis缓存 */
    inspector.DeleteCacheResp DeleteCache(1: required inspector.DeleteCacheReq req) (api.post="/ard/v2/itc/open-api/delete/cache", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='1')
    /** 查看预审合规报告 */
    inspector.GetReviewReportResp GetReviewReport(1: required inspector.GetReviewReportReq req) (api.get="/ard/v2/itc/review_report", api.category='报告相关', api.api_level='0')
    /** 查看报告结论原因概述 */
    inspector.GetReportConclusionResp GetReportConclusion(1: required inspector.GetReportConclusionReq req) (api.get="/ard/v2/itc/app/report/status", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 批量重检测包 */
    inspector.ReinspectPackageResp ReinspectPackage(1: required inspector.ReinspectPackageReq req) (api.post="/ard/v2/itc/open-api/app/version/report/reinspect", api.serializer='json', api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 通知审批人 */
    inspector.NotifyApproverResp NotifyApprover(1: required inspector.NotifyApproverReq req) (api.post="/ard/v2/itc/notify/approver", api.serializer='json', api.category='通知相关', api.api_level='0')
    // 查询问题池概览
    inspector.GetIssuePoolResp GetIssuePool(1: required inspector.GetIssuePoolReq req) (api.get="/ard/v2/itc/pool", api.category="问题池相关")
    // 查询问题池单个规则下的详情
    inspector.GetIssuePoolListResp GetIssuePoolList(1: required inspector.GetIssuePoolListReq req) (api.post="/ard/v2/itc/pool/list", api.serializer='json', api.category="问题池相关")
    // 查询问题池单个规则中的具体调用点详情
    inspector.GetIssuePoolItemDetailResp GetIssuePoolItemDetail(1: required inspector.GetIssuePoolItemDetailReq req) (api.get="/ard/v2/itc/pool/detail", api.category="问题池相关")
    // 批量删除问题池中的调用点结果
    inspector.DeleteIssuePoolItemsResp DeleteIssuePoolItems(1: required inspector.DeleteIssuePoolItemsReq req) (api.post="/ard/v2/itc/pool/delete", api.serializer='json', api.category="问题池相关")
    /** 规则检测历史记录 */
    inspector.DetectRuleHistoryResp DetectRuleHistory(1: required inspector.DetectRuleHistoryReq req) (api.get="/ard/v2/itc/open-api/detect/history", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 组件漏洞查询 */
    inspector.CheckComponentResp CheckComponent(1: required inspector.CheckComponentReq req) (api.get="/ard/v2/itc/component/item", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 安全漏洞查询 */
    inspector.GetSecurityItemsResp GetSecurityItems(1: required inspector.GetSecurityItemsReq req) (api.get="/ard/v2/itc/security/item", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 网络审计风险 */
    inspector.GetNetarchDetailResp GetNetarchDetail(1: required inspector.GetNetarchDetailReq req) (api.get="/ard/v2/itc/netarch/item", api.category='ep.artifact.web未迁移部分', api.api_level='0')
    /** 通过md5查询检测状态细节 */
    inspector.GetDetectResultByMd5Resp GetDetectResultByMd5(1: required inspector.GetDetectResultByMd5Req req)  (api.get="/ard/v2/itc/package/detect_result", api.category='检测结果相关', api.api_level='0')
    /** 预审单获取包检测历史列表信息 */
    inspector.GetDetectVersionListResp GetDetectVersionList(1: required inspector.GetDetectVersionListReq req)
    /** 预审单二进制包扫描报告基本信息 */
    inspector.GetDetectBasicInfoResp GetDetectBasicInfo(1: required inspector.GetDetectBasicInfoReq req) (api.post="/ard/v2/review/ticket/detect_basic", api.serializer='json', api.category='预审概览报告', api.api_level='0')
    /** OCR识别图片中的文字 */
    inspector.DetectPictureResp DetectPicture(1: required inspector.DetectPictureReq req)
    /** 文字敏感词匹配 */
    inspector.DetectTextResp DetectText(1: required inspector.DetectTextReq req)
    /** 通知组件负责人 */
    inspector.NotifyComponentOwnerResp NotifyComponentOwner(1: required inspector.NotifyComponentOwnerReq req) (api.post="/ard/v2/itc/notify/component/owner", api.serializer='json', api.category='通知相关', api.api_level='0')
    /** 安全检测平台配置 */
    inspector.SetDetectPlatformConfigResp SetDetectPlatformConfig(1: required inspector.SetDetectPlatformConfigReq req) (api.post="/ard/v2/itc/platform/config", api.serializer='json', api.category='平台配置相关', api.api_level='0')
    /** 获取安全检测平台配置信息 */
    inspector.GetDetectPlatformConfigResp GetDetectPlatformConfig(1: required inspector.GetDetectPlatformConfigReq req) (api.get="/ard/v2/itc/platform/config", api.category='平台配置相关', api.api_level='0')
    /** 废弃报告 */
    inspector.AbandonReportResp AbandonReport(1: required inspector.AbandonReportReq req) (api.post="/ard/v2/itc/report/abandon", api.category='报告相关', api.api_level='0')
    /** 发起群聊 */
    inspector.CreateLarkGroupResp CreateLarkGroup(1: required inspector.CreateLarkGroupReq req) (api.post = "/ard/v2/itc/lark_group/create", api.category='群聊相关', api.api_level='0')
    /** 获取「发起群聊」的前置信息，用于前端判断是否弹窗 */
    inspector.GetPreInfoForCreateGroupResp GetPreInfoForCreateGroup(1: required inspector.GetPreInfoForCreateGroupReq req) (api.get = "/ard/v2/itc/lark_group/pre_info", api.category='群聊相关', api.api_level='0')
    /** 源码定位 */
    inspector.GetCodeRetraceInfosResp GetCodeRetraceInfos(1: required inspector.GetCodeRetraceInfosReq req) (api.get="/ard/v2/itc/open-api/code_retrace", api.category='源码定位相关', api.api_level='0')
    /** PDPO工单查询 */
    inspector.GetPDPOItemsResp GetPDPOItems(1: required inspector.GetPDPOItemsReq req) (api.get="/ard/v2/itc/pdpo/item", api.category='PDPO工单相关', api.api_level='0')
    /** 针对检测问题发布评论 */
    inspector.PostIssueCommentResp PostIssueComment(1: required inspector.PostIssueCommentReq req) (api.post="/ard/v2/itc/issue/post_comment", api.category='检测结果相关', api.api_level='0')
    /** 获取检测问题的全部评论 */
    inspector.GetIssueCommentsResp GetIssueComments(1: required inspector.GetIssueCommentsReq req) (api.get="/ard/v2/itc/issue/get_comment", api.category='检测结果相关', api.api_level='0')
    /** 获取报告列表的数据概览 */
    inspector.GetDataOverviewOfReportsResp GetDataOverviewOfReports(1: required inspector.GetDataOverviewOfReportsReq req) (api.get="/ard/v2/itc/app/reports/overview", api.category='报告相关', api.api_level='0')
    /** 获取报告列表的筛选项 */
    inspector.GetFilterItemsOfReportsResp GetFilterItemsOfReports(1: required inspector.GetFilterItemsOfReportsReq req) (api.get="/ard/v2/itc/app/reports/filter_items", api.category='报告相关', api.api_level='0')

    /** 适用于workflow准出的可视化 */
    /** 查看报告结论概述 */
    inspector.GetReportConclusionForWorkflowResp GetReportConclusionForWorkflow(1: required inspector.GetReportConclusionForWorkflowReq req)
    /** 通过md5查询检测状态细节 */
    inspector.GetDetectResultForWorkflowResp GetDetectResultForWorkflow(1: required inspector.GetDetectResultForWorkflowReq req)
    /** 查看版本合规报告 */
    inspector.GetVersionReportForWorkflowResp ItcGetVersionReportForWorkflow(1: required inspector.GetVersionReportForWorkflowReq req) (api.get="/ard/v2/itc/workflow/version_report", api.category='报告相关', api.api_level='0')
    /** 查看预审合规报告 */
    inspector.GetReviewReportForWorkflowResp ItcGetReviewReportForWorkflow(1: required inspector.GetReviewReportForWorkflowReq req) (api.get="/ard/v2/itc/workflow/review_report", api.category='报告相关', api.api_level='0')
    /** 组件漏洞查询 */
    inspector.GetComponentItemsForWorkflowResp ItcGetComponentItemsForWorkflow(1: required inspector.GetComponentItemsForWorkflowReq req) (api.get="/ard/v2/itc/workflow/component/item", api.category='组件漏洞相关', api.api_level='0')
    /** 安全漏洞查询 */
    inspector.GetSecurityItemsForWorkflowResp ItcGetSecurityItemsForWorkflow(1: required inspector.GetSecurityItemsForWorkflowReq req) (api.get="/ard/v2/itc/workflow/security/item", api.category='安全漏洞相关', api.api_level='0')
    /** 网络审计风险 */
    inspector.GetNetarchItemsForWorkflowResp ItcGetNetarchItemsForWorkflow(1: required inspector.GetNetarchItemsForWorkflowReq req) (api.get="/ard/v2/itc/workflow/netarch/item", api.category='网络审计相关', api.api_level='0')
}
