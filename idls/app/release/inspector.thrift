include "../../base.thrift"

namespace go bytedance.artifact.inspector.itc
namespace py bytedance.artifact.inspector.itc

struct Rule { // common rule info, may add all engine rule detail infos
    1: required i64 id,
    3: required string type,
    4: required string level,
    6: required string name,
    7: required string owner,
    9: required string description, // 简略描述
    10: required string support_region,
    12: required bool able,
    13: required bool deleted,
    15: required string compliance_level,
    16: optional list<RuleDetail> details,
    17: required string md_description, // 详细描述
    18: required string case, // 案例
    19: required i64 upsert_time, // 全局规则的updsert时间

    20: optional i64 sync_time, // app规则的同步时间  不拆结构体了, thrift没有继承
    21: optional list<string> policy_id,
    22: optional i64 related_rule_id,
}

struct RuleDetail {
    1: required string engine,
    3: required string config,
    4: required i64 id,
    5: required bool deleted,
    6: required i64 rule_id,
    7: required string rule_index_info,
}

/** 检测状态 */
enum DetectionStatus {
	Processing  = 0	// 正在进行中
	Succeeded   = 1	// 成功
	Failed      = 2 // 失败
}

/** 报告状态 */
enum ReportStatus {
    Loading = 0  // 基准包选定完毕报告生成中
    Pending = 1  // 报告存在待处理，不存在待整改
    Fixing = 2   // 报告存在待整改
    Approved = 3 // 报告审批通过
    Unset = 4    // 未选定基准包
    Failed = 5   // 报告生成失败
    Passed = 6   // 没有新增，检测自动通过
    Abandoned = 7 // 报告废弃
}

enum ApprovalStatus {
    NoApproval   = 0 //暂无
    TempApproved = 1 //临时通过
    InApproval   = 2 //审批中
    Reject       = 3 //打回整改
    Approved     = 4 //审批通过
    Closed       = 5 //关闭
    MissReport   = 6 //误报
}

enum ApprovalSource {
    CD = 0     // CD审批
    CI = 1     // CI继承审批
    CIToCD = 2 // CD修改CI继承审批
}

/** 触发场景 */
enum SourceType {
    Unknown  = -1
    Manual   = 0 // 手动触发
    Security = 1 // 业务安全
    Sdk      = 2 // 三方SDK(已废弃)
    Review   = 3 // 预审流程(已合并入业务安全)
}

/** 包类型 */
enum PackageType {
    unknown     = -1
    apk         = 0
    ipa         = 1
    aar         = 2
    so          = 3
    a           = 4
    framework   = 5
    zip         = 6
    jar         = 7
    aab         = 8
}

/** 检测工具 */
enum Tool {
    PrivacyBinary = 1   // 二进制检测，细化到调用点，需要反混淆文件
    PrivacyString = 2   // 字符检测，只匹配原文
    PrivacyChinese = 3  // 中文常量检测
    PrivacyDomain = 4   // 跨境域名检测
    PrivacyProperty = 5 // 资源文件检测
    CallGraph = 6       // 调用链检测
    PrivacyPermission = 7 // 权限扫描
    Mobius = 10          // 安全中心漏洞检测
    Netarch = 20         // 网络中台审计
    Bpea = 30            // BPEA
}

/** 技术栈 */
enum Platform {
    Android = 0
    iOS     = 1
}

/** 包支持的CPU架构 */
enum Architecture {
    SupportNone = 0
    Support32 = 1
    Support64 = 2
    Support32And64 = 3
}

/** plist权限声明状态 */
enum PlistStatus {
    DECLARED            = 1
    USED                = 2
    DECLARED_AND_USED   = 3
}

/** 报告类型 */
enum ReportType {
    PackageReport = 0 // 灰度发布的对比报告
    ReleaseReport = 1 // 正式发布的大版本对比报告
}

/** 发布类型 */
enum ReleaseType {
    Gray     = 1    // 灰度
    Official = 2    // 正式
}

enum DetectType { //TODO 考虑和Tool合并
    Binary = 1 //二进制检查，调用信息更详细
    String = 2 //字符检查，只有敏感词信息
    Domain = 3 //跨境域名检查
}

/* 包(整包/中间产物) */
/** 包信息 */
struct Package {
    1: required PackageSource source
    2: optional PackageType pkg_type
    3: optional string filename
    4: optional string pkg_name
    5: optional string app_name
    /** 标识迭代的大版本号 */
    6: optional string main_version
    /** 对内小版本号 */
    7: optional string sub_version
    8: optional string md5
    /** 是否为新包 */
    9: optional bool is_new
    10: optional Architecture architecture
    11: optional string commit_id
    12: optional string branch
    13: optional i64 size
    14: optional string channel
    /** 目标操作系统版本号 */
    15: optional string target_sdk_version
    /** 最小兼容的操作系统版本号 */
    16: optional string min_sdk_version
    /** 真实对外大版本号 */
    25: optional string version_code
    // 与发布/包管理/预审的交互
    /** 本项目 - 包id */
    17: optional i64 pkg_id
    /** 包管理 - 产物id */
    18: optional i64 artifact_id
    /** 本项目 - 任务id */
    19: optional i64 task_id
    /** 发布流水线 - 任务id */
    20: optional i64 job_id
    /** 发布阶段 */
    21: optional string phase
    /** 上传人 */
    22: optional string creator
    /** 上传时间 */
    23: optional i64 upload_time
    24: optional ReleaseType release_type
    /** 构建任务名 */
    26: optional string build_job_name
    /** 构建时间 */
    27: optional i64 build_job_time
    /** 构建地址 */
    28: optional string build_job_url
}

/** 包来源以及辅助检测的信息 */
struct PackageSource {
    1: optional Platform platform
    2: required string download_url
    /** 带行号的混淆文件(Android) */
    3: optional string mapping_url
    /** dSYM.zip(iOS) */
	4: optional string dsym_url
	/** LinkMap.zip(iOS) */
	5: optional string linkmap_url
	6: optional Aux aux_param
	/** Jenkins打包链接 */
	7: optional string jenkins_url
	/** 重签名包的下载链接 */
    8: optional string resign_pkg_url
	/** 检测结果下载链接 */
	9: optional list<string> result_url_list
}

/** 辅助检测信息 */
struct Aux {
    /** 流水线触发检测时的完整请求参数 */
	1: optional string pipeline_info
}

/** 包状态 */
struct PackageStatus {
    1: required Package package_info
    2: required DetectionStatus detection_status
    3: required ReportStatus report_status
    4: optional string err_info
    5: optional string upload_phase
    6: optional string upload_source
    7: optional i64 display_id
    8: optional i64 abandoned_time
    9: optional string abandoned_reason
    10: optional string abandoned_operator
    11: optional string release_trigger_url
    12: required string current_status
    13: required string report_overview
    14: optional string approval_time
}

/** 检测工具调用情况 */
struct ToolCallResult {
	1: required Tool tool
	2: required DetectionStatus code
	3: optional string json_string_content
	4: optional string err_msg
	5: optional i64 pkg_id
	6: optional PackageSource source
	7: optional string job_url
}

/** 版本报告组成单元 */
struct VersionReportRst {
    1: required list<i64> in_approval_ids
    2: required ApprovalStatus approval_status
    3: required Rule rule
    4: required string rule_type_mark
    5: required list<string> related_emails
}

/** 报告权限信息 */
struct ReportPermission {
    1: required string name
    2: required string description
    3: required string reference
    4: required string level
    5: required bool is_declared
    6: optional bool is_used
    7: required string system_mark
}

/** 版本报告信息 */
struct VersionReport {
    1: required list<ReportPermission> permission_list
    2: required list<VersionReportRst> binary_scan_list
}


/** 预审报告组成单元 */
struct ReviewReportIssue {
    1: required list<i64> in_approval_ids
    2: required ApprovalStatus approval_status
    3: required Rule rule
    4: required list<string> related_emails
    5: required string system_mark
}

/** 预审报告信息 */
struct ReviewReport {
    1: required list<ReviewReportIssue> sensitive_word
    2: required list<ReviewReportIssue> privacy_api
    3: required list<ReviewReportIssue> sensitive_component
}

struct DetectObject {
    1: required string issue_stage
    2: required string origin
    3: required string version
    4: required i64 count
}

/** 审批信息 */
struct ApprovalInfo {
    /** 审批ID */
    1: required i64 id
    /** 审批人邮箱 */
    2: optional string approver_email
    /** 审批理由 */
    3: optional string approval_reason
    /** 审批时间 */
    4: optional string approval_time
}

struct ToolDisplay {
    1: required string tool_cn_name
    2: required string tool_en_name
    3: required list<DisplayIssue> display_issues
}

struct DisplayIssue {
    1: required string name
    2: required string description
    3: required ApprovalStatus approval_status
    4: required list<IssueDetail> details
}

/** 检测工具对于单条规则检测出的问题详情 */
struct IssueDetail {
    1: required i64 id
    2: required IssueSpot spot
    3: optional ApprovalInfo approval_info
    4: optional ApprovalStatus approval_status
    5: optional ApprovalSource approval_source
    6: optional string sub_version
    7: optional string report_url
    8: optional string resemble_mark
    9: optional string content_new
    10: optional string content_old
    /** CI阶段审批记录 */
    11: optional ApprovalInfo ci_approval_info
    /** CI阶段审批状态 */
    12: optional ApprovalStatus ci_approval_status
    /** 出现问题的代码仓库MR链接 */
    13: optional string mr_url
    /** 问题调用点的组件信息 */
    14: optional list<ComponentInfo> component_infos
    /** meego缺陷单链接 */
    15: optional string meego_url
    /** 调用链信息 */
    16: optional string call_graph_info
}

struct ComponentInfo {
    /** 代码文件 */
    1: optional string codebase_url
    /** 组件名 */
    2: optional string component_name
    /** 组件版本 */
    3: optional string component_version
    /** 组件二/三方属性 */
    4: optional bool is_third_party
    /** 组件负责人 */
    5: optional list<string> component_owners
    /** 组件页面 */
    6: optional string component_url
}

struct PlistContent {
    1: required string name
    2: required string description
    3: required string status
    4: required string value
    5: required string reference
    6: required string supported_version
    7: required list<string> related_permissions
}

struct ApprovalDetailData {
    /** 审批ID数组集合，将用于批量勾选规则时，计算用户已勾选的待审批问题数，以及审批时传递的ID数组 */
    1: required list<i64> in_approval_ids
    2: required Rule rule
    3: required ApprovalStatus approval_status
}

struct DetectRuleHistory {
    1: required i64 rule_id
    2: required Package package
    3: required string report_url
    4: required list<DetectRuleDetail> details
}

struct DetectRuleDetail {
    1: required i64 id
    2: required string content
    3: required string status
    4: required string approval_status
    5: required string approval_reason
}

/** 问题池概览结构体，目前展示内容只有规则信息，当后续需要展示其他信息时，可在此结构中添加 */
struct IssuePool {
    1: required Rule rule
}

/** 问题池详情调用点结构体 */
struct IssueSpot {
    /** 关键词 */
    1: required string item_key
    /** 原文信息 */
    2: optional string original
    /** 组件名 */
    3: optional string component
    /** 文件名 */
    4: optional string file
    /** 类名 */
    5: optional string class
    /** 方法名 */
    6: optional string method
    /** 行号 */
    7: optional string line_number
    /** 域名归属地 */
    8: optional string source
    /** 精准归因 codebase URL */
    9: optional list<string> codebase_urls
}

/** 问题池列表 */
struct IssuePoolItem {
    1: required i64 issue_pool_id
    2: required IssueSpot spot
    3: required i64 status
    4: required string version
    5: required string approver
    6: required string approval_time
}

struct CreateMeegoReqInfo {
    /*审批节点ID*/
    1: required i64 id
    /*组件负责人列表*/
    2: required list<string> component_owners
}

/** 组件漏洞 */
struct CheckComponentDetail {
    1: required string name
    2: required string version
    3: required string description
    4: required string check_result
    5: required string owner
    6: required string sec_detail_url
}

/** 安全中心漏洞 */
struct SecurityItem {
    1: required string name
    2: required string vuln_description
    3: required string state
    4: required string vuln_type
    5: required i64 level
    6: required string detail_url
    7: required string ticket_url
}

/** 网络审计漏洞 */
struct NetarchDetail {
    1: required string name
    2: required string detail
    3: required string result
    4: required string info_doc
    5: required string audit_link
}

/** 预审流程平台中关注的检测内容*/
struct DetectBasicInfo {
    1: required list<string> basic_info
    2: required list<string> used_and_undeclared
    3: required list<string> unused_and_declared
}

struct PkgInfo {
    1: required i64 sub_task_id
    2: required string min_version
    3: required string pkg_name
    4: required string md5
}

/****************** definition of request and response ******************/

struct GetPackageInfoReq {
	1: required list<PackageSource> package_source_list
	2: optional SourceType source_type
	3: optional i64 task_id
    255: base.Base Base
}

struct GetPackageInfoResp {
	1: optional list<Package> package_info_list
	2: optional list<string> failed_url_list
    255: base.BaseResp BaseResp
}

struct CreatePackageTaskReq {
	1: required list<Package> package_info_list
	2: required SourceType source_type
	3: required i64 app_id
	4: required Platform platform
	5: optional list<Tool> tool_id_list
	6: required string creator
	7: optional list<string> lark_user_name_list
	8: optional list<string> lark_group_id_list
	9: optional string callback_url
	10: optional string extra
    255: base.Base Base
}

struct CreatePackageTaskResp {
	1: optional i64 task_id
	2: optional list<ToolCallResult> failed_tool_call_list
    255: base.BaseResp BaseResp
}

struct CreateVersionTaskReq {
    1: required SourceType source_type
    2: required i64 app_id
    3: required Platform platform
    4: required string main_version
    5: required string creator
    6: optional i64 bits_id
    255: base.Base Base
}

struct CreateVersionTaskResp {
    1: required i64 task_id
    255: base.BaseResp BaseResp
}

struct UploadPackageReq {
    1: required i64 task_id
    2: optional Package package_info
    3: required string creator
    4: optional list<Tool> tool_id_list
    /** 灰度轮次 */
    5: optional i64 gray_round
    6: optional ReleaseType release_type
    /** 发布流水线 - 任务id */
    7: optional i64 bits_job_id
    /** 包管理 - 产物id */
    8: optional i64 artifact_id
    /** 发布流水线/workflow - 检测结果回调地址 */
    9: optional string callback_url
    /** 回调来源 - 区分发布流水线和workflow */
    10: optional bool is_workflow
    /** 是否进行中文检测 */
    11: optional bool chinese_scan
    /** 卡口检验范围 */
    12: optional CheckRuleConfig check_rule
    /** workflow触发检测的阶段名称 */
    13: optional string upload_phase
    /** 触发检测的跳转workflow链接 */
    14: optional string upload_source
    255: base.Base Base
}

struct UploadPackageResp {
    1: required i64 pkg_id
	2: optional list<ToolCallResult> failed_tool_call_list
    255: base.BaseResp BaseResp
}

struct RetryPackageReq {
    1: required i64 pkg_id
    2: required string creator
    255: base.Base Base
}

struct RetryPackageResp {
    1: optional list<ToolCallResult> failed_tool_call_list
    255: base.BaseResp BaseResp
}

struct DeletePackageReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct DeletePackageResp {
    255: base.BaseResp BaseResp
}

struct GetPackageStatusReq {
    1: optional i64 app_id
    2: optional Platform platform
    3: optional i64 pkg_id
    4: optional i64 page_size
    5: optional i64 page_num
    6: optional string upload_phase // 触发来源筛选
    7: optional DetectionStatus detection_status // 报告状态筛选
    8: optional ReportStatus report_status // 报告状态筛选
    9: optional string channel  // 产物渠道筛选
    10: optional string common_search_item  // 其他筛选项：版本号、commit_id
    255: base.Base Base
}

struct GetPackageStatusResp {
    1: required list<PackageStatus> package_status_list
    2: required i64 page_size
    3: required i64 page_num
    4: required i64 total
    255: base.BaseResp BaseResp
}

struct GetAvailablePackagesReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetAvailablePackagesResp {
    1: required list<Package> recommended_list
    2: required list<Package> entire_list
    255: base.BaseResp BaseResp
}

struct GetBenchmarkPackageReq {
    1: required i64 pkg_id
    2: required i64 report_type
    255: base.Base Base
}

struct GetBenchmarkPackageResp {
    1: optional Package package_info
    2: optional Package benchmark_info
    255: base.BaseResp BaseResp
}

struct ToolCallbackReq {
	1: required DetectionStatus code
	2: optional string err_msg
	3: required i64 task_id
	4: required Tool tool_id
	/** 检测json字串结果 */
	5: optional string data
	/** 任务执行地址 */
	6: optional string job_url
	/** 任务id */
	7: optional string job_id
	/** 任务结果json文件下载链接 */
	8: optional string job_file
	255: base.Base Base
}

struct ToolCallbackResp {
	255: base.BaseResp BaseResp
}

struct UpsertToolConfigReq {
    1: required i64 tool_id
    2: required i64 platform
    3: required string operator
    4: optional string method
    5: optional string content_type
    6: optional string url
    7: optional string param
    8: optional string header
    9: optional i64 status
    10: optional string cn_name
    11: optional string en_name
    12: optional i64 config_id
}

struct UpsertToolConfigResp {
    255: base.BaseResp BaseResp
}

struct ExtractResultReq {
    1: required i64 pkg_id
    2: required Tool tool
    255: base.Base Base
}

struct ExtractResultResp {
    255: base.BaseResp BaseResp
}

struct CreateVersionReportReq {
    1: required i64 pkg_id
    2: required i64 base_pkg_id
    255: base.Base Base
}

struct CreateVersionReportResp {
    255: base.BaseResp BaseResp
}

struct GetVersionReportReq {
    1: required i64 pkg_id
    2: optional ReportType report_type
	255: base.Base Base
}

struct GetVersionReportResp {
    1: required VersionReport current_report
    2: required VersionReport remain_report
    3: required VersionReport all_report
    4: required list<string> check_rule_level
    5: required i64 check_rule_count
    6: required list<string> not_check_rule_level
    7: required i64 not_check_rule_count
    8: required i64 reject_rule_count
    255: base.BaseResp BaseResp
}

struct GetVersionReportForWorkflowReq {
    1: required i64 pkg_id
	255: base.Base Base
}

struct GetVersionReportForWorkflowResp {
    1: required VersionReport check_current_report
    2: required list<string> check_rule_level
    3: required i64 check_rule_count
    4: required i64 reject_rule_count
    255: base.BaseResp BaseResp
}

struct CreateVersionReportSummaryReq {
    1: required i64 pkg_id
    2: optional string creator_email
    255: base.Base Base
}

struct CreateVersionReportSummaryResp {
    1: required string lark_open_chat_id
    255: base.BaseResp BaseResp
}

struct GetReviewReportReq {
    1: required i64 pkg_id
    2: optional ReportType report_type
    255: base.Base Base
}

struct GetReviewReportResp {
    1: required ReviewReport current_report
    2: required ReviewReport remain_report
    3: required ReviewReport all_report
    4: required list<string> check_rule_level
    5: required i64 check_rule_count
    6: required list<string> not_check_rule_level
    7: required i64 not_check_rule_count
    8: required i64 reject_rule_count
    255: base.BaseResp BaseResp
}

struct GetReviewReportForWorkflowReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetReviewReportForWorkflowResp {
    1: required ReviewReport check_current_report
    2: required list<string> check_rule_level
    3: required i64 check_rule_count
    4: required i64 reject_rule_count
    255: base.BaseResp BaseResp
}

struct GetReportConclusionReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetReportConclusionResp {
    1: required i64 current_rule_count
    2: required i64 current_count
    3: required i64 intercept_count
    4: required i64 in_approval_count
    5: required i64 reject_approval_count
    6: required list<string> check_privacy_level
    7: required list<string> check_review_level
    8: required double checkpoint_approval_rate
    9: optional ReportStatus privacy_status
    10: optional ReportStatus review_status
    11: optional ReportStatus security_status
    12: optional ReportStatus component_status
    13: optional ReportStatus netarch_status
    14: optional DetectionStatus call_graph_status
    255: base.BaseResp BaseResp
}

struct GetReportConclusionForWorkflowReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetReportConclusionForWorkflowResp {
    1: required i64 check_in_approval_count
    2: required i64 reject_approval_count
    255: base.BaseResp BaseResp
}

/* 查询版本任务下单个包 针对单个命中规则的问题详情 */
struct IssueDetailReq {
    1: required i64 pkg_id
    2: required i64 status
    3: required i64 rule_id
    4: required i64 report_type
	255: base.Base Base
}

struct IssueDetailResp {
    1: required Rule rule
    2: required string filename
    3: required string sub_version
    4: optional list<ToolDisplay> tool_list
    255: base.BaseResp BaseResp
}

struct PlistIssueDetailReq {
    1: required i64 pkg_id
    2: required string plist_name
	255: base.Base Base
}

struct PlistIssueDetailResp {
    1: optional PlistContent plist_content
    2: optional list<ToolDisplay> tool_list
    255: base.BaseResp BaseResp
}

struct ProcessApprovalDetailsReq {
    1: required i64 pkg_id
    2: required list<i64> ids
    3: required i64 command
    4: required string operator_email
    5: required string approval_reason
    255: base.Base Base
}

struct ProcessApprovalDetailsResp {
	255: base.BaseResp BaseResp
}

struct ChangeApprovalDetailsReq {
    1: required i64 pkg_id
    2: required list<i64> ids
    3: required i64 command
    4: required string operator
    5: required string approval_reason
    255: base.Base Base
}

struct ChangeApprovalDetailsResp {
	255: base.BaseResp BaseResp
}

struct CreateMeegoBugReq {
    1: required i64 pkg_id
    2: required list<CreateMeegoReqInfo> infos
    3: required string operator_email
    255: base.Base Base
}

struct CreateMeegoBugResp {
	255: base.BaseResp BaseResp
}

struct GetApproverListReq {
    1: required i64 bits_id
    255: base.Base Base
}

struct GetApproverListResp {
    1: required list<string> approver_emails
    255: base.BaseResp BaseResp
}

struct ApprovalDetailListReq {
    1: required i64 pkg_id
    2: required string user_email
    255: base.Base Base
}

struct ApprovalDetailListResp {
    1: optional Package package
    2: optional ReportStatus report_status
    /** 隐私合规详情列表 */
    3: required list<ApprovalDetailData> privacy
    /** 预审合规详情列表 */
    4: required list<ApprovalDetailData> review
    /** 新增问题总数 */
    5: required i64 total_count
    /** 新增问题中已审批通过数 */
    6: required i64 passed_count
    /** 新增问题中打回整改数 */
    7: required i64 rejected_count
    /** 新增问题中待审批数 */
    8: required i64 approval_count
    /** 新增问题中隐私合规待审批数 */
    9: required i64 privacy_approval_count
    /** 新增问题中预审合规待审批数 */
    10: required i64 review_approval_count
    255: base.BaseResp BaseResp
}

struct MatchTaskWithVersionReq {
    1: required i64 app_id
    2: required Platform platform
    3: required string version
    255: base.Base Base
}

struct MatchTaskWithVersionResp {
    1: optional i64 task_id
    255: base.BaseResp BaseResp
}

struct ActiveTerminationReq {
    255: base.Base Base
}

struct ActiveTerminationResp {
    255: base.BaseResp BaseResp
}

struct PassApprovalDetailsReq {
    1: required list<i64> pkg_id_list
    2: required string operator_email
    3: required string approval_reason
    255: base.Base Base
}

struct PassApprovalDetailsResp {
    255: base.BaseResp BaseResp
}

struct DeleteCacheReq {
    1: required string key
    255: base.Base Base
}

struct DeleteCacheResp {
    255: base.BaseResp BaseResp
}

struct ReinspectPackageReq {
    1: required list<i64> pkg_id_list
    2: required i64 before_day
    3: required i64 max_size
    255: base.Base Base
}

struct ReinspectPackageResp {
    1: required list<i64> failed_pkg_list
    255: base.BaseResp BaseResp
}

struct GetIssuePoolReq {
    1: required i64 bits_id
    255: base.Base Base
}

struct GetIssuePoolResp {
    1: required list<IssuePool> issue_pools
    255: base.BaseResp BaseResp
}

struct GetIssuePoolListReq {
    1: required i64 bits_id
    2: required i64 rule_id
    3: optional i64 page_size
    4: optional i64 page_num
    5: optional string search_approver
    6: optional i64 search_status
    7: optional string order
    8: optional string search_content
    9: optional DetectType detect_type
    255: base.Base Base
}

struct GetIssuePoolListResp {
    1: required list<IssuePoolItem> issue_pool_items
    2: required i64 total
    3: required i64 page_size
    4: required i64 page_num
    5: required i64 total_pages
    255: base.BaseResp BaseResp
}

struct GetIssuePoolItemDetailReq {
    1: required i64 issue_pool_id
    255: base.Base Base
}

struct GetIssuePoolItemDetailResp {
    1: required string item_key
    2: required i64 status
    3: required Package package
    4: required string report_url
    5: required Rule rule
    6: required IssueSpot spot
    7: required string content
    8: required string approver
    9: required string approval_time
    10: required string approval_reason
    255: base.BaseResp BaseResp
}

struct InitApprovalDetailsReq {
    1: optional i64 start_pkg_id
    2: optional i64 max_size
    255: base.Base Base
}

struct InitApprovalDetailsResp {
    255: base.BaseResp BaseResp
}

struct DeleteIssuePoolItemsReq {
    1: required list<i64> issue_pool_ids
    2: required string operator
    255: base.Base Base
}

struct DeleteIssuePoolItemsResp {
    255: base.BaseResp BaseResp
}

struct DetectRuleHistoryReq {
    1: required i64 rule_id
    2: required i64 platform
    3: optional i64 app_id
    255: base.Base Base
}

struct DetectRuleHistoryResp {
    1: required list<DetectRuleHistory> historys
    255: base.BaseResp BaseResp
}

struct NotifyApproverReq {
    1: required i64 pkg_id
    2: required string operator_email
    3: required list<string> approvers
    4: required i64 urgent_type
    255: base.Base Base
}

struct NotifyApproverResp {
    255: base.BaseResp BaseResp
}

struct CheckComponentReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct CheckComponentResp {
    1: required list<CheckComponentDetail> detail
    255: base.BaseResp BaseResp
}

struct GetSecurityItemsReq {
    1: required i64 app_id
    2: required string platform
    3: required string version
    255: base.Base Base
}

struct GetSecurityItemsResp {
    1: required list<SecurityItem> data
    2: required string err_msg
    3: required i64 error_code
    4: required i64 total
    255: base.BaseResp BaseResp
}

struct GetNetarchDetailReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetNetarchDetailResp {
    1: required list<NetarchDetail> details
    255: base.BaseResp BaseResp
}

struct GetDetectResultByMd5Req {
    1: required string md5
    255: base.Base Base
}

struct GetDetectResultByMd5Resp {
    1: required string report_url
    2: required ReportStatus report_status
    3: optional list<CheckComponentDetail> component_detail
    4: optional list<SecurityItem> security_detail
    5: optional list<NetarchDetail> netarch_detail
    255: base.BaseResp BaseResp
}

struct GetDetectVersionListReq {
    1: required i64 main_id
    255: required base.Base Base
}

struct GetDetectVersionListResp {
    1: required list<PkgInfo> pkg_list
    255: required base.BaseResp BaseResp
}

struct GetDetectBasicInfoReq {
    1: required i64 pkg_id
    255: required base.Base Base
}

struct GetDetectBasicInfoResp {
    1: required i64 report_status
    2: required string report_detail_url
    3: optional DetectBasicInfo current
    4: optional DetectBasicInfo remain
    5: optional bool is_illegal_channel
    6: optional string channel
    255: required base.BaseResp BaseResp
}

// 敏感词检测结果
struct SensitiveWord {
    1: required string text
    2: required string sensitive_word
    3: required string category
    4: required i32 start // 该敏感词在文本中的开始位置
    5: required i32 end // 该敏感词在文本中的结束位置
}

struct DetectPictureReq {
    1: required list<string> urls
    255: base.Base Base
}

// 图片敏感词
struct PictureSensitiveWords {
	1: required string url
	2: required list<SensitiveWord> words
}

struct DetectPictureResp {
	1: required list<PictureSensitiveWords> words
    255: base.BaseResp BaseResp
}

struct DetectTextReq {
	1: required list<string> text
    255: base.Base Base
}

struct DetectTextResp {
    1: required list<SensitiveWord> words
    255: base.BaseResp BaseResp
}

struct NotifyComponentOwnerReq {
    1: required i64 pkg_id                    // 当前包ID
    2: required string operator_email         // 当前登录的审批人邮箱
    3: required list<i64> detail_ids          // 审批人勾选的问题详情ID
    4: required list<string> component_owners // 审批人勾选的组件负责人邮箱
    255: base.Base Base
}

struct NotifyComponentOwnerResp {
    255: base.BaseResp BaseResp
}

// 检测平台配置字段
struct DetectPlatformConfig {
    1: required i64 bits_id                      // bits空间app ID
    2: optional list<string> product_owner       // 产品负责人
    3: optional list<string> approver            // 问题审批人
    4: optional list<string> compliance_bp       // 安全合规BP
    5: optional CheckRuleConfig check_rule       // 卡口规则
    6: optional MeegoBugConfig meego_bug_config  // meego缺陷单配置-暂时仅头条
    7: required string operator                  // 数据提交人
}

struct CheckRuleConfig {
    1: optional list<RuleLevel> privacy   // 隐私合规规则
    2: optional list<RuleLevel> review    // 预审合规规则
    3: optional list<SecurityLevel> security // 安全漏洞规则
    4: optional list<ByteSibLevel> component // 组件漏洞规则
    5: optional list<ByteSibLevel> netarch   // 网络审计规则
}

enum RuleLevel {
    Unknown = -1
    P0 = 0
    P1 = 1
    P2 = 2
    P3 = 3
}

enum SecurityLevel {
    Unknown = 0
    Low = 1
    Mid = 2
    High = 3
    Critical = 4
}

enum ByteSibLevel {
    Unknown = 0
    Block = 1
    Warn = 2
}

struct MeegoBugConfig {
    1: required bool auto_meego // 是否自动创建meego缺陷
}

// 获取检测平台最新的配置信息
struct GetDetectPlatformConfigReq {
    1: required i64 bits_id    // bits空间app ID
    255: base.Base Base
}

struct GetDetectPlatformConfigResp {
    1: required DetectPlatformConfig config_data  // 检测平台配置信息
    255: base.BaseResp BaseResp
}

// 检测平台配置
struct SetDetectPlatformConfigReq {
    1: required DetectPlatformConfig config_data  // 检测平台配置信息
    255: base.Base Base
}

struct SetDetectPlatformConfigResp {
    255: base.BaseResp BaseResp
}

struct GetDetectResultForWorkflowReq {
    1: required string md5
    255: base.Base Base
}

struct GetDetectResultForWorkflowResp {
    1: required string report_url
    2: required ReportStatus report_status
    3: optional list<CheckComponentDetail> component_detail
    4: optional list<SecurityItem> security_detail
    5: optional list<NetarchDetail> netarch_detail
    255: base.BaseResp BaseResp
}

struct AbandonReportReq {
    1: required list<i64> pkg_ids
    2: required string abandoned_reason
    3: required string abandoned_operator
    255: base.Base Base
}

struct AbandonReportResp {
    255: base.BaseResp BaseResp
}

struct CreateLarkGroupReq {
    1: required i64 pkg_id
    2: required string operator
    255: base.Base Base
}

struct CreateLarkGroupResp {
    1: required string lark_group_open_id
    255: base.BaseResp BaseResp
}

struct GetPreInfoForCreateGroupReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetPreInfoForCreateGroupResp {
    1: optional list<string> blame_owners
    2: optional string lark_group_open_id
    255: base.BaseResp BaseResp
}

struct GetCodeRetraceInfosReq {
    1: required i64 app_id         // 公司内通用的app_id
    2: required i64 bits_app_id    // bits空间的app_id
    3: required Platform platform  // 平台，0->Android, 1->iOS
    4: required string commit_id   // 打包commit_id
    5: required list<string> spot_name_list // 查询时提供的调用点字段，Android->类名，iOS->文件名
    255: base.Base Base
}

struct RetraceInfo {
    1: optional string git_address     // 源码git地址
    2: optional string file_path       // 文件路径
    3: optional string checkout_info   // 一般为commit_id或版本号
    4: optional string sdk_name        // 组件名
    5: optional string sdk_version     // 组件版本
    6: optional string commit_id       // 最新提交
    7: optional string committer       // 最新提交人
    8: optional string codebase_url    // 源码链接
}

struct GetCodeRetraceInfosResp {
    1: required map<string, RetraceInfo> results
    255: base.BaseResp BaseResp
}

struct GetPDPOItemsReq {
    1: required i64 app_id
    2: required string platform
    255: base.Base base
}

/** PDPO工单 */
struct PDPOItem {
    1: required string title        // 工单标题
    2: required string description  // 工单描述
    3: required string report_url   // 工单链接
    4: required string version      // 版本号
    5: required string status       // 工单状态
    6: required string tester       // 测试人
    7: required string create_time  // 创建时间
}

struct GetPDPOItemsResp {
    1: required list<PDPOItem> data
    2: required string err_msg
    3: required i64 error_code
    4: required i64 total
    255: base.BaseResp BaseResp
}

struct PostIssueCommentReq {
    1: required i64 pkg_id
    2: required i64 rule_id
    3: required i64 detect_result_id
    4: required string comment_content
    5: required string commentator
    255: base.Base Base
}

struct PostIssueCommentResp {
    255: base.BaseResp BaseResp
}

struct GetIssueCommentsReq {
    1: required i64 detect_result_id
    255: base.Base Base
}

struct IssueComment {
    1: required string comment_content
    2: required string commentator_email
    3: required string commentator_name
    4: required string comment_time
}

struct GetIssueCommentsResp {
    1: required list<IssueComment> comments
    255: base.BaseResp BaseResp
}

struct GetDataOverviewOfReportsReq {
    1: required i64 bits_app_id
    255: base.Base Base
}

struct GetDataOverviewOfReportsResp {
    1: required double report_approval_rate
    2: required double privacy_approval_rate
    3: required double review_approval_rate
    4: required i64 reject_approval_count
    5: required map<string, i64> processed_level_count
    6: required map<string, i64> unprocessed_level_count
    7: required map<string, map<string, i64>> version_level_count
    255: base.BaseResp BaseResp
}

enum FilterType {
    UploadPhase = 1
    Channel = 2
}

struct GetFilterItemsOfReportsReq {
    1: required i64 bits_app_id
    2: required FilterType filter_type
    255: base.Base Base
}

struct GetFilterItemsOfReportsResp {
    1: required list<string> filter_items
    255: base.BaseResp BaseResp
}

struct GetComponentItemsForWorkflowReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetComponentItemsForWorkflowResp {
    1: required list<CheckComponentDetail> details
    255: base.BaseResp BaseResp
}

struct GetSecurityItemsForWorkflowReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetSecurityItemsForWorkflowResp {
    1: required list<SecurityItem> details
    255: base.BaseResp BaseResp
}

struct GetNetarchItemsForWorkflowReq {
    1: required i64 pkg_id
    255: base.Base Base
}

struct GetNetarchItemsForWorkflowResp {
    1: required list<NetarchDetail> details
    255: base.BaseResp BaseResp
}
