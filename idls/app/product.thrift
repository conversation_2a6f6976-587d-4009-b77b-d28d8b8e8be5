include "../base.thrift"

namespace go bytedance.bits.product_data

struct EmptyRequest {
    255: required base.Base Base;
}

struct product{
  1:  i32 id;
  2:  i32 app_cloud_id;
  3: string logo;
  4:  string chinese_name;
  5:  string creator_name;
  6:  string english_name;
  7:  i32 technology_type;
  8:  string description;
  9:  string region;
  10: i64 create_time;
  11: i64 update_time;
}

struct platform {
  1: i32 id;
  2: i32 product_id;
  3: string name;
  4: string description;
  5: i64 create_time;
  6: i64 update_time;
}

struct application {
  1:  i32 id;
  2:  i32 app_cloud_id;
  3:  i32 product_id;
  4:  i32 app_id;
  5:  string relate_platform;
  7:  i64 create_time;
  8:  i64 update_time;
}

struct GetProductsByChieseNameRequest{
    1: required string product_name;
    255: required base.Base Base;

}

struct GetProductsResponse{
     1:list<product> products;
     255: required base.BaseResp BaseResp;

}

struct GetProductsByPlatformRequest {
   1: required string platform;
   2: optional i32 app_id;
   255: required base.Base Base;
}

struct GetProductsByCloudIDRequest {
   1:required i32 cloud_id;
   255: required base.Base Base;
}


struct  GetProductByProductIDRequest{
   1: required i32 product_id;
   255: required base.Base Base;
}

struct SetProductsRequest {
  1: required list<product> products;
  255: required base.BaseResp BaseRep;
}



struct SetProductsResponse {
  255: required base.BaseResp BaseResp;
}


struct GetApplictionsByProductIDRequest{
  1: required i32 product_id;
  255: required base.Base Base;
}
struct GetApplictionsResponse{
   1: list<application> applications;
   255: required base.BaseResp BaseResp;
}
struct GetApplictionsByCloudIDRequest {
   1:required i32 cloud_id;
   255: required base.Base Base;

}

struct SetApplictionsRequest {
   1:list<application> applications;
   255: required base.Base Base;
}

struct SetApplictionsResponse {
    255: required base.BaseResp BaseResp;
}

struct RegisterProductRequest {
   1: required list<string> platforms;
   2: required product  product;
   255: required base.Base Base;
}

struct RegisterProductResponse {
   255: required base.BaseResp BaseResp;
}

struct GetPlatformsByProductIDRequest  {
   1: required i32 product_id;
   255: required base.Base Base;
}

struct GetPlatformsByProductIDResponse {
   1:required list<platform> platforms;
   255: required base.BaseResp BaseResp;
}

struct QueryProductsResponse {
   1: required list<product> Products
   255: required base.BaseResp BaseResp
}

service productDataCenterService {
	//产品接口
	GetProductsResponse GetProductsByChieseName(1:GetProductsByChieseNameRequest req);
	GetProductsResponse GetProductsByPlatform(1:GetProductsByPlatformRequest req);
	GetProductsResponse GetProductsByCloudID(1:GetProductsByCloudIDRequest req);
	GetProductsResponse GetProductByProductID(1:GetProductByProductIDRequest req);
	SetProductsResponse SetProducts(1:SetProductsRequest req)
	RegisterProductResponse RegisterProduct(1:RegisterProductRequest req)
	GetPlatformsByProductIDResponse GetPlatformsByProductID(1:GetPlatformsByProductIDRequest req)
	GetProductsResponse SearchProductsByChieseName(1:GetProductsByChieseNameRequest req);
    QueryProductsResponse QueryProducts(1: EmptyRequest req)
	//应用接口
	GetApplictionsResponse GetApplictionsByProductID(1:GetApplictionsByProductIDRequest req);
	GetApplictionsResponse GetApplictionsByCloudID(1:GetApplictionsByCloudIDRequest req);
	SetApplictionsResponse SetApplictions(1:SetApplictionsRequest req);
}
