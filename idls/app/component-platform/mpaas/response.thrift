include "repo.thrift"

namespace go bits.component.api

struct TriggerWorkflowByComponentResponse {
    1: i64 id
}

struct SearchReposResponse {
    1: i64 err_no
    2: string err_msg
    3: SearchReposResponseData data
}
struct SearchReposResponseData {
    1: i64 total
    2: list<SearchRepoItem> list
}
struct SearchRepoItem {
    1: i64 id
    2: i64 app_type
    3: i64 app_id
    4: string name
    // 5: GroupInfo group_info
    6: string description
    7: list<RepoTag> tags
    8: Belonging product_belonging
    9: string git_repository
    10: Issue issue
    11: list<RepoOwner> owner
    12: string status
    // 13: operations
    14: CommunityInfo community
    15: bool use_optimus
    16: string version
    // 17: task
    18: repo.LarkGroup lark_info
    19: i64 component_type_id
    20: string android_ext_info
    21: string doc_url
    22: bool has_playground
    23: string pod_owner
    24: list<i64> related_apps
    25: string ios_ext_info
    26: string third_audit_ext_info
    27: i64 gitlab_id
    28: i64 is_biz_pod // 是否是业务组件
    29: i64 team_id // 可能是间接关联的，不是直属业务线的
    30: string team_name // 可能是间接关联的，不是直属业务线的
    31: i64 onesite_space_id
    32: string onesite_space_name
    33: string onesite_space_name_en
    34: bool is_business
    35: bool is_standard
}
struct RepoTag {
    1: string name
    2: i64 id
    3: i64 order
}
struct Belonging {
    1: string name
    2: string text
}
struct Issue {
    1: i64 closed_total
    2: i64 opening_total
    3: string url
}
struct RepoOwner {
    1: string name
    2: string full_name
    3: string lark_id // 虽然这个字段已经废弃了，但还是要保留。因为是返回值，外部可能会解。删除会crash。
}
struct CommunityInfo {
    1: CommunityItemInfo like
    2: CommunityItemInfo collection
    3: CommunityItemInfo comment
    4: CommunityItemInfo view
}
struct CommunityItemInfo {
    1: bool state
    2: i64 count
}

struct ListAllRepoTagsResponse {
    1: list<AppGroups> filters
}

struct AppGroups {
    1: i64 id
    2: string name
    3: i64 order
    4: list<FilterTagItem> app_types
    5: list<FilterTagItem> tags
}

struct FilterTagItem {
    1: string name
    2: i64 id
    3: i64 order
}

struct ListAllRepoTagsResponseData {
    1: list<Tag> filters
}

struct Tag {
    1: i64 id
    2: string name
    3: i64 order
    4: list<TagItem> app_types
    5: list<TagItem> tags
}

struct TagItem {
    1: i64 id
    2: string name
    3: i64 order
}

struct CreateRepoResponse {
    1: i64 id // 还有更多字段
}

struct GetGitInfoResponse {
    1: list<string> branches
    2: string homepage
}

struct GetRepoResponse {
    1: i64 err_no
    2: string err_msg
    3: GetRepoData data
}

struct EditRepoResponse {
    1: i64 err_no
    2: string err_msg
    3: GetRepoData data
}

struct GetRepoData {
    1: i64 id
    2: i64 meta_app_id
    3: bool is_biz_pod // 是否业务库
    4: i64 type
    5: bool is_external // 是否三方库
    6: bool is_business // 是否商业组件
    7: i64 gitlabId
    8: string repoName
    9: string repo
    10: bool isPublic
    11: string branch
    12: string version
    13: string actVersion
    14: i64 buildResult
    15: i64 taskId
    16: string taskUrl
    17: string description
    18: string repohomepage
    19: i64 appId
    20: string operateUser
    21: string changeLog
    22: list<i64> tags
    23: string appCommitId
    24: string docUrl
    25: i64 subappId // 归属子业务线 ID
    26: i64 projectId // 仓库表 ID
    27: bool isUseOptimus // 组件是否接入 Optimus
    28: RepoConfig config
    29: string delete_at
    30: string creatd_at
    31: string updated_at
    32: i64 is_from_bam
    33: string idl_repo_namespace
    34: i64 idl_repo_type // 理论应该有类型这里 1=gerrit 2=gitlab
    35: i64 codebase_id // codebase id
    36: string pod_owner // 组件负责人
    37: i64 cloud_build_template_id // 云构建模板 ID
    38: i64 security_status  // 安全审核状态，0:无需审核 1:审核中 2:审核通过 3:审核不通过
    39: i64 security_ticket_id // 安全审核工单 ID
    40: string security_audit_url // 安全审核工单链接
    41: string tob_yml_path // 商业化组件 yml 相对路径
    42: string db_repo_group_name // db 里面的 repoGroupName 字段
    43: string buildResultText
    44: bool isFollow
    // 还有几个字段，应该用不到
}

struct RepoConfig {
    1: list<repo.RepoMember> members
    2: repo.LarkGroup larkGroup
    3: repo.RepoIssue issues
    4: repo.IOSExtInfo iosExtInfo
    5: repo.AndroidExtInfo androidExtInfo
    6: repo.FlutterExtInfo flutterExtInfo
    7: repo.RubyGemExtInfo rubyGemExtInfo
    8: repo.CommonExtInfo commonExtInfo
    9: list<repo.RepoDirOwner> dir_owner
    10: repo.RepoReleaseConfig release_config
    11: repo.SecurityAuditConfig thirdAuditExtInfo
    12: repo.HarmonyExtInfo harmonyExtInfo
}

struct GetTtpPublicReposResponse {
    1: list<i64> repo_ids
}
