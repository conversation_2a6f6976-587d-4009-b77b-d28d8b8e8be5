include "../../base.thrift"
include "../../infra/feature/feature_enums.thrift"
include "../../infra/feature/feature_common.thrift"
include "../../infra/feature/response.thrift"
include "../../infra/feature/request.thrift"
include "../../infra/feature/feature_page_view.thrift"

namespace go bits.gateway.api

service FeatureApiService {
    // 获取某个 OneSite 空间下的所有需求流转配置，配置里面有信号s
    GetStoryTransitionConfigsResponse GetStoryTransitionConfigs(1: GetStoryTransitionConfigsRequest req) (api.get = "/api/v1/dev/feature/story/transition/configs", api.category = "feature-story")

    base.EmptyResponse SaveStoryTransitionSignal(1: SaveStoryTransitionSignalRequest req) (api.post = "/api/v1/dev/feature/story/transition/save", api.category = "feature-story")

    // 获取 Meego 某个空间的需求的 signal 字段
    GetMeegoSignalFieldsResponse GetMeegoSignalFields(1: GetMeegoSignalFieldsRequest req) (api.get = "/api/v1/dev/feature/meego/signal_fields", api.category = "feature-story")

    base.EmptyResponse CreatePageView(1: feature_page_view.CreatePageViewRequest req) (api.post = "/api/v1/dev/feature/page_views/create", api.category = "feature-story")
    base.EmptyResponse DeletePageView(1: feature_page_view.DeletePageViewRequest req) (api.delete = "/api/v1/dev/feature/page_views/delete", api.category = "feature-story")
    base.EmptyResponse UpdatePageView(1: feature_page_view.UpdatePageViewRequest req) (api.post = "/api/v1/dev/feature/page_views/update", api.category = "feature-story")
    feature_page_view.ListPageViewBySpaceIdResponse ListPageViewBySpaceId(1: feature_page_view.ListPageViewBySpaceIdRequest req) (api.get = "/api/v1/dev/feature/page_views/list", api.category = "feature-story")
    response.ListWorkItemStatusesResponse ListWorkItemStatuses(1: request.ListWorkItemStatusesRequest req) (api.get = "/api/v1/dev/feature/work_items/statuses", api.category = "feature-story")
    response.ListAcrossProjectWorkItemsResponse ListAcrossProjectWorkItems(1: request.ListAcrossProjectWorkItemsRequest req) (api.post = "/api/v1/dev/feature/across_work_items", api.category = "feature-story")
    response.ListPageViewWorkItemsResponse ListPageViewWorkItems(1: request.ListPageViewWorkItemsRequest req) (api.get = "/api/v1/dev/feature/page_view_work_items", api.category = "feature-story")
    response.ListPageViewWorkItemsResponse ListPageViewWorkItems2(1: request.ListPageViewWorkItemsRequest req) (api.post = "/api/v1/dev/feature/page_view_work_items", api.category = "feature-story")
    response.GetWorkItemByIdResponse GetWorkItemById(1: request.GetWorkItemByIdRequest req) (api.get = "/api/v1/dev/feature/get_work_item_by_id", api.category = "feature-story")
    response.GetWorkItemByIdResponse GetWorkItemByIdV2(1: request.GetWorkItemByIdV2Request req) (api.get = "/api/v1/dev/feature/get_work_item_by_id_v2", api.category = "feature-story")
    response.ListWorkItemsResponse ListWorkItems(1: request.ListWorkItemsRequest req) (api.get = "/api/v1/dev/feature/list_work_items", api.category = "feature-story")
    response.ListWorkItemsResponse SearchWorkItems(1: request.SearchWorkItemsRequest req)(api.get = "/api/v1/dev/feature/search_work_items", api.category = "feature-story")
    response.ListWorkItemsResponse SearchWorkItemsV2(1: request.SearchWorkItemsV2Request req)(api.post = "/api/v1/dev/feature/search_work_items_v2", api.category = "feature-story")
    base.EmptyResponse RefreshStoryTransitionName(1: RefreshStoryTransitionNameRequest req) (api.get = "/api/v1/dev/feature/story/transition/refresh", api.category = "feature-story")
    response.GetTransitionStageBySpaceIdResponse GetTransitionStageBySpaceId(1: request.GetTransitionStageBySpaceIdQuery req)(api.get = "/api/v1/dev/feature/transition_stage", api.category = "feature-story")
    response.GetFeatureMeegoProjectDetailResponse GetFeatureMeegoProjectDetail(1: request.GetFeatureMeegoProjectDetailQuery req) (api.get="/api/feature/meego/project/detail", api.category = "feature-story") // 获取meego项目的详情
    response.GetWorkItemBriefResponse GetWorkItemBrief(1: request.GetWorkItemBriefRequest req) (api.post="/api/feature/work_item/briefs", api.category = "feature-story") // 获取工作项的简要信息
    response.GetWorkItemBriefResponse GetWorkItemBriefV2(1: request.GetWorkItemBriefRequest req) (api.post="/api/feature/work_item/briefs_v2", api.category = "feature-story") // 获取工作项的简要信息
}

struct RefreshStoryTransitionNameRequest{
    1: required i64 spaceId
}

struct GetStoryTransitionConfigsRequest {
    1: required i64 spaceId (go.tag="form:\"spaceId\"")
    2: required feature_enums.Platform platform (go.tag="form:\"platform\"")
    255: optional base.Base Base
}

struct GetStoryTransitionConfigsResponse {
    1: list<StoryTransitionConfig> configs
    255: optional base.BaseResp BaseResp
}

struct SaveStoryTransitionSignalRequest {
    1: required i64 spaceId // onesite space id
    2: required feature_enums.Platform platform
    3: list<StoryTransitionConfig> configs
    255: optional base.Base Base
}

struct GetMeegoSignalFieldsRequest {
    1: string projectId (go.tag="form:\"projectId\"")
    255: optional base.Base Base
}

struct GetMeegoSignalFieldsResponse {
    1: list<feature_common.MeegoField> fields
    255: optional base.BaseResp BaseResp
}

struct StoryTransitionConfig {
    1: string platform
    2: string projectId // save 的时候这个字段已经要传递过来，后端根据这个字段进行更新
    3: string name
    4: string nameI18n
    5: required list<TransitionSignal> signals
}

struct TransitionSignal {
    1: feature_enums.StoryTransitionPosition position
    2: list<Signal> signals
    3: optional list<i64> templateIds
    4: optional list<i64> workflowIds
    5: optional string positionName
    6: optional string positionNameI18n
    7: optional map<i64, i64> workflowIdNodeIdMap // 发布单自定义阶段
}

struct Signal {
    1: string fieldName
    2: string fieldAlias
}
