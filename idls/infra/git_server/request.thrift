include "../../base.thrift"
include "gitlab.thrift"
include "code_change.thrift"
include "codebase.thrift"
namespace go bytedance.bits.git_server

struct GetCommitMrsRequest{
    1: required i64 project_id,
    2: required string sha,
    255: optional base.Base Base
}

struct GitlabLoginRequest {
    1: string redirect_url (go.tag="form:\"redirect_url\"")
    255: optional base.Base Base
}

struct GitlabLoginCallbackRequest {
    1: string code (go.tag="form:\"code\"")
    2: string state (go.tag="form:\"state\"")
    255: optional base.Base Base
}

struct GitlabLoginStateRequest {
    1: required string username;
}

struct GetWorkItemRequest {
    1: required i64 project_id,
    2: required i64 iid,
    3: required string username,
    255: optional base.Base Base,
}

struct CreatWorkItemRequest {
    1: required i64 project_id,
    2: required i64 iid,
    3: required string username,
    4: required list<string> combination_ids,
    5: required string type,
    255: optional base.Base Base,
}

struct DeleteWorkItemRequest {
    1: required i64 project_id,
    2: required i64 iid,
    3: required string username,
    4: required list<string> combination_ids,
    255: optional base.Base Base,
}

struct GetWorkItemsStatusRequest {
    1:required string username,
    2:required list<string> combination_ids,
    255: optional base.Base Base,
}

struct ProjectIid {
    1: required i64 project_id,
    2: required i64 iid,
}

struct GetChangeStatusRequest {
    1: required list<ProjectIid> project_iids
    2: required string username,
    255: optional base.Base Base,
}

struct GetRepoStatusRequest {
    1:required list<i64> project_ids,
    2: required string username,
    255: optional base.Base Base,
}

struct QueryRepoLastCommitRequest {
    1: required i64 repo_id,
    2: required string ref,
    3: optional string username,
    255: optional base.Base Base,
}

struct GetCodebaseBranchRequest {
    1: optional i64 codebase_id, // codebase id, 三选一
    2: optional i64 gitlab_id, // gitlab id, 三选一
    3: optional string git_url, // git ssh url, 三选一
    4: required string branch, // 分支名
    255: optional base.Base Base,
}

struct QueryRepoCommitsRequest {
    1: required i64 repo_id,
    2: required i64 page = 0,
    3: required i64 page_size = 100,
    4: required string ref,
    5: optional string username,
    255: optional base.Base Base,
}

struct CreateBranchOptions  {
    1: optional string branch
    2: optional string ref
}

struct CreateBranchRequest {
    1: required i32 project_id, //gitlab id
    2: required CreateBranchOptions options, //repo url
    3: optional string username,
    255: optional base.Base Base,
}

struct GetBranchRequest {
    1: required i32 project_id //gitlab id
    2: required string branch //repo url
    255: optional base.Base Base
}

struct GetBranchByRepoUrlRequest {
    1: required string project_url (go.tag="form:\"projectUrl\"") // 仓库地址
    2: required string branch (go.tag="form:\"branch\"") // 分支名字
    255: optional base.Base Base
}

struct GetFileMetaDataRequest {
    1: required i32 project_id, //project id
    2: required string file_path,
    3: required string ref
    255: optional base.Base Base,
}

struct GetFileContentRequest {
    1: optional i32 project_id, //project id
    2: required string file_path,
    3: required string ref,
    4: optional i32 codebase_id, //codebase id
    255: optional base.Base Base,
}

struct GetContentRequest {
    1: required i64 project_id
    2: required string filename
    3: required string revision
    255: optional base.Base Base
}

struct UpdateFileContentRequest {
    1: required string file_name,
    2: required string file_content,
    3: required string branch,
    4: required i64 project_id,
    5: required string author_name,
    6: required string author_email,
    7: required string commit_message,
    255: optional base.Base Base
}

struct GetFileContentByURLRequest {
	1: required string content_url
	255: optional base.Base Base
}

struct BatchGetFileContentsRequest {
    1: optional string username
    2: required i64 project
    3: required string revision
    4: optional list<string> paths
    5: optional string regex // 支持使用正则或者准确路径去获取文件
	255: optional base.Base Base
}

struct AddProjectHookInfo {
    1: required i64 projectId,
    2: optional string url,
    3: optional string token,
    4: optional bool pushEvents,
    5: optional string pushEventsBranchFilter,
    6: optional bool issuesEvents,
    7: optional bool ConfidentialIssuesEvents,
    8: optional bool mergeRequestsEvents,
    9: optional bool tagPushEvents,
    10: optional bool noteEvents
    11: optional bool jobEvents,
    12: optional bool pipelineEvents,
    13: optional bool wikiPageEvents,
    14: optional bool enableSSLVerification,
    15: optional bool confidentialNoteEvents,
}

struct AddProjectHookRequest {
    1: required AddProjectHookInfo info,
    255: optional base.Base Base,
}

struct EditProjectHookRequest {
    1: required i64 hookId,
    2: required AddProjectHookInfo info,
    255: optional base.Base Base,
}

struct GetProjectHookByURLRequest {
    1: required i64 projectId,
    2: required string url,
    255: optional base.Base Base,
}

struct CreateTagRequest {
    1: required i64 project_id,
    2: required string tag_name,
    3: required string sha
    4: optional string message,
    5: optional string release_description,
    255: optional base.Base Base
}

struct DeleteTagRequest {
    1: required i64 project_id
    2: required string tag_name
    255: optional base.Base Base
}

struct GetLatestCommitIdRequest {
    1: required i32 project_id, //gitlab id
    2: required string sha, //branch or tag
    255: optional base.Base Base,
}

struct DeleteBranchRequest {
    1: required i32 project_id, //gitlab id
    2: required string branch
    255: optional base.Base Base,
}

struct GetProtectedBranchRequest {
    1: required i32 project_id, //gitlab id
    2: required string branch,
    255: optional base.Base Base,
}

struct ProtectBranchRequest {
    1: required i32 project_id //gitlab id
    2: required string branch
    3: optional bool developers_can_push
    4: optional bool developers_can_merge
    255: optional base.Base Base
}

struct ListNamespacesRequest {
    1: i32 page
    2: i32 per_page
    3: optional string search
    255: optional base.Base Base
}


struct GetCommitInfoRequest {
    1: required i32 project_id, //gitlab id
    2: required string sha, //git sha
    255: optional base.Base Base,
}

struct CherryPickCommitRequest {
    1: required i64 project_id,
    2: required string branch,
    3: required string sha,
    255: optional base.Base Base
}

struct RevertCommitRequest {
    1: required i64 project_id
    2: required string sha // commit id
    3: optional string branch
    10: optional string username
    255: optional base.Base Base
}

struct AddProjectMemberRequest {
    1: required i32 project_id, //gitlab id
    2: required string user_name,
    3: optional gitlab.AccessLevel access_level,
    255: optional base.Base Base,
}

struct GetUserByNameRequest {
    1: required string user_name, //user_name
    255: optional base.Base Base,
}

struct CreateProjectOptions {
    1: optional string name
    2: optional string path
    3: optional string default_branch
    4: optional i32 namespace_id
    5: optional string description
    6: optional gitlab.VisibilityValue visibility
}

struct CreateProjectRequest {
    1: required CreateProjectOptions options //gitlab id
    255: optional base.Base Base
}

struct GetProjectByIdRequest {
    1: required i32 project_id //gitlab id
    255: optional base.Base Base
}

struct GetProjectByRepoRequest {
    1: required string repo_url //gitlab id
}

struct GetProjectByRepoInfoRequest {
    1: required string project_namespace // 仓库namespace
    2: required string project_name // 仓库名
    255: optional base.Base Base,
}

enum MRPermisssion {
    create, update, abandon, review
}

struct GetUserHasMRPermissionRequest {
    1: required string repo_url
    2: required string username
    3: required MRPermisssion permission
    255: optional base.Base Base
}

struct GetUserHasMRPermissionV2Request {
    1: required i64 repo_id // codebase repo id
    2: required string username
    3: required MRPermisssion permission
    255: optional base.Base Base
}

struct GetRepoPermissionsRequest {
    1: required i64 repo_id // codebase repo id
    2: required string username
    255: optional base.Base Base
}

struct QueryGitlabTokenRequest {
    1: optional string user
    255: optional base.Base Base
}

struct UpdateMergeRequestRequest {
    1: required i64 project_id
    2: required i64 iid
    3: optional string title
    4: optional string description
    5: optional string target_branch
    6: optional bool remove_source_branch
    7: optional bool squash
    8: optional string username
}

struct GetMRRequest {
    1: required i64 project_id
    2: required i64 mr_iid
    255: optional base.Base Base
}

struct CreateMergeRequestRequest {
    1: required i64 project_id
    2: required string source_branch
    3: required string target_branch
    4: required string title
    5: optional bool remove_source_branch
    6: optional string description
    7: optional list<string> labels
    8: optional list<i64> assignee_ids
    9: optional bool squash
    10: optional bool allow_collaboration
    11: optional string username // 如果这个字段传入，那么则使用用户身份创建 merge request
    255: optional base.Base Base
}

struct GetMRByBranchRequest {
    1: required i32 project_id //gitlab id
    2: required string source_branch
    3: required string target_branch
    4: optional gitlab.MergeRequestState state
    5: optional i64 startTime
    6: optional i64 endTime
    7: optional i64 page
    8: optional i64 pageSize
    255: optional base.Base Base
}

struct ListProjectMergeRequestsRequest {
    1: optional string username
    2: required i64 project_id // gitlab id
    3: required i64 page
    4: required i64 per_page
    5: optional list<i64> iids
    255: optional base.Base Base
}

struct CreateCommitRequest {
    1: required list<gitlab.CommitAction> files
    2: required string commit_message
    3: required string repo_url
    4: required string branch
    255: optional base.Base Base
}

struct CloseMRRequest {
    1: required i32 project_id //project id
    2: required i32 iid
    255: optional base.Base Base
}

struct GetCommitsByBranchRequest {
    1: required i32 project_id (go.tag = "json:\"project_id\" form:\"project_id\"") //gitlab id
    2: required string ref (go.tag = "json:\"ref\" form:\"ref\"") // ref branch
    3: optional i64 page // 分页
    4: optional i64 page_size
    5: optional string path // 指定目录
    6: optional i64 since   // 只包含在此时间点之后的 commit
    255: optional base.Base Base
}

struct GetMRCommitsRequest {
    1: required i32 project_id //project id
    2: required i32 iid
    255: optional base.Base Base
}

struct GetMRCommitsWithTotalRequest {
    1: required i32 project_id //project id
    2: required i32 iid
    3: required i32 page
    4: required i32 pageSize
    255: optional base.Base Base
}

struct InstallCodebaseAppRequest {
    1: required string repo_url
    2: optional i64 project_id
    255: optional base.Base Base
}

struct GetCodebaseBlameInfoRequest {
    1: optional i64 project_id
    2: required string file_path
    3: required i64 line
    4: required string ref // commit-sha/branch/tag
    5: optional string repo_url
    255: optional base.Base Base
}

struct CompareRefRequest {
    1: required i32 project_id //gitlab id
    2: required string from //	the commit SHA or branch name
    3: required string to //	the commit SHA or branch name
    4: required bool straight //comparison method, true for direct comparison between from and to (from..to), false to compare using merge base (from…to)’. Default is false
    255: optional base.Base Base
}

struct CompareRefsRequest {
    1:list<CompareRefs> compare_refs
    255: optional base.Base Base
}

struct CompareRefs{
    1: required i32 project_id //gitlab id
    2: required string from //	the commit SHA or branch name
    3: required string to //	the commit SHA or branch name
    4: required bool straight //comparison method, true for direct comparison between from and to (from..to), false to compare using merge base (from…to)’. Default is false
}

struct GetCodebaseRepoByProjectIdRequest {
    1: required i32 project_id //gitlab id
    2: optional string username
    255: optional base.Base Base
}

struct GetCodebaseRepoByRepoIdRequest {
    1: required i64 repo_id //codebase repo id
    2: optional string username
    255: optional base.Base Base
}

struct GetCodebaseRepoByURLRequest {
    1: required string url
    2: optional string username
    255: optional base.Base Base
}

struct GetRepoDefaultBranchRequest {
    //两个参数任选一个即可
    1: optional i32 project_id (go.tag = "json:\"project_id\" form:\"project_id\"") //gitlab id
    2: optional string repo_url (go.tag = "json:\"repo_url\" form:\"repo_url\"") //仓库地址
    3: optional i64 meta_app_id (go.tag = "json:\"meta_app_id\" form:\"meta_app_id\"") //基座app id
    4: optional bool no_from_cache (go.tag = "json:\"no_from_cache\" form:\"no_from_cache\"") //是否从缓存中获取，默认从缓存中获取
    255: optional base.Base Base
}

struct GetAllBranchesByRepoRequest {
    1: required string repo //repo url
    255: optional base.Base Base
}

struct GetAllBranchesByProjectIdRequest {
    1: required i32 project_id //gitlab id
    255: optional base.Base Base
}

struct GetCommitRefsRequest {
    1: required i32 project_id //gitlab id
    2: required string sha //	The commit hash
    3: optional i64 page
    4: optional i64 page_size
    255: optional base.Base Base
}

struct GetMergeBaseRequest {
    1: required i32 project_id //gitlab id
    2: required string from //cid or brancch
    3: required string to //cid or branch
    255: optional base.Base Base
}

struct GetMergeBaseForGitlabRequest {
    1: required i64 project_id // gitlab project id
    2: required string from // commit or brancch
    3: required string to // commit or branch
    255: optional base.Base Base
}

struct GetChangeByGitlabIdRequest {
    1: required i32 project_id //gitlab id
    2: required i32 iid
    3: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct GetChangeByRepoIdRequest {
    1: required i64 repo_id //repo id
    2: required i64 change_id
    3: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct GetChangeAndRepoByRepoIdRequest {
    1: required i64 repo_id //repo id
    2: required i64 change_id
    3: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct GetChangesCheckSuitesRequest {
    1: required i64 RepoID
    2: required i64 ChangeID
    3: optional string username
    255: optional base.Base Base
}

enum ChangeState {
    open, submitted, abandoned, locked
}

enum ChangeSearchOrderBy {
    id, created_at, updated_at
}

enum ChangeSearchSort {
    desc, asc
}

struct SearchChangeOptions {
    1: optional i64 source_repo_id //repo id
    2: optional ChangeState status
    3: optional string source_branch
    4: optional string target_branch
    5: optional string pending_review_username
    6: optional i64 id_less_than
    7: optional ChangeSearchOrderBy order_by
    8: optional ChangeSearchSort sort
    9: optional i64 per_page
    10: optional i64 created_since
    11: optional i64 updated_since
    12: optional string ApprovedReviewerName
    13: optional string DisapprovedReviewerName
}

struct SearchChangeRequest {
    1: optional string username //用户名，codebase鉴权会用到
    2: required SearchChangeOptions options //搜索条件
    255: optional base.Base Base
}

struct CreateChangeOptions {
	1: optional string title
	2: optional string description
	3: optional string source_branch
	4: optional string target_branch
	5: optional i64 target_repo_id //codebase repo id
	6: optional list<ReviewRule> review_rules
	7: optional bool remove_source_branch
	8: optional list<string> labels
	9: optional list<codebase.Link> links
	10: optional bool draft
}

struct CreateChangeRequest {
    1: required i64 repo_id //codebase repo id
    2: required string username //用户名，codebase鉴权会用到
    3: required CreateChangeOptions change_options //project id
    255: optional base.Base Base
}

struct ReviewRule  {
	1: optional string name
	2: optional i32 approvals_required // 辅 reviewer approvals_required =0
	3: optional list<string> user_names //用户名
}

struct UpdateChangeOptions {
    1: optional string title
    2: optional string description
    3: optional string target_branch
    4: optional list<ReviewRule> review_rules
    5: optional list<codebase.Link> links
    6: optional bool draft
}


struct UpdateChangeRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: required string username //用户名，codebase鉴权会用到
    4: required UpdateChangeOptions change_options //project id
    255: optional base.Base Base
}

struct SubmitChangeRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: optional string username //用户名，codebase鉴权会用到
    4: required bool should_remove_source_branch
    5: optional string merge_commit_message
    255: optional base.Base Base
}

struct ForceSubmitChangeRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: optional string username //用户名，codebase鉴权会用到
    4: required bool should_remove_source_branch
    5: optional string latest_sha //最新的commit id,合入不成功的时候用于skip
    6: optional string merge_commit_message
    255: optional base.Base Base
}

struct AbandonChangeRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: required string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct RestoreChangeRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: required string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

const i64 skip_type_checkrun = 0
const i64 skip_type_cr = 1
const i64 skip_type_checksuite = 2
const string skip_reason_etc = "etc"
struct SkipDetail {
    1: required i64 skip_type // 1 cr 0 check run
    2: optional i64 check_run_id
    3: optional string skip_reason
    4: optional string custom_detail
    5: optional i64 check_suite_id
    6: optional string check_run_name
}

struct SkipChangeCheckRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: required string sha
    4: optional list<SkipDetail> skip_details
    5: optional string username
    255: optional base.Base Base
}

struct CodebaseReviewCommit {
    1: required i64 repo_id
    2: required i64 change_id
    3: required string sha
}

enum ReviewEventType {
    approve, disapprove, revoke, comment
}

struct CodebaseReviewStatus {
    1: required list<CodebaseReviewCommit> commits
    2: required ReviewEventType event
}

struct UpdateReviewStatusRequest {
    1: required CodebaseReviewStatus review_status
    2: required string username
    255: optional base.Base Base
}

struct GetChangeSubmittableRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: required string sha
    4: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct GetCommentsStatsRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct CreateMRCommentUseUserIdentityRequest {
    1: required i64 project_id
    2: required i64 iid
    3: required string username
    4: required string note
    5: required string access_token
    255: optional base.Base Base
}

struct CreateMergeRequestNoteRequest {
    1: required i64 project // gitlab project id
    2: required i64 iid // gitlab merge-request iid
    3: required string body
    4: optional string username
    255: optional base.Base Base
}

struct GetMergeRequestNotesRequest{
    1: required i64 project // gitlab project id
    2: required i64 iid // gitlab merge-request iid
    3: optional string orderBy
    4: optional string sort
    5: optional gitlab.ListOptions pagination // 如果有这个参数,那么就分页,如果没有这个参数,那么全部返回
    6: optional string username
    255: optional base.Base Base
}

struct ListCommentsForGitlabRequest {
    1: optional string username
    2: required i64 project_id // gitlab project id
    3: required i64 mr_iid // gitlab merge-request iid
    4: optional i64 thread_id
    5: optional bool draft // 是否草稿

    255: optional base.Base Base
}

struct CreateCommentForGitlabRequest {
    1: optional string username
    2: required i64 project_id // gitlab project id
    3: required i64 mr_iid // gitlab merge-request iid
    4: string content // 评论内容
    5: optional i64 thread_id
    6: optional i64 in_reply_to // 回复谁
    7: bool draft // 是否设置为草稿
    8: i64 patchset_num
    9: string side
    10: optional i64 left_patchset_num
    11: optional i64 right_patchset_num
    12: optional string left_sha
    13: optional string right_sha
    14: string path
    15: i64 start_line
    16: i64 start_column
    17: i64 end_line
    18: i64 end_column
    19: string origin_path
    20: i64 origin_start_line
    21: i64 origin_start_column
    22: i64 origin_end_line
    23: i64 origin_end_column
    255: optional base.Base Base
}

struct GetDiffsRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    3: optional string sha  //不传默认取mr最新的sha
    4: optional string base_sha //base 如果为空则取 merge base
    5: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct GetDiffFilesRequest {
    1: required i64 project_id
    2: required i64 iid
    3: optional string sha
    4: optional string base_sha
    5: optional string username
    255: optional base.Base Base
}

struct GetPatchSetsRequest {
    1: required i64 repo_id //codebase repo id
    2: required i64 change_id //codebase change id
    5: optional string username //用户名，codebase鉴权会用到
    255: optional base.Base Base
}

struct GitCallBackRequest {
    1: required string event_type //事件类型
    2: required string body //body
    255: optional base.Base Base
}

struct QueryRepoInfoByAppIdRequest {
    1: required i64 meta_app_id // 基座 AppID
    255: optional base.Base Base
}

struct QueryAppIdsByRepoInfoRequest {
    1: optional i64 project_id  //gitlab pid
    2: optional i64 repo_id  //codebase repo id
    255: optional base.Base Base
}

struct GetAppIdByProjectIdRequest {
    1: required i64 project_id // gitlab project id
    2: optional string username
    255: optional base.Base Base
}

struct UnprotectBranchesRequest {
    1: required i32 project_id //gitlab id
    2: required string branch //repo url
    255: optional base.Base Base
}

struct ProtectBranchesRequest {
    1: required i32 project_id //gitlab id
    2: required string branch //repo url
    3: optional gitlab.AccessLevelValue push_access_level //push权限
    4: optional gitlab.AccessLevelValue merge_access_level //merge权限
    5: i64 repo_id //codebase repo id
    6: optional bool check_required
    7: optional bool review_required
    8: optional bool code_owner_review_required
    9: optional string username
    255: optional base.Base Base
}

struct GetProtectedBranchSettingsRequest {
    1: required i64 repo_id //codebase repo id
    2: required string branch // branch
    255: optional base.Base Base
}

struct MListProtectedBranchesRequest {
    1: required i64 repo_id // codebase repo id
    2: required list<string> branches // branches
    255: optional base.Base Base
}

struct MListProtectedBranchesRequestV2 {
    1: required i64 repo_id // codebase repo id
    2: required list<string> branches // branches
    255: optional base.Base Base
}

struct GetTagRequest {
    1: required i64 project_id // gitlab id
    2: required string tag // tag的名字
    255: optional base.Base Base
}

struct TriggerMergeRequestDiffStatRequest {
    1: required i64 project_id
    2: required i64 mr_iid
    255: optional base.Base Base
}

struct QueryMergeRequestDiffStatRequest {
    1: required i64 project_id
    2: required i64 mr_iid
    255: optional base.Base Base
}

struct DeleteMergeRequestDiffStatRequest {
    1: required i64 project_id
    2: required i64 mr_iid
    255: optional base.Base Base
}

struct AcceptMergeRequestRequest {
    1: required i64 project_id
    2: required i64 mr_iid
    3: optional string merge_commit_message
    4: optional string squash_commit_message
    5: optional bool squash
    6: optional bool should_remove_source_branch
    7: optional bool merge_when_pipeline_succeeds
    8: optional string sha
    9: optional string merge_method // merge_commit, merge_commit_with_semi_linear_history, rebase_merge, squash_merge
    10: optional string username
    255: optional base.Base Base
}

struct GetCommitDiffRequest {
    1: required i64 project_id
    2: required string sha
    3: optional gitlab.ListOptions pagination // 如果有这个参数,那么就分页,如果没有这个参数,那么全部返回
    255: optional base.Base Base
}

struct ListTagsRequest {
    1: required i64 project_id
    2: optional i64 page // default first page, twenty per page
    3: optional i64 per_page
    4: optional string order_by
    5: optional string search
    6: optional string sort
    255: optional base.Base Base
}

enum  CodeChangeCreationMode {
     trackExisted = 1
}

struct CodeChangeCreateConfig {
    1: required string title
    2: i64 bitsRepoId
    3: required string targetBranch
    4: required string sourceBranch
    5: required string username
    6: required code_change.CodeChangePlatformType platformType
    7: optional string platformChangeConfig
    8: optional string description
    9: optional string repoPath
    10: optional list<codebase.Link> links
    11: optional bool draft
}

struct ExtraCreateConfig {
    1: optional GitlabMrExtraCreateConfig gitlabMrExtraCreateConfig
}

struct GitlabMrExtraCreateConfig {
    1: required i64 project_id
    2: optional i64 iid
    5: optional bool remove_source_branch
    7: optional list<string> labels
    8: optional list<i64> assignee_ids
    9: optional bool squash
    10: optional bool allow_collaboration
}

enum CreateCodeChangeApiType {
    gitlab = 0
    codebase = 1
}

struct CreateCodeChangeRequest {
    1: required CodeChangeCreateConfig codeChangeCreateConfig
    2: optional ExtraCreateConfig extraCreateConfig
    3: optional CodeChangeCreationMode creationMode
    4: optional CreateCodeChangeApiType apiType
    255: optional base.Base Base
}

struct GetCodeChangeRequest {
    1: required i64 id
    255: optional base.Base Base
}

struct BatchGetCodeChangesRequest {
    1: required list<i64> ids
    255: optional base.Base Base
}

struct GetCodeChangeDetailRequest {
    1: required i64 id
    255: optional base.Base Base
}

struct CodeChangeUpdateOptions {
    1: optional string title
    2: optional string description
    3: optional string targetBranch
    4: optional string sourceBranch
    5: optional code_change.CodeChangeStatus status
    6: optional string platformChangeConfig
    7: optional bool removeSourceBranch
    8: optional bool squash
    9: optional bool draft
}

struct UpdateCodeChangeRequest {
    1: required i64 id
    2: required CodeChangeUpdateOptions codeChange
    255: optional base.Base Base
}

struct CloseCodeChangeRequest {
    1: required i64 codeChangeId
    255: optional base.Base Base
}

struct CodeChangeMergeOptions {
    1: optional GitlabMrMergeOptions gitlabMrMergeOptions
}

struct GitlabMrMergeOptions {
    1: optional string merge_commit_message
    2: optional string squash_commit_message
    3: optional bool squash
    4: optional bool should_remove_source_branch
    5: optional bool merge_when_pipeline_succeeds
    6: optional string sha
    7: optional string merge_method // merge_commit, merge_commit_with_semi_linear_history, rebase_merge, squash_merge
    8: optional string username
}

struct MergeCodeChangeRequest {
    1: required i64 codeChangeId
    2: optional CodeChangeMergeOptions mergeOptions
    255: optional base.Base Base
}

struct GetOpenedCodeChangeGitlabMrByProjectIDAndSourceBranchRequest {
    1: required i64 projectID
    2: required string sourceBranch
    255: optional base.Base Base
}

struct GetCodeChangeGitlabMrRequest {
    1: required i64 projectID
    2: required i64 iid
    3: i64 codebaseRepoId,
    4: i64 codebaseChangeId,
    255: optional base.Base Base
}

struct BatchGetCodeChangeGitlabMrByCodeChangeIdsRequest {
    1: required list<i64> cids
    255: optional base.Base Base
}

struct BatchGetCodeChangeGitlabMrByCodebaseChangeIdsRequest {
    1: required list<i64> codebaseChangeIDs
    255: optional base.Base Base
}

struct GetRangeDiffsRequest{
    1: required i64 project_id
    2: required string from_range
    3: required string to_range
    4: optional string username
    255: optional base.Base Base
}

struct GetRepoDiffFileForGitlabRequest {
    1: optional string username
    2: required i64 project_id
    3: required string range
    4: required string filepath
    5: optional string format = 'line'
    255: optional base.Base Base
}

struct GetRepoDiffByCommitRequest {
    1: optional string username
    2: required i64 repo_id
    3: required string source_commit
    4: required string target_commit
    255: optional base.Base Base
}

struct GetMergeRequestVersionsRequest{
    1: required i64 project_id
    2: required i64 iid
    3: optional string username
    255: optional base.Base Base
}

struct GetCodebaseCiCheckStatusRequest {
    1: required i64 repo_id,
    2: required list<i64> change_ids,
    3: optional string username,
    255: optional base.Base Base
}

struct LockBranchRequest {
    1:list<gitlab.ProjectBranch> projects
    2: required string business_type
    3: required i64 business_id
    4: required string lock_type
    255: optional base.Base Base
}

struct UnLockBranchRequest {
    1:list<gitlab.ProjectBranch> projects
    2: required string business_type
    3: required i64 business_id
    4: required string lock_type
    255: optional base.Base Base
}


struct GetBranchLockRequest {
    1:list<gitlab.ProjectBranch> projects
    255: optional base.Base Base
}

struct GetConflictFilesByCodebaseChangeRequest {
    1: string repo_path,
    2: i64 codebase_repo_id,
    3: i64 codebase_change_id,
    4: bool rebase,
    255: optional base.Base Base
}

struct GetConflictFilesByGitlabIidRequest {
    1: string repo_path,
    2: i64 gitlab_project_id,
    3: i64 gitlab_iid,
    4: bool rebase,
    255: optional base.Base Base
}

struct GetConflictFilesByGerritChangeRequest {
    1: string repo_name,
    2: i64 gerrit_change_id,
    3: bool rebase,
    255: optional base.Base Base
}

struct CompareRefsCountRequest{
    1: optional list<codebase.CompareItem> compare_items
    2: optional string username
    255: optional base.Base Base
}

struct GetProjectMemberRequest{
    1: required string project_id_or_path,
    2: required i64 user_id,
}

struct GetProjectPrivilegedUserRequest {
    1: required string gitRepoName;
    255: optional base.Base Base,
}

struct GetCodebaseChangeReviewRequest {
    1: required i64 repoId
    2: required i64 changeId
    3: optional string username
    100: optional bool try_use_cache // 是否尝试从缓存中获取数据,因为开发任务的 MR,做了缓存一致性
    255: optional base.Base Base
}

struct MergeBranchRequest {
    1: required string branch
    2: required string upstream
    3: required string username
    4: optional string repo_path
    5: optional i64 codebase_repo_id
    6: optional i64 gitlab_project_id
    7: optional string message
    8: optional bool use_admin_role
    255: optional base.Base Base
}

struct RebaseBranchRequest {
    1: required i64 codebase_repo_id
    2: required i64 codebase_change_id
    3: required string username
    255: optional base.Base Base
}

struct RebaseBranchesRequest{
    1: i64 codebase_repo_id
    2: string username
    3: string branch
    4: string upstream
}

struct SearchCodebaseBranchRequest {
    1: required string repo_id_or_repo_path,
    2: optional string match_query,
    3: optional i64 page,
    4: optional i64 per_page,
}

struct QueryUserLeavesBatchRequest{
    1: required list<string> usernames
    255: optional base.Base Base
}

struct GetBranchConflictFilesRequest {
    1: required string branch
    2: required string baseBranch
    3: optional string repo_path,
    4: optional i64 codebase_repo_id,
    5: optional i64 gitlab_project_id,
    6: optional bool rebase,
    255: optional base.Base Base
}

struct CreateRevertBranchRequest {
    1: required i64 pid,  // 仓库 ID
    2: required string sha,  // 要 revert 的 commit
    3: required string ref, // 依赖分支
    255: optional base.Base Base
}

struct GetPipelineByRefRequest {
    1: required i64 projectID;
    2: required string ref;
}

struct GetRepoFeaturesRequest {
    1: optional i64 codebase_repo_id ,
    2: optional i64 project_id,
}

struct GetPipelineJobsRequest {
    1: required i64 projectID;
    2: required i64 pipelineID;
}

struct RetryPipelineJobRequest {
    1: required i64 jobID;
    2: required i64 projectID;
}

struct RetryCodebaseCIJob {
    1: required i64 job_id,
    2: required i64 project_id,
}

struct GetCodebaseChangeRequestedReviewersRequest {
    1: required i64 repo_id,
    2: required i64 change_id,
}

struct GetUserGitlabAccessTokenRequest {
    1: required string username
    255: optional base.Base Base
}

enum ProtectLevel {
    NoPermissions = 0
	GuestPermissions = 10
	ReporterPermissions = 20
	DeveloperPermissions = 30
	MaintainerPermissions = 40
	OwnerPermissions = 50
}

struct SearchReposRequest {
    1: required string username // 必传
    2: required string query // e.g. devinfra/hagrid 按照这个格式
    3: required i64 page
    4: required i64 page_size
    255: optional base.Base Base
}

struct GetRepoMigrationRequest {
    1: required i64 repo_id (go.tag="form:\"repoId\"")// codebase repo id
    2: optional i64 devops_space_id // 不需要填写
}

struct UpdateRepoMigrationRequest {
    1: required i64 repo_id // codebase repo id
    2: required bool migrated // 是否要迁移
}

struct ListAllTagsByProjectIdRequest {
    1: required i64 project_id
    10: optional string username
    255: optional base.Base Base
}

struct ListAllTagsByRepoNameRequest {
    1: required string repo_name // e.g. devinfra/hagrid
    10: optional string username
    255: optional base.Base Base
}

struct ListAllTagsByRepoIdRequest {
    1: required i64 repo_id
    10: optional string username
    255: optional base.Base Base
}
