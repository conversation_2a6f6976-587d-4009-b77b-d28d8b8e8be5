syntax = "proto3";

package byted.bc.shared;

import "validate/validate.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/sharedpb";

message PageInfo {
  // 总数
  int64 total = 1;
  // 总页码
  int64 total_page = 2;
  // 当前页
  int64 page_num = 3;
  // 当前页个数
  int64 page_size = 4;
}

enum ProjectType {
  PROJECT_TYPE_UNSPECIFIED = 0;

  // TCE
  PROJECT_TYPE_TCE = 1;

  // ByteFaaS
  PROJECT_TYPE_FAAS = 2;

  // Cronjob
  PROJECT_TYPE_CRONJOB = 3;

  // 新版 Web
  PROJECT_TYPE_WEB = 4;

  // 跨端
  PROJECT_TYPE_HYBRID = 5;

  // 组件/模块
  PROJECT_TYPE_LIBRARY = 6;

  // 新版 NodeJS
  PROJECT_TYPE_NODEJS = 7;

  // 新版 Monorepo
  PROJECT_TYPE_MONOREPO = 8;

  // sidecar
  PROJECT_TYPE_SIDECAR = 9;

  // webapp
  PROJECT_TYPE_WEB_APP = 10;

  // 旧版web
  PROJECT_TYPE_OLD_WEB = 11;

  // 自定义项目类型
  PROJECT_TYPE_CUSTOM = 100;
}

enum ControlPlane {
  // UNSPECIFIED 在阶段模板配置里，可以额外用于未指定控制面（自由流水线）
  CONTROL_PLANE_UNSPECIFIED = 0;
  CONTROL_PLANE_CN = 1;
  CONTROL_PLANE_I18N = 2;
  CONTROL_PLANE_TTP = 3;
}

enum SCMPubBase {
  SCM_PUB_BASE_UNSPECIFIED = 0;
  // 基于版本发布
  SCM_PUB_BASE_VERSION = 1;
  // 基于分支发布
  SCM_PUB_BASE_BRANCH = 2;
}

message ProjectIdentity {
  string project_unique_id = 1;

  shared.ProjectType project_type = 2;

  string project_name = 3;
}

// 工作项平台
enum WorkItemPlatform {
  // 未知
  PLATFORM_UNSPECIFIED = 0;
  // Meego工作项
  PLATFORM_MEEGO = 1;
  // Jira工作项
  PLATFORM_JIRA = 2;
}

enum SolutionUsageScene {
  // 未指定
  SOLUTION_USAGE_SCENE_UNSPECIFIED = 0;
  // continuous_delivery: 用于需求研发交付场景
  SOLUTION_USAGE_SCENE_CD = 1;
  // release_train: 用于火车研发交付场景
  SOLUTION_USAGE_SCENE_RT = 2;
  // project_ticket: 用于项目工单交付场景
  SOLUTION_USAGE_SCENE_PT = 3;
  // frontend: 用于前端交付场景
  SOLUTION_USAGE_SCENE_FE = 4;
  // free_pipeline: 用于自由流水线交付场景
  SOLUTION_USAGE_SCENE_FP = 5;
}

enum NodeUsageScene {
  NODE_USAGE_SCENE_UNSPECIFIED = 0;
  // 工单的BOE联调场景
  NODE_USAGE_SCENE_BOE = 1;
  // 工单的PPE联调场景
  NODE_USAGE_SCENE_PPE = 2;
  // 工单的指定SCM部署场景
  NODE_USAGE_SCENE_SPECIFY_SCM_DEPLOY = 3;
  // 工单的上线场景
  NODE_USAGE_SCENE_PROD = 4;
  // 工单的部署升级场景
  NODE_USAGE_SCENE_UPGRADE = 5;
  // 工单的合码场景
  NODE_USAGE_SCENE_MERGE = 6;
  // 工单的回归测试场景
  NODE_USAGE_SCENE_EXECUTION_REGRESSION_TEST = 7;
  // 工单的发布场景
  NODE_USAGE_SCENE_EXECUTION_DEPLOY = 8;

  // 火车测试场景（包含开发自测 + Feature测试 + 测试）
  NODE_USAGE_SCENE_TRAIN_TEST = 9;
  // 火车回归测试场景
  NODE_USAGE_SCENE_TRAIN_REGRESSION = 10;
  // 火车集成测试场景
  NODE_USAGE_SCENE_TRAIN_INTEGRATE = 11;
  // 火车发布场景
  NODE_USAGE_SCENE_TRAIN_DEPLOY = 12;

  // 需求开发场景
  NODE_USAGE_SCENE_FEATURE_DEVELOP = 13;
  // 需求测试场景
  NODE_USAGE_SCENE_FEATURE_TEST = 14;
  // 需求发布场景
  NODE_USAGE_SCENE_FEATURE_DEPLOY = 15;

  // PPE联调多地区
  NODE_USAGE_SCENE_PPE_MULTI_IDC = 16;
  // 多地区上线
  NODE_USAGE_SCENE_PROD_MULTI_IDC = 17;
  // 多地区部署升级
  NODE_USAGE_SCENE_UPGRADE_MULTI_IDC = 18;
  // SCM版本发布的BOE联调阶段
  NODE_USAGE_SCENE_SCM_BOE = 19;
  // SCM版本发布的PPE联调阶段
  NODE_USAGE_SCENE_SCM_PPE = 20;

  // 前端开发场景
  NODE_USAGE_SCENE_MARCH_DEVELOP = 21;
  // 前端测试场景
  NODE_USAGE_SCENE_MARCH_TEST = 22;
  // 前端发布场景
  NODE_USAGE_SCENE_MARCH_DEPLOY = 23;
}

message AuthInfo {
  // jwt 类型，包括个人jwt 和 服务账号 jwt
  JwtType jwt_type = 1;

  // jwt
  string jwt = 2 [(validate.rules).string = { min_len: 1 }];

  // username
  string username = 3;
}

enum JwtType {
  // 服务账号jwt
  JWT_Type_Service_Account = 0;

  // 用户jwt
  JWT_Type_Person_Account = 1;
}

enum TemplateUsageScene {
  // 未指定
  TEMPLATE_USAGE_SCENE_UNSPECIFIED = 0;
  // feature_delivery - 需求
  TEMPLATE_USAGE_SCENE_FEATURE_DELIVERY = 1;
  // release_train - 火车
  TEMPLATE_USAGE_SCENE_RELEASE_TRAIN = 2;
  // execution - 工单
  TEMPLATE_USAGE_SCENE_EXECUTION = 3;
  // frontend - 前端
  TEMPLATE_USAGE_SCENE_FRONTEND = 4;
}

enum TCEEnv {
  TCE_ENV_UNSPECIFIED = 0;
  TCE_ENV_PROD = 1;
  TCE_ENV_BOE = 2;
  TCE_ENV_PPE = 3;
}

message HaloError {
  // 错误码
  int64 code = 1;
  // 错误码的释义
  string err_message = 2;
  // 错误码的释义I18N
  string err_message_i18n = 3;
  // 前端引导信息
  string tip_message = 4;
  // 前端引导信息I18N
  string tip_message_i18n = 5;
}