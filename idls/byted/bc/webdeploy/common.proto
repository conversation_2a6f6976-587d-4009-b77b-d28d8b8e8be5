syntax = "proto3";

package byted.bc.webdeploy;

import "idls/byted/bc/shared/goofy.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/webdeploypb";

message RegionItem {
  shared.GoofyDeployRegion region = 1;
  string label = 2;
  bool is_online = 3;
}

message RegionOptionItem {
  shared.GoofyDeployRegion region = 1;
  string label = 2;
  uint64 create_time = 3;
  uint64 deploy_unit_id = 4;
}