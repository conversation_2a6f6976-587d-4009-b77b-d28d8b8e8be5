syntax = "proto3";

package byted.bc.webdeploy;

import "idls/byted/api/api.proto";
import "idls/byted/bc/webdeploy/webdeploy_service.proto";
import "idls/byted/devinfra/infra/rpc/annotations.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/webdeploypb";

service WebDeployAPI {
  option (devinfra.infra.rpc.service) = {
    psm: "bc.webdeploy.api",
    framework: FRAMEWORK_HERTZ,
    gw_domain: "bc_webdeploy_api",
  };

  /* Project-WebDeploy related 项目 Web 部署相关接口 */

  // 获取项目全部控制面部署信息
  rpc GetWebDeployByProjectID(GetWebDeployByProjectIDReq) returns (GetWebDeployByProjectIDResp) {
    option (api.get) = "/api/v1/projects/:projectId/web_deploy";
  }

  // 创建控制面下项目和部署信息的映射
  rpc CreateProjectWebDeploy(CreateProjectWebDeployReq) returns (CreateProjectWebDeployResp) {
    option (api.post) = "/api/v1/projects/:projectId/web_deploy";
  }

  // 修改控制面下项目和部署信息的映射
  rpc UpdateProjectWebDeploy(UpdateProjectWebDeployReq) returns (UpdateProjectWebDeployResp) {
    option (api.put) = "/api/v1/projects/:projectId/web_deploy";
  }

  // 删除控制面下项目和部署信息的映射
  rpc DeleteProjectWebDeploy(DeleteProjectWebDeployReq) returns (DeleteProjectWebDeployResp) {
    option (api.delete) = "/api/v1/projects/:projectId/web_deploy";
  }

  // 把项目元信息中的 Goofy Deploy 导入到项目和部署信息映射表
  rpc ImportProjectWebDeployFromMeta(ImportProjectWebDeployFromMetaReq) returns (ImportProjectWebDeployFromMetaResp) {
    option (api.post) = "/api/v1/projects/web_deploy/import";
  }
  /* End of Project-WebDeploy */

  /* Goofy-api related Goofy Deploy open-api */

  // 获取项目部署区域
  rpc GetDeployUnitList(GetDeployUnitListReq) returns (GetDeployUnitListResp) {
    option (api.get) = "/api/v1/projects/:projectId/deploy_units";
  }
  // 获取部署区域环境列表
  rpc GetChannelList(GetChannelListReq) returns (GetChannelListResp) {
    option (api.get) = "/api/v1/projects/deploy_units/:deployUnitId/channels";
  }
  // 获取环境详情
  rpc GetChannelInfo(GetChannelInfoReq) returns (GetChannelInfoResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId";
  }
  // 获取项目详情
  rpc GetAppInfo(GetAppInfoReq) returns (GetAppInfoResp) {
    option (api.get) = "/api/v1/projects/:projectId/web_deploy/detail";
  }
  // 修改环境部署能力
  rpc UpdateChannelUniversalConfig(UpdateChannelUniversalConfigReq) returns (UpdateChannelUniversalConfigResp) {
    option (api.put) = "/api/v1/projects/channels/:channelId/universal_config";
  }
  // 获取区域域名列表
  rpc GetDeployUnitHostPrefixList(GetDeployUnitHostPrefixListReq) returns (GetDeployUnitHostPrefixListResp) {
    option (api.get) = "/api/v1/projects/deploy_units/:deployUnitId/host_prefixes";
  }
  // 修改区域信息
  rpc UpdateDeployUnit(UpdateDeployUnitReq) returns (UpdateDeployUnitResp) {
    option (api.put) = "/api/v1/projects/deploy_units/:deployUnitId";
  }
  // 创建区域
  rpc CreateDeployUnit(CreateDeployUnitReq) returns (CreateDeployUnitResp) {
    option (api.post) = "/api/v1/projects/:projectId/deploy_units";
  }
  // 环境路由配置
  rpc GetChannelRouteConfig(GetChannelRouteConfigReq) returns (GetChannelRouteConfigResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId/route_configs";
  }
  //  新增环境路由配置
  rpc CreateChannelRouteConfig(CreateChannelRouteConfigReq) returns (CreateChannelRouteConfigResp) {
    option (api.post) = "/api/v1/projects/channels/:channelId/route_configs";
  }
  //  修改环境路由配置
  rpc UpdateChannelRouteConfig(UpdateChannelRouteConfigReq) returns (UpdateChannelRouteConfigResp) {
    option (api.put) = "/api/v1/projects/channels/route_configs/:routeId";
  }
  //  删除环境路由配置
  rpc DeleteChannelRouteConfig(DeleteChannelRouteConfigReq) returns (DeleteChannelRouteConfigResp) {
    option (api.delete) = "/api/v1/projects/channels/route_configs/:routeId";
  }
  // 路由项目文件路径列表
  rpc GetChannelRouteConfigFileTree(GetChannelRouteConfigFileTreeReq) returns (GetChannelRouteConfigFileTreeResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId/route_configs/file_tree";
  }
  // 环境路由配置是否变化
  rpc GetChannelRouteConfigDiff(GetChannelRouteConfigDiffReq) returns (GetChannelRouteConfigDiffResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId/route_configs/route_diff";
  }
  // 获取 Channel 域名前缀 Diff 信息（和部署单元配置对比）
  rpc GetChannelConfigHostnamePrefixDiff(GetChannelConfigHostnamePrefixDiffReq)
      returns (GetChannelConfigHostnamePrefixDiffResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId/route_configs/host_diff";
  }
  // 批量获取环境路由配置是否变化
  rpc GetBatchChannelRouteConfigDiff(GetBatchChannelRouteConfigDiffReq) returns (GetBatchChannelRouteConfigDiffResp) {
    option (api.get) = "/api/v1/projects/channels/route_configs/route_diff";
  }
  // 批量获取 Channel 域名前缀 Diff 信息（和部署单元配置对比）
  rpc GetBatchChannelConfigHostnamePrefixDiff(GetBatchChannelConfigHostnamePrefixDiffReq)
      returns (GetBatchChannelConfigHostnamePrefixDiffResp) {
    option (api.get) = "/api/v1/projects/channels/route_configs/host_diff";
  }
  // @deprecated
  // 获取环境 online 部署详情
  rpc GetChannelOnlineDeploymentInfo(GetChannelOnlineDeploymentInfoReq) returns (GetChannelOnlineDeploymentInfoResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId/online_deployment";
  }
  // 获取环境单一部署记录
  rpc GetDeploymentInfo(GetDeploymentInfoReq) returns (GetDeploymentInfoResp) {
    option (api.get) = "/api/v1/projects/channels/deployments/:deploymentId";
  }
  // 修改环境基础配置
  rpc UpdateChannelBasicInfo(UpdateChannelBasicInfoReq) returns (UpdateChannelBasicInfoResp) {
    option (api.put) = "/api/v1/projects/channels/:channelId";
  }
  // 获取团队 tce 集群
  rpc GetTcePsmInfoBySpace(GetTcePsmInfoBySpaceReq) returns (GetTcePsmInfoBySpaceResp) {
    option (api.get) = "/api/v1/spaces/tce_cluster";
  }
  // 判断区域是否为 lark psm
  rpc GetDeployUnitIsLarkPsm(GetDeployUnitIsLarkPsmReq) returns (GetDeployUnitIsLarkPsmResp) {
    option (api.get) = "/api/v1/projects/deploy_units/:deployUnitId/lark_canary_psm_validity";
  }
  // 创建区域时可复用的已创建区域列表
  rpc GetExistedRegionList(GetExistedRegionListReq) returns (GetExistedRegionListResp) {
    option (api.get) = "/api/v1/projects/:projectId/deploy_unit_list";
  }
  // 获取工程方案部署配置信息
  rpc GetSolutionWebDeployConfig(GetSolutionWebDeployConfigReq) returns (GetSolutionWebDeployConfigResp) {
    option (api.get) = "/api/v1/projects/solutions/:solutionId/web_deploy_config";
  }
  // 获取区域基准环境部署配置信息
  rpc GetMainChannelConfigByDeployUnitId(GetMainChannelConfigByDeployUnitIdReq)
      returns (GetMainChannelConfigByDeployUnitIdResp) {
    option (api.get) = "/api/v1/projects/deploy_units/:deployUnitId/channels/main_channel";
  }
  // 获取文件协议路径检测值
  rpc GetGitRepoFrameworkFilePath(GetGitRepoFrameworkFilePathReq) returns (GetGitRepoFrameworkFilePathResp) {
    option (api.get) = "/api/v1/projects/:projectId/channels/route_configs/file_protocol";
  }
  // 获取线上部署的 channel 配置
  rpc GetOnlineDeploymentChannelConfig(GetOnlineDeploymentChannelConfigReq)
      returns (GetOnlineDeploymentChannelConfigResp) {
    option (api.get) = "/api/v1/projects/channels/:channelId/online_deployment_config";
  }
  // 删除泳道环境
  rpc DeleteSmallTrafficChannel(DeleteSmallTrafficChannelReq) returns (DeleteSmallTrafficChannelResp) {
    option (api.delete) = "/api/v1/projects/channels/:channelId";
  }
  // 创建泳道环境
  rpc CreateSmallTrafficChannel(CreateSmallTrafficChannelReq) returns (CreateSmallTrafficChannelResp) {
    option (api.post) = "/api/v1/projects/channels";
  }
  // 获取接入层 PSM
  rpc GetDeployUnitResolvedPsm(GetDeployUnitResolvedPsmReq) returns (GetDeployUnitResolvedPsmResp) {
    option (api.get) = "/api/v1/projects/deploy_units/resolved_psm";
  }
  // 获取区域信息
  rpc GetDeployUnitInfo(GetDeployUnitInfoReq) returns (GetDeployUnitInfoResp) {
    option (api.get) = "/api/v1/projects/deploy_units/:deployUnitId";
  }
  // 创建 web 部署及 goofy deploy 项目
  rpc CreateGoofyDeployApp(CreateGoofyDeployAppReq) returns (CreateGoofyDeployAppResp) {
    option (api.post) = "/api/v1/projects/:projectId/web_deploy/apps";
  }
  // 搜索项目 goofy deploy app
  rpc GetGoofyDeployListByProjectId(GetGoofyDeployListByProjectIdReq) returns (GetGoofyDeployListByProjectIdResp) {
    option (api.get) = "/api/v1/projects/:projectId/web_deploy/apps";
  }
  // 获取区域部署记录
  rpc GetDeploymentListByDeployUnitId(GetDeploymentListByDeployUnitIdReq)
      returns (GetDeploymentListByDeployUnitIdResp) {
    option (api.get) = "/api/v1/projects/deploy_units/:deployUnitId/deployments";
  }
  // 获取团队 TOS Bucket 列表
  rpc GetTosBucketListBySpaceId(GetTosBucketListBySpaceIdReq) returns (GetTosBucketListBySpaceIdResp) {
    option (api.get) = "/api/v1/spaces/:spaceId/tos_buckets";
  }
  // 删除区域
  rpc DeleteDeployUnitById(DeleteDeployUnitByIdReq) returns (DeleteDeployUnitByIdResp) {
    option (api.delete) = "/api/v1/projects/deploy_units/:deployUnitId";
  }
  /* End of Goofy-api */

  // 订阅 GoofyDeployEvent
  rpc GetGoofyDeployEvent(GetGoofyDeployEventReq) returns (GetGoofyDeployEventResp) {
    option (api.post) = "/api/v1/eventbridge/goofydeploy";
  }

  // 获取区域列表
  rpc GetRegionList(GetRegionListReq) returns (GetRegionListResp) {
    option (api.get) = "/api/v1/projects/web_deploy/region_list";
  }
}
