load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "featflag_proto",
    srcs = ["featflag.proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "featflag_go_proto",
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/featflagpb",
    proto = ":featflag_proto",
    visibility = ["//visibility:public"],
)
