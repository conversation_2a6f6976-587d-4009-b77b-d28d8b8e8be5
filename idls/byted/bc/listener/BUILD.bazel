load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "listener_proto",
    srcs = [
        "listener_api.proto",
        "msg.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_proto",
    ],
)

go_proto_library(
    name = "listener_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_kitex_proto",
        "@io_bazel_rules_go//proto:xhertz_plugin_go",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/listenerpb",
    proto = ":listener_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
    ],
)
