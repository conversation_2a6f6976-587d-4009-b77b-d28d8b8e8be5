load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "trigger_proto",
    srcs = ["trigger.proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "trigger_go_proto",
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/triggerpb",
    proto = ":trigger_proto",
    visibility = ["//visibility:public"],
)
