load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "resource_proto",
    srcs = [
        "project.proto",
        "resource_api.proto",
        "resource_service.proto",
        "template.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_proto",
        "//idls/byted/bc/shared:shared_proto",
        "//idls/byted/bc/varstore:varstore_proto",
        "//idls/byted/bc/workflow:workflow_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_proto",
        "@com_github_envoyproxy_protoc_gen_validate//validate:validate_proto",
    ],
)

go_proto_library(
    name = "resource_go_proto_xrpc_and_kitex_ResourceService",
    compilers = [
        "@io_bazel_rules_go//proto:xrpc_plugin_go",
        "@io_bazel_rules_go//proto:go_kitex_proto_service",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/resourcepb/resourceservice",
    proto = ":resource_proto",
    visibility = ["//visibility:public"],
    deps = [
        ":resource_go_proto",
        "//idls/byted/api:api_go_proto",
        "//idls/byted/bc/shared:shared_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/bc/workflow:workflow_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
        "//validate:validate_go_proto",
    ],
)

go_proto_library(
    name = "resource_go_proto_mockgen_ResourceService",
    compilers = ["@io_bazel_rules_go//proto:go_kitex_proto_mockgen"],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/resourcepb/resourceservice_mock",
    proto = ":resource_proto",
    visibility = ["//visibility:public"],
    deps = [
        ":resource_go_proto",
        "//idls/byted/api:api_go_proto",
        "//idls/byted/bc/shared:shared_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/bc/workflow:workflow_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
        "//validate:validate_go_proto",
    ],
)

go_proto_library(
    name = "resource_go_proto_kitexcli_ResourceService",
    compilers = ["@io_bazel_rules_go//proto:kitexcli_plugin_go"],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/rpc/bc/resourcerpc",
    proto = ":resource_proto",
    visibility = ["//visibility:public"],
    deps = [
        ":resource_go_proto_xrpc_and_kitex_ResourceService",
        "//idls/byted/api:api_go_proto",
        "//idls/byted/bc/shared:shared_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/bc/workflow:workflow_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
        "//validate:validate_go_proto",
    ],
)

go_proto_library(
    name = "resource_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_kitex_proto",
        "@io_bazel_rules_go//proto:xhertz_plugin_go",
        "@io_bazel_rules_go//proto:go_validate_proto",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/bc/resourcepb",
    proto = ":resource_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_go_proto",
        "//idls/byted/bc/shared:shared_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/bc/workflow:workflow_go_proto",
        "//idls/byted/devinfra/infra/rpc:rpc_go_proto",
        "//validate:validate_go_proto",
    ],
)
