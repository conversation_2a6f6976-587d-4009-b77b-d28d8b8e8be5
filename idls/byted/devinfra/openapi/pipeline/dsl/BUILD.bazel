load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "dsl_proto",
    srcs = [
        "auth.proto",
        "notification.proto",
        "pipeline.proto",
        "runner.proto",
        "template.proto",
        "trigger.proto",
        "var_definition.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_proto",
        "//idls/byted/bc/varstore:varstore_proto",
        "//idls/byted/devinfra/authz:authz_proto",
        "//idls/byted/devinfra/infra:infra_proto",
        "//idls/byted/devinfra/pipeline/dsl:dsl_proto",
        "//idls/byted/devinfra/pipeline/platform:platform_proto",
        "@com_github_envoyproxy_protoc_gen_validate//validate:validate_proto",
        "@com_google_protobuf//:struct_proto",
    ],
)

go_proto_library(
    name = "dsl_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_validate_proto",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/openapi/pipeline/dsl/dslpb",
    proto = ":dsl_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/devinfra/authz:authz_go_proto",
        "//idls/byted/devinfra/infra:infra_go_proto",
        "//idls/byted/devinfra/pipeline/dsl:dsl_go_proto",
        "//idls/byted/devinfra/pipeline/platform:platform_go_proto",
        "//validate:validate_go_proto",
    ],
)
