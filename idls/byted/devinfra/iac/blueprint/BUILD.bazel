load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "blueprint_proto",
    srcs = ["blueprint.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/iac:iac_proto",
        "//idls/byted/devinfra/iac/blueprint/meta:meta_proto",
        "//idls/byted/devinfra/iac/blueprint/release/pipeline:pipeline_proto",
        "//idls/byted/devinfra/iac/blueprint/resource/scm:scm_proto",
        "//idls/byted/devinfra/iac/platforms/ci:ci_proto",
        "@com_google_protobuf//:any_proto",
    ],
)

go_proto_library(
    name = "blueprint_go_proto",
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/blueprintpb",
    proto = ":blueprint_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/devinfra/iac:iac_go_proto",
        "//idls/byted/devinfra/iac/blueprint/meta:meta_go_proto",
        "//idls/byted/devinfra/iac/blueprint/release/pipeline:pipeline_go_proto",
        "//idls/byted/devinfra/iac/blueprint/resource/scm:scm_go_proto",
        "//idls/byted/devinfra/iac/platforms/ci:ci_go_proto",
    ],
)
