syntax = "proto3";

package byted.devinfra.rolloutv2.generator;

import "idls/byted/devinfra/iac/rolloutv2/apiserver/asset.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/iac/rolloutv2/generatorpb";

message InputPair {
  string key = 1;
  bytes value = 2;
}

message Opt {
  string key = 1;
  string value = 2;
}

message GenerateRequest {
  // name of the generator.
  string name = 1;
  // generator inputs.
  repeated InputPair inputs = 2;
  // generator options.
  repeated Opt opts = 3;
}

message GenerateResponse {
  // assets.
  repeated apiserver.Asset assets = 1;
}
