syntax = "proto3";

package byted.devinfra.bytecd;

import "idls/byted/api/api.proto";
import "validate/validate.proto";
import "idls/byted/devinfra/bytecd/base.proto";
import "idls/byted/devinfra/bytecd/pipeline_execution.proto";
import "idls/byted/devinfra/bytecd/apply_execution.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/bytecdpb";

message Project {
  string id = 1;
  string git_repo = 2;
  string dir = 3;
  string oic_repo = 4;
  string ros_project_name = 5;
  repeated string ros_control_planes = 6;
  repeated string owners = 7;
  string generator_name = 8;
  GitTriggerStatus git_trigger_status = 9;
  string created_by = 10;
  string created_at = 11;
}

message CreateProjectReq {
  string git_repo = 1;
  string dir = 2;
  repeated string owners = 3 [(validate.rules).repeated = { unique: true, min_items: 1 }];
  string generator_name = 4;
  GitTriggerStatus git_trigger_status = 5;
}

message CreateProjectResp {
  Project project = 1;
}

message ListProjectReq {
  string project_id = 1 [(api.query) = "projectId"];
  string git_repo = 2 [(api.query) = "gitRepo"];
  string dir = 3 [(api.query) = "dir"];
  string generator_name = 4 [(api.query) = "generatorName"];

  // 分页信息
  int32 page_size = 100 [(api.query) = "pageSize", (validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(api.query) = "pageNum", (validate.rules).int32 = { gt: 0 }];
}

message ListProjectResp {
  Pagination pagination = 1;
  repeated Project projects = 2;
}

message GetProjectReq {
  string project_id = 1 [(api.path) = "projectId", (validate.rules).string = { min_len: 1 }];
}

message GetProjectResp {
  Project project = 1;
}

message UpdateProjectReq {
  string project_id = 1 [(api.path) = "projectId", (validate.rules).string = { min_len: 1 }];
  string git_repo = 2;
  string dir = 3;
  repeated string owners = 4;
  string generator_name = 5;
  GitTriggerStatus git_trigger_status = 6;
}

message UpdateProjectResp {
  Project project = 1;
}

enum GitTriggerStatus {
  GIT_TRIGGER_STATUS_UNSPECIFIED = 0;
  GIT_TRIGGER_STATUS_DISABLE = 1;
  GIT_TRIGGER_STATUS_ENABLE = 2;
}

message StackApplyConfig {
  bool auto_confirm = 1;
}

message Stack {
  string name = 1;
  string ros_control_plane = 2;
  StackApplyConfig apply_config = 3;
}

message BootstrapProjectReq {
  string project_id = 1 [(api.path) = "projectId", (validate.rules).string = { min_len: 1 }];
  string byte_cycle_workspace_label = 2;
  repeated Stack stacks = 3;
}

message BootstrapProjectResp {}

message ExecuteProjectPipelineReq {
  string project_id = 1 [(api.path) = "projectId", (validate.rules).string = { min_len: 1 }];
  string token = 2;           // 作为幂等Token，相同的值只触发一次
  string git_commit_sha = 3;  // git commit sha, 为空时选择最新的commit
  string description = 4;
}

message ExecuteProjectPipelineResp {
  PipelineExecution pipeline_execution = 1;
}

message ApplyProjectReq {
  string project_id = 1 [(api.path) = "projectId", (validate.rules).string = { min_len: 1 }];
  string token = 2;           // 作为幂等Token，相同的值只触发一次
  string git_commit_sha = 3;  // git commit sha, 为空时选择最新的commit
  string description = 4;
}

message ApplyProjectResp {
  ApplyExecution apply_execution = 1;
}