syntax = "proto3";

package byted.devinfra.cd.release_ticket;

import "idls/byted/devinfra/cd/shared/base.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb";

enum TaskAction {
  TASK_ACTION_UNSPECIFIED = 0;
  TASK_ACTION_START = 1;   // 让任务开始
  TASK_ACTION_CANCEL = 2;  // 任务被取消了
  TASK_ACTION_RESET = 3;   // 任务重制
  TASK_ACTION_PING = 4;    // 轮询此任务
}

message DispatchTaskReq {
  string username = 1;     // 调用 TriggerExecution 时候如果传入 username 了, 那么这里就会有值
  int64 execution_id = 2;  // 流程引擎的流程 id
  int64 task_id = 3;       // 流程引擎的任务的 id
  string task_name = 4;    // 任务名称
  TaskAction action = 5;
  bytes params = 6;             // 透传参数, 调用 TriggerExecution 的时候传递的
  int64 release_ticket_id = 7;  // 发布单 id, 也就是 TriggerExecution 的 unique_id 参数
  bytes prev_task_context = 8;
  string byteflow_execution_id = 9;  // byteflow 的 execution id
  bytes store = 10;                  // RetryTast 的时候传递过来的
}

message DispatchTaskResp {
  // 表示这个任务是否结束了
  bool finished = 1;
  // 这个任务结束,传递个下一个任务的数据,一般不需要传递这个字段
  bytes next_task_context = 2;

  // 增加框架 resp
  shared.BaseResp base_resp = 255;
}
