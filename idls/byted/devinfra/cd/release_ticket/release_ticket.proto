syntax = "proto3";

package byted.devinfra.cd.release_ticket;

import "idls/byted/api/api.proto";
import "idls/byted/devinfra/cd/shared/shared.proto";
import "idls/byted/devinfra/cd/workflow/workflow.proto";
import "idls/byted/devinfra/cd/workflow/node.proto";
import "idls/byted/devinfra/cd/workflow/integration_conf.proto";
import "validate/validate.proto";
import "idls/byted/devinfra/cd/release_ticket/shared/common.proto";
import "idls/byted/devinfra/cd/release_ticket/shared/variable.proto";
import "idls/byted/devinfra/cd/release_ticket/shared/task.proto";
import "idls/byted/devinfra/cd/release_ticket/stage.proto";
import "idls/byted/devinfra/cd/release_ticket/change_item.proto";
import "idls/byted/devinfra/cd/change_item/change_item.proto";
import "idls/byted/devinfra/cd/change_item/change_item_tce.proto";
import "idls/byted/devinfra/cd/change_item/change_item_web.proto";
import "idls/byted/devinfra/cd/paas/tce.proto";
import "idls/byted/devinfra/cd/paas/faas.proto";
import "idls/byted/devinfra/cd/paas/gecko.proto";
import "idls/byted/devinfra/cd/paas/goofy.proto";
import "idls/byted/devinfra/cd/paas/cronjob.proto";
import "idls/byted/devinfra/cd/paas/custom.proto";
import "idls/byted/devinfra/cd/paas/tcc.proto";
import "idls/byted/devinfra/cd/env/env_project_config.proto";
import "idls/byted/devinfra/cd/workflow/workflow_share.proto";
import "idls/byted/devinfra/cd/branching_model_config/branching_model_config.proto";
import "idls/byted/devinfra/cd/team_flow_config/team_flow_config.proto";
import "idls/byted/devinfra/cd/project/project.proto";
import "idls/byted/devinfra/cd/paas/scm.proto";
import "idls/byted/devinfra/cd/shared/base.proto";
import "idls/byted/devinfra/git/commits.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticketpb";

enum WorkItemPlatformType {
  WORK_ITEM_PLATFORM_TYPE_UNSPECIFIED = 0;
  WORK_ITEM_PLATFORM_TYPE_JIRA = 1;
  WORK_ITEM_PLATFORM_TYPE_MEEGO = 2;
}

enum ProjectIntegrationLockedType {
  PROJECT_INTEGRATION_LOCKED_TYPE_UNSPECIFIED = 0;
  // 流水线校验未通过
  PROJECT_INTEGRATION_LOCKED_TYPE_PIPELINE_STATUS_INVALID = 1;
  // 部署工单校验未通过
  PROJECT_INTEGRATION_LOCKED_TYPE_DEPLOY_EXECUTION_STATUS_INVALID = 2;
  // 回滚校验未通过
  PROJECT_INTEGRATION_LOCKED_TYPE_ROLLBACK_STATUS_INVALID = 3;
}

enum ProjectBugfixDevTaskUnmergeableType {
  PROJECT_BUGFIX_DEV_TASK_UNMERGEABLE_TYPE_UNSPECIFIED = 0;
  // 已经完成部署
  PROJECT_BUGFIX_DEV_TASK_UNMERGEABLE_TYPE_DEPLOYED = 1;
  // 发起过回滚
  PROJECT_BUGFIX_DEV_TASK_UNMERGEABLE_TYPE_TRIGGERED_ROLLBACK = 2;
}

enum UpdateChangeItemDeployStrategyTriggerType {
  UPDATE_CHANGE_ITEM_DEPLOY_STRATEGY_TRIGGER_TYPE_UNSPECIFIED = 0;
  // 流水线校验未通过
  UPDATE_CHANGE_ITEM_DEPLOY_STRATEGY_TRIGGER_TYPE_AUTO = 1;
  // 部署工单校验未通过
  UPDATE_CHANGE_ITEM_DEPLOY_STRATEGY_TRIGGER_TYPE_MANUAL = 2;
}

// 迁移开发任务类型
enum MigrationDevTaskType {
  MIGRATION_DEV_TASK_TYPE_UNSPECIFIED = 0;
  // 重建
  MIGRATION_DEV_TASK_TYPE_RECREATION = 1;
  // 重新绑定
  MIGRATION_DEV_TASK_TYPE_REBINDING = 2;
}

// 迁移开发任务状态
enum MigrationDevTaskStatus {
  MIGRATION_DEV_TASK_STATUS_UNSPECIFIED = 0;
  // 初始化 未开始
  MIGRATION_DEV_TASK_STATUS_INIT = 1;
  // 进行中
  MIGRATION_DEV_TASK_STATUS_RUNNING = 2;
  // 成功
  MIGRATION_DEV_TASK_STATUS_SUCCEED = 3;
  // 失败跳过
  MIGRATION_DEV_TASK_STATUS_SKIP = 4;
  // 失败
  MIGRATION_DEV_TASK_STATUS_ERROR = 5;
}

enum MigrationDevTaskOperate {
  MIGRATION_DEV_TASK_OPERATE_UNSPECIFIED = 0;
  // 重试
  MIGRATION_DEV_TASK_OPERATE_RETRY = 1;
  // 跳过
  MIGRATION_DEV_TASK_OPERATE_SKIP = 2;
}

enum InitTaskType {
  INIT_TASK_TYPE_UNSPECIFIED = 0;
  // 初始化任务
  INIT_TASK_TYPE_INIT = 1;
  // 重建开发任务
  INIT_TASK_TYPE_RECREATION_DEV_TASK = 2;
  // 重新绑定开发任务
  INIT_TASK_TYPE_REBINDING_DEV_TASK = 3;
}

enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  // 自动合入失败消息
  MESSAGE_TYPE_AUTO_MERGE_FAILED = 1;
}

message SubmitReleaseTicketResp {
  // 上线单 ID
  uint64 release_ticket_id = 1;

  // 增加框架 resp
  shared.BaseResp base_resp = 255;
}

message CreateLarkGroup {
  repeated string usernames = 1;
  string lark_group_name = 2;
}

message LinkLarkGroup {
  repeated string usernames = 1;
  string lark_group_id = 2;
}
message WorkItem {
  string id = 1;
  WorkItemPlatformType work_item_platform_type = 2;
  string space_key = 3;
  string type = 4;
  string space_id = 5;
  string status = 6;
  string url = 7;
  string name = 8;
}

message SubmitReleaseTicketReq {
  // 名称
  // @required
  string name = 1 [(validate.rules).string = {
    min_len: 1,
    max_len: 255,
  }];

  // workspace_id
  // @required
  uint64 workspace_id = 2;

  // workflow_id
  // @required
  uint64 workflow_id = 3;

  // description
  // @required
  string description = 4;

  // boe_env_name
  // @optional
  string boe_env_name = 5;

  // ppe_env_name
  // @optional
  string ppe_env_name = 6;

  // creator
  // @required
  string creator = 7;

  // 阶段环境配置。Key 为 `Workflow.Orchestration[].Node.ID`
  // @required
  map<uint64, workflow.Env> stage_envs_map = 8;

  // 公告
  // @optional
  string notice = 9;

  // 时间计划
  // 开始集成时间
  // @optional
  int64 start_integration_at = 10;
  // 结束集成时间
  // @optional
  int64 end_intergration_at = 11;
  // 项目
  repeated Project projects = 12;
  // larkID
  // @optional
  repeated string lark_group_ids = 14;

  // 工作项
  // @optional
  repeated WorkItem work_items = 15;

  // 发布负责人列表
  // @required
  repeated string release_approvers = 16 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];

  // 控制面
  repeated shared.ControlPlane control_planes = 17;

  // 用户自定义变量信息,包括可输入与不可输入
  repeated Variable custom_vars = 19;

  // 测试负责人列表
  // @required
  repeated string test_approvers = 20 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 0,
  }];

  // 阶段计划开始时间
  // @deprecated 已废弃，计划回归测试时间用 test_at，计划开始发布时间用 release_at
  repeated StagePlanTimeInfo stages_start_time_plans = 21;
  // 创建飞书群
  repeated CreateLarkGroup create_lark_groups = 22;
  // 关联飞书群
  repeated LinkLarkGroup link_lark_groups = 23;
  // 是否来自创建开发任务
  // @optional
  bool is_from_create_dev_task = 24;
  // 研发流程id
  // @optional
  uint64 team_flow_id = 25;
  // 工作分支配置
  // @optional
  repeated BranchConfig work_branches = 26;
  // 计划回归测试时间
  // @optional
  int64 test_at = 27;
  // 计划开始发布时间
  // @optional
  int64 release_at = 28;
  // 源发布单
  // @optional
  uint64 copy_from_rt_id = 29;
  // 批量迁移开发任务
  // @optional
  repeated MigrationDevTask migration_dev_tasks = 30;

  // 透传给环境平台的参数
  // @optional
  map<string, string> env_project_data = 31;
}

// 迁移开发任务参数
message MigrationDevTask {
  // 开发任务ID
  // @required
  uint64 dev_task_id = 1;
  // 是否自动合入
  // @optional
  bool change_auto_merge_enabled = 2;
}

// 分支配置
message BranchConfig {
  // 分支名
  string name = 1;
  // 分支类型
  branching_model_config.BranchType branch_type = 2;
  // 发布单完成后是否删除
  bool delete_after_complete = 3;
  // 起点commit
  string ref = 4;
  // 是否自动同步
  bool auto_sync = 5;
}

message Project {
  ProjectInfo project_info = 1;
  repeated BuildConfig build_configs = 2;
  // 项目依赖关系
  repeated ProjectInfo deploy_dependencies = 3;
  // 跨端 channel
  repeated paas.ChannelItem channel_items = 4;
}

message ProjectInfo {
  string type = 1;
  string name = 2;
  optional string psm = 3;
  string project_unique_id = 4;
  // 原 goofy web 项目名称，和 bc1.0 里 web 项目名称是 n:1 关系。为了兼容 BC1.0 遗留数据所设置
  string ttp_web_name = 5;
  // 项目负责人
  repeated string project_owners = 6;
  ScmVersionChoiceType scmVersionChoice= 7;
}

message ProjectControlPlaneInfo {
  // 项目信息
  ProjectInfo project_info = 1;
  shared.ControlPlane control_plane = 2;
}

message BuildConfig {
  repeated ScmDependency scm_dependencies = 1;
  // keep num 2, because we will add geco item
  string control_panel = 3;
  // 非scm项目使用RepoDependency传递repo信息
  repeated RepoDependency repo_dependencies = 2;
  // 主仓信息
  MainRepoInfo main_repo_info = 4;
  // 合并编译的相关信息
  shared.ScmMergeBuildInfo scm_merge_build_info = 5;
}

message ScmDependency {
  string name = 1;
  int64 id = 2;
  string revision = 3;
  string pub_base = 4;
  string git_repo_name = 5;
  optional bool is_main = 6;
  // 项目变更类型
  change_item.PreChangeItemType pre_change_item_type = 7;
  // 源分支
  string source_branch = 8;
  // 目标分支
  string target_branch = 9;
  // commit id
  string commit_id = 10;
    // 是否永远使用最新版本
  bool useLatest = 16;
  int64 git_repo_id = 11;
}

message MainRepoInfo {
  // 仓库名
  string git_repo_name = 1;
  // 发布方式 version/branch/commit
  string pub_base = 2;
  // 集成分支
  string integration_branch = 4;
  // 发布分支
  string release_branch = 5;
  // 归档分支
  string archive_branch = 6;
  // 发布分支的源
  string release_ref = 7;
  // 版本 pub_base = version时消费
  string version = 10;
  // 预留 commit_id
  string commit_id = 14;
  // 主仓的scm id， 没有则不填
  int64 scm_id = 15;
  // 主仓的scm name， 没有则不填
  string scm_name = 16;
  // 发布后是否删除集成分支
  bool delete_integration_branch_after_release = 17;
  // 发布后是否删除发布分支
  bool delete_release_branch_after_release = 18;
  // create mr兼容，预留字段，后续可能废弃
  change_item.PreChangeItemType pre_change_item_type = 21;
  // create mr兼容，预留字段，后续可能废弃
  string source_branch = 22;
  // create mr兼容，预留字段，后续可能废弃
  string target_branch = 23;
}

message RepoDependency {
  string commit_id = 1;
  string git_repo_name = 2;
  string branch = 3;
  // 项目变更类型
  change_item.PreChangeItemType pre_change_item_type = 4;
  // 源分支
  string source_branch = 5;
  // 目标分支
  string target_branch = 6;
  string revision = 7;
  string pub_base = 8;
}

message StagePlanTimeInfo {
  // 拓扑图节点id
  uint64 node_id = 1;
  // 时间戳
  int64 timestamp = 2;
}

message GetReleaseTicketInitTaskInfoReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 空间ID
  // @required
  uint64 workspace_id = 2 [(api.query) = "workspaceId"];
}

message GetReleaseTicketInitTaskInfoResp {
  // 所有任务详情（包括不展示的任务）
  repeated InitTaskDetail init_task_details = 1;
  // 所有任务是否在运行中（包括不展示的任务）
  bool is_running = 2;
  // 开始时间戳
  uint64 created_at = 3;
  // 结束时间戳
  uint64 end_at = 4;
}

message InitTaskDetail {
  // 任务名
  InitTaskName task_name = 1;
  // 任务状态
  InitTaskState task_state = 2;
  // 是否展示在前端
  bool is_show = 3;
  // 任务耗时
  uint64 spend_time = 4;
  // 错误码
  uint64 err_code = 5;
  // 错误信息
  string err_msg = 6;
}

message RetryReleaseTicketInitTaskReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message RetryReleaseTicketInitTaskResp {}

message CancelReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];

  // 操作人
  string operator = 2;

  // 取消原因类型
  CancelReasonType cancel_reason_type = 3;

  // 取消原因
  string cancel_reason = 4;
}

message CancelReleaseTicketResp {}

message StartIntegrationReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 操作人
  string operator = 2;
  // 读发布单配置，尝试开始集成
  // @optional
  bool is_according_rt_workflow = 3;
}

message GetReleaseTicketIntegrationInfoReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

enum ReleaseTicketApproverRole {
  RELEASE_TICKET_APPROVER_ROLE_UNSPECIFIED = 0;
  RELEASE_TICKET_APPROVER_ROLE_RELEASE = 1;
  RELEASE_TICKET_APPROVER_ROLE_TEST = 2;
}

enum IntegrationStatus {
  INTEGRATION_STATUS_UNSPECIFIED = 0;
  INTEGRATION_STATUS_INTEGRATING = 1;
  INTEGRATION_STATUS_COMPLETED = 2;
}

// 发布单开始集成触发方式
enum IntegrationTriggerType {
  INTEGRATION_TRIGGER_TYPE_UNSPECIFIED = 0;
  // 用户点击
  INTEGRATION_TRIGGER_TYPE_USER_CLICK = 1;
  // 创建后自动开始
  INTEGRATION_TRIGGER_TYPE_AUTO_START = 2;
  // 定时
  INTEGRATION_TRIGGER_TYPE_TIMING = 3;
}

// 发布单结束集成触发方式
enum FinishIntegrationTriggerType {
  FINISH_INTEGRATION_TRIGGER_TYPE_UNSPECIFIED = 0;
  // 用户点击
  FINISH_INTEGRATION_TRIGGER_TYPE_USER_CLICK = 1;
  // 发布开始触发
  FINISH_INTEGRATION_TRIGGER_TYPE_AUTO_START_AFTER_DEPLOY = 2;
  // 定时
  FINISH_INTEGRATION_TRIGGER_TYPE_TIMING = 3;
  // 阶段结束触发
  FINISH_INTEGRATION_TRIGGER_TYPE_STAGE_END = 4;
  // 所有开发任务完成触发
  FINISH_INTEGRATION_TRIGGER_TYPE_ALL_DEV_TASK_END = 5;
}

message VersionInfo {
  string version = 1;
  string creator = 2;
  string created_at = 3;
}

message GetReleaseTicketIntegrationInfoResp {
  uint64 integration_id = 1;
  uint64 finish_count = 2;
  uint64 total_count = 3;
  IntegrationStatus status = 4;
  // protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
  repeated VersionInfo version_list = 5;
}

message ReopenReleaseTicketIntegrationReq {
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // reason
  // @required
  string reason = 2;
}

message ReopenReleaseTicketIntegrationResp {
  bool result = 1;
}

message StartIntegrationReleaseTicketResp {}

message CompleteIntegrationReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 操作人
  string operator = 2;
  // 完成集成触发类型
  FinishIntegrationTriggerType trigger_type = 5 [(api.body) = "triggerType"];
  // 是否自动踢除 open 的开发任务
  workflow.FinishIntegrationCancelRunningDevelopmentTask cancel_running_development_task = 6
  [(api.body) = "cancelRunningDevelopmentTask"];
}

// 发布单结束集成触发方式
enum CompleteReleaseTicketTriggerType {
  COMPLETE_RELEASE_TICKET_TRIGGER_TYPE_UNSPECIFIED = 0;
  // 用户点击强制完成
  COMPLETE_RELEASE_TICKET_TRIGGER_TYPE_USER_CLICK = 1;
  // 发布开始触发
  COMPLETE_RELEASE_TICKET_TRIGGER_TYPE_PIPELINE_CALLBACK = 2;
  // 用户点击"归档并强制完成"
  COMPLETE_RELEASE_TICKET_TRIGGER_TYPE_USER_CLICK_WITH_ARCHIVE = 3;
}

message CompleteIntegrationReleaseTicketResp {}

message CompleteReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 操作人
  string operator = 2;
  // 完成触发类型
  CompleteReleaseTicketTriggerType trigger_type = 5 [(api.body) = "triggerType"];
}

message CompleteReleaseTicketResp {
  // 是否被阻塞
  bool is_blocked = 1;
  // 阻塞原因
  string block_reason = 2;
  // 阻塞原因 en
  string block_reason_en = 3;
}

message GetReleaseTicketProjectsGitDefaultBranchReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketProjectsGitDefaultBranchResp {
  // 各项目仓库默认分支名称
  // @optional
  repeated ProjectGitDefaultBranchItem project_git_default_branches = 1;
}

message GetReleaseTicketProjectsBranchReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketProjectsBranchResp {
  // 各项目仓库分支名称
  // @optional
  repeated ProjectBranch project_branches = 1;
}

message ProjectBranch {
  // 项目唯一 ID
  // @required
  string project_unique_id = 1;
  // 项目类型
  // @required
  shared.ProjectType project_type = 2;
  // 项目名称
  string project_name = 3;
  // 控制面
  shared.ControlPlane control_plane = 5;
  // 分支模型
  branching_model_config.BranchingModelConfig work_branch = 6;
  // 仓库
  // @required
  GitInfo git_info = 7;
  // tbd 发布时的分支信息，后续用于兼容其他流程
  ReleaseInfo release_info = 8;
  shared.ScmPubBase pub_base = 9; //发布方式
  string release_ref = 10;
}

message ReleaseInfo {
  // 发布分支
  string release_branch = 1;
  // 发布commit，回归测试时 = head_commit
  string release_commit_id = 2;
}

message GetReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketResp {
  // 上线单信息
  ReleaseTicketDetail release_ticket = 1;

  // 增加框架 resp
  shared.BaseResp base_resp = 255;
}

// 交付清单的默认项目信息提前计算
message PreloadRTChangeItemReq {
  int64 release_ticket_id = 1 [(api.path) = "releaseTicketId"];
  shared.ControlPlane control_plane = 2 [(api.path) = "cp"];
}

message PreloadRTChangeItemResp {
  repeated PreloadRTChangeItem items = 1;
}

message PreloadRTChangeItem {
  ReleaseTicketChangeItem items = 1;
  // 当前控制面
  bool is_service_err = 2;
  bool is_git_err = 3;
}

// UpdateReleaseTicketReq 修改发布单 https://bytedance.feishu.cn/wiki/L7uGw5ZZSigxznkF8aicv4Ionmd
message UpdateReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];

  // 名称
  // @optional
  optional string name = 2;

  // 公告
  // @optional
  optional string notice = 3;

  // 发布负责人列表
  // @optional
  repeated string release_approvers = 4;

  // 测试负责人列表
  // @optional
  repeated string test_approvers = 5;

  // 版本号，更新使用
  int64 version = 6;
  // 变量
  map<uint64, workflow.Env> stage_envs_map = 7;
  // 关联工作项
  repeated WorkItem work_items = 8;
  // 控制面
  repeated shared.ControlPlane control_planes = 9;
  // lark_group_ids
  repeated string lark_group_ids = 10;
  // 创建飞书群
  repeated CreateLarkGroup create_lark_groups = 11;
  // 关联飞书群
  repeated LinkLarkGroup link_lark_groups = 12;

  // 发布单计划集成时间
  // @optional
  int64 start_integration_at = 13;
  // 发布单计划结束集成时间
  // optional
  int64 end_integration_at = 14;
  // 阶段计划时间
  // @deprecated 已废弃，计划回归测试时间用 test_at，计划开始发布时间用 release_at
  repeated StagePlanTimeInfo stage_plan_time_infos = 15;

  // 用户自定义变量信息，包括可输入与不可输入
  //
  // @optional
  repeated Variable custom_vars = 16;

  // 单发布单关联项目
  repeated Project projects = 17;
  // 发布单结束时间
  string end_at = 18;

  // 以下是因为pb无法传输长度为0的数组添加的兼容项
  // @optional
  bool is_work_items_empty = 19;
  bool is_control_planes_empty = 20;
  bool is_lark_group_ids_empty = 21;
  bool is_create_lark_groups_empty = 22;
  bool is_link_lark_groups_empty = 23;
  bool is_projects_empty = 24;
  bool is_custom_vars_empty = 25;
  bool is_release_approvers_empty = 26;
  bool is_test_approvers_empty = 27;
  // 是否设置发布单计划时间，加这个字段用于区分不更新计划时间和设置计划时间为空两种情况
  // optional
  bool is_set_rt_plan_time = 28;

  // 单发布单项目锁定状态
  // @optional
  optional bool single_ticket_change_item_locked = 29;

  // 计划回归测试时间
  // @optional
  int64 test_at = 30;
  // 计划开始发布时间
  // @optional
  int64 release_at = 31;

  optional bool is_auto_sync = 32;

  // 透传给环境平台的参数
  // @optional
  map<string, string> env_project_data = 33;
}

message UpdateReleaseTicketResp {
  // 版本号，更新使用
  int64 version = 1;
}

message ReleaseTicketDetail {
  // 上线单 ID
  uint64 release_ticket_id = 1;
  // 创建时间
  string created_at = 2;
  // 更新时间
  string updated_at = 3;
  // 删除时间
  string deleted_at = 4;
  // 名称
  // @required
  string name = 5 [(validate.rules).string = {
    min_len: 1,
    max_len: 255,
  }];

  // 状态
  // @required
  ReleaseTicketStatus status = 6;

  // workspace_id
  // @required
  uint64 workspace_id = 7;

  // workflow_id
  // @required
  uint64 workflow_id = 8;

  // orchestration
  // @required
  // protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
  repeated release_ticket.Stage orchestration = 9 [deprecated = true];

  // workflow_config
  // @required
  workflow.WorkflowConfig workflow_config = 10;

  // workflow名称
  string workflow_name = 11 [(validate.rules).string = {min_len: 1, max_len: 200}];
  // workflow英文名称
  string workflow_name_i18n = 12 [(validate.rules).string = {max_len: 200}];

  // control_planes
  // @required
  repeated shared.ControlPlane control_planes = 13 [(validate.rules).repeated = {
    max_items: 10,
    min_items: 1,
  }];

  // description
  // @required
  string description = 14;

  // boe_env_name
  string boe_env_name = 15;

  // ppe_env_name
  string ppe_env_name = 16;

  // 创建人
  // @required
  string creator = 17;

  // 发布负责人列表
  // @required
  repeated string release_approvers = 18 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];

  // 发布单绑定的变更项
  repeated ReleaseTicketChangeItem change_items = 19;

  workflow.Env env = 20;

  // 集成区 id
  uint64 integration_id = 21;

  // 回滚状态
  RollbackStatus rollback_status = 22;

  // 回滚原因
  string rollback_reason = 23;

  // 测试负责人列表
  // @required
  repeated string test_approvers = 24 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 0,
  }];
  // 公告
  // @optional
  string notice = 25;

  // 时间计划
  // 开始集成时间
  // @optional
  int64 start_integration_at = 26;
  // 结束集成时间
  // @optional
  int64 end_intergration_at = 27;
  // 发布时间
  // @optional
  int64 release_at = 29;
  // larkID
  // @optional
  repeated string lark_group_ids = 30;
  // 工作项
  // @optional
  repeated WorkItem work_items = 31;

  // 版本号，更新使用
  int64 version = 32;

  // 阶段计划开始时间
  // @deprecated 已废弃，计划回归测试时间用 test_at，计划开始发布时间用 release_at
  repeated StagePlanTimeInfo stages_start_time_plans = 33;
  // 状态改变时间
  int64 status_change_at = 34;

  // 发布单开始时间，格式：yyyy-MM-dd HH:mm:ss
  string start_at = 35;

  // 发布单结束时间,格式：yyyy-MM-dd HH:mm:ss
  string end_at = 36;

  // 关联开发任务数量类型
  workflow.NumOfAssociatedDevTaskType num_of_associated_dev_task_type = 37;

  // 单发布单项目锁定状态
  // @optional
  optional bool single_ticket_change_item_locked = 38;

  // 发布单异常原因，如创建检测项接口失败导致阶段处于未开始
  ExceptionReason exception_reason = 39;

  // 各项目默认分支名称。当分支模型设置为git默认时有效
  // @optional
  repeated ProjectGitDefaultBranchItem project_git_default_branches = 40;

  // 发布单强制完成状态
  ForceCompleteStatus force_complete_status = 41;

  // 完成发布时间,格式：yyyy-MM-dd HH:mm:ss
  string end_release_time = 42;
  // 是否是分批次(分批次流程可分批次，可不分批)
  bool is_incremental_delivery = 43;

  // 使用的研发流程
  // 研发流程 id
  uint64 team_flow_id = 50;
  // 研发流程类型
  shared.TeamFlowType team_flow_type = 51;
  // 研发流程名
  string team_flow_name = 52;
  // 研发流程英文名
  string team_flow_name_i18n = 53;
  // 流程引擎 execution_id
  uint64 execution_id = 54;

  // 计划回归测试时间
  // @optional
  int64 test_at = 55;

  // 取消原因
  CancelRtReason cancel_rt_reason = 56;

  // 源发布单
  uint64 copy_from_rt_id = 57;

  // 根据发布单项目计算出来的控制面信息，用于前端展示
  // @required
  repeated shared.ControlPlane control_planes_from_cd_projects = 58 [(validate.rules).repeated = {
    max_items: 10,
    min_items: 0,
  }];

  // 发布单关联的开发任务们的控制面并集
  // @required
  repeated shared.ControlPlane control_planes_from_dev = 59 [(validate.rules).repeated = {
    max_items: 10,
    min_items: 0,
  }];
}

message CancelRtReason {
  // 取消原因类型
  CancelReasonType cancel_reason_type = 1;
  // 取消原因
  string cancel_reason = 2;
}

message ProjectGitDefaultBranchItem {
  // 控制面
  // @required
  shared.ControlPlane control_plane = 1;
  // 项目唯一 ID
  // @required
  string project_unique_id = 2;
  // 项目类型
  // @required
  shared.ProjectType project_type = 3;
  // 项目名称
  string project_name = 4;
  // 仓库
  // @required
  GitInfo git_info = 5;
  // 分支名
  // @required
  string branch_name = 6;
}

enum ExceptionReason {
  EXCEPTION_REASON_UNSPECIFIED = 0;
  // 阶段未开始
  EXCEPTION_REASON_STAGE_PENDING = 1;
}

message GetReleaseTicketListReq {
  // 空间 id
  // uint64 workspace_id = 1 [(api.query) = "workspaceId", (validate.rules).uint64 = { gt: 0 }];
  uint64 workspace_id = 1 [(api.query) = "workspaceId"];
  // 开发任务模板id, 存在获取某个开发任务模板下的发布单列表
  // @optional
  uint64 dev_task_workflow_id = 2 [(api.query) = "devTaskWorkflowId"];
  // 状态列表, statuses 为空，表示不区分状态
  // @optional
  repeated ReleaseTicketStatus statuses = 3 [(api.query) = "statuses"];
  // 创建人
  // @optional
  string creator = 4 [(api.query) = "creator"];
  // 发布单名称
  // @optional
  string release_ticket_name = 5 [(api.query) = "releaseTicketName"];
  // 发布单回滚状态
  // @optional
  repeated RollbackStatus rollback_statuses = 6 [(api.query) = "rollbackStatus"];
  // 发布单关联任务类型
  // optional
  repeated workflow.NumOfAssociatedDevTaskType num_of_associated_dev_task_types = 7
  [(api.query) = "numOfAssociatedDevTaskTypes"];
  // 项目唯一标识。例如，对于 TCE 项目来说是 P.S.M
  // @optional
  optional string project_unique_id = 8 [(api.query) = "projectUniqueId"];
  // 项目类型
  // @optional
  optional shared.ProjectType project_type = 9 [(api.query) = "projectType"];
  // 发布单模板ids
  // @optional
  repeated uint64 release_ticket_workflow_ids = 10 [(api.query) = "releaseTicketWorkflowIds"];

  // 排序参数
  repeated SortParam sort_params = 11 [(api.query) = "sortParams"];

  // 研发流程ids
  // @optional
  repeated uint64 team_flow_ids = 12 [(api.query) = "teamFlowIds"];

  // 项目维度筛选，传入`[{"project_unique_id":"demo.demo.demo","project_type":1}]`
  string search_project = 13 [(api.query) = "searchProject"];

  // 发布单过滤规则
  // @optional
  AssociateRTFilterBy filter_by = 14 [(api.query) = "filterBy"];

  // 分页页码
  int64 page = 100 [(api.query) = "page", (validate.rules).int64 = {gt: 0}];
  // 分页大小
  int64 page_size = 101 [(api.query) = "pageSize", (validate.rules).int64 = {gt: 0}];
}

message SearchReleaseTicketReq {
  // 空间 id
  uint64 workspace_id = 1;
  // 开发任务模板id, 存在获取某个开发任务模板下的发布单列表
  // @optional
  uint64 dev_task_workflow_id = 2;
  // 状态列表, statuses 为空，表示不区分状态
  // @optional
  repeated ReleaseTicketStatus statuses = 3;
  // 创建人
  // @optional
  string creator = 4;
  // 发布单名称
  // @optional
  string release_ticket_name = 5;
  // 发布单回滚状态
  // @optional
  repeated RollbackStatus rollback_statuses = 6;
  // 发布单关联任务类型
  // optional
  repeated workflow.NumOfAssociatedDevTaskType num_of_associated_dev_task_types = 7;
  // 发布单模板ids
  // @optional
  repeated uint64 release_ticket_workflow_ids = 8;

  // 排序参数
  repeated SortParam sort_params = 9;

  // 项目维度筛选
  repeated SearchProjectParam search_projects = 10;

  // 有关人员
  // @optional
  string about_user = 11;

  repeated string about_users = 12;

  repeated uint64 meego_ids = 13;

  // 分页页码
  int64 page = 100;
  // 分页大小
  int64 page_size = 101;
}

message SearchProjectParam {
  string project_unique_id = 1;
  // 项目类型
  // @optional
  shared.ProjectType project_type = 2;
}

message GetReleaseTicketListResp {
  // 上线单列表
  repeated ReleaseTicketListItem release_tickets = 1;
  // 分页信息
  shared.PageInfo page_info = 100;
}

message ReleaseTicketListItem {
  // 上线单 ID
  uint64 release_ticket_id = 1;
  // 创建时间
  string created_at = 2;
  // 更新时间
  string updated_at = 3;
  // 删除时间
  string deleted_at = 4;
  // 名称
  // @required
  string name = 5 [(validate.rules).string = {
    min_len: 1,
    max_len: 255,
  }];

  // 状态
  // @required
  ReleaseTicketStatus status = 6;

  // workspace_id
  // @required
  uint64 workspace_id = 7;

  // workflow_id
  // @required
  uint64 workflow_id = 8;

  // control_planes
  // @required
  repeated shared.ControlPlane control_planes = 9 [(validate.rules).repeated = {
    max_items: 10,
    min_items: 1,
  }];

  // description
  // @required
  string description = 10;

  // boe_env_name
  string boe_env_name = 11;

  // ppe_env_name
  string ppe_env_name = 12;

  // 创建人
  // @required
  string creator = 13;

  // 发布负责人列表
  // @required
  repeated string release_approvers = 14 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];

  // 测试负责人列表
  // @required
  repeated string test_approvers = 15 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 0,
  }];

  // 关联的集成区 ID
  uint64 integration_id = 17;

  // 集成分支
  string integration_branch = 18;

  // 当前所处阶段
  string current_stage = 19;

  // 开始发布时间
  string released_at = 20;

  // 发布单回滚状态
  // optional
  RollbackStatus rollback_status = 21;

  // 是否 hotfix 发布单
  bool is_hotfix = 22;

  // 当前所处阶段
  string current_stage_i18n = 23;

  // 完成发布时间
  string end_release_time = 24;

  // 研发流程id
  uint64 team_flow_id = 25;

  // 研发流程类型
  shared.TeamFlowType team_flow_type = 26;

  // 研发流程名
  string team_flow_name = 27;

  // 研发流程英文名
  string team_flow_name_i18n = 28;

  // 通过发布单项目计算出来的，是个缓存
  // @required
  repeated shared.ControlPlane control_planes_from_cd_projects = 29 [(validate.rules).repeated = {
    max_items: 10,
    min_items: 0,
  }];

  // 发布分支
  repeated string release_branch = 30;

  repeated ProjectControlPlaneInfo project_info = 31;

  map<string,workflow.Env> env = 32;

  repeated WorkItem work_items = 33;

  // 发布单强制完成状态
  ForceCompleteStatus force_complete_status = 41;
}

message GetReleaseTicketOrchestrationReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketOrchestrationResp {
  // 编排信息+状态信息
  // protolint:disable:next REPEATED_FIELD_NAMES_PLURALIZED
  repeated release_ticket.Stage orchestration = 1;
}

message UpdateReleaseTicketEnvReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 阶段环境配置。Key 为 `Stage.StageId`
  // @required
  map<uint64, workflow.Env> stage_envs_map = 2;

  // 透传给环境平台的参数
  // @optional
  map<string, string> env_project_data = 3;
}

message UpdateReleaseTicketEnvResp {}

message GetTCEClusterConfigReq {
  // 控制面
  // @required
  shared.ControlPlane control_plane = 1 [(api.query) = "controlPlane"];
  // 项目唯一 ID
  // @required
  string project_unique_id = 2 [(api.query) = "projectUniqueId"];
}

message GetTCEClusterConfigResp {
  repeated env.TCEClusterConfig clusters = 1;
}

message DeleteProjectClusterReq {
  uint64 stage_id = 1;
  string project_unique_id = 2;
  shared.ProjectType project_type = 3;
  shared.ControlPlane control_plane = 4;
  string operator = 5;
  workflow.Lane will_delete_lane = 6; // 要删除的集群对应所在的泳道
  env.TCEClusterConfig will_delete_cluster = 7; // 仅使用 name/zone/virtualCluster 字段
}

message DeleteProjectClusterResp {}

message UpdateReleaseTicketMRStateReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 查询MR信息
  // @required
  repeated MergeRequest mr_requests = 2;
}

message MergeRequest {
  // 仓库id
  uint64 project_id = 1;
  // 源分支
  string source_branch = 2;
  // 目标分支
  string target_branch = 3;
  // 仓库全称
  string git_repo_name = 4;
  // MR author, 因为获取不到实际合入人
  string author = 5;
  // mr url
  string mr_url = 6;
}

message UpdateReleaseTicketMRStateResp {
  // 是否有更新
  bool has_updated = 1;
}

message GetReleaseTicketChangeItemDependencyReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketChangeItemDependencyResp {
  // 依赖信息
  ReleaseTicketDependency dependency = 1;
}

message SaveReleaseTicketChangeItemDependencyReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];

  repeated change_item.Dependency dependencies = 2;

  // 是否同步存储到空间中
  bool is_sync_workspace = 3;

  string username = 4;
}

message SaveReleaseTicketChangeItemDependencyResp {
  bool is_sync_success = 1;
}

message CanUpdateStageProjectDeployDetailReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // StageID
  // @required
  uint64 stage_id = 2 [(api.query) = "stageId", (validate.rules).uint64 = {gt: 0}];
  // 控制面
  // @required
  shared.ControlPlane control_plane = 3 [(api.query) = "controlPlane"];
  // 项目唯一 ID
  // @required
  string project_unique_id = 4 [(api.query) = "projectUniqueId"];
  // 项目类型
  // @required
  shared.ProjectType project_type = 5 [(api.query) = "projectType"];
}

message CanUpdateStageProjectDeployDetailResp {
  bool can_update = 1;
}

message GetChangeItemDeployStrategyReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 控制面
  // @required
  shared.ControlPlane control_plane = 3 [(api.query) = "controlPlane"];
  // 项目唯一 ID
  // @required
  string project_unique_id = 4 [(api.query) = "projectUniqueId"];
  // 项目类型
  // @required
  shared.ProjectType project_type = 5 [(api.query) = "projectType"];
  // @required
  string jwt = 101;
}

// Next id: 12
message GetChangeItemDeployStrategyResp {
  repeated change_item.SCMArtifact tce_base_scm_artifacts = 1;
  change_item.ClusterInfo cluster_info = 2;
  // 部署信息已审核通过的审核人列表
  repeated string reviewers = 3;
  // scm 配置，后续替代 tce_base_scm_artifacts，现在未使用
  repeated change_item.SCMArtifact scm_artifacts = 4;
  // 部署配置，后续替代 cluster_info
  oneof strategy {
    change_item.ClusterInfo tce_strategy = 5;
    paas.WebDeployStrategy web_strategy = 6;
    paas.GeckoDeployStrategy gecko_strategy = 9;
    paas.FaasDeployStrategy faas_strategy = 11;
    paas.CustomDeployStrategy custom_strategy = 12;
    paas.TccDeployStrategy tcc_strategy = 13;
    paas.CronJobDeployStrategy cronjob_strategy = 14;
  }
  // cluster 所属的 zones 信息，用于前端展示
  repeated string zones = 7;
  // 主仓信息部署资源/部署产物/部署内容
  change_item.ChangeItemArtifact deploy_resource = 8;
  // 升级模式
  paas.UpgradeMode upgrade_mode = 10;
}

message ChangeItemDeployStrategyReqInfo {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1;
  // 控制面
  // @required
  shared.ControlPlane control_plane = 2;
  // 项目唯一 ID
  // @required
  string project_unique_id = 3;
  // 项目类型
  // @required
  shared.ProjectType project_type = 4;
}

// Next id: 16
message ChangeItemDeployStrategyRespInfo {
  repeated change_item.SCMArtifact tce_base_scm_artifacts = 1;
  change_item.ClusterInfo cluster_info = 2;
  // 部署信息已审核通过的审核人列表
  repeated string reviewers = 3;
  shared.ControlPlane control_plane = 4;
  string project_unique_id = 5;
  shared.ProjectType project_type = 6;
  change_item.ChangeItemDeployStrategy deploy_strategy = 7 [deprecated = true];
  // scm 配置，后续替代 tce_base_scm_artifacts
  repeated change_item.SCMArtifact scm_artifacts = 8;
  // 部署配置，后续替代 cluster_info
  oneof strategy {
    change_item.ClusterInfo tce_strategy = 9;
    paas.WebDeployStrategy web_strategy = 10;
    paas.GeckoDeployStrategy gecko_strategy = 12;
    paas.FaasDeployStrategy faas_strategy = 14;
    paas.CustomDeployStrategy custom_strategy = 16;
    paas.TccDeployStrategy tcc_strategy = 17;
    paas.CronJobDeployStrategy cronjob_strategy = 19;
  }
  // 主仓信息部署资源/部署产物/部署内容
  change_item.ChangeItemArtifact deploy_resource = 11;
  // 升级模式
  paas.UpgradeMode upgrade_mode = 13;
  // 模板信息
  TemplateMeta template_meta = 15;
}

message GetMultiChangeItemsDeployStrategiesReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // @required
  repeated ChangeItemDeployStrategyReqInfo change_item_infos = 2;
  // @required
  string jwt = 101;
}

message GetMultiChangeItemsDeployStrategiesResp {
  repeated ChangeItemDeployStrategyRespInfo deploy_strategy_infos = 1;
}

// Next id: 16
message UpdateChangeItemDeployStrategyReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 控制面
  // @required
  shared.ControlPlane control_plane = 3 [(api.query) = "controlPlane"];
  // 项目唯一 ID
  // @required
  string project_unique_id = 4 [(api.query) = "projectUniqueId"];
  // 项目类型
  // @required
  shared.ProjectType project_type = 5 [(api.query) = "projectType"];

  // TCE 依赖仓版本信息
  repeated change_item.SCMArtifact tce_base_scm_artifacts = 6;

  // TCE 集群部署信息
  change_item.ClusterInfo cluster_info = 7;

  // 部署信息已审核通过的审核人
  string username = 8;
  // 废弃字段
  change_item.ChangeItemDeployStrategy deploy_strategy = 9 [deprecated = true];
  // scm 配置，后续替代 tce_base_scm_artifacts
  repeated change_item.SCMArtifact scm_artifacts = 10;
  shared.ScmMergeBuildInfo scm_merge_build_info = 19;

  oneof strategy {
    // 部署配置，后续替代 cluster_info
    change_item.ClusterInfo tce_strategy = 11;
    paas.WebDeployStrategy web_strategy = 12;
    paas.GeckoDeployStrategy gecko_strategy = 13;
    paas.FaasDeployStrategy faas_strategy = 15;
    paas.CustomDeployStrategy custom_strategy = 17;
    paas.TccDeployStrategy tcc_strategy = 18;
    paas.CronJobDeployStrategy cronjob_strategy = 20;
  }
  // 升级模式
  paas.UpgradeMode upgrade_mode = 14;
  // 手动触发更新部署参数
  UpdateChangeItemDeployStrategyTriggerType trigger_type = 16;
}

message UpdateChangeItemDeployStrategyRes {
  repeated change_item.SCMArtifact tce_base_scm_artifacts = 1;
  change_item.ClusterInfo cluster_info = 2;
  // scm 配置，后续替代 tce_base_scm_artifacts
  repeated change_item.SCMArtifact scm_artifacts = 3;
  oneof strategy {
    // 部署配置，后续替代 cluster_info
    change_item.ClusterInfo tce_strategy = 4;
    paas.WebDeployStrategy web_strategy = 5;
    paas.GeckoDeployStrategy gecko_strategy = 6;
    paas.FaasDeployStrategy faas_strategy = 7;
    paas.CustomDeployStrategy custom_strategy = 8;
    paas.TccDeployStrategy tcc_strategy = 9;
    paas.CronJobDeployStrategy cronjob_strategy = 11;
  }
  shared.ScmMergeBuildInfo scm_merge_build_info = 10;
}

message GetClusterInfoReq {
  string psm = 1 [(api.query) = "psm"];
  string app_env = 2 [(api.query) = "appEnv"];
  string idc = 3 [(api.query) = "idc"];
  string tce_env = 4 [(api.query) = "tceEnv"];
}

message GetClusterInfoResp {
  repeated paas.ClusterInfo cluster_infos = 1;
}

message GetUnlockableProjectListReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

// 建议使用gitlab.GitInfo
message GitInfo {
  string git_repo_name = 1;
  int64 git_repo_id = 2;
}

message UnlockableProjectItem {
  // 项目唯一 ID
  // @required
  string project_unique_id = 1;
  // 项目类型
  // @required
  shared.ProjectType project_type = 2;
  // 项目名称
  // @required
  string project_name = 3;
  // 当前进行中阶段工作分支
  // @required
  string work_branch = 4;
  // 控制面
  // @required
  repeated shared.ControlPlane control_planes = 5;
  // @optional
  GitInfo git_info = 6;
  // 是否可解锁
  // @required
  bool unlockable = 7;
  // 不可解锁原因
  // @required
  ProjectIntegrationLockedType locked_reason = 8;
}

message GetUnlockableProjectListResp {
  // @required
  repeated UnlockableProjectItem projects = 1;
}

message ListReleaseTicketEnvProjectConfigsReq {
  // 发布单 ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}
message ListReleaseTicketEnvProjectConfigsResp {
  repeated env.EnvProjectConfig project_configs = 1;
}

message UpdateReleaseTicketEnvProjectConfigsReq {
  // 发布单 ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];

  // 项目配置
  // @required
  repeated env.EnvProjectConfig project_configs = 2;

  // 透传给环境平台的参数
  // @optional
  map<string, string> env_project_data = 3;
}
message UpdateReleaseTicketEnvProjectConfigsResp {}

message GetWebDeployUnitReq {
  string project_unique_id = 1 [(api.query) = "projectUniqueID"];
  shared.ControlPlane control_plane = 2 [(api.query) = "controlPlane"];
  string ttp_web_name = 3 [(api.query) = "ttpWebName"];
}

message GetWebDeployUnitRsp {
  repeated paas.DeployUnit deploy_units = 1;
}

message GetWebChannelsReq {
  // 废弃字段
  uint64 deploy_unit_id = 1;
  paas.ChannelType channel_type = 2 [(api.query) = "channelType"];
  // goofy deploy region
  // @required
  paas.GoofyDeployRegion goofy_deploy_region = 3 [(api.query) = "goofyDeployRegion"];
  // @required
  string project_unique_id = 4 [(api.query) = "projectUniqueID"];
  // @optional 废弃字段，这个只用于兼容 bc ttp
  // 后续 appId + regionCode 就可以直接查到对应区域的 channels 列表，跟发布单业务逻辑无关
  string ttp_web_name = 5 [(api.query) = "ttpWebName", deprecated = true];
  // @optional
  string app_id = 6 [(api.query) = "appId"];
}

message GetWebChannelsRsp {
  repeated paas.ChannelBasicInfo channels = 1;
}

message GetWebRollbackProgressReq {
  // web 回滚工单 ids，根据系统变量生成
  // @required
  string rollback_deployment_ids = 1;
  // callback 需要显式传递 username
  string username = 2;
  // 是否 ttp
  bool is_ttp = 3;
}

message GetWebRollbackProgressRsp {
  // https://bytedance.sg.feishu.cn/docs/doccn0gq4IcElOwvvVjdUWjKOFb
  // status: success, running, fail
  string status = 1;

  string message = 2;

  string url = 3;

  repeated WebhookKV data = 4;
}

message GetWebRollbackDetailReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.query) = "releaseTicketId", (validate.rules).uint64 = {gt: 0}];
  // StageID
  // @required
  uint64 stage_id = 2 [(api.query) = "stageId", (validate.rules).uint64 = {gt: 0}];
  // 控制面
  // @required
  shared.ControlPlane control_plane = 3 [(api.query) = "controlPlane"];
  // 项目唯一 ID
  // @required
  string project_unique_id = 4 [(api.query) = "projectUniqueId"];
  // 项目类型
  // @required
  shared.ProjectType project_type = 5 [(api.query) = "projectType"];
}

message GetWebRollbackDetailRsp {
  repeated change_item.RegionTicketWeb region_tickets = 1;
}

message WebhookKV {
  // https://bytedance.sg.feishu.cn/docs/doccn0gq4IcElOwvvVjdUWjKOFb
  string field_name = 1;
  string value = 2;
}

message UnmergeableProjectItem {
  // 项目唯一 ID
  // @required
  string project_unique_id = 1;
  // 项目类型
  // @required
  shared.ProjectType project_type = 2;
  // 项目名称
  // @required
  string project_name = 3;
  // 控制面
  // @required
  shared.ControlPlane control_plane = 4;
  // 不可合并 bugfix 开发任务原因
  // @required
  ProjectBugfixDevTaskUnmergeableType unmergeable_reason = 5;
}

message GetUnmergeableProjectListReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetUnmergeableProjectListResp {
  // @required
  repeated UnmergeableProjectItem projects = 1;
}

// 用于开发任务关联发布单时获取发布单列表，需要返回当前空间所有发布单及关联开发任务模版的关系
message GetReleaseTicketListForDevTaskAssociateReq {
  // 空间 id
  // uint64 workspace_id = 1 [(api.query) = "workspaceId", (validate.rules).uint64 = { gt: 0 }];
  uint64 workspace_id = 1 [(api.query) = "workspaceId"];
  // 开发任务模板id, 存在获取某个开发任务模板下的发布单列表
  // @optional
  uint64 dev_task_workflow_id = 2 [(api.query) = "devTaskWorkflowId"];
  // 发布单关联任务类型
  // optional
  repeated workflow.NumOfAssociatedDevTaskType num_of_associated_dev_task_types = 3
  [(api.query) = "numOfAssociatedDevTaskTypes"];
  // 发布单名称
  // @optional
  string release_ticket_name = 4 [(api.query) = "releaseTicketName"];
  // 分页页码
  int64 page = 100 [(api.query) = "page", (validate.rules).int64 = {gt: 0}];
  // 分页大小
  int64 page_size = 101 [(api.query) = "pageSize", (validate.rules).int64 = {gt: 0}];
}

// 开发任务关联发布单时获取发布单列表V2
message GetReleaseTickectsByTeamFlowIdReq {
  // 研发流程 id
  // required
  uint64 team_flow_id = 1 [(api.path) = "teamFlowId"];
  // 空间 id
  // required
  uint64 workspace_id = 2 [(api.query) = "workspaceId"];
  // 开发任务模板 id
  // @optional
  uint64 dev_task_workflow_id = 3 [(api.query) = "devTaskWorkflowId"];
  // 发布单过滤规则
  // @optional
  AssociateRTFilterBy filter_by = 4 [(api.query) = "filterBy"];
  // 发布单id 精确匹配
  // @optional
  uint64 release_ticket_id = 5 [(api.query) = "releaseTicketId"];
  // 发布单name 模糊搜索
  // @optional
  string release_ticket_name = 6 [(api.query) = "releaseTicketName"];
  // 分页页码
  int64 page = 100 [(api.query) = "page", (validate.rules).int64 = {gt: 0}];
  // 分页大小
  int64 page_size = 101 [(api.query) = "pageSize", (validate.rules).int64 = {gt: 0}];
}

message GetReleaseTickectsByTeamFlowIdResp {
  // 发布单状态到发布单列表的映射，保存已关联某一特定开发任务模版的发布单
  map<uint64, ReleaseTicketListDetails> status_to_rt_list = 1;
  // 研发流程不一致的发布单列表
  repeated ReleaseTicketListItem incorrect_teamflow_release_tickets = 2;
  // 分页信息
  shared.PageInfo page_info = 100;
}

message ReleaseTicketListDetails {
  repeated ReleaseTicketListItem items = 1;
}

message GetReleaseTicketListForDevTaskAssociateResp {
  // 发布单状态到发布单列表的映射，保存已关联某一特定开发任务模版的发布单
  map<uint64, ReleaseTicketListDetails> status_to_rt_list = 1;
  // 未关联某一特定开发任务模版的发布单列表
  repeated ReleaseTicketListItem un_associate_specific_dev_task_workflow_items = 2;
  // 已关联开发任务的1:1发布单列表
  repeated ReleaseTicketListItem associate_dev_task_one_to_one_rt_lists = 3;
  // 分页信息
  shared.PageInfo page_info = 100;
}

message OperateReleaseTicketStatusReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];

  // 要流转到的发布单状态
  // @required
  ReleaseTicketStatus status = 2;
}

message OperateReleaseTicketStatusResp {
  // 是否操作成功
  // @required
  bool is_succeed = 1;
}

message GetNotificationInfoReq {
  // 空间ID
  // @required
  uint64 workspace_id = 1 [(api.body) = "workspaceId", (validate.rules).uint64 = {gt: 0}];

  // @required
  string notification_event_name = 2 [(api.body) = "notificationEventName"];

  // @required
  string event_key = 3 [(api.body) = "eventKey"];

  // @required
  string event_body = 4 [(api.body) = "eventBody"];
}

message GetNotificationInfoResp {
  // 发送lark消息所需的参数
  map<string, string> params = 1;

  // 发送消息的对象
  repeated string usernames = 2;

  // 发送消息的群
  repeated string chat_ids = 3;
}

message AuditProjectItem {
  // 控制面
  // @required
  shared.ControlPlane control_plane = 1;
  // 项目唯一 ID
  // @required
  string project_unique_id = 2;
  // 项目类型
  // @required
  shared.ProjectType project_type = 3;
  // git 信息
  // @required
  AuditProjectGitInfo git_info = 4;
}

message AuditProjectGitInfo {
  // git 仓库名称
  // @required
  string git_repo_name = 1;
  // 分支名称
  // @required
  string branch_name = 2;
}

message GetActiveReleaseTicketByProjectsReq {
  // 待检测项目列表
  // @required
  repeated AuditProjectItem audit_project_items = 1;
}

message ActiveReleaseTicketItem {
  // 发布单 id
  // @required
  uint64 release_ticket_id = 1;
  // 发布单状态
  // @required
  ReleaseTicketStatus status = 2;
  // 发布单名称
  // @required
  string name = 3;
  // 控制面
  // @required
  shared.ControlPlane control_plane = 4;
  // 项目唯一 ID
  // @required
  string project_unique_id = 5;
  // 项目类型
  // @required
  shared.ProjectType project_type = 6;
  // 发布负责人列表
  // @required
  repeated string release_approvers = 7 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];
  // 测试负责人列表
  // @required
  repeated string test_approvers = 8 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 0,
  }];
  // 项目名称
  // @required
  string project_name = 9;
  // 空间 id
  // @required
  uint64 workspace_id = 10;
}

message GetActiveReleaseTicketByProjectsResp {
  // 活跃的发布单列表
  // @required
  repeated ActiveReleaseTicketItem active_release_tickets = 1;
}

message GetReleaseTicketBasicInfoByIDsReq {
  // 发布单 id 列表
  // @required
  repeated uint64 release_ticket_ids = 1 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];
}

message ReleaseTicketsBasicInfoItem {
  // 发布单 id
  // @required
  uint64 release_ticket_id = 1;
  // 发布单状态
  // @required
  ReleaseTicketStatus status = 2;
  // 发布单名称
  // @required
  string name = 3;
  // 发布单基本信息
  // 发布负责人列表
  // @required
  repeated string release_approvers = 7 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];
  // 测试负责人列表
  // @required
  repeated string test_approvers = 8 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 0,
  }];
  // 所属空间的 id
  // @required
  uint64 workspace_id = 9;

  // 控制面
  repeated shared.ControlPlane control_planes = 10;
  // 是否是分批次
  bool is_incremental_delivery = 11;
  // integrationId
  uint64 integration_id = 12;
  // 关联开发任务的类型
  workflow.NumOfAssociatedDevTaskType num_of_associated_dev_task_type = 14;
  // 回滚状态
  RollbackStatus rollback_status = 15;
  // team flow id
  uint64 team_flow_id = 16;
}

message GetReleaseTicketBasicInfoByIDsResp {
  // 发布单基本信息
  // @required
  repeated ReleaseTicketsBasicInfoItem release_ticket_items = 1;
}

message CheckIgnoreControlPlaneDiffReq {
  // 发布单 id
  // @required
  uint64 release_ticket_id = 1 [(api.query) = "releaseTicketId"];
}
message CheckIgnoreControlPlaneDiffResp {
  // 是否忽略
  bool ignore = 1;
}

message UpdateIgnoreControlPlaneDiffReq {
  // 发布单 id
  // @required
  uint64 release_ticket_id = 1;
  // 是否忽略
  bool ignore = 2;
}
message UpdateIgnoreControlPlaneDiffResp {}

message ReleaseTicketChangeItemKey {
  // 控制面
  shared.ControlPlane control_plane = 1;
  // 项目唯一 ID
  string project_unique_id = 2;
  // 项目类型
  shared.ProjectType project_type = 3;
  // 仓库名
  string git_repo_name = 4;
  // 兜底分支,一般是归档分支
  string branch = 5;
}

message BranchCheckoutRef {
  // 对应的项目
  ReleaseTicketChangeItemKey change_item = 1;
  // 从哪里 checkout 出来
  string checkout_ref = 2;
  // 从哪里 checkout 出来的分支名，仅用于调试
  string checkout_branch = 3;
}

message GetIntegrationBranchCheckoutRefInHotfixReq {
  uint64 release_ticket_id = 1;
  // 要查询的项目 hotfix commit
  // @deprecated 废弃不用
  repeated ReleaseTicketChangeItemKey change_items = 2 [deprecated = true];
  // []*dev.DevChangeDeployConfig 序列化
  string project_change_deploy_configs = 3;
}

message GetIntegrationBranchCheckoutRefInHotfixRes {
  bool is_hotfix = 1;
  repeated BranchCheckoutRef integration_branch_checkout_refs = 2;
  // 增加框架 resp
  shared.BaseResp base_resp = 255;
}

message GetOneRepoIntegrationBranchCheckoutRefInHotfixReq {
  uint64 release_ticket_id = 1;
  // 仓库名
  string git_repo_name = 4;
  // 要查询的项目 hotfix commit
  repeated ReleaseTicketChangeItemKey change_items = 2;
}

message GetOneRepoIntegrationBranchCheckoutRefInHotfixRes {
  // 是否是 hotfix 发布单
  bool is_hotfix = 1;
  // 发布单 id
  uint64 release_ticket_id = 2;
  // 仓库名
  string git_repo_name = 3;
  // 当前仓库在当前发布单下，集成分支 checkout 的 commit
  string integration_branch_checkout_ref = 4;
}

message GetReleaseTicketOpenApiReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketOpenApiResp {
  // 上线单信息
  ReleaseTicketDetailOpenApi release_ticket = 1;
}

message ReleaseTicketDetailOpenApi {
  // 上线单 ID
  uint64 release_ticket_id = 1;
  // 创建时间
  string created_at = 2;
  // 更新时间
  string updated_at = 3;
  // 删除时间
  string deleted_at = 4;
  // 名称
  // @required
  string name = 5 [(validate.rules).string = {
    min_len: 1,
    max_len: 255,
  }];

  // 状态
  // @required
  ReleaseTicketStatus status = 6;

  // workspace_id
  // @required
  uint64 workspace_id = 7;

  // control_planes
  // @required
  repeated shared.ControlPlane control_planes = 13 [(validate.rules).repeated = {
    max_items: 10,
    min_items: 1,
  }];

  // description
  // @required
  string description = 14;

  // boe_env_name
  string boe_env_name = 15;

  // ppe_env_name
  string ppe_env_name = 16;

  // 创建人
  // @required
  string creator = 17;

  // 发布负责人列表
  // @required
  repeated string release_approvers = 18 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 1,
  }];

  // 发布单绑定的变更项
  repeated ReleaseTicketOpenApiChangeItem change_items = 19;

  // 测试负责人列表
  // @required
  repeated string test_approvers = 24 [(validate.rules).repeated = {
    max_items: 100,
    min_items: 0,
  }];

  // mrlist
  repeated MRInfo mr_lists = 25;
}

message MRInfo {
  uint64 mr_id = 1;
  string repo_name = 2;
  string title = 3;
  string source_branch = 4;
  string target_branch = 5;
  string status = 6;
  string mr_url = 7;
  repeated string project_names = 8;
  string creator = 9;
  uint64 repo_id = 10;
  int64 create_time = 11;
}

message TemplateMeta {
  // 流程模版 ID
  uint64 workflow_id = 1;
  // 阶段 ID
  uint64 node_id = 2;
  // 模版类型
  workflow.WorkflowType workflow_type = 3;
  // 模版 ID
  uint64 template_id = 4;
}

message UpdateReleaseTicketTeamFlowReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "releaseTicketId", (validate.rules).uint64 = {gt: 0}];
  // 研发流程 ID
  //  @required
  uint64 team_flow_id = 2;
}

message UpdateReleaseTicketTeamFlowResp {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1;
  // 研发流程 ID
  //  @required
  uint64 team_flow_id = 2;
}

message GetReleaseTicketTeamFlowReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "releaseTicketId", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketTeamFlowResp {
  // 发布单ID
  uint64 release_ticket_id = 1;
  // team flow detail
  team_flow_config.TeamFlowConfig team_flow_config = 2;
}

message RTArtifactHealthCheckItem {
  // 项目
  ReleaseTicketChangeItem change_item = 1;
  // 发布清单情况
  ChangeItemHealthCode health_code = 2;
}

// 获取发布单发布清单从集成区的同步进度
message GetRTArtifactHealthCheckReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "releaseTicketId", (validate.rules).uint64 = {gt: 0}];
}

message GetRTArtifactHealthCheckResp {
  repeated RTArtifactHealthCheckItem change_items = 1;
}

message GetDeployOverviewBasicInfoReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetDeployOverviewBasicInfoResp {
  repeated project.ProjectBasicInfoItem deploy_overviews = 1;
}

message GetScmVersionDetailReq {
  // scm id
  // @required
  uint64 scm_id = 1 [(api.query) = "scmId"];
  // scm version
  // @required
  string scm_version = 2 [(api.query) = "scmVersion"];
  // 控制面
  // @required
  shared.ControlPlane control_plane = 3 [(api.query) = "controlPlane"];
}

message GetScmVersionDetailResp {
  paas.ScmVersion version_raw = 1;
}

message UpdateReleaseTicketControlPlaneReq {
  // 发布单id
  uint64 release_ticket_id = 1;
  // 新增的控制面
  repeated shared.ControlPlane control_planes = 2;
}

message UpdateReleaseTicketControlPlaneResp {}

message InitTccEnvReq {
  // 环境
  workflow.Env env = 1;
  // 选择的服务，目前只会将tcc服务部署在环境中
  repeated ProjectParams selected_projects = 2;
  // 发布单 id
  uint64 release_ticket_id = 3 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 控制面
  shared.ControlPlane control_plane = 4;
}

message InitTccEnvResp {}

message SyncTccVersionToTargetReq {
  // 环境
  workflow.Env env = 1;
  // 选择运行的项目列表
  repeated ProjectParams selected_projects = 2;
  // 发布单 id
  uint64 release_ticket_id = 3 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 控制面
  shared.ControlPlane control_plane = 4;
}

message SyncTccVersionToTargetResp {}

message CheckStoragePsmVersionReq {
  // 发布单id
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // psm list, 不传查全量
  repeated string psms = 2;
}

message CheckStoragePsmVersionResp {
  bool pass = 1;
  repeated CheckStoragePsmVersionResult results = 2;
}

message CheckStoragePsmVersionResult {
  // psm
  string psm = 1;
  // 是否落后
  bool is_backward = 2;
}

// 批量查询的 changeItem 最近发布成功的发布单和发布commit
message GetChangeItemsLatestReleasedInfoReq {
  repeated QueryChangeItem change_items = 1;
}

message QueryChangeItem {
  // 项目类型
  shared.ProjectType project_type = 1;
  // 项目唯一ID(psm或项目ID)
  string project_unique_id = 2;
  // 锁定项目控制面
  shared.ControlPlane control_plane = 3;
  // 项目名称
  string projectName = 4;
}


message ChangeItemActiveWorkBranchQuery {
  // 项目类型
  shared.ProjectType project_type = 1;
  // 项目唯一ID(psm或项目ID)
  string project_unique_id = 2;
  // 锁定项目控制面
  shared.ControlPlane control_plane = 3;
  // 项目的git仓库id
  repeated string repo_names = 4;
}


message GetChangeItemsLatestReleasedInfoResp {
  // protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
  repeated ChangeItemLatestReleasedInfo latest_released_info = 1;
}

message ChangeItemLatestReleasedInfo {
  // 项目类型
  shared.ProjectType project_type = 1;
  // 项目唯一ID(psm或项目ID)
  string project_unique_id = 2;
  // 锁定项目控制面
  shared.ControlPlane control_plane = 3;
  // 最近发布单 LatestReleasedRt
  release_ticket.ReleaseTicketDetail latest_released_rt = 4;
  // 发布成功 的 commit
  git.Commit released_commit = 5;
}

message GetReleaseTicketMultiChangeItemsScmArtifactsReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // @required
  repeated ChangeItemScmArtifactReqInfo change_item_infos = 2;
}

message GetReleaseTicketMultiChangeItemsScmArtifactsResp {
  uint64 release_ticket_id = 1;
  // 当前线上版本
  repeated ChangeItemScmArtifacts changeitem_artifacts = 2;
}

message GetReleaseTicketArtifactListReq {
  // ReleaseTicketId
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

message GetReleaseTicketArtifactListResp {
  uint64 release_ticket_id = 1;
  // 发布单所处阶段名称
  string current_stage_name = 2;
  // 发布单所处阶段ID
  uint64 current_stage_id = 3;
  // 发布对象信息列表
  repeated ChangeItemDeployObjectArtifacts change_item_artifacts = 4;
}

message GetChangeItemsActiveWorkBranchReq {
  // 发布单 id
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  repeated ChangeItemActiveWorkBranchQuery change_items = 2;
}

message GetChangeItemsActiveWorkBranchResp {
  repeated ChangeItemActiveWorkBranch change_item_active_branch = 1;
}

message GetReleaseTicketDevTaskMigrationTaskReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // @required
  MigrationDevTaskType migration_type = 2 [(api.query) = "migrationType"];
}

// 重建发布单迁移开发任务结果
message GetReleaseTicketDevTaskMigrationTaskResp {
  // 所有任务详情（包括不展示的任务）
  repeated DevTaskMigrationResult dev_task_results = 1;
  // 所有任务是否在运行中（包括不展示的任务）
  bool is_running = 2;
  // 开始时间戳
  uint64 created_at = 3;
  // 结束时间戳
  uint64 end_at = 4;
}

message DevTaskMigrationResult {
  // 迁移结果ID
  uint64 migration_result_id = 1;
  // 原开发任务ID
  uint64 source_dev_task_id = 2;
  // 新开发任务ID
  uint64 target_dev_task_id = 3;
  // 任务名称
  string source_dev_task_name = 4;
  // 任务状态
  MigrationDevTaskStatus task_status = 5;
  // 错误码
  uint64 err_code = 6;
  // 错误信息
  string err_msg = 7;
}

message RetryReleaseTicketDevTaskMigrationTaskReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 迁移类型
  // @required
  MigrationDevTaskType migration_type = 2 [(api.query) = "migrationType"];
  // 迁移结果ID
  // @optional
  uint64 migration_result_id = 3 [(api.query) = "migrationResultId"];
}

message RetryReleaseTicketDevTaskMigrationTaskResp {}

message SkipReleaseTicketDevTaskMigrationTaskReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 迁移类型
  // @required
  MigrationDevTaskType migration_type = 2 [(api.query) = "migrationType"];
  // 迁移结果ID
  // @optional
  uint64 migration_result_id = 3 [(api.query) = "migrationResultId"];
}

message SkipReleaseTicketDevTaskMigrationTaskResp {}

message AdminBatchCancelReleaseTicketsRequest {
  // 空间 id
  uint64 workspace_id = 1 [(validate.rules).uint64 = {gt: 0}];
  // 发布单创建时间
  string release_ticket_create_at = 2 [
    (validate.rules).string = {
      min_len: 1,
      max_len: 255,
    }
  ];
  // 分页信息
  shared.PageInfo page_info = 100;
}

message AdminBatchCancelReleaseTicketsResponse {
  int32 total = 1;
}
message TimeRange {
  int64 from = 1;
  int64  to = 2;
}

message Filter {
  repeated ReleaseTicketStatus status = 1; // 状态
  StageType stage = 2; // 阶段，当前发布单不处于已完成状态的第一个阶段
  repeated  shared.ControlPlane control_planes = 3; // 控制面
  repeated SearchProjectParam search_projects = 4; // 项目
  repeated string release_branch = 5; //发布分支
  repeated string release_owner = 6; //发布负责人
  repeated string qa_owner = 7; // 质量负责人
  repeated string project_owner = 8; // 项目负责人
  string author = 9; // 创建人
  TimeRange release_time = 10; // 开始发布时间
  TimeRange finish_time = 11; // 完成发布时间
  repeated int64 teamflow_id = 12; // teamflow id
  repeated int64 workflow_id = 13; // 流程id
  bool release_owner_or = 14; // 发布负责人取并集
  bool qa_owner_or = 15; // 质量负责人取并集
  bool project_owner_or = 16; // 项目负责人取并集
  TimeRange created_at = 17;
  repeated RollbackStatus rollback_status = 18;
  repeated string about_users = 19;
  repeated shared.ProjectType project_types = 20; // 按照项目类型搜索
}

message SortOption {
  enum SortField {
    none = 0; // 没有排序，默认创建时间
    status = 1; //状态
    create_at = 2;  // 创建时间
    release_time = 3; // 开始发布时间
    finish_time = 4; // 完成时间
  }
  enum SortOrder {
    DESC = 0;
    ASC = 1;
  }
  SortField field = 1;
  SortOrder order = 2;
}

message View {
  uint64 id = 1;
  string name = 2;
  // @optional
  Filter filter = 3;
  // @optional
  SortOption sort = 4;
  // @optional
  string creator = 5;
  // @optional
  int64 create_at = 6;
  // @optional
  int64 update_at = 7;
  // 分享给用户
  // @optional
  repeated string shared_to = 8;
  // 协作者
  // @optional
  repeated string collaborator = 9;
}

message ListViewReq {
  string username = 1 [(api.query) = "username"];
  uint64 space_id = 2 [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
}

message ListViewResp {
  repeated View public_views = 1;
  repeated View user_views = 2;
  repeated View shared_views = 3; // 分享给我的view
  repeated View collaborator_views = 4; // 作为协作者的View
}

enum ViewType {
  User = 0;
  Public = 1;
}

message SaveViewReq {
  View view = 1; // 如果id = 0则为新建，需要传下面的参数，否则则为update
  ViewType type = 2;
  uint64 workspace_id = 3 [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
  string username = 4;
}

message SaveViewResp {
  uint64 view_id = 1;
}

message DeleteViewReq{
  uint64 viewId = 1;
  // @optional
  ViewType type = 2;
  uint64 workspace_id = 3 [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
  string username = 4;
}

message SearchOpt {
  string name = 1;
  repeated uint64 meego_ids = 2;
}

message GetViewUserConfigReq{
  uint64 workspace_id = 1 [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
  string username = 2 [(api.query) = "username"];
}

message UpdateViewUserConfigReq{
  uint64 workspace_id = 1 [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
  string username = 2;
  repeated ViewOrder order = 3;
}

message ViewOrder{
  uint64 view_id = 1;
  bool visible = 2;
}

message GetViewUserConfigResp{
  repeated ViewOrder order = 1;
}

message GetReleaseTicketByViewReq{
  View view = 1;
  SearchOpt search = 2;
  string username = 3;
  uint64 workspace_id = 4  [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
  uint32 pageSize = 5;
  uint32 page = 6;
  bool debug = 255; // debug 用，前端不用管
}

message GetViewDetailReq{
  uint64 workspace_id = 1 [(api.path) = "workspaceId", (validate.rules).uint64 = {gt: 0}];
  uint64 view_id = 2 [(api.path) = "viewId", (validate.rules).uint64 = {gt: 0}];
}

message GetViewDetailResp{
  View detail = 1;
}

message GetReleaseTicketInitTaskOverviewReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
}

// 发布单初始化任务概览
message GetReleaseTicketInitTaskOverviewResp {
  // 所有任务详情
  repeated InitTaskOverview init_task_overview = 1;
  // 开始时间
  uint64 create_at = 2;
  // 结束时间
  uint64 end_at = 3;
}

// 初始化任务概览
message InitTaskOverview {
  // 任务类型
  InitTaskType init_task_type = 1;
  // 任务是否在运行中
  bool is_running = 2;
  // 任务是否成功
  bool is_success = 3;
}

message AdminCheckReleaseTicketFunctionalityReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.query) = "releaseTicketId", (validate.rules).uint64 = {gt: 0}];
  repeated string filter = 2; // key1, key2
}

message AdminCheckReleaseTicketFunctionalityRespItem {
  string key = 1;
  bool is_consistent = 2;
}

message AdminCheckReleaseTicketFunctionalityResp {
  repeated AdminCheckReleaseTicketFunctionalityRespItem result = 1;
}


message GetReleaseTicketMigrationTaskAutoMergeResultReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 查询用户 用于区分是否忽略失败
  // @required
  string username = 2[(api.query) = "username"];
}

message GetReleaseTicketMigrationTaskAutoMergeResultResp {
  // 强制合入结果
  repeated MigrationTaskAutoMergeResult  results = 1;
  // 忽略失败
  bool ignore_failed = 2;
}

message MigrationTaskAutoMergeResult {
  // 开发任务ID
  uint64  dev_task_id = 1;
  // 是否成功
  bool is_success = 2;
}

message MarkAsReadReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = {gt: 0}];
  // 消息类型
  // @required
  MessageType message_type = 2 [(api.body) = "messageType"];
  // @required 操作人
  string operator = 3 [(api.body) = "operator"];
}

message MarkAsReadResp {
}

message CanForceCompleteReleaseTicketReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = { gt: 0 }];
}

message CanForceCompleteReleaseTicketResp {
  bool can_force_complete = 1;
}

message RemoveRtControlPlaneReq {
  // 发布单ID
  // @required
  uint64 release_ticket_id = 1 [(api.path) = "Id", (validate.rules).uint64 = { gt: 0 }];
  // 发布单列表
  repeated shared.ControlPlane control_planes = 2;
}

message RemoveRtControlPlaneResp {}

message GetReleaseTicketIDByStageIDReq {
  uint64 stage_id = 1;
}

message GetReleaseTicketIDByStageIDResp {
  uint64 release_ticket_id = 1;
}


message ProjectPipelineRerunCheckReq{
  int64  release_ticket_id = 1 [(api.path) = "Id", (validate.rules).int64 = { gt: 0 }];
  int64  stage_id = 2 [(api.path) = "StageId", (validate.rules).int64 = { gt: 0 }];
  shared.ControlPlane control_plane = 3 [(api.query) = "controlPlane"];
}

message ProjectPipelineRerunCheckResp{
  repeated ProjectPipelineRerunCheckItem project = 1;
}

message ProjectPipelineRerunCheckItem{
  shared.ProjectType project_type =1;
  string project_unique_id = 2;
  bool can_run = 3;
  repeated ProjectPipelineRerunCheckNotRunnableReason reason = 4;
  release_ticket.DeploymentMutexInfo mutexInfo = 5; // 部署锁信息，如果有值说明部署锁没通过
  FailReasonType stage_pre_check_fail_reason = 6; // 多批次使用，阶段前置检查失败原因
  shared.ProjectDeployStatus project_deploy_status = 7; // 项目发布状态
}

enum ProjectPipelineRerunCheckNotRunnableReason{
  Unknown = 0;
  PROJECT_FINISHED = 1; // 本项目已部署完成，无法重新运行
  PROJECT_ROLLBACKING = 2; //  本项目正在回滚中,无法重新运行,请等待回滚完成或取消回滚后再操作
  PROJECT_RUNNING = 3; // 项目正在部署中，请取消项目流水线后，再选择本项目。注意，取消项目流水线会同时取消对应的部署工单，请谨慎操作
  PROJECT_ROLLBACKED = 4; // 本项目已发起回滚，无法重新运行
  PROJECT_TCE_ROLLBACK = 5; // 该项目的 TCE 工单正在运行，无法选择
  PROJECT_CRONJOB_RUNNING = 6; // CronJob 工单正在部署中，无法重新运行
  PROJECT_CRONJOB_FINISHED = 7; // CronJob工单已完成部署，无法重新运行
  PROJECT_CRONJOB_ROLLBACKING = 8; // CronJob工单正在回滚中，无法重新运行
  PROJECT_GATEKEEP_NOT_PASS = 9; // 发布准入检测未通过，无法部署更新后的项目，请等待检测项通过
}

message UpdateChangeItemsReq {
  uint64 release_ticket_id = 1;
  repeated ReleaseTicketChangeItem items = 2;
}

message UpdateChangeItemsResp {
  repeated ReleaseTicketChangeItem failed_items = 1;
}