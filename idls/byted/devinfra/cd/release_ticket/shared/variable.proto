syntax = "proto3";

package byted.devinfra.cd.release_ticket;

import "idls/byted/api/api.proto";
import "validate/validate.proto";
import "idls/byted/bc/varstore/variable.proto";
import "idls/byted/bc/varstore/shared.proto";
import "idls/byted/bc/varstore/group.proto";
import "idls/byted/devinfra/cd/shared/shared.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb";

message Variable {
  // 变量定义
  // @required
  bc.varstore.VarDefinition definition = 1;

  // 变量值,当用户没有输入时与定义中的默认值相同
  // @required
  bc.varstore.VarValue value = 2;

  // 关联对象
  // @required
  repeated AssociationItem association_items = 3;

  // 变量组id
  // @required
  uint64 group_id = 4;

  // 变量组version
  // @required
  int64 version = 5;

  // 变量组名称, 用于项目变量展示来源
  // @optional
  bc.varstore.StringInMultiLang group_name = 6;

  // 变量组 scope，用于变量展示来源
  // @optional
  bc.varstore.CustomScope custom_scope = 7;
}

message AssociationItem {
  // 关联类型
  // @required
  AssociationItemType type = 1;

  // 关联信息
  // @required
  AssociationItemInfo detail = 2;
}

enum AssociationItemType {
  ASSOCIATION_ITEM_TYPE_UNSPECIFIED = 0;

  // 项目
  ASSOCIATION_ITEM_TYPE_PROJECT = 1;

  // 发布单
  ASSOCIATION_ITEM_TYPE_RELEASE_TICKET = 2;

  // 开发任务
  ASSOCIATION_ITEM_TYPE_DEV_TASK = 3;

  // 流水线
  ASSOCIATION_ITEM_TYPE_PIPELINE = 4;
}

message AssociationItemInfo {
  oneof info {
    AssociationItemPipeline pipeline = 1;
    AssociationItemProject project = 2;
    AssociationItemReleaseTicket release_ticket = 3;
    AssociationItemDevTask dev_task = 4;
  }
}

message AssociationItemPipeline {
  // 流水线ID
  // @required
  uint64 pipeline_id = 1;

  // 流水线类型
  // @required
  shared.PipelineType pipeline_type = 2;

  // 项目唯一标识,当前为项目流水线是才赋值
  string project_unique_id = 3;

  // 项目类型,当前为项目流水线是才赋值
  shared.ProjectType project_type = 4;

  // 项目名称,当前为项目流水线是才赋值
  string project_name = 5;
}

message AssociationItemProject {
  // 项目唯一标识
  // @required
  string project_unique_id = 1;

  // 项目类型
  // @required
  shared.ProjectType project_type = 2;

  // 项目名称
  // @required
  string project_name = 3;
}

message AssociationItemReleaseTicket {
  // workflow id
  uint64 workflow_id = 1;
}

message AssociationItemDevTask {
  // workflow id
  uint64 workflow_id = 1;
}

message GetReleaseTicketVarsReq {
  // 空间ID
  uint64 workspace_id = 1 [(api.query) = "workspaceId", (validate.rules).uint64 = { gt: 0 }];

  // workflow id
  // 在发布单没有实例化时,通过workflow查询
  uint64 workflow_id = 2 [(api.query) = "workflowId"];

  // 发布单ID
  // 与 "workflow_id" 互斥
  uint64 release_ticket_id = 3 [(api.query) = "releaseTicketId"];

  // 是否包含系统变量
  bool include_sys_vars = 4 [(api.query) = "includeSysVars"];
}

message GetReleaseTicketVarsResp {
  // 运行前可以修改的变量
  repeated Variable runtime_vars = 1;

  // 运行前不可修改的变量
  repeated Variable immutable_vars = 2;

  // 系统变量, 仅在 include_sys_vars = true 时返回
  repeated Variable system_vars = 3;
}

message GetStagePipelineRunVarsReq {
  // Stage ID
  uint64 stage_id = 1;

  // 发布单 ID
  uint64 release_ticket_id = 2;

  // 用户所选控制面
  shared.ControlPlane selected_control_plane = 3;

  // 用户选中的项目
  repeated shared.ProjectUniqueKey selected_projects = 4;
}

message GetStagePipelineRunVarsResp {
  // 运行前可以修改的变量
  repeated Variable runtime_vars = 1;

  // 运行前不可修改的变量
  repeated Variable immutable_vars = 2;
}

message GetAvailableVarsReq {
  // @required
  // 关联的空间id
  uint64 space_id = 1 [(api.query) = "spaceId"];
  // 关联流水线ID
  uint64 pipeline_id = 2 [(api.query) = "pipelineId"];
  // 关联模版ID
  uint64 template_id = 3 [(api.query) = "templateId"];
  // 关联开发单流程ID
  uint64 dev_task_workflow_id = 4 [(api.query) = "devTaskWorkflowId"];
  // 关联开发单流程ID
  uint64 release_ticket_workflow_id = 5 [(api.query) = "releaseTicketWorkflowId"];
}

message GetAvailableVarsResp {
  // 自定义变量
  repeated bc.varstore.AvailableVar custom_vars = 1;
  // 系统变量
  repeated bc.varstore.AvailableVar system_vars = 2;
}
