load("@rules_proto//proto:defs.bzl", "proto_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")

proto_library(
    name = "release_ticket_proto",
    srcs = [
        "check.proto",
        "common.proto",
        "task.proto",
        "variable.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_proto",
        "//idls/byted/bc/varstore:varstore_proto",
        "//idls/byted/devinfra/cd/shared:shared_proto",
        "@com_github_envoyproxy_protoc_gen_validate//validate:validate_proto",
    ],
)

go_proto_library(
    name = "release_ticket_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_validate_proto",
    ],
    importpath = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/release_ticket/sharedpb",
    proto = ":release_ticket_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//idls/byted/api:api_go_proto",
        "//idls/byted/bc/varstore:varstore_go_proto",
        "//idls/byted/devinfra/cd/shared:shared_go_proto",
        "//validate:validate_go_proto",
    ],
)
