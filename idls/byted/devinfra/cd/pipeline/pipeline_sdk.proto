syntax = "proto3";

package byted.devinfra.cd.pipeline;

import "idls/byted/devinfra/cd/shared/shared.proto";
import "idls/byted/bc/varstore/group.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/pipelinepb";

// ------ 请求创建流水线的信息 -----
message SDKCreatePipelineReq {
  // 用户名，邮箱前缀
  string username = 1;

  // ByteCycle 空间 ID
  uint64 workspace_id = 2;

  // 主流水线创建参数
  MainPipelineCreationOptions main_pipeline_create_options = 3;

  // 子流水线创建参数
  repeated ProjectPipelineCreationOptions project_pipeline_create_options = 4;

  // 变量替换 provider
  bc.varstore.SysProvider var_provider = 5;

  string pipeline_driver = 6;
}

// 主流水线创建选项
message MainPipelineCreationOptions {
  uint64 template_id = 1;  // 主流水线模版 ID

  // 流水线名称
  string pipeline_name = 2;

  // 流水线名称英文
  string pipeline_name_i18n = 3;

  // 流水线标签
  string pipeline_scene_tag = 4;
}

// 项目流水线创建选项
message ProjectPipelineCreationOptions {
  // 模版 ID
  uint64 template_id = 1;

  // 项目类型
  shared.ProjectType project_type = 2;

  // 项目唯一标识
  string project_unique_id = 3;

  // 项目名称
  string project_name = 4;

  // 流水线名称
  string pipeline_name = 5;

  // 流水线名称英文
  string pipeline_name_i18n = 6;

  // 流水线标签
  string pipeline_scene_tag = 7;

  // 依赖信息
  repeated Dependencies dependencies = 8;
}

// 依赖信息
message Dependencies {
  // 项目类型
  shared.ProjectType project_type = 1;

  // 项目唯一标识
  string project_unique_id = 2;
}

// 创建流水线响应信息
message SDKCreatePipelineResp {
  // 主流水线详情
  MainPipelineCreationResult main_pipeline_info = 1;

  // 项目子流水线详情
  repeated ProjectPipelineCreationResult project_pipelines = 2;
}

// 主流水线创建结果
message MainPipelineCreationResult {
  // 主流水线模板 ID
  uint64 template_id = 1;

  // 模版快照版本
  uint64 template_version = 2;

  // 主流水线 ID
  uint64 pipeline_id = 3;
}

// 项目流水线创建结果
message ProjectPipelineCreationResult {
  // 模板 ID
  uint64 template_id = 1;

  // 模版快照版本
  uint64 template_version = 2;

  // 流水线 ID
  uint64 pipeline_id = 3;

  // 项目类型
  shared.ProjectType project_type = 4;

  // 项目名称
  string project_name = 5;

  // 项目唯一标识
  string project_unique_id = 6;

  // 驱动器 ID
  string pipeline_driver_atom_service_key = 7;
}

// 项目流水线信息
message ProjectPipelineInfo {
  // 模板 ID
  uint64 template_id = 1;

  // 流水线 ID
  uint64 pipeline_id = 2;

  // 模版快照版本
  uint64 template_version = 3;

  // 驱动器 ID
  string pipeline_driver_atom_service_key = 4;

  // 项目类型
  shared.ProjectType project_type = 5;

  // 项目名称
  string project_name = 6;

  // 项目唯一标识
  string project_unique_id = 7;

  // 依赖信息
  repeated Dependencies dependencies = 8;
}

// ----- 主流水线运行参数 -----
message SDKRunMainPipelineReq {
  // 用户名
  string username = 1;

  // 所有的流水线详情
  repeated PipelineOverview pipeline_overviews = 2;

  // 选中运行的控制面
  shared.ControlPlane selected_control_plane = 3;

  // 选择运行的项目列表
  repeated ProjectParams selected_projects = 4;

  // 流水线ID-AssignmentIDs
  map<uint64, AssignmentIds> pipeline_id_to_assignment_ids = 5;

  // 流水线ID-系统变量Map
  map<uint64, Vars> pipeline_id_to_vars_map = 6;

  // 是否是自动触发
  bool auto_trigger = 7;
}

// Assignment IDs
message AssignmentIds {
  repeated uint64 ids = 1;
}

// Vars
message Vars {
  map<string, string> vars = 1;
}

// 所有的流水线详情
message PipelineOverview {
  // 控制面
  shared.ControlPlane control_plane = 1;

  // 流水线类型（主/项目）
  shared.PipelineType pipeline_type = 2;

  // 主流水线详情，类型是 PIPELINE_TYPE_MAIN 生效
  MainPipeline main_pipeline = 3;

  // 项目流水线详情，类型是 PIPELINE_TYPE_PROJECT 生效
  ProjectPipeline project_pipeline = 4;
}

// 主流水线详情
message MainPipeline {
  // 流水线 ID
  uint64 pipeline_id = 1;

  // 是否含有 placeholder
  bool has_placeholder = 2;
}

// 项目流水线详情
message ProjectPipeline {
  // 流水线 ID
  uint64 pipeline_id = 1;

  // 项目唯一 ID
  string project_unique_id = 2;

  // 项目类型
  shared.ProjectType project_type = 3;

  // 项目名称
  string project_name = 4;
}

// 项目信息
message ProjectParams {
  // 项目唯一 ID
  string project_unique_id = 1;

  // 项目类型
  shared.ProjectType project_type = 2;

  // 项目名称
  string project_name = 3;
}

message SDKRunMainPipelineResp {}