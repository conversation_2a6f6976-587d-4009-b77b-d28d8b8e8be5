syntax = "proto3";

package byted.devinfra.cd.paas;

import "idls/byted/devinfra/cd/shared/shared.proto";
import "idls/byted/devinfra/cd/paas/scm.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/cd/paaspb";

// Gecko 平台控制面
enum GeckoControlPlane {
  GECKO_CONTROL_PLANE_UNSPECIFIED = 0;
  GECKO_CONTROL_PLANE_CN = 1;
  GECKO_CONTROL_PLANE_BOE_CN = 2;
  GECKO_CONTROL_PLANE_I18N = 3;
  GECKO_CONTROL_PLANE_BOE_I18N = 4;
  GECKO_CONTROL_PLANE_TT_ROW = 5;
  GECKO_CONTROL_PLANE_US_TTP = 6;
  GECKO_CONTROL_PLANE_EU_TTP = 7;
}

// Gecko 环境类型
enum GeckoEnvType {
  GECKO_ENV_TYPE_UNSPECIFIED = 0;
  GECKO_ENV_TYPE_PROD = 1;  // 线上
  GECKO_ENV_TYPE_TEST = 2;  // 内测
}

// Gecko 资源包类型
enum GeckoResourceType {
  GECKO_RESOURCE_TYPE_UNSPECIFIED = 0;
  GECKO_RESOURCE_TYPE_ONLINE = 1;   // 在线包
  GECKO_RESOURCE_TYPE_OFFLINE = 2;  // 离线包
}

// Gecko 平台IES/TT
enum GeckoPlatformType {
  GECKO_PLATFORM_TYPE_UNSPECIFIED = 0;
  GECKO_PLATFORM_TYPE_IES = 1;
  GECKO_PLATFORM_TYPE_TT = 2;
}

// Gecko 工单状态
enum GeckoTicketOriginStatus {
  GECKO_TICKET_ORIGIN_STATUS_UNSPECIFIED = 0;
  // 未开始
  GECKO_TICKET_ORIGIN_STATUS_NOTREADY = 1;
  // 进行中
  GECKO_TICKET_ORIGIN_STATUS_RUNNING = 2;
  // 成功
  GECKO_TICKET_ORIGIN_STATUS_SUCCESS = 3;
  // 失败
  GECKO_TICKET_ORIGIN_STATUS_FAILED = 4;
  // 已取消
  GECKO_TICKET_ORIGIN_STATUS_CANCELLED = 5;
  // 取消中
  GECKO_TICKET_ORIGIN_STATUS_CANCELLING = 6;
  // 回滚中
  GECKO_TICKET_ORIGIN_STATUS_ROLLBACKING = 7;
  // 已回滚
  GECKO_TICKET_ORIGIN_STATUS_ROLLBACKED = 8;
}

message ChannelItem {
  GeckoControlPlane region = 1;
  uint64 gecko_app_id = 2;
  // @optional
  string gecko_app_name = 3;
  GeckoEnvType gecko_env_type = 4;
  uint64 gecko_deployment_id = 5;
  // @optional
  string gecko_deployment_name = 6;
  uint64 gecko_channel_id = 7;
  // @optional
  string gecko_channel_name = 8;
  // 资源包类型，多选
  repeated GeckoResourceType resource_types = 9;
  // @optional
  // 下发规则 id，Gecko-IES 必须
  string deploy_rule = 10;
  // 平台类型
  GeckoPlatformType platform = 11;

  // @泳道相关
  // 环境标识前缀。例如 `boe_cn_env_`
  string lane_type = 12;
  // 环境标识后缀。例如 `test`。`${lane_type}${lane_id}` 拼接后就是完整的环境名称
  string lane_id = 13;
  // 额外的泳道标识。当用户指定的情况下，将覆盖 `lane_type` 作为最终的环境名称前缀
  string overwrite_prefix = 14;
  // 快照中是否被选中
  bool is_checked = 15;
  // 在应用中心中是否被删除
  bool is_deleted_in_app_center = 16;

  // channel 配置的 scm 产物路径
  string scm_artifact_path = 17;
  // 在线包是否 mcdn
  bool is_mcdn = 18;
}

message GeckoDeployStrategy {
  repeated ChannelItem channel_items = 1;
}

message GeckoProject {
  string project_unique_id = 1;
  shared.ControlPlane control_plane = 2;
}

message BatchGetChannelReq {
  repeated GeckoProject projects = 1;
}

// protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
message ChannelInfo {
  GeckoProject project = 1;
  repeated ChannelItem channel_info = 2;
}

// protolint:disable REPEATED_FIELD_NAMES_PLURALIZED
message BatchGetChannelResp {
  repeated ChannelInfo channel_info = 1;
}

// Gecko 回滚确认信息
message GeckoRollbackInfo {
  paas.BriefScmVersionInfo scm_version_info = 1;
  repeated GeckoRollbackInfoItem rollback_info = 2;
}

// gecko 资源包回滚信息
message GeckoRollbackInfoItem {
  // job index，暂时用作 key
  string job_index = 1;
  // 暂时用 gecko 灰度原子 job 状态映射为工单状态
  GeckoTicketOriginStatus status = 2;
  // 原子灰度百分比
  int32 percent = 3;
  // 资源包链接
  string gecko_link = 4;
  GeckoControlPlane region = 6;
  uint64 gecko_app_id = 7;
  string gecko_app_name = 8;
  GeckoEnvType gecko_env_type = 9;
  uint64 gecko_deployment_id = 10;
  string gecko_deployment_name = 11;
  uint64 gecko_channel_id = 12;
  string gecko_channel_name = 13;
  // 资源包类型
  GeckoResourceType resource_type = 14;
  // Gecko 平台IES/TT
  GeckoPlatformType platform = 15;
  // 原子 job_id
  uint64 job_id = 16;
  // 资源包 id
  string package_id = 17;
  // 是否可回滚
  bool can_rollback = 18;
  // 只有 IES channel 批量发布回滚时使用，正向工单 id
  string gecko_ticket_id = 19;
}

// Gecko 回滚信息入参
message GeckoRollbackPayload {
  repeated GeckoRollbackPayloadItem rollback_payload_info = 1;
}

message GeckoRollbackPayloadItem {
  // 原子 job_index
  string job_index = 1;
  // 原子 job_id
  uint64 job_id = 2;
  // 资源包 id
  uint64 package_id = 3;
  GeckoControlPlane region = 4;
  uint64 gecko_app_id = 5;
  string gecko_app_name = 6;
  GeckoEnvType gecko_env_type = 7;
  uint64 gecko_deployment_id = 8;
  string gecko_deployment_name = 9;
  uint64 gecko_channel_id = 10;
  string gecko_channel_name = 11;
  GeckoResourceType resource_type = 12;
  // 只有 IES channel 批量发布回滚时使用，正向工单 id
  string gecko_ticket_id = 13;
}