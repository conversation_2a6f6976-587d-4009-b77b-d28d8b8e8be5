// protolint:disable REPEATED_FIELD_NAMES_PLURALIZED FIELD_NAMES_LOWER_SNAKE_CASE
syntax = "proto3";

package byted.devinfra.crossborder;
import "idls/byted/api/api.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/crossborderpb";

// req for getting scm version
message SCMGetVersionInfoReq {
  // The scm repo id
  string scm_repo_id = 1 [(api.path) = "scm_repo_id"];

  // The version of scm repo
  string version = 2 [(api.path) = "version"];

  // The x-gw-domain header.
  string x_gw_domain = 110 [(api.header) = "x-gw-domain"];
}

// resp for getting scm version
message SCMGetVersionInfoResp {
  // The version of the message.
  string version = 1;

  // The type of the message.
  string type = 3;

  // The user who created this scm version. -Corporation
  string create_user = 4;

  // The Git URL associated with this message.
  string git_url = 5;

  // Description for the scm version.
  string desc = 6;

  // Binary path information.
  string bin_path = 7;

  // The number of repositories.
  uint32 repos = 8;

  // Public base information.
  string pub_base = 9;

  // The branch name in Git.
  string branch_name = 11;

  // Base commit hash for this message.
  string base_commit_hash = 12;

  // Tarball URL.
  string tar_url = 14;

  // Tarball URL for aarch64 architecture.
  string tar_url_aarch64 = 15;

  // Local date information.
  string local_date = 16;

  // Commit URL.
  string commit_url = 17;

  // Status information.
  string status = 18;

  // Status information for aarch64 architecture.
  string status_aarch64 = 19;

  // Date of creation for this message.
  string create_date = 20;

  // Flag indicating AWS synchronization.
  bool sync_aws = 21;

  // Status display information.
  string status_display = 22;

  // Status display information for aarch64 architecture.
  string status_display_aarch64 = 23;

  // Custom user-specific information.
  string user_custom_info = 24;

  // The unique identifier for this message.
  uint32 id = 25;

  // Flag indicating OSS synchronization.
  bool sync_oss = 26;

  // Git tag information.
  string git_tag = 27;

  // Job ID associated with this message.
  string job_id = 28;

  // Flag indicating OSS VA synchronization.
  bool sync_oss_va = 30;

  // Flag indicating BVC synchronization.
  bool sync_bvc = 31;

  // File URL information.
  string file_url = 32;

  // File URL information for aarch64 architecture.
  string file_url_aarch64 = 33;

  // MD5 URL for the file.
  string file_md5_url = 34;

  // Runtime type information.
  string runtime_type = 35;

  // Repository name.
  string repo_name = 36;

  // Flag indicating the presence of a preonline process.
  bool has_preonline = 37;

  // Flag indicating the presence of a preonline process for aarch64 architecture.
  bool has_preonline_aarch64 = 38;

  // Source code download URL.
  string src_download_url = 41;

  // Notification level for the message.
  uint32 notification_level = 42;

  // Error message information.
  string error_message = 43;

  // Error message information for aarch64 architecture.
  string error_message_aarch64 = 44;

  // Origin information.
  string origin = 46;

  // User-specific environment information.
  string user_envs = 47;

  // Source code management environment information.
  string scm_envs = 48;

  // Product size information.
  string product_size = 49;

  // Product size information for aarch64 architecture.
  string product_size_aarch64 = 50;

  // Finish date for the message.
  string finish_date = 51;

  // Finish date for aarch64 architecture.
  string finish_date_aarch64 = 52;

  // The user who disabled the message.
  string disabled_by = 53;

  // Reason for disabling the message.
  string disabled_reason = 54;

  // Security-related information.
  string security_info = 55;

  // Task information associated with the message.
  string tasks = 56;

  // Resource upload type.
  string resource_upload_type = 57;

  // Build image information.
  string build_image = 59;

  // Compilation time information.
  string compile_time = 60;

  // Compilation time information for aarch64 architecture.
  string compile_time_aarch64 = 61;

  // Repository type information.
  string repo_type = 62;

  // Public image name information.
  string pub_image_name = 63;

  // Build status information.
  string build_status = 65;

  // Build URL.
  string build_url = 67;
}

// Request for getting scm repo.
message SCMGetRepoReq {
  // The scm repo id.
  int64 id = 1 [(api.path) = "id"];
}

// Response for getting scm repo.
message SCMGetRepoResp {
  // The id of the scm repo.
  int64 id = 1;

  // The name of the scm repo, e.g. s/c/m.
  string name = 2;
}
