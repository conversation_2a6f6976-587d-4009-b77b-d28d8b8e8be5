syntax = "proto3";

package byted.devinfra.goodcommit;
option go_package = "code.byted.org/devinfra/hagrid/pbgen/goodcommitpb";

import "validate/validate.proto";

enum PipelineStatus {
  // 简化的 pipeline 状态，只有成功和失败，正在运行，取消这几种状态
  // 将来如果需要“queuing”, "awaiting user interaction"等，以后再加。
  PIPELINE_STATUS_RUNNING = 0;
  PIPELINE_STATUS_SUCCESS = 1;
  PIPELINE_STATUS_FAILED = 2;
  PIPELINE_STATUS_CANCELED = 3;
  PIPELINE_STATUS_UNSPECIFIED = 4;
  PIPELINE_STATUS_BLOCKING = 5;
  PIPELINE_STATUS_QUEUING = 6;
}

message GIT_COMMIT {
  string repo = 1 [
    (validate.rules).string = {
      pattern: "^[a-zA-Z0-9_\\-\\.]+/[a-zA-Z0-9_\\-\\.]+$", // \w + / + \w,
      min_len: 3,
      max_len: 255,
    }
  ];
  string sha = 2 [
    (validate.rules).string = {
      pattern: "^[0-9a-f]{40,64}$"
    }
  ];
  optional string commit_message = 3;
  optional string author = 4;
  optional string committer = 5;
  // TODO: maybe add more
}

