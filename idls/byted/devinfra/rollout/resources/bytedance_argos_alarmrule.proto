syntax = "proto3";

package byted.devinfra.rollout.resources;

import "idls/byted/devinfra/rollout/resources/annotations.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rollout/resourcespb";

//"US-East",
//"US-EE",
//"US-Central",
//"SouthAmerica-East",
//"US-West",
//"US-SouthWest",
message ArgosAlarmRule {
  option (resource_schema_meta) = {
    type: ARGOS_ALARM_RULE,
    ros_type: "ByteDance::Argos::AlarmRule",
    property_category: SPEC,
    create_only_properties: [ "PSM", "Cluster" ]
    partition_by_placement_regex: [
      {key:"^VGeo-RoW\\|(SouthAmerica-East|US-East|US-EE|US-Central|US-West|US-SouthWest)(\\|.*)*$",value:"us"}
    ]
  };
  message MeasurementData {
    optional string top_bottom = 1 [json_name = "TopBottom"];
    repeated string aggregators = 2 [json_name = "Aggregators"];
    optional string downsample_interval = 3 [json_name = "DownsampleInterval"];
    optional string downsample_func = 4 [json_name = "DownsampleFunc"];
    repeated MeasurementTag measurement_tags = 5 [json_name = "MeasurementTags"];
    optional string measurement = 6 [json_name = "Measurement"];
    optional string observable_object = 7 [json_name = "ObservableObject"];
    optional bool context_link = 8 [json_name = "ContextLink"];
  }
  message WebhookConfig {
    optional string key_name = 1 [json_name = "KeyName"];
    optional string type = 2 [json_name = "Type"];
    repeated WebhookConfigParam params = 3 [json_name = "Params"];
  }
  message Matcher {
    optional int32 id = 1 [json_name = "Id"];
    optional string name = 2 [json_name = "Name"];
    optional string pattern = 3 [json_name = "Pattern"];
    optional string status = 4 [json_name = "Status"];
  }
  message RoutingType {
    optional string name = 1 [json_name = "Name"];
    optional string rule_config_type = 2 [json_name = "RuleConfigType"];
  }
  message RoutingConfig {
    repeated UpgradeInfo upgrade_info_list = 1 [json_name = "UpgradeInfoList"];
    optional int32 setting_id = 2 [json_name = "SettingId"];
    repeated string receivers = 3 [json_name = "Receivers"];
    repeated string lark_groups = 4 [json_name = "LarkGroups"];
  }
  message Condition {
    optional string type = 1 [json_name = "Type"];
    GeneralCondition general_condition = 2 [json_name = "GeneralCondition"];
  }
  message Computation {
    optional string alias = 1 [json_name = "Alias"];
    optional string formula = 2 [json_name = "Formula"];
    MetricData metric_data = 3 [json_name = "MetricData"];
    MeasurementData measurement_data = 4 [json_name = "MeasurementData"];
    optional string type = 5 [json_name = "Type"];
  }
  message MetricDataTag {
    optional string key = 1 [json_name = "Key"];
    optional string value = 2 [json_name = "Value"];
    optional bool is_filter = 3 [json_name = "IsFilter"];
    optional string function = 4 [json_name = "Function"];
  }
  message Sli {
    optional string success_qps = 1 [json_name = "SuccessQPS"];
    optional string error_qps = 2 [json_name = "ErrorQPS"];
    MeasurementData measurement_data = 3 [json_name = "MeasurementData"];
    optional string sli_type = 4 [json_name = "SliType"];
    optional string sli = 5 [json_name = "Sli"];
    optional string success_rate = 6 [json_name = "SuccessRate"];
    optional string total_qps = 7 [json_name = "TotalQPS"];
  }
  message RuleTag {
    optional string key = 1 [json_name = "Key"];
    optional string val = 2 [json_name = "Val"];
    optional string namespace = 3 [json_name = "Namespace"];
  }
  message AlterRecovery {
    optional int32 consecutive_send_cycle_num = 1 [json_name = "ConsecutiveSendCycleNum"];
    optional string mode = 2 [json_name = "Mode"];
    optional bool send_notification = 3 [json_name = "SendNotification"];
  }
  message OndutyRouting {
    RoutingType routing_type = 1 [json_name = "RoutingType"];
    RoutingConfig routing_config = 2 [json_name = "RoutingConfig"];
  }
  message ConditionGroup {
    Slo slo = 1 [json_name = "Slo"];
    optional int32 window_delay_min = 2 [json_name = "WindowDelayMin"];
    repeated Condition conditions = 3 [json_name = "Conditions"];
    optional string logic_relation_type = 4 [json_name = "LogicRelationType"];
    repeated Computation computations = 5 [json_name = "Computations"];
  }
  message Slo {
    optional string detection_type = 1 [json_name = "DetectionType"];
    optional double slo = 2 [json_name = "Slo"];
    BurnRateSetting burn_rate_setting = 3 [json_name = "BurnRateSetting"];
  }
  message MetricData {
    optional string top_bottom = 1 [json_name = "TopBottom"];
    optional string downsample_interval = 2 [json_name = "DownsampleInterval"];
    optional string downsample_func = 3 [json_name = "DownsampleFunc"];
    optional string downsample_fill_policy = 4 [json_name = "DownsampleFillPolicy"];
    RateOptions rate_options = 5 [json_name = "RateOptions"];
    optional string metrics_name = 6 [json_name = "MetricsName"];
    optional string aggregator = 7 [json_name = "Aggregator"];
    repeated MetricDataTag tags = 8 [json_name = "Tags"];
    optional bool rate = 9 [json_name = "Rate"];
  }
  message RateOptions {
    optional int32 counter_max = 1 [json_name = "CounterMax"];
    optional int32 reset_value = 2 [json_name = "ResetValue"];
    optional bool diff = 3 [json_name = "Diff"];
    optional string order = 4 [json_name = "Order"];
    optional bool counter = 5 [json_name = "Counter"];
  }
  message MeasurementTag {
    optional string key = 1 [json_name = "Key"];
    optional string value = 2 [json_name = "Value"];
    optional bool is_filter = 3 [json_name = "IsFilter"];
    optional string function = 4 [json_name = "Function"];
  }
  message Extension {
    OndutyRouting onduty_routing = 1 [json_name = "OndutyRouting"];
    ConditionGroup condition_group = 2 [json_name = "ConditionGroup"];
    Call call = 3 [json_name = "Call"];
  }
  message Call {
    optional bool exclude_stress_tag = 1 [json_name = "ExcludeStressTag"];
    optional bool use_trace = 2 [json_name = "UseTrace"];
  }
  message UpgradeInfo {
    optional int32 setting_id = 1 [json_name = "SettingId"];
    optional string upgrade_to = 2 [json_name = "UpgradeTo"];
    optional string phone_call_strategy = 3 [json_name = "PhoneCallStrategy"];
    optional int32 level = 4 [json_name = "Level"];
  }
  message GeneralCondition {
    optional double threshold = 1 [json_name = "Threshold"];
    Sli sli = 2 [json_name = "Sli"];
    MetricData metric_data = 3 [json_name = "MetricData"];
    optional string detection_type = 4 [json_name = "DetectionType"];
    optional int32 evaluation_window = 5 [json_name = "EvaluationWindow"];
    optional int32 comparison_window = 6 [json_name = "ComparisonWindow"];
    optional string aggr_func = 7 [json_name = "AggrFunc"];
    optional string change_type = 8 [json_name = "ChangeType"];
    optional string data_type = 9 [json_name = "DataType"];
    optional bool is_main = 10 [json_name = "IsMain"];
    optional string operator = 11 [json_name = "Operator"];
    optional string data_alias = 12 [json_name = "DataAlias"];
  }
  message BurnRateSetting {
    optional int32 slo_compliance_period_days = 1 [json_name = "SloCompliancePeriodDays"];
    repeated BurnRateCondition burn_rate_conditions = 2 [json_name = "BurnRateConditions"];
  }
  message BurnRateCondition {
    optional double burn_rate_value = 1 [json_name = "BurnRateValue"];
    optional int32 check_window_min = 2 [json_name = "CheckWindowMin"];
    optional int32 recover_window_min = 3 [json_name = "RecoverWindowMin"];
  }
  message WebhookConfigParam {
    optional string value_type = 1 [json_name = "ValueType"];
    optional string value_str = 2 [json_name = "ValueStr"];
    optional string key = 3 [json_name = "Key"];
  }
  message Var {
    optional string name = 1 [json_name = "name"];
    optional string value = 2 [json_name = "value"];
    optional string type = 3 [json_name = "type"];
  }

  optional string level = 1 [json_name = "Level"];
  optional string callback_url = 2 [json_name = "CallbackURL"];
  optional int32 check_interval = 3 [json_name = "CheckInterval"];
  optional string handle_suggestion = 4 [json_name = "HandleSuggestion"];
  optional bool degrade_send_opened = 5 [json_name = "DegradeSendOpened"];
  optional string name = 6 [json_name = "Name"];
  optional string suit_type = 7 [json_name = "SuitType"];
  optional string vregion = 8 [json_name = "Vregion"];
  optional string owners = 9 [json_name = "Owners"];
  optional string rule_alias = 10 [json_name = "RuleAlias"];
  optional string extra = 11 [json_name = "Extra"];
  Matcher matcher = 12 [json_name = "Matcher"];
  AlterRecovery alter_recovery = 13 [json_name = "AlterRecovery"];
  optional string type = 14 [json_name = "Type"];
  optional string expr_render_type = 15 [json_name = "ExprRenderType"];
  repeated string alarm_methods_list = 16 [json_name = "AlarmMethodsList"];
  optional int32 send_interval = 17 [json_name = "SendInterval"];
  repeated string webhooks = 18 [json_name = "Webhooks"];
  optional string zone = 19 [json_name = "Zone"];
  optional string cluster = 20 [json_name = "Cluster"];
  optional int64 unit_id = 21 [json_name = "UnitId"];
  optional string attach_mode = 22 [json_name = "AttachMode"];
  optional string rule = 23 [json_name = "Rule"];
  optional string trigger_url = 24 [json_name = "TriggerURL"];
  Extension extension = 25 [json_name = "Extension"];
  optional int32 threshold = 26 [json_name = "Threshold"];
  repeated RuleTag tags = 27 [json_name = "Tags"];
  map<string, string> rule_substitutions = 28 [json_name = "RuleSubstitutions"];
  optional string ding_ding_chan_ids = 29 [json_name = "DingDingChanIds"];
  optional int32 count_to_send = 30 [json_name = "CountToSend"];
  optional string psm = 31 [json_name = "PSM"];
  optional string status = 32 [json_name = "Status"];
  repeated WebhookConfig webhook_configs = 33 [json_name = "WebhookConfigs"];
  optional string alarm_methods = 34 [json_name = "AlarmMethods"];
  repeated Var vars = 35 [json_name = "Vars"];
}

message ArgosAlarmRuleStatus {
  option (resource_schema_meta) = {
    type: ARGOS_ALARM_RULE,
    ros_type: "ByteDance::Argos::AlarmRule",
    property_category: STATUS,
  };

  optional int64 id = 1 [json_name = "Id"];
}

message ArgosAlarmRulePrimaryIdentifier {
  option (resource_schema_meta) = {
    type: ARGOS_ALARM_RULE,
    ros_type: "ByteDance::Argos::AlarmRule",
    property_category: PRIMARY_IDENTIFIER,
  };

  optional int64 id = 1 [json_name = "Id"];
}
