syntax = "proto3";

package byted.devinfra.pipeline.service;

import "idls/byted/devinfra/infra/rpc/annotations.proto";
import "validate/validate.proto";
import "idls/byted/devinfra/pipeline/platform/pipeline_group.proto";
import "idls/byted/devinfra/pipeline/platform/pipeline_record.proto";
import "idls/byted/devinfra/pipeline/dsl/pipeline.proto";
import "idls/byted/devinfra/pipeline/dsl/notification.proto";
import "idls/byted/devinfra/pipeline/platform/pipeline_run.proto";
import "idls/byted/devinfra/pipeline/platform/pipeline.proto";
import "idls/byted/devinfra/pipeline/platform/job_run.proto";
import "idls/byted/devinfra/pipeline/platform/atom.proto";
import "idls/byted/devinfra/pipeline/platform/runner.proto";
import "idls/byted/devinfra/pipeline/platform/variable.proto";
import "idls/byted/devinfra/pipeline/dsl/variable.proto";
import "idls/byted/devinfra/pipeline/dsl/status.proto";
import "idls/byted/devinfra/pipeline/dsl/trigger.proto";
import "idls/byted/devinfra/pipeline/platform/auth.proto";
import "idls/byted/devinfra/pipeline/platform/metrics.proto";
import "idls/byted/bc/varstore/assignment.proto";
import "idls/byted/bc/varstore/group.proto";
import "idls/byted/bc/varstore/variable.proto";
import "idls/byted/devinfra/shared/pagination.proto";
import "idls/byted/devinfra/space/rpc/space.proto";
import "google/protobuf/struct.proto";
import "idls/byted/devinfra/infra/i18n.proto";
import "google/protobuf/timestamp.proto";
import "idls/byted/devinfra/atommanage/atom.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/servicepb";

// 名称使用驼峰形式，Hertz服务以API为后缀
service PipelineService {
  // 标识服务的 PSM 与框架等信息，用于指导代码生成
  option (infra.rpc.service) = {
    psm: "bits.pipeline.rpc",
    framework: FRAMEWORK_KITEX,
  };

  // pipeline
  rpc EditFavorPipeline(EditFavorPipelineRequest) returns (EditFavorPipelineResponse);

  // 流水线分组
  // 创建: one-site流水线分组 创建
  rpc CreatePipelineGroup(CreatePipelineGroupRequest) returns (CreatePipelineGroupResponse);

  // 查询: 单个one-site流水线分组
  rpc GetPipelineGroup(GetPipelineGroupRequest) returns (GetPipelineGroupResponse);

  // 更新: one-site流水线分组 更新
  rpc UpdatePipelineGroup(UpdatePipelineGroupRequest) returns (UpdatePipelineGroupResponse);

  // 查询: one-site流水线分组 列表
  rpc ListPipelineGroup(ListPipelineGroupRequest) returns (ListPipelineGroupResponse);

  // 查询: 自定义分组顺序下的one-site流水线分组 列表
  rpc ListPipelineGroupOrder(ListPipelineGroupOrderRequest) returns (ListPipelineGroupOrderResponse);

  // 删除: one-site流水线分组
  rpc DeletePipelineGroup(DeletePipelineGroupRequest) returns (PipelineGroupEmptyResponse);

  // 批量添加流水线到分组
  rpc CreatePipelineGroupItems(CreatePipelineGroupItemsRequest) returns (PipelineGroupEmptyResponse);

  // 批量从某分组移除流水线
  rpc DeletePipelineGroupItems(DeletePipelineGroupItemsRequest) returns (PipelineGroupEmptyResponse);

  // 更新流水线的分组
  rpc EditGroupOfPipeline(EditGroupOfPipelineRequest) returns (PipelineGroupEmptyResponse);

  // 创建流水线分组顺序
  rpc CreatePipelineGroupOrder(CreatePipelineGroupOrderRequest) returns (PipelineGroupEmptyResponse);

  // 流水线预览
  rpc TranslateYamlToPipeline(TranslateYamlToPipelineRequest) returns (TranslateYamlToPipelineResponse);
  // yaml校验
  rpc VerifyYaml(VerifyYamlRequest) returns (VerifyYamlResponse);

  // 提供表达式计算能力
  rpc EvalExpression(EvalExpressionRequest) returns (EvalExpressionResponse);

  rpc GetPipeline(GetPipelineRequest) returns (GetPipelineResponse);
  rpc CreatePipeline(CreatePipelineRequest) returns (CreatePipelineResponse);
  rpc RunPipeline(RunPipelineRequest) returns (RunPipelineResponse);
  rpc RunPipelineLite(RunPipelineRequest) returns (RunPipelineLiteResponse);
  rpc CreatePipelineRun(RunPipelineRequest) returns (CreatePipelineRunResponse);

  rpc UpdatePipeline(UpdatePipelineRequest) returns (UpdatePipelineResponse);
  //  patch pipeline from template, called by templaterpc
  rpc TemplatePatchPipeline(TemplatePatchPipelineRequest) returns (TemplatePatchPipelineResponse);
  // pipeline run
  rpc OperatePipelineRun(OperatePipelineRunRequest) returns (OperatePipelineRunResponse);
  // precheck
  rpc PreCheckPipeline(PreCheckPipelineRequest) returns (PreCheckPipelineResponse);
  rpc GetPreCheckJobs(platform.GetPreCheckJobsRequest) returns (platform.GetPreCheckJobsResponse);
  // git pipeline run
  rpc RunGitPipeline(RunGitPipelineRequest) returns (RunGitPipelineResponse);
  // job run
  rpc GetJobRun(GetJobRunRequest) returns (GetJobRunResponse);
  rpc CheckJobRunOperationPermission(platform.CheckJobRunOperationPermissionRequest) returns (platform.CheckJobRunOperationPermissionResponse);
  // 查询流水线运行中某个原子信息
  rpc GetJobRunsByPipelineRunIDAndAtomUID(GetJobRunsByPipelineRunIDAndAtomUIDRequest) returns (GetJobRunsByPipelineRunIDAndAtomUIDResponse);
  rpc OperateJobRun(OperateJobRunRequest) returns (OperateJobRunResponse);
  // Job_run: 查询 Job run 的 变量
  rpc GetJobRunVars(GetJobRunVarsRequest) returns (GetJobRunVarsResponse);
  // Job_run: gpt智能分析数据
  rpc GetJobGptData(GetJobGptDataRequest) returns (GetJobGptDataResponse);
  // Job_run: gpt智能分析数据，适用于bits
  rpc GetJobGptDataForBits(GetJobGptDataForBitsRequest) returns (GetJobGptDataForBitsResponse);
  // Job_run: 获取失败的job run，用于ai评测或者其他场景
  rpc ListFailJobRunIdsForGpt(ListFailJobRunIdsForGptRequest) returns (ListFailJobRunIdsForGptResponse);
  // Job_run: 获取原子最近一次指定状态的运行，用于测试 ai 排障配置里的日志 url
  rpc GetAtomLastJobRunLog(platform.GetAtomLastJobRunLogRequest) returns (platform.GetAtomLastJobRunLogResponse);
  // 迁移相关的接口，迁移期间有效，后续会删除
  rpc MigrateCreatePipeline(MigrateCreatePipelineRequest) returns (MigrateCreatePipelineResponse);
  // 更新流水线变量，目前仅提供给bc open-api proxy使用
  rpc UpdatePipelineVars(UpdatePipelineVarsRequest) returns (UpdatePipelineVarsResponse);
  rpc UpdatePipelineControlPanel(UpdatePipelineControlPanelRequest) returns (UpdatePipelineVarsResponse);
  // callback相关接口
  rpc MigrateNotifications(MigrateNotificationsRequest) returns (MigrateNotificationsResponse);
  // notification 相关接口
  rpc CreateNotification(CreateNotificationRequest) returns (CreateNotificationResponse);
  rpc UpdateNotification(UpdateNotificationRequest) returns (UpdateNotificationResponse);
  rpc DeleteNotification(DeleteNotificationRequest) returns (DeleteNotificationResponse);
  rpc GetNotifications(GetNotificationsRequest) returns (GetNotificationsResponse);
  //  notification group相关接口，提供给模版使用
  rpc SaveNotificationGroup(SaveNotificationGroupRequest) returns (SaveNotificationGroupResponse);
  rpc GetNotificationGroup(GetNotificationGroupRequest) returns (GetNotificationGroupResponse);
  rpc ListPipeline(ListPipelineRequest) returns (ListPipelineResponse);
  rpc GetPipelineRun(GetPipelineRunRequest) returns (GetPipelineRunResponse);
  rpc BatchGetLatestPipelineRuns(BatchGetLatestPipelineRunsRequest) returns (BatchGetLatestPipelineRunsResponse);
  rpc BatchGetPipelineRunLites(BatchGetPipelineRunLitesRequest) returns (BatchGetPipelineRunLitesResponse);
  rpc ListPipelineRun(ListPipelineRunRequest) returns (ListPipelineRunResponse);
  rpc WebhookCallback(WebhookCallbackRequest) returns (WebhookCallbackResponse);
  rpc ArchivePipeline(ArchivePipelineRequest) returns (ArchivePipelineResponse);
  rpc DeletePipeline(DeletePipelineRequest) returns (DeletePipelineResponse);
  rpc GetJobRerunStepsOutput(GetJobRerunStepsOutputRequest) returns (GetJobRerunStepsOutputResponse);
  rpc GetPipelineRunByCheckRunID(GetPipelineRunByCheckRunIDRequest) returns (GetPipelineRunByCheckRunIDResponse);
  rpc GetJobParameter(GetJobParameterRequest) returns (GetJobParameterResponse);
  rpc GetJobRerunSteps(GetJobRerunStepsRequest) returns (GetJobRerunStepsResponse);
  rpc GetJobLog(GetJobLogRequest) returns (GetJobLogResponse);
  rpc GetJobLogs(platform.GetJobLogsRequest) returns (platform.GetJobLogsResponse);
  rpc GetPipelineRunBySeq(GetPipelineRunBySeqRequest) returns (GetPipelineRunBySeqResponse);
  rpc GetJobStepLog(GetJobStepLogRequest) returns (GetJobStepLogResponse);
  rpc UpdateParentPipelineRun(UpdateParentPipelineRunRequest) returns (UpdateParentPipelineRunResponse);
  rpc RecommendPipelineVars(RecommendPipelineVarsRequest) returns (RecommendPipelineVarsResponse) {}

  // 列出归档的流水线
  rpc ListArchivedPipelineRun(ListArchivedPipelineRunRequest) returns (ListArchivedPipelineRunResponse);

  // PipelineRun: 为 pipeline_run 增加备注
  rpc NotePipelineRun(NotePipelineRunRequest) returns (NotePipelineRunResponse) {};

  // GetPipelineRunsByRepoAndMr 根据repo和mr查询git触发的pipeline run
  rpc GetPipelineRunsByMr(GetPipelineRunsByMrRequest) returns (GetPipelineRunsByMrResponse) {};
  // 查询流水线配置定时删除是否不足24小时
  rpc GetPipelineScheduledDelete(GetPipelineScheduledDeleteRequest) returns (GetPipelineScheduledDeleteResponse);
  rpc ScheduledArchivedPipeline(ScheduledArchivedPipelineRequest) returns (ScheduledArchivedPipelineResponse);

  /* ------------------------ 研发模式2.1 相关接口 ------------------------- */
  // 更新流水线的dsl，研发模式2.1专用
  rpc UpdatePipelineDsl(UpdatePipelineDslRequest) returns (UpdatePipelineDslResponse);
  // 修改流水线的角色，研发模式2.1专用
  rpc UpdatePipelineRole(UpdatePipelineRoleRequest) returns (UpdatePipelineRoleResponse);
  // 更新流水线触发器
  rpc UpdatePipelineTriggers(UpdatePipelineTriggersRequest) returns (UpdatePipelineTriggersResponse);
  // 更新流水线变量
  rpc UpdatePipelineVarGroup(UpdatePipelineVarGroupRequest) returns (UpdatePipelineVarGroupResponse);

  /* ------------------------ codebase ci 相关接口 ------------------------- */
  rpc GetGitPipeline(GetGitPipelineRequest) returns (GetGitPipelineResponse) {};
  // 流水线列表：获取 Codebase 上流水线列表：查询、搜索、排序 流水线信息 list
  rpc ListGitPipeline(ListGitPipelineRequest) returns (ListGitPipelineResponse) {};
  // 创建: CI流水线创建
  rpc CreateGitPipeline(CreateGitPipelineRequest) returns (CreateGitPipelineResponse) {};
  // 更新: CI流水线编辑
  rpc UpdateGitPipeline(UpdateGitPipelineRequest) returns (UpdateGitPipelineResponse) {};
  // PipelineRun: ci 流水线构建信息 list 显示
  rpc ListGitPipelineRun(ListGitPipelineRunRequest) returns (ListGitPipelineRunResponse) {};
  // PostTriggers: ci 子流水线构建信息 list 显示
  rpc ListPostTriggers(ListPostTriggersRequest) returns (ListPostTriggersResponse) {};
  // PipelineRun: 查询流水线变量
  rpc GetPipelineRunVars(GetPipelineRunVarsRequest) returns (GetPipelineRunVarsResponse) {};
  // 流水线运行列表：获取 Codebase 上最近运行流水线列表信息
  rpc ListRecentGitPipelineRun(ListRecentGitPipelineRunRequest) returns (ListRecentGitPipelineRunResponse) {};
  // PipelineRun: ci 流水线构建信息的详情
  rpc GetGitPipelineRunBySeq(GetGitPipelineRunBySeqRequest) returns (GetGitPipelineRunBySeqResponse) {};
  rpc CheckGitEvent(CheckGitEventRequest) returns (CheckGitEventResponse);
  // CI流水线: 获取流水线 inputs 信息
  rpc GetGitPipelineInputs(GetGitPipelineInputsRequest) returns (GetGitPipelineInputsResponse) {};
  // CI流水线：变量
  rpc SaveRepoVariables(SaveRepoVariablesRequest) returns (SaveRepoVariablesResponse) {};
  rpc GetRepoVariables(GetRepoVariablesRequest) returns (GetRepoVariablesResponse) {};

  // CI流水线：runner相关
  rpc CreateCIRunner(CreateCIRunnerRequest) returns (CreateCIRunnerResponse) {};
  rpc AssociateCIRunner(AssociateCIRunnerRequest) returns (AssociateCIRunnerResponse) {};
  rpc GetCIRunners(GetCIRunnersRequest) returns (GetCIRunnersResponse) {};
  rpc GetCIRunner(GetCIRunnerRequest) returns (GetCIRunnerResponse) {};
  rpc GetCIRunnerJobs(GetCIRunnerJobsRequest) returns (GetCIRunnerJobsResponse) {};
  rpc UpdateCIRunner(UpdateCIRunnerRequest) returns (UpdateCIRunnerResponse) {};
  rpc DeleteCIRunner(DeleteCIRunnerRequest) returns (DeleteCIRunnerResponse) {};

  /* ----------------------------------------------------------------------- */
  // 搜索流水线可引用的变量
  rpc GetAvailableVars(GetAvailableVarsRequest) returns (GetAvailableVarsResponse) {};
  // 解析表达式中引用的变量
  rpc GetVarsRef(platform.GetVarsRefRequest) returns (platform.GetVarsRefResponse) {};
  // 获取流水线最后一个执行原子的变量
  rpc GetLatestJobVarMap(platform.GetLatestJobVarMapRequest) returns (platform.GetLatestJobVarMapResponse) {};
  // 计算表达式结果
  rpc CalculateExpression(platform.CalculateExpressionRequest) returns (platform.CalculateExpressionResponse) {};

  // GetPipelineTagSetting:查询流水线空间tag配置
  rpc GetPipelineTagSetting(GetPipelineTagSettingRequest) returns (GetPipelineTagSettingResponse) {};
  // 查询流水线空间tag配置
  rpc SavePipelineTagSetting(SavePipelineTagSettingRequest) returns (SavePipelineTagSettingResponse) {};
  // 更新流水线job run的tag配置
  rpc UpdateJobRunTag(UpdateJobRunTagRequest) returns (UpdateJobRunTagResponse) {};
  // 批量取消pipeline run
  rpc OperatePipelineRunList(OperatePipelineRunListRequest) returns (OperatePipelineRunListResponse) {};
  // Pipeline: 查询pipeline的并发运行信息
  rpc GetPipelineConcurrency(GetPipelineConcurrencyRequest) returns (GetPipelineConcurrencyResponse) {};
  // Pipeline: 回收站查询删除的流水线
  rpc ListArchivedPipeline(ListArchivedPipelineRequest) returns (ListArchivedPipelineResponse) {};
  // Pipeline: 删除流水线
  rpc RecoverArchivedPipeline(RecoverArchivedPipelineRequest) returns (RecoverArchivedPipelineResponse) {};
  // Pipeline: 复制流水线
  rpc CopyPipeline(CopyPipelineRequest) returns (CopyPipelineResponse) {}
  // Pipeline: 个人工作台流水线列表
  rpc ListWorkbenchPipeline(ListWorkbenchPipelineRequest) returns (ListWorkbenchPipelineResponse) {}
  // Pipeline: 版本列表
  rpc ListPipelineVersion(ListPipelineVersionRequest) returns (ListPipelineVersionResponse) {}
  // Pipeline: 版本diff preview
  rpc GetPipelineVersionDiff(GetPipelineVersionDiffRequest) returns (GetPipelineVersionDiffResponse) {}
  // Pipeline: 获取最近访问的流水线列表
  rpc ListRecentPipeline(ListRecentPipelineRequest) returns (ListRecentPipelineResponse) {}
  // Pipeline: 回退流水线版本
  rpc RollbackPipelineVersion(RollbackPipelineVersionRequest) returns (RollbackPipelineVersionResponse) {}
  // Pipeline: 跨空间迁移
  rpc MigratePipeline(MigratePipelineRequest) returns (MigratePipelineResponse) {}
  // Pipeline: 跨空间批量迁移流水线和模版
  rpc BatchMigrateBitsPipeline(platform.BatchMigrateBitsPipelineRequest)
      returns (platform.BatchMigrateBitsPipelineResponse) {}

  rpc AllowPipelineMigration(AllowPipelineMigrationRequest) returns (AllowPipelineMigrationResponse) {}
  // 获取流水线运行耗时数据
  rpc GetPipelineRunDurationMetrics(platform.GetPipelineRunDurationMetricsRequest)
      returns (platform.GetPipelineRunDurationMetricsResponse) {}
  //  pipeline编辑时 复制 Job 信息
  rpc DuplicatePipelineJob(DuplicatePipelineJobRequest) returns (platform.DuplicatePipelineJobResponse) {}
  //  pipeline编辑时 复制 Job 信息
  rpc GetDuplicatePipelineJob(GetDuplicatePipelineJobRequest) returns (platform.GetDuplicatePipelineJobResponse) {}
  //  pipeline编辑时 删除 复制的Job 信息
  rpc DeletePipelineJobDuplicate(DeletePipelineJobDuplicateRequest) returns (platform.DuplicatePipelineJobResponse) {}
  //  pipeline编辑时 获取编排规则
  rpc GetPipelineRules(platform.GetPipelineRulesRequest) returns (platform.GetPipelineRulesResponse) {}
  // pipeline 操作记录
  rpc ListPipelineRecord(platform.ListPipelineRecordRequest) returns (platform.ListPipelineRecordResponse) {}
  // 批量删除流水线（流水线页面或者模板绑定的流水线列表页）时，前端过滤展示非终态的流水线
  rpc FilterPipelineNotEnd(platform.FilterPipelineNotEndRequest) returns (platform.FilterPipelineNotEndResponse) {}

  /* ------------------------ 原子市场 相关接口 ------------------------- */
  // AtomMarket：获取原子市场list全部数据
  rpc ListAtom(ListAtomRequest) returns (platform.ListAtomNewResponse);
  // AtomMarket：获取某个原子的历史版本
  rpc GetAtomVersions(GetAtomVersionRequest) returns (platform.GetAtomVersionResponse);
  // AtomMarket：获取某个原子的表单信息，详情信息
  rpc GetAtomAttr(GetAtomInputFormRequest) returns (platform.GetAtomInputFormResponse);
  // AtomMarket：关注、取关原子
  rpc EditFavoriteAtom(EditFavoriteAtomRequest) returns (platform.EditFavoriteAtomResponse);
}

message CreateCIRunnerRequest {
  uint64 repo_id = 1;
  string cluster_tag = 2;
  string description = 3;
}

message CreateCIRunnerResponse {
  platform.RunnerResponse runner_res = 1;
}

message AssociateCIRunnerRequest {
  uint64 repo_id = 1;
  string cluster_tag = 2;
  string description = 3;
  string share_token = 4;
}

message AssociateCIRunnerResponse {
  platform.RunnerResponse runner_res = 1;
}

message GetCIRunnersRequest {
  uint64 repo_id = 1;
}

message GetCIRunnersResponse {
  repeated platform.RunnerResponse runners_res = 1;
}

message GetCIRunnerRequest {
  uint64 repo_id = 1;
  uint64 runner_id = 2;
}

message GetCIRunnerResponse {
  platform.RunnerResponse runner_res = 1;
}

message GetCIRunnerJobsRequest {
  uint64 repo_id = 1;
  uint64 runner_id = 2;
  string platform = 4;
  string ip = 5;
  string status = 6;
  string repo = 10;
}

message GetCIRunnerJobsResponse {
  repeated platform.JobDetailsResponse jobs = 1;
}

message UpdateCIRunnerRequest {
  uint64 repo_id = 1;
  uint64 runner_id = 2;
  string description = 3;
  string cluster_tag = 4;
  optional bool default = 5;
  optional bool enabled = 6;
}

message UpdateCIRunnerResponse {
  platform.RunnerResponse runner_res = 1;
}

message DeleteCIRunnerRequest {
  uint64 repo_id = 1;
  uint64 runner_id = 2;
}

message DeleteCIRunnerResponse {}

message GetGitPipelineInputsRequest {
  // 根据 git repo name 查询
  string git_repo = 1 [(validate.rules).string = { min_len: 1 }];
  // 根据 git repo branch name 查询
  string git_branch = 2;
  // 根据 yaml file name查询
  string yaml_filename = 3 [(validate.rules).string = { min_len: 1 }];
  // 根据 git repo tag name 查询
  string git_tag = 4;
}
message GetGitPipelineInputsResponse {
  // 流水线名称
  string pipeline_name = 1;
  // inputs信息
  repeated google.protobuf.Struct inputs = 2;
}

message SaveRepoVariablesRequest {
  // 仓库标识：repoID
  uint64 repo_id = 1;
  // 变量系统结构体
  bc.varstore.VarGroup group = 2;
  // 变量创建人or更新人
  string username = 3;
}

message SaveRepoVariablesResponse {
  bc.varstore.VarGroup group = 1;
}

message GetRepoVariablesRequest {
  // 仓库标识：repoID
  uint64 repo_id = 1;
  // 变量系统访问人
  string username = 3;
}

message GetRepoVariablesResponse {
  bc.varstore.VarGroup group = 1;
}

message CreatePipelineGroupRequest {
  // 空间id
  // @required
  uint64 space_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 分组名
  // @required
  string name = 2 [(validate.rules).string = { min_len: 1 }];
  // 是否为私有分组，true为私有，false表示公开，默认为false
  bool is_private = 3;
  string username = 4 [(validate.rules).string = { min_len: 1 }];
}

message CreatePipelineGroupResponse {
  // 分组信息
  pipeline.platform.PipelineGroup pipeline_group = 1;
}

message GetPipelineGroupRequest {
  // group_id
  // @required
  uint64 group_id = 1 [(validate.rules).uint64 = { gt: 0 }];
}

message DeletePipelineGroupRequest {
  // group_id
  // @required
  uint64 group_id = 2 [(validate.rules).uint64 = { gt: 0 }];
  // username
  // @required
  string username = 4 [(validate.rules).string = { min_len: 1 }];
}

message GetPipelineGroupResponse {
  // 分组信息
  pipeline.platform.PipelineGroup pipeline_group = 1;
}

message UpdatePipelineGroupRequest {
  // 分组id
  // @required
  uint64 group_id = 2 [(validate.rules).uint64 = { gt: 0 }];
  string name = 3;
  bool is_private = 4;
  string username = 5 [(validate.rules).string = { min_len: 1 }];
}

message UpdatePipelineGroupResponse {
  // 分组信息
  pipeline.platform.PipelineGroup pipeline_group = 1;
}

message ListPipelineGroupRequest {
  // 空间id
  // @required
  uint64 space_id = 1;
  // 流水线id, 获得流水线所在的所有分组
  uint64 pipeline_id = 2;
  // username
  // @required
  string username = 3 [(validate.rules).string = { min_len: 1 }];
  // 搜索，目前支持按照分组名进行搜索
  string search = 4;
  // 分页大小
  // @required
  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  // 页码
  // @required
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];
}

message ListPipelineGroupResponse {
  // 分页信息
  shared.Pagination pagination = 1;
  // 分组列表
  repeated pipeline.platform.PipelineGroup pipeline_groups = 2;
}

message ListPipelineGroupOrderRequest {
  // 空间id
  // @required
  uint64 space_id = 1;
  // username
  // @required
  string username = 2 [(validate.rules).string = { min_len: 1 }];
  // 搜索，目前支持按照分组名进行搜索
  string search = 3;
  // 分页大小
  // @required
  int32 page_size = 100;
  // 页码
  // @required
  int32 page_num = 101;
}

message ListPipelineGroupOrderResponse {
  // 分页信息
  shared.Pagination pagination = 1;
  // 是否定义了顺序
  bool has_order = 2;
  // 分组列表
  repeated pipeline.platform.PipelineGroup pipeline_groups = 3;
}

message CreatePipelineGroupItemsRequest {
  // 所属分组id列表
  repeated uint64 group_ids = 1;
  // 流水线id列表
  repeated uint64 pipeline_ids = 2;
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message DeletePipelineGroupItemsRequest {
  // 所属分组id
  uint64 group_id = 1;
  // 流水线id列表
  repeated uint64 pipeline_ids = 2;
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message PipelineGroupEmptyResponse {}

message EditGroupOfPipelineRequest {
  // 流水线id
  uint64 pipeline_id = 1;
  // 分组id列表
  repeated uint64 group_ids = 2;
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message EditGroupOfPipelineResponse {
  // 流水线id
  uint64 pipeline_id = 1;
  // 分组id列表
  repeated uint64 group_ids = 2;
}

message CreatePipelineGroupOrderRequest {
  // 所属空间id
  uint64 space_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 分组id的顺序
  repeated uint64 group_id_orders = 2;
  string username = 3 [(validate.rules).string = { min_len: 1 }];
  // group order v2: 表示顺序、隐藏。除新增/删除的公有分组外所有分组
  repeated pipeline.platform.PipelineGroupOrder orders = 4;
}

message EditFavorPipelineRequest {
  repeated uint64 pipeline_ids = 1 [(validate.rules).repeated = { unique: true min_items: 1 }];
  bool is_favor = 2;
  string username = 3 [(validate.rules).string.min_len = 1];
}

message EditFavorPipelineResponse {
  int32 count = 1;
}

message TranslateYamlToPipelineRequest {
  string yaml_file_content = 1;
}

message TranslateYamlToPipelineResponse {
  pipeline.dsl.Pipeline pipeline = 1;
}

message VerifyYamlRequest {
  string yaml_file_content = 1;
}

message VerifyYamlResponse {
  bool is_valid = 1;
  repeated pipeline.platform.YamlErrorInfo error_infos = 2;
}

// Onesite get a pipeline req
message GetPipelineRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // not necessary, only for determine if it's favorite pipeline
  string username = 3;
  // 控制非必要逻辑运行
  bool need_last_run_id_event_blocked = 4; // 仅前端需要，用于控制流水线详情页“最新”tab的展示
  // 忽略模板信息，仅返回 pipeline 相关字段
  bool ignore_template_info = 5;
}

// Onesite get a pipeline resp
message GetPipelineResponse {
  platform.Pipeline pipeline = 1;
  bool gpt_enabled = 2;
}

// Onesite create a pipeline req
message CreatePipelineRequest {
  uint64 space_id = 6;
  // 空间类型
  space.rpc.SpaceType space_type = 7;
  // yaml / gui
  platform.EditorType editor_type = 8;
  // pipeline object when editor type is gui
  pipeline.dsl.Pipeline pipeline = 11;
  // pipeline yaml when editor type is yaml
  string yaml_file_content = 10;
  // 模版创建时带上template_id
  uint64 template_id = 24;
  // 流水线是否可见
  bool is_invisible = 30;
  // 流水线场景类型
  platform.SceneType pipeline_scene_type = 31;
  // 版本编辑方式
  platform.PipelineVersionUpdatedType updated_type = 35;
  // operator
  string username = 50 [(validate.rules).string.min_len = 1];
}

// Onesite create a pipeline resp
message CreatePipelineResponse {
  platform.Pipeline pipeline = 1;
}

// Onesite run a pipeline req
message RunPipelineRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // pipeline run trigger type, 3rd-party use TRIGGER_TYPE_API = 3 or ignore
  platform.TriggerType trigger_type = 2;
  // run params, map[string]any
  google.protobuf.Struct run_params = 3;
  // api调用时外部系统传入，生成pipeline_run的assignmentIds
  // 和custom_vars互斥，存在时可忽略custom_vars，
  repeated uint64 assignment_ids = 4;
  // 新版变量系统由前端传入的用户自定义变量，生成pipeline_run的assignmentIds
  repeated bc.varstore.VarAssignEntry custom_vars = 5;
  // 若有，可指定parent run id; 若无，请忽略
  uint64 parent_run_id = 10;
  // 通知信息，仅callback使用，没有可以忽略该字段
  repeated pipeline.dsl.Notification notifications = 11;
  // 若有，可指定pipeline编排版本; 若无，请忽略
  uint64 pipeline_version = 12;
  // 本次运行的备注
  string note = 13;
  string username = 20 [(validate.rules).string.min_len = 1];
  string jwt = 101;
}

message RunPipelineResponse {
  platform.PipelineRun pipeline_run = 1;
}

message RunPipelineLiteResponse {
  platform.LitePipelineRun pipeline_run = 1;
}

message CreatePipelineRunResponse {
  platform.LitePipelineRun pipeline_run = 1;
}

// Onesite update a pipeline req
message UpdatePipelineRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // gui or yaml
  platform.EditorType editor_type = 8;
  // if editor type is gui, use pipeline object
  dsl.Pipeline pipeline = 11;
  // if editor is yaml, use yaml file content
  string yaml_file_content = 12;
  // 版本编辑方式
  platform.PipelineVersionUpdatedType updated_type = 15;
  // operator
  string username = 20 [(validate.rules).string.min_len = 1];
  // jwt
  string jwt = 21;
  // 区分控制面
  atommanage.RegionEnum region = 22;
  // note
  string note = 23;
}

// Onesite update a pipeline resp
message UpdatePipelineResponse {
  platform.Pipeline pipeline = 1;
}

message TemplatePatchPipelineRequest {
  // 基准版本模板生成的pipeline dsl
  // @required
  pipeline.dsl.Pipeline base_template_initialized_pipeline = 1;

  // 目标版本模板生成的pipeline dsl，基于两个版本的Patch应用到pipeline
  // @required
  pipeline.dsl.Pipeline dest_template_initialized_pipeline = 2;

  // 流水线更新日志
  string note = 3;
}

message TemplatePatchPipelineResponse {
  platform.Pipeline pipeline = 1;
}

// pipeline_run 进行 重运行、取消等 operation 操作 request
message OperatePipelineRunRequest {
  // pipeline_run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // pipeline_run operation
  platform.PipelineRunOperation operation = 2;
  // 新版变量系统由前端传入的用户自定义变量，生成pipeline_run的self_assignmentIds, only for frontend
  repeated bc.varstore.VarAssignEntry custom_vars = 3;
  // content of note, length of note must between [0,255].
  string note = 5 [(validate.rules).string = { max_len: 255 }];
  // 操作流水线开始运行的参数
  platform.PipelineRunOperateStartParams pipeline_run_operate_start_params = 6;
  // operator
  string username = 20 [(validate.rules).string.min_len = 1];
  string jwt = 101;
}

// pipeline_run 进行 重运行、取消等 operation 操作的返回 request
message OperatePipelineRunResponse {
  // pipeline run status
  dsl.PipelineRunStatus status = 1;
  // if operation is rollback, return rollback info
  platform.RunRollback run_rollback = 2;
}

message ListRecentGitPipelineRunRequest {
  // 仓库名称（必填项）
  string git_repo = 1;
  // 仓库分支（筛选项）
  repeated string git_branches = 2;
  // 触发类型（筛选项）
  repeated platform.GitTriggerType trigger_types = 3;
  // 标签触发（筛选项）
  repeated string git_tags = 4;
  // 运行状态（筛选项）
  repeated dsl.PipelineRunStatus statuses = 5;
  // 触发人（筛选项）
  repeated string trigger_users = 6;
  // yaml file (筛选项)
  string yaml_file = 7;

  // 滚轮滑动
  int32 page_size = 100;
  int32 page_num = 101;
}

message ListRecentGitPipelineRunResponse {
  // 最近运行pipeline run list
  repeated platform.PipelineRun pipeline_runs = 1;
}

message GetJobRunRequest {
  // 当前 Job_run 的 id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 当前 pipeline_run 的 id
  uint64 pipeline_run_id = 2;
  // job 是否需要获取 log 信息
  bool need_logs = 5;
  // job 是否需要获取 输出 信息
  bool need_outputs = 6;
  // replace vars 仅用于前端展示
  // https://bytedance.larkoffice.com/wiki/UDt0w8lsSixsXWkGVTPcG6xEnKf、https://bytedance.larkoffice.com/docx/ZqyhdZJe2oxN07xIrCCcvId7nEc
  bool replace_vars = 7;
  // space id
  uint64 space_id = 8;
  // operator
  string username = 20 [(validate.rules).string.min_len = 1];
}

message GetJobRunResponse {
  platform.JobRun job_run = 1;
  // 用于灰度控制AI诊断工具
  bool gpt_enabled = 2;
}

message GetJobGptDataRequest {
  // job run id
  uint64 run_id = 1;
  // job rerun seq
  uint64 job_run_seq = 2;
  string username = 3;
}

message GetJobGptDataResponse {
  // 仓库基础信息
  string repo_name = 1;
  string branch = 2;
  string tag = 3;
  string yaml_filename = 4;
  string programming_language = 5;
  // 流水线运行信息
  uint64 pipeline_run_id = 6;
  uint64 orca_job_id = 7;
  string job_image = 9;
  string job_label = 10;
  string job_run_env = 11;
  // 报错对应step的执行信息
  repeated string command_steps = 12;
  string step_atom = 13;
  // 是否使用测试模型
  bool use_test_model = 14;
  // 根据之前上下文信息中流水线成功状态辅助进行错误分析
  platform.StepErrLog diff_step_log = 15;
  uint64 job_run_seq = 16;
  // job step log ci-action err log
  platform.StepErrLog step_log = 20;
  // 提示词
  string prompt = 21;
  // 知识召回提示信息
  string recall_message = 22;
  // extra info
  google.protobuf.Struct extra = 23;
}

message GetJobGptDataForBitsRequest {
  // job run id
  uint64 run_id = 1;
  // job rerun seq
  uint64 job_run_seq = 2;
  // 用户 token
  string user_token = 10;
}

message GetJobGptDataForBitsResponse {
  // 流水线信息
  uint64 pipeline_id = 1;
  string pipeline_name = 2;
  // 流水线运行信息
  uint64 pipeline_run_id = 6;
  // 原子信息
  platform.JobData job = 11;
  repeated platform.JobData predecessor_jobs = 12;
  // 内部日志信息
  platform.StepErrLog step_log = 15;
  // 提示信息用于数据召回
  platform.RecallHintMessage recall_hint_message = 16;
}

message ListFailJobRunIdsForGptRequest {
  // job类别 不同于job类型 是细化到atom type/action type的类别
  platform.JobCategory job_category = 1;
  // import策略
  platform.SelectPolicy policy = 2;
  // 开始时间
  google.protobuf.Timestamp started_at = 3;
  // 结束时间
  google.protobuf.Timestamp ended_at = 4;
  // 需要的case数量，注意，兜底限制500条上限
  int32 case_count = 5;
}

message ListFailJobRunIdsForGptResponse {
  // 所有符合条件的job的id
  repeated uint64 job_run_ids = 1;
}

message OperateJobRunRequest {
  // 当前 Job_run 的 id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // job 要执行的操作
  // operation 可取的值 枚举依据 platform.job.JobOperation
  platform.JobRunOperation operation = 2;
  // job force skip等时 需要填写原因
  string reason = 3;
  // 当前 pipeline_run 的 id
  uint64 pipeline_run_id = 4;
  // job 要执行的操作时可能需要的参数，如原子自定义的操作请求数据
  google.protobuf.Struct inputs = 10;
  // job 强制跳过时，需要打标时记录的标签信息
  repeated string tags = 11;
  uint64 space_id = 13; // 传了减少后端一次数据库查询
  // 是否来自workflow对job的操作，如果是的情况下，忽略对流水线状态的判断，仅判断当前job所允许的操作
  bool from_workflow = 12;
  // operator
  string username = 20 [(validate.rules).string.min_len = 1];
  string auth_brn = 21; // 如果使用服务账号等非真人身份，权限点需要基于此而非username进行校验，此字段在openapi层注入
}

// 当前 job_run 进行 operation 操作的返回 request
message OperateJobRunResponse {
  // operation 状态返回，重试中、取消中、完成等
  dsl.JobRunStatus status = 1;
}
// CI 触发流水线运行 request
message RunGitPipelineRequest {
  // ci 触发时，git repo name
  string git_repo = 1;
  // ci 触发时，git repo branch
  string git_branch = 2;
  // ci 触发时，git repo ci yaml文件名
  string yaml_filename = 3;
  // 触发方式，manual git_mr git_push cron webhook...
  platform.TriggerType trigger_type = 4;
  // 手动或者主从运行时会传入的用户自定义变量
  repeated bc.varstore.VarAssignEntry custom_vars = 5;
  // ci 在 merge request 页面触发需要传入, 此字段用于 codebase 主站
  // change_id 代表一个 mr 页面的唯一标识, 此处在 onesite 内命名为 mr_id
  int64 change_id = 6;
  // 手动触发传入用户传入的用户自定义信息
  google.protobuf.Struct inputs = 7;
  // ci 触发时，git repo tag
  string git_tag = 8;

  // ci 触发时，git commit sha
  string git_commit_sha = 9;

  bool is_pre_submit = 10;

  string username = 20 [(validate.rules).string.min_len = 1];

  // 手动触发传入用户自定义的环境变量
  map<string,string> envs = 21;
}

message RunGitPipelineResponse {
  platform.PipelineRun pipeline_run = 1;
}

// Only for migration, will delete later
// Onesite migrate create a pipeline req
// pipeline id；var group id; sth_at; sth_by set by source
message MigrateCreatePipelineRequest {
  uint64 space_id = 6;
  // yaml / gui
  platform.EditorType editor_type = 7;
  // pipeline object when editor type is gui
  pipeline.dsl.Pipeline pipeline = 11;
  // pipeline yaml when editor type is yaml
  string yaml_file_content = 10;
  google.protobuf.Timestamp last_run_at = 20;
  // 流水线创建时间
  google.protobuf.Timestamp created_at = 100;
  // 流水线创建人
  string created_by = 101;
  // 流水线更新时间
  google.protobuf.Timestamp updated_at = 102;
  // 流水线更新人
  string updated_by = 103;
  // 流水线归档时间
  google.protobuf.Timestamp archived_at = 104;
  // 流水线归档人
  string archived_by = 105;
}

// Onesite create a pipeline resp
message MigrateCreatePipelineResponse {
  platform.Pipeline pipeline = 1;
}

message UpdatePipelineVarsRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // Specify variable preference in pipeline running
  // including default, latest pipeline run vars by pipeline, latest pipeline run vars by pipeline runner
  pipeline.dsl.VarPreference var_preference = 2;

  // 流水线变量组, only for onesite
  bc.varstore.VarGroup var_group = 3;
  string username = 4 [(validate.rules).string = { min_len: 1 }];
}

message UpdatePipelineVarsResponse {
  pipeline.dsl.VarPreference var_preference = 1;
  bc.varstore.VarGroup var_group = 2;
}

message UpdatePipelineControlPanelRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  pipeline.dsl.ControlPanel control_panel = 2;
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message UpdatePipelineControlPanelResponse {}

message ListArchivedPipelineRunRequest {
  uint64 pipeline_id = 1;
  int32 page_size = 2 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 3 [(validate.rules).int32 = { gt: 0 }];
}

message ListArchivedPipelineRunResponse {
  repeated platform.ArchivedPipelineRun pipeline_runs = 1;
  int64 total = 2;
}

// Onesite 获取流水线列表
message ListPipelineRequest {
  // 根据 spaceID 查询
  uint64 space_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 根据流水线 id 查询流水线的详细信息
  repeated uint64 pipeline_ids = 2;
  // 根据username，查询我收藏的
  bool favor_only = 3;
  // 根据username，查询我创建的
  bool my_only = 4;
  // 根据流水线 name查询
  string search = 5;
  // 根据分组 id 查询
  uint64 group_id = 6;
  // 查询结果是否包含不可见流水线
  bool include_invisible = 7;
  // 根据控制面查询
  dsl.ControlPanel control_panel = 8;

  string username = 20 [(validate.rules).string.min_len = 1];
  // 根据 tag 查询
  devinfra.pipeline.dsl.Tag tags = 21;

  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];
}

// 流水线列表主要是Pipeline Run的信息
message ListPipelineResponse {
  shared.Pagination pagination = 1;
  repeated platform.LitePipeline pipelines = 2;
}

// 获取 pipeline_run 信息
message GetPipelineRunRequest {
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  bool with_webhook_event = 2;
  // webhook触发的分页
  int32 webhook_event_page = 3;
  // webhook触发的分页
  int32 webhook_event_pagesize = 4;
  // 是否包含空间
  bool with_space_id = 5;
  // 是否包含回滚计算
  bool with_rollback_calculate = 6;
}

// 获取 pipeline_run 信息
message GetPipelineRunResponse {
  platform.PipelineRun pipeline_run = 1;
  uint64 space_id = 2;
}

message BatchGetLatestPipelineRunsRequest {
  repeated uint64 pipeline_ids = 1;
  // 这个字段如果为 true, 会去请求 orca 服务的 status, 否则不会请求, 也就是 response 中 status 不会设置值
  bool with_status = 2;
}

message BatchGetLatestPipelineRunsResponse {
  repeated platform.SimplePipelineRun runs = 1;
}

message BatchGetPipelineRunLitesRequest {
  repeated uint64 pipeline_run_ids = 1;
  bool with_status = 2;
  bool real_time_status = 3;
}

message BatchGetPipelineRunLitesResponse {
  repeated platform.SimplePipelineRun runs = 1;
}

// Next id: 12
message ListPipelineRunRequest {
  // 根据流水线 id查询
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 根据 run ids 查询
  repeated uint64 run_ids = 2;
  // 全部运行下的筛选条件： 1. 根据触发人查询
  repeated string trigger_users = 3;
  // 2. 根据 pipeline_run状态筛选
  repeated dsl.PipelineRunStatus statuses = 5;
  // 3. 开始时间
  google.protobuf.Timestamp started_at = 6;
  // 4. 结束时间
  google.protobuf.Timestamp ended_at = 7;
  // tab类型
  platform.PipelineRunListTab tab = 8;
  // @deprecated 代码里并没有实际用到
  // 是否查询每个 job 的信息,不写默认 true 查询
  bool with_job = 10 [deprecated = true];
  // 是否查询每个 job 的信息,不写默认 false 查询
  bool without_job = 11;
  // 是否查询Pipeline编排信息，如果只需要pipeline_id，请设置为true
  bool without_pipeline = 12;
  string order_by = 20;
  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];

  // 搜索条件
  // 根据分支筛选历史功能，筛选结果同时包含git push事件的分支，以及mr事件的源分支、目标分支
  // @optional
  optional string branch = 21;
}

message ListPipelineRunResponse {
  int32 count = 1;
  repeated platform.PipelineRun pipeline_runs = 2;
  int32 blocking_count = 5;
  int32 running_count = 6;
}

message WebhookCallbackRequest {
  int64 app_id = 1;
  string run_version = 2;
  int64 event_time = 3;
  string event_type = 4;
  google.protobuf.Struct pipeline = 5;
  google.protobuf.Struct stage = 6;
  google.protobuf.Struct job = 7;
  google.protobuf.Struct step = 8;
  string pipeline_dsl = 9;
  string stage_dsl = 10;
  string job_dsl = 11;
  string step_dsl = 12;
}

message WebhookCallbackResponse {}

message ArchivePipelineRequest {
  repeated uint64 pipeline_ids = 1;
  platform.PipelineArchivedType archived_type = 2;
  string username = 20 [(validate.rules).string.min_len = 1];
}

message ArchivePipelineResponse {
  int32 count = 1;
}

message DeletePipelineRequest {
  repeated uint64 pipeline_ids = 1;
  string username = 20 [(validate.rules).string.min_len = 1];
}

message DeletePipelineResponse {
  int32 count = 1;
}

// job-atom、step-atom rerun step-list and output request
message GetJobRerunStepsOutputRequest {
  // job run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // job rerun seq
  uint64 job_run_seq = 2;
  // job 类型
  platform.AtomType atom_type = 5;
}

// job-atom、step-atom rerun step-list and output response
message GetJobRerunStepsOutputResponse {
  // job step 每次执行信息
  repeated platform.StepInfo step_infos = 1;
}

// 获取 pipeline_run 信息 by check run id
message GetPipelineRunByCheckRunIDRequest {
  uint64 check_run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
}

message GetPipelineRunByCheckRunIDResponse {
  string git_branch = 1;
  string yaml_filename = 2;
  uint64 run_seq = 3;
}

message GetJobParameterRequest {
  // job run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 当前 pipeline_run 的 id
  uint64 pipeline_run_id = 2 [(validate.rules).uint64 = { gt: 0 }];
  // getJobRun 接口返回的 job id
  string job_id = 3;
}

message GetJobParameterResponse {
  // job step型原子的参数数据
  string git_parameter = 1;
}

// atom-job run step request
message GetJobRerunStepsRequest {
  // job run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // job rerun seq
  uint64 job_run_seq = 2;
  // job 是否需要获取 输出 信息
  bool need_outputs = 5;
}

// atom-job run steps response
message GetJobRerunStepsResponse {
  // job step 每次执行信息
  repeated platform.StepInfo step_infos = 1;
}

message GetJobLogRequest {
  // job run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // job rerun seq
  uint64 job_run_seq = 2;
}

message GetJobLogResponse {
  // job 服务型原子的每次执行的日志信息
  repeated platform.StepInfo step_infos = 1;
}

// 获取 pipeline_run 信息 by pipeline_id && run_seq
message GetPipelineRunBySeqRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  uint64 run_seq = 2 [(validate.rules).uint64 = { gt: 0 }];
}

// 获取 pipeline_run 信息
message GetPipelineRunBySeqResponse {
  platform.PipelineRun pipeline_run = 1;
}

message GetJobStepLogRequest {
  // job run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // step uid name
  string step_uid = 2;
  // engine step id
  int64 step_id = 3;
  // job run seq
  int64 run_seq = 4;
  int32 page_size = 10;
  int32 page_num = 11;
}

message GetJobStepLogResponse {
  // job step log ci-action log
  platform.StepLog step_log = 1;
}

message NotePipelineRunRequest {
  // run id
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];

  // content of note, length of note must between [0,255].
  string note = 2 [(validate.rules).string = { max_len: 255 }];

  // username
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message NotePipelineRunResponse {}

message UpdateParentPipelineRunRequest {
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  uint64 parent_run_id = 2 [(validate.rules).uint64 = { gt: 0 }];
}

message UpdateParentPipelineRunResponse {}

message RecommendPipelineVarsRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 当前构建是从哪次构建上重新运行的
  uint64 from_pipeline_run_id = 2;

  string username = 100 [(validate.rules).string.min_len = 1];
}

message RecommendPipelineVarsResponse {
  repeated bc.varstore.VarInput custom_vars = 1;
}

// CI页面获取git流水线
message GetGitPipelineRequest {
  // 根据 git repo name 查询
  string git_repo = 1 [(validate.rules).string = { min_len: 1 }];
  // 根据 git repo branch name 查询
  string git_branch = 2;
  // 根据 yaml file name查询
  string yaml_filename = 3 [(validate.rules).string = { min_len: 1 }];
  // 根据 git repo tag name 查询
  string git_tag = 4;
}

// CI页面获取git流水线
message GetGitPipelineResponse {
  platform.GitPipeline pipeline = 1;
  // 远程分支/文件是否删除
  bool is_deleted = 2;
  // ci_gpt白名单使用
  bool gpt_enabled = 3;
}

// CI 获取git流水线列表
message ListGitPipelineRequest {
  // 根据流水线 name查询
  string search = 1;
  // 根据 git repo name 查询
  string git_repo = 2;
  // 根据 git repo branch name 查询
  string git_branch = 3;
  // 根据 git repo tag name 查询
  string git_tag = 4;
  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];
}

// 流水线列表主要是Pipeline Run的信息
message ListGitPipelineResponse {
  shared.Pagination pagination = 1;
  repeated platform.LiteGitPipeline pipelines = 2;
}

// CI页面创建Git流水线
message CreateGitPipelineRequest {
  // git repo name
  string git_repo = 3;
  // git repo branch name
  string git_branch = 4;
  // git repo yaml pipeline name
  string yaml_filename = 5;
  // 流水线创建方式 yaml / gui
  platform.EditorType editor_type = 6;
  // 流水线 gui创建pipeline
  pipeline.dsl.Pipeline pipeline = 11;
  // 流水线 yaml文件创建pipeline
  string yaml_file_content = 12;
}

// CI页面创建Git流水线
message CreateGitPipelineResponse {
  platform.GitPipeline pipeline = 1;
}

// Git流水线更新
message UpdateGitPipelineRequest {
  // git repo name
  string git_repo = 3;
  // git repo branch name
  string git_branch = 4;
  // git repo yaml pipeline name
  string yaml_filename = 5;
  // 流水线编辑方式 yaml / gui
  platform.EditorType editor_type = 6;
  // 流水线，对标yaml文件信息
  pipeline.dsl.Pipeline pipeline = 11;
  // 流水线 yaml文件创建pipeline
  string yaml_file_content = 12;
  // git repo tag name
  string git_tag = 13;
}

// Git流水线更新
message UpdateGitPipelineResponse {
  platform.GitPipeline pipeline = 1;
}

message ListPostTriggersRequest {
  // 根据 git repo branch file run_id 查询
  repeated platform.PostTrigger post_triggers = 1;
}

message ListPostTriggersResponse {
  // 子流水线 pipeline run list
  repeated platform.PipelineRun pipeline_runs = 1;
}

message ListGitPipelineRunRequest {
  // 根据 git repo name 查询
  string git_repo = 1;
  // 根据 git repo branch name 查询
  string git_branch = 2;
  // 根据 yaml file name查询
  string yaml_filename = 3;
  // 根据触发人查询
  string triggered_by = 4;
  // 根据 pipeline_run任务的当前状态
  dsl.PipelineRunStatus status = 5;
  // 根据 commit_sha 查询
  string commit_sha = 6;
  // 根据 git repo tag name 查询
  string git_tag = 7;
  // 是否查询每个 job 的信息,不写默认 true 查询
  bool with_job = 10;
  google.protobuf.Timestamp since = 11;
  google.protobuf.Timestamp until = 12;
  // 根据多个触发人查询
  repeated string trigger_users = 13;
  // 根据多个 pipeline_run状态筛选
  repeated dsl.PipelineRunStatus statuses = 14;
  repeated platform.TriggerType trigger_types = 15;
  // 需要排序的key name 如 pipelineId\createAt等
  string order_by = 20;
  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];
}

message ListGitPipelineRunResponse {
  int32 count = 1;
  repeated platform.PipelineRun pipeline_runs = 2;
}

message GetGitPipelineRunBySeqRequest {
  // 根据 git repo name 查询
  string git_repo = 1;
  // 根据 git repo branch name 查询
  string git_branch = 2;
  // 根据 yaml file name查询
  string yaml_filename = 3;
  // 根据 run_seq 查询
  uint64 run_seq = 4 [(validate.rules).uint64 = { gt: 0 }];
  // 根据 git repo tag name 查询
  string git_tag = 5;
}

message GetGitPipelineRunBySeqResponse {
  platform.PipelineRun pipeline_run = 1;
}

message CheckGitEventRequest {
  string git_event_type = 1;
  google.protobuf.Struct git_event_body = 2;
}

message CheckGitEventResponse {}

message GetAvailableVarsRequest {
  // @required
  uint64 space_id = 1;
  // 流水线和模版id必须存在一个，如果都存在，仅pipeline_id生效
  // 流水线ID
  uint64 pipeline_id = 2;
  // 模版ID
  uint64 template_id = 3;
  platform.CreateEntityType create_from = 4;
}

message GetAvailableVarsResponse {
  // 自定义变量(除模版自定义变量外的其他自定义变量)
  repeated bc.varstore.AvailableVar custom_vars = 1;
  // 自定义模版变量
  repeated bc.varstore.AvailableVar custom_template_vars = 3;
  // 系统变量
  repeated bc.varstore.AvailableVar system_vars = 2;
}

// 迁移通知
message MigrateNotificationsRequest {
  uint64 instance_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  InstanceType instance_type = 2;
  repeated uint64 notification_ids = 3;
  string username = 4 [(validate.rules).string = { min_len: 1 }];
}

enum InstanceType {
  INSTANCE_TYPE_UNSPECIFIED = 0;
  INSTANCE_TYPE_PIPELINE = 1;
  INSTANCE_TYPE_PIPELINE_RUN = 2;
  INSTANCE_TYPE_TEMPLATE = 3;
  INSTANCE_TYPE_REPO = 4;
}

message MigrateNotificationsResponse {
  repeated pipeline.dsl.Notification notifications = 1;
}

message GetPipelineTagSettingRequest {
  // 查询空间的id
  // @required
  uint64 space_id = 1 [(validate.rules).uint64 = { gt: 0 }];
}

message GetPipelineTagSettingResponse {
  // @required
  platform.PipelineTagSetting tag = 1;
}

message SavePipelineTagSettingRequest {
  // 本空间的id
  // @required
  uint64 space_id = 1 [(validate.rules).uint64 = { gt: 0 }];

  platform.PipelineTagSetting tag = 2;
  // operator
  string username = 20 [(validate.rules).string.min_len = 1];
}

message SavePipelineTagSettingResponse {}

message UpdateJobRunTagRequest {
  // 当前 Job_run 的 id
  // @required
  uint64 run_id = 1 [(validate.rules).uint64 = { gt: 0 }];

  repeated string tags = 2;
  // operator
  string username = 20 [(validate.rules).string.min_len = 1];
}

message UpdateJobRunTagResponse {}

message GetNotificationGroupRequest {
  // @required
  uint64 group_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  uint64 version = 2 [(validate.rules).uint64 = { gt: 0 }];
}

message GetNotificationGroupResponse {
  pipeline.dsl.NotificationGroup notification_group = 1;
}

message SaveNotificationGroupRequest {
  // group_id 为 0 时，表示创建新的 group
  dsl.NotificationGroup notification_group = 1;
  string operated_by = 3;
}

message SaveNotificationGroupResponse {
  dsl.NotificationGroup notification_group = 1;
}

message OperatePipelineRunListRequest {
  repeated uint64 run_ids = 1;
  platform.PipelineRunOperation operation = 2;
  uint64 pipeline_id = 5;
  string git_repo = 10;
  string git_branch = 11;
  string yaml_filename = 12;
  string git_tag = 13;
  string username = 20 [(validate.rules).string.min_len = 1];
}

message OperatePipelineRunListResponse {
  // @required total pipeline runs
  int32 total = 1;
  // @required failed pipeline runs
  int32 failed_count = 2;
}

message GetPipelineRunsByMrRequest {
  // codebase repo id
  // @required
  uint64 repo_id = 1 [(validate.rules).uint64 = { gt: 0 }];

  // codebase change id
  // @required
  int64 change_id = 2 [(validate.rules).int64 = { gt: 0 }];
}

message GetPipelineRunsByMrResponse {
  repeated platform.PipelineRun pipeline_runs = 1;
}

message GetPipelineConcurrencyRequest {
  uint64 pipeline_id = 1;
  int32 page_size = 100;
  int32 page_num = 101;
}

message GetPipelineConcurrencyResponse {
  // @deprecated
  repeated platform.PipelineRun blocking_pipeline_runs = 1;
  // @deprecated
  repeated platform.PipelineRun ongoing_pipeline_runs = 2;
  repeated platform.PipelineRun pipeline_runs = 3;
  shared.Pagination pagination = 4;
  pipeline.dsl.Concurrency concurrency = 5;
  int64  blocking_pipeline_run_count = 6;
  int64  ongoing_pipeline_run_count = 7;
}

message ListArchivedPipelineRequest {
  // 根据 spaceID 查询
  uint64 space_id = 1;
  // 根据流水线 name查询
  string search = 5;

  int32 page_size = 100;
  int32 page_num = 101;
}

// 流水线列表主要是Pipeline Run的信息
message ListArchivedPipelineResponse {
  shared.Pagination pagination = 1;
  repeated platform.Pipeline pipelines = 2;
}

message RecoverArchivedPipelineRequest {
  // 根据流水线 id 查询流水线的详细信息
  repeated uint64 pipeline_ids = 2;
  bool from_template = 3;
  string username = 5;
}

message RecoverArchivedPipelineResponse {
  // recover succeeded, pipeline id - pipeline url
  repeated platform.RecoverArchivedPipelineResult succeeded_pipelines = 1;
  // recover failed, pipeline id - reason
  repeated platform.RecoverArchivedPipelineResult failed_pipelines = 2;
}

message CopyPipelineRequest {
  uint64 pipeline_id = 1;
  infra.I18nString pipeline_name = 2;
  infra.I18nString pipeline_desc = 3;
  repeated uint64 space_ids = 5;
  // 保持与模板的绑定关系
  bool keep_binding = 6;
  string username = 10;
}
message CopyPipelineResponse {
  repeated platform.Pipeline pipelines = 1;
}

message GetPipelineRunVarsRequest {
  uint64 run_id = 1;
}

message GetPipelineRunVarsResponse {
  repeated bc.varstore.VarAssignment custom_vars = 1;
  repeated bc.varstore.VarAssignment system_vars = 2;
  repeated bc.varstore.VarAssignment predefined_environment_vars = 4;
  repeated bc.varstore.VarAssignment custom_environment_vars = 5;
  google.protobuf.Struct init_params = 6;
  // fullName: CiteInfo
  map<string, pipeline.dsl.CitedInfo> cited_by = 3;
}

message ListWorkbenchPipelineRequest {
  string username = 1 [(validate.rules).string.min_len = 1];

  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];
}

// 流水线列表主要是Pipeline Run的信息
message ListWorkbenchPipelineResponse {
  shared.PageInfo page_info = 1;
  repeated platform.LitePipeline pipelines = 2;
}

message ListPipelineVersionRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  int32 page_size = 100 [(validate.rules).int32 = { gt: 0 }];
  int32 page_num = 101 [(validate.rules).int32 = { gt: 0 }];
}

message ListPipelineVersionResponse {
  // @required
  shared.Pagination pagination = 1;
  // @required
  repeated platform.PipelineVersion pipeline_versions = 2;
}

message GetPipelineVersionDiffRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 对比version传递
  uint64 from_version = 2 [(validate.rules).uint64 = { gt: 0 }];
  uint64 to_version = 3 [(validate.rules).uint64 = { gt: 0 }];
  // edit保存前的preview diff
  pipeline.dsl.Pipeline pipeline = 4; // 当前修改的pipeline
}

message GetPipelineVersionDiffResponse {
  // @required 基准模板的yaml preview
  platform.PipelineYamlPreview from_pipeline_yaml_preview = 1;
  // @required 目标模板的yaml preview
  platform.PipelineYamlPreview to_pipeline_yaml_preview = 2;
}

message ListRecentPipelineRequest {
  uint64 space_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  string username = 2 [(validate.rules).string.min_len = 1];
}

message ListRecentPipelineResponse {
  repeated platform.LitePipeline pipelines = 1;
}

message GetJobRunVarsRequest {
  uint64 job_run_id = 1;
}

// 获取 job 变量 信息 response
message GetJobRunVarsResponse {
  repeated bc.varstore.VarAssignment custom_vars = 1;
  repeated bc.varstore.VarAssignment system_vars = 2;
  repeated bc.varstore.VarAssignment predefined_environment_vars = 3;
  repeated bc.varstore.VarAssignment custom_environment_vars = 4;
  google.protobuf.Struct job_context = 5;
  // Name：JobOutput
  map<string, platform.JobOutput> prefix_job_outputs = 6;
  // Name: ContextExtra
  map<string, platform.ContextExtra> job_context_extra = 7;
}

message RollbackPipelineVersionRequest {
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  uint64 pipeline_version = 2 [(validate.rules).uint64 = { gt: 0 }];
  string username = 10 [(validate.rules).string.min_len = 1];
}

message RollbackPipelineVersionResponse {
  platform.Pipeline pipeline = 1;
}

message PreCheckPipelineRequest {
  // pipeline id
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 流水线触发方式,manual git cron webhook(前端可不传)
  platform.TriggerType trigger_type = 2;
  // bc遗留，传入触发的初始参数, map[string]interface{}
  google.protobuf.Struct run_params = 3;
  // api调用时外部系统传入，生成pipeline_run的upstream_assignmentIds
  // 和variables互斥，存在时可忽略variables，
  repeated uint64 assignment_ids = 4;
  // 新版变量系统由前端传入的用户自定义变量，生成pipeline_run的self_assignmentIds
  repeated bc.varstore.VarAssignEntry custom_vars = 5;
  // job uid, 用于单个job的precheck
  string job_id = 6;
  // stage uid，用于定位唯一job
  string stage_id = 7;
  // username
  string username = 10;
}

message PreCheckPipelineResponse {
  // 校验成功的数量
  int32 check_passed_total = 1;
  // 校验失败的数量
  int32 check_failed_total = 2;
  // 单个job的校验是否通过
  bool single_job_check_result = 3;
  // 校验详情
  repeated platform.PreCheckDetail check_details = 4;
}

// Pipeline: 更新流水线触发器
message UpdatePipelineTriggersRequest {
  // pipeline id
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];

  // Specify triggers that run this pipeline automatically
  repeated pipeline.dsl.Trigger triggers = 2;

  // 更新人
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message UpdatePipelineTriggersResponse {
  dsl.Pipeline pipeline = 1;
}

// Pipeline: 更新流水线变量
message UpdatePipelineVarGroupRequest {
  // pipeline id
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];

  // Specify variable preference in pipeline running
  // including default, latest pipeline run vars by pipeline, latest pipeline run vars by pipeline runner
  pipeline.dsl.VarPreference var_preference = 2;

  // 流水线变量组, only for onesite
  bc.varstore.VarGroup var_group = 3;

  // 更新人
  string username = 4 [(validate.rules).string = { min_len: 1 }];
}

message UpdatePipelineVarGroupResponse {
  dsl.Pipeline pipeline = 1;
}

message UpdatePipelineDslResponse {
  dsl.Pipeline pipeline = 1;
}
message UpdatePipelineRoleResponse {
  dsl.Pipeline pipeline = 1;
}

message UpdatePipelineDslRequest {
  repeated dsl.Stage stages = 1;
  uint64 pipeline_id = 2;
}

message UpdatePipelineRoleRequest {
  repeated platform.RoleBinding grant_bindings = 1;
  repeated platform.RoleBinding revoke_bindings = 2;
  uint64 pipeline_id = 3;
  // 用户的jwt_token
  string jwt = 100 [(validate.rules).string = { min_len: 1 }];
}

// leave it empty, not used for now
message JetExprOption {}

message PythonExprOption {
  bool keep_expr_on_error = 1;
}

message EvalExpressionRequest {
  // variable context for expression evaluation
  google.protobuf.Struct expr_context = 1;
  // inputs for expression evaluation, datas with expressions
  google.protobuf.Struct inputs = 2;
  dsl.ExprSyntax expr_syntax = 10;
  oneof options {
    JetExprOption jet_option = 20;
    PythonExprOption python_option = 21;
  }
}

message EvalExpressionResponse {
  google.protobuf.Struct result = 1;
}

message MigratePipelineRequest {
  // @required
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // @required
  uint64 to_space_id = 2 [(validate.rules).uint64 = { gt: 0 }];
  // 是否跳过重命名
  bool no_rename = 3;
  // @required
  string username = 10;
}

message MigratePipelineResponse {
  platform.Pipeline pipeline = 1;
}

message AllowPipelineMigrationRequest {
  // @required
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // @required
  uint64 to_space_id = 2 [(validate.rules).uint64 = { gt: 0 }];
}
message AllowPipelineMigrationResponse {
  // @required
  repeated atommanage.AtomUniqueIdAndVersion unopened_atoms = 2;
}

message DuplicatePipelineJobRequest {
  // @required
  pipeline.dsl.Job job = 1;
  // @required
  string username = 2 [(validate.rules).string = { min_len: 1 }];
  atommanage.RegionEnum region = 3;
}

message DeletePipelineJobDuplicateRequest {
  // @required
  // job duplicate id
  uint64 id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // @required
  string username = 2 [(validate.rules).string = { min_len: 1 }];
  atommanage.RegionEnum region = 3;
}

message GetDuplicatePipelineJobRequest {
  // @required
  string username = 1 [(validate.rules).string = { min_len: 1 }];
  // 根据 spaceID 查询
  uint64 space_id = 2;
  atommanage.RegionEnum region = 3;
}

message CreateNotificationRequest {
  InstanceType instance_type = 1;
  uint64 instance_id = 2 [(validate.rules).uint64 = { gt: 0 }];
  pipeline.dsl.Notification notification = 3;
  string username = 4 [(validate.rules).string = { min_len: 1 }];
}

message CreateNotificationResponse {
  pipeline.dsl.Notification notification = 1;
}

message UpdateNotificationRequest {
  // 通知id
  uint64 notification_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // 通知
  pipeline.dsl.Notification notification = 2;
  string username = 3 [(validate.rules).string = { min_len: 1 }];
}

message UpdateNotificationResponse {
  pipeline.dsl.Notification notification = 1;
}

message DeleteNotificationRequest {
  // 通知id
  uint64 notification_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  string username = 2 [(validate.rules).string = { min_len: 1 }];
}

message DeleteNotificationResponse {}

message GetNotificationsRequest {
  InstanceType instance_type = 1;
  uint64 instance_id = 2 [(validate.rules).uint64 = { gt: 0 }];
}

message GetNotificationsResponse {
  repeated pipeline.dsl.Notification notifications = 1;
}

// 获取原子信息List接口，platform层自定义请求和返回结构体（兼容原子市场2个api）
message ListAtomRequest {
  // 根据 classify 分类 查询
  atommanage.ClassifyEvent classify = 1;
  atommanage.FilterEvent filter = 2;
  bool is_archived = 3;
  // 根据 关键字查询
  string keyword = 4;
  // 根据原子市场 atom id 查询
  repeated int64 ids = 5;
  // 根据原子市场 atom unique_id 查询
  repeated string unique_ids = 6;
  // 是否查询 service原子的表单信息
  bool with_service_atom_meta = 7;
  // 原子类型
  atommanage.AtomTypeEvent atom_type = 8;
  // 空间 id
  int64 space_id = 9;
  // 根据 region 查询
  atommanage.RegionEnum region = 10;
  // @required
  int32 page_size = 100;
  // @required
  int32 page_num = 101;
  // 获取信息的人员
  string username = 102 [(validate.rules).string = { min_len: 1 }];
  string jwt = 103;
}

message GetAtomVersionRequest {
  // 原子服务 id
  int64 id = 1;
  string unique_id = 2;
  platform.AtomType atom_type = 3;
  // 获取信息的人员
  string username = 101 [(validate.rules).string = { min_len: 1 }];
  string jwt = 102;
}

// 获取原子详细数据 Request
message GetAtomInputFormRequest {
  // 原子服务 id
  int64 id = 1;
  string unique_id = 2;
  // 原子服务 version
  string version = 3;
  // 原子服务 is_published
  bool is_published = 4;
  platform.AtomType atom_type = 5;
  atommanage.RegionEnum region = 6;
  int64 space_id = 7;
  // 获取信息的人员
  string username = 101 [(validate.rules).string = { min_len: 1 }];
  string jwt = 102;
}

// 原子收藏，参数和 atom接口保持一致
message EditFavoriteAtomRequest {
  // atom id
  uint64 atom_id = 1;
  // 是否收藏
  bool is_favor = 2;
  // atom type
  platform.AtomType atom_type = 3;
  // 获取信息的人员
  string username = 101 [(validate.rules).string = { min_len: 1 }];
  string jwt = 102;
}

message GetPipelineScheduledDeleteRequest {
  // 根据流水线 id查询
  uint64 pipeline_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  string username = 2;
}

message GetPipelineScheduledDeleteResponse {
  bool delete_within_one_day = 1;
}

message ScheduledArchivedPipelineRequest {}

message ScheduledArchivedPipelineResponse {}

message GetJobRunsByPipelineRunIDAndAtomUIDRequest {
  // pipeline run id
  uint64 pipeline_run_id = 1 [(validate.rules).uint64 = { gt: 0 }];
  // job name
  string job_name = 2;
}

message GetJobRunsByPipelineRunIDAndAtomUIDResponse {
  repeated platform.JobRun job_runs = 1;
}
