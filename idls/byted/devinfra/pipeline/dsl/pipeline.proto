syntax = "proto3";

package byted.devinfra.pipeline.dsl;

import "idls/byted/bc/varstore/group.proto";
import "idls/byted/devinfra/pipeline/dsl/trigger.proto";
import "idls/byted/devinfra/pipeline/dsl/notification.proto";
import "idls/byted/devinfra/pipeline/dsl/variable.proto";
import "idls/byted/devinfra/pipeline/dsl/runner.proto";
import "idls/byted/devinfra/infra/i18n.proto";
import "idls/byted/devinfra/authz/authz.proto";
import "google/protobuf/struct.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pipeline/dsl/dslpb";

message Pipeline {
  // Schema version
  // - v1: https://bytedance.feishu.cn/wiki/wikcn98ZWMAHiIwkxhJNeBaH5Bd#
  // - v2: current version
  string schema_version = 1;

  // Identity of this pipeline, hidden from user-perspective
  uint64 id = 2;

  // Name of this pipeline
  // Value: <multi_lang_string>
  infra.I18nString name = 3 [(infra.i18n) = {
    key_format: "#id/name",
  }];

  // Description of this pipeline
  // Value: <multi_lang_string>
  infra.I18nString desc = 4 [(infra.i18n) = {
    key_format: "#id/desc",
  }];

  // Specify which syntax used in expressions
  // Possible values:
  // - jet: JET template engines with some extra functions, ref:
  // https://bytedance.feishu.cn/wiki/wikcn98ZWMAHiIwkxhJNeBaH5Bd#nKR6yc
  // - python: Python-like interpreter, ref: https://bytedance.feishu.cn/wiki/wikcniJnZVFUnnoJdNsjF9Rrxig
  ExprSyntax expr_syntax = 5;

  // Defines stages in this pipeline, stages run in sequential
  // A stage is composed of jobs
  repeated Stage stages = 6;

  // Limit how many instances of a pipeline can run at the same time and how to handle queuing runs when the limit
  // reached By default, there is no limit of concurrent runs. And queuing runs will be `blocking` when limit reached
  Concurrency concurrency = 8;

  // Global environment variables, works for all script-type jobs in this pipeline
  map<string, string> env = 9;

  // Specify variable preference in pipeline running
  // including default, latest pipeline run vars by pipeline, latest pipeline run vars by pipeline runner
  VarPreference var_preference = 10;

  // Specify triggers that run this pipeline automatically
  repeated Trigger triggers = 12;

  // Send notifications(e.g., Lark messages, webhooks) on specified pipeline run statuses
  repeated Notification notifications = 13;

  // 流水线变量组, only for onesite
  bc.varstore.VarGroup var_group = 14;

  // 变量被哪些地方引用，目前只有原子服务 key为变量fullName, 服务端计算，调用设置无效
  map<string, CitedInfo> cited_by = 15;

  // 流水线权限, only onesite
  repeated authz.Authorization authorizations = 16;

  // 流水线 基础配置 相关信息
  Tag tag = 17;

  // lock info
  repeated LockInfo lock_infos = 20;

  // trigger group 和原 triggers 字段相同，增加额外的group和版本信息
  TriggerGroup trigger_group = 21;

  // notification group 和原 notifications 字段相同，增加额外的group和版本信息
  NotificationGroup notification_group = 22;

  // options of pipeline variable
  PipelineVarOption var_option = 23;

  ControlPanel control_panel = 24;

  uint64 expired_days = 25;
}

message CitedInfo {
  repeated infra.I18nString atom_names = 1;
}

message Concurrency {
  // How many instances of a pipeline can run at the same time, negative values means no limit
  // By default, there's no limit(-1).
  int32 max = 1;

  // Cancel the oldest in-progress run when limit reached
  // By default, newly created runs will be in blocking status until an old in-progress run finished.
  bool new_run_first = 2;

  // later:
  //
  // group key of pipelines in a git repository
  // string group = 3;
}

message Stage {
  // Identity of this stage, unique in a pipeline, hidden from user-perspective
  string id = 1;

  // Name of this stage
  // <multi_lang_string>
  infra.I18nString name = 2;

  // Run jobs in this stage only when a condition is met
  // 产品侧不展示
  string if = 3;

  // Specify jobs in this stage
  // By default, jobs in a stage are run in parallel
  // format: <identity>: <job>
  repeated Job jobs = 4;
}

message Job {
  // id of job, the key of job map in yaml
  string id = 1;

  // Name of this job
  // <multi_lang_string>
  infra.I18nString name = 2;

  // Run jobs in this stage only when a condition is met.
  string if = 3;
  // 跳过执行，与if互斥 https://meego.larkoffice.com/bitsdevops/story/detail/4970005816
  string if_skip = 107;

  // Specify ids of jobs that this job is depending on
  // (-- api-linter: core::0158::response-plural-first-field=disabled --)
  repeated string depends_on = 4;

  // 功能同 if. 用于 UI 模式下, 设置用户不可见的 if 条件, 如选择器场景.
  // 当 if 和 extra_if 同时设置, 它们之间是 And 关系.
  string extra_if = 5;

  // Start this job manually
  bool manual = 6;

  // for atom ref: [10, 20)
  // e.g., 'job_atom/scm_compile@v1'
  string uses = 10;

  // atom inputs
  google.protobuf.Struct inputs = 11;

  // bits job outputs
  repeated Output outputs = 12;

  // env upgrade
  repeated Env envs = 13;

  // 缓存相关
  repeated Cache caches = 14;

  // 指定输入产物
  repeated ArtifactFilter input_artifacts = 15;

  RunnerSpec runs_on = 23;

  // environment variables of this job
  map<string, string> env = 22;

  // exclusive with "uses"
  repeated Step steps = 24;

  // works only when you choose a Kubernetes engine in runs_on
  repeated ContainerService services = 25;

  // allow_push give job git-push permission
  bool allow_push = 26;

  // auto_checkout add actions/checkout for all codebase-ci jobs
  bool auto_checkout = 27;

  // auto_cache add actions/cache for all codebase-ci jobs
  bool auto_cache = 28;

  // auto_git_lfs_cache add actions/git-lfs-cache for all codebase-ci jobs
  bool auto_git_lfs_cache = 29;

  // auto_go_module_proxy add actions/go-module-proxy
  bool auto_go_module_proxy = 30;

  // true is support do some operation in case of job-run timeout
  bool support_timeout = 100;

  // <duration>
  // default: 1 hour(for script-type jobs), 90 days(for service-type jobs),unit seconds
  // max: same as default
  int32 timeout = 101;

  OnFailedRetryPolicy retry = 102;

  // specify actions to do when job failed
  OnFailed on_failed = 103;

  // specify actions to do on timeout
  // by default, script-type jobs will fail on timeout, and for service-type jobs, it's a nop
  OnTimeout on_timeout = 104;

  // Specify manual operations
  JobOperations manual_operations = 105;

  // Specify whether the jobs depending on current job to run or not when if evaluated to false.
  OnIgnored on_ignored = 106;

  // Enable rollback or not
  bool enable_pipeline_rollback = 120;
  // Artifact artifact = 2;
  // Cache cache = 1;
  // repeated VolumeMount mounts = 26;

  repeated Notification notifications = 200;

  // For front-end use, record the number of user_form_required_inputs have not been write
  int32 missing_required_inputs = 301;

  // Specify feature env the job runs on
  string run_env = 350;

  // Support pipeline-job schedule set can not actionable operations
  repeated JobRunOperation disable_operations = 351;
  repeated JobRunOperationWithPermission job_run_operations = 352;
  string job_run_operations_used = 353; //  job_run_operations 或者 disable_operations 代表用哪一个字段，为空也是disable_operations。用于区分上面两者都为空到底是用哪一个。
}

// artifact provider
enum ExternalArtifactProvider {
  EXTERNAL_ARTIFACT_PROVIDER_UNSPECIFIED = 0;
  EXTERNAL_ARTIFACT_PROVIDER_SCM = 1;
}

// artifact filter: 用于在job中指定具体的依赖的产物信息
message ArtifactFilter {
  string from_job_id = 1;
  ExternalArtifactProvider provider = 2;
}

message JobOperations {
  bool skip = 1;

  bool cancel = 2;

  bool force_skip = 3;

  bool rollback = 4;
}

message OnFailedRetryPolicy {
  // 重试次数
  int32 max = 1;

  // 间隔时间, 单位 秒
  int32 interval = 2;

  // later:
  //
  // retry jobs for only specific failure cases
  // e.g., when: runner_system_failure
  // string when = 3;
}

message ContainerService {
  string id = 1;

  string name = 2;

  string image = 3;

  map<string, string> env = 4;

  repeated string commands = 5;

  // todo: more from healthcheck
  // detail: https://docs.docker.com/compose/compose-file/compose-file-v3/#healthcheck
}

enum OnTimeout {
  ON_TIMEOUT_UNSPECIFIED = 0;

  // default for a script-type job
  ON_TIMEOUT_FAIL = 1;

  ON_TIMEOUT_SKIP = 2;

  ON_TIMEOUT_CANCEL = 3;
}

enum OnIgnored {
  ON_IGNORED_UNSPECIFIED = 0;

  ON_IGNORED_ABORT = 1;  // 当前 job ignored 时, (直接&间接)依赖它的 job 默认不会运行.

  ON_IGNORED_CONTINUE = 2;  // 当前 job ignored 时, (直接&间接)依赖它的 job 默认会运行.
}

message Output {
  string key = 1;

  string value = 2;

  string description = 3;
}

message Env {
  string key = 1;

  string value = 2;

  string description = 3;
}

message Step {
  // uniq in a job
  string id = 1;

  string name = 2;

  string if = 3;

  // OR: depends_on
  // depends_on: specify steps this step depending on
  // zero value means depending on all previous jobs
  // ['-'] means this step has no previous step
  bool parallel_with_next_step = 4;

  OnFailed on_failed = 5;

  OnFailedRetryPolicy retry = 6;

  // for action ref: [10, 20)
  // e.g.
  // - step_atom/scm_compile@v1
  // - step_atom/clone_code
  string uses = 10;

  google.protobuf.Struct inputs = 11;

  repeated string commands = 20;
}

enum OnFailed {
  ON_FAILED_UNSPECIFIED = 0;

  // 失败, 「服务性」(默认)+「脚本型」
  ON_FAILED_FAIL = 1;

  // 重试, 「服务性」+「脚本型」
  ON_FAILED_RETRY = 2;

  // continue on error：失败时跳过。即失败的 job状态是失败，并且会继续运行后续依赖的 job，「脚本型」
  ON_FAILED_CONTINUE = 5;

  // ignore error：失败时成功。即失败的 job状态变为成功，并且会继续运行后续依赖的 job，「脚本型」
  ON_FAILED_IGNORE = 6;
}

enum VarPreference {
  VAR_PREFERENCE_UNSPECIFIED = 0;
  VAR_PREFERENCE_BY_DEFAULT = 1;
  VAR_PREFERENCE_BY_PIPELINE = 2;
  VAR_PREFERENCE_BY_RUNNER = 3;
}

message Tag {
  // 流水线基础信息 psm
  string psm = 1;
  // 用户自定义标签
  repeated string customs = 2;
  // env: boe\ppe 标签
  repeated string envs = 3;
  // region: cn\i18n 标签
  repeated string regions = 4;
  // 资源: tce\faas 标签
  repeated string resources = 5;
}

message LockInfo {
  // field path, for example: inputs.scm_configs.revision
  string field_path = 1;
}

message PipelineVarOption {
  // 是否将自定义变量注入build_context
  bool allow_custom_vars_in_build_context = 1;
}

enum ControlPanel {
  CONTROL_PANEL_UNSPECIFIED = 0;
  CONTROL_PANEL_CN = 1;
  CONTROL_PANEL_USTTP = 2;
  CONTROL_PANEL_EUTTP = 3;
  CONTROL_PANEL_CN_STAGE = 4;
}

// job 可进行的
// 操作（operation）；注意:由于历史原因，这里只能新建一个结构体，但要和pipeline.platform.JobRunOperation保持一致
enum JobRunOperation {
  // job run
  JOB_RUN_OPERATION_UNSPECIFIED = 0;

  // job run
  JOB_RUN_OPERATION_RUN = 1;

  // job rerun
  JOB_RUN_OPERATION_RERUN = 2;

  // job cancel
  JOB_RUN_OPERATION_CANCEL = 3;

  // job skip
  JOB_RUN_OPERATION_SKIP = 4;

  // job force skip
  JOB_RUN_OPERATION_FORCE_SKIP = 5;

  // job rollback
  JOB_RUN_OPERATION_ROLLBACK = 6;

  // job reschedule,restart from some job
  JOB_RUN_OPERATION_RESCHEDULE = 7;

  // atom user define method
  JOB_RUN_OPERATION_CUSTOM = 8;

  // job can tag
  JOB_RUN_OPERATION_TAG = 9;

  // failed
  JOB_RUN_OPERATION_FAIL = 10;
}

message JobRunOperationWithPermission {
  JobRunOperation support_operation = 1; // 支持的操作
  repeated string allow_roles = 2; // 允许的角色
  repeated string allow_usernames = 3; // 允许的用户，支持变量表达式
}