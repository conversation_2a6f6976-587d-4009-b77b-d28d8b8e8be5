syntax = "proto3";

package byted.devinfra.infra.rpc;

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/infra/rpcpb";

// Standard ErrorInfo 标准错误信息
// Reference: https://bytedance.feishu.cn/wiki/wikcntA1ILEQ220MIQ9kKrAHCYd
message ErrorInfo {
  // The error code, 0 means no error.
  //
  // 协议错误码, 0 表示无错误.
  int32 code = 1;

  // The reason of the error, should be a string enumeration defined in
  // Protobuf of each domain.
  //
  // 业务错误码, API 维护错误码列表.
  string reason = 2;

  // Description of code, from developer perspective.
  //
  // 错误码解释.
  string description = 3;

  // Error message, from user perspective.
  //
  // 错误信息，用来前端显示.
  string message = 4;

  // Additional structured details about this error.
  //
  // Keys should match /[a-zA-Z0-9-_]/ and be limited to 64 characters in
  // length. And values should be limited to 512 characters in length.
  //
  // 结构化的错误详情.
  // Key 应符合正则 /[a-zA-Z0-9-_]/, 最多 64 个字符, value 最多 512 个字符.
  map<string, string> details = 5;
}