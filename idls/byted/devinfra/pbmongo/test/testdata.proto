syntax = "proto3";
package byted.devinfra.pbmongo.test;

import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "idls/byted/devinfra/pbmongo/objectid.proto";

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/pbmongo/test";

message SubData {
  string name = 1;
}

message Data {
  google.protobuf.BoolValue bool_value = 1 [json_name = "customize_bool_name"];
  google.protobuf.BytesValue bytes_value = 2;
  google.protobuf.DoubleValue double_value = 3;
  google.protobuf.FloatValue float_value = 4;
  google.protobuf.Int32Value int32_value = 5;
  google.protobuf.Int64Value int64_value = 6;
  google.protobuf.StringValue string_value = 7;
  google.protobuf.UInt32Value uint32_value = 8;
  google.protobuf.UInt64Value uint64_value = 9;
  google.protobuf.Timestamp timestamp = 10 [json_name = "customize_timestamp_name"];
  byted.devinfra.pbmongo.ProtoObjectId id = 11;
  bool bool_simple = 12;
  int32 int_simple = 13;
  string string_simple = 14;
  google.protobuf.Any any_data = 15;
  SubData sub_data = 16;
  oneof one_of_data {
    SubData one_of_sub_data = 17;
    string one_of_string = 18;
  }
}
