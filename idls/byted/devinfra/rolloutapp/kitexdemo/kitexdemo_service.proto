syntax = "proto3";

package byted.devinfra.rolloutapp.kitexdemo;

option go_package = "code.byted.org/devinfra/hagrid/pbgen/byted/devinfra/rolloutapp/kitexdemopb";

message EchoRequest {
  string msg = 1;
}

message EchoResponse {
  string vdc = 1;
  string region = 2;
  string cluster = 3;
  string env = 4;
  uint32 counter = 5;
  string msg = 6;
}

service KitexDemoService {
  rpc Echo(EchoRequest) returns (EchoResponse);
}
